/* NFT抽卡玩法后台接口 */

syntax = "proto3";
package vas.xactivity;
option go_package = "buf.bilibili.co/bapis/bapis-gen/vas/xactivity.service";
option java_package = "com.bapis.vas.xactivity.service";
option java_multiple_files = true;

import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "extension/wdcli/wdcli.proto";
option (wdcli.appid) = "main.vas.xactivity-service";

// 抽卡后台接口
service NftCardBackend {
    // 列表
    rpc NftCardList(NftCardListReq) returns (NftCardListResp);
    // 详情
    rpc NftCardDetail(NftCardDetailReq) returns (NftCardDetailResp);
    // 保存
    rpc NftCardSave(NftCardSaveReq) returns (NftCardSaveResp);
    // 初始化接口(下发公式)
    rpc NftCardIndex(NftCardIndexReq) returns (NftCardIndexResp);
    // 删除
    rpc NftCardDel(NftCardDelReq) returns (NftCardDelResp);
    // 上线/下线
    rpc NftCardUpdateStatus(NftCardUpdateStatusReq) returns (NftCardUpdateStatusResp);
}

// 列表
message NftCardListReq {
    // 页码
    int64 page = 1 [(gogoproto.jsontag) = "page", (gogoproto.moretags) = 'form:"page"', json_name = "page"];
    // 每页数量
    int64 size = 2 [(gogoproto.jsontag) = "size", (gogoproto.moretags) = 'form:"size"', json_name = "size"];
}
message NftCardListResp {
    // 总数
    int64 total = 1 [(gogoproto.jsontag) = "total", (gogoproto.moretags) = 'form:"total"', json_name = "total"];
    // 列表数据
    repeated ListItem list = 2 [(gogoproto.jsontag) = "list", (gogoproto.moretags) = 'form:"list"', json_name = "list"];
    message ListItem {
        // 活动ID
        int64 act_id = 1 [(gogoproto.jsontag) = "act_id", (gogoproto.moretags) = 'form:"act_id"', json_name = "act_id"];
        // 活动名
        string act_name = 2 [(gogoproto.jsontag) = "act_name", (gogoproto.moretags) = 'form:"act_name"', json_name = "act_name"];
        // 预热开始时间
        int64 pre_start_time = 3 [(gogoproto.jsontag) = "pre_start_time", (gogoproto.moretags) = 'form:"pre_start_time"', json_name = "pre_start_time"];
        // 预热结束时间
        int64 pre_end_time = 4 [(gogoproto.jsontag) = "pre_end_time", (gogoproto.moretags) = 'form:"pre_end_time"', json_name = "pre_end_time"];
        // 活动开始时间
        int64 start_time = 5 [(gogoproto.jsontag) = "start_time", (gogoproto.moretags) = 'form:"start_time"', json_name = "start_time"];
        // 活动结束时间
        int64 end_time = 6 [(gogoproto.jsontag) = "end_time", (gogoproto.moretags) = 'form:"end_time"', json_name = "end_time"];
        // 当前时间
        int64 cur_time = 7 [(gogoproto.jsontag) = "cur_time", (gogoproto.moretags) = 'form:"cur_time"', json_name = "cur_time"];
        // 活动状态码
        int64 status = 8 [(gogoproto.jsontag) = "status", (gogoproto.moretags) = 'form:"status"', json_name = "status"];
        // 活动状态展示
        string status_desc = 9 [(gogoproto.jsontag) = "status_desc", (gogoproto.moretags) = 'form:"status_desc"', json_name = "status_desc"];
        // 创建人
        string operator = 10 [(gogoproto.jsontag) = "operator", (gogoproto.moretags) = 'form:"operator"', json_name = "operator"];
        // 最近修改时间
        int64 mtime = 11 [(gogoproto.jsontag) = "mtime", (gogoproto.moretags) = 'form:"mtime"', json_name = "mtime"];
        // 活动类型 （0：普通付费活动，默认；1：免费活动）
        int64 act_type = 12 [(gogoproto.jsontag) = "act_type", (gogoproto.moretags) = 'form:"act_type"', json_name = "act_type"];
    }
}
// 详情
message NftCardDetailReq {
    // 活动ID
    int64 act_id = 1 [(gogoproto.jsontag) = "act_id", (gogoproto.moretags) = 'form:"act_id"', json_name = "act_id"];
}
message NftCardDetailResp {
    // 活动ID
    int64 act_id = 1 [(gogoproto.jsontag) = "act_id", (gogoproto.moretags) = 'form:"act_id"', json_name = "act_id"];
    // 活动名
    string act_name = 2 [(gogoproto.jsontag) = "act_name", (gogoproto.moretags) = 'form:"act_name"', json_name = "act_name"];
    // 预热开始时间
    int64 pre_start_time = 3 [(gogoproto.jsontag) = "pre_start_time", (gogoproto.moretags) = 'form:"pre_start_time"', json_name = "pre_start_time"];
    // 预热结束时间
    int64 pre_end_time = 4 [(gogoproto.jsontag) = "pre_end_time", (gogoproto.moretags) = 'form:"pre_end_time"', json_name = "pre_end_time"];
    // 活动开始时间
    int64 start_time = 5 [(gogoproto.jsontag) = "start_time", (gogoproto.moretags) = 'form:"start_time"', json_name = "start_time"];
    // 活动结束时间
    int64 end_time = 6 [(gogoproto.jsontag) = "end_time", (gogoproto.moretags) = 'form:"end_time"', json_name = "end_time"];
    // 当前时间
    int64 cur_time = 7 [(gogoproto.jsontag) = "cur_time", (gogoproto.moretags) = 'form:"cur_time"', json_name = "cur_time"];
    // 活动方
    string act_source = 8 [(gogoproto.jsontag) = "act_source", (gogoproto.moretags) = 'form:"act_source"', json_name = "act_source"];
    // 活动攻略
    string act_desc = 9 [(gogoproto.jsontag) = "act_desc", (gogoproto.moretags) = 'form:"act_desc"', json_name = "act_desc"];
    // 图鉴封面
    string act_x_img = 10 [(gogoproto.jsontag) = "act_x_img", (gogoproto.moretags) = 'form:"act_x_img"', json_name = "act_x_img"];
    // 活动封面
    string act_y_img = 11 [(gogoproto.jsontag) = "act_y_img", (gogoproto.moretags) = 'form:"act_y_img"', json_name = "act_y_img"];
    // 活动状态 （0下线 1上线）
    int64 status = 12 [(gogoproto.jsontag) = "status", (gogoproto.moretags) = 'form:"status"', json_name = "status"];
    // 商品名
    string item_name = 13 [(gogoproto.jsontag) = "item_name", (gogoproto.moretags) = 'form:"item_name"', json_name = "item_name"];
    // 商品图片
    string item_img = 14 [(gogoproto.jsontag) = "item_img", (gogoproto.moretags) = 'form:"item_img"', json_name = "item_img"];
    // 售卖开始时间
    int64 sale_start_time = 15 [(gogoproto.jsontag) = "sale_start_time", (gogoproto.moretags) = 'form:"sale_start_time"', json_name = "sale_start_time"];
    // 售卖结束时间
    int64 sale_end_time = 16 [(gogoproto.jsontag) = "sale_end_time", (gogoproto.moretags) = 'form:"sale_end_time"', json_name = "sale_end_time"];
    // 售卖价格（单位：分）
    int64 sale_price = 17 [(gogoproto.jsontag) = "sale_price", (gogoproto.moretags) = 'form:"sale_price"', json_name = "sale_price"];
    // 单用户购买上限（0表示不限制）
    int64 single_user_limit = 18 [(gogoproto.jsontag) = "single_user_limit", (gogoproto.moretags) = 'form:"single_user_limit"', json_name = "single_user_limit"];
    // 分享配置(json格式)
    string share_json = 19 [(gogoproto.jsontag) = "share_json", (gogoproto.moretags) = 'form:"share_json"', json_name = "share_json"];
    // 轮次配置(json格式)
    string round_json = 20 [(gogoproto.jsontag) = "round_json", (gogoproto.moretags) = 'form:"round_json"', json_name = "round_json"];
    // 兑换配置(json格式)
    string exchange_json = 21 [(gogoproto.jsontag) = "exchange_json", (gogoproto.moretags) = 'form:"exchange_json"', json_name = "exchange_json"];
    // 累计任务开关(0:关闭 1:打开),废弃
    int64 is_buy_task = 22 [(gogoproto.jsontag) = "is_buy_task", (gogoproto.moretags) = 'form:"is_buy_task"', json_name = "is_buy_task"];
    // 累计任务奖励发放时间
    int64 buy_task_send_time = 23 [(gogoproto.jsontag) = "buy_task_send_time", (gogoproto.moretags) = 'form:"buy_task_send_time"', json_name = "buy_task_send_time"];
    // 众筹任务配置(json格式)
    string buy_task_json = 24 [(gogoproto.jsontag) = "buy_task_json", (gogoproto.moretags) = 'form:"buy_task_json"', json_name = "buy_task_json"];
    // 测试合约（创建后不可修改）
    string test_contract = 25 [(gogoproto.jsontag) = "test_contract", (gogoproto.moretags) = 'form:"test_contract"', json_name = "test_contract"];
    // 线上合约（创建后不可修改）
    string prod_contract = 26 [(gogoproto.jsontag) = "prod_contract", (gogoproto.moretags) = 'form:"prod_contract"', json_name = "prod_contract"];
    // 合约中文名（创建后不可修改）
    string contract_cn_name = 27 [(gogoproto.jsontag) = "contract_cn_name", (gogoproto.moretags) = 'form:"contract_cn_name"', json_name = "contract_cn_name"];
    // 合约英文名（创建后不可修改）
    string contract_en_name = 28 [(gogoproto.jsontag) = "contract_en_name", (gogoproto.moretags) = 'form:"contract_en_name"', json_name = "contract_en_name"];
    // 操作人
    string operator = 29 [(gogoproto.jsontag) = "operator", (gogoproto.moretags) = 'form:"operator"', json_name = "operator"];
    // 是否删除(0否 1是)
    int64 is_deleted = 30 [(gogoproto.jsontag) = "is_deleted", (gogoproto.moretags) = 'form:"is_deleted"', json_name = "is_deleted"];
    // 卡牌ID和卡牌名映射
    map<int64, string> card_info = 31 [(gogoproto.jsontag) = "card_info", (gogoproto.moretags) = 'form:"card_info"', json_name = "card_info"];
    // 活动是否有预热时间 （0没有预热时间 1 有预热时间）
    int64 is_pre = 32 [(gogoproto.jsontag) = "is_pre", (gogoproto.moretags) = 'form:"is_pre"', json_name = "is_pre"];
    // 是否打开预热玩法 （0关闭 1打开）
    int64 is_pre_play = 33 [(gogoproto.jsontag) = "is_pre_play", (gogoproto.moretags) = 'form:"is_pre_play"', json_name = "is_pre_play"];
    // 预热玩法配置(json格式)
    string pre_play_json = 34 [(gogoproto.jsontag) = "pre_play_json", (gogoproto.moretags) = 'form:"pre_play_json"', json_name = "pre_play_json"];
    // 限时权益配置(json格式)
    string right_json = 35 [(gogoproto.jsontag) = "right_json", (gogoproto.moretags) = 'form:"right_json"', json_name = "right_json"];
    // 活动类型 （0：普通付费活动，默认；1：免费活动）
    int64 act_type = 36 [(gogoproto.jsontag) = "act_type", (gogoproto.moretags) = 'form:"act_type"', json_name = "act_type"];
}
// 保存
message NftCardSaveReq {
    // 活动ID (0表示新增 其他表示修改)
    int64 act_id = 1 [(gogoproto.jsontag) = "act_id", (gogoproto.moretags) = 'form:"act_id"', json_name = "act_id"];
    // 活动名
    string act_name = 2 [(gogoproto.jsontag) = "act_name", (gogoproto.moretags) = 'form:"act_name" validate:"required"', json_name = "act_name"];
    // 预热开始时间
    int64 pre_start_time = 3 [(gogoproto.jsontag) = "pre_start_time", (gogoproto.moretags) = 'form:"pre_start_time"', json_name = "pre_start_time"];
    // 预热结束时间
    int64 pre_end_time = 4 [(gogoproto.jsontag) = "pre_end_time", (gogoproto.moretags) = 'form:"pre_end_time"', json_name = "pre_end_time"];
    // 活动开始时间
    int64 start_time = 5 [(gogoproto.jsontag) = "start_time", (gogoproto.moretags) = 'form:"start_time" validate:"required"', json_name = "start_time"];
    // 活动结束时间
    int64 end_time = 6 [(gogoproto.jsontag) = "end_time", (gogoproto.moretags) = 'form:"end_time" validate:"required"', json_name = "end_time"];
    // 活动方
    string act_source = 7 [(gogoproto.jsontag) = "act_source", (gogoproto.moretags) = 'form:"act_source" validate:"required"', json_name = "act_source"];
    // 活动攻略
    string act_desc = 8 [(gogoproto.jsontag) = "act_desc", (gogoproto.moretags) = 'form:"act_desc"', json_name = "act_desc"];
    // 图鉴封面
    string act_x_img = 9 [(gogoproto.jsontag) = "act_x_img", (gogoproto.moretags) = 'form:"act_x_img"', json_name = "act_x_img"];
    // 活动封面
    string act_y_img = 10 [(gogoproto.jsontag) = "act_y_img", (gogoproto.moretags) = 'form:"act_y_img"', json_name = "act_y_img"];
    // 活动状态 （0下线 1上线）
    int64 status = 11 [(gogoproto.jsontag) = "status", (gogoproto.moretags) = 'form:"status"', json_name = "status"];
    // 商品名
    string item_name = 12 [(gogoproto.jsontag) = "item_name", (gogoproto.moretags) = 'form:"item_name"', json_name = "item_name"];
    // 商品图片
    string item_img = 13 [(gogoproto.jsontag) = "item_img", (gogoproto.moretags) = 'form:"item_img"', json_name = "item_img"];
    // 售卖开始时间
    int64 sale_start_time = 14 [(gogoproto.jsontag) = "sale_start_time", (gogoproto.moretags) = 'form:"sale_start_time"', json_name = "sale_start_time"];
    // 售卖结束时间
    int64 sale_end_time = 15 [(gogoproto.jsontag) = "sale_end_time", (gogoproto.moretags) = 'form:"sale_end_time"', json_name = "sale_end_time"];
    // 售卖价格（单位：分）
    int64 sale_price = 16 [(gogoproto.jsontag) = "sale_price", (gogoproto.moretags) = 'form:"sale_price"', json_name = "sale_price"];
    // 单用户购买上限（0表示不限制）
    int64 single_user_limit = 17 [(gogoproto.jsontag) = "single_user_limit", (gogoproto.moretags) = 'form:"single_user_limit"', json_name = "single_user_limit"];
    // 分享配置(json格式)
    string share_json = 18 [(gogoproto.jsontag) = "share_json", (gogoproto.moretags) = 'form:"share_json"', json_name = "share_json"];
    // 轮次配置(json格式)
    string round_json = 19 [(gogoproto.jsontag) = "round_json", (gogoproto.moretags) = 'form:"round_json"', json_name = "round_json"];
    // 兑换配置(json格式)
    string exchange_json = 20 [(gogoproto.jsontag) = "exchange_json", (gogoproto.moretags) = 'form:"exchange_json"', json_name = "exchange_json"];
    // 累计任务开关(0:关闭 1:打开) 废弃
    int64 is_buy_task = 21 [(gogoproto.jsontag) = "is_buy_task", (gogoproto.moretags) = 'form:"is_buy_task"', json_name = "is_buy_task"];
    // 累计任务奖励发放时间
    int64 buy_task_send_time = 22 [(gogoproto.jsontag) = "buy_task_send_time", (gogoproto.moretags) = 'form:"buy_task_send_time"', json_name = "buy_task_send_time"];
    // 众筹任务配置(json格式)
    string buy_task_json = 23 [(gogoproto.jsontag) = "buy_task_json", (gogoproto.moretags) = 'form:"buy_task_json"', json_name = "buy_task_json"];
    // 合约中文名（创建后不可修改）
    string contract_cn_name = 24 [(gogoproto.jsontag) = "contract_cn_name", (gogoproto.moretags) = 'form:"contract_cn_name"', json_name = "contract_cn_name"];
    // 合约英文名（创建后不可修改）
    string contract_en_name = 25 [(gogoproto.jsontag) = "contract_en_name", (gogoproto.moretags) = 'form:"contract_en_name"', json_name = "contract_en_name"];
    // 操作人
    string operator = 26 [(gogoproto.jsontag) = "operator", (gogoproto.moretags) = 'form:"operator"', json_name = "operator"];
    // 活动是否有预热时间 （0没有预热时间 1 有预热时间）
    int64 is_pre = 27 [(gogoproto.jsontag) = "is_pre", (gogoproto.moretags) = 'form:"is_pre"', json_name = "is_pre"];
    // 是否打开预热玩法 （0关闭 1打开）
    int64 is_pre_play = 28 [(gogoproto.jsontag) = "is_pre_play", (gogoproto.moretags) = 'form:"is_pre_play"', json_name = "is_pre_play"];
    // 预热玩法配置(json格式)
    string pre_play_json = 29 [(gogoproto.jsontag) = "pre_play_json", (gogoproto.moretags) = 'form:"pre_play_json"', json_name = "pre_play_json"];
    // 限时权益配置(json格式)
    string right_json = 30 [(gogoproto.jsontag) = "right_json", (gogoproto.moretags) = 'form:"right_json"', json_name = "right_json"];
    // 活动类型 （0：普通付费活动，默认；1：免费活动）
    int64 act_type = 31 [(gogoproto.jsontag) = "act_type", (gogoproto.moretags) = 'form:"act_type"', json_name = "act_type"];
}
message NftCardSaveResp {
    // 错误码（0表示成功 其他表示失败）
    int64 status_code = 1 [(gogoproto.jsontag) = "status_code", (gogoproto.moretags) = 'form:"status_code"', json_name = "status_code"];
    // 错误信息
    string err_message = 2 [(gogoproto.jsontag) = "err_message", (gogoproto.moretags) = 'form:"err_message"', json_name = "err_message"];
}

// 初始化接口(下发公式)
message SelectItem {
    // 主题ID
    int64 item_id = 1 [(gogoproto.jsontag) = "item_id", (gogoproto.moretags) = 'form:"item_id"', json_name = "item_id"];
    // 主题名
    string item_name = 2 [(gogoproto.jsontag) = "item_name", (gogoproto.moretags) = 'form:"item_name"', json_name = "item_name"];
}
message NftCardIndexReq {}
message NftCardIndexResp {
    // 公式列表
    repeated SelectItem lottery_formula_list = 1 [(gogoproto.jsontag) = "lottery_formula_list", (gogoproto.moretags) = 'form:"lottery_formula_list"', json_name = "lottery_formula_list"];
}

// 删除
message NftCardDelReq {
    // 活动id
    int64 act_id = 1 [(gogoproto.jsontag) = "act_id", (gogoproto.moretags) = 'form:"act_id"', json_name = "act_id"];
}

message NftCardDelResp {
    // 错误码（0表示成功 其他表示失败）
    int64 status_code = 1 [(gogoproto.jsontag) = "status_code", (gogoproto.moretags) = 'form:"status_code"', json_name = "status_code"];
    // 错误信息
    string err_message = 2 [(gogoproto.jsontag) = "err_message", (gogoproto.moretags) = 'form:"err_message"', json_name = "err_message"];
}

// 上线/下线
message NftCardUpdateStatusReq {
    // 活动id
    int64 act_id = 1 [(gogoproto.jsontag) = "act_id", (gogoproto.moretags) = 'form:"act_id"', json_name = "act_id"];
    // 状态
    int64 status = 2 [(gogoproto.jsontag) = "status", (gogoproto.moretags) = 'form:"status"', json_name = "status"];
}

message NftCardUpdateStatusResp {
    // 错误码（0表示成功 其他表示失败）
    int64 status_code = 1 [(gogoproto.jsontag) = "status_code", (gogoproto.moretags) = 'form:"status_code"', json_name = "status_code"];
    // 错误信息
    string err_message = 2 [(gogoproto.jsontag) = "err_message", (gogoproto.moretags) = 'form:"err_message"', json_name = "err_message"];
}