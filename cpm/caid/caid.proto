syntax = "proto3";

package rpc.caid;

option go_package = "buf.bilibili.co/bapis/bapis-gen/cpm/caid;api";
option java_package = "com.bapis.cpm.caid";
option java_multiple_files = true;

message DeviceInfo {
  string boot_time_in_sec = 1;  // 设备启动时间
  string country_code = 2;  // 国家
  string language = 3;  // 语言
  string device_name = 4;  // 设备名称
  string system_version = 5;  // 系统版本
  string machine = 6;  // 设备machine
  string carrier_info = 7;  // 运营商
  string memory = 8;  // 物理内存
  string disk = 9;  // 磁盘容量
  string sys_file_time = 10;  // 系统更新时间
  string model = 11;  // 设备model
  string time_zone = 12;  // 时区
}

message CaidRequest {
  enum Source {
    DEFAULT = 0;
    BRAND = 1;
    TAB3 = 2;
  }
  string request_id = 1;  // 请求id
  repeated string options = 2;  // 选项, 返回对应的caid
  DeviceInfo device_info = 3;  // 设备信息
  Source source = 4;
  string passkey = 5;
}

message CaidResponse {
  int32 code = 1;
  map<string, string> caids = 2;  // <option,caid>
}

service CaidService {
    rpc get_caid(CaidRequest) returns (CaidResponse);
}