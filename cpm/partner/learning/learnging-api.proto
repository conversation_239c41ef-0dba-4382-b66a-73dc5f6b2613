syntax = "proto3";
package cpm.partner.learning;

import "extension/wdcli/wdcli.proto";
import "google/protobuf/timestamp.proto";
option (wdcli.appid) = "sycpb.cpm.business-partner";

option java_package = "com.bapis.cpm.partner.learning";
option go_package = "buf.bilibili.co/bapis/bapis-gen/cpm/partner.learning;v1";
option java_multiple_files = true;
option java_outer_classname = "LearningApi";
service LearningServiceApi {
  rpc CreateLearningGuidance(CreateLearningGuidanceReq) returns (CreateLearningGuidanceResponse){}
  rpc ListLearningGuidance(ListLearningGuidanceReq) returns (ListLearningGuidanceResponse){}
  rpc UpdateLearningGuidance(UpdateLearningGuidanceReq) returns (UpdateLearningGuidanceResponse){}

  rpc CreateLearningCase(CreateLearningCaseReq) returns (CreateLearningCaseResponse){}
  rpc ListLearningCase(ListLearningCaseReq) returns (ListLearningCaseResponse){}
  rpc UpdateLearningCase(UpdateLearningCaseReq) returns (UpdateLearningCaseResponse){}

  rpc QueryLearningHistory(QueryLearningHistoryReq) returns (LearningHistoryResponse){}
  //更新排序信息
  rpc UpdateLearningSort(UpdateLearningSortReq) returns (UpdateLearningSortResponse){}
  rpc UpdateLearningStatus(UpdateLearningStatusReq) returns (UpdateLearningStatusResponse){}
  rpc ArchiveDetail(ArchiveDetailReq) returns (ArchiveDetailResponse){}
}
message UpdateLearningStatusReq{
  int64 id = 1;
  LearningStatusEnum status_enum = 2;
  LearningContentTypeEnum content_type_enum = 3;
  string operator = 4;
}
message UpdateLearningStatusResponse{
  int32 code = 1;
  string message = 2;
  bool data = 3;
}
message ArchiveDetailReq{
  int64 av_id = 1;
}
message ArchiveDetailResponse{
  int32  code = 1;
  string message = 2;
  ArchiveDetail  data = 3;
}
message ArchiveDetail{
  int64 av_id = 1;
  string title = 2;
  string cover_url = 3;
  string first_industry = 4;
}
enum LearningContentTypeEnum{
  TYPE_UNKNOWN = 0;
  NEWBIE_GUIDANCE = 1;
  ADVANCED_GUIDANCE = 2;
  EXCELLENT_CASE = 3;
}
message UpdateLearningSortReq{
  repeated int64 id = 1;
  LearningContentTypeEnum content_type_enum = 2;
  string operator = 3;
}
message UpdateLearningSortResponse{
  int32  code = 1;
  string message = 2;
  bool  data = 3;
}
message UpdateLearningCaseReq{
  int64 id = 1;
  //可以为空,为空则不更新
  string recommend_reason = 2;
  //为空则清空
  repeated string content_labels = 3;
  //为空则清空
  repeated int64 black_list = 4;
  repeated int64 white_list = 5;
  string operator = 6;
}

enum LearningStatusEnum{
  //  未知
  STATUS_UNKNOWN = 0;
  //  待上线
  PENDING_ONLINE = 1;
  //  已上线
  ONLINE = 2;
  //  已下线
  OFFLINE = 3;
}
message UpdateLearningCaseResponse{
  int32  code = 1;
  string message = 2;
  bool  data = 3;
}
message UpdateLearningGuidanceReq{
  //  可以为空，为空则不更新
  string link_url = 2;
  //  可以为空，为空则不更新
  string title = 3;
  //  可以为空，为空则不更新
  string cover_url = 4;
  //  如果为空，则会清空黑白名单
  repeated int64 black_list = 5;
  repeated int64 white_list = 6;
  //  不能为空
  int64 id = 7;
  string operator = 8;
  LearningContentTypeEnum content_type_enum = 9;
}
message UpdateLearningGuidanceResponse{
  int32  code = 1;
  string message = 2;
  bool  data = 3;
}
message ListLearningCaseReq{
  int64 av_id = 1;
  LearningStatusEnum case_status_enum = 2;
  int32 page = 3;
  int32 page_size = 4;
  //稿件可以浏览。0：不限定；1：可以；2：不可以
  int32 archive_browsable = 5;
  //用户MID，如果>0则会进行黑白名单过滤
  int64 mid = 6;
}
message ListLearningCaseResponse{
  int32  code = 1;
  string message = 2;
  repeated LearningCaseDetail data = 3;
  int32 total_size = 4;
}
message LearningCaseDetail{
  int64 av_id = 1;
  LearningContentTypeEnum content_type_enum = 2;
  string first_industry = 3;
  string recommend_reason = 5;
  string link_url = 6;
  string title = 7;
  string cover_url = 8;
  repeated string content_labels = 9;
  int64 id = 10;
  //未知ID，连续展示
  int64 position_id = 11;
  //排序ID，表示先后顺序
  int64 sort_id = 12;
  google.protobuf.Timestamp mtime = 13;
  LearningStatusEnum case_status_enum = 14;
  repeated int64 black_list = 15;
  repeated int64 white_list = 16;
  google.protobuf.Timestamp publish_date = 17;
  bool had_new_lab = 18;
}
message ListLearningGuidanceReq{
  LearningContentTypeEnum content_type_enum = 1;
  LearningStatusEnum guidance_status_enum = 2;
  int32 page = 3;
  int32 page_size = 4;
  //用户MID，如果>0则会进行黑白名单过滤
  int64 mid = 5;
}
message ListLearningGuidanceResponse{
  int32  code = 1;
  string message = 2;
  repeated LearningGuidanceDetail data = 3;
  int32 total_size = 4;
}
message LearningGuidanceDetail{
  int64 id = 1;
  //未知ID，连续展示
  int64 position_id = 2;
  //排序ID，表示先后顺序
  int64 sort_id = 3;
  LearningContentTypeEnum content_type_enum = 4;
  string link_url = 5;
  string title = 6;
  string cover_url = 7;
  LearningStatusEnum guidance_status_enum = 8;
  google.protobuf.Timestamp mtime = 9;
  repeated int64 black_list = 10;
  repeated int64 white_list = 11;
  bool had_new_lab = 12;
}
message CreateLearningGuidanceReq{
  LearningContentTypeEnum content_type_enum = 1;
  string link_url = 2;
  string title = 3;
  string cover_url = 4;
  repeated int64 black_list = 5;
  repeated int64 white_list = 6;
  string operator = 7;
}

message CreateLearningGuidanceResponse{
  int32  code = 1;
  string message = 2;
  bool  data = 3;
}
message CreateLearningCaseResponse{
  int32  code = 1;
  string message = 2;
  bool  data = 3;
}

message CreateLearningCaseReq{
  int64 av_id = 1;
  string first_industry = 2;
  string recommend_reason = 4;
  repeated string content_labels = 5;
  repeated int64 black_list = 6;
  repeated int64 white_list = 7;
  string operator = 8;
}
message QueryLearningHistoryReq{
  int64 id = 1;
  LearningContentTypeEnum content_type_enum = 2;
}
message LearningHistoryResponse{
  int32  code = 1;
  string message = 2;
  repeated LearningHistoryDetail data = 3;
}

message LearningHistoryDetail{
  google.protobuf.Timestamp event_time = 1 ;
  string title = 2;
  repeated LearningHistoryItem items = 3;
}
message LearningHistoryItem{
  //变更列名称
  string column_name = 1;
  //列名描述
  string column_desc = 2;
  //变更后的新值
  string newValue = 3;
  //变更前的新值
  string oldValue = 4;
  //  排序ID
  int32 sort_id = 5;
  //列名描述信息
  string value_desc = 6;
}