syntax="proto3";

package rec;

option go_package = "buf.bilibili.co/bapis/bapis-gen/cpm/ecom.rec.rec;api";
option java_package = "com.bapis.cpm.ecom.rec.rec";
option java_multiple_files = true;

import "cpm/ecom-rec/rec/item.proto";

message BlockedId {
    int32 content_type = 1;
    repeated int64 content_id = 2;
}

message VideoInfo {
    int64 av_id = 1;
}

message ItemInfo {
    int64 content_id = 1; // [选填] 需要置顶的内容id
    int32 content_type = 2; // [选填] 需要置顶的内容type, 1:视频/2:图文/3:会员购商品/4:直播/5:游戏
    int32 circle_id = 3; // [选填] 如果是圈子Feed，表示要展示哪个圈子id下的内容
    int64 topic_id = 4; // [选填] 主站话题下的圈子推荐，传的topic_id
    int32 cate_id = 5;   // 商品的类目
    string track_id = 6; // 指定的trackid
    int64 ip_id = 7; // IP圈子的ip
    string guide_source = 8; //置顶商品来源

    repeated LinkedItem linked_item = 10;
    string venue_id = 20; // 会场id
    repeated int64 item_pool_ids = 21; //会场下关联的选品池Id
}

message ContextInfo {
    string from = 1;
    bool cold_boot = 2;
    int32 brush = 3; // [必填] 当前刷次
    int32 story_brush = 4;

    string query = 10; // [选填] 搜索关键词

    repeated BlockedId blocked_id = 20; // [选填] 返回中屏蔽哪些内容
    repeated int64 blocked_mid = 21; // [选填] 返回中屏蔽出哪些up主的内容

    VideoInfo video_info = 22;
    ItemInfo item_info = 23; // [选填] 需要置顶的内容信息
    ItemInfo linked_item_info = 24;
    repeated ItemInfo item_infos = 25; // [选填] 当前商详页、订单页、购物车页上的商品id
    
    bool in_review = 30; // bilibili的app是否在审核阶段（审核期间可能要有不一样的策略，比如屏蔽某些东西）
    
}
