syntax = "proto3";

import "cpm/auth-admin/api.proto";

// package 命名使用 {appid}.{version} 的方式, version 形如 v1, v2 ..
package sycpb.up.income.auth.admin.v1;

option go_package = "buf.bilibili.co/bapis/bapis-gen/cpm/auth.admin;api";
option java_package = "com.bapis.cpm.auth.admin";
option java_multiple_files = true;


message Privilege {
  int64 PrivilegeId = 1;
  string PrivilegeName = 2;
  string PrivilegeCode = 3;
  repeated Module Modules = 4;
  string Description = 5;
  PrivilegeType PrivilegeType = 6;
  DataStatus status = 7;
}

enum PrivilegeType {
  PrivilegeUnlimited = 0;
  PrivilegeTypePage = 1;
  PrivilegeTypeDownload = 2;
  PrivilegeTypeApi = 3;
  PrivilegeTypeApiPattern = 4;
}

message Module {
  int64 ModuleId = 1;
  string ModuleName = 2;
  repeated Module SubModule = 3;
  // 模块下的权限点
  repeated Privilege Privileges = 4;
  string ModuleCode = 5;
}