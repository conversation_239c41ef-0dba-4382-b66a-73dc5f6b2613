syntax = "proto3";

package cheese.player.service.finance.v1;

import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";

option go_package = "buf.bilibili.co/bapis/bapis-gen/cheese/service.finance;api";
option java_multiple_files = true;
option java_package = "com.bapis.cheese.service.finance";

service Finance {
    // 私域售卖分销活动订单统计
    rpc CpsActivityOrderStat (CpsActivityOrderStatReq) returns (CpsActivityOrderStatReply);
    // 获取渠道结算配置
    rpc ChannelSettleConfig (ChannelSettleConfigReq) returns (ChannelSettleConfigReply);
    // 私域售卖分销活动渠道返佣配置录入
    rpc CpsActivityChannelConfig (CpsActivityChannelConfigReq) returns (google.protobuf.Empty);
    // 获取课程合同信息
    rpc SeasonContract (SeasonContractReq) returns (SeasonContractReply);
    // 设置渠道结算配置
    rpc ChannelSettleConfigOperation (ChannelSettleConfigOperationReq) returns (google.protobuf.Empty);
    // 结算报表生成
    rpc GetSettleReport (GetSettleReportReq) returns (google.protobuf.Empty);
    // 查询单位时间内的返佣订单信息
    rpc GetCpsOrderStat (GetCpsOrderStatRequest) returns (GetCpsOrderStatResponse);
    // 协议课自动设置资金结算并免审
    rpc AgreementSeasonSettleConfig (AgreementSeasonSettleConfigRequest) returns (AgreementSeasonSettleConfigResponse);
    // 获取分成配置
    rpc GetSettleRateDetail (GetSettleRateDetailRequest) returns (GetSettleRateDetailResponse);
    // 星河计划课程供应商绑定up主
    rpc SupplierBind (SupplierBindRequest) returns (SupplierBindResponse);
    // 课程的结算配置是否已配置---无需审核通过
    rpc FinanceConfigExit (FinanceConfigExitRequest) returns (FinanceConfigExitResponse);
    //判断课程的结算配置是否已配置
    rpc JudgeFinanceConfig (JudgeFinanceConfigRequest) returns (JudgeFinanceConfigResponse);
    //判断课程的结算配置类型
    rpc GetSeasonSettleContractType (GetSeasonSettleContractTypeRequest) returns (GetSeasonSettleContractTypeResponse);
}

// 设置渠道结算配置req
message ChannelSettleConfigOperationReq {
    repeated ChannelSettleConfigOperationModel config = 1;
}

// 设置渠道结算配置
message ChannelSettleConfigOperationModel {
    // mid
    int64 mid = 1;
    // 结算方式
    SettleMode settle_mode = 2;
}

// 获取课程合同信息req
message SeasonContractReq {
    // 课程id
    repeated int32 season_ids = 1;

}
// 获取课程合同信息reply
message SeasonContractReply {
    repeated SeasonContractModel season_contracts = 1;
}

message SeasonContractModel {
    // 合同编号
    string contract_no = 1;
    // 合同标题
    string contract_title = 2;
    // 合同开始时间
    google.protobuf.Timestamp contract_start_at = 3;
    // 合同到期时间
    google.protobuf.Timestamp contract_expire_at = 4;
    // 合同方类型 0：公司，1：个人
    string contract_party_type = 5;
    // 合同方名称
    string contract_party_name = 6;
    // 税率
    int32 contract_rate = 7;
    // 合同金额(人民币)
    string amount_rmb = 8;
    // 课程id
    int32 season_id = 9;

}

// 私域售卖分销活动渠道返佣配置录入req
message CpsActivityChannelConfigReq {
    repeated CpsActivityChannelConfigModel channel_configs = 1;
}

message CpsActivityChannelConfigModel {
    // 活动id
    int32 activity_id = 1;
    // 渠道id
    int64 channel_id = 2;
    // 分销链接
    string url = 3;
    // 分佣比例
    int32 rate = 4;
    // 渠道分销状态
    ChannelStatus channel_status = 5;
    //分佣类型
    int32 channel_type = 6;
}

// 渠道结算配置req
message ChannelSettleConfigReq {
    // mid信息
    repeated int64 mid_list = 1;
}

// 渠道结算配置reply
message ChannelSettleConfigReply {
    // 结算配置
    repeated ChannelSettleConfigModel config_list = 1;
}

message ChannelSettleConfigModel {
    // mid
    int64 mid = 1;
    // 结算方式
    SettleMode settle_mode = 2;
}

// 私域售卖分销活动订单统计Req
message CpsActivityOrderStatReq {
    // 统计类型目前为活动为3种(活动、渠道、分销链接) 本期需求渠道和分销链接一对一 后续可能会改成一对多 所以增加分销链接作为统计类型
    // 统计维度按照传入的统计类型(eg：传入活动id，返回结果以活动id为维度统计 活动id+渠道id 返回结果以活动id+渠道id统计)
    repeated CpsActivityOrderStatParam param = 1;
}

// 私域售卖分销活动订单统计reply
message CpsActivityOrderStatReply {
    // 统计信息
    repeated CpsActivityOrderStatModel stat_result = 1;
}

message CpsActivityOrderStatModel {
    // 分销渠道数
    int32 channel_count = 1;
    // 总订单数
    int32 order_count = 2;
    // 总返佣金额
    int32 order_amount = 3;
    // 活动id
    int32 activity_id = 4;
    // 渠道id
    int64 channel_id = 5;
    // 分销链接
    string cps_url = 6;
}

message CpsActivityOrderStatParam {
    // 活动id
    int32 activity_id = 1;
    // 渠道id
    int64 channel_id = 2;
    // 分销链接
    string cps_url = 3;
    // 活动开始时间
    google.protobuf.Timestamp activity_start_at = 4;
    // 活动到期时间
    google.protobuf.Timestamp activity_expire_at = 5;
}

message GetSettleReportReq {
    // 生成的报表类型
    repeated ReportType report_type = 1;
    // 生成报表的月份(yyyy-mm格式)
    string month = 2;
}

message GetCpsOrderStatRequest {
    // 开始时间
    google.protobuf.Timestamp start_time = 1;
    // 结束时间
    google.protobuf.Timestamp end_time = 2;
    // 订单状态
    int32 order_status = 3;
}

message GetCpsOrderStatResponse {
    repeated CpsOrderModel order_list = 1;
}

message CpsOrderModel {
    // 返佣渠道id
    int64 cps_channel_id = 1;
    // 返佣渠道类型1:自引流 2:普通渠道 3:高级渠道
    int64 cps_channel_type = 2;
    // 活动id
    int32 activity_id = 3;
    // 订单成功时间
    google.protobuf.Timestamp success_time = 4;
    // 分佣链接的csource映射值 自引流存的是一次性csource 渠道引流存的是hash值
    string csource_mapping =5;
}

message AgreementSeasonSettleConfigRequest {
    //课程的所属up主
    int64 mid = 1;
    //课程id
    int32 season_id = 2;
}

message AgreementSeasonSettleConfigResponse{

}

// 结算方式枚举 默认是贝壳
enum SettleMode {
    // 贝壳
    MODE_SHELL = 0;
    // 现金
    MODE_CASH = 1;
}

// 分销活动渠道分销状态
enum ChannelStatus {
    // 未知
    CHANNEL_STATUS_UNKNOWN = 0;
    // 未处理
    CHANNEL_STATUS_UN_HANDLE = 1;
    // 不通过
    CHANNEL_STATUS_UN_PASS = 2;
    // 通过
    CHANNEL_STATUS_PASS = 3;
    // 终止
    CHANNEL_STATUS_END = 4;
}

enum ReportType {
    // 未知
    REPORT_TYPE_UNKNOWN = 0;
    // 返佣概览报表
    REPORT_TYPE_CPS_OVERVIEW = 1;
    // 返佣明细报表
    REPORT_TYPE_CPS_DETAIL = 2;
    // 资金流结算概览报表
    REPORT_TYPE_SETTLE_OVERVIEW = 3;
    // 资金流结算明细报表
    REPORT_TYPE_SETTLE_DETAIL = 4;
    // up主线上对账概览报表
    REPORT_TYPE_CHECK_ONLINE_OVERVIEW = 5;
    // up主线上对账明细报表
    REPORT_TYPE_CHECK_ONLINE_DETAIL = 6;
}

enum ContractType {
    // 未知
    CONTRACT_TYPE_UNSPECIFIED = 0;
    // 课程合同
    CONTRACT_TYPE_CONTRACT = 1;
    // 课程协议
    CONTRACT_TYPE_AGREEMENT = 2;
    // 制作费合同
    CONTRACT_TYPE_PRODUCTION = 3;
}


message GetSettleRateDetailRequest {
    //课程id
    int32 season_id = 1;
}


message GetSettleRateDetailResponse {
    // 保底金额 分
    //int64 guaranteed_amount = 1;
    // 分成配置
    repeated SettleRate rate = 2;
}

message SettleRate {
    // 阶梯起始金额, 单位分
    int64 level_start = 1;
    // 阶梯结束金额 无限是-1, 单位分
    int64 level_end = 2;
    // 阶梯类型 1 按比例
    int32 rate_type = 3;
    // 合作方分成比例
    string rate = 4;
}

message SupplierBindRequest {
    // up主id
    int64 up_id = 1;
    // 供应商编码
    string supplier_code = 2;
    // 税率 格式：0.01、0.03、0.06
    double tax_rate = 3;
}

message SupplierBindResponse {
}

message FinanceConfigExitRequest {
    // 课程id
    int32 season_id = 1;
}

message FinanceConfigExitResponse {
    // true:配置存在
    bool flag = 1;
}

message JudgeFinanceConfigRequest {
    // 课程id
    int32 season_id = 1;
}

message JudgeFinanceConfigResponse {
    // true:配置存在
    bool flag = 1;
}

message GetSeasonSettleContractTypeRequest {
    // 课程id
    int32 season_id = 1;
}

message GetSeasonSettleContractTypeResponse {
    // 课程结算配置 0：未知，1合同，2协议
    int32 settle_contract_type = 1;
    // 结算配置状态
    int32 settle_status = 2;
}