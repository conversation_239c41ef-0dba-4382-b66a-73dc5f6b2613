# AccountService Redis 缓存实现说明

## 功能概述

为 `AccountService#queryAccountIndustryList` 方法添加了 Redis 缓存功能，通过缓存账户行业信息来提高查询性能，减少对 CRM 服务的 RPC 调用。

## 实现方案

### 缓存策略

- **缓存键格式**: `risk:account:industry:{accountId}`
- **缓存过期时间**: 30分钟
- **缓存粒度**: 单个账户ID级别
- **缓存类型**: 使用 Redisson 的 RBucket

### 核心逻辑

1. **批量查询优化**: 支持批量查询时的部分缓存命中
2. **缓存穿透处理**: 先尝试从缓存获取，未命中的再通过 RPC 查询
3. **自动缓存更新**: RPC 获取的数据自动存入缓存
4. **缓存管理**: 提供手动清除缓存的方法

## 主要方法

### 1. queryAccountIndustryList(List<Integer> accountIds)
```java
public List<AccountIndustryItemBo> queryAccountIndustryList(List<Integer> accountIds)
```
- **功能**: 查询账户行业信息列表（已添加缓存）
- **缓存逻辑**:
  1. 遍历账户ID列表，尝试从缓存获取
  2. 缓存命中的直接返回，未命中的收集起来
  3. 对未命中的账户ID进行 RPC 批量查询
  4. 将 RPC 结果存入缓存并返回完整结果

### 2. queryAccountIndustryListByRpc(List<Integer> accountIds)
```java
private List<AccountIndustryItemBo> queryAccountIndustryListByRpc(List<Integer> accountIds)
```
- **功能**: 通过 RPC 调用获取账户行业信息（原始逻辑）
- **用途**: 缓存未命中时的数据获取

### 3. clearAccountIndustryCache(Integer accountId)
```java
public void clearAccountIndustryCache(Integer accountId)
```
- **功能**: 清除单个账户的行业缓存
- **使用场景**: 账户行业信息发生变化时

### 4. clearAccountIndustryCache(List<Integer> accountIds)
```java
public void clearAccountIndustryCache(List<Integer> accountIds)
```
- **功能**: 批量清除账户行业缓存
- **使用场景**: 批量更新账户行业信息时

## 缓存配置

### 缓存常量
```java
private static final String ACCOUNT_INDUSTRY_CACHE_KEY_PREFIX = "risk:account:industry:";
private static final long CACHE_EXPIRE_TIME = 30; // 缓存过期时间，单位分钟
```

### Redis 配置
```java
@Resource(name = RedisConfig.ADP_CLUSTER)
private RedissonClient adpRedissonClient;
```

## 性能优化效果

### 缓存命中场景
- **减少 RPC 调用**: 缓存命中时无需调用 CRM 服务
- **降低响应时间**: 从毫秒级 RPC 调用降低到微秒级缓存读取
- **减少服务压力**: 降低对 CRM 服务的访问压力

### 批量查询优化
- **部分缓存命中**: 支持批量查询中部分数据从缓存获取
- **智能 RPC 调用**: 只对缓存未命中的账户进行 RPC 查询
- **最小化网络开销**: 减少不必要的数据传输

## 使用示例

### 1. 正常查询（自动使用缓存）
```java
@Autowired
private AccountService accountService;

// 查询单个账户
List<Integer> accountIds = Arrays.asList(12345);
List<AccountIndustryItemBo> result = accountService.queryAccountIndustryList(accountIds);

// 查询多个账户
List<Integer> accountIds = Arrays.asList(12345, 12346, 12347);
List<AccountIndustryItemBo> result = accountService.queryAccountIndustryList(accountIds);
```

### 2. 清除缓存
```java
// 清除单个账户缓存
accountService.clearAccountIndustryCache(12345);

// 批量清除缓存
List<Integer> accountIds = Arrays.asList(12345, 12346, 12347);
accountService.clearAccountIndustryCache(accountIds);
```

### 3. 在账户信息更新时清除缓存
```java
public void updateAccountIndustry(Integer accountId, String newIndustry) {
    // 更新账户行业信息
    updateAccountIndustryInDatabase(accountId, newIndustry);
    
    // 清除缓存，确保下次查询获取最新数据
    accountService.clearAccountIndustryCache(accountId);
}
```

## 日志输出

### 缓存命中日志
```
从缓存获取账户行业信息 accountId=12345
所有账户行业信息从缓存获取成功 accountIds=[12345, 12346]
```

### 缓存未命中日志
```
从RPC获取账户行业信息 accountIds=[12347, 12348]
账户行业信息已缓存 accountId=12347, 过期时间=30分钟
```

### 缓存清除日志
```
账户行业缓存已清除 accountId=12345
批量清除账户行业缓存完成 accountIds=[12345, 12346, 12347]
```

## 注意事项

### 1. 数据一致性
- 账户行业信息更新后需要手动清除缓存
- 建议在更新操作后立即清除相关缓存

### 2. 缓存过期时间
- 当前设置为30分钟，可根据业务需求调整
- 行业信息变更频率较低，30分钟是合理的设置

### 3. 内存使用
- 每个账户的行业信息占用内存较小
- 大量账户缓存时注意 Redis 内存使用情况

### 4. 异常处理
- 缓存操作异常不会影响正常的 RPC 查询
- 缓存读取失败时会自动降级到 RPC 查询

## 监控建议

### 1. 缓存命中率监控
- 监控缓存命中率，评估缓存效果
- 命中率过低时考虑调整缓存策略

### 2. RPC 调用量监控
- 监控对 CRM 服务的 RPC 调用量变化
- 缓存生效后应该显著减少

### 3. 响应时间监控
- 监控 `queryAccountIndustryList` 方法的响应时间
- 缓存命中时应该有明显的性能提升

## 扩展建议

### 1. 缓存预热
```java
public void warmUpAccountIndustryCache(List<Integer> accountIds) {
    // 预热指定账户的缓存
    queryAccountIndustryList(accountIds);
}
```

### 2. 缓存统计
```java
public CacheStats getAccountIndustryCacheStats() {
    // 返回缓存命中率、调用次数等统计信息
}
```

### 3. 分布式缓存更新
```java
public void refreshAccountIndustryCache(Integer accountId) {
    // 主动刷新缓存，而不是简单清除
    clearAccountIndustryCache(accountId);
    queryAccountIndustryList(Arrays.asList(accountId));
}
```
