package com.bilibili.ad.manager.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * @ClassName SanlianPpcEnum
 * <AUTHOR>
 * @Date 2024/3/14 2:57 下午
 * @Version 1.0
 **/
@AllArgsConstructor
@Getter
public enum SanlianPromotionContentTypeEnum {
    UNKNOWN(0, "未知"),
    APP_DOWNLOAD(4, "应用包下载"),
    GAME_CENTER(6, "安卓游戏中心"),
    APP_CALL_UP(60, "应用唤起"),
    LIVE_ROOM(8, "直播间"),
    LIVE_RESERVE(21, "直播预约"),
    LANDING_PAGE(2, "线索"),
    DAIHUO_CONTENT(61, "带货内容"),
    E_COMMERCE_URL(62, "电商链接"),
    TAOBAO_TMALL_GOODS(63, "淘天商品"),
    CONTENT_LAUNCH(64, "内容投放"),
    ;

    private final Integer code;
    private final String name;

    public static SanlianPromotionContentTypeEnum getByCode(Integer code) {
        return Arrays.stream(values())
                .filter(pptEnum -> Objects.equals(code,
                        pptEnum.getCode())).findFirst()
                .orElse(UNKNOWN);
    }

}
