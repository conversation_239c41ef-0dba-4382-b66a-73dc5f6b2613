package com.bilibili.ad.manager.common.enums.task;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/04/02
 * 审核任务日志类型
 */
@AllArgsConstructor
public enum AuditTaskLogType {

    /**
     * 审核通过/一审通过
     */
    FIRST_AUDIT_PASS(101, "审核通过"),

    /**
     * 审核驳回/一审驳回
     */
    FIRST_AUDIT_REJECT(102, "审核驳回"),

    /**
     * 标记为待审复杂创意/一审搁置
     */
    MARK_COMPLEX(103, "标记为待审复杂创意"),

    /**
     * 机器一审通过
     */
    MACHINE_FIRST_AUDIT_PASS(105,"机器一审通过"),

    /**
     * 机器一审驳回
     */
    MACHINE_FIRST_AUDIT_REJECT(106,"机器一审驳回"),

    /**
     * 质检通过
     */
    RECHECK_PASS(201, "质检通过"),

    /**
     * 质检驳回
     */
    RECHECK_REJECT(202, "质检驳回"),

    /**
     * 回查确认
     */
    RECHECK_CONFIRM(203, "回查确认"),

    /**
     * 编辑创意评级标签
     */
    GRADE_TAG(301, "编辑创意评级标签"),

    /**
     * 编辑稿件评级标签
     */
    ARCHIVE_GRADE_TAG(302, "编辑稿件评级标签"),

    /**
     * 框下允许
     */
    UNDER_FRAME_PASS(401, "框下允许"),

    /**
     * 框下驳回
     */
    UNDER_FRAME_REJECT(402, "框下驳回"),


    UNKNOWN(99, "未知");

    @Getter
    private final Integer code;
    @Getter
    private final String desc;

    public static AuditTaskLogType getByCode(Integer code) {
        for (AuditTaskLogType bean : values()) {
            if (Objects.equals(bean.getCode(),code)) {
                return bean;
            }
        }
        return UNKNOWN;
    }

    public static List<Integer> getAuditPassAndRejectList() {
        return Arrays.asList(AuditTaskLogType.FIRST_AUDIT_REJECT.getCode(), AuditTaskLogType.RECHECK_REJECT.getCode()
                , FIRST_AUDIT_PASS.getCode(), RECHECK_PASS.getCode());
    }
}
