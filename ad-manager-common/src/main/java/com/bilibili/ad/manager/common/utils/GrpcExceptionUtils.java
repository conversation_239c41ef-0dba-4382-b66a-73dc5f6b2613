package com.bilibili.ad.manager.common.utils;

import io.grpc.StatusRuntimeException;

/**
 * @ClassName GrpcExceptionUtils
 * <AUTHOR>
 * @Date 2022/12/25 6:34 下午
 * @Version 1.0
 **/
public class GrpcExceptionUtils {

    private final static String GRPC_INVALID_ARGUMENT_PREFIX = "INVALID_ARGUMENT: ";

    /**
     * 为了避开BaseController getFriendlyMessage 前20个字符汉字需要大于数字的问题
     */
    public static String generateGrpcExceptionMessage(Exception e) {
        String message = e.getMessage();
        if (e instanceof StatusRuntimeException
                && message.contains(GRPC_INVALID_ARGUMENT_PREFIX)) {
            message = message.replaceFirst(GRPC_INVALID_ARGUMENT_PREFIX, "");
        }
        return message;
    }

}
