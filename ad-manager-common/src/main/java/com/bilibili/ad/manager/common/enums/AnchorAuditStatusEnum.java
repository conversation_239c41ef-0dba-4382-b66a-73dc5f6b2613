package com.bilibili.ad.manager.common.enums;

import lombok.Getter;

@Getter
public enum AnchorAuditStatusEnum {

    AUDITING(0, "审核中"),
    AUDIT_PADDED(1, "审核通过"),
    AUDIT_REJECTED(2, "审核驳回"),
    UNKNOWN(-1, "未知");

    private final Integer code;
    private final String desc;

    AnchorAuditStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static AnchorAuditStatusEnum getByCode(int code) {
        for (AnchorAuditStatusEnum bean : values()) {
            if (bean.getCode() == code) {
                return bean;
            }
        }
        return UNKNOWN;
    }
}
