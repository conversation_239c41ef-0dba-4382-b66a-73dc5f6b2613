package com.bilibili.adp.launch.biz.pojo;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FlowTicketOrderPo implements Serializable {
    /**
     * 自增ID
     */
    private Integer id;

    /**
     * mid
     */
    private Long mid;

    /**
     * 流量券id
     */
    private Integer ticketId;

    /**
     * 主站视频avid
     */
    private Long avid;

    /**
     * 订单状态
     */
    private Integer orderStatus;

    /**
     * 订单名称
     */
    private String name;

    /**
     * 封图
     */
    private String cover;

    /**
     * 投放开始时间
     */
    private Timestamp launchBeginTime;

    /**
     * 投放结束时间
     */
    private Timestamp launchEndTime;

    /**
     * 原因（审核拒绝、主动取消订单时填写）
     */
    private String reason;

    /**
     * 退款状态（0-未退款 1-已退款）
     */
    private Integer refundStatus;

    /**
     * 软删除,0是有效,1是删除
     */
    private Integer isDeleted;

    /**
     * 添加时间
     */
    private Timestamp ctime;

    /**
     * 修改时间
     */
    private Timestamp mtime;

    /**
     * 账号id
     */
    private Integer accountId;

    /**
     * 订单编号
     */
    private Long orderNo;

    private static final long serialVersionUID = 1L;
}