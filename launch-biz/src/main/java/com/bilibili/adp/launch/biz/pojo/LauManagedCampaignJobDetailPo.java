package com.bilibili.adp.launch.biz.pojo;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LauManagedCampaignJobDetailPo implements Serializable {
    /**
     * 自增主键ID
     */
    private Long id;

    /**
     * 托管计划id
     */
    private Integer campaignId;

    /**
     * 托管单元id
     */
    private Integer unitId;

    /**
     * 托管创意id
     */
    private Integer creativeId;

    /**
     * 稿件id
     */
    private Long videoId;

    /**
     * 稿件名称
     */
    private String name;

    /**
     * 投放视频类型:1-商业内容 2-非商业内容 3-其他 4-代投
     */
    private Integer videoType;

    /**
     * 0-未创建 1-创建成功 2-创建失败
     */
    private Integer status;

    /**
     * 失败原因
     */
    private String failReason;

    /**
     * 批次号
     */
    private Integer batchNo;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 修改时间
     */
    private Timestamp mtime;

    private static final long serialVersionUID = 1L;
}