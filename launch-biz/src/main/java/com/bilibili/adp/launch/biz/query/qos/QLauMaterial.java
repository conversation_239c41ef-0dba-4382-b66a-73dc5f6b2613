package com.bilibili.adp.launch.biz.query.qos;

import static com.querydsl.core.types.PathMetadataFactory.*;
import com.bilibili.adp.launch.biz.query.dos.LauMaterialDo;


import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;

import com.querydsl.sql.ColumnMetadata;
import java.sql.Types;




/**
 * QLauMaterial is a Querydsl query type for LauMaterialDo
 */
@Generated("com.querydsl.sql.codegen.MetaDataSerializer")
public class QLauMaterial extends com.querydsl.sql.RelationalPathBase<LauMaterialDo> {

    private static final long serialVersionUID = 2009354602;

    public static final QLauMaterial lauMaterial = new QLauMaterial("lau_material");

    public final DateTimePath<java.sql.Timestamp> ctime = createDateTime("ctime", java.sql.Timestamp.class);

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final StringPath materialContent = createString("materialContent");

    public final StringPath materialMd5 = createString("materialMd5");

    public final NumberPath<Integer> materialType = createNumber("materialType", Integer.class);

    public final DateTimePath<java.sql.Timestamp> mtime = createDateTime("mtime", java.sql.Timestamp.class);

    public final com.querydsl.sql.PrimaryKey<LauMaterialDo> primary = createPrimaryKey(id);

    public QLauMaterial(String variable) {
        super(LauMaterialDo.class, forVariable(variable), "null", "lau_material");
        addMetadata();
    }

    public QLauMaterial(String variable, String schema, String table) {
        super(LauMaterialDo.class, forVariable(variable), schema, table);
        addMetadata();
    }

    public QLauMaterial(String variable, String schema) {
        super(LauMaterialDo.class, forVariable(variable), schema, "lau_material");
        addMetadata();
    }

    public QLauMaterial(Path<? extends LauMaterialDo> path) {
        super(path.getType(), path.getMetadata(), "null", "lau_material");
        addMetadata();
    }

    public QLauMaterial(PathMetadata metadata) {
        super(LauMaterialDo.class, metadata, "null", "lau_material");
        addMetadata();
    }

    public void addMetadata() {
        addMetadata(ctime, ColumnMetadata.named("ctime").withIndex(2).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(id, ColumnMetadata.named("id").withIndex(1).ofType(Types.BIGINT).withSize(20).notNull());
        addMetadata(materialContent, ColumnMetadata.named("material_content").withIndex(6).ofType(Types.VARCHAR).withSize(256).notNull());
        addMetadata(materialMd5, ColumnMetadata.named("material_md5").withIndex(4).ofType(Types.VARCHAR).withSize(32).notNull());
        addMetadata(materialType, ColumnMetadata.named("material_type").withIndex(5).ofType(Types.TINYINT).withSize(3).notNull());
        addMetadata(mtime, ColumnMetadata.named("mtime").withIndex(3).ofType(Types.TIMESTAMP).withSize(19).notNull());
    }

}

