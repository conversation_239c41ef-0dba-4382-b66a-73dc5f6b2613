package com.bilibili.adp.launch.biz.config.mysql;

import com.bilibili.bjcom.querydsl.BaseQueryFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import pleiades.component.mysql.datasource.BiliDataSource;

import javax.sql.DataSource;

import static com.bilibili.adp.launch.biz.config.mysql.AdPlatformMysqlConfig.*;

/**
 * 建议 用项目自己定义的adCore配置，并关闭这个配置类
 *
 * <AUTHOR>
 * @date 2024/12/3
 */
@Slf4j
@Configuration
@ConditionalOnProperty(name = "ad-platform.data-source.ad.enabled", havingValue = "true", matchIfMissing = true)
public class AdPlatformBusinessAdConfig {
    @Value("${jdbc.cluster:unknown}")
    private String adCluster;
    @Value("${jdbc.url}")
    private String adUrl;
    @Value("${jdbc.username}")
    private String adUser;
    @Value("${jdbc.password}")
    private String adPassword;
    @Value("${jdbc.driver}")
    private String adDriver;

    @Value("${ad.read.cluster:unknown}")
    private String adReadCluster;
    @Value("${ad.read.url}")
    private String adReadUrl;
    @Value("${ad.read.username}")
    private String adReadUser;
    @Value("${ad.read.password}")
    private String adReadPassword;
    @Value("${ad.read.driver}")
    private String adReadDriver;

    @Bean(AD_WRITE_DS)
    public BiliDataSource adDataSource() {
        log.info("init ds: jar-ad-write");
        return MySqlUtils.newBiliDataSource(adCluster, adUrl, adUser, adPassword, adDriver);
    }

    @Bean(AD_READ_DS)
    public BiliDataSource adReadDataSource() {
        log.info("init ds: jar-ad-read");
        return MySqlUtils.newBiliDataSource(adReadCluster, adReadUrl, adReadUser, adReadPassword, adReadDriver);
    }

    @Bean(AD_DYNAMIC_DS)
    public DataSource adDynamicDataSource(@Qualifier(AD_WRITE_DS) DataSource adDataSource, @Qualifier(AD_READ_DS) DataSource adReadDataSource) {
        return MySqlUtils.newDynamicDataSource(adDataSource, adReadDataSource);
    }

    @Bean(AD_DYNAMIC_BQF)
    public BaseQueryFactory adDynamicBqf(@Qualifier(AD_DYNAMIC_DS) DataSource adDynamicDataSource) {
        return MySqlUtils.newBaseQueryFactory(adDynamicDataSource, false);
    }

    @Bean(AD_DYNAMIC_SF)
    public SqlSessionFactory adDynamicSessionFactory(@Qualifier(AD_DYNAMIC_DS) DataSource adDynamicDataSource) {
        return MySqlUtils.newSessionFactory(adDynamicDataSource, false, "classpath:mapper/new_launch/*.xml");
    }

    @Bean(AD_DYNAMIC_TM)
    public DataSourceTransactionManager adDynamicTransactionManager(@Qualifier(AD_DYNAMIC_DS) DataSource adDynamicDataSource) {
        return MySqlUtils.newTxMgr(adDynamicDataSource);
    }
}