package com.bilibili.adp.launch.biz.ad_data.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ResTargetMinigameMapPo implements Serializable {
    private Long id;

    /**
     * 小程序id
     */
    private String appId;

    /**
     * 小程序名称
     */
    private String name;

    private Timestamp ctime;

    private Timestamp mtime;

    private static final long serialVersionUID = 1L;
}