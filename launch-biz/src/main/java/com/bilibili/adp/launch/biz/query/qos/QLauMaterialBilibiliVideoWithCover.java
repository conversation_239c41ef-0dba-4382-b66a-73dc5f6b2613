package com.bilibili.adp.launch.biz.query.qos;

import com.bilibili.adp.launch.biz.query.dos.LauMaterialBilibiliVideoWithCoverDo;
import com.querydsl.core.types.Path;
import com.querydsl.core.types.PathMetadata;
import com.querydsl.core.types.dsl.DateTimePath;
import com.querydsl.core.types.dsl.NumberPath;
import com.querydsl.core.types.dsl.StringPath;
import com.querydsl.sql.ColumnMetadata;

import javax.annotation.Generated;
import java.sql.Types;

import static com.querydsl.core.types.PathMetadataFactory.forVariable;


/**
 * QLauMaterialBilibiliVideoWithCover is a Querydsl query type for LauMaterialBilibiliVideoWithCoverPo
 */
@Generated("com.querydsl.sql.codegen.MetaDataSerializer")
public class QLauMaterialBilibiliVideoWithCover extends com.querydsl.sql.RelationalPathBase<LauMaterialBilibiliVideoWithCoverDo> {

    private static final long serialVersionUID = -1082859713;

    public static final QLauMaterialBilibiliVideoWithCover lauMaterialBilibiliVideoWithCover = new QLauMaterialBilibiliVideoWithCover("lau_material_bilibili_video_with_cover");

    public final NumberPath<Long> avid = createNumber("avid", Long.class);

    public final NumberPath<Long> cid = createNumber("cid", Long.class);

    public final StringPath coverMd5 = createString("coverMd5");

    public final StringPath coverUrl = createString("coverUrl");

    public final DateTimePath<java.sql.Timestamp> ctime = createDateTime("ctime", java.sql.Timestamp.class);

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final StringPath materialMd5 = createString("materialMd5");

    public final DateTimePath<java.sql.Timestamp> mtime = createDateTime("mtime", java.sql.Timestamp.class);

    public final StringPath videoMd5 = createString("videoMd5");

    public final com.querydsl.sql.PrimaryKey<LauMaterialBilibiliVideoWithCoverDo> primary = createPrimaryKey(id);

    public QLauMaterialBilibiliVideoWithCover(String variable) {
        super(LauMaterialBilibiliVideoWithCoverDo.class, forVariable(variable), "null", "lau_material_bilibili_video_with_cover");
        addMetadata();
    }

    public QLauMaterialBilibiliVideoWithCover(String variable, String schema, String table) {
        super(LauMaterialBilibiliVideoWithCoverDo.class, forVariable(variable), schema, table);
        addMetadata();
    }

    public QLauMaterialBilibiliVideoWithCover(String variable, String schema) {
        super(LauMaterialBilibiliVideoWithCoverDo.class, forVariable(variable), schema, "lau_material_bilibili_video_with_cover");
        addMetadata();
    }

    public QLauMaterialBilibiliVideoWithCover(Path<? extends LauMaterialBilibiliVideoWithCoverDo> path) {
        super(path.getType(), path.getMetadata(), "null", "lau_material_bilibili_video_with_cover");
        addMetadata();
    }

    public QLauMaterialBilibiliVideoWithCover(PathMetadata metadata) {
        super(LauMaterialBilibiliVideoWithCoverDo.class, metadata, "null", "lau_material_bilibili_video_with_cover");
        addMetadata();
    }

    public void addMetadata() {
        addMetadata(avid, ColumnMetadata.named("avid").withIndex(5).ofType(Types.BIGINT).withSize(20).notNull());
        addMetadata(cid, ColumnMetadata.named("cid").withIndex(9).ofType(Types.BIGINT).withSize(20).notNull());
        addMetadata(coverMd5, ColumnMetadata.named("cover_md5").withIndex(7).ofType(Types.VARCHAR).withSize(32).notNull());
        addMetadata(coverUrl, ColumnMetadata.named("cover_url").withIndex(6).ofType(Types.VARCHAR).withSize(256).notNull());
        addMetadata(ctime, ColumnMetadata.named("ctime").withIndex(2).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(id, ColumnMetadata.named("id").withIndex(1).ofType(Types.BIGINT).withSize(20).notNull());
        addMetadata(materialMd5, ColumnMetadata.named("material_md5").withIndex(4).ofType(Types.VARCHAR).withSize(32).notNull());
        addMetadata(mtime, ColumnMetadata.named("mtime").withIndex(3).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(videoMd5, ColumnMetadata.named("video_md5").withIndex(8).ofType(Types.VARCHAR).withSize(32).notNull());
    }

}

