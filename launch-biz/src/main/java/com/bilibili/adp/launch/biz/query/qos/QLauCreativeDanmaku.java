package com.bilibili.adp.launch.biz.query.qos;

import com.bilibili.adp.launch.biz.query.dos.LauCreativeDanmakuDo;
import com.querydsl.core.types.Path;
import com.querydsl.core.types.PathMetadata;
import com.querydsl.core.types.dsl.DateTimePath;
import com.querydsl.core.types.dsl.NumberPath;
import com.querydsl.core.types.dsl.StringPath;
import com.querydsl.sql.ColumnMetadata;

import javax.annotation.Generated;
import java.sql.Types;

import static com.querydsl.core.types.PathMetadataFactory.forVariable;

/**
 * QLauCreativeDanmaku is a Querydsl query type for LauCreativeDanmakuDo
 */
@Generated("com.querydsl.sql.codegen.MetaDataSerializer")
public class QLauCreativeDanmaku extends com.querydsl.sql.RelationalPathBase<LauCreativeDanmakuDo> {

    private static final long serialVersionUID = -2133656909;

    public static final QLauCreativeDanmaku lauCreativeDanmaku = new QLauCreativeDanmaku("lau_creative_danmaku");

    public final NumberPath<Integer> creativeId = createNumber("creativeId", Integer.class);

    public final DateTimePath<java.sql.Timestamp> ctime = createDateTime("ctime", java.sql.Timestamp.class);

    public final StringPath danmaku = createString("danmaku");

    public final NumberPath<Integer> id = createNumber("id", Integer.class);

    public final NumberPath<Integer> isDeleted = createNumber("isDeleted", Integer.class);

    public final DateTimePath<java.sql.Timestamp> mtime = createDateTime("mtime", java.sql.Timestamp.class);

    public final com.querydsl.sql.PrimaryKey<LauCreativeDanmakuDo> primary = createPrimaryKey(id);

    public QLauCreativeDanmaku(String variable) {
        super(LauCreativeDanmakuDo.class, forVariable(variable), "null", "lau_creative_danmaku");
        addMetadata();
    }

    public QLauCreativeDanmaku(String variable, String schema, String table) {
        super(LauCreativeDanmakuDo.class, forVariable(variable), schema, table);
        addMetadata();
    }

    public QLauCreativeDanmaku(String variable, String schema) {
        super(LauCreativeDanmakuDo.class, forVariable(variable), schema, "lau_creative_danmaku");
        addMetadata();
    }

    public QLauCreativeDanmaku(Path<? extends LauCreativeDanmakuDo> path) {
        super(path.getType(), path.getMetadata(), "null", "lau_creative_danmaku");
        addMetadata();
    }

    public QLauCreativeDanmaku(PathMetadata metadata) {
        super(LauCreativeDanmakuDo.class, metadata, "null", "lau_creative_danmaku");
        addMetadata();
    }

    public void addMetadata() {
        addMetadata(creativeId, ColumnMetadata.named("creative_id").withIndex(2).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(ctime, ColumnMetadata.named("ctime").withIndex(5).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(danmaku, ColumnMetadata.named("danmaku").withIndex(3).ofType(Types.VARCHAR).withSize(64).notNull());
        addMetadata(id, ColumnMetadata.named("id").withIndex(1).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(isDeleted, ColumnMetadata.named("is_deleted").withIndex(4).ofType(Types.TINYINT).withSize(3).notNull());
        addMetadata(mtime, ColumnMetadata.named("mtime").withIndex(6).ofType(Types.TIMESTAMP).withSize(19).notNull());
    }

}

