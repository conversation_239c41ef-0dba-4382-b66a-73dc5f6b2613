package com.bilibili.adp.launch.biz.ad_core.mybatis;

import com.bilibili.adp.launch.biz.pojo.LauCreativeQualificationPo;
import com.bilibili.adp.launch.biz.pojo.LauCreativeQualificationPoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface LauCreativeQualificationDao {
    long countByExample(LauCreativeQualificationPoExample example);

    int deleteByExample(LauCreativeQualificationPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(LauCreativeQualificationPo record);

    int insertBatch(List<LauCreativeQualificationPo> records);

    int insertUpdateBatch(List<LauCreativeQualificationPo> records);

    int insert(LauCreativeQualificationPo record);

    int insertUpdateSelective(LauCreativeQualificationPo record);

    int insertSelective(LauCreativeQualificationPo record);

    List<LauCreativeQualificationPo> selectByExample(LauCreativeQualificationPoExample example);

    LauCreativeQualificationPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") LauCreativeQualificationPo record, @Param("example") LauCreativeQualificationPoExample example);

    int updateByExample(@Param("record") LauCreativeQualificationPo record, @Param("example") LauCreativeQualificationPoExample example);

    int updateByPrimaryKeySelective(LauCreativeQualificationPo record);

    int updateByPrimaryKey(LauCreativeQualificationPo record);
}