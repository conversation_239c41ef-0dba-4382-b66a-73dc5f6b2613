package com.bilibili.adp.launch.biz.lau_dao;

import com.bilibili.adp.launch.biz.pojo.SigningBonusPayPo;
import com.bilibili.adp.launch.biz.pojo.SigningBonusPayPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface SigningBonusPayDao {
    long countByExample(SigningBonusPayPoExample example);

    int deleteByExample(SigningBonusPayPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(SigningBonusPayPo record);

    int insertBatch(List<SigningBonusPayPo> records);

    int insertUpdateBatch(List<SigningBonusPayPo> records);

    int insert(SigningBonusPayPo record);

    int insertUpdateSelective(SigningBonusPayPo record);

    int insertSelective(SigningBonusPayPo record);

    List<SigningBonusPayPo> selectByExample(SigningBonusPayPoExample example);

    SigningBonusPayPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") SigningBonusPayPo record, @Param("example") SigningBonusPayPoExample example);

    int updateByExample(@Param("record") SigningBonusPayPo record, @Param("example") SigningBonusPayPoExample example);

    int updateByPrimaryKeySelective(SigningBonusPayPo record);

    int updateByPrimaryKey(SigningBonusPayPo record);
}