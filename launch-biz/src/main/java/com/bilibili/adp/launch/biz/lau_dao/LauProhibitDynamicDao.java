package com.bilibili.adp.launch.biz.lau_dao;

import com.bilibili.adp.launch.biz.pojo.LauProhibitDynamicPo;
import com.bilibili.adp.launch.biz.pojo.LauProhibitDynamicPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface LauProhibitDynamicDao {
    long countByExample(LauProhibitDynamicPoExample example);

    int deleteByExample(LauProhibitDynamicPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(LauProhibitDynamicPo record);

    int insertBatch(List<LauProhibitDynamicPo> records);

    int insertUpdateBatch(List<LauProhibitDynamicPo> records);

    int insert(LauProhibitDynamicPo record);

    int insertUpdateSelective(LauProhibitDynamicPo record);

    int insertSelective(LauProhibitDynamicPo record);

    List<LauProhibitDynamicPo> selectByExample(LauProhibitDynamicPoExample example);

    LauProhibitDynamicPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") LauProhibitDynamicPo record, @Param("example") LauProhibitDynamicPoExample example);

    int updateByExample(@Param("record") LauProhibitDynamicPo record, @Param("example") LauProhibitDynamicPoExample example);

    int updateByPrimaryKeySelective(LauProhibitDynamicPo record);

    int updateByPrimaryKey(LauProhibitDynamicPo record);
}