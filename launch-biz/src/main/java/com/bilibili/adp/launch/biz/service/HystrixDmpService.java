package com.bilibili.adp.launch.biz.service;

import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.launch.api.service.IHystrixDmpService;
import com.bilibili.adp.launch.biz.hystrix.*;
import com.bilibili.dmp.dto.SimilarUpsDto;
import com.bilibili.dmp.dto.SoaBusinessCategoryDto;
import com.bilibili.dmp.dto.ThirdPeopleGroupDto;
import com.bilibili.dmp.soa.entity.BusinessCategory;
import com.bilibili.dmp.soa.entity.PeopleGroup;
import com.bilibili.dmp.soa.entity.PermissionGroup;
import com.bilibili.dmp.soa.entity.TargetTagsRequest;
import com.netflix.hystrix.HystrixCommand;
import com.netflix.hystrix.HystrixCommandGroupKey;
import com.netflix.hystrix.HystrixCommandProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.factory.config.AutowireCapableBeanFactory;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 熔断保护DMP服务。
 */
@Service
@Deprecated
public class HystrixDmpService implements IHystrixDmpService {

    private static final Logger LOGGER = LoggerFactory.getLogger(HystrixDmpService.class);

    private static final String GROUP_KEY = "dmp";

    @Value("${hystrix.dmp.timeoutInMilliseconds:3000}")
    private Integer timeoutInMilliseconds;

    @Value("${hystrix.dmp.sleepWindowInMilliseconds:60000}")
    private Integer sleepWindowInMilliseconds;

    @Autowired
    private AutowireCapableBeanFactory capableBeanFactory;

    @Override
    public Long getMaxCrowdCountFromDmp(TargetTagsRequest targetTagsRequest) {
        return generateGetMaxCrowdCountHystrixCommand(targetTagsRequest).execute();
    }

    private GetMaxCrowdCountHystrixCommand generateGetMaxCrowdCountHystrixCommand(TargetTagsRequest targetTagsRequest) {
        GetMaxCrowdCountHystrixCommand command = new GetMaxCrowdCountHystrixCommand(
                HystrixCommand.Setter.withGroupKey(HystrixCommandGroupKey.Factory.asKey(GROUP_KEY))
                        .andCommandPropertiesDefaults(HystrixCommandProperties.Setter()
                                .withExecutionTimeoutInMilliseconds(timeoutInMilliseconds)
                                .withCircuitBreakerSleepWindowInMilliseconds(sleepWindowInMilliseconds)),
                targetTagsRequest);
        capableBeanFactory.autowireBean(command);
        return command;
    }

    @Override
    public List<BusinessCategory> getAllBusinessCategory() {
        return generateGetAllBusinessCategoryHystrixCommand().execute();
    }

    private GetAllBusinessCategoryHystrixCommand generateGetAllBusinessCategoryHystrixCommand() {
        GetAllBusinessCategoryHystrixCommand command = new GetAllBusinessCategoryHystrixCommand(
                HystrixCommand.Setter.withGroupKey(HystrixCommandGroupKey.Factory.asKey(GROUP_KEY))
                        .andCommandPropertiesDefaults(HystrixCommandProperties.Setter()
                                .withExecutionTimeoutInMilliseconds(timeoutInMilliseconds)
                                .withCircuitBreakerSleepWindowInMilliseconds(sleepWindowInMilliseconds)));
        capableBeanFactory.autowireBean(command);
        return command;
    }

    @Override
    public List<SoaBusinessCategoryDto> getBusinessCategoryTree() {
        return generateGetBusinessCategoryTreeHystrixCommand().execute();
    }

    private GetBusinessCategoryTreeHystrixCommand generateGetBusinessCategoryTreeHystrixCommand() {
        GetBusinessCategoryTreeHystrixCommand command = new GetBusinessCategoryTreeHystrixCommand(
                HystrixCommand.Setter.withGroupKey(HystrixCommandGroupKey.Factory.asKey(GROUP_KEY))
                        .andCommandPropertiesDefaults(HystrixCommandProperties.Setter()
                                .withExecutionTimeoutInMilliseconds(timeoutInMilliseconds)
                                .withCircuitBreakerSleepWindowInMilliseconds(sleepWindowInMilliseconds)));
        capableBeanFactory.autowireBean(command);
        return command;
    }

    @Override
    public Map<PermissionGroup, List<PeopleGroup>> getAllPermissionGroupAndPeopleGroup() throws ServiceException {
        return generateGetAllPermissionGroupAndPeopleGroupHystrixCommand().execute();
    }

    private GetAllPermissionGroupAndPeopleGroupHystrixCommand generateGetAllPermissionGroupAndPeopleGroupHystrixCommand() {
        GetAllPermissionGroupAndPeopleGroupHystrixCommand command = new GetAllPermissionGroupAndPeopleGroupHystrixCommand(
                HystrixCommand.Setter.withGroupKey(HystrixCommandGroupKey.Factory.asKey(GROUP_KEY))
                        .andCommandPropertiesDefaults(HystrixCommandProperties.Setter()
                                .withExecutionTimeoutInMilliseconds(timeoutInMilliseconds)
                                .withCircuitBreakerSleepWindowInMilliseconds(sleepWindowInMilliseconds)));
        capableBeanFactory.autowireBean(command);
        return command;
    }

    @Override
    public List<PeopleGroup> getPeopleGroupByPermissionIdsAndAccountId(List<Integer> permissionIdList,
                                                                       Integer accountId) {
        return generateGetPeopleGroupByAccountIdHystrixCommand(permissionIdList, accountId).execute();
    }

    private GetPeopleGroupByAccountIdHystrixCommand generateGetPeopleGroupByAccountIdHystrixCommand(
            List<Integer> permissionIdList, Integer accountId) {
        GetPeopleGroupByAccountIdHystrixCommand command = new GetPeopleGroupByAccountIdHystrixCommand(
                HystrixCommand.Setter.withGroupKey(HystrixCommandGroupKey.Factory.asKey(GROUP_KEY))
                        .andCommandPropertiesDefaults(HystrixCommandProperties.Setter()
                                .withExecutionTimeoutInMilliseconds(timeoutInMilliseconds)
                                .withCircuitBreakerSleepWindowInMilliseconds(sleepWindowInMilliseconds)),
                permissionIdList,
                accountId);
        capableBeanFactory.autowireBean(command);
        return command;
    }

    @Override
    public boolean isPeopleGroupCircuitBreakerOpen() {
        return generateGetPeopleGroupByAccountIdHystrixCommand(null, null).isCircuitBreakerOpen()
                || generateGetPeopleGroupByGroupIdHystrixCommand(null).isCircuitBreakerOpen();
    }

    @Override
    public List<PeopleGroup> getPeopleGroupByPeopleGroupIds(List<Integer> peopleGroupIdList) {
        return generateGetPeopleGroupByGroupIdHystrixCommand(peopleGroupIdList).execute();
    }

    private GetPeopleGroupByGroupIdHystrixCommand generateGetPeopleGroupByGroupIdHystrixCommand(
            List<Integer> peopleGroupIdList) {
        GetPeopleGroupByGroupIdHystrixCommand command = new GetPeopleGroupByGroupIdHystrixCommand(
                HystrixCommand.Setter.withGroupKey(HystrixCommandGroupKey.Factory.asKey(GROUP_KEY))
                        .andCommandPropertiesDefaults(HystrixCommandProperties.Setter()
                                .withExecutionTimeoutInMilliseconds(timeoutInMilliseconds)
                                .withCircuitBreakerSleepWindowInMilliseconds(sleepWindowInMilliseconds)),
                peopleGroupIdList);
        capableBeanFactory.autowireBean(command);
        return command;
    }

    @Override
    public List<ThirdPeopleGroupDto> queryAllPublishedThirdPeopleGroup() {
        return generateQueryAllPublishedThirdPeopleGroupHystrixCommand().execute();
    }

    private QueryAllPublishedThirdPeopleGroupHystrixCommand generateQueryAllPublishedThirdPeopleGroupHystrixCommand() {
        QueryAllPublishedThirdPeopleGroupHystrixCommand command = new QueryAllPublishedThirdPeopleGroupHystrixCommand(
                HystrixCommand.Setter.withGroupKey(HystrixCommandGroupKey.Factory.asKey(GROUP_KEY))
                        .andCommandPropertiesDefaults(HystrixCommandProperties.Setter()
                                .withExecutionTimeoutInMilliseconds(timeoutInMilliseconds)
                                .withCircuitBreakerSleepWindowInMilliseconds(sleepWindowInMilliseconds)));
        capableBeanFactory.autowireBean(command);
        return command;
    }

    @Override
    public SimilarUpsDto getSimilarUpInfo(Long upMid) {

//        return SimilarUpsDto.builder()
//                .coreUps(Arrays.asList(1L,2L,3L))
//                .qualityUps(Arrays.asList(4L,5L,6L))
//                .potentialUps(Arrays.asList(7L,8L,9L))
//                .build();
        return generateGetSimilarUpInfoHystrixCommand(upMid).execute();
    }

    private GetSimilarUpInfoHystrixCommand generateGetSimilarUpInfoHystrixCommand(Long upMid) {
        GetSimilarUpInfoHystrixCommand command = new GetSimilarUpInfoHystrixCommand(
                HystrixCommand.Setter.withGroupKey(HystrixCommandGroupKey.Factory.asKey(GROUP_KEY))
                        .andCommandPropertiesDefaults(HystrixCommandProperties.Setter()
                                .withExecutionTimeoutInMilliseconds(timeoutInMilliseconds)
                                .withCircuitBreakerSleepWindowInMilliseconds(sleepWindowInMilliseconds)),upMid);
        capableBeanFactory.autowireBean(command);
        return command;
    }
}
