package com.bilibili.adp.launch.biz.lau_dao;

import com.bilibili.adp.launch.biz.pojo.LauCreativeLayoutPo;
import com.bilibili.adp.launch.biz.pojo.LauCreativeLayoutPoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface LauCreativeLayoutDao {
    long countByExample(LauCreativeLayoutPoExample example);

    int deleteByExample(LauCreativeLayoutPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(LauCreativeLayoutPo record);

    int insertSelective(LauCreativeLayoutPo record);

    List<LauCreativeLayoutPo> selectByExampleWithBLOBs(LauCreativeLayoutPoExample example);

    List<LauCreativeLayoutPo> selectByExample(LauCreativeLayoutPoExample example);

    LauCreativeLayoutPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") LauCreativeLayoutPo record, @Param("example") LauCreativeLayoutPoExample example);

    int updateByExampleWithBLOBs(@Param("record") LauCreativeLayoutPo record, @Param("example") LauCreativeLayoutPoExample example);

    int updateByExample(@Param("record") LauCreativeLayoutPo record, @Param("example") LauCreativeLayoutPoExample example);

    int updateByPrimaryKeySelective(LauCreativeLayoutPo record);

    int updateByPrimaryKeyWithBLOBs(LauCreativeLayoutPo record);

    int updateByPrimaryKey(LauCreativeLayoutPo record);
}