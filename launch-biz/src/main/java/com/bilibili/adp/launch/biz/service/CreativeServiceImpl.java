package com.bilibili.adp.launch.biz.service;

import com.bilibili.adp.common.DbTable;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.enums.AuditStatus;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.launch.api.common.CreativeSourceType;
import com.bilibili.adp.launch.api.common.CreativeStatus;
import com.bilibili.adp.launch.api.common.LaunchStatus;
import com.bilibili.adp.launch.api.creative.dto.Creative;
import com.bilibili.adp.launch.api.service.ICreativeService;
import com.bilibili.adp.launch.api.up.ICashUpOrderService;
import com.bilibili.adp.launch.api.up.ISigningBonusTrustOrderService;
import com.bilibili.adp.launch.api.up.IUpOrderService;
import com.bilibili.adp.launch.biz.common.LaunchUtil;
import com.bilibili.adp.launch.biz.ad_core.mybatis.UnitCreativeDao;
import com.bilibili.adp.launch.biz.exception.LaunchExceptionCode;
import com.bilibili.adp.launch.biz.po.CreativeReportPo;
import com.bilibili.adp.launch.biz.po.UnitCreativePo;
import com.bilibili.adp.launch.biz.pojo.LauUnitCreativePo;
import com.bilibili.adp.log.dto.OperationType;
import com.bilibili.adp.log.service.ILogOperateService;
import com.bilibili.adp.log.service.ILogService;
import com.bilibili.fly.soa.ISoaFlyOrderService;
import com.bilibili.mas.common.utils.BeanHelper;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.activation.MimetypesFileTypeMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.bilibili.adp.launch.biz.config.mysql.AdPlatformMysqlConfig.AD_CORE_DYNAMIC_TM;

/**
 * Created by walker on 16/9/3.
 */
@Service
@Deprecated
public class CreativeServiceImpl implements ICreativeService {

    private static final Logger LOGGER = LoggerFactory.getLogger(CreativeServiceImpl.class);
    private static MimetypesFileTypeMap mimetypesFileTypeMap = new MimetypesFileTypeMap();

    static {
        mimetypesFileTypeMap.addMimeTypes("image/png png PNG");
    }

    @Value("${bfs.categoryname}")
    private String categoryName;
    @Value("${lau.creative.query.stag.url:}")
    private String queryStagUrl;

    @Autowired
    private UnitCreativeDao unitCreativeDao;
    @Autowired
    private ILogService logService;
    @Autowired
    private ILogOperateService logOperateService;
    @Autowired
    private LauCreativeServiceDelegate lauCreativeServiceDelegate;
    @Autowired
    private IUpOrderService upOrderService;
    @Autowired
    private ICashUpOrderService cashUpOrderService;
    @Autowired
    private ISigningBonusTrustOrderService signingBonusTrustOrderService;
    @Autowired
    private ISoaFlyOrderService soaFlyOrderService;

    @Transactional(value = AD_CORE_DYNAMIC_TM, rollbackFor = Exception.class)
    @Override
    public void auditCreative(Operator operator, List<Creative> creatives) throws ServiceException {
        if (CollectionUtils.isEmpty(creatives)) {
            throw new ServiceException(LaunchExceptionCode.CREATIVE_AUDIT_IDS_NULL);
        }
        List<Integer> creativeIds = Lists.newArrayList();
        List<UnitCreativePo> creativePos = Lists.newArrayList();
        for (Creative dto : creatives) {
            creativeIds.add(dto.getCreativeId());
            if (dto.getAuditStatus() == AuditStatus.REJECT.getCode()) {
                if ((Strings.isNullOrEmpty(dto.getReason()) || Strings.isNullOrEmpty(dto.getReason().trim()))) {
                    throw new ServiceException(LaunchExceptionCode.CREATIVE_AUDIT_REASON_NULL);
                }
            }

            Integer creativeStatus = LaunchUtil.convertToCreativeStatus(AuditStatus.getByCode(dto.getAuditStatus()));
            creativePos.add(UnitCreativePo.builder()
                    .creativeId(dto.getCreativeId()).version(dto.getVersion()).reason(dto.getReason())
                    .auditStatus(dto.getAuditStatus())
                    .creativeStatus(creativeStatus)
                    .campaignId(dto.getCampaignId())
                    .unitId(dto.getUnitId())
                    .bilibiliUserId(dto.getBilibiliUserId())
                    .build());
        }

        lauCreativeServiceDelegate.saveCreativeImageAuditLog(BeanHelper.copyForBeans(creativePos, LauUnitCreativePo::new));

        List<CreativeReportPo> cpos = unitCreativeDao.getByIds(creativeIds);
        Map<Integer, List<Integer>> creativeStatusMap = cpos.stream().collect(Collectors.groupingBy(CreativeReportPo::getStatus, Collectors.mapping(CreativeReportPo::getCreativeId, Collectors.toList())));
        Map<Integer, CreativeReportPo> creativeMap = cpos.stream().collect(Collectors.toMap(CreativeReportPo::getCreativeId, c -> c));

        List<UnitCreativePo> auditPos = null;
        List<Integer> accountIds = null;

        for (Entry<Integer, List<Integer>> e : creativeStatusMap.entrySet()) {
            List<Integer> auditIds = creativeStatusMap.get(e.getKey());
            auditPos = creativePos
                    .stream()
                    .filter(c -> auditIds.contains(c.getCreativeId()))
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(auditPos)) {
                continue;
            }

            if (e.getKey() == LaunchStatus.STOP.getCode()) {
                auditPos.forEach(po -> {
                    po.setCreativeStatus(CreativeStatus.PAUSED.getCode());
                });
            }

            int result = unitCreativeDao.auditCreative(auditPos);

            if (result < 1) {
                throw new ServiceException(LaunchExceptionCode.CREATIVE_UPDATE_AUDIT_STATUS_FAILED);
            }

            auditUpCreativeOrder(operator, auditPos, creativeMap);

            logService.insertLog(DbTable.LAU_UNIT_CREATIVE.getName(),
                    OperationType.UPDATE, operator,
                    String.format("{creativeIds:%s, auditStatus:%s, creativeStatus:%d}", auditIds,
                            auditPos.get(0).getAuditStatus(),
                            LaunchUtil.convertToCreativeStatus(AuditStatus.getByCode(auditPos.get(0).getAuditStatus()))));

            accountIds = auditIds.stream().map(id -> creativeMap.get(id).getAccountId()).collect(Collectors.toList());

            List<Integer> auditCreativeIds = auditPos.stream().map(UnitCreativePo::getCreativeId).collect(Collectors.toList());
            logOperateService.addBatchUpdateStatusLog(DbTable.LAU_UNIT_CREATIVE, operator, auditPos, auditCreativeIds, accountIds);
        }
    }

    private void auditUpCreativeOrder(Operator operator, List<UnitCreativePo> auditPos, Map<Integer, CreativeReportPo> creativeMap) {
        List<UnitCreativePo> upAuditPos = auditPos.stream()
                .filter(creativePo -> creativeMap.containsKey(creativePo.getCreativeId()))
                .filter(creativePo -> CreativeSourceType.PERSONAL_FLY.getCode().equals(creativeMap.get(creativePo.getCreativeId()).getBilibiliUserId()))
                .collect(Collectors.toList());
        List<UnitCreativePo> cashAuditPos = auditPos.stream()
                .filter(creativePo -> creativeMap.containsKey(creativePo.getCreativeId()))
                .filter(creativePo -> CreativeSourceType.CASH_PERSONAL_FLY.getCode().equals(creativeMap.get(creativePo.getCreativeId()).getBilibiliUserId())
                        || CreativeSourceType.SIGNING_BONUS_ORDER_FLY.getCode().equals(creativeMap.get(creativePo.getCreativeId()).getBilibiliUserId()))
                .collect(Collectors.toList());
        List<UnitCreativePo> trustAuditPos = auditPos.stream()
                .filter(creativePo -> creativeMap.containsKey(creativePo.getCreativeId()))
                .filter(creativePo -> CreativeSourceType.SIGNING_BONUS_TRUST_FLY.getCode().equals(creativeMap.get(creativePo.getCreativeId()).getBilibiliUserId()))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(upAuditPos)) {
            upAuditPos
                    .stream()
                    .filter(creativePo -> Objects.equals(AuditStatus.ACCEPT.getCode(), creativePo.getAuditStatus()))
                    .forEach(creativePo -> {
                                upOrderService.cmAuditPass(operator, creativePo.getCreativeId());
                                soaFlyOrderService.cmAuditPass(creativePo.getCampaignId(), creativePo.getCreativeId());
                    });
            upAuditPos
                    .stream()
                    .filter(creativePo -> Objects.equals(AuditStatus.REJECT.getCode(), creativePo.getAuditStatus()))
                    .forEach(creativePo -> {
                        upOrderService.cmAuditRejectWithoutValidate(operator, creativePo.getCreativeId(), creativePo.getReason());
                        soaFlyOrderService.cmAuditRejectWithoutValidate(creativePo.getCampaignId(), creativePo.getCreativeId(), creativePo.getReason());
                    });
        }
        if (!CollectionUtils.isEmpty(cashAuditPos)) {
            for (UnitCreativePo cashAuditPo : cashAuditPos) {
                if (Objects.equals(AuditStatus.ACCEPT.getCode(), cashAuditPo.getAuditStatus())) {
                    cashUpOrderService.cmAuditPass(operator, cashAuditPo.getCreativeId(), cashAuditPo.getCampaignId());
                    soaFlyOrderService.cmAuditPass(cashAuditPo.getCampaignId(), cashAuditPo.getCreativeId());
                } else if (Objects.equals(AuditStatus.REJECT.getCode(), cashAuditPo.getAuditStatus())) {
                    //起飞订单
                    cashUpOrderService.cmAuditRejectWithoutValidate(operator, cashAuditPo.getCreativeId(), cashAuditPo.getCampaignId(), cashAuditPo.getReason());
                    soaFlyOrderService.cmAuditRejectWithoutValidate(cashAuditPo.getCampaignId(), cashAuditPo.getCreativeId(), cashAuditPo.getReason());
                }
            }
        }
        if (!CollectionUtils.isEmpty(trustAuditPos)){
            for (UnitCreativePo trustAuditPo : trustAuditPos) {
                if (Objects.equals(AuditStatus.ACCEPT.getCode(), trustAuditPo.getAuditStatus())){
                    signingBonusTrustOrderService.cmAuditPass(operator, trustAuditPo.getCreativeId());
                    soaFlyOrderService.cmAuditPass(trustAuditPo.getCampaignId(), trustAuditPo.getCreativeId());
                }else {
                    signingBonusTrustOrderService.cmAuditReject(operator, trustAuditPo.getCreativeId(), trustAuditPo.getReason());
                    soaFlyOrderService.cmAuditRejectWithoutValidate(trustAuditPo.getCampaignId(), trustAuditPo.getCreativeId(), trustAuditPo.getReason());

                }
            }
        }
    }

}
