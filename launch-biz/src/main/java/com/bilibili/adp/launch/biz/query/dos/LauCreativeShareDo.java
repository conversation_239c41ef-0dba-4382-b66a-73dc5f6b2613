package com.bilibili.adp.launch.biz.query.dos;

import javax.annotation.Generated;
import java.io.Serializable;

/**
 * LauCreativeShareDo is a Querydsl bean type
 */
@Generated("com.querydsl.codegen.BeanSerializer")
public class LauCreativeShareDo implements Serializable {

    private Integer creativeId;

    private java.sql.Timestamp ctime;

    private Integer id;

    private String imageMd5;

    private String imageUrl;

    private Integer isDeleted;

    private java.sql.Timestamp mtime;

    private Integer state;

    private String subtitle;

    private String title;

    public Integer getCreativeId() {
        return creativeId;
    }

    public void setCreativeId(Integer creativeId) {
        this.creativeId = creativeId;
    }

    public java.sql.Timestamp getCtime() {
        return ctime;
    }

    public void setCtime(java.sql.Timestamp ctime) {
        this.ctime = ctime;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getImageMd5() {
        return imageMd5;
    }

    public void setImageMd5(String imageMd5) {
        this.imageMd5 = imageMd5;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public java.sql.Timestamp getMtime() {
        return mtime;
    }

    public void setMtime(java.sql.Timestamp mtime) {
        this.mtime = mtime;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public String getSubtitle() {
        return subtitle;
    }

    public void setSubtitle(String subtitle) {
        this.subtitle = subtitle;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    @Override
    public String toString() {
        return "creativeId = " + creativeId + ", ctime = " + ctime + ", id = " + id + ", imageMd5 = " + imageMd5 + ", imageUrl = " + imageUrl + ", isDeleted = " + isDeleted + ", mtime = " + mtime + ", state = " + state + ", subtitle = " + subtitle + ", title = " + title;
    }

}

