package com.bilibili.adp.launch.biz.query.dos;

import javax.annotation.Generated;
import java.io.Serializable;

/**
 * LauCreativeAutoDo is a Querydsl bean type
 */
@Generated("com.querydsl.codegen.BeanSerializer")
public class LauCreativeAutoDo implements Serializable {

    private Integer creativeId;

    private java.sql.Timestamp ctime;

    private Integer id;

    private Integer isDeleted;

    private java.sql.Timestamp mtime;

    private Integer refreshType;

    private String sourceImageMd5;

    private String sourceImageUrl;

    public Integer getCreativeId() {
        return creativeId;
    }

    public void setCreativeId(Integer creativeId) {
        this.creativeId = creativeId;
    }

    public java.sql.Timestamp getCtime() {
        return ctime;
    }

    public void setCtime(java.sql.Timestamp ctime) {
        this.ctime = ctime;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public java.sql.Timestamp getMtime() {
        return mtime;
    }

    public void setMtime(java.sql.Timestamp mtime) {
        this.mtime = mtime;
    }

    public Integer getRefreshType() {
        return refreshType;
    }

    public void setRefreshType(Integer refreshType) {
        this.refreshType = refreshType;
    }

    public String getSourceImageMd5() {
        return sourceImageMd5;
    }

    public void setSourceImageMd5(String sourceImageMd5) {
        this.sourceImageMd5 = sourceImageMd5;
    }

    public String getSourceImageUrl() {
        return sourceImageUrl;
    }

    public void setSourceImageUrl(String sourceImageUrl) {
        this.sourceImageUrl = sourceImageUrl;
    }

    @Override
    public String toString() {
        return "creativeId = " + creativeId + ", ctime = " + ctime + ", id = " + id + ", isDeleted = " + isDeleted + ", mtime = " + mtime + ", refreshType = " + refreshType + ", sourceImageMd5 = " + sourceImageMd5 + ", sourceImageUrl = " + sourceImageUrl;
    }

}

