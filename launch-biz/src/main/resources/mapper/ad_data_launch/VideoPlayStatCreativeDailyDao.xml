<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.adp.launch.biz.ad_data.dao.VideoPlayStatCreativeDailyDao">
  <resultMap id="BaseResultMap" type="com.bilibili.adp.launch.biz.ad_data.po.VideoPlayStatCreativeDailyPo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="campaign_id" jdbcType="INTEGER" property="campaignId" />
    <result column="unit_id" jdbcType="INTEGER" property="unitId" />
    <result column="creative_id" jdbcType="INTEGER" property="creativeId" />
    <result column="fly_type" jdbcType="INTEGER" property="flyType" />
    <result column="avid" jdbcType="BIGINT" property="avid" />
    <result column="stat_date" jdbcType="DATE" property="statDate" />
    <result column="play_count" jdbcType="INTEGER" property="playCount" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="stat_hour" jdbcType="VARCHAR" property="statHour" />
    <result column="play_cost" jdbcType="BIGINT" property="playCost" />
    <result column="is_day" jdbcType="INTEGER" property="isDay" />
    <result column="play_show_count" jdbcType="INTEGER" property="playShowCount" />
    <result column="like_count" jdbcType="INTEGER" property="likeCount" />
  </resultMap>
  <resultMap id="AliasBaseResultMap" type="com.bilibili.adp.launch.biz.ad_data.po.VideoPlayStatCreativeDailyPo">
    <id column="video_play_stat_creative_daily_id" jdbcType="INTEGER" property="id" />
    <result column="video_play_stat_creative_daily_campaign_id" jdbcType="INTEGER" property="campaignId" />
    <result column="video_play_stat_creative_daily_unit_id" jdbcType="INTEGER" property="unitId" />
    <result column="video_play_stat_creative_daily_creative_id" jdbcType="INTEGER" property="creativeId" />
    <result column="video_play_stat_creative_daily_fly_type" jdbcType="INTEGER" property="flyType" />
    <result column="video_play_stat_creative_daily_avid" jdbcType="BIGINT" property="avid" />
    <result column="video_play_stat_creative_daily_stat_date" jdbcType="DATE" property="statDate" />
    <result column="video_play_stat_creative_daily_play_count" jdbcType="INTEGER" property="playCount" />
    <result column="video_play_stat_creative_daily_version" jdbcType="INTEGER" property="version" />
    <result column="video_play_stat_creative_daily_ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="video_play_stat_creative_daily_mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="video_play_stat_creative_daily_is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="video_play_stat_creative_daily_stat_hour" jdbcType="VARCHAR" property="statHour" />
    <result column="video_play_stat_creative_daily_play_cost" jdbcType="BIGINT" property="playCost" />
    <result column="video_play_stat_creative_daily_is_day" jdbcType="INTEGER" property="isDay" />
    <result column="video_play_stat_creative_daily_play_show_count" jdbcType="INTEGER" property="playShowCount" />
    <result column="video_play_stat_creative_daily_like_count" jdbcType="INTEGER" property="likeCount" />
  </resultMap>
  <sql id="Alias_Base_Column_List">
    ${alias}.id as video_play_stat_creative_daily_id, ${alias}.campaign_id as video_play_stat_creative_daily_campaign_id, 
    ${alias}.unit_id as video_play_stat_creative_daily_unit_id, ${alias}.creative_id as video_play_stat_creative_daily_creative_id, 
    ${alias}.fly_type as video_play_stat_creative_daily_fly_type, ${alias}.avid as video_play_stat_creative_daily_avid, 
    ${alias}.stat_date as video_play_stat_creative_daily_stat_date, ${alias}.play_count as video_play_stat_creative_daily_play_count, 
    ${alias}.version as video_play_stat_creative_daily_version, ${alias}.ctime as video_play_stat_creative_daily_ctime, 
    ${alias}.mtime as video_play_stat_creative_daily_mtime, ${alias}.is_deleted as video_play_stat_creative_daily_is_deleted, 
    ${alias}.stat_hour as video_play_stat_creative_daily_stat_hour, ${alias}.play_cost as video_play_stat_creative_daily_play_cost, 
    ${alias}.is_day as video_play_stat_creative_daily_is_day, ${alias}.play_show_count as video_play_stat_creative_daily_play_show_count, 
    ${alias}.like_count as video_play_stat_creative_daily_like_count
  </sql>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, campaign_id, unit_id, creative_id, fly_type, avid, stat_date, play_count, version, 
    ctime, mtime, is_deleted, stat_hour, play_cost, is_day, play_show_count, like_count
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.adp.launch.biz.ad_data.po.VideoPlayStatCreativeDailyPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from video_play_stat_creative_daily
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from video_play_stat_creative_daily
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from video_play_stat_creative_daily
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.adp.launch.biz.ad_data.po.VideoPlayStatCreativeDailyPoExample">
    delete from video_play_stat_creative_daily
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.adp.launch.biz.ad_data.po.VideoPlayStatCreativeDailyPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into video_play_stat_creative_daily (campaign_id, unit_id, creative_id, 
      fly_type, avid, stat_date, 
      play_count, version, ctime, 
      mtime, is_deleted, stat_hour, 
      play_cost, is_day, play_show_count, 
      like_count)
    values (#{campaignId,jdbcType=INTEGER}, #{unitId,jdbcType=INTEGER}, #{creativeId,jdbcType=INTEGER}, 
      #{flyType,jdbcType=INTEGER}, #{avid,jdbcType=BIGINT}, #{statDate,jdbcType=DATE}, 
      #{playCount,jdbcType=INTEGER}, #{version,jdbcType=INTEGER}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=TINYINT}, #{statHour,jdbcType=VARCHAR}, 
      #{playCost,jdbcType=BIGINT}, #{isDay,jdbcType=INTEGER}, #{playShowCount,jdbcType=INTEGER}, 
      #{likeCount,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.adp.launch.biz.ad_data.po.VideoPlayStatCreativeDailyPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into video_play_stat_creative_daily
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="campaignId != null">
        campaign_id,
      </if>
      <if test="unitId != null">
        unit_id,
      </if>
      <if test="creativeId != null">
        creative_id,
      </if>
      <if test="flyType != null">
        fly_type,
      </if>
      <if test="avid != null">
        avid,
      </if>
      <if test="statDate != null">
        stat_date,
      </if>
      <if test="playCount != null">
        play_count,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="statHour != null">
        stat_hour,
      </if>
      <if test="playCost != null">
        play_cost,
      </if>
      <if test="isDay != null">
        is_day,
      </if>
      <if test="playShowCount != null">
        play_show_count,
      </if>
      <if test="likeCount != null">
        like_count,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="campaignId != null">
        #{campaignId,jdbcType=INTEGER},
      </if>
      <if test="unitId != null">
        #{unitId,jdbcType=INTEGER},
      </if>
      <if test="creativeId != null">
        #{creativeId,jdbcType=INTEGER},
      </if>
      <if test="flyType != null">
        #{flyType,jdbcType=INTEGER},
      </if>
      <if test="avid != null">
        #{avid,jdbcType=BIGINT},
      </if>
      <if test="statDate != null">
        #{statDate,jdbcType=DATE},
      </if>
      <if test="playCount != null">
        #{playCount,jdbcType=INTEGER},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="statHour != null">
        #{statHour,jdbcType=VARCHAR},
      </if>
      <if test="playCost != null">
        #{playCost,jdbcType=BIGINT},
      </if>
      <if test="isDay != null">
        #{isDay,jdbcType=INTEGER},
      </if>
      <if test="playShowCount != null">
        #{playShowCount,jdbcType=INTEGER},
      </if>
      <if test="likeCount != null">
        #{likeCount,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.adp.launch.biz.ad_data.po.VideoPlayStatCreativeDailyPoExample" resultType="java.lang.Long">
    select count(*) from video_play_stat_creative_daily
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update video_play_stat_creative_daily
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.campaignId != null">
        campaign_id = #{record.campaignId,jdbcType=INTEGER},
      </if>
      <if test="record.unitId != null">
        unit_id = #{record.unitId,jdbcType=INTEGER},
      </if>
      <if test="record.creativeId != null">
        creative_id = #{record.creativeId,jdbcType=INTEGER},
      </if>
      <if test="record.flyType != null">
        fly_type = #{record.flyType,jdbcType=INTEGER},
      </if>
      <if test="record.avid != null">
        avid = #{record.avid,jdbcType=BIGINT},
      </if>
      <if test="record.statDate != null">
        stat_date = #{record.statDate,jdbcType=DATE},
      </if>
      <if test="record.playCount != null">
        play_count = #{record.playCount,jdbcType=INTEGER},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.statHour != null">
        stat_hour = #{record.statHour,jdbcType=VARCHAR},
      </if>
      <if test="record.playCost != null">
        play_cost = #{record.playCost,jdbcType=BIGINT},
      </if>
      <if test="record.isDay != null">
        is_day = #{record.isDay,jdbcType=INTEGER},
      </if>
      <if test="record.playShowCount != null">
        play_show_count = #{record.playShowCount,jdbcType=INTEGER},
      </if>
      <if test="record.likeCount != null">
        like_count = #{record.likeCount,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update video_play_stat_creative_daily
    set id = #{record.id,jdbcType=INTEGER},
      campaign_id = #{record.campaignId,jdbcType=INTEGER},
      unit_id = #{record.unitId,jdbcType=INTEGER},
      creative_id = #{record.creativeId,jdbcType=INTEGER},
      fly_type = #{record.flyType,jdbcType=INTEGER},
      avid = #{record.avid,jdbcType=BIGINT},
      stat_date = #{record.statDate,jdbcType=DATE},
      play_count = #{record.playCount,jdbcType=INTEGER},
      version = #{record.version,jdbcType=INTEGER},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      stat_hour = #{record.statHour,jdbcType=VARCHAR},
      play_cost = #{record.playCost,jdbcType=BIGINT},
      is_day = #{record.isDay,jdbcType=INTEGER},
      play_show_count = #{record.playShowCount,jdbcType=INTEGER},
      like_count = #{record.likeCount,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.adp.launch.biz.ad_data.po.VideoPlayStatCreativeDailyPo">
    update video_play_stat_creative_daily
    <set>
      <if test="campaignId != null">
        campaign_id = #{campaignId,jdbcType=INTEGER},
      </if>
      <if test="unitId != null">
        unit_id = #{unitId,jdbcType=INTEGER},
      </if>
      <if test="creativeId != null">
        creative_id = #{creativeId,jdbcType=INTEGER},
      </if>
      <if test="flyType != null">
        fly_type = #{flyType,jdbcType=INTEGER},
      </if>
      <if test="avid != null">
        avid = #{avid,jdbcType=BIGINT},
      </if>
      <if test="statDate != null">
        stat_date = #{statDate,jdbcType=DATE},
      </if>
      <if test="playCount != null">
        play_count = #{playCount,jdbcType=INTEGER},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="statHour != null">
        stat_hour = #{statHour,jdbcType=VARCHAR},
      </if>
      <if test="playCost != null">
        play_cost = #{playCost,jdbcType=BIGINT},
      </if>
      <if test="isDay != null">
        is_day = #{isDay,jdbcType=INTEGER},
      </if>
      <if test="playShowCount != null">
        play_show_count = #{playShowCount,jdbcType=INTEGER},
      </if>
      <if test="likeCount != null">
        like_count = #{likeCount,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.adp.launch.biz.ad_data.po.VideoPlayStatCreativeDailyPo">
    update video_play_stat_creative_daily
    set campaign_id = #{campaignId,jdbcType=INTEGER},
      unit_id = #{unitId,jdbcType=INTEGER},
      creative_id = #{creativeId,jdbcType=INTEGER},
      fly_type = #{flyType,jdbcType=INTEGER},
      avid = #{avid,jdbcType=BIGINT},
      stat_date = #{statDate,jdbcType=DATE},
      play_count = #{playCount,jdbcType=INTEGER},
      version = #{version,jdbcType=INTEGER},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      stat_hour = #{statHour,jdbcType=VARCHAR},
      play_cost = #{playCost,jdbcType=BIGINT},
      is_day = #{isDay,jdbcType=INTEGER},
      play_show_count = #{playShowCount,jdbcType=INTEGER},
      like_count = #{likeCount,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.adp.launch.biz.ad_data.po.VideoPlayStatCreativeDailyPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into video_play_stat_creative_daily (campaign_id, unit_id, creative_id, 
      fly_type, avid, stat_date, 
      play_count, version, ctime, 
      mtime, is_deleted, stat_hour, 
      play_cost, is_day, play_show_count, 
      like_count)
    values (#{campaignId,jdbcType=INTEGER}, #{unitId,jdbcType=INTEGER}, #{creativeId,jdbcType=INTEGER}, 
      #{flyType,jdbcType=INTEGER}, #{avid,jdbcType=BIGINT}, #{statDate,jdbcType=DATE}, 
      #{playCount,jdbcType=INTEGER}, #{version,jdbcType=INTEGER}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=TINYINT}, #{statHour,jdbcType=VARCHAR}, 
      #{playCost,jdbcType=BIGINT}, #{isDay,jdbcType=INTEGER}, #{playShowCount,jdbcType=INTEGER}, 
      #{likeCount,jdbcType=INTEGER})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      campaign_id = values(campaign_id),
      unit_id = values(unit_id),
      creative_id = values(creative_id),
      fly_type = values(fly_type),
      avid = values(avid),
      stat_date = values(stat_date),
      play_count = values(play_count),
      version = values(version),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
      stat_hour = values(stat_hour),
      play_cost = values(play_cost),
      is_day = values(is_day),
      play_show_count = values(play_show_count),
      like_count = values(like_count),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      video_play_stat_creative_daily
      (campaign_id,unit_id,creative_id,fly_type,avid,stat_date,play_count,version,ctime,mtime,is_deleted,stat_hour,play_cost,is_day,play_show_count,like_count)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.campaignId,jdbcType=INTEGER},
        #{item.unitId,jdbcType=INTEGER},
        #{item.creativeId,jdbcType=INTEGER},
        #{item.flyType,jdbcType=INTEGER},
        #{item.avid,jdbcType=BIGINT},
        #{item.statDate,jdbcType=DATE},
        #{item.playCount,jdbcType=INTEGER},
        #{item.version,jdbcType=INTEGER},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.statHour,jdbcType=VARCHAR},
        #{item.playCost,jdbcType=BIGINT},
        #{item.isDay,jdbcType=INTEGER},
        #{item.playShowCount,jdbcType=INTEGER},
        #{item.likeCount,jdbcType=INTEGER},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      video_play_stat_creative_daily
      (campaign_id,unit_id,creative_id,fly_type,avid,stat_date,play_count,version,ctime,mtime,is_deleted,stat_hour,play_cost,is_day,play_show_count,like_count)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.campaignId,jdbcType=INTEGER},
        #{item.unitId,jdbcType=INTEGER},
        #{item.creativeId,jdbcType=INTEGER},
        #{item.flyType,jdbcType=INTEGER},
        #{item.avid,jdbcType=BIGINT},
        #{item.statDate,jdbcType=DATE},
        #{item.playCount,jdbcType=INTEGER},
        #{item.version,jdbcType=INTEGER},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.statHour,jdbcType=VARCHAR},
        #{item.playCost,jdbcType=BIGINT},
        #{item.isDay,jdbcType=INTEGER},
        #{item.playShowCount,jdbcType=INTEGER},
        #{item.likeCount,jdbcType=INTEGER},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      campaign_id = values(campaign_id),
      unit_id = values(unit_id),
      creative_id = values(creative_id),
      fly_type = values(fly_type),
      avid = values(avid),
      stat_date = values(stat_date),
      play_count = values(play_count),
      version = values(version),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
      stat_hour = values(stat_hour),
      play_cost = values(play_cost),
      is_day = values(is_day),
      play_show_count = values(play_show_count),
      like_count = values(like_count),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.adp.launch.biz.ad_data.po.VideoPlayStatCreativeDailyPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into video_play_stat_creative_daily
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="campaignId != null">
        campaign_id,
      </if>
      <if test="unitId != null">
        unit_id,
      </if>
      <if test="creativeId != null">
        creative_id,
      </if>
      <if test="flyType != null">
        fly_type,
      </if>
      <if test="avid != null">
        avid,
      </if>
      <if test="statDate != null">
        stat_date,
      </if>
      <if test="playCount != null">
        play_count,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="statHour != null">
        stat_hour,
      </if>
      <if test="playCost != null">
        play_cost,
      </if>
      <if test="isDay != null">
        is_day,
      </if>
      <if test="playShowCount != null">
        play_show_count,
      </if>
      <if test="likeCount != null">
        like_count,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="campaignId != null">
        #{campaignId,jdbcType=INTEGER},
      </if>
      <if test="unitId != null">
        #{unitId,jdbcType=INTEGER},
      </if>
      <if test="creativeId != null">
        #{creativeId,jdbcType=INTEGER},
      </if>
      <if test="flyType != null">
        #{flyType,jdbcType=INTEGER},
      </if>
      <if test="avid != null">
        #{avid,jdbcType=BIGINT},
      </if>
      <if test="statDate != null">
        #{statDate,jdbcType=DATE},
      </if>
      <if test="playCount != null">
        #{playCount,jdbcType=INTEGER},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="statHour != null">
        #{statHour,jdbcType=VARCHAR},
      </if>
      <if test="playCost != null">
        #{playCost,jdbcType=BIGINT},
      </if>
      <if test="isDay != null">
        #{isDay,jdbcType=INTEGER},
      </if>
      <if test="playShowCount != null">
        #{playShowCount,jdbcType=INTEGER},
      </if>
      <if test="likeCount != null">
        #{likeCount,jdbcType=INTEGER},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="campaignId != null">
        campaign_id = values(campaign_id),
      </if>
      <if test="unitId != null">
        unit_id = values(unit_id),
      </if>
      <if test="creativeId != null">
        creative_id = values(creative_id),
      </if>
      <if test="flyType != null">
        fly_type = values(fly_type),
      </if>
      <if test="avid != null">
        avid = values(avid),
      </if>
      <if test="statDate != null">
        stat_date = values(stat_date),
      </if>
      <if test="playCount != null">
        play_count = values(play_count),
      </if>
      <if test="version != null">
        version = values(version),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="statHour != null">
        stat_hour = values(stat_hour),
      </if>
      <if test="playCost != null">
        play_cost = values(play_cost),
      </if>
      <if test="isDay != null">
        is_day = values(is_day),
      </if>
      <if test="playShowCount != null">
        play_show_count = values(play_show_count),
      </if>
      <if test="likeCount != null">
        like_count = values(like_count),
      </if>
    </trim>
  </insert>
</mapper>