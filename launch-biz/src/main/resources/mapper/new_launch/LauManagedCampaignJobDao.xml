<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.adp.launch.biz.lau_dao.LauManagedCampaignJobDao">
  <resultMap id="BaseResultMap" type="com.bilibili.adp.launch.biz.pojo.LauManagedCampaignJobPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="account_id" jdbcType="INTEGER" property="accountId" />
    <result column="operator_name" jdbcType="VARCHAR" property="operatorName" />
    <result column="campaign_id" jdbcType="INTEGER" property="campaignId" />
    <result column="ocpx_target" jdbcType="TINYINT" property="ocpxTarget" />
    <result column="cost_price" jdbcType="INTEGER" property="costPrice" />
    <result column="is_long_term_launch" jdbcType="TINYINT" property="isLongTermLaunch" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="batch_no" jdbcType="INTEGER" property="batchNo" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="app_package_id" jdbcType="INTEGER" property="appPackageId" />
  </resultMap>
  <resultMap id="AliasBaseResultMap" type="com.bilibili.adp.launch.biz.pojo.LauManagedCampaignJobPo">
    <id column="lau_managed_campaign_job_id" jdbcType="BIGINT" property="id" />
    <result column="lau_managed_campaign_job_account_id" jdbcType="INTEGER" property="accountId" />
    <result column="lau_managed_campaign_job_operator_name" jdbcType="VARCHAR" property="operatorName" />
    <result column="lau_managed_campaign_job_campaign_id" jdbcType="INTEGER" property="campaignId" />
    <result column="lau_managed_campaign_job_ocpx_target" jdbcType="TINYINT" property="ocpxTarget" />
    <result column="lau_managed_campaign_job_cost_price" jdbcType="INTEGER" property="costPrice" />
    <result column="lau_managed_campaign_job_is_long_term_launch" jdbcType="TINYINT" property="isLongTermLaunch" />
    <result column="lau_managed_campaign_job_status" jdbcType="TINYINT" property="status" />
    <result column="lau_managed_campaign_job_batch_no" jdbcType="INTEGER" property="batchNo" />
    <result column="lau_managed_campaign_job_ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="lau_managed_campaign_job_mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="lau_managed_campaign_job_app_package_id" jdbcType="INTEGER" property="appPackageId" />
  </resultMap>
  <sql id="Alias_Base_Column_List">
    ${alias}.id as lau_managed_campaign_job_id, ${alias}.account_id as lau_managed_campaign_job_account_id, 
    ${alias}.operator_name as lau_managed_campaign_job_operator_name, ${alias}.campaign_id as lau_managed_campaign_job_campaign_id, 
    ${alias}.ocpx_target as lau_managed_campaign_job_ocpx_target, ${alias}.cost_price as lau_managed_campaign_job_cost_price, 
    ${alias}.is_long_term_launch as lau_managed_campaign_job_is_long_term_launch, ${alias}.status as lau_managed_campaign_job_status, 
    ${alias}.batch_no as lau_managed_campaign_job_batch_no, ${alias}.ctime as lau_managed_campaign_job_ctime, 
    ${alias}.mtime as lau_managed_campaign_job_mtime, ${alias}.app_package_id as lau_managed_campaign_job_app_package_id
  </sql>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, account_id, operator_name, campaign_id, ocpx_target, cost_price, is_long_term_launch, 
    status, batch_no, ctime, mtime, app_package_id
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.adp.launch.biz.pojo.LauManagedCampaignJobPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from lau_managed_campaign_job
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from lau_managed_campaign_job
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from lau_managed_campaign_job
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.adp.launch.biz.pojo.LauManagedCampaignJobPoExample">
    delete from lau_managed_campaign_job
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.adp.launch.biz.pojo.LauManagedCampaignJobPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_managed_campaign_job (account_id, operator_name, campaign_id, 
      ocpx_target, cost_price, is_long_term_launch, 
      status, batch_no, ctime, 
      mtime, app_package_id)
    values (#{accountId,jdbcType=INTEGER}, #{operatorName,jdbcType=VARCHAR}, #{campaignId,jdbcType=INTEGER}, 
      #{ocpxTarget,jdbcType=TINYINT}, #{costPrice,jdbcType=INTEGER}, #{isLongTermLaunch,jdbcType=TINYINT}, 
      #{status,jdbcType=TINYINT}, #{batchNo,jdbcType=INTEGER}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP}, #{appPackageId,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.adp.launch.biz.pojo.LauManagedCampaignJobPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_managed_campaign_job
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        account_id,
      </if>
      <if test="operatorName != null">
        operator_name,
      </if>
      <if test="campaignId != null">
        campaign_id,
      </if>
      <if test="ocpxTarget != null">
        ocpx_target,
      </if>
      <if test="costPrice != null">
        cost_price,
      </if>
      <if test="isLongTermLaunch != null">
        is_long_term_launch,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="batchNo != null">
        batch_no,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="appPackageId != null">
        app_package_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="operatorName != null">
        #{operatorName,jdbcType=VARCHAR},
      </if>
      <if test="campaignId != null">
        #{campaignId,jdbcType=INTEGER},
      </if>
      <if test="ocpxTarget != null">
        #{ocpxTarget,jdbcType=TINYINT},
      </if>
      <if test="costPrice != null">
        #{costPrice,jdbcType=INTEGER},
      </if>
      <if test="isLongTermLaunch != null">
        #{isLongTermLaunch,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="batchNo != null">
        #{batchNo,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="appPackageId != null">
        #{appPackageId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.adp.launch.biz.pojo.LauManagedCampaignJobPoExample" resultType="java.lang.Long">
    select count(*) from lau_managed_campaign_job
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update lau_managed_campaign_job
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.accountId != null">
        account_id = #{record.accountId,jdbcType=INTEGER},
      </if>
      <if test="record.operatorName != null">
        operator_name = #{record.operatorName,jdbcType=VARCHAR},
      </if>
      <if test="record.campaignId != null">
        campaign_id = #{record.campaignId,jdbcType=INTEGER},
      </if>
      <if test="record.ocpxTarget != null">
        ocpx_target = #{record.ocpxTarget,jdbcType=TINYINT},
      </if>
      <if test="record.costPrice != null">
        cost_price = #{record.costPrice,jdbcType=INTEGER},
      </if>
      <if test="record.isLongTermLaunch != null">
        is_long_term_launch = #{record.isLongTermLaunch,jdbcType=TINYINT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.batchNo != null">
        batch_no = #{record.batchNo,jdbcType=INTEGER},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.appPackageId != null">
        app_package_id = #{record.appPackageId,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update lau_managed_campaign_job
    set id = #{record.id,jdbcType=BIGINT},
      account_id = #{record.accountId,jdbcType=INTEGER},
      operator_name = #{record.operatorName,jdbcType=VARCHAR},
      campaign_id = #{record.campaignId,jdbcType=INTEGER},
      ocpx_target = #{record.ocpxTarget,jdbcType=TINYINT},
      cost_price = #{record.costPrice,jdbcType=INTEGER},
      is_long_term_launch = #{record.isLongTermLaunch,jdbcType=TINYINT},
      status = #{record.status,jdbcType=TINYINT},
      batch_no = #{record.batchNo,jdbcType=INTEGER},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      app_package_id = #{record.appPackageId,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.adp.launch.biz.pojo.LauManagedCampaignJobPo">
    update lau_managed_campaign_job
    <set>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=INTEGER},
      </if>
      <if test="operatorName != null">
        operator_name = #{operatorName,jdbcType=VARCHAR},
      </if>
      <if test="campaignId != null">
        campaign_id = #{campaignId,jdbcType=INTEGER},
      </if>
      <if test="ocpxTarget != null">
        ocpx_target = #{ocpxTarget,jdbcType=TINYINT},
      </if>
      <if test="costPrice != null">
        cost_price = #{costPrice,jdbcType=INTEGER},
      </if>
      <if test="isLongTermLaunch != null">
        is_long_term_launch = #{isLongTermLaunch,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="batchNo != null">
        batch_no = #{batchNo,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="appPackageId != null">
        app_package_id = #{appPackageId,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.adp.launch.biz.pojo.LauManagedCampaignJobPo">
    update lau_managed_campaign_job
    set account_id = #{accountId,jdbcType=INTEGER},
      operator_name = #{operatorName,jdbcType=VARCHAR},
      campaign_id = #{campaignId,jdbcType=INTEGER},
      ocpx_target = #{ocpxTarget,jdbcType=TINYINT},
      cost_price = #{costPrice,jdbcType=INTEGER},
      is_long_term_launch = #{isLongTermLaunch,jdbcType=TINYINT},
      status = #{status,jdbcType=TINYINT},
      batch_no = #{batchNo,jdbcType=INTEGER},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      app_package_id = #{appPackageId,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.adp.launch.biz.pojo.LauManagedCampaignJobPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_managed_campaign_job (account_id, operator_name, campaign_id, 
      ocpx_target, cost_price, is_long_term_launch, 
      status, batch_no, ctime, 
      mtime, app_package_id)
    values (#{accountId,jdbcType=INTEGER}, #{operatorName,jdbcType=VARCHAR}, #{campaignId,jdbcType=INTEGER}, 
      #{ocpxTarget,jdbcType=TINYINT}, #{costPrice,jdbcType=INTEGER}, #{isLongTermLaunch,jdbcType=TINYINT}, 
      #{status,jdbcType=TINYINT}, #{batchNo,jdbcType=INTEGER}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP}, #{appPackageId,jdbcType=INTEGER})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      account_id = values(account_id),
      operator_name = values(operator_name),
      campaign_id = values(campaign_id),
      ocpx_target = values(ocpx_target),
      cost_price = values(cost_price),
      is_long_term_launch = values(is_long_term_launch),
      status = values(status),
      batch_no = values(batch_no),
      ctime = values(ctime),
      mtime = values(mtime),
      app_package_id = values(app_package_id),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      lau_managed_campaign_job
      (account_id,operator_name,campaign_id,ocpx_target,cost_price,is_long_term_launch,status,batch_no,ctime,mtime,app_package_id)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.accountId,jdbcType=INTEGER},
        #{item.operatorName,jdbcType=VARCHAR},
        #{item.campaignId,jdbcType=INTEGER},
        #{item.ocpxTarget,jdbcType=TINYINT},
        #{item.costPrice,jdbcType=INTEGER},
        #{item.isLongTermLaunch,jdbcType=TINYINT},
        #{item.status,jdbcType=TINYINT},
        #{item.batchNo,jdbcType=INTEGER},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.appPackageId,jdbcType=INTEGER},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      lau_managed_campaign_job
      (account_id,operator_name,campaign_id,ocpx_target,cost_price,is_long_term_launch,status,batch_no,ctime,mtime,app_package_id)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.accountId,jdbcType=INTEGER},
        #{item.operatorName,jdbcType=VARCHAR},
        #{item.campaignId,jdbcType=INTEGER},
        #{item.ocpxTarget,jdbcType=TINYINT},
        #{item.costPrice,jdbcType=INTEGER},
        #{item.isLongTermLaunch,jdbcType=TINYINT},
        #{item.status,jdbcType=TINYINT},
        #{item.batchNo,jdbcType=INTEGER},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.appPackageId,jdbcType=INTEGER},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      account_id = values(account_id),
      operator_name = values(operator_name),
      campaign_id = values(campaign_id),
      ocpx_target = values(ocpx_target),
      cost_price = values(cost_price),
      is_long_term_launch = values(is_long_term_launch),
      status = values(status),
      batch_no = values(batch_no),
      ctime = values(ctime),
      mtime = values(mtime),
      app_package_id = values(app_package_id),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.adp.launch.biz.pojo.LauManagedCampaignJobPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_managed_campaign_job
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        account_id,
      </if>
      <if test="operatorName != null">
        operator_name,
      </if>
      <if test="campaignId != null">
        campaign_id,
      </if>
      <if test="ocpxTarget != null">
        ocpx_target,
      </if>
      <if test="costPrice != null">
        cost_price,
      </if>
      <if test="isLongTermLaunch != null">
        is_long_term_launch,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="batchNo != null">
        batch_no,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="appPackageId != null">
        app_package_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="operatorName != null">
        #{operatorName,jdbcType=VARCHAR},
      </if>
      <if test="campaignId != null">
        #{campaignId,jdbcType=INTEGER},
      </if>
      <if test="ocpxTarget != null">
        #{ocpxTarget,jdbcType=TINYINT},
      </if>
      <if test="costPrice != null">
        #{costPrice,jdbcType=INTEGER},
      </if>
      <if test="isLongTermLaunch != null">
        #{isLongTermLaunch,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="batchNo != null">
        #{batchNo,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="appPackageId != null">
        #{appPackageId,jdbcType=INTEGER},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="accountId != null">
        account_id = values(account_id),
      </if>
      <if test="operatorName != null">
        operator_name = values(operator_name),
      </if>
      <if test="campaignId != null">
        campaign_id = values(campaign_id),
      </if>
      <if test="ocpxTarget != null">
        ocpx_target = values(ocpx_target),
      </if>
      <if test="costPrice != null">
        cost_price = values(cost_price),
      </if>
      <if test="isLongTermLaunch != null">
        is_long_term_launch = values(is_long_term_launch),
      </if>
      <if test="status != null">
        status = values(status),
      </if>
      <if test="batchNo != null">
        batch_no = values(batch_no),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="appPackageId != null">
        app_package_id = values(app_package_id),
      </if>
    </trim>
  </insert>
</mapper>