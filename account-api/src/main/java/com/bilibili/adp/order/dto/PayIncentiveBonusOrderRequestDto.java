package com.bilibili.adp.order.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PayIncentiveBonusOrderRequestDto implements Serializable {
    private static final long serialVersionUID = 1L;

    private String orderId;

    private Long mid;

    private Integer payType;

    /**
     * 商户id
     * 非必填，不填就用系统默认的
     */
    private String appkey;

    /**
     * 非必填，不填就用系统默认的
     */
    private String secretKey;

}
