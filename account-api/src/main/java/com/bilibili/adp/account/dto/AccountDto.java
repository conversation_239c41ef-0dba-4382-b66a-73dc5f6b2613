package com.bilibili.adp.account.dto;

import com.bilibili.bjcom.util.sensitive.annotation.Sensitive;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * Created by fan<PERSON><PERSON> on 2017/2/28.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AccountDto {
    private Integer accountId;
    private String username;
    @Sensitive
    private String mobile;
    private Integer status;
    private Long mid;
    private Integer accountType;
    private Timestamp activeTime;
    private String name;
    @Sensitive
    private String icpRecordNumber;
    private String icpInfoImage;
    @Sensitive
    private String brandDomain;
    private Integer userType;
    private Integer adStatus;
    private Integer categoryFirstId;
    private Integer categorySecondId;
    private String remark;
    private Boolean isAgent;
    private Integer gdStatus;
    private Integer dependencyAgentId;
    private String companyName;
    private Integer isSupportSeller;
    private Integer isSupportGame;
    private Integer isSupportContent;
    private Integer isSupportLocalAd;
    private Integer isSupportDpa;
    private Integer companyGroupId;
    // 是否支持互选广告 0-否 1-是
    private Integer isSupportMas;
    /**
     * 是否支持商业起飞投放 0-否 1-是
     */
    private Integer isSupportFly;
    /**
     * 是否内部账号 0-否 1-是
     */
    private Integer isInner;

    /**
     * 是否允许现金支付（个人起飞专用） 0-不允许 1-允许
     */
    private Integer allowCashPay;

    /**
     * 是否允许激励金支付（个人起飞专用） 0-不允许 1-允许
     */
    private Integer allowIncentiveBonusPay;

    /**
     * 商业行业一级分类id(新版)
     */
    private Integer commerceCategoryFirstId;

    /**
     * 商业行业二级分类id（新版）
     */
    private Integer commerceCategorySecondId;

    /**
     * 是否允许签约金支付（个人起飞专用） 0-不允许 1-允许
     */
    private Integer allowSigningBonusPay;
    /**
     * 是否支持起飞币支付 0-否 1-是
     */
    private Integer allowFlyCoinPay;

    /**
     * 是否支持流量券支付 0-否 1-是
     */
    private Integer allowFlowTicketPay;

    /**
     * 客户id
     */
    private Integer customerId;

    /**
     * 产品id
     */
    private Integer productId;

    /**
     * 是否含有落地页表单 0-不包含 1-包含
     */
    private Integer hasMgkForm;

    /**
     * 建站隐私政策是否同意 0-未设置 1-同意 2 不同意
     */
    private Integer mgkFormPrivacyPolicy;

    private Integer financeType;
    private Integer departmentId;

    /**
     * 是否支持线索通投放 0-否 1-是
     */
    private Integer isSupportCluePass;

    /**
     * 是否支持花火 0-否 1-是
     */
    private Integer isSupportPickup;
    private Integer isDeleted;

}
