/** 
* <AUTHOR> 
* @date  2017年10月23日
*/ 

package com.bilibili.adp.account.dto;

import java.sql.Timestamp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AccAccountWalletLogDto {
	private Long id;
    private Integer accountId;
    private Integer operationType;
    private Long cash;
    private Long redPacket;
    private Long specialRedPacket;
    private String remark;
    private Integer salesType;    
    private Timestamp date;

    /**
     * 签约-托管余额（单位分）
     */
    private Long trustFund;

    /**
     * 签约-订单余额（单位分）
     */
    private Long withholdFund;

    /**
     * 起飞-现金-托管-余额（单位分）
     */
    private Long trustCash;

    /**
     * 起飞-激励金-托管-余额（单位分）
     */
    private Long trustIncentive;

    /**
     * 起飞-起飞币-托管-余额（单位分）
     */
    private Long trustFlyCoin;
}
