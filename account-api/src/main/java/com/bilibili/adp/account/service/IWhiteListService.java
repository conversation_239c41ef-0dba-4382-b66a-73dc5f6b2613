package com.bilibili.adp.account.service;

import com.bilibili.adp.account.dto.*;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;

public interface IWhiteListService {

    PageResult<WhiteListInfoBaseDto> queryWhiteListInfos(WhiteListInfoQueryDto queryDto);

    Long addWhiteList(WhiteListInfoBaseDto dto, Operator operator);

    Integer updateWhiteListSwitchStatus(Long id, Integer switchStatus);

    PageResult<WhiteListDetailDto> queryWhiteListDetailInfos(WhiteListDetailQueryDto queryDto);

    Integer updateWhiteListUserSwitchStatus(Long id, Integer switchStatus);

    Integer batchAddWhiteListUser(BatchAddWhiteListUserDto dto, Operator operator);

    boolean checkUserInWhite(Long whiteListId, Long objId);
}
