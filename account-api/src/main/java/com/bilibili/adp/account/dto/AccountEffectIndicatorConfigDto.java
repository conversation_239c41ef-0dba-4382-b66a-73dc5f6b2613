package com.bilibili.adp.account.dto;

/**
 * <AUTHOR>
 * @date 2017/12/4
 **/

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AccountEffectIndicatorConfigDto {
    private Integer accountId;
    private Integer ecpm;
    private Integer ctr;
    private Integer isDeleted;
}
