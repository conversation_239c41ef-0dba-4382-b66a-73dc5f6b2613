syntax = "proto3";
import "google/protobuf/empty.proto";
package ad.component;

option go_package = "buf.bilibili.co/bapis/bapis-gen/ad/component;api";
option java_package = "com.bapis.ad.component";
option java_multiple_files = true;

// 废弃中, 预计迁移至pandora
service StoryComponentService {
  // 新增
  rpc Save(StoryComponent) returns (StoryComponentIdAndType);
  // 获取
  rpc Get(StoryComponentIdAndType) returns (StoryComponent);
  // 获取
  rpc GetByComponentIds(StoryComponentIds) returns (StoryComponents);
  // 列表
  rpc List(StoryComponentListParams) returns (StoryComponents);
  // 删除
  rpc Delete(StoryComponentIdAndType) returns (.google.protobuf.Empty);
  // 根据商品id查询，用于story商品组件
  rpc ListByItemId(StoryComponentIdAndType) returns (StoryComponents);
}

message Common {
  string image_url = 1;
  string image_md5 = 2;
  string title = 3;
  string desc = 4;
  int32 button_id = 5;
  int32 button_type = 6;
  string button_text = 7;
  repeated string key_words = 8;
}

message Coupon {
  string desc = 1;
  int32 cost_fen = 2;
  int64 use_period_start_milli = 3;
  int64 use_period_end_milli = 4;
  int64 obtain_period_start_milli = 5;
  int64 obtain_period_end_milli = 6;
  string comment = 7;
  int32 button_id = 8;
  int32 button_type = 9;
  string button_text = 10;
}

message Image {
  string image_url = 1;
  string image_md5 = 2;
  GoodsInfo goods_info = 3;
}

message PriceDifference {
  string image_url = 1;
  string image_md5 = 2;
  string title = 3;
  int32 original_price = 4;
  int32 price = 5;
  GoodsInfo goods_info = 6;
}

message GoodsInfo {
  int64 item_id = 1;
  int32 item_source = 2;
  int32 jump_type = 3;
  string jump_url = 4;
  string schema_url = 5;
  string mini_program_id = 6;
  string mini_program_name = 7;
  string mini_program_path = 8;
}

message BaseComponent {
  int64 id = 1;
  int32 type = 2;
  string name = 3;
  int32 account_id = 4;
  int64 ctime_milli = 5;
}

message StoryComponent {
  oneof component {
    Common common = 1;
    Coupon coupon = 2;
    Image image = 3;
    PriceDifference priceDifference = 5;
  }
  BaseComponent base_component = 4;
}

message StoryComponentIdAndType {
  int64 id = 1;
  int32 type = 2;
  int64 item_id = 3;
}

message StoryComponentIds {
  repeated int64 id = 1;
  int32 type = 2;
  int64 item_id = 3;
}


message StoryComponentListParams {
  int32 account_id = 1;
  int64 ctime_milli_start = 2;
  int64 ctime_milli_end = 3;
  int32 scope = 4; // 0-全部（默认）1-商品组件 2-非商品组件
}

message StoryComponents {
  repeated StoryComponent components = 1;
}