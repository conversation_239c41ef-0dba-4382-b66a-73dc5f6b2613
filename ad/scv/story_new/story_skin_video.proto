syntax = "proto3";
package ad.scv.story_new;

option go_package = "buf.bilibili.co/bapis/bapis-gen/ad/scv.story_new;api";
option java_package = "com.bapis.ad.scv.story_new";
option java_multiple_files = true;

import "ad/audit/api.proto";
import "google/protobuf/empty.proto";

// appid: sycpb.cpm.scv
service StorySkinVideoService {
  // 获取上传视频的链接
  rpc fetchUploadUrl(.google.protobuf.Empty) returns(UploadResp);

  // 查询
  rpc list(SaveSkinVideoListReq)returns(SkinVideoResp);

  // 保存视频
  rpc saveSkinVideo(SaveSkinVideoReq)returns(SaveSkinVideoResp);

  // 编辑
  rpc update(UpdateSkinVideoReq)returns(SaveSkinVideoResp);

  // 删除
  rpc delete(UpdateSkinVideoReq)returns(SaveSkinVideoResp);

}

message SaveSkinVideoListReq {
  int32 page = 1;
  int32 size = 2;
}

message UploadResp {
  int32 code = 1;
  string msg = 2;
  UploadUrl url = 3;
}

message UploadUrl {
  string upload_url = 1;
  string download_url = 2;
}

message SaveSkinVideoReq {
  string name = 1;
  string rgb_video_url = 2;
  string alpha_video_url = 3;
  string show_webp_img = 4;
  ad.audit.Operator operator = 5;
}

message SaveSkinVideoResp {
  int32 code = 1;
  string result = 2;
}

message SkinVideoResp {
  int32 total = 1;
  repeated SkinVideoDetails details = 2;
}

message SkinVideoDetails {
  int64 id = 1;
  string name = 2;
  string compound_video_url = 3; // 合成视频url
  int32 is_bound_creative = 4; // 是否关联创意
  string operator_name = 5; // 操作人
  int64 mtime = 6;
  int32 deal_status = 7; // 合成视频状态
  string alpha_video_url = 8;
  string show_webp_img = 9;
  string rgb_video_url = 10;
}

message UpdateSkinVideoReq {
  string name = 1;
  string rgb_video_url = 2;
  string alpha_video_url = 3;
  string show_webp_img = 4;
  ad.audit.Operator operator = 5;
  int64 id = 6;
}