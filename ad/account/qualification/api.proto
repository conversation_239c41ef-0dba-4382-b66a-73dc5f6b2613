syntax = "proto3";
package ad.account.product;

option go_package = "buf.bilibili.co/bapis/bapis-gen/ad/account.qualification;api";
option java_package = "com.bapis.ad.account.qualification";
option java_multiple_files = true;

service AccountQualificationService {

  rpc QueryAccountQualificationByIds(QueryAccountQualificationReq) returns (QueryAccountQualificationResp);

}

message QueryAccountQualificationReq {
  repeated int32 qualification_ids = 1;
}

message QueryAccountQualificationResp {
  int32 code = 1;       // 0正确 其他异常
  string message = 2;   // 错误信息
  repeated QualificationDto data = 3;
}

message QualificationDto {
  int32 id = 1; // 资质id
  string name = 2; // 资质名
  string remark = 3; // 备注
  int32 status = 4; // 状态：1-启用 2-禁用


}