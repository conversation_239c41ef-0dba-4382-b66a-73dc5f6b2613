syntax = "proto3";
package ad.cmc.data.category_selection;

import "extension/wdcli/wdcli.proto";
option go_package = "buf.bilibili.co/bapis/bapis-gen/ad/cmc.data.category_selection;api";
option java_package = "com.bapis.ad.cmc.data.category_selection";
option java_multiple_files = true;
option (wdcli.appid) = "sycpb.e-commerce-goods.cmc-data";

service CategorySelection {
  // 品类选品工具
  rpc queryCategorySelection(CategorySelectionReq) returns(CategorySelectionResp);
}


message CategorySelectionReq{
  // up主mid列表
  int32 firstCategory_id = 1;
  // 商品来源
  int32 source_type = 2;
  // 查询开始时间
  int64 start_date = 3;
  // 查询结束时间
  int64 end_date = 4;
  // up主mid列表
  string firstCategory_name = 5;

}

message CategorySelectionResp{
  repeated CategoryResp categoryResp = 1;
}

message CategoryResp{

  // 带货一级类目ID
  int32 daihuo_category_id_lv1 = 1;
  // 带货二级类目ID
  int32 daihuo_category_id_lv2 = 2;
  // 带货一级类目名
  string daihuo_category_name_lv1 = 3;
  // 带货二级类目名
  string daihuo_category_name_lv2 = 4;
  // 商品来源
  int32 source_type = 5;
  // 日期
  string log_date = 6;
  // UP主数
  int64 avg_up_cnt = 7;
  // 稿件数
  int64 sum_arch_cnt = 8;
  // 曝光量
  int64 sum_show_pv = 9;
  // 唤起量
  int64 sum_callup_pv = 10;
  // 订单量
  int64 sum_order_pv = 11;
  // GMV
  int64 sum_gmv = 12;
  // 商品数
  int64 avg_item_cnt = 13;
}