syntax = "proto3";
package ad.cmc.partner_audit;

import "extension/wdcli/wdcli.proto";

option go_package = "buf.bilibili.co/bapis/bapis-gen/ad/cmc.partner.changelog;api";
option java_package = "com.bapis.ad.cmc.partner.changelog";
option java_multiple_files = true;
option (wdcli.appid) = "sycpb.e-commerce-goods.cmc-partner";

service PartnerChangelogService {
  // 分页查询服务商更新日志列表
  rpc paginateChangelogs(PartnerChangelogPageReq) returns (PartnerChangelogPageResp);
}

message PartnerChangelogPageReq {
  // 日志类型
  int32 type = 1;
  // 服务商id
  int64 partner_id = 2;
  // 操作人
  string operator = 3;
  // 操作开始时间戳
  int64 begin_ime = 4;
  // 操作结束时间戳
  int64 end_time = 5;
  // 页号
  int32 page = 6;
  // 分页大小
  int32 size = 7;
  // 服务商bid
  int64 bid = 8;
  // 操作类型：1-提交审核  2-审核通过  3-审核驳回  4-重新提交审核
  int32 operationType = 9;
  // 服务商名称
  string partnerName = 10;
}

message PartnerChangelogPageResp {
  // 总数
  int32 total = 1;
  // 更新日志列表
  repeated PartnerChangelog records = 2;
}

message PartnerChangelog {
  // 日志id
  int64 id = 1;
  // 服务商id
  int64 partner_id = 2;
  // 日志类型
  int32 type = 3;
  // 更新前旧值，json格式
  string before = 4;
  // 更新后新值，json格式
  string after = 5;
  // 操作人
  string operator = 6;
  // 操作时间
  int64 ctime = 7;
  // 服务商名称
  string partner_name = 8;
  // 操作类型
  int32 operation_type = 9;
  // 服务商bid
  int64 bid = 10;
}