syntax = "proto3";
import "google/protobuf/empty.proto";
import "extension/wdcli/wdcli.proto";

package ad.cmc.goods;

option go_package = "buf.bilibili.co/bapis/bapis-gen/ad/cmc.targeting;api";
option java_package = "com.bapis.ad.cmc.targeting";
option java_multiple_files = true;
option (wdcli.appid) = "sycpb.cpm.tavern-platform";

service TargetingService {
  // 新增人群包
  rpc addCrowdPackage(CrowdPackageAddReq) returns (CrowdPackageAddResp);
  // 更新人群包
  rpc updateCrowdPackage(CrowdPackageUpdateReq) returns (.google.protobuf.Empty);
  // 查询人群包列表
  rpc queryCrowdPackages(CrowdPackagesQueryReq) returns (CrowdPackagesQueryResp);
  // 查询人群包详情
  rpc queryCrowdPackageDetail(CrowdPackageDetailQueryReq) returns (CrowdPackageDetailQueryResp);

  // 新增商品包
  rpc addGoodsPackage(GoodsPackageAddReq) returns (GoodsPackageAddResp);
  // 更新商品包
  rpc updateGoodsPackage(GoodsPackageUpdateReq) returns (.google.protobuf.Empty);
  // 查询商品包列表
  rpc queryGoodsPackages(GoodsPackagesQueryReq) returns (GoodsPackagesQueryResp);
  // 查询商品包详情
  rpc queryGoodsPackageDetail(GoodsPackageDetailQueryReq) returns (GoodsPackageDetailQueryResp);

  // 新增推荐主题
  rpc addTheme(ThemeAddReq) returns (ThemeAddResp);
  // 更新推荐主题
  rpc updateTheme(ThemeUpdateReq) returns (.google.protobuf.Empty);
  // 查询推荐主题列表
  rpc queryThemes(ThemesQueryReq) returns (ThemesQueryResp);
  // 查询推荐主题详情
  rpc queryThemeDetail(ThemeDetailQueryReq) returns (ThemeDetailQueryResp);
  // 更新主题的上下架状态
  rpc updateThemeShelvesStatus(ThemesUpdateShelvesStatusReq) returns (.google.protobuf.Empty);
}

message CrowdPackageAddReq {
  // 包类型
  int32 packageType = 1;
  // 包名称
  string packageName = 2;
  // UP主带货等级列表
  repeated int32 cmcUpLevels = 3;
  // UP主主投分区Id列表
  repeated int32 upPartitionIds = 4;
  // UP主主推类目Id列表
  repeated int32 cmcCatIds = 5;
  // 粉丝起始个数（包含）
  int32 fansFrom = 6;
  // 粉丝结束个数（不包含）
  int32 fansTo = 7;
  // 黑名单UpMids列表
  repeated int64 blackMids = 8;
  // 通过上传mid文件创建人群包的mid列表
  repeated int64 mids = 9;
  // 黑名单文件链接
  string blackMidsUrl = 10;
  // 通过上传mid文件创建人群包的mid文件链接
  string midsUrl = 11;
  // 操作人
  string operator = 12;
  // 上传的文件的名称
  string fileName = 13;
  // 文件链接
  string fileUrl = 14;
}

message CrowdPackageAddResp {
  // 包id
  int64 id = 1;
}

message CrowdPackageUpdateReq {
  // 包id
  int64 id = 1;
  // 包类型
  int32 packageType = 2;
  // 包名称
  string packageName = 3;
  // UP主带货等级列表
  repeated int32 cmcUpLevels = 4;
  // UP主主投分区Id列表
  repeated int32 upPartitionIds = 5;
  // UP主主推类目Id列表
  repeated int32 cmcCatIds = 6;
  // 粉丝起始个数（包含）
  int32 fansFrom = 7;
  // 粉丝结束个数（不包含）
  int32 fansTo = 8;
  // 黑名单UpMids列表
  repeated int64 blackMids = 9;
  // 通过上传mid文件创建人群包的mid列表
  repeated int64 mids = 10;
  // 黑名单文件链接
  string blackMidsUrl = 11;
  // 通过上传mid文件创建人群包的mid文件链接
  string midsUrl = 12;
  // 操作人
  string operator = 13;
  // 上传的文件的名称
  string fileName = 14;
  // 文件链接
  string fileUrl = 15;
}

message CrowdPackagesQueryReq {
  // 包id
  int64 id = 1;
  // 包名称
  string packageName = 2;
  // 包创建人
  string creator = 3;
  // 页码
  int32 page = 4;
  // 每页个数
  int32 size = 5;
}

message CrowdPackagesQueryResp {
  // 总个数
  int32 total = 1;
  // 列表
  repeated CrowdPackageOverview records = 2;
}

message CrowdPackageOverview {
  // 包id
  int64 id = 1;
  // 包类型
  int32 packageType = 2;
  // 包名称
  string packageName = 3;
  // 包创建人
  string creator = 4;
  // 包创建时间
  int64 ctime = 5;
  // 包更新时间
  int64 mtime = 6;
}

message CrowdPackageDetailQueryReq {
  // 包id
  int64 id = 1;
}

message CrowdPackageDetailQueryResp {
  // 包id
  int64 id = 1;
  // 包类型
  int32 packageType = 2;
  // 包名称
  string packageName = 3;
  // up主带货等级列表
  repeated int32 cmcUpLevels = 4;
  // up主主投分区id列表
  repeated int32 upPartitions = 5;
  // up主主推类目id（带货侧）列表
  repeated int32 cmcCatIds = 6;
  // 粉丝个数起始个数（包含）
  int32 fansFrom = 7;
  // 粉丝个数结束个数（不包含）
  int32 fansTo = 8;
  // 黑名单列表
  repeated int64 blackMids = 9;
  // 通过上传mid文件创建人群包的mid列表
  repeated int64 mids = 10;
  // 创建人
  string creator = 11;
  // 创建时间
  int64 ctime = 12;
  // 上传的文件的名称
  string fileName = 13;
  // 文件链接
  string fileUrl = 14;
  // 更新时间
  int64 mtime = 15;
}

message GoodsPackageAddReq {
  // 包类型
  int32 packageType = 1;
  // 包名称
  string packageName = 2;
  // 商品名称（模糊匹配）列表
  repeated string goodsNamesLike = 3;
  // 店铺来源
  int32 shopSourceType = 4;
  // 店铺名称
  repeated string shopNames = 5;
  // 商品来源列表
  repeated int32 goodsSourceTypes = 6;
  // 商品类目id（带货侧）列表
  repeated int32 cmcCatIds = 7;
  // 起始价格（包含），单位：分
  int32 priceFrom = 8;
  // 结束价格（不包含），单位：分
  int32 priceTo = 9;
  // 起始佣金率（包含），例如：佣金率为 12%，则此值为 12
  int32 commissionRateFrom = 10;
  // 结束佣金率（不包含），例如：佣金率为 12%，则此值为 12
  int32 commissionRateTo = 11;
  // 起始全网销量（包含）
  int32 volumeFrom = 12;
  // 结束全网销量（不包含）
  int32 volumeTo = 13;
  // 起始B站销量（包含）
  int32 biliVolumeFrom = 14;
  // 结束B站销量（不包含）
  int32 biliVolumeTo = 15;
  // 商品黑名单列表
  repeated int64 blackItemIds = 16;
  // 通过上传mid文件创建人群包的mid列表
  repeated int64 itemIds = 17;
  // 黑名单文件链接
  string blackItemsUrl = 18;
  // 通过上传item文件创建人群包的item文件链接
  string itemsUrl = 19;
  // 操作人
  string operator = 20;
  // 上传的文件的名称
  string fileName = 21;
  // 文件链接
  string fileUrl = 22;
  // 货盘id
  string materialIds = 23;
}

message GoodsPackageAddResp {
  // 包id
  int64 id = 1;
}

message GoodsPackageUpdateReq {
  int64 id = 1;
  // 包类型
  int32 packageType = 2;
  // 包名称
  string packageName = 3;
  // 商品名称（模糊匹配）列表
  repeated string goodsNamesLike = 4;
  // 店铺来源
  int32 shopSourceType = 5;
  // 店铺名称
  repeated string shopNames = 6;
  // 商品来源列表
  repeated int32 goodsSourceTypes = 7;
  // 商品类目id（带货侧）列表
  repeated int32 cmcCatIds = 8;
  // 起始价格（包含），单位：分
  int32 priceFrom = 9;
  // 结束价格（不包含），单位：分
  int32 priceTo = 10;
  // 起始佣金率（包含），例如：佣金率为 12%，则此值为 12
  int32 commissionRateFrom = 11;
  // 结束佣金率（不包含），例如：佣金率为 12%，则此值为 12
  int32 commissionRateTo = 12;
  // 起始全网销量（包含）
  int32 volumeFrom = 13;
  // 结束全网销量（不包含）
  int32 volumeTo = 14;
  // 起始B站销量（包含）
  int32 biliVolumeFrom = 15;
  // 结束B站销量（不包含）
  int32 biliVolumeTo = 16;
  // 商品黑名单列表
  repeated int64 blackItemIds = 17;
  // 通过上传mid文件创建人群包的mid列表
  repeated int64 itemIds = 18;
  // 黑名单文件链接
  string blackItemsUrl = 19;
  // 通过上传item文件创建人群包的item文件链接
  string itemsUrl = 20;
  // 操作人
  string operator = 21;
  // 上传的文件的名称
  string fileName = 22;
  // 文件链接
  string fileUrl = 23;
  // 货盘id
  string materialIds = 24;
}

message GoodsPackagesQueryReq {
  // 包id
  int64 id = 1;
  // 包名称
  string packageName = 2;
  // 包创建人
  string creator = 3;
  // 页码
  int32 page = 4;
  // 每页个数
  int32 size = 5;
}

message GoodsPackagesQueryResp {
  // 总个数
  int32 total = 1;
  // 列表
  repeated GoodsPackageOverview records = 2;
}

message GoodsPackageOverview {
  // 包id
  int64 id = 1;
  // 包类型
  int32 packageType = 2;
  // 包名称
  string packageName = 3;
  // 包创建人
  string creator = 4;
  // 包创建时间
  int64 ctime = 5;
  // 更新时间
  int64 mtime = 6;
}

message GoodsPackageDetailQueryReq {
  // 包id
  int64 id = 1;
}

message GoodsPackageDetailQueryResp {
  int64 id = 1;
  // 包类型
  int32 packageType = 2;
  // 包名称
  string packageName = 3;
  // 商品名称（模糊匹配）列表
  repeated string goodsNamesLike = 4;
  // 店铺来源
  int32 shopSourceType = 5;
  // 店铺名称
  repeated string shopNames = 6;
  // 商品来源列表
  repeated int32 goodsSourceTypes = 7;
  // 商品类目id（带货侧）列表
  repeated int32 cmcCatIds = 8;
  // 起始价格（包含），单位：分
  int32 priceFrom = 9;
  // 结束价格（不包含），单位：分
  int32 priceTo = 10;
  // 起始佣金率（包含），例如：佣金率为 12%，则此值为 12
  int32 commissionRateFrom = 11;
  // 结束佣金率（不包含），例如：佣金率为 12%，则此值为 12
  int32 commissionRateTo = 12;
  // 起始全网销量（包含）
  int32 volumeFrom = 13;
  // 结束全网销量（不包含）
  int32 volumeTo = 14;
  // 起始B站销量（包含）
  int32 biliVolumeFrom = 15;
  // 结束B站销量（不包含）
  int32 biliVolumeTo = 16;
  // 商品黑名单列表
  repeated int64 blackItemIds = 17;
  // 通过上传mid文件创建人群包的mid列表
  repeated int64 itemIds = 18;
  // 创建人
  string creator = 19;
  // 创建时间
  int64 ctime = 20;
  // 上传的文件的名称
  string fileName = 21;
  // 文件链接
  string fileUrl = 22;
  // 更新时间
  int64 mtime = 23;
  // 货盘id
  string materialIds = 24;
}

message ThemeAddReq {
  // 主题名称
  string themeName = 1;
  // 主题生效开始时间
  int64 effectiveStartTime = 2;
  // 主题生效结束时间
  int64 effectiveEndTime = 3;
  // 主题坑位
  int32 themeOrder = 4;
  // 商品包id列表
  repeated int64 goodsPackageIds = 5;
  // 人群包id列表
  repeated int64 crowdPackageIds = 6;
  // 主题创建人
  string operator = 7;
}

message ThemeAddResp {
  int64 id = 1;
}

message ThemeUpdateReq {
  // 主题id
  int64 id = 1;
  // 主题名称
  string themeName = 2;
  // 主题生效开始时间
  int64 effectiveStartTime = 3;
  // 主题生效结束时间
  int64 effectiveEndTime = 4;
  // 主题坑位
  int32 themeOrder = 5;
  // 商品包id列表
  repeated int64 goodsPackageIds = 6;
  // 人群包id列表
  repeated int64 crowdPackageIds = 7;
  // 主题创建人
  string operator = 8;
}

message ThemesQueryReq {
  // 主题id
  int64 id = 1;
  // 主题名称
  string themeName = 2;
  // 主题坑位
  int32 themeOrder = 3;
  // 上下架状态，0 未知 1 上架 2 下架
  int32 shelvesStatus = 4;
  // 生效状态，0 未知 1 待生效 2 生效中 3 已过期
  int32 effectiveStatus = 5;
  // 页码
  int32 page = 6;
  // 每页大小
  int32 size = 7;
  // 创建人
  string creator = 8;
}

message ThemesQueryResp {
  // 总个数
  int32 total = 1;
  // 列表
  repeated ThemeOverview records = 2;
}

message ThemeOverview {
  // 主题id
  int64 id = 1;
  // 主题名称
  string themeName = 2;
  // 主题坑位
  int32 themeOrder = 3;
  // 上下架状态，0 未知 1 上架 2 下架
  int32 shelvesStatus = 4;
  // 生效状态，0 未知 1 待生效 2 生效中 3 已过期
  int32 effectiveStatus = 5;
  // 生效开始时间
  int64 effectiveStartTime = 6;
  // 生效结束时间
  int64 effectiveEndTime = 7;
  // 创建人
  string creator = 8;
  // 创建时间
  int64 ctime = 9;
  // 更新时间
  int64 mtime = 10;
}

message ThemeDetailQueryReq {
  // 主题id
  int64 id = 1;
}

message ThemeDetailQueryResp {
  // 主题id
  int64 id = 1;
  // 主题名称
  string themeName = 2;
  // 主题坑位
  int32 themeOrder = 3;
  // 上下架状态
  int32 shelvesStatus = 4;
  // 生效状态
  int32 effectiveStatus = 5;
  // 生效开始时间
  int64 effectiveStartTime = 6;
  // 生效结束时间
  int64 effectiveEndTime = 7;
  // 商品池id列表
  repeated int64 goodsPackageIds = 8;
  // 人群包id列表
  repeated int64 crowdPackageIds = 9;
  // 创建人
  string creator = 10;
  // 创建时间
  int64 ctime = 11;
  // 更新时间
  int64 mtime = 12;
}

message ThemesUpdateShelvesStatusReq {
  // 主题id
  int64 id = 1;
  // 上下架状态 0 未知 1 上架 2 下架
  int32 shelvesStatus = 2;
  // 操作人
  string operator = 3;
}

enum TargetingPackageType {
  // 未知
  UNKNOWN_TYPE = 0;
  // 根据规则圈选
  TYPE_RULE = 1;
  // 根据特定的包内容id圈选
  TYPE_OBJECT_ID = 2;
}

enum CmcUpLevel {
  // 未知
  UNKNOWN_LEVEL = 0;
  // 等级1
  LEVEL_1 = 1;
  // 等级2
  LEVEL_2 = 2;
  // 等级3
  LEVEL_3 = 3;
  // 等级4
  LEVEL_4 = 4;
  // 等级5
  LEVEL_5 = 5;
  // 等级6
  LEVEL_6 = 6;
  // 等级7
  LEVEL_7 = 7;
  // 等级8
  LEVEL_8 = 8;
}

enum EffectiveStatus {
  // 未知
  UNKNOWN_STATUS = 0;
  // 待生效
  TO_BE_EFFECTIVE = 1;
  // 生效中
  EFFECTIVE = 2;
  // 已过期
  EXPIRED = 3;
}

