syntax = "proto3";
import "extension/wdcli/wdcli.proto";

option go_package = "buf.bilibili.co/bapis/bapis-gen/ad/productsearch.source;api";
option java_package = "com.bapis.ad.productsearch.source";

option java_multiple_files = true;
option (wdcli.appid) = "sycpb.cpm.product-search-portal";

service SourceService {

  // 商品来源信息查询
  rpc SelectAllCustomSource(SourceQueryReq) returns (SourceResp);
}

message SourceQueryReq {

}

message SourceResp {
    repeated SourceDto dto = 1;
}

message SourceDto {
  //商品来源
  int32 sourceType = 1;
  //样式
/**
[
  {
    "placeType": 1,
    "buttonId": 4,
    "buttonName": "",
    "cardType": 10,
    "underframeCardStyle": 1,
    "showAdTag": false,
    "showGoodsCurPrice": false,
    "showDesc": false,
    "showExtraDesc": false
  }
]

其中 placeType 1-推荐位 2-弹幕 3-浮层 4-story 6-专栏 7-动态-软链 8-动态-卡图 9-动态-标签 10-橱窗 11-个人空间商品tab带货 12-评论
*/
  string styles = 2;
}