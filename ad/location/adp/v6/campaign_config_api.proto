syntax = "proto3";
package ad.location.adp.v6;

import "extension/wdcli/wdcli.proto";
import "ad/location/adp/config_input.proto";
import "ad/pandora/resource/campaign_api.proto";

option (wdcli.appid) = "sycpb.cpm.cpm-location";
option go_package = "buf.bilibili.co/bapis/bapis-gen/ad/location.adp.v6;api";
option java_package = "com.bapis.ad.location.adp.v6";
option java_multiple_files = true;

// deprecated
service CampaignConfigService {
    rpc CampaignPptConfig(CampaignPptConfigReq) returns(CampaignPptConfigReply);
    rpc CampaignContentConfig(CampaignContentConfigReq) returns(CampaignContentConfigReply);
}

message CampaignPptConfigReq {

}

message CampaignPptConfigReply {
    repeated CampaignPptConfigEntity entity = 1;
}

message CampaignPptConfigEntity {
    ConfigInput input = 1;
    pandora.resource.PromotionPurposeTypeInfo output = 2;
}

message CampaignContentConfigReq {

}

message CampaignContentConfigReply {
    repeated CampaignContentConfigEntity entity = 1;
}

message CampaignContentConfigEntity {
    ConfigInput input = 1;
    pandora.resource.CampaignContentConfigInfo output = 2;
}