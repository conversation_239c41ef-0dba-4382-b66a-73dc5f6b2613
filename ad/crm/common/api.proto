syntax = "proto3";
package ad.crm.common;

import "google/protobuf/empty.proto";

option go_package = "buf.bilibili.co/bapis/bapis-gen/ad/crm.common;api";
option java_package = "com.bapis.ad.crm.common";
option java_multiple_files = true;

message GrpcOperator {
  // 操作人ID
  int32 operator_id = 1;
  // 操作人名称
  string operator_name = 2;
  // 操作人类型
  OperatorType operator_type = 3;
  // 系统类型
  SystemType system_type = 4;
}

service QualificationService {
  // 主体资质下拉列表
  rpc GetQualificationList(.google.protobuf.Empty) returns (QualificationResp);
}

service SpecialInfoTypeService {
  // 行业资质下拉列表
  rpc GetSpecialInfoTypeList(.google.protobuf.Empty) returns (SpecialInfoTypeResp);
}

message QualificationResp {
  int32 code = 1;
  string message = 2;
  repeated QualificationInfo data = 3;
}

message QualificationInfo {
  int32 id = 1;
  string name = 2;
}

message SpecialInfoTypeResp {
  int32 code = 1;
  string message = 2;
  repeated SpecialInfoTypeInfo data = 3;
}

message SpecialInfoTypeInfo {
  int32 id = 1;
  string name = 2;
}

enum OperatorType {
  //广告主
  ADVERTISERS = 0;
  //运营人员
  OPERATING_PERSONNEL = 1;
  //系统
  SYSTEM = 2;
  //up主
  UP = 3;
  //代理商
  AGENT = 4;
  //代理商的系统管理员
  AGENT_SYS_ADMIN = 5;
  //代理商的投放管理员
  AGENT_LAUNCH_ADMIN = 6;
  //二级代理运营人员
  SECONDARY_AGENT = 7;
  //内部LDAP
  BILIBILIER = 100;
  //GD内部LDAP
  GD_BILIBILIER = 200;
  //个人起飞用户
  PERSON_FLY = 8;
  //MCN
  MCN = 9;
  //open_api
  OPEN_API = 10;

  // 自动投放
  EFFECT_AUTO_LAUNCH_SYSTEM = 11;

  // 自动投放 aaa项目
  EFFECT_AUTO_LAUNCH_AGENT = 12;
}

enum SystemType {
  //CRM系统
  CRM = 0;
  //运营后台
  MANAGER = 1;
  //效果广告
  CPM = 2;
  //CPT系统
  CPT = 3;
  //品牌广告
  GD = 4;
  //UP_AGENT
  UP_AGENT = 5;
  //UP_MNG
  UP_MNG = 6;
  //位置服务
  LOCATION = 7;
  //代理商系统
  SYS_AGENT = 8;
  //互选广告
  MAS = 9;
  //本地广告
  LOCAL = 10;
  //花火计划
  PICK_UP = 11;
  //新花火商单
  COMMERCIAL = 12;
  //（新生力场）一站式登录中心
  AD_ACCOUNT = 13;
  //起飞
  FLY = 14;
  //高能建站
  MGK = 15;
  //经营号
  BUSINESS = 16;
}

enum YesOrNo {
  //否
  NO = 0;
  //是
  YES = 1;
}

// 个人证件类型 0-未知 1-身份证（大陆地区） 2-护照（港澳台及海外）
enum PersonalIdCardType {
  UNKNOWN = 0;
  ID_CARD = 1; // 身份证（大陆地区）
  PASSPORT = 2; // 护照（港澳台及海外）
}