syntax = "proto3";
package ad.crm.resource;

option go_package = "buf.bilibili.co/bapis/bapis-gen/ad/crm.account_label;api";
option java_package = "com.bapis.ad.crm.account_label";
option java_multiple_files = true;

// appid: sycpb.cpm.crm-portal
service AccountLabelService {

  rpc CreateAccountLabel(CreateAccountLabelReq) returns (CreateAccountLabelResp);

  rpc CreateAccountLabelByLabelIds(CreateAccountLabelReq) returns (CreateAccountLabelResp);
}

message CreateAccountLabelResp {
  int32 code = 1;
  string message = 2;
}


message CreateAccountLabelReq {
  int32 account_id = 1;
  int32 label_id = 2;
  repeated int32 label_ids = 3;
}



