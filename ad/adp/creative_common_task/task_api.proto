syntax = "proto3";


import "extension/wdcli/wdcli.proto";

package ad.adp.creative_common_task;
option go_package = "buf.bilibili.co/bapis/bapis-gen/ad/adp.creative_common_task;api";
option java_package = "com.bapis.ad.adp.creative_common_task";
option java_multiple_files = true;

option (wdcli.appid) = "sycpb.cpm.cpm-adp";


service CreativeCommonTaskService {

  rpc GetCreativeCommonTaskList(CreativeCommonTaskReq) returns (CreativeCommonTaskListResp);

}
message CreativeCommonTaskReq {
  string from_time = 1;
  string to_time = 2;
  repeated string task_ids = 3;
  repeated string account_ids = 4;
  int32 task_type = 5;
  int32 page = 6;
  int32 page_size = 7;
}
message CreativeCommonTaskListResp{

  repeated CreativeCommonTask creativeCommonTaskList = 1;
  int64 total = 2 ;
}

message CreativeCommonTask {
  int64 event_id = 1;

  int64 account_id = 2;

  int64 up_mid = 3;

  string up_name = 4;

  int64 dynamic_id = 5;

  int64 show_uv = 6;

  int64 show_pv = 7;

  int64 click_uv = 8;

  int64 click_pv = 9;

  int64 play_uv = 10;

  int64 play_vv = 11;

  int64 callup_suc_uv = 12;

  int64 callup_suc_pv = 13;

  int64 comment_show_uv = 14;

  int64 comment_show_pv = 15;

  int64 comment_click_uv = 16;

  int64 comment_click_pv = 17;

  int64 private_ad_click_uv = 18;

  int64 private_ad_click_pv = 19;

  int64 dynamic_show_pv = 20;

  int64 dynamic_show_uv = 21;

  int64 charge = 22;//乘以10000

  string bvid = 23;

  int64 record_date = 24;

  string bvid_url = 25;

  string dynamic_url = 26;

  int64 log_date = 27;

  int64 campaign_id = 28;

  int64 unit_id = 29;

  int64 creative_id = 30;

  int64 pub_fans = 31;

  int64 interact_cnt = 32;
}