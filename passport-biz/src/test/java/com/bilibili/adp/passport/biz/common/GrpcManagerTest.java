package com.bilibili.adp.passport.biz.common;

import com.bilibili.adp.http.utils.OkHttpUtils;
import io.grpc.Channel;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.test.util.ReflectionTestUtils;

@RunWith(PowerMockRunner.class)
@PrepareForTest({OkHttpUtils.class})
@PowerMockIgnore("javax.net.ssl.*")
public class GrpcManagerTest {
    @InjectMocks
    private GrpcManager grpcManager;

    @Before
    public void before(){
        Channel channel = Mockito.mock(Channel.class);

        ReflectionTestUtils.setField(grpc<PERSON>anager, "identifyChannel", channel);
    }

    @Test
    public void getTokenInfo1() {
        try{
            grpcManager.getTokenInfo("FAA4C09BA7F8E0E4911DFDF1571A1551", "123", "111");
        }catch (Exception e){}
    }
}