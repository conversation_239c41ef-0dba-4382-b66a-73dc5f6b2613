package com.bilibili.adp.passport.biz.manager.bean.dynamic.v2;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DynamicResponse<E>  implements Serializable {
    private Integer code;

    private Integer ttl;

    private String msg;

    private String message;

    private E data;
}
