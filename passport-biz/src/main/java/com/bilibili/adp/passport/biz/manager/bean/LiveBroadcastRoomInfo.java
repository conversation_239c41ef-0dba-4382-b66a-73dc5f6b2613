package com.bilibili.adp.passport.biz.manager.bean;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LiveBroadcastRoomInfo implements Serializable {
    private static final long serialVersionUID = -7672593543450371043L;

    /** 直播间id **/
    @JSONField(name = "room_id")
    private Integer roomId;

    /** 直播用户mid **/
    private Long uid;
    /** 直播间封面 **/
    private String cover;
    /** 直播间title **/
    private String title;
    /** 老分区名称 **/
    private String area;

    /** 二级分区id **/
    @JSONField(name = "area_v2_id")
    private Integer areaV2Id;

    /** 二级分区名称 **/
    @JSONField(name = "area_v2_name")
    private String areaV2Name;

    /** 一级分区id **/
    @JSONField(name = "area_v2_parent_id")
    private Integer areaV2ParentId;

    /** 一级分区名称 **/
    @JSONField(name = "area_v2_parent_name")
    private String areaV2ParentName;

    /** 0-不可用 1-可用 **/
    @JSONField(name = "is_show")
    private Integer isShow;

    /** 落地页地址 **/
    private String link;
}
