/** 
* <AUTHOR> 
* @date  2018年2月5日
*/ 

package com.bilibili.adp.advertiser.portal.common;

public class Constants {
	private Constants() {
		throw new IllegalStateException("Constants class");
	}

	public static final String HTTP_TOKEN = "_cm";

	public static final String EFFECT_AD_TOKEN = "_effectAd";

	public static final String PRO_FLY_TOKEN = "_proFly";

	public static final String PICKUP_TOKEN = "_pickup";

	public static final String CLUE_PASS_TOKEN = "_cluePass";

	public final static String BVID_SWITCH = "_bvidSwitch";

	public final static String CAMPAIGN_DIS_LOCK_SUFFIX = ":camapign:fly:dislock";

}
