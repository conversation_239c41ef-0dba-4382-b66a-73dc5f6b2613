advertiser-platform.appName=cpm-adp
advertiser-platform.sso.host=dashboard-mng.bilibili.co
advertiser-platform.sso.port=0
advertiser-platform.sso.caller=ad-mng-platform
advertiser-platform.sso.apiKey=Lp2kwB8VJqaNORXWu312L3jTNCkmBIEz
advertiser-platform.sso.logoutPath=/platform/web_api/v1/logout
advertiser-platform.sso.enableHeaderToken=true
advertiser-platform.sso.cacheTimeout=180
advertiser-platform.sso.cacheSecret=3mo2alb8mx94krt5

advertiser-platform.ldap.not.filter.url=cm.bilibili.com
advertiser-platform.passport.refer.url=http://passport.bilibili.com/login
advertiser-platform.interceptor.not.filter.url=cm-mng.bilibili.co

ad.is.ping.url=false
ad.campaign.max.num=100000
ad.unit.max.num=100000
ad.creative.max.num=1000

advertiser-platform.solar.host=************
advertiser-platform.solar.port=8091
advertiser-platform.solar.cacheTimeout=180
advertiser-platform.solar.cacheSecret=fd6q0e5kbn5cyjra
advertiser-platform.solar.maxRetryTime=1
advertiser-platform.solar.baseRetryInterval=1
advertiser-platform.solar.intervalIncrease=1
advertiser-platform.solar.pool.maxWaitMillis=1000

passport.app.key=d975a3efa7eb66d3
passport.key=test
passport.auth.url=http://passport.bilibili.co/intranet/auth/cookieInfo
account.query.by.mid.url=http://account.bilibili.co/api/member/getCardByMid
archive.query.by.aid.url=http://api.bilibili.co/x/internal/v2/archive

access.control.allow.rigin=http://swagger.bilibili.co
access.control.allow.credentials=true

swagger.enable=1
ad.is.validate.video=true

platform.job.overwriteExistingJobs=true
platform.job.autoStartup=true
platform.job.startupDelay=20

http.invoker.domain=cm.bilibili.co
location.service.url=http://cm.bilibili.co/cpm-location/api/service
crm.service.url=http://cm.bilibili.co/crm/api/service

ad.budget.percentage=20
ad.campaign.budget.max.update.limit=10
ad.unit.budget.max.update.limit=10

platform.launch.support.orderby=true
lau.tag.impressions_total_threshold=2000000
lau.creative.query.stag.url=http://cm-mng.bilibili.co/platform/api/launch/mock/stag
lau.tag.max.size=20
lau.tag.max.length=10
lau.similar.tag.max.size=400

ad.unit.max.repeat.num=15
dmp.service.url=http://cm.bilibili.co/dmp/api/service
cpm.agent.service.url=http://cm.bilibili.co/agent/api/service

ad.max.cpc.target.count=3

platform.money.insufficient.mail.notify.users=<EMAIL>,<EMAIL>
platform.money.notify.accountids=10158
ad.min.cpc.bid=90


passport.auth.url=http://passport.bilibili.co/intranet/auth/cookieInfo
account.query.by.mid.url=http://account.bilibili.co/api/member/getCardByMid

archive.query.by.aid.url=http://api.bilibili.co/x/internal/v2/archive

account.query.in.mids.url=http://account.bilibili.co/api/member/getInfoByMid

passport.app.key=d975a3efa7eb66d3
passport.key=test

bfs.host=bfs.bilibili.co
bfs.port=0
bfs.accessKey=221bce6492eba70f
bfs.accessSecret=6eb80603e85842542f9736eb13b7e3
bfs.bucketname=test
bfs.categoryname=account

bfs.file.max.upload.size=**********
bfs.file.max.in.memory.size=2048000

security.bfs.host=bfs.bilibili.co
security.bfs.port=0
security.bfs.accessKey=a357118b36695483
security.bfs.accessSecret=48695398ed3b6a5f9bebb07a546732
security.bfs.bucketname=security
security.bfs.categoryname.account=test