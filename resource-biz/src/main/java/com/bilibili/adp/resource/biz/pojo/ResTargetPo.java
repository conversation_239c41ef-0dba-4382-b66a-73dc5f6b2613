package com.bilibili.adp.resource.biz.pojo;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR>
public class ResTargetPo implements Serializable {
    /**
     * 自增ID
     */
    private Integer id;

    /**
     * 定向类型
     */
    private Integer type;

    /**
     * 定向名称
     */
    private String name;

    /**
     * 是否可输入
     */
    private Integer isInput;

    /**
     * 是否可跨组选择
     */
    private Integer isMultiGroupSelected;

    /**
     * 是否有映射
     */
    private Integer hasMapping;

    /**
     * 映射类型
     */
    private Integer mappingType;

    /**
     * 排序顺序
     */
    private Integer sortOrder;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 软删除，0是有效，1是删除
     */
    private Integer isDeleted;

    /**
     * 添加时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    private static final long serialVersionUID = 1L;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getIsInput() {
        return isInput;
    }

    public void setIsInput(Integer isInput) {
        this.isInput = isInput;
    }

    public Integer getIsMultiGroupSelected() {
        return isMultiGroupSelected;
    }

    public void setIsMultiGroupSelected(Integer isMultiGroupSelected) {
        this.isMultiGroupSelected = isMultiGroupSelected;
    }

    public Integer getHasMapping() {
        return hasMapping;
    }

    public void setHasMapping(Integer hasMapping) {
        this.hasMapping = hasMapping;
    }

    public Integer getMappingType() {
        return mappingType;
    }

    public void setMappingType(Integer mappingType) {
        this.mappingType = mappingType;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Timestamp getCtime() {
        return ctime;
    }

    public void setCtime(Timestamp ctime) {
        this.ctime = ctime;
    }

    public Timestamp getMtime() {
        return mtime;
    }

    public void setMtime(Timestamp mtime) {
        this.mtime = mtime;
    }
}