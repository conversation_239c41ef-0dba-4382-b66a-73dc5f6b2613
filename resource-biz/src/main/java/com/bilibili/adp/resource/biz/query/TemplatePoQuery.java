package com.bilibili.adp.resource.biz.query;

import java.util.List;

/**
 * Created by z<PERSON><PERSON> on 2016/9/30.
 *
 * <AUTHOR>
 */
public class TemplatePoQuery {

    private List<Integer> templateIdList;

    private List<Integer> statusList;

    private Boolean isDeleted;

    public List<Integer> getTemplateIdList() {
        return templateIdList;
    }

    public TemplatePoQuery setTemplateIdList(List<Integer> ids) {
        this.templateIdList = ids;
        return this;
    }

    public List<Integer> getStatusList() {
        return statusList;
    }

    public TemplatePoQuery setStatusList(List<Integer> statusList) {
        this.statusList = statusList;
        return this;
    }

    public Boolean getIsDeleted() {
        return isDeleted;
    }

    public TemplatePoQuery setIsDeleted(Boolean deleted) {
        isDeleted = deleted;
        return this;
    }
}
