package com.bilibili.adp.resource.biz.query.qos;

import com.bilibili.adp.resource.biz.query.dos.ResAccountGroupSlotGroupDo;
import com.querydsl.core.types.Path;
import com.querydsl.core.types.PathMetadata;
import com.querydsl.core.types.dsl.DateTimePath;
import com.querydsl.core.types.dsl.NumberPath;
import com.querydsl.sql.ColumnMetadata;

import javax.annotation.Generated;
import java.sql.Types;

import static com.querydsl.core.types.PathMetadataFactory.forVariable;

/**
 * QResAccountGroupSlotGroup is a Querydsl query type for ResAccountGroupSlotGroupDo
 */
@Generated("com.querydsl.sql.codegen.MetaDataSerializer")
public class QResAccountGroupSlotGroup extends com.querydsl.sql.RelationalPathBase<ResAccountGroupSlotGroupDo> {

    private static final long serialVersionUID = *********;

    public static final QResAccountGroupSlotGroup resAccountGroupSlotGroup = new QResAccountGroupSlotGroup("res_account_group_slot_group");

    public final NumberPath<Integer> accountGroupId = createNumber("accountGroupId", Integer.class);

    public final DateTimePath<java.sql.Timestamp> ctime = createDateTime("ctime", java.sql.Timestamp.class);

    public final NumberPath<Integer> id = createNumber("id", Integer.class);

    public final NumberPath<Integer> isDeleted = createNumber("isDeleted", Integer.class);

    public final DateTimePath<java.sql.Timestamp> mtime = createDateTime("mtime", java.sql.Timestamp.class);

    public final NumberPath<Integer> slotGroupId = createNumber("slotGroupId", Integer.class);

    public final com.querydsl.sql.PrimaryKey<ResAccountGroupSlotGroupDo> primary = createPrimaryKey(id);

    public QResAccountGroupSlotGroup(String variable) {
        super(ResAccountGroupSlotGroupDo.class, forVariable(variable), "null", "res_account_group_slot_group");
        addMetadata();
    }

    public QResAccountGroupSlotGroup(String variable, String schema, String table) {
        super(ResAccountGroupSlotGroupDo.class, forVariable(variable), schema, table);
        addMetadata();
    }

    public QResAccountGroupSlotGroup(String variable, String schema) {
        super(ResAccountGroupSlotGroupDo.class, forVariable(variable), schema, "res_account_group_slot_group");
        addMetadata();
    }

    public QResAccountGroupSlotGroup(Path<? extends ResAccountGroupSlotGroupDo> path) {
        super(path.getType(), path.getMetadata(), "null", "res_account_group_slot_group");
        addMetadata();
    }

    public QResAccountGroupSlotGroup(PathMetadata metadata) {
        super(ResAccountGroupSlotGroupDo.class, metadata, "null", "res_account_group_slot_group");
        addMetadata();
    }

    public void addMetadata() {
        addMetadata(accountGroupId, ColumnMetadata.named("account_group_id").withIndex(2).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(ctime, ColumnMetadata.named("ctime").withIndex(5).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(id, ColumnMetadata.named("id").withIndex(1).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(isDeleted, ColumnMetadata.named("is_deleted").withIndex(4).ofType(Types.TINYINT).withSize(3).notNull());
        addMetadata(mtime, ColumnMetadata.named("mtime").withIndex(6).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(slotGroupId, ColumnMetadata.named("slot_group_id").withIndex(3).ofType(Types.INTEGER).withSize(10).notNull());
    }

}

