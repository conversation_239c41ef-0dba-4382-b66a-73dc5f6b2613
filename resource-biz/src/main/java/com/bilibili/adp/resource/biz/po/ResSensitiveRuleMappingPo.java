package com.bilibili.adp.resource.biz.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ResSensitiveRuleMappingPo implements Serializable {
    /**
     * 自增
     */
    private Integer id;

    /**
     * 敏感规则id
     */
    private Integer ruleId;

    /**
     * 用户mid
     */
    private Long mid;

    /**
     * 用户mid类型：1-普通mid，2-人群包id
     */
    private Integer midType;

    /**
     * 软删除: 0-有效 1-删除
     */
    private Integer isDeleted;

    /**
     * 添加时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    private static final long serialVersionUID = 1L;
}