package com.bilibili.adp.resource.biz.res_dao;

import com.bilibili.adp.resource.biz.po.ResBusinessMarkRuleDetailPo;
import com.bilibili.adp.resource.biz.po.ResSensitiveRuleDetailPo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/1/30
 **/
public interface ExtResBusMarkRuleDetailDao {

    void batchSave(@Param("recordList") List<ResBusinessMarkRuleDetailPo> records);
}
