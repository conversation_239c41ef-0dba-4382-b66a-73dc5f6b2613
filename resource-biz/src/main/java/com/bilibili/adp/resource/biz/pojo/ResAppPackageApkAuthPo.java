package com.bilibili.adp.resource.biz.pojo;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ResAppPackageApkAuthPo implements Serializable {
    /**
     * 自增id
     */
    private Integer id;

    /**
     * 权限名称
     */
    private String authName;

    /**
     * 权限code
     */
    private String authCode;

    /**
     * 删除标识 0-未删除 1-已删除
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    private static final long serialVersionUID = 1L;
}