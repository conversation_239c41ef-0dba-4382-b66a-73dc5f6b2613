package com.bilibili.adp.resource.biz.po;

import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by walker on 16/9/13.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SlotPo {

    /**自增id**/
    private Integer id;

    /**广告位ID**/
    private Integer slotId;

    /**广告位名称**/
    private String slotName;

    /**渠道ID**/
    private Integer channelId;

    /**库存**/
    private Integer inventory;

    /**是否支持cpt**/
    private Integer cpt;

    /**是否支持cpm**/
    private Integer cpm;

    /**是否支持cpc**/
    private Integer cpc;

    /**是否支持cpa**/
    private Integer cpa;

    /**是否支持cps**/
    private Integer cps;

    /**状态（1-有效，2-无效）**/
    private Integer status;

    /**软删除，0是有效，1是删除**/
    private Integer isDeleted;

    /**添加时间**/
    private Date addTime;

    /**更新时间**/
    private Date updateTime;
     
    /**模板ID**/
    private String templateIds;

}
