package com.bilibili.adp.manager.portal.webapi.commercialorder.vo.aftersales;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderCustomerInfoVo {

    @ApiModelProperty("所属订单编号")
    private String order_no;

    @ApiModelProperty("广告主账号ID")
    private Integer account_id;

    @ApiModelProperty("广告主账号名称")
    private String account_name;

    @ApiModelProperty("所属代理商名称")
    private String belong_agent_name;

    @ApiModelProperty("广告主客户公司名称")
    private String customer_company;

    @ApiModelProperty("代理商客户公司名称")
    private String agent_customer_company;

    @ApiModelProperty("下单账号类型（0-未知 1-核心代理商 2-一般代理商 3-核心品牌主 4-一般品牌主 5-内部）")
    private Integer order_creator_role;

    @ApiModelProperty("下单账号类型（0-未知 1-核心代理商 2-一般代理商 3-核心品牌主 4-一般品牌主 5-内部）")
    private String order_creator_role_desc;

    @ApiModelProperty("集团")
    private String group_name;

    @ApiModelProperty("部门")
    private String department_name;

    @ApiModelProperty("一级行业")
    private String industry_first_name;

    @ApiModelProperty("二级行业")
    private String industry_second_name;

    @ApiModelProperty("商机来源【非标商单】")
    private String opportunity_from;

    @ApiModelProperty("投放角色")
    private String advertising_role;

    @ApiModelProperty("资金池类型")
    private String finance_type_desc;
}
