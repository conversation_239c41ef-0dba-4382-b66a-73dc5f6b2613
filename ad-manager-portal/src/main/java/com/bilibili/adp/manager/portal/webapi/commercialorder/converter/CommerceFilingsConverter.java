package com.bilibili.adp.manager.portal.webapi.commercialorder.converter;

import com.alibaba.excel.EasyExcel;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.manager.portal.webapi.commercialorder.vo.filings.BatchAddFilingsVo;
import com.bilibili.adp.manager.portal.webapi.commercialorder.vo.filings.CommerceFilingsVo;
import com.bilibili.adp.manager.portal.webapi.commercialorder.vo.filings.FilingsOperateVo;
import com.bilibili.adp.manager.portal.webapi.commercialorder.vo.filings.VirtualOrderCreateVo;
import com.bilibili.adp.manager.portal.webapi.commercialorder.vo.order.DropVo;
import com.bilibili.commercialorder.soa.order.dto.req.CommerceFilingsOperateDto;
import com.bilibili.commercialorder.soa.order.dto.req.CommerceFilingsReqDto;
import com.bilibili.commercialorder.soa.order.dto.req.CommerceFilingsSaveDto;
import com.bilibili.commercialorder.soa.order.dto.req.CreateVirtualOrderReq;
import com.bilibili.commercialorder.soa.order.dto.res.BatchAddCommerceFilingsRes;
import com.bilibili.commercialorder.soa.order.dto.res.CommerceFilingsRes;
import com.bilibili.commercialorder.soa.upper.service.dto.SavePersonUpperDto;
import com.bilibili.commercialorder.soa.upper.service.dto.SoaSetUpperPowerDto;
import com.bilibili.crm.platform.api.company.dto.ProductDto;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collector;
import java.util.stream.Collectors;

/**
 * @Description 私单报备转换类
 * <AUTHOR>
 * @DATE 2022/3/14 5:12 下午
 **/
public class CommerceFilingsConverter {

    public static CommerceFilingsReqDto convert2CommerceFilingsReqDto(Integer filingsType, Long upperMid, String nickname, Integer page, Integer size){
        return CommerceFilingsReqDto.builder()
                .filingsType(filingsType)
                .upperMid(upperMid)
                .nickname(nickname)
                .page(page)
                .size(size)
                .build();
    }

    public static List<CommerceFilingsVo> convert2CommerceFilingsVos(List<CommerceFilingsRes> resList){
        if(CollectionUtils.isEmpty(resList)){
            return Collections.emptyList();
        }
        return resList.stream().map(t -> {
            CommerceFilingsVo vo = new CommerceFilingsVo();
            BeanUtils.copyProperties(t, vo);
            vo.setCtimeDesc(Utils.getTimestamp2StringBySecond(t.getCtime()));
            if(Objects.nonNull(t.getCtime())){
                vo.setCtime(t.getCtime().getTime());
            }
            return vo;
        }).collect(Collectors.toList());
    }

    public static CommerceFilingsSaveDto convert2CommerceFilingsSaveDto(Long upperMid, String remark, Integer filingsType, String creator){
        return CommerceFilingsSaveDto.builder()
                .upperMid(upperMid)
                .remark(remark)
                .filingsType(filingsType)
                .creator(creator)
                .build();
    }

    public static List<Object> validateBatchSave(MultipartFile multipartFile){
        Assert.notNull(multipartFile, "上传文件不能为空");
        String filename = multipartFile.getOriginalFilename();
        Assert.notNull(filename, "上传文件名不能为空");
        Assert.isTrue(filename.endsWith("xls") || filename.endsWith("xlsx"), "请上传xls或xlsx类型的文件");
        List<Object> contents = null;
        try {
            contents = EasyExcel.read(multipartFile.getInputStream())
                    .autoTrim(true)
                    .ignoreEmptyRow(true)
                    .autoCloseStream(true)
                    .sheet().doReadSync();
        } catch (Exception e) {
            throw new IllegalStateException("解析Excel失败：" + e.getMessage());
        }
        return contents;
    }

    public static List<CommerceFilingsSaveDto> analysisBatchSaveFilings(List<Object> contents, Integer filingsType, String creator) {
        Assert.isTrue(!CollectionUtils.isEmpty(contents), "导入内容不能为空");
        Assert.isTrue(contents.size() <= 100, "不得导入超过100条数据");

        AtomicInteger count = new AtomicInteger(1);
        List<CommerceFilingsSaveDto> list = new ArrayList<>(contents.size());
        contents.forEach(obj -> {
            Map map = (Map) obj;

            String upperMid = map.get(0) == null ? null : map.get(0).toString();
            String filingsStatus = "1";
            if(filingsType == 0){
                filingsStatus = map.get(1) == null ? null : map.get(1).toString();
                Assert.isTrue(StringUtils.isNumeric(filingsStatus), "第" + count.get() + "行可见性不能解析");
            }

            Assert.isTrue(StringUtils.isNumeric(upperMid), "第" + count.get() + "行UP主Mid不能解析");
            list.add(CommerceFilingsSaveDto.builder()
                    .upperMid(Long.valueOf(upperMid))
                    .filingsStatus(Integer.valueOf(filingsStatus))
                    .filingsType(filingsType)
                    .creator(creator)
                    .build());
            count.incrementAndGet();
        });
        return list;
    }

    public static BatchAddFilingsVo convert2BatchAddFilingsVo(BatchAddCommerceFilingsRes res){
        if(Objects.isNull(res)){
            return null;
        }
        return BatchAddFilingsVo.builder()
                .successCnt(res.getSuccessCnt())
                .failCnt(res.getFailCnt())
                .failUpperMids(res.getFailUpperMids())
                .build();
    }

    public static CommerceFilingsOperateDto convert2CommerceFilingsOperateDto(FilingsOperateVo vo){
        return CommerceFilingsOperateDto.builder()
                .ids(vo.getIds())
                .type(vo.getType())
                .build();
    }

    public static CreateVirtualOrderReq convert2CreateVirtualOrderReq(VirtualOrderCreateVo vo){
        CreateVirtualOrderReq req = new CreateVirtualOrderReq();
        BeanUtils.copyProperties(vo, req);
        return req;
    }

    public static List<DropVo> convert2DropVos(List<ProductDto> dtos){
        if(CollectionUtils.isEmpty(dtos)){
            return Collections.emptyList();
        }
        return dtos.stream().map(t -> DropVo.builder()
                .id(t.getId().longValue())
                .name(t.getName())
                .build()).collect(Collectors.toList());
    }
}
