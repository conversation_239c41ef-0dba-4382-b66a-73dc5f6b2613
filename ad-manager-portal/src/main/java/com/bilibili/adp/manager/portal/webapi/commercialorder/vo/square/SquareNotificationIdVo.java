/**
 * Inc.
 * Copyright (c) 2004-2022 All Rights Reserved.
 */
package com.bilibili.adp.manager.portal.webapi.commercialorder.vo.square;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import io.swagger.annotations.ApiModelProperty;

/**
 * 新任务广场主键id模型。
 * 
 * <AUTHOR>
 * @version $Id: SquareNotificationIdVo.java, v 0.1 2022-11-10 5:33 PM <PERSON> Exp $$
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SquareNotificationIdVo implements Serializable {

    private static final long serialVersionUID = -3328559916642661288L;

    @ApiModelProperty("主键id")
    private Long              id;

}