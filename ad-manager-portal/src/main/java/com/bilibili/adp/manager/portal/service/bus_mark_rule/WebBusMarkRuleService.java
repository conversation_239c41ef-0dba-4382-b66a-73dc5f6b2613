package com.bilibili.adp.manager.portal.service.bus_mark_rule;

import com.bilibili.adp.account.dto.IndustryCategoryDto;
import com.bilibili.adp.account.dto.IndustryCategoryParentDto;
import com.bilibili.adp.common.util.Page;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.manager.portal.webapi.res.bus_mark_rule.vo.BusMarkRuleVo;
import com.bilibili.adp.manager.portal.webapi.res.bus_mark_rule.vo.CreateBusMarkRuleVo;
import com.bilibili.adp.manager.portal.webapi.res.bus_mark_rule.vo.QueryBusMarkRuleVo;
import com.bilibili.adp.manager.portal.webapi.res.sensitive_rule.vo.CategoryNodeVo;
import com.bilibili.adp.resource.api.bus_mark_rule.dto.AdSysDto;
import com.bilibili.adp.resource.api.bus_mark_rule.dto.BusMarkRuleDto;
import com.bilibili.adp.resource.api.bus_mark_rule.dto.CreateBusMarkRuleDto;
import com.bilibili.adp.resource.api.bus_mark_rule.dto.QueryBusMarkRuleDto;
import com.bilibili.adp.resource.api.common.BusMarkRuleAdSysEnum;
import com.bilibili.adp.resource.api.common.BusMarkRuleDimensionEnum;
import com.bilibili.adp.resource.api.common.BusMarkRuleStatusEnum;
import com.bilibili.local.platform.api.common.dto.IdNamePairDto;
import com.bilibili.location.api.bus_mark.dto.BusMarkDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019/2/20
 **/
@Slf4j
@Service
public class WebBusMarkRuleService {

    public QueryBusMarkRuleDto vo2dto (QueryBusMarkRuleVo vo) {

        return QueryBusMarkRuleDto.builder()
                .ruleName(vo.getRule_name())
                .page(Page.valueOf(
                        vo.getPage() == null ? 1 : vo.getPage(),
                        vo.getSize() == null ? 10 : vo.getSize()))
                .build();
    }

    public List<BusMarkRuleVo> dtos2vos (List<BusMarkRuleDto> dtos) {

        if (CollectionUtils.isEmpty(dtos)) {
            return Collections.emptyList();
        }
        return dtos.stream().map(this::dto2vo).collect(Collectors.toList());
    }

    public BusMarkRuleVo dto2vo (BusMarkRuleDto dto) {

        BusMarkRuleVo vo = BusMarkRuleVo.builder()
                .id(dto.getId())
                .rule_name(dto.getRuleName())
                .dimension_type(dto.getDimensionType())
                .dimension_type_desc(BusMarkRuleDimensionEnum.getByCode(dto.getDimensionType()).getDesc())
                .status(dto.getStatus())
                .status_desc(BusMarkRuleStatusEnum.getByCode(dto.getStatus()).getDesc())
                .mtime(dto.getMtime())
                .mtime_desc(Utils.getTimestamp2String(dto.getMtime(), "yyyy-MM-dd HH:mm:ss"))
                .dimension_ids(dto.getDimensionIds())
                .ad_types(dto.getAdTypes())
                .mark_ids(dto.getMarkIds())
                .build();

        List<IdNamePairDto> dimension_ids_desc = dto.getDimensionIdNameMap().entrySet().stream().map(entry -> IdNamePairDto.builder()
                .id(entry.getKey())
                .name(entry.getValue())
                .build()).collect(Collectors.toList());

        List<IdNamePairDto> ad_types_desc = dto.getAdTypes().stream().map(adType -> IdNamePairDto.builder()
                .id(adType)
                .name(BusMarkRuleAdSysEnum.getByCode(adType).getDesc())
                .build()).collect(Collectors.toList());

        List<IdNamePairDto> mark_ids_desc = dto.getMarkIdNameMap().entrySet().stream().map(entry -> IdNamePairDto.builder()
                .id(entry.getKey())
                .name(entry.getValue())
                .build()).collect(Collectors.toList());

        vo.setDimension_ids_desc(dimension_ids_desc);
        vo.setAd_types_desc(ad_types_desc);
        vo.setMark_ids_desc(mark_ids_desc);
        return vo;
    }

    public CreateBusMarkRuleDto vo2dto (CreateBusMarkRuleVo vo) {

        return CreateBusMarkRuleDto.builder()
                .id(vo.getId())
                .ruleName(vo.getRule_name())
                .dimensionType(vo.getDimension_type())
                .dimensionIds(vo.getDimension_ids())
                .adTypes(vo.getAd_types())
                .markIds(vo.getMark_ids())
                .build();
    }

    public List<CategoryNodeVo> cateNodeDtos2vos (List<IndustryCategoryParentDto> dtos) {

        if (CollectionUtils.isEmpty(dtos)) {
            return Collections.emptyList();
        }
        List<IndustryCategoryParentDto> filtered = dtos.stream().filter(dto -> !CollectionUtils.isEmpty(dto.getChildren())).collect(Collectors.toList());
        return filtered.stream().map(this::dto2vo).collect(Collectors.toList());
    }

    private CategoryNodeVo dto2vo (IndustryCategoryParentDto dto) {

        return CategoryNodeVo.builder()
                .id(dto.getId())
                .name(dto.getName())
                .children(dto.getChildren().stream().map(this::dto2vo).collect(Collectors.toList()))
                .build();
    }

    private CategoryNodeVo dto2vo (IndustryCategoryDto dto) {

        return CategoryNodeVo.builder()
                .id(dto.getId())
                .name(dto.getName())
                .build();
    }

    public List<IdNamePairDto> getAdSysIdNameMap (List<AdSysDto> adSysList) {

        if (CollectionUtils.isEmpty(adSysList)) {
            return Collections.emptyList();
        }
        return adSysList.stream().map(adType -> IdNamePairDto.builder()
                .id(adType.getId())
                .name(adType.getName())
                .build()).collect(Collectors.toList());
    }

    public List<IdNamePairDto> getBusMarkIdNameMap (List<BusMarkDto> busMarkList) {

        if (CollectionUtils.isEmpty(busMarkList)) {
            return Collections.emptyList();
        }
        return busMarkList.stream().map(busMark -> IdNamePairDto.builder()
                .id(busMark.getId())
                .name(busMark.getName())
                .build()).collect(Collectors.toList());
    }
}
