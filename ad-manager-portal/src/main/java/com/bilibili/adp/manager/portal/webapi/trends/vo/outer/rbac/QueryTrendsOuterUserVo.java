package com.bilibili.adp.manager.portal.webapi.trends.vo.outer.rbac;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @file: QueryTrendsOuterUserVo
 * @author: xuh<PERSON><PERSON>
 * @date: 2021/11/17
 * @version: 1.0
 * @description
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "B站指数更新外网用户数据模型")
public class QueryTrendsOuterUserVo {

    /**
     * 搜索类型
     */
    @ApiModelProperty(value = "搜索类型")
    private Integer condition;

    /**
     * 搜索值
     */
    @ApiModelProperty(value = "搜索值")
    private String value;

    /**
     * 页码
     */
    @ApiModelProperty(value = "页码")
    private Integer page;

    /**
     * 页面大小
     */
    @ApiModelProperty(value = "页面大小")
    private Integer size;

}
