package com.bilibili.adp.manager.portal.webapi.mas.partner;

import com.bapis.ad.cmc.partner.invite.PartnerCollaboratePageResp;
import com.bapis.ad.cmc.partner.invite.PartnerInviteServiceGrpc;
import com.bapis.ad.cmc.partner.invite.SuspendCooperateReq;
import com.bapis.ad.cmc.partner.invite.SuspendCooperateResp;
import com.bilibili.adp.manager.portal.webapi.mas.partner.converter.PartnerCollaborationConverter;
import com.bilibili.adp.manager.portal.webapi.mas.partner.vo.collaboration.CollaborationVo;
import com.bilibili.adp.web.framework.controller.BaseExportController;
import com.bilibili.adp.web.framework.core.Context;
import com.bilibili.adp.web.framework.core.Pagination;
import com.bilibili.adp.web.framework.core.Response;
import com.bilibili.rbac.filter.annotation.Security;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 服务商合作controller
 *
 * <AUTHOR>
 * @since 2023/10/13
 */
@RestController
@RequestMapping("/web_api/v1/mas/partner/collaborations")
@Api(value = "/partner/collaboration", description = "服务商合作接口")
@Slf4j
@RequiredArgsConstructor
public class PartnerCollaborationController extends BaseExportController {

    private final PartnerInviteServiceGrpc.PartnerInviteServiceBlockingStub
            partnerInviteServiceBlockingStub;

    @Security("PartnerCollaborationController_paginateCollaborations")
    @ApiOperation(value = "分页查询服务商合作记录列表")
    @RequestMapping(value = "", method = RequestMethod.GET)
    @ResponseBody
    public Response<Pagination<List<CollaborationVo>>> paginateCollaborations(
            @ApiParam("up主mid") @RequestParam(value = "mid", required = false) Long mid,
            @ApiParam("服务商名称") @RequestParam(value = "partner_name", required = false) String partnerName,
            @ApiParam("合约类型") @RequestParam(value = "contract_type", required = false) Integer contractType,
            @ApiParam("邀约状态") @RequestParam(value = "status", required = false) Integer status,
            @ApiParam("合作开始时间") @RequestParam(value = "contract_begin_date", required = false) Long beginDate,
            @ApiParam("合作结束时间") @RequestParam(value = "contract_expire_date", required = false) Long endDate,
            @ApiParam("页码") @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
            @ApiParam("分页大小") @RequestParam(value = "size", required = false, defaultValue = "20") Integer size,
            @ApiParam("是否只看专供品合约") @RequestParam(value = "onlyExclusive", required = false) Boolean onlyExclusive,
            @ApiParam("服务商pid") @RequestParam(value = "pid", required = false) String pid
    ) {
        PartnerCollaboratePageResp resp = partnerInviteServiceBlockingStub
                .withDeadlineAfter(5000, TimeUnit.MILLISECONDS)
                .withWaitForReady()
                .queryCollaborates(PartnerCollaborationConverter.buildPageReq(mid, partnerName, contractType,
                        status, beginDate, endDate, page, size,onlyExclusive,pid));

        return Response.SUCCESS(new Pagination<>(page, resp.getTotal(),
                PartnerCollaborationConverter.grpcModels2VoList(resp.getRecordsList())));
    }

    @Security("PartnerCollaborationController_exportCollaborations")
    @ApiOperation(value = "导出服务商合作记录列表")
    @RequestMapping(value = "/export", method = RequestMethod.GET)
    @ResponseBody
    public void exportCollaborations(
            HttpServletResponse response,
            @ApiParam("up主mid") @RequestParam(value = "mid", required = false) Long mid,
            @ApiParam("服务商名称") @RequestParam(value = "partner_name", required = false) String partnerName,
            @ApiParam("合约类型") @RequestParam(value = "contract_type", required = false) Integer contractType,
            @ApiParam("邀约状态") @RequestParam(value = "status", required = false) Integer status,
            @ApiParam("合作开始时间") @RequestParam(value = "contract_begin_date", required = false) Long beginDate,
            @ApiParam("合作结束时间") @RequestParam(value = "contract_expire_date", required = false) Long endDate,
            @ApiParam("是否只看专供品合约") @RequestParam(value = "onlyExclusive", required = false) Boolean onlyExclusive,
            @ApiParam("服务商pid") @RequestParam(value = "pid", required = false) String pid
    ) {
        PartnerCollaboratePageResp resp = partnerInviteServiceBlockingStub
                .withDeadlineAfter(5000, TimeUnit.MILLISECONDS)
                .withWaitForReady()
                .queryCollaborates(PartnerCollaborationConverter.buildPageReq(mid, partnerName, contractType,
                        status, beginDate, endDate, 1, Integer.MAX_VALUE,onlyExclusive,pid));

        List<CollaborationVo> voList = PartnerCollaborationConverter.grpcModels2VoList(resp.getRecordsList());

        try {
            exportExcel(response, voList, CollaborationVo.class, "服务商合作记录.xls");
        } catch (Exception e) {
            log.error("failed to exportExcel", e);
        }
    }

    @Security("PartnerCollaborationController_terminateCollaboration")
    @ApiOperation(value = "解除合作")
    @RequestMapping(value = "/{collaboration_id}/terminate", method = RequestMethod.PUT)
    @ResponseBody
    public Response<Object> terminateCollaboration(
            @ApiIgnore Context context,
            @ApiParam("合作记录id") @PathVariable("collaboration_id") Long collaborationId) {
        SuspendCooperateResp resp = partnerInviteServiceBlockingStub
                .withDeadlineAfter(5000, TimeUnit.MILLISECONDS)
                .withWaitForReady()
                .suspendCooperate(SuspendCooperateReq.newBuilder()
                        .setId(collaborationId)
                        .setOperator(context.getUsername())
                        .build());

        if (resp.getIsSuccess()) {
            return Response.SUCCESS();
        } else {
            return Response.FAIL(500, resp.getMsg());
        }
    }
}
