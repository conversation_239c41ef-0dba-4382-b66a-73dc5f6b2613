package com.bilibili.adp.manager.portal.webapi.cmc.grass.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class VideoAndUpAuthCreateVo implements Serializable {

    @ApiModelProperty("权限类型：1-稿件白名单 2-up主白名单 3-稿件黑名单 4-up主黑名单")
    private Integer authType;

    @ApiModelProperty("权限实体id(auth_type=1/3 则表示稿件的bvid)")
    private String authObjIds;

    @ApiModelProperty("权限配置附加信息")
    private String authAddition;

    private static final long serialVersionUID = -4330635071379336589L;
}
