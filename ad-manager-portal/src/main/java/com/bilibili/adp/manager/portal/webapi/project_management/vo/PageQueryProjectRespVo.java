package com.bilibili.adp.manager.portal.webapi.project_management.vo;

import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/2/18
 */
@Data
@ToString
@JsonNaming()
public class PageQueryProjectRespVo {

    private Integer pageSize;

    private Integer pageIndex;

    private Integer pageCount;

    private Long recordCount;

    private List<ProjectInfoVo> data;
}
