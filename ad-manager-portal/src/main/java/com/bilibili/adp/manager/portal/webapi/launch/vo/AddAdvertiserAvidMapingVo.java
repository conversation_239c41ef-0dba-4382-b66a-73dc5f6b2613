package com.bilibili.adp.manager.portal.webapi.launch.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * @Description TODO
 * @Date 2019/4/17 15:23
 * @Created by wangbin01
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AddAdvertiserAvidMapingVo {
    @ApiModelProperty(notes="账号ID")
    @NotNull
    private Integer account_id;
    @ApiModelProperty(notes="视频id 必须是数值！多个以逗号(,)分隔")
    @NotNull
    private String video_ids;
}
