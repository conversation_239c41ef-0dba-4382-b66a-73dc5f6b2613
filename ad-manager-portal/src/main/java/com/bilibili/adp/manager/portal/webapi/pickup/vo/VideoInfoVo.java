package com.bilibili.adp.manager.portal.webapi.pickup.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VideoInfoVo implements Serializable {
    private static final long serialVersionUID = -4130209880784363505L;
    @ApiModelProperty("av_id")
    private String av_id;

    @ApiModelProperty("bv_id")
    private String bv_id;
}
