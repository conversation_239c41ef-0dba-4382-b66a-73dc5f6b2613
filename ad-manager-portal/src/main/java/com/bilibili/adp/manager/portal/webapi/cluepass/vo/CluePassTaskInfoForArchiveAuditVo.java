package com.bilibili.adp.manager.portal.webapi.cluepass.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: brady
 * @time: 2022/4/29 6:59 下午
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CluePassTaskInfoForArchiveAuditVo {
    @ApiModelProperty("任务ID")
    private Integer task_id;

    @ApiModelProperty("任务名称")
    private String task_name;

    @ApiModelProperty("任务Logo")
    private String task_logo_url;

    @ApiModelProperty("投放日期段")
    private String launch_date;

    @ApiModelProperty("任务提交时间")
    private String ctime;

    @ApiModelProperty("任务状态")
    private Integer status;
    @ApiModelProperty("任务状态描述")
    private String status_desc;

    @ApiModelProperty("任务审核状态")
    private Integer audit_status;
    @ApiModelProperty("任务审核状态描述")
    private String audit_status_desc;

    @ApiModelProperty("任务类型")
    private Integer type;
    @ApiModelProperty("任务类型描述")
    private String type_desc;

    @ApiModelProperty("账户ID")
    private Integer account_id;
    @ApiModelProperty("账户名称")
    private String account_name;

    @ApiModelProperty("代理商ID")
    private Integer agent_id;
    @ApiModelProperty("代理商名称")
    private String agent_name;

    @ApiModelProperty("一级行业ID")
    private Integer commerce_category_first_id;
    @ApiModelProperty("一级行业名称")
    private String commerce_category_first_name;
    @ApiModelProperty("二级行业ID")
    private Integer commerce_category_second_id;
    @ApiModelProperty("二级行业名称")
    private String commerce_category_second_name;

    /**
     * 配置的设备分端创意样式跳转连接(任务链接)
     */
    @ApiModelProperty("任务链接")
    private List<PlatformJumpUrlVo> jump_urls;

    @ApiModelProperty("备注(任务要求)")
    private String remark;

    @ApiModelProperty("任务审核通过时间")
    private String latest_operate_time;

    @ApiModelProperty("任务类型")
    private Integer task_type;
}
