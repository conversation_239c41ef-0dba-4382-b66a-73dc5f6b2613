package com.bilibili.adp.manager.portal.webapi.complain;

import com.bilibili.ad.manager.api.complain.IComplainService;
import com.bilibili.ad.manager.api.complain.dto.ComplainDto;
import com.bilibili.ad.manager.api.complain.dto.QueryComplainDto;
import com.bilibili.ad.manager.common.enums.SalesTypeQueryFlag;
import com.bilibili.ad.manager.common.utils.AdmUtils;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.manager.portal.helper.ComplainHelper;
import com.bilibili.adp.manager.portal.webapi.complain.vo.ComplainVo;
import com.bilibili.adp.resource.api.slot_group.IResSlotGroupService;
import com.bilibili.adp.resource.api.slot_group.ResSlotGroupBaseDto;
import com.bilibili.adp.web.framework.controller.BaseController;
import com.bilibili.adp.web.framework.core.Context;
import com.bilibili.adp.web.framework.core.Pagination;
import com.bilibili.adp.web.framework.core.Response;
import com.bilibili.rbac.filter.annotation.Security;
import com.dianping.cat.Cat;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.sql.Timestamp;
import java.util.List;

@RestController
@RequestMapping("/web_api/v1/complain")
@Api(value = "/complain", description = "倾音投诉")
public class ComplainController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ComplainController.class);

    @Autowired
    private IComplainService complainService;
    @Autowired
    private ComplainHelper complainHelper;
    @Autowired
    private AdmUtils admUtils;
    @Autowired
    private IResSlotGroupService resSlotGroupService;
    @Value("${complain.export.max.size:7000}")
    private Integer complainExportMaxSize;

    @Security("ComplainController_list")
    @ApiOperation(value = "倾音投诉列表")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    @ResponseBody
    public Response<Pagination<List<ComplainVo>>> list (
            @ApiIgnore Context context,
            @ApiParam("处理状态 0待处理 1有效 2无效 3待确认") @RequestParam(value = "status") List<Integer> status,
            @ApiParam("创意ID") @RequestParam(value = "creative_id", defaultValue = "") Long creativeId,
            @ApiParam("售卖类型 0GD/闪屏GD 1CPC/CPM 2ADX 3MAS互选 4CPT") @RequestParam(value = "sales_type_flag") Integer salesTypeFlag,
            @ApiParam("举报人") @RequestParam(value = "mid", defaultValue = "") Long mid,
            @ApiParam("举报关键字") @RequestParam(value = "complain_text", defaultValue = "") String complainText,
            @ApiParam("投诉开始时间") @RequestParam(value = "begin_time", defaultValue = "") Long beginTime,
            @ApiParam("投诉结束时间") @RequestParam(value = "end_time", defaultValue = "") Long endTime,
            @ApiParam("广告位组") @RequestParam(value = "slot_group_id", defaultValue = "") Integer slotGroupId,
            @ApiParam("举报类型") @RequestParam(value = "complain_type", defaultValue = "") Integer complainType,
            @ApiParam("对应创意被投诉次数排序标识") @RequestParam(value = "sort_flag", defaultValue = "") Integer sortFlag,
            @ApiParam("页码") @RequestParam(value = "page", defaultValue = "1") Integer page,
            @ApiParam("每页大小") @RequestParam(value = "size", defaultValue = "15") Integer pageSize
    ) throws ServiceException {

        SalesTypeQueryFlag.getByCode(salesTypeFlag);
        QueryComplainDto queryComplainDto = QueryComplainDto.builder()
                .status(status)
                .creativeId(creativeId)
                .salesTypeFlag(salesTypeFlag)
                .mid(mid)
                .complainText(complainText)
                .beginTime(beginTime == null ? null : Utils.getBeginOfDay(new Timestamp(beginTime)))
                .endTime(endTime == null ? null : Utils.getEndOfDay(new Timestamp(endTime)))
                .slotGroupId(slotGroupId)
                .complainType(complainType)
                .build();

        if(Utils.isPositive(slotGroupId)){
            ResSlotGroupBaseDto resSlotGroupBaseDto = resSlotGroupService.getGroupById(slotGroupId);
            if(null != resSlotGroupBaseDto && CollectionUtils.isNotEmpty(resSlotGroupBaseDto.getSlotIds())){
                queryComplainDto.setSourceIds(resSlotGroupBaseDto.getSlotIds());
            }
        }

        PageResult<ComplainDto> pageResult = complainService.list(queryComplainDto,page,pageSize);
        List<ComplainVo> vos = complainHelper.buildComplainVos(pageResult.getRecords(),salesTypeFlag,sortFlag,beginTime,endTime);

        return Response.SUCCESS(new Pagination<>(page, pageResult.getTotal(),vos));
    }

    @Security("ComplainController_export")
    @ApiOperation(value = "倾音投诉列表导出")
    @RequestMapping(value = "/excel", method = RequestMethod.GET)
    @ResponseBody
    public Response<String> export (
            @ApiIgnore Context context,
            @ApiParam("处理状态 0待处理 1有效 2无效 3待确认") @RequestParam(value = "status") List<Integer> status,
            @ApiParam("创意ID") @RequestParam(value = "creative_id", defaultValue = "") Long creativeId,
            @ApiParam("售卖类型") @RequestParam(value = "sales_type_flag") Integer salesTypeFlag,
            @ApiParam("举报人") @RequestParam(value = "mid", defaultValue = "") Long mid,
            @ApiParam("举报关键字") @RequestParam(value = "complain_text", defaultValue = "") String complainText,
            @ApiParam("投诉开始时间") @RequestParam(value = "begin_time", defaultValue = "") Long beginTime,
            @ApiParam("投诉结束时间") @RequestParam(value = "end_time", defaultValue = "") Long endTime,
            @ApiParam("广告位组") @RequestParam(value = "slot_group_id", defaultValue = "") Integer slotGroupId,
            @ApiParam("举报类型") @RequestParam(value = "complainType", defaultValue = "") Integer complainType,
            @ApiParam("对应创意被投诉次数排序标识") @RequestParam(value = "sort_flag", defaultValue = "") Integer sortFlag
    ) throws ServiceException {

        new Thread(() -> {
            try {
                SalesTypeQueryFlag.getByCode(salesTypeFlag);
                QueryComplainDto queryComplainDto = QueryComplainDto.builder()
                        .status(status)
                        .creativeId(creativeId)
                        .salesTypeFlag(salesTypeFlag)
                        .mid(mid)
                        .complainText(complainText)
                        .beginTime(beginTime == null ? null : Utils.getBeginOfDay(new Timestamp(beginTime)))
                        .endTime(endTime == null ? null : Utils.getEndOfDay(new Timestamp(endTime)))
                        .slotGroupId(slotGroupId)
                        .complainType(complainType)
                        .build();

                if (Utils.isPositive(slotGroupId)) {
                    ResSlotGroupBaseDto resSlotGroupBaseDto = resSlotGroupService.getGroupById(slotGroupId);
                    if (null != resSlotGroupBaseDto && org.apache.commons.collections.CollectionUtils.isNotEmpty(resSlotGroupBaseDto.getSlotIds())) {
                        queryComplainDto.setSourceIds(resSlotGroupBaseDto.getSlotIds());
                    }
                }

                PageResult<ComplainDto> pageResult = complainService.list(queryComplainDto, 1, complainExportMaxSize);
                List<ComplainVo> vos = complainHelper.buildComplainVos(pageResult.getRecords(), salesTypeFlag, sortFlag, beginTime, endTime);

                admUtils.writeAndSendFile(this.getOperator(context), vos, ComplainVo.class,
                        AdmUtils.generateFileName(beginTime, endTime, "_complain_data.xlsx"), AdmUtils.COMPLAIN_TITLE);
            } catch (Exception e) {
                Cat.logError("ComplainController.export",e);
                LOGGER.error("导出文件失败", e);
                throw new RuntimeException(e.getMessage());
            }

        }).start();

        return Response.SUCCESS("已将导出数据文件发送至你的b站邮箱。前往查看已将导出数据文件发送至你的b站邮箱。前往查看");
    }

    @Security("ComplainController_updateComplainStatus")
    @ApiOperation(value = "更改倾音投诉信息状态")
    @RequestMapping(value = "/update/status", method = RequestMethod.PUT)
    public
    @ResponseBody
    Response<Object> updateComplainStatus(
            @ApiIgnore Context context,
            @ApiParam("投诉信息id列表") @RequestParam(value = "ids") List<Integer> ids,
            @ApiParam("有效/无效") @RequestParam("status") Integer status)
            throws ServiceException {

        complainService.updateStatus(ids,status);
        return Response.SUCCESS(null);
    }

    @Security("ComplainController_updateComment")
    @ApiOperation(value = "倾音投诉信息更新备注")
    @RequestMapping(value = "/remark", method = RequestMethod.PUT)
    public
    @ResponseBody
    Response<String> updateComment(
            @ApiIgnore Context context,
            @RequestParam("id") Integer id,
            @RequestParam("remark") String remark) throws ServiceException {

        complainService.updateRemark(id,remark);
        return Response.SUCCESS(null);
    }

}
