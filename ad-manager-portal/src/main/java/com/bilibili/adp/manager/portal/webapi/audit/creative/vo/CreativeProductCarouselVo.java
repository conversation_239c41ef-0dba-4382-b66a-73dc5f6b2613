package com.bilibili.adp.manager.portal.webapi.audit.creative.vo;

import com.bilibili.ad.manager.biz.service.creative.bo.ImageBo;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class CreativeProductCarouselVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 轮播图片列表 1到3个
     */
    private List<ImageBo> images;

    /**
     * 主题色
     */
    private String themeColor;

    /**
     * 引导图片
     */
    private ImageBo guideImage;

    /**
     * 可触发开始时间
     */
    private Integer triggerStartTime;

}