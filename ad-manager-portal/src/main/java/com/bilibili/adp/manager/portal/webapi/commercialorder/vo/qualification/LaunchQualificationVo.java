package com.bilibili.adp.manager.portal.webapi.commercialorder.vo.qualification;

import com.bilibili.adp.manager.portal.webapi.res.file.vo.BfsFileVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: brady
 * @time: 2022/3/24 9:24 下午
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LaunchQualificationVo {
    @ApiModelProperty("资质ID")
    private Long launch_qualification_id;

    @ApiModelProperty("创建日期")
    private String ctime;

    @ApiModelProperty("客户名称")
    private String customer_name;

    @ApiModelProperty("账号名称")
    private String account_name;

    @ApiModelProperty("资质类型")
    private String type_desc;

    @ApiModelProperty("资质名称")
    private String name;

    @ApiModelProperty("有效期")
    private String validity_period;

    @ApiModelProperty("资质照片")
    private List<BfsFileVo> attachments;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("是否删除 1 正常 2 删除")
    private Integer status;
}
