package com.bilibili.adp.manager.portal.webapi.res.pricing.vo;

import java.math.BigDecimal;
import java.util.List;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2016年12月27日
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class NewSlotPriceVo {

	@ApiModelProperty("id")
    private Integer id;

	@ApiModelProperty(value = "创意类型", allowableValues = "0-不限 1-静态图文 2-GIF图文 3-静态视频 4-广告位播放视频")
	private Integer creativeStyle;

	@ApiModelProperty(value = "创意类型名称", allowableValues = "0-不限 1-静态图文 2-GIF图文 3-静态视频 4-广告位播放视频")
	private String creativeStyleName;

	@ApiModelProperty("模板编号")
	private Integer templateId;

	@ApiModelProperty("模板名称")
	private String templateName;

	@ApiModelProperty("展示方式 479-普通展示 480-冷启常规 481-冷启首刷")
	private Integer inlineSalesType;

	@ApiModelProperty("基础价格")
	private BigDecimal basePrice;

	@ApiModelProperty("售卖方式 0-cpm 1-cpv 2-小时售卖 3-PD业务的cpm售卖")
	private Integer salesType;

	@ApiModelProperty("下一阶段价格")
	private BigDecimal nextStagePrice;

	@ApiModelProperty("刊例周期id")
	private Integer cycleId;

	@ApiModelProperty("唤起加收比例")
	private Integer wakeRaiseRatio;
}
