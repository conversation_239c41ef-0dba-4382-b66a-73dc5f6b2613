package com.bilibili.adp.manager.portal.webapi.acc.group.vo;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import io.swagger.annotations.ApiModelProperty;

/**
 * Created by <PERSON><PERSON><PERSON> on 2016/10/8.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class AccountGroupAllInfoVo {

    @ApiModelProperty(notes = "用户组ID")
    private Integer                 id;

    @ApiModelProperty(notes = "用户组名称")
    private String                  name;

    @ApiModelProperty(notes = "描述")
    private String                  description;

    @ApiModelProperty(notes = "角标列表")
    private List<CmMarkVo>          cm_marks;

    @ApiModelProperty(notes = "状态：1-有效 2-无效")
    private Integer                 status;

    @ApiModelProperty(notes = "用户组权限")
    private AccountGroupPrivilegeVo privilege;

    @ApiModelProperty(notes = "人群包组")
    private List<CrowdPackGroupVo>  crowd_pack_groups;

    @ApiModelProperty(notes = "人群包ids")
    private List<Integer>           crowd_pack_group_ids;
}
