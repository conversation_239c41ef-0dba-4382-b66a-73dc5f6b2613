package com.bilibili.adp.manager.portal.webapi.trends;

import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.manager.portal.service.trends.WebOuterUserService;
import com.bilibili.adp.manager.portal.webapi.trends.vo.outer.rbac.QueryTrendsOuterUserVo;
import com.bilibili.adp.manager.portal.webapi.trends.vo.outer.rbac.TrendsOuterUserInsertVo;
import com.bilibili.adp.manager.portal.webapi.trends.vo.outer.rbac.TrendsOuterUserUpdateVo;
import com.bilibili.adp.manager.portal.webapi.trends.vo.outer.rbac.TrendsOuterUserVo;
import com.bilibili.adp.manager.portal.webapi.trends.vo.status.TrendsContentStatusInfoVo;
import com.bilibili.adp.manager.portal.webapi.trends.vo.status.TrendsContentTidVo;
import com.bilibili.adp.web.framework.controller.BaseController;
import com.bilibili.adp.web.framework.core.Context;
import com.bilibili.adp.web.framework.core.Pagination;
import com.bilibili.adp.web.framework.core.Response;
import com.bilibili.rbac.filter.annotation.Security;
import com.bilibili.trends.api.control.dto.ContentStatusInsertDto;
import com.bilibili.trends.api.outer.rbac.user.ITrendsOuterUserService;
import com.bilibili.trends.api.outer.rbac.user.dto.OuterUserBaseDto;
import com.bilibili.trends.api.outer.rbac.user.soa.ISoaTrendsOuterUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

/**
 * @file: TrendsOuterRbacController
 * @author: xuhaoyu
 * @date: 2021/11/17
 * @version: 1.0
 * @description
 */
@Controller
@RequestMapping("/web_api/v1/trends/outer/user")
@Api(value = "trends/outer/user", description = "B站指数 外部用户相关")
public class TrendsOuterUserController extends BaseController {

    @Autowired
    private WebOuterUserService webOuterUserService;

    @Autowired
    private ISoaTrendsOuterUserService trendsOuterUserService;

    @ApiOperation(value = "外网用户列表查询")
    @Security(value = "TrendsOuterUserController_queryUserList")
    @RequestMapping(value = "/list",method = RequestMethod.GET)
    @ResponseBody
    public Response<Pagination<List<TrendsOuterUserVo>>> queryUserList(@ApiIgnore Context context, QueryTrendsOuterUserVo queryVo) {
        PageResult<OuterUserBaseDto> pageResult = trendsOuterUserService.queryOuterUserList(webOuterUserService.convertQueryVo2Dto(queryVo));
        return Response.SUCCESS(new Pagination<>(queryVo.getPage(), pageResult.getTotal(),
                webOuterUserService.convertDtos2Vos(pageResult.getRecords())));
    }

    @ApiOperation(value = "新增外网用户")
    @RequestMapping(value = "/insert", method = RequestMethod.POST)
    @Security(value = "TrendsOuterUserController_insert")
    @ResponseBody
    public Response<Integer> insert(@ApiIgnore Context context, @RequestBody TrendsOuterUserInsertVo vo) {
        trendsOuterUserService.insertOuterUser(webOuterUserService.convertInsertVo2Dto(vo));
        return Response.SUCCESS();
    }

    @ApiOperation(value = "修改外网用户")
    @RequestMapping(value = "/update", method = RequestMethod.PUT)
    @Security(value = "TrendsOuterUserController_update")
    @ResponseBody
    public Response<Integer> update(@ApiIgnore Context context, @RequestBody TrendsOuterUserUpdateVo vo) {
        trendsOuterUserService.updateOuterUserInfo(webOuterUserService.convertUpdateVo2Dto(vo));
        return Response.SUCCESS();
    }

    @ApiOperation(value = "删除外网用户")
    @RequestMapping(value = "/{uid}", method = RequestMethod.DELETE)
    @Security(value = "TrendsOuterUserController_delete")
    @ResponseBody
    public Response<Integer> delete(@ApiIgnore Context context, @PathVariable("uid") Long uid) {
        trendsOuterUserService.deleteOuterUser(uid);
        return Response.SUCCESS();
    }







}
