package com.bilibili.adp.manager.portal.webapi.cpt.business_side.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 2017/6/13.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BusinessSideVo {
    @ApiModelProperty("业务方id")
    private Integer id;
    @ApiModelProperty("业务方名称")
    private String name;
    @ApiModelProperty("业务方类型")
    private Integer type;
    @ApiModelProperty("业务方类型描述")
    private String type_desc;
    @ApiModelProperty("颜色")
    private String logo_color;

    @ApiModelProperty("剩余保障额度")
    private BigDecimal residual_red_packet;

    @ApiModelProperty("剩余非保障额度")
    private BigDecimal residual_cash;

    @ApiModelProperty("本月初始保障额度")
    private BigDecimal initial_red_packet;
    
    @ApiModelProperty("下月初始保障额度")
    private BigDecimal next_month_initial_red_packet;

    @ApiModelProperty("用户")
    private List<UserVo> users;

    @ApiModelProperty("状态1-有效,2-无效")
    private Integer status;
    
    @ApiModelProperty("状态描述 1-有效,2-无效")
    private String status_desc;

    @ApiModelProperty("部门id")
    private Integer department_id;

    @ApiModelProperty("部门名称")
    private String department_name;

    @ApiModelProperty("商业帐号id")
    private Integer account_id;

    @ApiModelProperty("商业帐号名称")
    private String account_name;

    @ApiModelProperty("mids")
    private String mids;
}

