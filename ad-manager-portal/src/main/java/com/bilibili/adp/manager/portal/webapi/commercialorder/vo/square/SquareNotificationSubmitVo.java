/**
 * Inc.
 * Copyright (c) 2004-2022 All Rights Reserved.
 */
package com.bilibili.adp.manager.portal.webapi.commercialorder.vo.square;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import io.swagger.annotations.ApiModelProperty;

/**
 * 新任务广场创建消息模型。
 * 
 * <AUTHOR>
 * @version $Id: SquareNotificationSubmitVo.java, v 0.1 2022-11-10 11:58 AM Tony Zhao Exp $$
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SquareNotificationSubmitVo implements Serializable {

    private static final long serialVersionUID = -4588117092476374888L;

    @ApiModelProperty("业务线/系统类型 1-新商单 2-悬赏任务 3-互选合伙人 4-招募任务")
    private Integer           businessLineType;

    @ApiModelProperty("消息分类 1-公告(所有业务线平台公告) 2-活动通知(悬赏) 3-运营通知(合伙人/招募)")
    private Integer           msgType;

    @ApiModelProperty("主标题")
    private String            title;

    @ApiModelProperty("副标题")
    private String            subTitle;

    @ApiModelProperty("消息内容描述")
    private String            content;

    @ApiModelProperty("消息配图")
    private String            imageUrl;

    @ApiModelProperty("点击之后跳转url")
    private String            linkUrl;

    @ApiModelProperty("消息投递方式 0-所有up主 1-部分up主")
    private Integer           deliverType;

    @ApiModelProperty("接收消息的用户id,若列表为空代表所有up主都接收")
    private String            userIds;

}