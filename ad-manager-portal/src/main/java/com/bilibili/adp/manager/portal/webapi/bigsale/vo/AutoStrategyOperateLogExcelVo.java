package com.bilibili.adp.manager.portal.webapi.bigsale.vo;

import com.bilibili.adp.web.framework.annotations.ExcelResources;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AutoStrategyOperateLogExcelVo {
    @ExcelResources(title = "id")
    private Integer bizId;

    @ExcelResources(title = "生效范围")
    private String bizIdType;

    @ExcelResources(title = "开始时间")
    private String startTime;

    @ExcelResources(title = "结束时间")
    private String endTime;

    @ExcelResources(title = "cvr门槛值")
    private Integer cvrMin;

    @ExcelResources(title = "acp上界")
    private BigDecimal acpMax;

    @ExcelResources(title = "生效状态")
    private String status;

    @ExcelResources(title = "状态")
    private String isDeleted;

    @ExcelResources(title = "创建人")
    private String operateUser;

    @ExcelResources(title = "操作时间")
    private String mtime;
}
