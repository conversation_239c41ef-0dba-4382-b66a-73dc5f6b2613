package com.bilibili.adp.manager.portal.webapi.commercialorder;

import com.bilibili.ad.model.PageResult;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.manager.portal.common.DropBoxVo;
import com.bilibili.adp.manager.portal.webapi.commercialorder.vo.qualification.LaunchQualificationOrderBindReqVo;
import com.bilibili.adp.manager.portal.webapi.commercialorder.vo.qualification.LaunchQualificationOrderBindVo;
import com.bilibili.adp.manager.portal.webapi.commercialorder.vo.qualification.LaunchQualificationQueryVo;
import com.bilibili.adp.manager.portal.webapi.commercialorder.vo.qualification.LaunchQualificationVo;
import com.bilibili.adp.manager.portal.webapi.res.file.vo.BfsFileVo;
import com.bilibili.adp.web.framework.controller.BaseExportController;
import com.bilibili.adp.web.framework.core.Pagination;
import com.bilibili.adp.web.framework.core.Response;
import com.bilibili.commercialorder.api.qualification.dto.SoaLaunchQualificationOrderBindDto;
import com.bilibili.commercialorder.api.qualification.dto.SoaLaunchQualificationOrderBindReq;
import com.bilibili.commercialorder.api.qualification.service.ISoaLaunchQualificationOrderService;
import com.bilibili.commercialorder.api.session.service.bid.bean.UpstreamContext;
import com.bilibili.crm.platform.common.IsValid;
import com.bilibili.sycpb.acc.api.service.soa.ISoaLaunchQualificationService;
import com.bilibili.sycpb.acc.api.soa.dto.LaunchQualificationDto;
import com.bilibili.sycpb.acc.api.soa.dto.LaunchQualificationQueryDto;
import edu.emory.mathcs.backport.java.util.Collections;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.sql.Timestamp;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: brady
 * @time: 2022/3/24 7:21 下午
 */
@Slf4j
@Api("花火投放资质相关")
@RestController
@RequestMapping(value = "/web_api/v1/launch/qualification")
public class LaunchQualificationController extends BaseExportController {
    @Autowired
    private ISoaLaunchQualificationService soaLauQuaService;
    @Autowired
    private ISoaLaunchQualificationOrderService soaLaunchQualificationOrderService;

    @ApiOperation(value = "投放资质类型")
    @RequestMapping(value = "/type", method = RequestMethod.GET)
    public Response<List<DropBoxVo>> launchQualificationType(@ApiIgnore UpstreamContext context) {
        List<Pair<Integer, String>> validList = soaLauQuaService.getValidLauQualificationTypes();
        List<DropBoxVo> box = validList.stream().map(pair -> DropBoxVo.builder().id(pair.getLeft()).name(pair.getRight()).build()).collect(Collectors.toList());
        return Response.SUCCESS(box);
    }

    @ApiOperation(value = "投放资质列表")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public Response<Pagination<List<LaunchQualificationVo>>> queryQualificationList(
            @ApiIgnore UpstreamContext context,
            @ApiParam("资质筛选条件") LaunchQualificationQueryVo queryVo) {
        PageResult<LaunchQualificationDto> lauQuaPage = soaLauQuaService.queryLaunchQualificationByPage(queryVo2Dto(queryVo));

        List<LaunchQualificationVo> vos = lauQuaDto2Vo(lauQuaPage.getRecords());
        Pagination<List<LaunchQualificationVo>> res = new Pagination<>(queryVo.getPage(), lauQuaPage.getTotal(), vos);
        return Response.SUCCESS(res);
    }

    @ApiOperation(value = "投放资质订单绑定列表")
    @RequestMapping(value = "/order/bind/list", method = RequestMethod.GET)
    public Response<Pagination<LaunchQualificationOrderBindVo>> queryQualificationOrderBindList(
            @ApiIgnore UpstreamContext context,
            @ApiParam("筛选条件") LaunchQualificationOrderBindReqVo queryVo) {

        SoaLaunchQualificationOrderBindReq queryReq = SoaLaunchQualificationOrderBindReq.builder().build();
        BeanUtils.copyProperties(queryVo, queryReq);

        queryReq.setCtimeBegin(null != queryVo.getCtimeBegin() ? new Timestamp(queryVo.getCtimeBegin()) : null);
        queryReq.setCtimeEnd(null != queryVo.getCtimeEnd() ? new Timestamp(queryVo.getCtimeEnd()) : null);

        PageResult<SoaLaunchQualificationOrderBindDto> pageResult = soaLaunchQualificationOrderService.queryBindPage(queryReq);

        List<LaunchQualificationOrderBindVo> vos = lauQuaOrderBindDto2Vo(pageResult.getRecords());
        Pagination<LaunchQualificationOrderBindVo> res = new Pagination(queryVo.getPage(), pageResult.getTotal(), vos);
        return Response.SUCCESS(res);
    }

    private List<LaunchQualificationOrderBindVo> lauQuaOrderBindDto2Vo(List<SoaLaunchQualificationOrderBindDto> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return Collections.emptyList();
        }
        return dtoList.stream().map(item -> {
            return LaunchQualificationOrderBindVo.builder()
                    .orderNo(null != item.getOrderNo() ? item.getOrderNo() + "" : "")
                    .bvId(item.getBvId())
                    .bindId(item.getBindId())
                    .lauQuaTypeDesc(item.getLauQuaTypeDesc())
                    .name(item.getName())
                    .ctime(Utils.date2String(item.getCtime()))
                    .accountName(item.getAccountName())
                    .customerName(item.getCustomerName())
                    .validity_period(buildValidityPeriod(LaunchQualificationDto.builder()
                            .quaValidFrom(item.getQuaValidFrom())
                            .quaValidTo(item.getQuaValidTo())
                            .perennial(item.getPerennial())
                            .build()))
                    .attachments(buildAttachments(LaunchQualificationDto.builder()
                            .attachmentUrls(item.getAttachmentUrls())
                            .build()))
                    .build();
        }).collect(Collectors.toList());
    }

    private List<LaunchQualificationVo> lauQuaDto2Vo(List<LaunchQualificationDto> lauQuaList) {
        if (CollectionUtils.isEmpty(lauQuaList)) {
            return Collections.emptyList();
        }
        return lauQuaList.stream().map(item -> {
            return LaunchQualificationVo.builder()
                    .launch_qualification_id(item.getId())
                    .type_desc(item.getLauQuaTypeDesc())
                    .name(item.getName())
                    .ctime(Utils.date2String(item.getCtime()))
                    .account_name(item.getAccountName())
                    .customer_name(item.getCustomerName())
                    .validity_period(buildValidityPeriod(item))
                    .attachments(buildAttachments(item))
                    .status(item.getLauQuaStatus())
                    .build();
        }).collect(Collectors.toList());
    }

    private List<BfsFileVo> buildAttachments(LaunchQualificationDto dto) {
        if (CollectionUtils.isEmpty(dto.getAttachmentUrls())) {
            return Collections.emptyList();
        }
        return dto.getAttachmentUrls().stream().map(item -> BfsFileVo.builder().url(item).build()).collect(Collectors.toList());
    }

    private String buildValidityPeriod(LaunchQualificationDto dto) {
        String fromDate = Utils.getTimestamp2String(dto.getQuaValidFrom());
        if (IsValid.TRUE.getCode().equals(dto.getPerennial())) {
            return fromDate + "~" + "长期有效";
        }
        String toDate = Utils.getTimestamp2String(dto.getQuaValidTo());
        return fromDate + "~" + toDate;
    }

    private LaunchQualificationQueryDto queryVo2Dto(LaunchQualificationQueryVo queryVo) {
        Timestamp begin = null != queryVo.getBegin_time() ? new Timestamp(queryVo.getBegin_time()) : null;
        Timestamp end = null != queryVo.getEnd_time() ? new Timestamp(queryVo.getEnd_time()) : null;
        return LaunchQualificationQueryDto.builder()
                //.accountId(queryVo.getAccount_id())
                .ctimeBegin(begin)
                .ctimeEnd(end)
                .customerId(queryVo.getCustomer_id())
                .customerNameLike(queryVo.getCustomer_name_like())
                .accountNameLike(queryVo.getAccount_name_like())
                .lauQuaType(queryVo.getType())
                .page(queryVo.getPage())
                .size(queryVo.getSize())
                .build();
    }
}
