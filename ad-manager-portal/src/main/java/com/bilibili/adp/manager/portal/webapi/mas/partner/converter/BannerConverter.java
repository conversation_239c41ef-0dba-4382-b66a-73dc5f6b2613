package com.bilibili.adp.manager.portal.webapi.mas.partner.converter;

import com.bapis.ad.product.banner.BannerDto;
import com.bapis.ad.product.banner.BannerPageReq;
import com.bapis.ad.product.banner.BannerStatus;
import com.bapis.ad.product.banner.UpsertBannerReq;
import com.bilibili.adp.manager.portal.webapi.mas.partner.vo.banner.BannerVo;
import com.bilibili.adp.manager.portal.webapi.mas.partner.vo.banner.UpsertBannerVo;
import com.google.common.base.Strings;
import edu.emory.mathcs.backport.java.util.Collections;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/8/30
 */
@SuppressWarnings("unchecked")
public class BannerConverter {

    public static BannerPageReq buildPageReq(Integer businessType,
                                             String nameLike,
                                             Integer status,
                                             Integer page,
                                             Integer size) {
        BannerPageReq.Builder builder = BannerPageReq.newBuilder()
                .setBusinessType(businessType)
                .setNameLike(Strings.nullToEmpty(nameLike))
                .setPage(page)
                .setSize(size);
        BannerStatus bannerStatus = status == null ? null : BannerStatus.forNumber(status);
        if (bannerStatus != null) {
            builder.setStatus(bannerStatus);
        }
        return builder.build();
    }

    public static BannerVo bannerDto2Vo(BannerDto bannerDto) {
        if (bannerDto == null) {
            return null;
        }

        BannerVo bannerVo = new BannerVo();
        BeanUtils.copyProperties(bannerDto, bannerVo);
        if (bannerDto.getStartTime() > 0) {
            bannerVo.setBeginTime(new Date(bannerDto.getStartTime()));
        }
        if (bannerDto.getEndTime() > 0) {
            bannerVo.setEndTime(new Date(bannerDto.getEndTime()));
        }
        if (bannerDto.getCtime() > 0) {
            bannerVo.setCreateTime(new Date(bannerDto.getCtime()));
        }
        bannerVo.setStatus(bannerDto.getStatusValue());
        return bannerVo;
    }

    public static List<BannerVo> bannerDtoList2VoList(List<BannerDto> bannerDtoList) {
        if (CollectionUtils.isEmpty(bannerDtoList)) {
            return Collections.emptyList();
        }
        return bannerDtoList.stream().map(BannerConverter::bannerDto2Vo).collect(Collectors.toList());
    }

    public static UpsertBannerReq buildUpsertReq(UpsertBannerVo vo, Integer id, String operator) {
        if (vo == null) {
            return null;
        }

        UpsertBannerReq.Builder builder = UpsertBannerReq.newBuilder()
                .setName(vo.getName())
                .setImageUrl(vo.getImageUrl())
                .setJumpUrl(vo.getJumpUrl())
                .setStartTime(vo.getBeginTime())
                .setEndTime(vo.getEndTime())
                .setSort(vo.getSort())
                .setBusinessType(vo.getBusinessType())
                .setOperator(operator);
        if (id != null) {
            builder.setId(id);
        }
        return builder.build();
    }
}
