package com.bilibili.adp.manager.portal.webapi.blacklist.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2016年10月25日
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class BlackListVo {
    @ApiModelProperty(notes="黑名单ID")
    private Integer id;

    @ApiModelProperty(notes="黑名单类型")
    private Integer type;

    @ApiModelProperty(notes="黑名单类型描述：1-ip 2-userid 3-buvid 4-sid 5-imei 6-app")
    private String type_desc;

    @ApiModelProperty(notes="黑名单值")
    private String value;

    @ApiModelProperty(notes="黑名单状态")
    private Integer status;

    @ApiModelProperty(notes="黑名单状态描述：1-有效 2-无效")
    private String status_desc;


}
