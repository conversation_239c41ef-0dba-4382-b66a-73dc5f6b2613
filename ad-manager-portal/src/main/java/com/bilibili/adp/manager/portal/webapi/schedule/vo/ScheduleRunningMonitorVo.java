package com.bilibili.adp.manager.portal.webapi.schedule.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/8/30
 */
@Data
@ApiModel("跑量监控信息")
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ScheduleRunningMonitorVo {
    @ApiModelProperty("订单id")
    private Integer orderId;
    @ApiModelProperty("订单名称")
    private String orderName;
    @ApiModelProperty("排期id")
    private Integer scheduleId;
    @ApiModelProperty("排期名称")
    private String scheduleName;
    @ApiModelProperty("订单类型名称")
    private String orderProductName;
    @ApiModelProperty("投放日期 yyyy-MM-dd")
    private String launchDate;
    @ApiModelProperty("投放时间 HH:mm:ss~HH:mm:ss")
    private String launchTime;
    @ApiModelProperty("期望展示")
    private Integer expectCpm;
    @ApiModelProperty("实际曝光")
    private String actualCpm;
    @ApiModelProperty("进度")
    private String progress;
    @ApiModelProperty("剩余cpm")
    private String remainingCpm;
    @ApiModelProperty("期望完成率，默认1，大于1则超推，否则小于1")
    private Float completionRatio;
    @ApiModelProperty("加速比，默认-1，[0,1] 表示减速，>1表示加速")
    private Float speedRatio;
    @ApiModelProperty("权重比，默认-1，取值范围[0,1]")
    private Float weightRatio;
}
