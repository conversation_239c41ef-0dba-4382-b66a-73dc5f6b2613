package com.bilibili.adp.manager.portal.webapi.enterprise.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/3
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class RejectAuditComponentVo {
    
    private List<Long> componentIds;

    private String rejectReason;
}
