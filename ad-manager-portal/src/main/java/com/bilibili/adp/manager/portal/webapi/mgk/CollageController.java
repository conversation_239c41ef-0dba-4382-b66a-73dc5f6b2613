package com.bilibili.adp.manager.portal.webapi.mgk;

import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.manager.portal.service.mgk.CollageService;
import com.bilibili.adp.manager.portal.service.mgk.WebCollageService;
import com.bilibili.adp.web.framework.controller.BaseController;
import com.bilibili.adp.web.framework.core.Context;
import com.bilibili.adp.web.framework.core.Response;
import com.bilibili.collage.api.dto.CollageWaterMarkDto;
import com.bilibili.collage.api.service.IPatternService;
import com.bilibili.collage.api.soa.ISoaPatternService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import java.io.File;
import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2018/11/14
 * 拼贴艺术公共接口
 **/
@RestController
@RequestMapping("/web_api/v1/collage")
@Api(value = "/collage", description = "拼贴艺术公共接口")
public class CollageController extends BaseController {

    @Autowired
    private WebCollageService webCollageService;

    @Autowired
    private CollageService collageService;

    @ApiOperation(value = "上传文件")
    @RequestMapping(value = "/upload", method = RequestMethod.POST)
    @ResponseBody
    public Response<String> upload(@RequestParam("file") MultipartFile multipartFile) throws IOException, ServiceException {
        File file = webCollageService.multipartToFile(multipartFile);
        String url = collageService.upload(file);
        url = collageService.replaceHttpsProtocolInUAT(url);
        if (webCollageService.isImageType(file)) {
            url = url.concat(webCollageService.getWebpSuffix(file));
        }
        return Response.SUCCESS(url);
    }

    @ApiOperation(value = "图片上传并生成水印")
    @RequestMapping(value = "/image/watermark", method = RequestMethod.POST)
    @ResponseBody
    public Response<String> watermark(@RequestParam("file") MultipartFile multipartFile) throws IOException, ServiceException {
        CollageWaterMarkDto resultDto = webCollageService.uploadAndGenalWaterMark(multipartFile.getOriginalFilename(), multipartFile.getBytes());
        return Response.SUCCESS(resultDto.getWaterMarkImgUrl());
    }
}
