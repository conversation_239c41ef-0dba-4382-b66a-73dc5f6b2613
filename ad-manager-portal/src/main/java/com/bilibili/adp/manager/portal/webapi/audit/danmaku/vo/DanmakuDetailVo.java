package com.bilibili.adp.manager.portal.webapi.audit.danmaku.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName DanmakuVo
 * <AUTHOR>
 * @Date 2024/1/8 2:16 下午
 * @Version 1.0
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class DanmakuDetailVo {
    private String danmakuId;

    private Integer accountId;

    private String groupId;

    private Integer danmakuType;

    private String danmakuTypeDesc;

    private String danmakuText;

    private String danmakuColor;

    private String danmakuIconId;

    private String danmakuPicUrl;

    private Integer danmakuStatus;

    private Integer danmakuAuditStatus;

    private String danmakuMd5;

    private String ctime;

    private String mtime;
}
