package com.bilibili.adp.manager.portal.webapi.commercialorder.vo.contract;

import com.bilibili.adp.web.framework.annotations.ExcelResources;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.sql.Timestamp;

/**
 * @Author: jinyuehai
 * @Date: 2021/8/12 5:56 下午
 * @Description:
 */

@Data
@Builder
public class UpperContractBindVo {

    @ApiModelProperty("关联的up主mid")
    @ExcelResources(title = "关联的up主mid")
    private Long upper_mid;


    @ApiModelProperty("昵称")
    @ExcelResources(title = "昵称")
    private String nickname;


    @ApiModelProperty("当前绑定的合同号")
    @ExcelResources(title = "当前绑定的合同号")
    private String effective_contract_id;

    @ApiModelProperty("合同创建时间")
    @ExcelResources(title = "合同创建时间")
    private String contract_ctime_str;

    @ApiModelProperty("合同状态")
    private Integer electronic_status;

    @ApiModelProperty("合同状态描述")
    @ExcelResources(title = "合同状态描述")
    private String electronic_status_desc;

    @ApiModelProperty("发起重签的时间")
    @ExcelResources(title = "发起重签的时间")
    private String launch_resign_time;


    @ApiModelProperty("合同模板名称")
    @ExcelResources(title = "合同模板名称")
    private String contract_name;

    @ApiModelProperty("结算信息描述")
    @ExcelResources(title = "结算信息描述")
    private String settle_info_desc;

    @ApiModelProperty("oa合同编号")
    @ExcelResources(title = "oa合同编号")
    private String oa_contract_num;

    @ApiModelProperty("oa流程编号")
    @ExcelResources(title = "oa流程编号")
    private String oa_flow_num;

    @ApiModelProperty("线下上传oa合同编号")
    @ExcelResources(title = "线下上传oa合同编号")
    private String oa_contract_num_offline;

    @ApiModelProperty("线下上传oa合同到期时间")
    @ExcelResources(title = "线下上传oa合同到期时间")
    private String oa_contract_offline_expireTime;


}
