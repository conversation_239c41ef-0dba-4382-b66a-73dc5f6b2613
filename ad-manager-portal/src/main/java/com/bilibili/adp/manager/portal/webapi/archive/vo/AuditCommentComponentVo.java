package com.bilibili.adp.manager.portal.webapi.archive.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AuditCommentComponentVo {
    @ApiModelProperty(value = "aid")
    private Long aid;
    @ApiModelProperty(value = "动态id")
    private Long dynamicId;
    @ApiModelProperty(value = "拒审原因")
    private String reason;
}
