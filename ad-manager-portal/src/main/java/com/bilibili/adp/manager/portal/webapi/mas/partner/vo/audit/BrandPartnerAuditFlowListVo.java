package com.bilibili.adp.manager.portal.webapi.mas.partner.vo.audit;

import com.bilibili.adp.web.framework.annotations.ExcelResources;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BrandPartnerAuditFlowListVo {


    /**
     * 审核id
     */
    private Long id;

    /**
     * 服务商id
     */
    private Long partnerId;

    /**
     * 服务商bid
     */
    @ExcelResources(title = "商家服务商bid")
    private Long bid;

    /**
     * 服务商名称
     */
    @ExcelResources(title = "商家服务商名称")
    private String partnerName;

    /**
     * 推广品牌
     */
    @ExcelResources(title = "推广品牌")
    private String brand;

    /**
     * 主体资质分类id
     */
    private Integer qualificationId;



    /**
     * 主体资质分类描述
     */
    @ExcelResources(title = "主体资质类型")
    private String qualificationIdStr;


    /**
     * 主营行业id列表
     */
    private List<Integer> mainCategoryIds;



    /**
     * 主营类目
     */
    @ExcelResources(title = "主营类目")
    private String mainCategoryIdStr;

    /**
     * 提交时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ExcelResources(title = "提交时间")
    private Date submitTime;

    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date auditTime;

    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ExcelResources(title = "入驻时间")
    private Date enterTime;

    /**
     * 审核状态
     */
    private Integer status;


    /**
     * 审核状态
     */
    @ExcelResources(title = "审核状态")
    private String statusStr;

    /**
     * 审核人
     */
    @ExcelResources(title = "审核人")
    private String auditor;


    /**
     * 服务商类型（商家/代理商）
     */
    @ExcelResources(title = "服务商类型（商家/代理商）")
    private String partnerTypeStr;
}
