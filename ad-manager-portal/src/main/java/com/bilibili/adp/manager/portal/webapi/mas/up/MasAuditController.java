package com.bilibili.adp.manager.portal.webapi.mas.up;


import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.log.bean.DiffItem;
import com.bilibili.adp.manager.portal.webapi.history.vo.DiffItemVo;
import com.bilibili.adp.manager.portal.webapi.mas.up.vo.RejectTitleVo;
import com.bilibili.adp.manager.portal.webapi.mas.up.vo.TitleAuditLogVo;
import com.bilibili.adp.manager.portal.webapi.mas.up.vo.TitleAuditVo;
import com.bilibili.adp.web.framework.controller.BaseExportController;
import com.bilibili.adp.web.framework.core.Context;
import com.bilibili.adp.web.framework.core.Pagination;
import com.bilibili.adp.web.framework.core.Response;
import com.bilibili.mas.api.soa.IMasSoaUpService;
import com.bilibili.mas.api.upper.dto.QueryTitleAuditParam;
import com.bilibili.mas.api.upper.dto.TitleAuditDto;
import com.bilibili.mas.api.upper.dto.TitleAuditLogDto;
import com.bilibili.rbac.filter.annotation.Security;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Controller
@RequestMapping("/web_api/v1/mas/audit")
@Api(value = "/mas/audit", description = "MAS 审核相关")
public class MasAuditController extends BaseExportController {

    @Autowired
    private IMasSoaUpService masSoaUpService;

    @Security("MasAuditController_commentTitleList")
    @ApiOperation("评论标题审核列表")
    @RequestMapping(value = "/comment_title/list", method = RequestMethod.GET)
    @ResponseBody
    public Response<Pagination<List<TitleAuditVo>>> commentTitleList (
            @ApiIgnore HttpServletResponse response,
            @RequestParam(value = "upper_mid", required = false) @ApiParam("UP主ID") Long upperMid,
            @RequestParam(value = "upper_name", required = false) @ApiParam("UP主昵称") String upperName,
            @RequestParam(value = "comment_id", required = false) @ApiParam("评论ID") String commentId,
            @RequestParam(value = "another_name", required = false) @ApiParam("商品别名") String anotherName,
            @RequestParam(value = "from_ctime", required = false) @ApiParam("创建时间从") Long fromCtime,
            @RequestParam(value = "to_ctime", required = false) @ApiParam("创建时间到") Long toCtime,
            @RequestParam(value = "item_id", required = false) @ApiParam("商品ID") String itemId,
            @RequestParam(value = "audit_status", required = false) @ApiParam("审核状态（null-全部 1-接受 2-驳回）") Integer auditStatus,
            @RequestParam(value = "page", defaultValue = "1") @ApiParam("页码") int page,
            @RequestParam(value = "size", defaultValue = "20") @ApiParam("页长") int size) {
        PageResult<TitleAuditDto> pageResult = masSoaUpService.commentTitleAuditList(QueryTitleAuditParam.builder()
                .upperMid(upperMid)
                .upperName(upperName)
                .commentId(commentId)
                .anotherName(anotherName)
                .fromCtime(fromCtime)
                .toCtime(toCtime)
                .itemId(itemId)
                .auditStatus(auditStatus)
                .page(page)
                .size(size)
                .build());
        return Response.SUCCESS(new Pagination<>(page, pageResult.getTotal(),
                titleAuditDtos2Vos(pageResult.getRecords())));
    }

    private List<TitleAuditVo> titleAuditDtos2Vos(List<TitleAuditDto> pageResult) {
        List<TitleAuditVo> vos = new ArrayList<>();
        if(CollectionUtils.isEmpty(pageResult)){
            return vos;
        }
        vos = pageResult.stream().map(dto->{
            return TitleAuditVo.builder()
                    .id(dto.getId())
                    .comment_id(dto.getCommentId())
                    .upper_mid(dto.getUpperMid())
                    .upper_name(dto.getUpperName())
                    .another_name(dto.getAnotherName())
                    .audit_status(dto.getAuditStatus())
                    .audit_status_desc(dto.getAuditStatusDesc())
                    .item_id(dto.getItemId())
                    .item_name(dto.getItemName())
                    .category_first_name(dto.getCategoryFirstName())
                    .price(dto.getPrice())
                    .jump_url(dto.getJumpUrl())
                    .ctime(dto.getCtime())
                    .mtime(dto.getMtime())
                    .ctime_desc(dto.getCtimeDesc())
                    .mtime_desc(dto.getMtimeDesc())
                    .build();
        }).collect(Collectors.toList());
        return vos;
    }

    @Security("MasAuditController_commentTitleReject")
    @ApiOperation("批量驳回标题")
    @RequestMapping(value = "/comment_title/reject", method = RequestMethod.PUT)
    @ResponseBody
    public Response<Integer> commentTitleReject(
            @ApiIgnore Context context,
            @ApiIgnore HttpServletResponse response,
            @RequestBody RejectTitleVo rejectTitleVo) {
        Integer ans = masSoaUpService.commentTitleReject(rejectTitleVo.getReject_reason(),rejectTitleVo.getIds(),getOperator(context));
        return Response.SUCCESS(ans);
    }

    @Security("MasAuditController_commentTitleHistory")
    @ApiOperation("评论标题审核历史")
    @RequestMapping(value = "/comment_title/history", method = RequestMethod.GET)
    @ResponseBody
    public Response<List<TitleAuditLogVo>> commentTitleHistory(
            @ApiIgnore HttpServletResponse response,
            @RequestParam(value = "id", required = false) @ApiParam("主键ID") Integer id) {
        List<TitleAuditLogDto> dtos = masSoaUpService.commentTitleHistory(id);
        return Response.SUCCESS(titleAuditLogDto2Vos(dtos));
    }

    private List<TitleAuditLogVo> titleAuditLogDto2Vos(List<TitleAuditLogDto> dtos) {
        List<TitleAuditLogVo> vos = new ArrayList<>();
        if(CollectionUtils.isEmpty(dtos)){
            return vos;
        }
        vos = dtos.stream().map(dto->{
            TitleAuditLogVo vo =  new TitleAuditLogVo();
            vo.setType(dto.getType());
            vo.setOperator_username(dto.getOperatorUsername());
            vo.setOperator_type(dto.getOperatorType());
            vo.setValue(diffItemDto2Vos(dto.getValue()));
            vo.setCreated_at(dto.getCreatedAt().getTime());
            vo.setTitle_audit_operate_type(dto.getTitleAuditOperateType());
            vo.setTitle_audit_operate_type_desc(dto.getTitleAuditOperateTypeDesc());
            return vo;
        }).collect(Collectors.toList());
        return vos;
    }

    private List<DiffItemVo> diffItemDto2Vos(List<DiffItem> diffItems){
        List<DiffItemVo> vos = new ArrayList<>();
        if(CollectionUtils.isEmpty(diffItems)){
            return vos;
        }
        vos = diffItems.stream().map(dto->{
          return DiffItemVo.builder()
                  .desc(dto.getDesc())
                  .new_value(dto.getNewValue())
                  .old_value(dto.getOldValue())
                  .build();
        }).collect(Collectors.toList());
        return vos;
    }
}
