package com.bilibili.adp.manager.portal.webapi.mas.task.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/4/8
 **/

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SaveUpTaskVideoVo {
    @ApiModelProperty("MID")
    private Integer mid;
    @ApiModelProperty("任务ID")
    private Integer task_id;
    @ApiModelProperty("视频ID")
    private List<Long> avids;
    @ApiModelProperty("绑定/解绑位置 1-推荐位 2-弹幕")
    private Integer place_type;
}
