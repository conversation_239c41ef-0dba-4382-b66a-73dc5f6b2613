package com.bilibili.adp.manager.portal.webapi.res.mspermission;

import com.bapis.ad.brand.mbm.*;
import com.bilibili.ad.manager.biz.config.GrpcConfig;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.manager.portal.converter.BrandMainSiteAccountPermissionConverter;
import com.bilibili.adp.manager.portal.utils.RpcBuilderUtils;
import com.bilibili.adp.manager.portal.webapi.res.mspermission.vo.BrandMainSiteAccountConfigVo;
import com.bilibili.adp.passport.api.dto.UserInfoDto;
import com.bilibili.adp.passport.api.service.IPassportService;
import com.bilibili.adp.web.framework.controller.BaseController;
import com.bilibili.adp.web.framework.core.Context;
import com.bilibili.adp.web.framework.core.Pagination;
import com.bilibili.adp.web.framework.core.Response;
import com.bilibili.rbac.filter.annotation.Security;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import pleiades.venus.starter.rpc.client.RPCClient;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/10/23 15:04
 */
@Slf4j
@Api("品牌主站账号权限管理")
@RestController
@RequestMapping(value = "/web_api/v1/res/ms_account_permission")
public class BrandMainSiteAccountPermissionController extends BaseController {

    @RPCClient(GrpcConfig.BRAND_ADP_SERVICE_NAME)
    private BrandMainSiteBusinessManageServiceGrpc.BrandMainSiteBusinessManageServiceBlockingStub mainSiteBusinessManageService;
    @Autowired
    private IPassportService passportService;

    @Security("BrandMainSiteAccountPermissionController_list")
    @ApiOperation(value = "获取配置列表")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    @ResponseBody
    public Response<Pagination<List<BrandMainSiteAccountConfigVo>>> list(
            @ApiIgnore Context context,
            @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
            @RequestParam(value = "size", required = false, defaultValue = "10") Integer size,
            @RequestParam(value = "mid", required = false, defaultValue = "") List<Long> midList)
            throws ServiceException {
        GetMainSiteAccountConfigListRes res = this.mainSiteBusinessManageService.getMainSiteAccountConfigList(
                GetMainSiteAccountConfigListReq.newBuilder()
                        .setRequester(RpcBuilderUtils.buildRequester(getOperator(context)))
                        .setPageable(RpcBuilderUtils.buildPageable(page, size))
                        .addAllMid(Objects.isNull(midList) ? Collections.emptyList() : midList)
                        .build());
        RpcBuilderUtils.checkSuccess(res.getResponser());
        List<BrandMainSiteAccountConfigVo> configs = res.getAccountConfigList().stream()
                .map(BrandMainSiteAccountPermissionConverter.MAPPER::toMainSiteAccountConfigVo)
                .collect(Collectors.toList());
        return Response.SUCCESS(new Pagination<>(page, (int) res.getPageable().getTotalCount(), configs));
    }

    @Security("BrandMainSiteAccountPermissionController_detail")
    @ApiOperation(value = "获取配置详情")
    @RequestMapping(value = "/detail/{id}", method = RequestMethod.GET)
    @ResponseBody
    public Response<BrandMainSiteAccountConfigVo> detail(@ApiIgnore Context context,
                                                         @PathVariable(value = "id") Long id) throws ServiceException {
        GetMainSiteAccountConfigListRes res = this.mainSiteBusinessManageService.getMainSiteAccountConfigList(
                GetMainSiteAccountConfigListReq.newBuilder()
                        .setRequester(RpcBuilderUtils.buildRequester(getOperator(context)))
                        .setPageable(RpcBuilderUtils.buildPageable(1, 1))
                        .addAllId(Lists.newArrayList(id))
                        .build());
        RpcBuilderUtils.checkSuccess(res.getResponser());
        List<BrandMainSiteAccountConfigVo> configs = res.getAccountConfigList().stream()
                .map(BrandMainSiteAccountPermissionConverter.MAPPER::toMainSiteAccountConfigVo)
                .collect(Collectors.toList());
        return Response.SUCCESS(CollectionUtils.isEmpty(configs) ? null : configs.get(0));
    }

    @Security("BrandMainSiteAccountPermissionController_save")
    @ApiOperation(value = "保存配置(新增或者更新)")
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    @ResponseBody
    public Response<Long> save(@ApiIgnore Context context,
                               @RequestBody BrandMainSiteAccountConfigVo accountConfigVo) throws ServiceException {
        MainSiteAccountConfig mainSiteAccountConfig = BrandMainSiteAccountPermissionConverter.MAPPER.toMainSiteAccountConfig(accountConfigVo);
        SaveMainSiteAccountConfigRes res = this.mainSiteBusinessManageService.saveMainSiteAccountConfig(
                SaveMainSiteAccountConfigReq.newBuilder()
                        .setRequester(RpcBuilderUtils.buildRequester(getOperator(context)))
                        .setAccountConfig(mainSiteAccountConfig)
                        .build());
        RpcBuilderUtils.checkSuccess(res.getResponser());
        return Response.SUCCESS(res.getId());
    }

    @Security("BrandMainSiteAccountPermissionController_delete")
    @ApiOperation(value = "删除配置")
    @RequestMapping(value = "/delete/{id}", method = {RequestMethod.POST, RequestMethod.DELETE})
    @ResponseBody
    public Response<String> delete(@ApiIgnore Context context,
                                   @PathVariable(value = "id") Long id) throws ServiceException {
        DeleteMainSiteAccountConfigRes res = this.mainSiteBusinessManageService.deleteMainSiteAccountConfig(
                DeleteMainSiteAccountConfigReq.newBuilder()
                        .setRequester(RpcBuilderUtils.buildRequester(getOperator(context)))
                        .setId(id)
                        .build());
        RpcBuilderUtils.checkSuccess(res.getResponser());
        return Response.SUCCESS("");
    }

    @ApiOperation(value = "查询用户信息")
    @RequestMapping(value = "/getUser", method = RequestMethod.GET)
    @ResponseBody
    public Response<String> getUser(@ApiIgnore Context context,
                                    @RequestParam(value = "mid") Long mid) throws ServiceException {
        Map<Long, UserInfoDto> userMap = passportService.getUserMapInMids(Lists.newArrayList(mid));
        Assert.isTrue(Objects.nonNull(userMap) && userMap.containsKey(mid), "用户不存在");
        return Response.SUCCESS(userMap.get(mid).getName());
    }

}
