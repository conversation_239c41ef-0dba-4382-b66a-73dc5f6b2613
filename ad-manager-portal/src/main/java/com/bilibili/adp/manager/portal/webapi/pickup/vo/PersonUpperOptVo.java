package com.bilibili.adp.manager.portal.webapi.pickup.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class PersonUpperOptVo implements Serializable {
    private static final long serialVersionUID = -6564754893475984599L;

    @ApiModelProperty("id数组(以英文逗号分隔)")
    private String ids;

    @ApiModelProperty("操作原因(审核驳回、冻结操作必须填原因)")
    private String opt_reason;
}
