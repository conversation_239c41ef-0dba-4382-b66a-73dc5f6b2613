package com.bilibili.adp.manager.portal.webapi.ogv.convertor;

import com.bapis.ad.brand.ogv.BrandOgvContent;
import com.bapis.ad.brand.ogv.SaveBrandOgvContent;
import com.bilibili.adp.manager.portal.webapi.ogv.vo.OgvBrandContentVo;
import com.bilibili.adp.manager.portal.webapi.ogv.vo.SaveOgvBrandContentVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/5/31
 **/

@Mapper
public interface OgvBrandContentConvert {

    OgvBrandContentConvert MAPPER = Mappers.getMapper(OgvBrandContentConvert.class);

    OgvBrandContentVo rpcToVo(BrandOgvContent content);

    SaveBrandOgvContent saveVoToRpc(SaveOgvBrandContentVo vo);
}
