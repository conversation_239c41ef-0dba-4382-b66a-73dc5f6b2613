package com.bilibili.adp.manager.portal.webapi.trends;

import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.manager.portal.webapi.trends.vo.rbac.TrendsRoleVo;
import com.bilibili.adp.manager.portal.webapi.trends.vo.rbac.TrendsUserVo;
import com.bilibili.adp.web.framework.controller.BaseController;
import com.bilibili.adp.web.framework.core.Context;
import com.bilibili.adp.web.framework.core.Pagination;
import com.bilibili.adp.web.framework.core.Response;
import com.bilibili.rbac.api.dto.RoleBaseDto;
import com.bilibili.rbac.api.dto.UserBaseDto;
import com.bilibili.rbac.api.dto.UserDto;
import com.bilibili.rbac.api.service.IUserRoleService;
import com.bilibili.rbac.api.service.IUserService;
import com.bilibili.rbac.filter.annotation.Security;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @file: UserController
 * @author: xuhaoyu
 * @date: 2021/05/14
 * @version: 1.0
 * @description
 */

@Controller
@RequestMapping("/web_api/v1/trends/user")
@Api(value = "/trends/user", description = "B站指数-权限管理-用户管理相关【1.4.2】")
public class TrendsUserController extends BaseController {

    @Autowired
    private Integer trendsTenantId;
    @Autowired
    private IUserService userService;
    @Autowired
    private IUserRoleService userRoleService;

    @Security("TrendsUserController.userList")
    @ApiOperation(value = "用户列表")
    @RequestMapping(value = "", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<Pagination<List<TrendsUserVo>>> userList(
            Context context,
            @RequestParam(value = "condition", required = false, defaultValue = "0") @ApiParam("查询条件 1:名称 2:昵称 3:角色") Integer condition,
            @RequestParam(value = "value", required = false, defaultValue = "") @ApiParam("查询值") String value,
            @RequestParam(value = "page", required = false, defaultValue = "1") @ApiParam("页码") int p,
            @RequestParam(value = "size", required = false, defaultValue = "15") @ApiParam("页长") int size) throws ServiceException {
        if (condition == null || condition == 0) {
            PageResult<UserDto> pageResult = userService.listUsers(trendsTenantId, super.getOperator(context).getOperatorName(), p, size);
            if (pageResult.getTotal() > 0) {
                List<UserDto> userDtos = pageResult.getRecords();
                List<TrendsUserVo> userAllInfoVos = userDtos.stream().map(dto -> userDto2Vo(dto)).collect(Collectors.toList());
                return Response.SUCCESS(new Pagination<>(p, pageResult.getTotal(), userAllInfoVos));
            } else {
                return Response.SUCCESS(new Pagination<>(p, 0, Collections.emptyList()));
            }
        } else {
            List<UserDto> allUsers = userService.listUsers(trendsTenantId, super.getOperator(context).getOperatorName(), 1, Integer.MAX_VALUE)
                    .getRecords();
            List<UserDto> selectedUsers = allUsers.stream().filter((dto)->{
                if (condition == 1) {
                    return dto.getUserBaseDto().getUsername().contains(value);
                } else if (condition == 2) {
                    return dto.getUserBaseDto().getNickname().contains(value);
                } else if (condition == 3){
                    boolean flag = false;
                    for (RoleBaseDto role : dto.getRoles()) {
                        if (role.getName().contains(value)) {
                            flag = true;
                        }
                    }
                    return flag;
                }{
                    return false;
                }
            }).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(selectedUsers)) {
                return Response.SUCCESS(new Pagination<>(p, 0, Collections.emptyList()));
            } else {
                List<TrendsUserVo> vos = selectedUsers.stream().map(dto->userDto2Vo(dto)).collect(Collectors.toList());
                int offset = (p - 1) * size;
                if (offset >= vos.size()) {
                    return Response.SUCCESS(new Pagination<>(p, 0, Collections.emptyList()));
                } else {
                    int end = Math.min(vos.size(), offset + size);
                    List<TrendsUserVo> pagedVos = vos.subList(offset, end);
                    return Response.SUCCESS(new Pagination<>(p, vos.size(), pagedVos));
                }
            }
        }
    }


    @Security("TrendsUserController.addUser")
    @ApiOperation(value = "添加用户")
    @RequestMapping(value = "", method = RequestMethod.POST)
    public
    @ResponseBody
    Response<Integer> addUser(@ApiIgnore Context context,
                              @ApiParam("用户名(必填)") @RequestParam(value = "user_name", required = true) String userName,
                              @ApiParam("用户昵称") @RequestParam(value = "nick_name", required = false) String nickName) throws ServiceException {
        Integer userId = userService.insert(trendsTenantId, UserBaseDto.builder().nickname(nickName).username(userName.trim()).build(), super.getOperator(context));
        return Response.SUCCESS(userId);
    }

    @Security("TrendsUserController.updateUser")
    @ApiOperation(value = "编辑用户")
    @RequestMapping(value = "/{user_id}", method = RequestMethod.PUT)
    public
    @ResponseBody
    Response<Object> updateUser(@ApiIgnore Context context,
                                @ApiParam("用户ID") @PathVariable("user_id") Integer userId,
                                @ApiParam("用户昵称") @RequestParam(value = "nick_name", required = false) String nickName) throws ServiceException {
        userService.update(trendsTenantId, UserBaseDto.builder().id(userId).nickname(nickName).build(), super.getOperator(context));
        return Response.SUCCESS(null);
    }

    @Security("TrendsUserController.deleteUser")
    @ApiOperation(value = "删除用户")
    @RequestMapping(value = "/{user_id}", method = RequestMethod.DELETE)
    public
    @ResponseBody
    Response<Object> deleteUser(@ApiIgnore Context context,
                                @ApiParam("用户ID(必填)") @PathVariable("user_id") Integer userId) throws ServiceException {
        userService.delete(trendsTenantId, userId, super.getOperator(context));
        return Response.SUCCESS(null);
    }

    @Security("TrendsUserController.saveUserPrivilege")
    @ApiOperation(value = "用户权限配置")
    @RequestMapping(value = "/{user_id}/privilege", method = RequestMethod.POST)
    public
    @ResponseBody
    Response<Object> saveUserPrivilege(@ApiIgnore Context context,
                                       @ApiParam("用户ID") @PathVariable("user_id") Integer userId,
                                       @ApiParam("角色ID列表") @RequestParam(value = "role_ids", required = false) List<Integer> roleIds,
                                       @ApiParam("权限组ID列表") @RequestParam(value = "group_ids", required = false) List<Integer> groupIds) throws ServiceException {
        List<Integer> updateRoleIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(roleIds)) {
            updateRoleIds.addAll(roleIds);
        }
        if (!CollectionUtils.isEmpty(groupIds)) {
            updateRoleIds.addAll(groupIds);
        }
        userRoleService.updateUserRoles(trendsTenantId, userId, updateRoleIds, super.getOperator(context));
        return Response.SUCCESS(null);
    }

    private TrendsUserVo userDto2Vo(UserDto dto) {
        if (dto == null) {
            return null;
        }
        UserBaseDto userBaseDto = dto.getUserBaseDto();
        List<RoleBaseDto> roleDtos = dto.getRoles();
        TrendsUserVo allInfoVo = TrendsUserVo.builder().user_id(userBaseDto.getId())
                .user_name(userBaseDto.getUsername()).nick_name(userBaseDto.getNickname())
                .groups(roleDtos2Vos(roleDtos)).build();
        return allInfoVo;
    }

    private List<TrendsRoleVo> roleDtos2Vos(List<RoleBaseDto> roleDtos) {
        if (CollectionUtils.isEmpty(roleDtos)) {
            return Collections.emptyList();
        } else {
            return roleDtos.stream().map(dto -> TrendsRoleVo.builder().id(dto.getId()).name(dto.getName())
                    .level(dto.getLevel()).build()).collect(Collectors.toList());
        }
    }
}
