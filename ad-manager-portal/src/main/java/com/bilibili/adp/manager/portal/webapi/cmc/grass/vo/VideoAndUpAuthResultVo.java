package com.bilibili.adp.manager.portal.webapi.cmc.grass.vo;

import com.bilibili.adp.common.bean.Operator;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class VideoAndUpAuthResultVo implements Serializable {
    @ApiModelProperty("主键id")
    private String id;
    @ApiModelProperty("up主mid")
    private String mid;

    @ApiModelProperty("up主Name")
    private String upName;

    @ApiModelProperty("权限类型")
    private Integer authType;

    @ApiModelProperty("稿件avid")
    private String avid;

    @ApiModelProperty("稿件bvid")
    private String bvid;

    @ApiModelProperty("稿件标题")
    private String archiveTitle;

    @ApiModelProperty("默认种草词")
    private String authAddition;

    @ApiModelProperty("添加时间")
    private Timestamp ctime;

    @ApiModelProperty("操作人")
    private Operator operator;

    private static final long serialVersionUID = -1595096063785197752L;
}
