package com.bilibili.adp.manager.portal.webapi.schedule.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/10
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ContractAccountConfigDetailVo {

    @ApiModelProperty(value = "cpt轮数配置", name = "cpt_rotation_configs")
    private List<ContractAccountCptRotationConfigVo> cptRotationConfigs;

    @ApiModelProperty(value = "扭一扭配置", name = "twist_configs")
    private List<ContractAccountTwistConfigVo> twistConfigs;
}
