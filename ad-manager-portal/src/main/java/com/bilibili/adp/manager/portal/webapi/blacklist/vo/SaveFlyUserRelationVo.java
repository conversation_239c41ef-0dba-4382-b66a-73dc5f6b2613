package com.bilibili.adp.manager.portal.webapi.blacklist.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020年1月5日
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class SaveFlyUserRelationVo {

    @ApiModelProperty("UP主MID")
    private Long mid;

    @ApiModelProperty(notes = "黑名单用户MID")
    private List<Long> black_mids;

}
