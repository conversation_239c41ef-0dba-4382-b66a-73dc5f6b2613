package com.bilibili.adp.manager.portal.webapi.mgk.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @file: QueryArticleVo
 * @author: gaoming
 * @date: 2021/03/24
 * @version: 1.0
 * @description:
 **/
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class QueryArticleVo {

    @ApiModelProperty(value = "文章id")
    private List<Long> article_ids;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "行业 1-电商 2-游戏 3-网服 4-教育 5-其他")
    private List<Integer> industries;

    @ApiModelProperty(value = "状态 0-正常 1-禁用")
    private Integer article_status;

    @ApiModelProperty(value = "页面")
    private Integer page;

    @ApiModelProperty(value = "大小")
    private Integer size;
}
