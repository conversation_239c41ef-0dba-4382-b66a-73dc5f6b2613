package com.bilibili.adp.manager.portal.webapi.fb.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/6/27
 **/

@AllArgsConstructor
@NoArgsConstructor
@Data
@SuperBuilder
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class FbResourceConfigVo {

    /**
     * 自增主键
     */
    private Long id;

    /**
     * 资源ID: FB+数字
     */
    private String resourceId;

    /**
     * 资源类型: 1 - 流量非标-CPT, 2 - 流量非标-CPM, 3 - 内容非标（包装权益/植入权益）, 4 - 页面非标, 5 - 其他（资管底表录入）, 6 - 其他（策略手动录入）
     */
    private Integer type;

    /**
     * 资源一级分类
     */
    private String firstCategory;

    /**
     * 资源二级分类
     */
    private String secondCategory;

    /**
     * 资源名称
     */
    private String name;

    /**
     * 终端
     */
    private String platform;

    /**
     * 资源描述
     */
    private String description;

    /**
     * 项目级别: S+, S, A+, A, B
     */
    private String projectLevel;

    /**
     * 前端是否含商: 0 - 否, 1 - 是
     */
    private Integer containBusiness;

    /**
     * 资源核价模式
     */
    private String salesType;

    /**
     * 单位
     */
    private String unit;

    /**
     * 初始刊例单价
     */
    private Long originalPrice;

    /**
     * 折扣
     */
    private Integer discount;

    /**
     * 单位曝光数量
     */
    private Long unitCpm;

    /**
     * 时长系数
     */
    private Integer durationRatio;

    /**
     * 适用项目大类
     */
    protected String projectMajorType;
}
