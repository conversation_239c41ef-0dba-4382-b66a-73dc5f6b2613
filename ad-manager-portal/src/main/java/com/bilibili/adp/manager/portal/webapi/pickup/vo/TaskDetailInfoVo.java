package com.bilibili.adp.manager.portal.webapi.pickup.vo;

import java.math.BigDecimal;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskDetailInfoVo {
	
	@ApiModelProperty("任务id")
	private Integer id;
	
	@ApiModelProperty("任务编号")
	private Long task_no;
	
	@ApiModelProperty("任务总价")
	private BigDecimal total_task_cost;
	
	@ApiModelProperty("任务状态")
	private Integer status;
	
	@ApiModelProperty("任务状态描述")
	private String status_desc;
	
	@ApiModelProperty("客户公司名称")
	private String customer_company_name;
	
	@ApiModelProperty("任务类型（1-视频定制--品牌植入  2-视频定制--内容定制   3-动态发布）")
	private Integer task_type;
	
	@ApiModelProperty("任务类型描述")
	private String task_type_desc;
	
	@ApiModelProperty("品牌名称")
	private String brand_name;
	
	@ApiModelProperty("产品名称")
	private String product_name;
	
	@ApiModelProperty("任务标题")
	private String task_title;
	
	@ApiModelProperty("需求描述")
	private String task_desc;
	
	@ApiModelProperty("任务上线时间")
	private String online_time;
	
	@ApiModelProperty("接单截止时间")
	private String accept_order_deadline;
	
	@ApiModelProperty("上传脚本截止时间")
	private String upload_script_deadline;
	
	@ApiModelProperty("上传视频截止时间")
	private String upload_video_deadline;
	
	@ApiModelProperty("完成时间")
	private String complete_time;
	
	@ApiModelProperty("任务创建时间")
	private String ctime;
	
	@ApiModelProperty("服务类型(1-自助服务   2-销售支持服务)")
	private Integer service_type; 
	
	@ApiModelProperty("服务类型描述")
	private String service_type_desc; 
	
	@ApiModelProperty("【2.0】是否发布邀约广告（0-否  1-是）")
	private Integer is_publish_invitation_ad;
	
	@ApiModelProperty("任务分配小组类型（0-无 1-品牌支持 2-效果支持 3-平台支持）")
	private Integer assign_group_type;
	
	@ApiModelProperty("任务分配小组类型（0-无 1-品牌支持 2-效果支持 3-平台支持）")
	private String assign_group_type_desc;

	@ApiModelProperty("代理商账号名称")
	private String agent_name;


	@ApiModelProperty("是否跳过脚本（0-不跳过 1-跳过）")
	private Integer is_skip_script;

	@ApiModelProperty("是否跳过视频（0-不跳过 1-跳过）")
	private Integer is_skip_video;

	@ApiModelProperty("CRM合同号【pickup_1.5】")
	private Long crm_contract_no;

	@ApiModelProperty("【2.0】邀约广告投放类型(2-落地页  4-应用下载  102-商品)")
	private Integer invitation_ad_stype;

	@ApiModelProperty("【2.0】邀约广告投放类型描述")
	private String invitation_ad_stype_desc;

	@ApiModelProperty("【2.0】邀约广告投放位置(1-播放器下)")
	private Integer invitation_ad_position;

	@ApiModelProperty("【2.0】邀约广告投放位置描述")
	private String invitation_ad_position_desc;
}
