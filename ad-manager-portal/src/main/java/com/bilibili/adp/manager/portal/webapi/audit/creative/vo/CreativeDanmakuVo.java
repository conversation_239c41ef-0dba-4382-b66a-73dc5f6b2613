package com.bilibili.adp.manager.portal.webapi.audit.creative.vo;

import com.bilibili.enums.DanmakuAtmosphereTypeEnum;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/12 21:03
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class CreativeDanmakuVo implements Serializable {
    private static final long serialVersionUID = -2640389601350984044L;

    /**
     * 氛围类型，0：自定义，1：七夕主题
     * @see DanmakuAtmosphereTypeEnum
     */
    private Integer atmosphereType;

    /**
     * 氛围类型，0：自定义，1：七夕主题
     * @see DanmakuAtmosphereTypeEnum
     */
    private String atmosphereTypeName;

    /**
     * 按钮动效类型，1：webp，2：lottie
     * @see com.bilibili.enums.AnimationTypeEnum
     */
    private Integer btnAnimType;

    /**
     * 按钮动效链接
     */
    private String btnAnimUrl;

    /**
     * 按钮icon
     */
    private String btnIcon;

    /**
     * 按钮文案
     */
    private String btnText;

    /**
     * 弹幕头图
     */
    private String dmLeaderIcon;

    /**
     * 弹幕文案色值
     */
    private String dmTextColor;

    /**
     * 弹幕背景色值
     */
    private String dmBgColor;

    /**
     * 弹幕文案
     */
    private List<String> dmContents;
}
