package com.bilibili.adp.manager.portal.webapi.project_management.vo;

import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/2/18
 */
@Data
@ToString
@JsonNaming()
public class ProjectInfoVo {

    private Long parentProjectId;

    private String parentProjectName;

    private String projectName;

    private Long projectId;

    private Integer effectPlatform;

    private Long projectBudget;

    private Long projectBalance;

    private String projectBeginDate;

    private String projectEndDate;

    private Integer status;

    private String docContent;

    private List<CouponTemplateVo> coupons;

    private List<EffectIndustryInfoVo> effectIndustryInfos;

    private Integer effectCustomerType;

    private Integer fundType;

    private String projectTeamLeader;

    private String projectLeader;

    private List<String> projectOperators;

    private ProjectRuleVo projectRule;

}
