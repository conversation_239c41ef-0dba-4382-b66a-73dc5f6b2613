package com.bilibili.adp.manager.portal.webapi.audit.task.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class NativeCreativeRelativityQueryAuditTaskVo {

    @ApiModelProperty("排序标识 0降序 1升序")
    private Integer sort_flag;

    @ApiModelProperty("页号")
    private Integer page;

    @ApiModelProperty("页大小")
    private Integer size;

    @ApiModelProperty("任务类型 10机器一审任务 20人工一审任务 21人工质检通过任务 22人工质检驳回任务 30搁置任务 99未知")
    private List<Integer> type;

    @ApiModelProperty("任务状态 0游离 1执行中 2完成 3终止 99未知")
    private List<Integer> state;

    @ApiModelProperty("任务id")
    private List<String> id;

    @ApiModelProperty("任务创建时间起")
    private Long create_time_from;

    @ApiModelProperty("任务创建时间止")
    private Long create_time_to;

    @ApiModelProperty("任务认领时间起")
    private Long accept_time_from;

    @ApiModelProperty("任务认领时间止")
    private Long accept_time_to;

    @ApiModelProperty("任务处理时间起")
    private Long execute_time_from;

    @ApiModelProperty("任务处理时间止")
    private Long execute_time_to;

    @ApiModelProperty("任务处理人")
    private List<String> executor_name;

    @ApiModelProperty("创意id")
    private List<Integer> creative_id;

    @ApiModelProperty("创意标题")
    private String like_title;

    @ApiModelProperty("创意描述")
    private String like_description;

    @ApiModelProperty("创意长描述")
    private String like_ext_description;

    @ApiModelProperty("投放开始时间")
    private Long launch_time_from;

    @ApiModelProperty("投放结束时间")
    private Long launch_time_to;

    @ApiModelProperty("创意更新时间起")
    private Long creative_mtime_from;

    @ApiModelProperty("创意更新时间止")
    private Long creative_mtime_to;

    @ApiModelProperty("创意审核状态")
    private Integer audit_status;

    @ApiModelProperty("创意投放状态")
    private List<Integer> creative_status;

    @ApiModelProperty("创意形态")
    private List<Integer> style_ability;

    @ApiModelProperty("推广目的")
    private List<Integer> promotion_purpose_type;

    @ApiModelProperty("售卖类型")
    private Integer sales_type;

    @ApiModelProperty("广告位组")
    private List<Integer> slot_group_id;

    @ApiModelProperty("创意评级标签")
    private List<Integer> creative_grade_tag_id;

    @ApiModelProperty("个人起飞创意来源 1现金 2激励金 3签约金 4签约金-托管")
    private List<Integer> person_fly_creative_source;

    @ApiModelProperty("创意类型 1必选 2个人起飞 3内容起飞 4商业起飞 5程序化创意")
    private List<Integer> creative_type;

    @ApiModelProperty("账号id")
    private Integer account_id;

    @ApiModelProperty("账号一级行业")
    private List<Integer> first_category_id;

    @ApiModelProperty("账号二级行业")
    private List<Integer> second_category_id;

    @ApiModelProperty("账号内外部属性 0外部 1内部")
    private Integer is_inner;

    @ApiModelProperty("是否绑定了落地页组")
    private List<Integer> is_page_group;

    @ApiModelProperty("稿件一级分区")
    private List<Integer> first_partition_id;

    @ApiModelProperty("稿件二级分区")
    private List<Integer> second_partition_id;

    @ApiModelProperty("稿件avid")
    private List<Long> avid;

    @ApiModelProperty("稿件bvid")
    private List<String> bvid;

    @ApiModelProperty("dynamic_ids")
    private List<Long> dynamic_ids;
    @ApiModelProperty("room_ids")
    private List<Long> room_ids;

    @ApiModelProperty("原生稿件审核状态")
    private Integer nativeArchiveStatus;

    // 只会传一个目前
    private List<Integer> native_body_types;


    /**
     * 打上该质检规则标的
     */
    private Integer rule_id;

    @ApiModelProperty("统一一级行业标签")
    private List<Integer> united_first_industry_ids;

    @ApiModelProperty("统一二级行业标签")
    private List<Integer> united_second_industry_ids;

    @ApiModelProperty("统一三级行业标签")
    private List<Integer> united_third_industry_ids;

}
