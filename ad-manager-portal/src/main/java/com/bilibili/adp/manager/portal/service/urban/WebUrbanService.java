package com.bilibili.adp.manager.portal.service.urban;

import com.bilibili.adp.account.dto.AccountBaseDto;
import com.bilibili.adp.account.service.IQueryAccountService;
import com.bilibili.adp.common.enums.AuditStatus;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.launch.api.common.CreativeStatus;
import com.bilibili.adp.manager.portal.service.location.WebLocationService;
import com.bilibili.adp.manager.portal.webapi.urban.vo.UrbanOfflineCreativeVo;
import com.bilibili.adp.manager.portal.webapi.urban.vo.UrbanOnlineCreativeVo;
import com.bilibili.adp.resource.api.slot_group.IResSlotGroupService;
import com.bilibili.adp.resource.api.slot_group.SlotGroupSimpleDto;
import com.bilibili.local.platform.api.campaign.dto.LocCampaignDto;
import com.bilibili.local.platform.api.creative.dto.*;
import com.bilibili.local.platform.api.unit.dto.OnlineUnitDto;
import com.bilibili.local.platform.common.enums.LocCreativeStatus;
import com.bilibili.local.platform.common.enums.LocalShowStyle;
import com.bilibili.local.platform.common.utils.TimeUtils;
import com.bilibili.location.common.CmMarkEnum;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
public class WebUrbanService {

    @Autowired
    private IQueryAccountService queryAccountService;
    @Autowired
    private IResSlotGroupService resSlotGroupService;
    @Autowired
    private WebLocationService webLocationService;

    public List<UrbanOnlineCreativeVo> onlineUnitCreativeDtos2Vos(List<LauUnitCreativeDto> records) {
        if (CollectionUtils.isEmpty(records)){
            return Collections.emptyList();
        }

        Set<Integer> accountIds = Sets.newHashSet();
        Set<Integer> slotGroupIds = Sets.newHashSet();
        records.forEach(r -> {
            accountIds.add(r.getAccountId());
            if (r.getUnitDto() != null && r.getUnitDto().getSlotGroupId() != null){
                slotGroupIds.add(r.getUnitDto().getSlotGroupId());
            }
        });
        final Map<Integer, AccountBaseDto> accountMap = queryAccountService.getAccountBaseDtoMapInIds(Lists.newArrayList(accountIds));
        List<SlotGroupSimpleDto> slotGroupDtos = resSlotGroupService.getSlotGroupByIds(Lists.newArrayList(slotGroupIds));
        final Map<Integer, String> slotGroupMap = slotGroupDtos.stream().collect(Collectors.toMap(SlotGroupSimpleDto::getId, SlotGroupSimpleDto::getSlotGroupName));
        return records.stream().map(dto -> lauUnitCreativeDto2Vo(dto, accountMap, slotGroupMap)).collect(Collectors.toList());
    }

    private UrbanOnlineCreativeVo lauUnitCreativeDto2Vo(LauUnitCreativeDto dto, Map<Integer, AccountBaseDto> accountMap, Map<Integer, String> slotGroupMap) {
        UrbanOnlineCreativeVo creativeVo = UrbanOnlineCreativeVo.builder()
            .creative_id(dto.getCreativeId())
            .creative_name(dto.getCreativeName())
            .unit_id(dto.getUnitId())
            .unit_name("")
            .account_id(dto.getAccountId())
            .account_name("")
            .campaign_id(dto.getCampaignId())
            .campaign_name("")
            .slot_group_name("")
            .creative_type(dto.getCreativeType())
            .promotion_purpose_content(dto.getPromotionPurposeContent())
            .customized_imp_url(dto.getCustomizedImpUrl())
            .customized_click_url(dto.getCustomizedClickUrl())
            .title(dto.getTitle())
            .description(dto.getDescription())
            .ext_description(dto.getExtDescription())
            .video_url("")
            .video_id(dto.getVideoId())
            .ext_image_url(dto.getExtImageUrl())
            .audit_status(dto.getAuditStatus())
            .audit_status_desc(AuditStatus.getByCode(dto.getAuditStatus()).getName())
            .status(dto.getStatus())
            .reason(dto.getReason())
            .version(dto.getVersion())
            .cm_mark_name(CmMarkEnum.getByCode(dto.getCmMark()).getDesc())
            .image_urls(Collections.emptyList())
            .button_copy("")
            .button_copy_url("")
            .submit_time(Utils.getTimestamp2String(dto.getMtime()))
            .promotion_purpose_type(0)
            .promotion_purpose_type_desc("")
            .launch_times(Collections.emptyList())
            .creative_status(dto.getCreativeStatus())
            .creative_status_desc(CreativeStatus.getByCode(dto.getCreativeStatus()).getDesc())
            .scheme_url(dto.getSchemeUrl())
            .template(webLocationService.convertTemplateDtoToVo(dto.getTemplateDto()))
            .device_type("")
            .build();

        AccountBaseDto accountBaseDto = accountMap.get(dto.getAccountId());
        if (accountBaseDto != null && !Strings.isNullOrEmpty(accountBaseDto.getUsername())){
            creativeVo.setAccount_name(accountBaseDto.getUsername());
        }

        List<Timestamp> dates = dto.getDates();
        if (!CollectionUtils.isEmpty(dates)){
            creativeVo.setLaunch_times(dates.stream().map(Utils::getTimestamp2String).collect(Collectors.toList()));
        }

        CreativeButtonCopyDto buttonCopyDto = dto.getButtonCopyDto();
        if (buttonCopyDto != null){
            creativeVo.setButton_copy(Strings.isNullOrEmpty(buttonCopyDto.getContent()) ? "" : buttonCopyDto.getContent());
            creativeVo.setButton_copy_url(Strings.isNullOrEmpty(buttonCopyDto.getJumpUrl()) ? "" : buttonCopyDto.getJumpUrl());
        }

        List<ImageDto> imageDtos = dto.getImageDtos();
        if (!CollectionUtils.isEmpty(imageDtos)){
            creativeVo.setImage_urls(imageDtos.stream().map(ImageDto::getImageUrl).collect(Collectors.toList()));
        }

        LocCampaignDto campaignDto = dto.getCampaignDto();
        if (campaignDto != null){
            creativeVo.setCampaign_name(Strings.isNullOrEmpty(campaignDto.getCampaignName()) ? "" : campaignDto.getCampaignName());
        }

        OnlineUnitDto onlineUnitDto = dto.getUnitDto();
        if (onlineUnitDto != null){
            creativeVo.setUnit_name(Strings.isNullOrEmpty(onlineUnitDto.getUnitName()) ? "" : onlineUnitDto.getUnitName());
            if (onlineUnitDto.getSlotGroupId() != null){
                creativeVo.setSlot_group_name(slotGroupMap.getOrDefault(onlineUnitDto.getSlotGroupId(), ""));
            }
            if (onlineUnitDto.getPromotionPurposeType() != null){
                creativeVo.setPromotion_purpose_type(onlineUnitDto.getPromotionPurposeType().getCode());
                creativeVo.setPromotion_purpose_type_desc(onlineUnitDto.getPromotionPurposeType().getDesc());
            }
            if (onlineUnitDto.getLaunchType() != null){
                creativeVo.setDevice_type(onlineUnitDto.getLaunchType().getName());
            }
        }

        return creativeVo;
    }

    public List<UrbanOfflineCreativeVo> offlineUnitCreativeDtos2Vos(List<LocUnitCreativeDto> records) {
        if (CollectionUtils.isEmpty(records)){
            return Collections.emptyList();
        }

        List<Integer> accountIds = records.stream().map(LocUnitCreativeDto::getAccountId).distinct().collect(Collectors.toList());
        Map<Integer, AccountBaseDto> accountMap = queryAccountService.getAccountBaseDtoMapInIds(accountIds);
        return records.stream().map(dto -> offlineCreativeDto2Vo(dto, accountMap.getOrDefault(dto.getAccountId(), AccountBaseDto.builder().username("").build()).getUsername())).collect(Collectors.toList());
    }

    private UrbanOfflineCreativeVo offlineCreativeDto2Vo(LocUnitCreativeDto dto, String username){
        UrbanOfflineCreativeVo creativeVo = UrbanOfflineCreativeVo.builder()
                .account_id(dto.getAccountId())
                .account_name(username)
                .creative_id(dto.getId())
                .creative_name(dto.getCreativeName())
                .campaign_id(dto.getCampaignId())
                .campaign_name(dto.getCampaignName())
                .unit_id(dto.getOfflineUnitId())
                .unit_name(dto.getOfflineUnitName())
                .show_style(dto.getShowStyle())
                .show_style_desc(LocalShowStyle.getByCode(dto.getShowStyle()).getName())
                .image_url(dto.getImageUrl())
                .video_url("")
                .launch_times(Collections.emptyList())
                .reason(dto.getReason())
                .status(dto.getStatus())
                .status_desc(LocCreativeStatus.getByCode(dto.getStatus()).getDesc())
                .build();

        CreativeVideoDto video = dto.getVideo();
        if (video != null){
            creativeVo.setVideo_url(Strings.isNullOrEmpty(video.getUrl()) ? "" : video.getUrl());
        }
        if (!CollectionUtils.isEmpty(dto.getDates())){
            List<Timestamp> timestamps = dto.getDates().stream().filter(date -> date.getCreativeDate() != null).map(date -> Utils.getTimestamp(date.getCreativeDate().getTime())).distinct().collect(Collectors.toList());
            creativeVo.setLaunch_times(TimeUtils.getDateStringFromTimestamps(timestamps));
        }
        return creativeVo;
    }

}
