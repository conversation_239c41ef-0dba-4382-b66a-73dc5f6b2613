package com.bilibili.adp.manager.portal.webapi.mas.partner.vo.account;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 服务商账号列表项vo
 *
 * <AUTHOR>
 * @since 2023/10/16
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AccountListVo implements Serializable {

    private static final long serialVersionUID = -7198218103224728573L;

    /**
     * bid和partnerId的关联id
     */
    private Long id;

    /**
     * bid
     */
    private Long bid;

    /**
     * up主mid
     */
    private Long mid;

    /**
     * up主名称
     */
    private String upName;

    /**
     * 角色，1-管理员，2-操作员子账号
     */
    private Integer roleType;

    /**
     * 服务商信息权限，1-查看，2-编辑，3-不可见
     */
    private Integer partnerInfoState;

    /**
     * up主合作权限，1-查看，2-编辑，3-不可见
     */
    private Integer upCollaborationState;


    /**
     * 商品邀约权限，1-可编辑，2-仅查看，3-不可见
     */
    private Integer editGoodsInvite;

    /**
     * 专供品邀约管理 1可编辑、2仅查看、3不可见
     */
    private Integer editExclusiveInvite;

}
