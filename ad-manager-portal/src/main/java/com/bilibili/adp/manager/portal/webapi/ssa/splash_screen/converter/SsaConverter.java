package com.bilibili.adp.manager.portal.webapi.ssa.splash_screen.converter;

import com.bilibili.adp.manager.portal.webapi.audit.creative.vo.ComponentVo;
import com.bilibili.adp.manager.portal.webapi.cluepass.utils.DateUtil;
import com.bilibili.adp.manager.portal.webapi.ssa.splash_screen.vo.CustomizeBrandInfoVo;
import com.bilibili.adp.manager.portal.webapi.ssa.splash_screen.vo.SplashScreenBaseImageVo;
import com.bilibili.adp.manager.portal.webapi.ssa.splash_screen.vo.SsaAdditionalComponentsVo;
import com.bilibili.ssa.platform.api.splash_screen.dto.CustomizeBrandInfoDto;
import com.bilibili.ssa.platform.api.splash_screen.dto.SsaAdditionalComponentsDto;
import com.bilibili.ssa.platform.api.splash_screen.dto.SsaSplashScreenBaseImageDto;
import com.bilibili.ssa.platform.api.splash_screen.dto.SsaStoryComponentDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/4
 */
@Mapper(imports = {DateUtil.class})
public interface SsaConverter {
    SsaConverter MAPPER = Mappers.getMapper(SsaConverter.class);

    CustomizeBrandInfoVo toVo(CustomizeBrandInfoDto brandInfoDto);

    List<ComponentVo> toVos(List<SsaStoryComponentDto> components);

    @Mappings({
            @Mapping(target = "countDownComponent.activityStartTime", expression = "java(DateUtil.toStr(ssaCountDownComponentDto.getActivityStartTime()))")
    })
    SsaAdditionalComponentsVo toAdditionalComponentsVo(SsaAdditionalComponentsDto additionalComponentsDto);
}
