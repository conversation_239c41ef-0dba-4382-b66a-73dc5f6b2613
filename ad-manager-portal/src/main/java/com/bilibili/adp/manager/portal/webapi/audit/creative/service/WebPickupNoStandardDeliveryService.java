package com.bilibili.adp.manager.portal.webapi.audit.creative.service;

import com.bilibili.adp.common.util.Page;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.manager.portal.webapi.audit.creative.vo.AuditNoStandardDeliveryVo;
import com.bilibili.adp.manager.portal.webapi.audit.creative.vo.NoStandardDeliveryVo;
import com.bilibili.adp.manager.portal.webapi.audit.creative.vo.QueryNoStandardDeliveryVo;
import com.bilibili.adp.manager.portal.webapi.commercialorder.vo.order.AttachmentVo;
import com.bilibili.commercialorder.api.order.enums.CooperationType;
import com.bilibili.commercialorder.api.order.service.dto.OrderFileAuditDto;
import com.bilibili.commercialorder.soa.order.dto.OrderAttachmentDto;
import com.bilibili.crm.platform.api.pickup.dto.NoStandardDeliveryDto;
import com.bilibili.crm.platform.api.pickup.dto.QueryNoStandardDeliveryDto;
import edu.emory.mathcs.backport.java.util.Collections;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * description：审核工作台-花火非标交付件审核
 * date       ：2020/12/23 11:27 上午
 */
@Service
public class WebPickupNoStandardDeliveryService {

    public QueryNoStandardDeliveryDto queryVo2Dto(QueryNoStandardDeliveryVo vo){
        return QueryNoStandardDeliveryDto.builder()
                .orderNo(vo.getOrder_no())
                .taskNo(vo.getTask_no())
                .cooperationType(vo.getCooperation_type())
                .taskTitle(vo.getTask_title())
                .auditStatus(vo.getAudit_status())
                .pageInfo(Page.valueOf(
                        vo.getPage() == null ? 1 : vo.getPage(),
                        vo.getSize() == null ? 10 : vo.getSize()))
                .build();
    }

    public List<NoStandardDeliveryVo> dto2vo(List<NoStandardDeliveryDto> dtos){
        return dtos.stream().map(dto -> NoStandardDeliveryVo.builder()
                .id(dto.getId())
                .order_id(dto.getOrderId())
                .order_file_id(dto.getDeliveryId())
                .order_status(dto.getOrderStatus())
                .order_status_desc(dto.getOrderStatusDesc())
                .order_no(String.valueOf(dto.getOrderNo()))
                .task_no(String.valueOf(dto.getTaskNo()))
                .cooperation_type(dto.getCooperationType())
                .cooperation_type_desc(CooperationType.getDescByCode(dto.getCooperationType()))
                .cooperation_type_remark(dto.getCooperationTypeRemark())
                .order_begin_time(getDateStr(dto.getOrderBeginTime()))
                .order_end_time(getDateStr(dto.getOrderEndTime()))
                .order_create_date(getDateStr(dto.getOrderCreateDate()))
                .task_title(dto.getTaskTitle())
                .customer_name(dto.getCustomerName())
                .account_id(dto.getAccountId())
                .account_name(dto.getAccountName())
                .agent_account_id(dto.getAgentAccountId())
                .agent_account_name(dto.getAgentAccountName())
                .brand_name(dto.getBrandName())
                .mid(dto.getLongMid())
                .upper_name(dto.getUpperName())
                .mcn_name(dto.getMcnName())
                .department_id(dto.getDepartmentId())
                .department_name(dto.getDepartmentName())
                .delivery_detail(this.attachementDto2Vos(dto.getDeliveryDetail()))
                .audit_status(dto.getAuditStatus())
                .audit_status_desc(dto.getAuditStatusDesc())
                .audit_remark(dto.getAuditRemark())
                .auditor_id(dto.getAuditorId())
                .auditor_name(dto.getAuditorName())
                .build()).collect(Collectors.toList());
    }

    public OrderFileAuditDto auditVo2Dto(AuditNoStandardDeliveryVo vo){
        return OrderFileAuditDto.builder()
                .orderId(vo.getOrder_id())
                .orderFileId(vo.getOrder_file_id())
                .orderFileAuditPass(vo.getOrder_file_audit_pass())
                .reason(vo.getReason())
                .build();
    }

    private List<AttachmentVo> attachementDto2Vos(List<OrderAttachmentDto> dtos){
        if(CollectionUtils.isEmpty(dtos)){
            return Collections.emptyList();
        }
        return dtos.stream().map(dto -> AttachmentVo.builder()
                .url(dto.getUrl())
                .oss_key(dto.getOssKey())
                .file_name(dto.getName())
                .ctime(this.getDateStr(dto.getCtime()))
                .img_shoot_time(this.getDateStr(dto.getImgShootTime()))
                .build()).collect(Collectors.toList());
    }

    private String getDateStr(Timestamp date) {

        if (Objects.isNull(date)) {
            return "";
        }
        return Utils.getTimestamp2String(date, "yyyy-MM-dd HH:mm:ss");
    }

}
