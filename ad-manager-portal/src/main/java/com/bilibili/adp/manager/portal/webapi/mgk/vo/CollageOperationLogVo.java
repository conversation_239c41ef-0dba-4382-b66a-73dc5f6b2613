package com.bilibili.adp.manager.portal.webapi.mgk.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @date 2020/09/10
 **/

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CollageOperationLogVo {
    @ApiModelProperty(value = "自增ID")
    private String id;

    @ApiModelProperty(value = "对象ID")
    private String obj_id;

    @ApiModelProperty(value = "操作对象类型: 1-站点")
    private Integer obj_flag;

    @ApiModelProperty(value = "操作对象类型: 1-站点")
    private String obj_flag_desc;

    @ApiModelProperty(value = "操作类型:1-新建 2-删除 3-修改 4-复制 5-发布 6-下线 7-管理员驳回")
    private Integer operate_type;

    @ApiModelProperty(value = "操作描述")
    private String operate_type_desc;

    @ApiModelProperty(value = "操作人")
    private String operator_username;

    @ApiModelProperty(value = "操作人类型: 0-广告主 1-运营 2-系统 4-代理商 5-代理商的系统管理员 6-代理商的投放管理员 7-二级代理运营人员 100-内部LDAP登陆")
    private Integer operator_type;

    @ApiModelProperty(value = "操作人类型描述")
    private String operator_type_desc;

    @ApiModelProperty(value = "更新时间")
    private String mtime;

    @ApiModelProperty("新值")
    @JsonProperty("oldValue")
    private String oldValue;

    @ApiModelProperty("旧值")
    @JsonProperty("newValue")
    private String newValue;
}
