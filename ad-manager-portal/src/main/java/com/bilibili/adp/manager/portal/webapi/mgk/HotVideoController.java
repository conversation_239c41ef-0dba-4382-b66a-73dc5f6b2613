package com.bilibili.adp.manager.portal.webapi.mgk;

import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.util.Page;
import com.bilibili.adp.manager.portal.service.mgk.WebHotVideoService;
import com.bilibili.adp.manager.portal.webapi.mgk.vo.HotVideoListVo;
import com.bilibili.adp.web.framework.controller.BaseController;
import com.bilibili.adp.web.framework.core.Context;
import com.bilibili.adp.web.framework.core.Pagination;
import com.bilibili.adp.web.framework.core.Response;
import com.bilibili.mgk.platform.api.hot_video.dto.HotVideoBlackDto;
import com.bilibili.mgk.platform.api.hot_video.dto.HotVideoBussInterestDto;
import com.bilibili.mgk.platform.api.hot_video.dto.HotVideoDto;
import com.bilibili.mgk.platform.api.hot_video.dto.QueryHotVideoDto;
import com.bilibili.mgk.platform.api.hot_video.soa.ISoaHotVideoBlackService;
import com.bilibili.mgk.platform.api.hot_video.soa.ISoaHotVideoService;
import com.bilibili.mgk.platform.api.landing_page.dto.LandingPageConfigDto;
import com.bilibili.mgk.platform.common.MgkHotVideoBlackTypeEnum;
import com.bilibili.mgk.platform.common.MgkHotVideoDataTypeEnum;
import com.bilibili.mgk.platform.common.MgkHotVideoIsBlackEnum;
import com.bilibili.mgk.platform.common.MgkHotVideoOrderByEnum;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @file: HotVideoController
 * @author: gaoming
 * @date: 2020/12/23
 * @version: 1.0
 * @description:
 **/

@RestController
@RequestMapping("/web_api/v1/hot_videos")
@Api(value = "/hot_videos", description = "热门视频管理")
public class HotVideoController extends BaseController {

    @Autowired
    private ISoaHotVideoService hotVideoService;

    @Autowired
    private ISoaHotVideoBlackService hotVideoBlackService;

    @Autowired
    private WebHotVideoService webHotVideoService;

    @ApiOperation(value = "运营后台——热门视频列表")
    @RequestMapping(value = "", method = RequestMethod.GET)
    @ResponseBody
    public Response<Pagination<List<HotVideoListVo>>> getListWithBlack(@ApiIgnore Context context,
                                                                       @ApiParam(value = "bvid") @RequestParam(value = "bvid", defaultValue = "") String bvid,
                                                                       @ApiParam(value = "视频标题") @RequestParam(value = "title", defaultValue = "") String title,
                                                                       @ApiParam(value = "一级分区") @RequestParam(value = "tname", defaultValue = "") List<String> tNames,
                                                                       @ApiParam(value = "商业兴趣") @RequestParam(value = "buss_interest", defaultValue = "") List<Integer> bussInterest,
                                                                       @ApiParam(value = "时间类型") @RequestParam(value = "date_type", defaultValue = "0") Integer dateType,
                                                                       @ApiParam(value = "禁用状态") @RequestParam(value = "status", defaultValue = "") Integer status,
                                                                       @ApiParam(value = "排序") @RequestParam(value = "order_by", defaultValue = "") Integer orderBy,
                                                                       @ApiParam(value = "页码") @RequestParam(value = "page", defaultValue = "1") Integer page,
                                                                       @ApiParam(value = "每页大小") @RequestParam(value = "size", defaultValue = "15") Integer size) {
        QueryHotVideoDto queryHotVideoDto = QueryHotVideoDto.builder()
                .bvid(bvid)
                .titles(Strings.isNullOrEmpty(title) ? Collections.emptyList() : Arrays.asList(title.replaceAll("\\s+", " ").split(" ")))
                .tnames(tNames)
                .bussInterest(bussInterest)
                .blackStatus(status)
                .dateType(dateType == null ? MgkHotVideoDataTypeEnum.YESTERDAY.getCode() : dateType)
                .orderType(orderBy == null ? MgkHotVideoOrderByEnum.PLAY_DAILY.getCode() : orderBy)
                .page(Page.valueOf(page, size))
                .build();

        List<String> bvids = hotVideoBlackService.getBlackList(MgkHotVideoBlackTypeEnum.HOT_VIDEO.getCode()).stream().map(HotVideoBlackDto::getBvid).collect(Collectors.toList());

        queryHotVideoDto.setBlackList(bvids);

        PageResult<HotVideoDto> pageResult = hotVideoService.getHotVideoDtos(this.getOperator(context), queryHotVideoDto);

        Map<Integer, String> bussInterestMap = hotVideoService.getBussInterest().stream().collect(Collectors.toMap(HotVideoBussInterestDto::getId, HotVideoBussInterestDto::getName));

        pageResult.getRecords().forEach(record -> {
            record.setBlackStatus(bvids.contains(record.getBvid()) ? MgkHotVideoIsBlackEnum.BLACK.getCode() : MgkHotVideoIsBlackEnum.NORMAL.getCode());
            if (Strings.isNullOrEmpty(record.getBussInterest())) {
                record.setBussInterestDesc("");
                return;
            }
            List<Integer> bussInterestList = Arrays.stream(record.getBussInterest().split(",")).map(Integer::valueOf).collect(Collectors.toList());
            String bussInterestDesc = bussInterestList.stream().map(b -> bussInterestMap.getOrDefault(b, "")).collect(Collectors.joining(","));
            record.setBussInterestDesc(bussInterestDesc);
        });

        return Response.SUCCESS(new Pagination<>(page, pageResult.getTotal(), webHotVideoService.convertListDtos2Vos(pageResult.getRecords())));
    }

    @ApiOperation(value = "启用")
    @RequestMapping(value = "/enable/{bvid}", method = RequestMethod.GET)
    @ResponseBody
    public Response<String> enable(@ApiIgnore Context context,
                                   @ApiParam(value = "bvid") @PathVariable(value = "bvid") String bvid,
                                   @ApiParam(value = "收藏类型") @RequestParam(value = "black_type", defaultValue = "0") Integer blackType) {

        hotVideoBlackService.enable(getOperator(context), bvid, blackType);
        return Response.SUCCESS(null);
    }

    @ApiOperation(value = "禁用")
    @RequestMapping(value = "/disable/{bvid}", method = RequestMethod.GET)
    @ResponseBody
    public Response<String> disable(@ApiIgnore Context context,
                                    @ApiParam(value = "bvid") @PathVariable(value = "bvid") String bvid,
                                    @ApiParam(value = "收藏类型") @RequestParam(value = "", defaultValue = "0") Integer blackType) {
        hotVideoBlackService.disable(getOperator(context), bvid, blackType);
        return Response.SUCCESS(null);
    }
}
