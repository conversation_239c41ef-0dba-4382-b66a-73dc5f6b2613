package com.bilibili.adp.manager.portal.databus.dynamic;

import com.bilibili.adp.manager.portal.config.DatabusSub;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @author: guowei05
 * @mail: <EMAIL>
 * @time: 2022-09-27 21:02:58
 * @description:
 **/

@Component
public class ProhibitDynamicAdDatabus extends DatabusSub {
    @Value("${databus.appkey:}")
    private String appKey;

    @Value("${databus.secret:}")
    private String secret;

    @Value("${databus.redis.host:}")
    private String host;

    @Value("${databus.redis.port:0}")
    private Integer port;

    @Value("${databus.prohibit.dynamicTopic:DynamicFrozen-T}")
    private String topic;

    @Value("${databus.prohibit.dynamicGroup:DynamicFrozen-SycpbCpmCpmMng-S}")
    private String group;

    @Value("${databus.interval:10}")
    private Integer interval;

    @Override
    protected String getAppKey() {
        return appKey;
    }

    @Override
    protected String getSecret() {
        return secret;
    }

    @Override
    protected String getHost() {
        return host;
    }

    @Override
    protected int getPort() {
        return port;
    }

    @Override
    protected String getTopic() {
        return topic;
    }

    @Override
    protected String getGroup() {
        return group;
    }

    @Override
    protected int getInterval() {
        return interval;
    }


}
