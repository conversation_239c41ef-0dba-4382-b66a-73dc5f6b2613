package com.bilibili.adp.manager.portal.webapi.live.vo;

import com.bapis.ad.cmc.up.CmcBlackInfo;
import com.bilibili.bjcom.util.mybatis.MybatisUtils;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.net.ntp.TimeStamp;

import java.sql.Timestamp;

@Data
@Builder
public class LiveBlackInfo {
    /**
     * 昵称
     */
    public String nickname;
    /**
     * mid
     */
    public Long mid;
    /**
     * 操作人
     */
    public String operator;
    /**
     * 创建时间
     */
    public Long ctime;

    public static LiveBlackInfo convert2UpBlackInfo(CmcBlackInfo dto) {
        if (dto == null) {
            return null;
        }

        LiveBlackInfo liveBlackInfoDto = LiveBlackInfo.builder()
                .mid(dto.getMid())
                .nickname(dto.getNickname())
                .ctime(dto.getCtime())
                .operator(dto.getOperator())
                .build();


        return liveBlackInfoDto;
    }
}

