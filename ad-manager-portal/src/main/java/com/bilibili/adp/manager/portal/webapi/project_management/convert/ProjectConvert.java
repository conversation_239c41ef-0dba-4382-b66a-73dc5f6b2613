package com.bilibili.adp.manager.portal.webapi.project_management.convert;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.bapis.ad.crm.agg.AggQueryInfo;
import com.bapis.ad.crm.agg.AggQueryReq;
import com.bapis.ad.crm.agg.AggQueryResp;
import com.bapis.ad.crm.agg.BizInfo;
import com.bapis.ad.crm.industry.IndustryInfoAgg;
import com.bapis.ad.crm.platform.project.*;
import com.bilibili.ad.manager.api.project.CouponConditionDimensionDto;
import com.bilibili.adp.manager.portal.webapi.project_management.vo.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/2/17
 */
public class ProjectConvert {

    public static ParentProjectResponseVo convert(ParentProjectInfo parentProjectInfo) {
        if (null == parentProjectInfo) {
            return null;
        }
        ParentProjectResponseVo parentProjectResponseVo = new ParentProjectResponseVo();
        parentProjectResponseVo.setProjectName(parentProjectInfo.getParentProjectName());
        parentProjectResponseVo.setProjectAim(parentProjectInfo.getParentProjectAim());
        parentProjectResponseVo.setParentProjectId(parentProjectInfo.getParentProjectId());
        parentProjectResponseVo.setParentProjectBalance(parentProjectInfo.getParentProjectBalance());
        parentProjectResponseVo.setParentProjectRestBalance(parentProjectInfo.getParentProjectRestBalance());
        parentProjectResponseVo.setRunningProjects(parentProjectInfo.getRunningProjects());
        parentProjectResponseVo.setParentProjectBegin(parentProjectInfo.getParentProjectBegin());
        parentProjectResponseVo.setParentProjectEnd(parentProjectInfo.getParentProjectEnd());
        parentProjectResponseVo.setRelatedProjects(parentProjectInfo.getRelatedProjects());
        return parentProjectResponseVo;
    }

    public static CreateProjectReq convert(ProjectCreateVo projectCreateVo, String operator) {
        CreateProjectReq.Builder builder = CreateProjectReq.newBuilder();
        if (null == projectCreateVo) {
            return builder.build();
        }

        builder.setOperator(operator);
        builder.setProjectName(projectCreateVo.getProjectName());
        builder.setParentProjectId(projectCreateVo.getParentProjectId());
        builder.setEffectPlatform(projectCreateVo.getEffectPlatform());
        builder.setProjectBudget(projectCreateVo.getProjectBudget());
        builder.setProjectBeginTime(projectCreateVo.getProjectBeginDate());
        builder.setProjectEndTime(projectCreateVo.getProjectEndDate());
        builder.setEffectCustomerType(null != projectCreateVo.getEffectCustomerType() ? projectCreateVo.getEffectCustomerType() : 0);
        builder.setFundType(null == projectCreateVo.getFundType() ? 0 : projectCreateVo.getFundType());
        List<EffectIndustryInfoVo> effectIndustryInfos = projectCreateVo.getEffectIndustryInfos();
        if (!CollectionUtils.isEmpty(effectIndustryInfos)) {
            List<IndustryInfo> industryInfos = new ArrayList<>();
            effectIndustryInfos.forEach(effectIndustryInfoVo -> {
                IndustryInfo.Builder indstryBuilder = IndustryInfo.newBuilder();
                indstryBuilder.setIndustryId(null == effectIndustryInfoVo.getEffectIndustryId() ? 0 : effectIndustryInfoVo.getEffectIndustryId());
                indstryBuilder.setIndustryName(StringUtils.isEmpty(effectIndustryInfoVo.getEffectIndustryName()) ? "" : effectIndustryInfoVo.getEffectIndustryName());
                industryInfos.add(indstryBuilder.build());
            });
            builder.addAllEffectIndustry(industryInfos);
        }
        List<CouponTemplateVo> coupons = projectCreateVo.getCoupons();
        builder.addAllCoupons(convertCoupon(coupons));
        builder.setProjectTeamLeader(null == projectCreateVo.getProjectTeamLeader() ? "" : projectCreateVo.getProjectTeamLeader());
        builder.setProjectLeader(null == projectCreateVo.getProjectLeader() ? "" : projectCreateVo.getProjectLeader());
        builder.addAllProjectOperators(null == projectCreateVo.getProjectOperators() ? Collections.emptyList() : projectCreateVo.getProjectOperators());
        if (null != projectCreateVo.getProjectRule()) {
            builder.setProjectRuleInfo(JSONObject.toJSONString(projectCreateVo.getProjectRule()));
        }
        builder.setTeamName(Optional.ofNullable(projectCreateVo.getTeamName()).orElse(""));
        builder.setAssessmentCriteria(Optional.ofNullable(projectCreateVo.getAssessmentCriteria()).orElse(""))
                .setApplicationDoc(Optional.ofNullable(projectCreateVo.getApplicationDoc()).orElse(""))
                .setEstimateAmount(Optional.ofNullable(projectCreateVo.getEstimateAmount()).orElse(0L))
                .setRemark(Optional.ofNullable(projectCreateVo.getRemark()).orElse(""))
                .setReviewDoc(Optional.ofNullable(projectCreateVo.getReviewDoc()).orElse(""))
                .setValidityPeriod(Optional.ofNullable(projectCreateVo.getValidityPeriod()).orElse(0))
                .setIsContinue(Optional.ofNullable(projectCreateVo.getIsContinue()).orElse(0))
                .setIsExpected(Optional.ofNullable(projectCreateVo.getIsExpected()).orElse(0))
                .setContinueProjectIds(Optional.ofNullable(projectCreateVo.getContinueProjectIds()).orElse(""))
        ;
        return builder.build();
    }

    public static UpdateProjectReq convert(ProjectUpdateVo projectUpdateVo, String operator) {
        if (null == projectUpdateVo) {
            return UpdateProjectReq.newBuilder().build();
        }

        UpdateProjectReq.Builder builder = UpdateProjectReq.newBuilder();
        builder.setOperator(operator);
        builder.setProjectId(projectUpdateVo.getProjectId());
        builder.setParentProjectId(projectUpdateVo.getParentProjectId());
        builder.setProjectBudget(projectUpdateVo.getProjectBudget());
        builder.setDocContent(projectUpdateVo.getDocContent());
        List<CouponTemplateVo> coupons = projectUpdateVo.getCoupons();
        builder.addAllCoupons(convertCoupon(coupons));

        builder.setEffectCustomerType(null != projectUpdateVo.getEffectCustomerType() ? projectUpdateVo.getEffectCustomerType() : 0);
        builder.setProjectTeamLeader(null == projectUpdateVo.getProjectTeamLeader() ? "" : projectUpdateVo.getProjectTeamLeader());
        builder.setProjectLeader(null == projectUpdateVo.getProjectLeader() ? "" : projectUpdateVo.getProjectLeader());
        builder.setTeamName(Optional.ofNullable(projectUpdateVo.getTeamName()).orElse(""));
        builder.addAllProjectOperators(null == projectUpdateVo.getProjectOperators() ? Collections.emptyList() : projectUpdateVo.getProjectOperators());
        if (null != projectUpdateVo.getProjectRule()) {
            builder.setProjectRuleInfo(JSONObject.toJSONString(projectUpdateVo.getProjectRule()));
        }
        List<EffectIndustryInfoVo> effectIndustryInfos = projectUpdateVo.getEffectIndustryInfos();
        if (!CollectionUtils.isEmpty(effectIndustryInfos)) {
            List<IndustryInfo> industryInfos = new ArrayList<>();
            effectIndustryInfos.forEach(effectIndustryInfoVo -> {
                IndustryInfo.Builder indstryBuilder = IndustryInfo.newBuilder();
                indstryBuilder.setIndustryId(null == effectIndustryInfoVo.getEffectIndustryId() ? 0 : effectIndustryInfoVo.getEffectIndustryId());
                indstryBuilder.setIndustryName(StringUtils.isEmpty(effectIndustryInfoVo.getEffectIndustryName()) ? "" : effectIndustryInfoVo.getEffectIndustryName());
                industryInfos.add(indstryBuilder.build());
            });
            builder.addAllEffectIndustry(industryInfos);
        }
        builder.setAssessmentCriteria(Optional.ofNullable(projectUpdateVo.getAssessmentCriteria()).orElse(""))
                .setApplicationDoc(Optional.ofNullable(projectUpdateVo.getApplicationDoc()).orElse(""))
                .setEstimateAmount(Optional.ofNullable(projectUpdateVo.getEstimateAmount()).orElse(0L))
                .setRemark(Optional.ofNullable(projectUpdateVo.getRemark()).orElse(""))
                .setReviewDoc(Optional.ofNullable(projectUpdateVo.getReviewDoc()).orElse(""))
                .setValidityPeriod(Optional.ofNullable(projectUpdateVo.getValidityPeriod()).orElse(0))
                .setProjectBeginTime(projectUpdateVo.getProjectBeginDate())
                .setProjectEndTime(projectUpdateVo.getProjectEndDate())
                .setIsContinue(null == projectUpdateVo.getIsContinue() ? 0 : projectUpdateVo.getIsContinue())
                .setIsExpected(null == projectUpdateVo.getIsExpected() ? 0 : projectUpdateVo.getIsExpected())
                .setContinueProjectIds(null == projectUpdateVo.getContinueProjectIds() ? "" : projectUpdateVo.getContinueProjectIds());

        return builder.build();
    }

    private static List<CouponTemplate> convertCoupon(List<CouponTemplateVo> coupons) {
        List<CouponTemplate> couponTemplates = new ArrayList<>();
        if (!CollectionUtils.isEmpty(coupons)) {
            coupons.forEach(coupon -> {
                CouponTemplate.Builder couponBuilder = CouponTemplate.newBuilder();
                couponBuilder.setCouponName(coupon.getCouponName());
                couponBuilder.setCouponFaceValue(null == coupon.getCouponFaceValue() ? 0 : coupon.getCouponFaceValue());
                couponBuilder.setCouponThreshold(null == coupon.getCouponThreshold() ? 0 : coupon.getCouponThreshold());
                couponBuilder.setCouponBeginDate(StringUtils.isEmpty(coupon.getCouponBeginDate()) ? "" : coupon.getCouponBeginDate());
                couponBuilder.setCouponEndDate(StringUtils.isEmpty(coupon.getCouponEndDate()) ? "" : coupon.getCouponEndDate());
                couponBuilder.setEffectDays(null == coupon.getEffectDays() ? 0 : coupon.getEffectDays());
                couponBuilder.setDateType(null == coupon.getDateType() ? 0 : coupon.getDateType());
                couponBuilder.setCouponType(null == coupon.getCouponType() ? 1 : coupon.getCouponType());
                couponBuilder.setCouponContent(StringUtils.isEmpty(coupon.getCouponContent()) ? "" : coupon.getCouponContent());
                couponBuilder.setCouponDiscount(null == coupon.getCouponDiscount() ? 0 : coupon.getCouponDiscount());
                couponBuilder.setCouponDiscountLimit(null == coupon.getCouponDiscountLimit() ? 0 : coupon.getCouponDiscountLimit());
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("improveTypes", CollectionUtils.isEmpty(coupon.getImproveTypes()) ? Collections.emptyList() : coupon.getImproveTypes());
                jsonObject.put("orderBelongingType", coupon.getOrderBelongingType());
                jsonObject.put("effectRange", coupon.getEffectRange());
                jsonObject.put("limitRange", coupon.getLimitRange());
                couponBuilder.setCouponUseRule(jsonObject.toJSONString());

                couponTemplates.add(couponBuilder.build());

            });
        }
        return couponTemplates;
    }

    public static UpdateProjectStatusReq convert(ProjectStatusUpdateVo projectStatusUpdateVo, String operator) {
        if (null == projectStatusUpdateVo) {
            return UpdateProjectStatusReq.newBuilder().build();
        }

        UpdateProjectStatusReq.Builder builder = UpdateProjectStatusReq.newBuilder();
        builder.setProjectId(projectStatusUpdateVo.getProjectId());
        builder.setStatusAction(projectStatusUpdateVo.getStatusAction());
        builder.setOperator(operator);
        return builder.build();
    }

    public static PageQueryProjectsReq convert(String operator, PageQueryProjectReqVo pageQueryProjectReqVo) {
        if (null == pageQueryProjectReqVo) {
            return PageQueryProjectsReq.newBuilder().build();
        }
        PageQueryProjectsReq.Builder builder = PageQueryProjectsReq.newBuilder();
        builder.setParentProjectId(null == pageQueryProjectReqVo.getParentProjectId() ? 0 : pageQueryProjectReqVo.getParentProjectId());
        builder.setProjectName(null == pageQueryProjectReqVo.getProjectName() ? "" : pageQueryProjectReqVo.getProjectName());
        builder.setProjectId(null == pageQueryProjectReqVo.getProjectId() ? 0 : pageQueryProjectReqVo.getProjectId());
        builder.setEffectPlatform(null == pageQueryProjectReqVo.getEffectPlatform() ? 0 : pageQueryProjectReqVo.getEffectPlatform());
        builder.setStatus(null == pageQueryProjectReqVo.getStatus() ? 0 : pageQueryProjectReqVo.getStatus());
        builder.setEffectIndustryName(null == pageQueryProjectReqVo.getEffectIndustryName() ? "" : pageQueryProjectReqVo.getEffectIndustryName());
        builder.setBeginDate(null == pageQueryProjectReqVo.getProjectBeginDate() ? "" : pageQueryProjectReqVo.getProjectBeginDate());
        builder.setEndDate(null == pageQueryProjectReqVo.getProjectEndDate() ? "" : pageQueryProjectReqVo.getProjectEndDate());
        builder.setPageIndex(null == pageQueryProjectReqVo.getPageIndex() ? 0 : pageQueryProjectReqVo.getPageIndex());
        builder.setPageSize(null == pageQueryProjectReqVo.getPageSize() ? 0 : pageQueryProjectReqVo.getPageSize());
        builder.setOperator(operator);
        builder.setLeader(null == pageQueryProjectReqVo.getLeader() ? "" : pageQueryProjectReqVo.getLeader());
        builder.setTeamLeader(null == pageQueryProjectReqVo.getTeamLeader() ? "" : pageQueryProjectReqVo.getTeamLeader());
        builder.setMemberContains(null == pageQueryProjectReqVo.getMemberContains() ? "" : pageQueryProjectReqVo.getMemberContains());
        builder.setTeamName(null == pageQueryProjectReqVo.getTeamName() ? "" : pageQueryProjectReqVo.getTeamName());
        return builder.build();
    }

    public static PageQueryProjectRespVo convert(ProjectInfoAgg projectInfoAgg) {
        if (null == projectInfoAgg) {
            return null;
        }

        PageQueryProjectRespVo result = new PageQueryProjectRespVo();
        result.setPageIndex(projectInfoAgg.getPageIndex());
        result.setPageSize(projectInfoAgg.getPageSize());
        result.setPageCount(projectInfoAgg.getPageCount());
        result.setRecordCount(projectInfoAgg.getRecordCount());
        List<ProjectInfo> projectInfoList = projectInfoAgg.getProjectInfoList();
        if (CollectionUtils.isEmpty(projectInfoList)) {
            result.setData(Collections.emptyList());
            return result;
        }

        List<ProjectInfoVo> projectInfoVos = new ArrayList<>();
        projectInfoList.forEach(projectInfo -> {
            ProjectInfoVo projectInfoVo = new ProjectInfoVo();
            projectInfoVo.setParentProjectId(projectInfo.getParentProjectId());
            projectInfoVo.setParentProjectName(projectInfo.getParentProjectName());
            projectInfoVo.setProjectName(projectInfo.getProjectName());
            projectInfoVo.setProjectId(projectInfo.getProjectId());
            projectInfoVo.setEffectPlatform(projectInfo.getEffectPlatform());
            projectInfoVo.setProjectBudget(projectInfo.getProjectBudget());
            projectInfoVo.setProjectBalance(projectInfo.getProjectBalance());
            projectInfoVo.setProjectBeginDate(projectInfo.getBeginDate());
            projectInfoVo.setProjectEndDate(projectInfo.getEndDate());
            projectInfoVo.setStatus(projectInfo.getStatus());
            projectInfoVo.setDocContent(projectInfo.getDocContent());
            projectInfoVo.setEffectCustomerType(projectInfo.getEffectCustomerType());
            projectInfoVo.setCoupons(convertCouponTemplate(projectInfo.getCouponsList()));
            projectInfoVo.setEffectIndustryInfos(convertEffectIndustryInfoVo(projectInfo.getEffectIndustryList()));
            projectInfoVo.setFundType(projectInfo.getFundType());
            projectInfoVo.setProjectTeamLeader(projectInfo.getProjectTeamLeader());
            projectInfoVo.setProjectLeader(projectInfo.getProjectLeader());
            projectInfoVo.setProjectOperators(projectInfo.getProjectOperatorsList());
            projectInfoVo.setProjectRule(buildProjectRule(projectInfo.getProjectRuleInfo()));

            projectInfoVo.setAssessmentCriteria(projectInfo.getAssessmentCriteria());
            projectInfoVo.setApplicationDoc(projectInfo.getApplicationDoc());
            projectInfoVo.setEstimateAmount(projectInfo.getEstimateAmount());
            projectInfoVo.setRemark(projectInfo.getRemark());
            projectInfoVo.setReviewDoc(projectInfo.getReviewDoc());
            projectInfoVo.setValidityPeriod(projectInfo.getValidityPeriod());
            projectInfoVo.setOrderStatus(projectInfo.getOrderStatus());
            projectInfoVo.setOrderUrl(projectInfo.getOrderUrl());
            projectInfoVo.setIsContinue(projectInfo.getIsContinue());
            projectInfoVo.setIsExpected(projectInfo.getIsExpected());
            projectInfoVo.setContinueProjectIds(projectInfo.getContinueProjectIds());
            projectInfoVo.setTeamName(projectInfo.getTeamName());
            projectInfoVos.add(projectInfoVo);
        });
        result.setData(projectInfoVos);
        return result;
    }

    private static ProjectRuleVo buildProjectRule(ProjectRuleInfo projectRuleInfo) {
        ProjectRuleVo projectRuleVo = new ProjectRuleVo();
        List<EffectLimitRangeVo> effectRangeVo = new ArrayList<>();
        EffectRangeInfo effectRange = projectRuleInfo.getEffectRange();
        if (!CollectionUtils.isEmpty(effectRange.getRangeInfoList())) {
            effectRange.getRangeInfoList().forEach(range -> {
                EffectLimitRangeVo effect = new EffectLimitRangeVo();
                effect.setObjectType(range.getObjectType());
                List<SingleRangeInfo> singleRangeInfos = range.getSingleRangeInfoList();
                if (!CollectionUtils.isEmpty(singleRangeInfos)) {
                    effect.setRangeInfo(convertRange(singleRangeInfos));
                }
                effectRangeVo.add(effect);
            });
        }
        projectRuleVo.setEffectRange(effectRangeVo);

        List<EffectLimitRangeVo> limitRangeVo = new ArrayList<>();
        LimitRangeInfo limitRange = projectRuleInfo.getLimitRange();
        if (!CollectionUtils.isEmpty(limitRange.getRangeInfoList())) {
            limitRange.getRangeInfoList().forEach(range -> {
                EffectLimitRangeVo limit = new EffectLimitRangeVo();
                limit.setObjectType(range.getObjectType());
                List<SingleRangeInfo> singleRangeInfoList = range.getSingleRangeInfoList();
                if (!CollectionUtils.isEmpty(singleRangeInfoList)) {
                    limit.setRangeInfo(convertRange(singleRangeInfoList));
                }
                limit.setAmountLimit(range.getAmountLimit());
                limitRangeVo.add(limit);
            });
        }
        projectRuleVo.setLimitRange(limitRangeVo);
        return projectRuleVo;
    }

    private static List<RangeInfoVo> convertRange(List<SingleRangeInfo> singleRangeInfos) {
        if (CollectionUtils.isEmpty(singleRangeInfos)) {
            return Collections.emptyList();
        }
        return singleRangeInfos.stream().map(singleRangeInfo -> {
            RangeInfoVo rangeInfoVo = new RangeInfoVo();
            rangeInfoVo.setObjectId(singleRangeInfo.getObjectId());
            rangeInfoVo.setObjectName(singleRangeInfo.getObjectName());
            return rangeInfoVo;
        }).collect(Collectors.toList());
    }

    public static List<ProjectOperationLogVo> convert(List<QueryProjectOperationLog> logs) {
        if (CollectionUtils.isEmpty(logs)) {
            return Collections.emptyList();
        }

        List<ProjectOperationLogVo> result = new ArrayList<>();
        logs.forEach(log -> {
            ProjectOperationLogVo vo = new ProjectOperationLogVo();
            vo.setOperatorName(log.getOperationName());
            vo.setOperatorDate(log.getOperationDate());
            vo.setOperatorContent(log.getOperationContent());
            vo.setOperatorType(log.getOperationType());
            vo.setOperateStatus(log.getOperateStatus());
            result.add(vo);
        });
        return result;
    }

    public static List<CouponTemplateVo> convertCouponTemplate(List<CouponTemplate> templateList) {
        if (CollectionUtils.isEmpty(templateList)) {
            return Collections.emptyList();
        }
        return templateList.stream().map(template -> {
            CouponTemplateVo couponTemplateVo = new CouponTemplateVo();
            couponTemplateVo.setCouponName(template.getCouponName());
            couponTemplateVo.setCouponFaceValue(template.getCouponFaceValue());
            couponTemplateVo.setCouponThreshold(template.getCouponThreshold());
            couponTemplateVo.setDateType(template.getDateType());
            couponTemplateVo.setCouponBeginDate(template.getCouponBeginDate());
            couponTemplateVo.setCouponEndDate(template.getCouponEndDate());
            couponTemplateVo.setEffectDays(template.getEffectDays());
            couponTemplateVo.setCouponContent(template.getCouponContent());
            couponTemplateVo.setCouponDiscount(template.getCouponDiscount());
            couponTemplateVo.setCouponDiscountLimit(template.getCouponDiscountLimit());
            couponTemplateVo.setCouponType(template.getCouponType());
            couponTemplateVo.setCouponTemplateId(template.getId());
            String couponUseRule = template.getCouponUseRule();
            if (StringUtils.isNotBlank(couponUseRule)) {
                JSONObject jsonObject = JSONObject.parseObject(couponUseRule);
                couponTemplateVo.setImproveTypes(jsonObject.getObject("improveTypes", new TypeReference<List<Integer>>() {
                }));
                CouponConditionDimensionDto effectRange = jsonObject.getObject("effectRange", CouponConditionDimensionDto.class);
                couponTemplateVo.setEffectRange(effectRange);
                CouponConditionDimensionDto limitRange = jsonObject.getObject("limitRange", CouponConditionDimensionDto.class);
                couponTemplateVo.setLimitRange(limitRange);
                Integer orderBelongingType = jsonObject.getObject("orderBelongingType", Integer.class);
                couponTemplateVo.setOrderBelongingType(orderBelongingType);
            }
            return couponTemplateVo;
        }).collect(Collectors.toList());
    }

    public static List<EffectIndustryInfoVo> convertEffectIndustryInfoVo(List<IndustryInfo> effectIndustries) {
        if (CollectionUtils.isEmpty(effectIndustries)) {
            return Collections.emptyList();
        }

        return effectIndustries.stream().map(industryInfo -> {
            EffectIndustryInfoVo effectIndustryInfoVo = new EffectIndustryInfoVo();
            effectIndustryInfoVo.setEffectIndustryId(industryInfo.getIndustryId());
            effectIndustryInfoVo.setEffectIndustryName(industryInfo.getIndustryName());
            effectIndustryInfoVo.setUseThirdIndustry(industryInfo.getUseThirdIndustry());
            return effectIndustryInfoVo;
        }).collect(Collectors.toList());
    }

    public static List<EffectCustomerTypeVo> convertCustomerType(List<EffectCustomerInfo> effectCustomerInfos) {
        if (CollectionUtils.isEmpty(effectCustomerInfos)) {
            return Collections.emptyList();
        }
        return effectCustomerInfos.stream().map(effectCustomerInfo -> {
            EffectCustomerTypeVo customerTypeVo = new EffectCustomerTypeVo();
            customerTypeVo.setEffectCustomerId(effectCustomerInfo.getId());
            customerTypeVo.setEffectCustomerName(effectCustomerInfo.getName());
            return customerTypeVo;
        }).collect(Collectors.toList());
    }

    public static List<EffectIndustryInfoVo> convertEffectIndustry(IndustryInfoAgg industryInfoAgg) {
        if (null == industryInfoAgg) {
            return Collections.emptyList();
        }
        List<com.bapis.ad.crm.industry.IndustryInfo> industryInfosList = industryInfoAgg.getIndustryInfosList();
        EffectIndustryInfoVo effectIndustryInfoVo = new EffectIndustryInfoVo();
        effectIndustryInfoVo.setEffectIndustryId(0);
        effectIndustryInfoVo.setEffectIndustryName("全部");
        if (CollectionUtils.isEmpty(industryInfosList)) {
            // 手动兼容
            List<EffectIndustryInfoVo> result = new ArrayList<>();
            result.add(effectIndustryInfoVo);
            return result;
        }
        List<EffectIndustryInfoVo> result = industryInfosList.stream().map(industryInfo -> {
            EffectIndustryInfoVo infoVo = new EffectIndustryInfoVo();
            infoVo.setEffectIndustryName(industryInfo.getName());
            infoVo.setEffectIndustryId(industryInfo.getId());
            return infoVo;
        }).collect(Collectors.toList());
        result.add(effectIndustryInfoVo);
        return result;
    }


    public static AggQueryReq convert(AggQueryReqVo reqVo) {
        AggQueryReq.Builder builder = AggQueryReq.newBuilder();
        if (null == reqVo) {
            return builder.build();
        }
        builder.setBizId(reqVo.getBizId() == null ? 0 : reqVo.getBizId());
        builder.setBizName(reqVo.getBizName() == null ? "" : reqVo.getBizName());
        builder.setBizType(reqVo.getBizType() == null ? 0 : reqVo.getBizType());
        builder.setPageIndex(reqVo.getPageIndex() == null ? 0 : reqVo.getPageIndex());
        builder.setPageSize(reqVo.getPageSize() == null ? 0 : reqVo.getPageSize());
        return builder.build();
    }

    public static AggQueryRespVo convert(AggQueryResp resp) {
        AggQueryRespVo aggQueryRespVo = new AggQueryRespVo();
        AggQueryInfo data = resp.getData();
        List<BizInfo> bizInfos = data.getBizInfoList();
        if (!CollectionUtils.isEmpty(bizInfos)) {
            List<AggBizInfoVo> infos = new ArrayList<>();
            bizInfos.forEach(biz -> {
                AggBizInfoVo aggBizInfoVo = new AggBizInfoVo();
                aggBizInfoVo.setBizName(biz.getBizName());
                aggBizInfoVo.setBizId(biz.getBizId());
                infos.add(aggBizInfoVo);
            });
            aggQueryRespVo.setRecordsCount(data.getRecordsCount());
            aggQueryRespVo.setBizInfos(infos);
        }
        return aggQueryRespVo;
    }
}
