package com.bilibili.adp.manager.portal.webapi.res.ogv.ogv;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/4 16:48
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class SaveOgvResourceReq implements Serializable {
    private static final long serialVersionUID = 6411031261439110535L;
    @ApiModelProperty("id")
    private Long id;
    @ApiModelProperty("season id")
    private List<Long> seasonIdList;
    @ApiModelProperty("投放开始时间")
    private Long beginTime;
    @ApiModelProperty("投放结束时间")
    private Long endTime;
    @ApiModelProperty("资源级别")
    private String level;
    @ApiModelProperty("可投放端贴次")
    private List<Integer> stickerIdList;
    @ApiModelProperty("可投放平台")
    private List<Integer> platformIdList;
}
