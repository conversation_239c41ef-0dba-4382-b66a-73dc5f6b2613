package com.bilibili.adp.manager.portal.webapi.pickup.vo.special_settle;

import com.bilibili.adp.web.framework.annotations.ExcelResources;
import com.bilibili.rbac.filter.annotation.Sensitive;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SpecialSettleDetailVo {
    @ApiModelProperty("id")
    private Integer id;

    @ApiModelProperty("状态:1-待审核 2-已通过 3-已拒绝 4-已撤回")
    private Integer status;

    @ApiModelProperty("mid")
    private String upper_mid;

    @ApiModelProperty("UP主昵称")
    private String nickname;

    @ApiModelProperty("粉丝总量")
    private Integer fans_num;

    @ApiModelProperty("一级分区名称")
    private String partition_name;

    @ApiModelProperty("二级分区名称")
    private String second_partition_name;

    @ApiModelProperty("申请时间")
    private String ctime;

    @ApiModelProperty("微信")
    @Sensitive("PersonUpperInfoVo.wechat")
    private String wechat;

    @ApiModelProperty("手机")
    private String mobile;

    @ApiModelProperty("QQ")
    @Sensitive("PersonUpperInfoVo.qq")
    private String qq;

    @ApiModelProperty("运营操作时间")
    private String mtime;

    @ApiModelProperty("操作人")
    private String operator;

    @ApiModelProperty("入驻截止时间")
    private String settle_deadline_time;

    @ApiModelProperty("提交材料")
    private List<String> urls;

    @ApiModelProperty("拒绝原因")
    private String reason;


}
