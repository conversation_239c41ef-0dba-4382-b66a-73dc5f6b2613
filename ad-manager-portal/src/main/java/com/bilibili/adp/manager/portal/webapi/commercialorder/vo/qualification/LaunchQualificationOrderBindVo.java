package com.bilibili.adp.manager.portal.webapi.commercialorder.vo.qualification;

import com.bilibili.adp.manager.portal.webapi.res.file.vo.BfsFileVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.util.List;

/**
 * @author: brady
 * @time: 2023/5/31 6:10 下午
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LaunchQualificationOrderBindVo {

    @ApiModelProperty("订单编号")
    private String orderNo;

    @ApiModelProperty("bvId")
    private String bvId;

    @ApiModelProperty("bindId")
    private Long bindId;

    @ApiModelProperty("创建时间")
    private String ctime;


    //资质信息
    @ApiModelProperty("账号名称")
    private String accountName;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("资质类型")
    private String lauQuaTypeDesc;

    @ApiModelProperty("资质名称")
    private String name;

    @ApiModelProperty("有效期")
    private String validity_period;

    @ApiModelProperty("资质照片")
    private List<BfsFileVo> attachments;
}
