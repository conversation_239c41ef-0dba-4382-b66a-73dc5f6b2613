package com.bilibili.adp.manager.portal.webapi.audit.task.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 审核任务执行结果vo对象
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NativeArchiveAuditTaskExecuteVo {

    @ApiModelProperty("任务id")
    private List<String> task_id;

    @ApiModelProperty("操作类型 101一审通过 102一审驳回 103一审搁置 201质检通过 202质检驳回 203回查确认 205修改驳回理由")
    private Integer operate_type;

    @ApiModelProperty("驳回分类")
    private String       reject_category;

    @ApiModelProperty("驳回原因")
    private String       reject_reason;

    @ApiModelProperty("驳回理由")
    private String       reject_remark;

    @ApiModelProperty("质检说明")
    private String       recheck_remark;

    @ApiModelProperty("标记为待审复杂创意的原因")
    private String       complex_reason;
    
}
