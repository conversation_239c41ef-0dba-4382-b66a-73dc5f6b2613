package com.bilibili.adp.manager.portal.webapi.tentative.vo.req;

import com.bapis.ad.cmc.cidgoods.SingleGoodsInfoCheckResp;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * @Description 搬山商品信息校验结果
 * <AUTHOR>
 * @Date 2024/2/29 16:51
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@Builder
public class TentativeGoodsInfoCheckResultVo {
    @ApiModelProperty(value = "校验字段")
    private String field;
    @ApiModelProperty(value = "是否有效")
    private Boolean isValid;
    @ApiModelProperty(value = "无效原因")
    private String msg;

    public static TentativeGoodsInfoCheckResultVo convert2Me(SingleGoodsInfoCheckResp singleGoodsInfoCheckResp) {
        if (singleGoodsInfoCheckResp == null) {
            return null;
        }
        return TentativeGoodsInfoCheckResultVo.builder()
                .field(singleGoodsInfoCheckResp.getField())
                .isValid(singleGoodsInfoCheckResp.getIsValid())
                .msg(singleGoodsInfoCheckResp.getMsg())
                .build();
    }
}
