package com.bilibili.adp.manager.task.job;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.manager.task.handler.LessDeductionHandler;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.*;

/**
 * Created by Captain on 2017/7/19.
 */
@Component
@JobHandler("LessDeductionsJob")
//8 8 7 * * ?
public class LessDeductionsJob extends IJobHandler {
    private final Logger LOGGER = LoggerFactory.getLogger(this.getClass());

    @Value("#{'${less.deductions.notify.mails}'.split(',')}")
    private List<String> notifyUserMails;

    @Value("#{'${less.deductions.salesType}'.split(',')}")
    private List<Integer> salesTypeList;

    @Value("${less.deductions.some.day.ago:1}")
    private Integer someDayAgo;

    @Autowired
    private LessDeductionHandler lessDeductionHandler;

    @Override
    public ReturnT<String> execute(String var1){
        LOGGER.info("=====>>>>>>>>>>>>>>>LessDeductionsJob start executing...");
        if (CollectionUtils.isEmpty(notifyUserMails) || CollectionUtils.isEmpty(salesTypeList)){
            return ReturnT.SUCCESS;
        }
        if (someDayAgo != null && someDayAgo.compareTo(1) >= 0){
            for (int i=someDayAgo; i>=1; i--){
                Timestamp startTime = Utils.getSomeDayAgo(Utils.getToday(), i);
                Timestamp endTime = Utils.getEndOfDay(startTime);
                lessDeductionHandler.statChargingAndSendEmail(startTime, endTime);

                try {
                    Thread.sleep(30000L);
                } catch (InterruptedException e) {
                    LOGGER.error("LessDeductionsJob InterruptedException", e);
                }
            }
        }

        LOGGER.info("=====>>>>>>>>>>>>>>>LessDeductionsJob end executing.");
        return ReturnT.SUCCESS;
    }
}
