package com.bilibili.adp.manager.portal.webapi.enterprise.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/3
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ComponentAuditDetailVo {
    private Long mid;

    private String nickName;

    private Integer position;

    private String time;

    private Long componentId;

    private Integer auditStatus;

    private String jumpUrl;

    private String componentContent;

    private List<ComponentMultiDeviceJumpInfoVo> multiDeviceJumpInfos;
}
