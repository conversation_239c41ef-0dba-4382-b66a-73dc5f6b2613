package com.bilibili.adp.manager.portal.webapi.school.vo;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CommercialInfoVo {

	@ApiModelProperty(value = "类目id")
	private Long id;

	@ApiModelProperty(value = "跳转链接")
	@JSONField(name = "jump_url")
	private String jumpUrl;

	@ApiModelProperty(value = "类目标题")
	private String title;

	@ApiModelProperty(value = "创建时间")
	private String ctime;

	@ApiModelProperty(value = "修改时间")
	private String mtime;

	@ApiModelProperty(value = "是否在三连首页露出")
	@JSONField(name = "sanlian_is_show")
	private Integer sanlianIsShow;

	@ApiModelProperty(value = "三连首页露出顺序")
	@JSONField(name = "sanlian_show_order_num")
	private Integer sanlianShowOrderNum;

}
