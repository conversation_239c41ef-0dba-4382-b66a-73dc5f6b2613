package com.bilibili.adp.manager.portal.common;


import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class KvVo {
    @ApiModelProperty(notes="ID")
    private Integer id;
    @ApiModelProperty(notes="名称")
    private String name;
}
