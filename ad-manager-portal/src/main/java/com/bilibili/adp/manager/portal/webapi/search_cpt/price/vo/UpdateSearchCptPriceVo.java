package com.bilibili.adp.manager.portal.webapi.search_cpt.price.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class UpdateSearchCptPriceVo {

    @ApiModelProperty(notes="刊例价ID")
    private Integer id;

    @ApiModelProperty(notes="外部刊例价,单位(元)")
    private BigDecimal external_price;

    @ApiModelProperty(notes="内部刊例价,单位(元)")
    private BigDecimal internal_price;

    @ApiModelProperty(notes="内部cpm净单价,单位(元)")
    private BigDecimal internal_cpm_price;

    @ApiModelProperty(notes="资源位状态：1-有效 2-无效")
    private Integer status;

    @ApiModelProperty(notes = "cpm单价,单位(元)")
    private BigDecimal cpm_price;

    @ApiModelProperty("唤起加收比例")
    private Integer wake_raise_ratio;

    @ApiModelProperty("品专pro加收比例")
    private Integer search_pro_raise_ratio;
}
