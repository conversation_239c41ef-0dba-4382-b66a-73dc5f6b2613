package com.bilibili.adp.order.service;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.exception.IExceptionCode;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.http.utils.OkHttpUtils;
import com.bilibili.adp.order.dto.*;
import com.bilibili.adp.order.service.model.HttpResponse;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

@Service
public class IncentiveBonusHttpService {

    public enum IncentiveBonusErrorCode implements IExceptionCode {
        REPEAT_CREATE(68500, "重复创建, 订单已存在"),
        REPEAT_PAY(68506, "订单已支付"),
        ORDER_NOT_EXIST(68502, "订单不存在"),
        ;
        private Integer code;
        private String message;

        IncentiveBonusErrorCode(Integer code, String message) {
            this.code = code;
            this.message = message;
        }
        @Override
        public Integer getCode() {
            return code;
        }

        @Override
        public String getMessage() {
            return message;
        }
    }

    private static final Logger LOGGER = LoggerFactory.getLogger(IncentiveBonusHttpService.class);
    @Value("${b.incentive.domain:http://api.bilibili.co}")
    protected String baseUrl;
    @Value("${b.incentive.secret:eTexvTgK24e6SSlryi847eZ1pazZy9Z5}")
    protected String secretKey;
    @Value("${b.incentive.appId:grflhGe3W549vY8bGJY6}")
    protected String appkey;

    public AccountIncentiveBonusDto getAccountIncentiveBonusInfoByMid(Long mid) {
        Assert.notNull(mid, "账号mid不能为空");

        TreeMap<String, Object> map = new TreeMap<>();
        map.put("mid", mid.toString());
        map.put("appkey", appkey);
        map.put("ts", System.currentTimeMillis() / 1000);
        map.put("sign", IncentiveBonusSignUtil.getSign(map, secretKey));

        AccountIncentiveBonusDto dto = null;
        String rounter = "/x/internal/growup-service/incentive/acc/customer/info";
        Transaction transaction = getCatTransaction(rounter, map);
        try {
            HttpResponse<AccountIncentiveBonusDto> response = OkHttpUtils.get(baseUrl + rounter).map(map).callForObject(new TypeToken<HttpResponse<AccountIncentiveBonusDto>>(){});

            Assert.isTrue(response != null
                    && response.getCode() != null
                    && response.getCode().equals(0), "获取账号激励金信息失败");

            if(transaction != null) {
                transaction.setStatus(Transaction.SUCCESS);
            }
            dto = response.getData();
        }catch (Throwable e) {
            if (transaction != null) {
                transaction.setStatus(e);
            }
            LOGGER.error("请求B端接口获取账号激励金信息失败", e);
        }finally {
            if (transaction != null) {
                transaction.complete();
            }
        }
        return dto;
    }

    public CreateIncentiveBonusOrderResponseDto createIncentiveBonusOrder(CreateIncentiveBonusOrderRequestDto requestDto) throws ServiceException {
        Assert.notNull(requestDto, "请求入参不能为空");
        Assert.isTrue(StringUtils.isNotBlank(requestDto.getOrderId()), "订单id不能为空");
        Assert.isTrue(StringUtils.isNotBlank(requestDto.getSubject()), "订单标题不能为空");
        Assert.isTrue(StringUtils.isNotBlank(requestDto.getNotifyUrl()), "支付成功异步通知地址不能为空");
        Assert.isTrue(requestDto.getAmount() != null && requestDto.getAmount() > 0, "总金额不能为空且必须大于0");
        Assert.notNull(requestDto.getExpire(), "失效时间不能为空");

        String realAppKey = Utils.getValueOrDefault(requestDto.getAppkey(), appkey);
        String realSecretKey = Utils.getValueOrDefault(requestDto.getSecretKey(), secretKey);

        TreeMap<String, Object> map = JSON.parseObject(JSON.toJSONString(requestDto), TreeMap.class);
        map.put("appkey", realAppKey);
        map.put("ts", System.currentTimeMillis() / 1000);
        map.put("sign", IncentiveBonusSignUtil.getSign(map, realSecretKey));

        CreateIncentiveBonusOrderResponseDto result = null;
        String rounter = "/x/internal/growup-service/incentive/trade/create";
        Transaction transaction = getCatTransaction(rounter, map);
        try {
            HttpResponse<CreateIncentiveBonusOrderResponseDto> response = OkHttpUtils.formPost(baseUrl + rounter).map(map).callForObject(new TypeToken<HttpResponse<CreateIncentiveBonusOrderResponseDto>>(){});
            Assert.isTrue(response != null
                    && response.getCode() != null, "创建激励金订单失败");

            if(transaction != null) {
                transaction.setStatus(Transaction.SUCCESS);
            }

            if(response.getCode().equals(0)) {
                result = response.getData();

            }else if(response.getCode().equals(IncentiveBonusErrorCode.REPEAT_CREATE.code)) {
                IncentiveBonusOrderDetailDto detailDto = getIncentiveBonusOrderDetail(RefundIncentiveBonusQueryRequestDto.builder()
                        .orderId(requestDto.getOrderId())
                        .appkey(realAppKey)
                        .secretKey(realSecretKey)
                        .build());
                if(detailDto != null) {
                    result = CreateIncentiveBonusOrderResponseDto.builder()
                            .order_id(detailDto.getOrder_id())
                            .trade_no(detailDto.getTrade_no())
                            .total_amount(detailDto.getAmount())
                            .build();
                }
            }else {
                throw new ServiceException(response.getCode(), response.getMessage());
            }
        }catch (Throwable e) {
            if (transaction != null) {
                transaction.setStatus(e);
            }
            LOGGER.error("请求B端接口创建激励金订单失败", e);
            throw e;
        }finally {
            if (transaction != null) {
                transaction.complete();
            }
        }
        return result;
    }

    public IncentiveBonusOrderDetailDto getIncentiveBonusOrderDetail (RefundIncentiveBonusQueryRequestDto queryRequestDto) throws ServiceException{
        Assert.isTrue(StringUtils.isNotBlank(queryRequestDto.getOrderId()), "请求入参不能为空");

        String realAppKey = Utils.getValueOrDefault(queryRequestDto.getAppkey(), appkey);
        String realSecretKey = Utils.getValueOrDefault(queryRequestDto.getSecretKey(), secretKey);

        TreeMap<String, Object> map = new TreeMap<>();
        map.put("order_id", queryRequestDto.getOrderId());
        map.put("appkey", realAppKey);
        map.put("ts", System.currentTimeMillis() / 1000);
        map.put("sign", IncentiveBonusSignUtil.getSign(map, realSecretKey));

        IncentiveBonusOrderDetailDto result = null;
        String rounter = "/x/internal/growup-service/incentive/trade/detail";
        Transaction transaction = getCatTransaction(rounter, map);
        try {
            HttpResponse<IncentiveBonusOrderDetailDto> response = OkHttpUtils.get(baseUrl + rounter).map(map).callForObject(new TypeToken<HttpResponse<IncentiveBonusOrderDetailDto>>(){});
            Assert.isTrue(response != null
                    && response.getCode() != null, "查询交易详情失败");

            if(response.getCode().equals(IncentiveBonusErrorCode.ORDER_NOT_EXIST.code)) {
                throw new ServiceException(IncentiveBonusErrorCode.ORDER_NOT_EXIST);
            }
            Assert.isTrue(response.getCode().equals(0), StringUtils.isNotBlank(response.getMessage()) ? response.getMessage() : "查询交易详情失败");

            if(transaction != null) {
                transaction.setStatus(Transaction.SUCCESS);
            }
            result = response.getData();
        }catch (Throwable e) {
            if (transaction != null) {
                transaction.setStatus(e);
            }
            LOGGER.error("请求B端接口查询交易详情失败", e);
            throw e;
        }finally {
            if (transaction != null) {
                transaction.complete();
            }
        }
        return result;
    }

//    @Deprecated
//    public IncentiveBonusOrderDetailDto getIncentiveBonusOrderDetail(String orderId) throws ServiceException {
//        return this.getIncentiveBonusOrderDetail(RefundIncentiveBonusQueryRequestDto.builder()
//                .orderId(orderId)
//                .build());
//    }

    public CreateIncentiveBonusOrderResponseDto payIncentiveBonusOrder(PayIncentiveBonusOrderRequestDto requestDto) throws ServiceException{
        Assert.isTrue(StringUtils.isNotBlank(requestDto.getOrderId()), "订单id不能为空");
        Assert.isTrue(Utils.isPositive(requestDto.getMid()), "支付人UID不能为空且必须大于0");

        String realAppKey = Utils.getValueOrDefault(requestDto.getAppkey(), appkey);
        String realSecretKey = Utils.getValueOrDefault(requestDto.getSecretKey(), secretKey);

        TreeMap<String, Object> map = new TreeMap<>();
        map.put("appkey", realAppKey);
        map.put("payer_id", requestDto.getMid());
        map.put("order_id", requestDto.getOrderId());
        if(requestDto.getPayType() != null) {
            map.put("pay_type", requestDto.getPayType());
        }
        map.put("ts", System.currentTimeMillis() / 1000);
        map.put("sign", IncentiveBonusSignUtil.getSign(map, realSecretKey));

        CreateIncentiveBonusOrderResponseDto result = null;
        String rounter = "/x/internal/growup-service/incentive/trade/pay";
        Transaction transaction = getCatTransaction(rounter, map);
        try {
            HttpResponse<CreateIncentiveBonusOrderResponseDto> response = OkHttpUtils.formPost(baseUrl + rounter).map(map).callForObject(new TypeToken<HttpResponse<CreateIncentiveBonusOrderResponseDto>>(){});
            Assert.isTrue(response != null
                    && response.getCode() != null, "支付激励金订单失败");

            if(transaction != null) {
                transaction.setStatus(Transaction.SUCCESS);
            }

            if(response.getCode().equals(0)) {
                result = response.getData();

            }else if(response.getCode().equals(IncentiveBonusErrorCode.REPEAT_PAY.code)) {
                IncentiveBonusOrderDetailDto detailDto = getIncentiveBonusOrderDetail(RefundIncentiveBonusQueryRequestDto.builder()
                        .orderId(requestDto.getOrderId())
                        .appkey(realAppKey)
                        .secretKey(realSecretKey)
                        .build());
                if(detailDto != null && detailDto.getStatus() != null && detailDto.getStatus() == 2) {
                    result = CreateIncentiveBonusOrderResponseDto.builder()
                            .order_id(detailDto.getOrder_id())
                            .trade_no(detailDto.getTrade_no())
                            .total_amount(detailDto.getAmount())
                            .build();
                }
            }else {
                throw new ServiceException(response.getCode(), response.getMessage());
            }
        }catch (Throwable e) {
            if (transaction != null) {
                transaction.setStatus(e);
            }
            LOGGER.error("请求B端接口支付激励金订单失败", e);
            throw e;
        }finally {
            if (transaction != null) {
                transaction.complete();
            }
        }
        return result;
    }

    @Deprecated
    public CreateIncentiveBonusOrderResponseDto payIncentiveBonusOrder(String orderId, Long mid,Integer payType) throws ServiceException {
        return this.payIncentiveBonusOrder(PayIncentiveBonusOrderRequestDto.builder()
                .orderId(orderId)
                .mid(mid)
                .payType(payType)
                .build());
    }

    public RefundIncentiveBonusDto refundIncentiveBonus(RefundIncentiveBonusRequestDto requestDto){
        Assert.isTrue(StringUtils.isNotBlank(requestDto.getOrderId()), "订单id不能为空");
        Assert.isTrue(Utils.isPositive(requestDto.getRefundAmount()), "退款金额不能为空，且大于0");

        String realAppKey = Utils.getValueOrDefault(requestDto.getAppkey(), appkey);
        String realSecretKey = Utils.getValueOrDefault(requestDto.getSecretKey(), secretKey);

        TreeMap<String, Object> map = new TreeMap<>();
        map.put("appkey", realAppKey);
        map.put("order_id", requestDto.getOrderId());
        map.put("refund_no", requestDto.getOrderId());
        map.put("refund_amount", requestDto.getRefundAmount());
        map.put("ts", System.currentTimeMillis() / 1000);
        map.put("sign", IncentiveBonusSignUtil.getSign(map, realSecretKey));

        String rounter = "/x/internal/growup-service/incentive/trade/refund";
        RefundIncentiveBonusDto result = null;
        Transaction transaction = getCatTransaction(rounter, map);
        try {
            HttpResponse<RefundIncentiveBonusDto> response = OkHttpUtils.formPost(baseUrl + rounter).map(map).callForObject(new TypeToken<HttpResponse<RefundIncentiveBonusDto>>(){});
            Assert.isTrue(response != null
                    && response.getCode() != null, "激励金退款失败");

            if(response.getCode().equals(0)) {
                result = response.getData();

            }else {
                result = getRefundDetail(RefundIncentiveBonusQueryRequestDto.builder()
                        .orderId(requestDto.getOrderId())
                        .appkey(realAppKey)
                        .secretKey(realSecretKey)
                        .build());
                if(result == null) {
                    throw new ServiceException(response.getCode(), response.getMessage());
                }
            }

            if(transaction != null) {
                transaction.setStatus(Transaction.SUCCESS);
            }
        }catch (Throwable e) {
            if (transaction != null) {
                transaction.setStatus(e);
            }
            LOGGER.error("请求B端激励金退款接口失败", e);
        }finally {
            if (transaction != null) {
                transaction.complete();
            }
        }
        return result;
    }

    @Deprecated
    public RefundIncentiveBonusDto refundIncentiveBonus(String orderId, Long refundAmount) {
        return this.refundIncentiveBonus(RefundIncentiveBonusRequestDto.builder()
                .orderId(orderId)
                .refundAmount(refundAmount)
                .build());
    }

    /**
     * 错误码 68516 --- 退款单不存在
     * @param queryRequestDto
     * @return
     */
    public RefundIncentiveBonusDto getRefundDetailWithoutCheck(RefundIncentiveBonusQueryRequestDto queryRequestDto){
        Assert.isTrue(StringUtils.isNotBlank(queryRequestDto.getOrderId()), "订单id不能为空");
        String realAppKey = Utils.getValueOrDefault(queryRequestDto.getAppkey(), appkey);
        String realSecretKey = Utils.getValueOrDefault(queryRequestDto.getSecretKey(), secretKey);
        TreeMap<String, Object> map = new TreeMap<>();
        map.put("appkey", realAppKey);
        map.put("refund_no", queryRequestDto.getOrderId());
        map.put("order_id", queryRequestDto.getOrderId());
        map.put("ts", System.currentTimeMillis() / 1000);
        map.put("sign", IncentiveBonusSignUtil.getSign(map, realSecretKey));

        String rounter = "/x/internal/growup-service/incentive/trade/refund/info";
        Transaction transaction = getCatTransaction(rounter, map);
        HttpResponse<RefundIncentiveBonusDto> response = OkHttpUtils.get(baseUrl + rounter).map(map).callForObject(new TypeToken<HttpResponse<RefundIncentiveBonusDto>>() {
        });
        Assert.isTrue(response !=null && response.getCode() != null, "查询订单退款信息失败");
        if (Integer.valueOf(68516).equals(response.getCode())){
            return null;
        }
        Assert.isTrue(response.getCode().equals(0), "非法的错误码"+response.getCode());
        if(transaction != null) {
            transaction.setStatus(Transaction.SUCCESS);
        }
        return response.getData();
    }

    public RefundIncentiveBonusDto getRefundDetail(RefundIncentiveBonusQueryRequestDto queryRequestDto){
        Assert.isTrue(StringUtils.isNotBlank(queryRequestDto.getOrderId()), "订单id不能为空");
        String realSecretKey = Utils.getValueOrDefault(queryRequestDto.getSecretKey(), secretKey);
        String realAppKey = Utils.getValueOrDefault(queryRequestDto.getAppkey(), appkey);

        TreeMap<String, Object> map = new TreeMap<>();
        map.put("appkey", realAppKey);
        map.put("order_id", queryRequestDto.getOrderId());
        map.put("refund_no", queryRequestDto.getOrderId());
        map.put("ts", System.currentTimeMillis() / 1000);
        map.put("sign", IncentiveBonusSignUtil.getSign(map, realSecretKey));

        RefundIncentiveBonusDto result = null;
        String rounter = "/x/internal/growup-service/incentive/trade/refund/info";
        Transaction transaction = getCatTransaction(rounter, map);
        try {
            HttpResponse<RefundIncentiveBonusDto> response = OkHttpUtils.get(baseUrl + rounter).map(map).callForObject(new TypeToken<HttpResponse<RefundIncentiveBonusDto>>(){});
            Assert.isTrue(response != null
                    && response.getCode() != null
                    && response.getCode().equals(0), "查询订单退款信息失败");

            result = response.getData();
            if(transaction != null) {
                transaction.setStatus(Transaction.SUCCESS);
            }
        }catch (Throwable e) {
            if (transaction != null) {
                transaction.setStatus(e);
            }
            LOGGER.error("请求B端接口查询订单退款信息失败", e);
        }finally {
            if (transaction != null) {
                transaction.complete();
            }
        }
        return result;
    }

//    @Deprecated
//    public RefundIncentiveBonusDto getRefundDetail(String orderId) {
//        return this.getRefundDetail(RefundIncentiveBonusQueryRequestDto.builder()
//                .orderId(orderId)
//                .build());
//    }



    /************************************************************************************************************************/

    public Transaction getCatTransaction(String name, Map<String, Object> map) {
        Transaction transaction = null;
        try {
            transaction = Cat.newTransaction("B.IncentiveBonus", name);
            transaction.addData("url", baseUrl + name + "?" + getParamStr(map));
        }catch (Throwable e) {
            transaction = null;
            LOGGER.error("获取cat事务报错", e);
        }
        return transaction;
    }

    private String getParamStr(Map<String, Object> map) {
        StringBuilder builder = new StringBuilder();
        for(Map.Entry<String, Object> entry : map.entrySet()) {
            if(builder.length() > 0) {
                builder.append("&");
            }
            builder.append(entry.getKey());
            builder.append("=");
            builder.append(entry.getValue());
        }
        return builder.toString();
    }

    public static class IncentiveBonusSignUtil{

        public static String getSign(Object obj, String secretKey) {
            TreeMap<String, String> map = JSON.parseObject(JSON.toJSONString(obj), TreeMap.class);

            String sign = null;
            try {
                sign = IncentiveBonusSignUtil.getSign(map, secretKey);
            } catch (UnsupportedEncodingException e) {
                throw new IllegalArgumentException("getSign error!", e);
            }
            return sign;
        }

        public static String getSign(TreeMap<String, String> paramsTreeMap
                , String appSecret) throws UnsupportedEncodingException {
            String signCalc = montage(paramsTreeMap);
            signCalc = DigestUtils.md5Hex(String.format("%s%s", signCalc, appSecret));
            return signCalc;
        }

        public static String montage(TreeMap<String, String> paramsTreeMap) throws UnsupportedEncodingException {
            String montageCalc = "";
            for (Map.Entry<String, String> entry : paramsTreeMap.entrySet()) {
                String key = entry.getKey(); // map中的key
                String value = "";
                value = String.valueOf(entry.getValue());
                montageCalc = String.format("%s%s=%s&", montageCalc, key
                        , URLEncoder.encode(value, "UTF-8"));
            }
            if (montageCalc.length() > 0) {
                montageCalc = montageCalc.substring(0, montageCalc.length() - 1);
            }
            return montageCalc;
        }

        private static String encodedFix(String encoded) {
            // required
            encoded = encoded.replace("+", "%20");
            encoded = encoded.replace("*", "%2A");
            encoded = encoded.replace("%7E", "~");

            // optional
            encoded = encoded.replace("!", "%21");
            encoded = encoded.replace("(", "%28");
            encoded = encoded.replace(")", "%29");
            encoded = encoded.replace("'", "%27");
            return encoded;
        }
    }
}
