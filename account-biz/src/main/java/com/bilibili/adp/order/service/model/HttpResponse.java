package com.bilibili.adp.order.service.model;

import java.io.Serializable;

public class HttpResponse<E> implements Serializable {
    private static final long serialVersionUID = -1619826314726589493L;
    private Integer code;
    private E data;
    private String message;

    public Integer getCode() {
        return code;
    }
    public void setCode(Integer code) {
        this.code = code;
    }
    public E getData() {
        return data;
    }
    public void setData(E data) {
        this.data = data;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}