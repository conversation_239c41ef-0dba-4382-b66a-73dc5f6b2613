package com.bilibili.adp.account.dao;

import com.bilibili.adp.account.po.WalletConsumeRecordPo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * Created by <PERSON><PERSON><PERSON> on 2016/10/21.
 */
public interface WalletConsumeRecordDao {
    public Integer getCountBySerialNumber(@Param("serial_number") String serialNumber);

    public Set<Integer> getBySerialNumberInAccountIds(@Param("serial_number") String serialNumber, @Param("account_ids") List<Integer> accountIds);

    List<WalletConsumeRecordPo> queryConsumeRecord (@Param("serial_number") String serialNumber, @Param("account_ids") List<Integer> accountIds, @Param("deduction_signs") List<Integer> deductionSigns);

    public void batchInsert(List<WalletConsumeRecordPo> recordPoList);
}
