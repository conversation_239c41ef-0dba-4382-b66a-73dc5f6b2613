package com.bilibili.adp.account.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CardPoolOpRecordPo implements Serializable {
    /**
     * 自增
     */
    private Long id;

    /**
     * 资金池类型(1-资金池 2-预扣款资金池)
     */
    private Integer poolType;

    /**
     * 卡id
     */
    private Integer cardId;

    /**
     * 账号id
     */
    private Integer accountId;

    /**
     * 总额（单位分）
     */
    private Long money;

    /**
     * 日志记录时间
     */
    private Timestamp opTime;

    /**
     * 操作类型 (1-消耗 2-过期 3-充值 4-转账（到预扣款资金池）5-转账（到资金池）)
     */
    private Integer operationType;

    /**
     * 软删除, 0是有效，1是删除
     */
    private Integer isDeleted;

    /**
     * 添加时间
     */
    private Timestamp ctime;

    /**
     * 修改时间
     */
    private Timestamp mtime;

    private static final long serialVersionUID = 1L;
}