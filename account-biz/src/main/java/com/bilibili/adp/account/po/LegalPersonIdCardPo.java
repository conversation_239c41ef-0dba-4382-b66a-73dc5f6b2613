package com.bilibili.adp.account.po;

import java.sql.Timestamp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2017年2月24日
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LegalPersonIdCardPo {
    
    /**自增ID**/
    private Integer id;
    
    /**账号ID**/
    private Integer accountId;
    
    /**身份证账号图片的URL**/
    private String idCardUrl;
    
    /**身份证账号图片类型: 0-正面 1-反面 3-手持身份证**/
    private Integer type;
    
    /**创建时间**/
    private Timestamp addTime;
    
    /**更新时间**/
    private Timestamp updateTime;
    
    /**是否删除: 0-否 1-是**/
    private Integer isDeleted;
    
}
