package com.bilibili.adp.account.po;

import java.sql.Timestamp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2017年2月27日
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SpecialInfoPo {
    /**自增ID**/
    private Integer id;
    
    /**账号ID**/
    private Integer accountId;
    
    /**资质描述**/
    private String desc;
    
    /**资质图片的URL**/
    private String imageUrl;
    
    /**创建时间**/
    private Timestamp addTime;
    
    /**更新时间**/
    private Timestamp updateTime;
    
    /**是否删除: 0-否 1-是**/
    private Integer isDeleted;
}
