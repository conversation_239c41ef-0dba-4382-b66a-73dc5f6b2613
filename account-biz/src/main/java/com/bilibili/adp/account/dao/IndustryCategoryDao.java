package com.bilibili.adp.account.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.bilibili.adp.account.po.IndustryCategoryPo;

/**
 * <AUTHOR>
 * @date 2017年2月27日
 */
public interface IndustryCategoryDao {

    List<IndustryCategoryPo> getByLevel(@Param("level") Integer level);

    List<IndustryCategoryPo> getByParentId(@Param("parentId") Integer parentId);

    List<IndustryCategoryPo> getInIds(@Param("ids") List<Integer> ids);

    List<IndustryCategoryPo> getAll();

    List<IndustryCategoryPo> getInParentIds(@Param("parentIds") List<Integer> parentIds);

    IndustryCategoryPo getByName(@Param("name") String name, @Param("level") int level);
}
