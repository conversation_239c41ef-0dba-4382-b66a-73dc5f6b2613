package com.bilibili.adp.account.pojo;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CrmSecondaryAgentCustomerPo implements Serializable {
    /**
     * 自增id
     */
    private Integer id;

    /**
     * 二级代理主站mid
     */
    private Long mid;

    /**
     * 二级代理商id
     */
    private Integer secondaryAgentId;

    /**
     * 客户id(即CRM账号ID)
     */
    private Integer accountId;

    /**
     * 软删除 0-未删除 1-删除
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    private static final long serialVersionUID = 1L;
}