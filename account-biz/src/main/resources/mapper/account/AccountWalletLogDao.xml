<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.bilibili.adp.account.dao.AccountWalletLogDao">
    <resultMap id="accountWalletLogMap" type="com.bilibili.adp.account.po.AccountWalletLogPo">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="account_id" property="accountId" jdbcType="INTEGER"/>
        <result column="operation_type" property="operationType" jdbcType="INTEGER"/>
        <result column="cash" property="cash" jdbcType="BIGINT"/>
        <result column="credit" property="credit" jdbcType="BIGINT"/>
        <result column="red_packet" property="redPacket" jdbcType="BIGINT"/>
        <result column="special_red_packet" property="specialRedPacket" jdbcType="BIGINT"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="ctime" property="createdAt" jdbcType="TIMESTAMP"/>
        <result column="mtime" property="updatedAt" jdbcType="TIMESTAMP"/>
        <result column="is_deleted" property="isDeleted" jdbcType="INTEGER"/>
        <result column="date" property="date" jdbcType="TIMESTAMP"/>
        <result column="sales_type" property="salesType" jdbcType="TIMESTAMP"/>
        <result column="agent_id" property="agentId" jdbcType="INTEGER"/>
    </resultMap>
    <sql id="all_columns">
        id,account_id,`operation_type`,cash,credit,red_packet,special_red_packet,remark,ctime,mtime,is_deleted, sales_type, agent_id
    </sql>

    <select id="getLogByAccountIdAndType" resultMap="accountWalletLogMap">
        SELECT
        <include refid="all_columns"/>
        FROM acc_account_wallet_log
        WHERE account_id=#{account_id} AND operation_type=#{operation_type} and is_deleted=0
    </select>

    <select id="getLogByAccountIds" resultMap="accountWalletLogMap">
        SELECT
        <include refid="all_columns"/>
        FROM acc_account_wallet_log
        WHERE
        <foreach collection="account_id_list" item="account_id" index="index" open="("
                 separator="," close=")">
            #{account_id}
        </foreach>
        and is_deleted=0
    </select>

    <insert id="insertWithDate" parameterType="com.bilibili.adp.account.po.AccountWalletLogPo"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        acc_account_wallet_log(account_id,`operation_type`,cash,credit,red_packet,special_red_packet,trust_fund,withhold_fund, trust_cash, trust_incentive, trust_fly_coin, remark,is_deleted, `date`, `sales_type`, `agent_id`)
        VALUES
        (
        #{accountId},
        #{operationType},
        #{cash},
        <choose>
            <when test="credit != null">
                #{credit},
            </when>
            <otherwise>
                0,
            </otherwise>
        </choose>
        #{redPacket},
        #{specialRedPacket},
        <choose>
            <when test="trustFund != null">
                #{trustFund},
            </when>
            <otherwise>
                0,
            </otherwise>
        </choose>
        <choose>
            <when test="withholdFund != null">
                #{withholdFund},
            </when>
            <otherwise>
                0,
            </otherwise>
        </choose>
        <choose>
            <when test="trustCash != null">
                #{trustCash},
            </when>
            <otherwise>
                0,
            </otherwise>
        </choose>
        <choose>
            <when test="trustIncentive != null">
                #{trustIncentive},
            </when>
            <otherwise>
                0,
            </otherwise>
        </choose>
        <choose>
            <when test="trustFlyCoin != null">
                #{trustFlyCoin},
            </when>
            <otherwise>
                0,
            </otherwise>
        </choose>
        #{remark},
        0,
        #{date},
        #{salesType},
        #{agentId}
        )
    </insert>

</mapper>