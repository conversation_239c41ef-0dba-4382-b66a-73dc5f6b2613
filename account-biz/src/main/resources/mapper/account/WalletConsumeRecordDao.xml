<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.bilibili.adp.account.dao.WalletConsumeRecordDao">
    <resultMap id="recordMap" type="com.bilibili.adp.account.po.WalletConsumeRecordPo">
        <result column="account_id" property="accountId" jdbcType="INTEGER"/>
        <result column="serial_number" property="serialNumber" jdbcType="VARCHAR"/>
        <result column="operation_type" property="operationType" jdbcType="TINYINT"/>
        <result column="deduction_sign" property="deductionSign" jdbcType="TINYINT"/>
        <result column="money" property="money" jdbcType="BIGINT"/>
    </resultMap>

    <select id="getCountBySerialNumber" resultType="java.lang.Integer">
        SELECT
        COUNT(1)
        FROM acc_wallet_consume_record
        WHERE serial_number=#{serial_number} and is_deleted=0
    </select>

    <select id="getBySerialNumberInAccountIds" resultType="java.lang.Integer">
        SELECT
        account_id
        FROM acc_wallet_consume_record
        WHERE serial_number=#{serial_number}
        <if test="account_ids.size() > 0">
            and account_id in
            <foreach open="("  separator="," close=")" collection="account_ids" item="aid">
                #{aid}
            </foreach>
        </if>
         and is_deleted=0
    </select>

    <select id="queryConsumeRecord" resultMap="recordMap">
        SELECT
        *
        FROM acc_wallet_consume_record
        WHERE serial_number=#{serial_number}
        <if test="account_ids.size() > 0">
            and account_id in
            <foreach open="("  separator="," close=")" collection="account_ids" item="aid">
                #{aid}
            </foreach>
        </if>
        <if test="deduction_signs.size() > 0">
            and deduction_sign in
            <foreach open="("  separator="," close=")" collection="deduction_signs" item="deduction_sign">
                #{deduction_sign}
            </foreach>
        </if>
        and is_deleted=0
    </select>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO `acc_wallet_consume_record`
        (
        account_id,
        serial_number,
        operation_type,
        deduction_sign,
        money
        )
        VALUES
        <foreach collection="list" item="item" index="index"
                 separator=",">
            (
            #{item.accountId},
            #{item.serialNumber},
            #{item.operationType},
            #{item.deductionSign},
            #{item.money}
            )
        </foreach>
    </insert>

</mapper>