<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.bilibili.adp.account.dao.IndustryCategoryDao">
    <resultMap id="IndustryCategoryResult" type="com.bilibili.adp.account.po.IndustryCategoryPo">
        <id property="id" column="id" />
        <result property="name" column="name" />
        <result property="level" column="level" />
        <result property="parentId" column="p_id" />
        <result property="isDeleted" column="is_deleted" />
        <result property="addTime" column="ctime" />
        <result property="updateTime" column="mtime" />
    </resultMap>

     <sql id="tbl_name">
        acc_category
    </sql>

    <sql id="select_sql">
        select `id`, `name`, `level`, `p_id` from <include refid="tbl_name"/>
    </sql>

    <select id="getByLevel" parameterType="map" resultMap="IndustryCategoryResult">
        <include refid="select_sql"/>
        WHERE
        level = #{level}
        and is_deleted=0
    </select>

    <select id="getByParentId" parameterType="map" resultMap="IndustryCategoryResult">
        <include refid="select_sql"/>
        WHERE
        p_id = #{parentId}
        and is_deleted=0
    </select>

    <select id="getInIds" parameterType="map" resultMap="IndustryCategoryResult">
        <include refid="select_sql"/>
        WHERE
        id IN
        <foreach item="id" index="index" collection="ids"
                     open="(" separator="," close=")">
            #{id}
        </foreach>
        and is_deleted=0
    </select>

    <select id="getAll" resultMap="IndustryCategoryResult">
        <include refid="select_sql"/>
        WHERE is_deleted=0
    </select>

    <select id="getInParentIds" parameterType="map" resultMap="IndustryCategoryResult">
        <include refid="select_sql"/>
        WHERE
        p_id IN
        <foreach item="parentId" index="index" collection="parentIds"
                     open="(" separator="," close=")">
            #{parentId}
        </foreach>
        and is_deleted=0
    </select>

    <select id="getByName" parameterType="map" resultMap="IndustryCategoryResult">
        <include refid="select_sql"/>
        WHERE
        name = #{name} and level = #{level}
        and is_deleted=0
    </select>
</mapper>