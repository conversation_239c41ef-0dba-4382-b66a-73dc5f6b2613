syntax = "proto3";

import "github.com/gogo/protobuf/gogoproto/gogo.proto";
// import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";


package ott.video.service;


option go_package = "buf.bilibili.co/bapis/bapis-gen/ott/video;api";
option java_package = "com.bapis.ott.video";
option (gogoproto.goproto_getters_all) = false;
option java_multiple_files = true;

import "extension/wdcli/wdcli.proto";
option (wdcli.appid) = "main.ott.ott-video-service";

service TvVideoService {
  /****          ****/
  /****    UGC   ****/
  /****          ****/
  // 查询稿件信息，tv本地数据
  rpc TvArcMetas (TvArcMetasReq) returns (TvArcMetasReply);
  // 查询稿件信息，第三方数据，简易版
  rpc TvSimpleArcs (TvSimpleArcsReq) returns (TvSimpleArcsReply);
  // 查询稿件信息，第三方数据，详细且可包括分p信息
  rpc TvArcs (TvArcsReq) returns (TvArcsReply);
  // 查询tv过审up的tv投稿数据
  rpc TvUpArcs (TvUpArcsReq) returns (TvUpArcsReply);
  // 查询tv过审up的tv投稿数据(缓存实时更新)
  rpc TvUpArcsV2 (TvUpArcsV2Req) returns (TvUpArcsV2Reply);
  // 分页查询tv过审up的tv投稿数据(支持排序), 根据 hasMore 返回值确定是否下刷
  rpc TvUpArcsV3 (TvUpArcsV3Req) returns (TvUpArcsV3Reply);
  // 查询tv端up过审的充电稿件(缓存实时更新)
  rpc TvUpChargingArcs (TvUpChargingArcsReq) returns (TvUpChargingArcsReply);
  // 查询ugc所有分区信息
  rpc TvArcTypes (NoArgRequest) returns (TvArcTypesReply);

  // 更新ott_all_video.ott_all_archive_white_tenant表字段
  rpc OttUpdateAllArcWhiteTenant (OttUpdateAllArcWhiteTenantReq) returns (.google.protobuf.Empty);
  // 更新 ugc_archive表的属性
  rpc OttUpdateUgcArchiveProperty(OttUpdateUgcArchivePropertyReq) returns (OttUpdateUgcArchivePropertyResp);

  /****           ****/
  /**** UGC-Video ****/
  /****           ****/
  // 查询稿件分p信息，tv本地数据
  rpc TvVideos (TvVideosReq) returns (TvVideosReply);


  // 更新ott_all_video.ott_all_video_white_tenant表字段
  rpc OttUpdateAllVideoWhiteTenant (OttUpdateAllVideoWhiteTenantReq) returns (.google.protobuf.Empty);
  // 更新ugc_video表属性，如firsti_status字段
  rpc OttUpdateUgcVideoProperty(OttUpdateUgcVideoPropertyReq) returns (.google.protobuf.Empty);



  /****          ****/
  /****    OGV   ****/
  /****          ****/
  // 查询season信息，tv本地数据
  rpc TvSeasonMetas(TvSeasonMetasReq) returns (TvSeasonMetasResp);
  // 查询season信息，第三方数据，pgc/service.Cards
  rpc TvOgvCards(TvOgvCardsReq) returns (TvOgvCardsResp);
  // 查询season信息，第三方数据，discovery://pgc.gateway.view/pgc/internal/view/v2/ott/season
  rpc TvOgvView(TvOgvViewReq) returns (TvOgvViewResp);
  // 查询season当前可播的牌照方
  rpc TvSeasonCanPlay(TvSeasonCanPlayReq) returns (TvSeasonCanPlayResp);
  // 对PayConfig服务的接口进行封装
  rpc TvSnPayCfg(TvSnPayCfgReq) returns (TvSnPayCfgResp);
  // 查询ott专享sids
  rpc OttOnlySids(NoArgRequest) returns (OttOnlySidsResp);
  // 查询指定Season所在系列的所有season
  rpc TvSeriesSeasonMapBySid(TvSeriesSeasonMapBySidReq) returns (TvSeriesSeasonMapBySidResp);


  // 更新ott_all_video.ott_all_season_white_tenant表字段
  rpc OttUpdateAllSeasonWhiteTenant (OttUpdateAllSeasonWhiteTenantReq) returns (.google.protobuf.Empty);

  /****            ****/
  /****  OGV - EP  ****/
  /****            ****/
  // 查询ep信息，tv本地数据
  rpc TvEpMetas(TvEpMetasReq) returns (TvEpMetasResp);
  // 查询ep list信息By Sids，tv本地数据
  rpc TvEpMetaListBySids(TvEpMetaListBySidsReq) returns (TvEpMetaListBySidsResp);
  // 查询ep信息，第三方数据，pgc/service.List
  rpc TvOgvEps(TvOgvEpsReq) returns (TvOgvEpsResp);
  // 根据条件查询eps，结果按id排序（暂无缓存，禁大流量请求）
  rpc TvEpQuery(TvEpQueryReq) returns (TvEpQueryResp);
  // 根据sid查询下游所有符合条件 epid ，第三方数据，pgc/serivce.List
  rpc ListEpBySeasonId(ListEpBySeasonIdReq) returns (ListEpBySeasonIdResp);

  // 更新ott_all_video.ott_all_episode_white_tenant表字段
  rpc OttUpdateAllEpisodeWhiteTenant (OttUpdateAllEpisodeWhiteTenantReq) returns (.google.protobuf.Empty);



  // 课堂
  // 查询课堂season信息，tv本地数据
  rpc TvClassSeasonMetas(TvClassSeasonMetasReq) returns (TvClassSeasonMetasResp);
  // 查询课堂Episode信息，tv本地数据
  rpc TvClassEpisodeMetas(TvClassEpisodeMetasReq) returns (TvClassEpisodeMetasResp);

  // 榜单
  // OGV 榜单 根据榜单id查找榜单信息
  rpc TvOgvRanks (TvOgvRanksReq) returns (TvOgvRanksReply);
  // OGV 榜单 根据分区id查找榜单信息
  rpc OGVRankByCat(OGVRankByCatReq) returns (OGVRankByCatResp);
  // OGV 榜单 根据season id 查找最高排名榜单
  rpc OGVRankBySid(OGVRankBySidReq) returns (OGVRankBySidResp);
  // OGV片单干预配置(分区维度)
  rpc OgvsModifyByCat(OgvsModifyByCatReq) returns (OgvsModifyByCatResp);

  // 专题
  rpc TvTopics (TvTopicsReq) returns (TvTopicsReply);
  // 查询高画质分区专题特殊排序缓存数据
  rpc TvHQTopic (TvHQTopicReq) returns (TvHQTopicReply);

  // UP主
  // 查询up主信息
  rpc TvUppers (TvUppersReq) returns (TvUppersReply);

  // ugc稿件入库相关
  // 系统过滤
  rpc ArcsAllow (ArcsAllowReq) returns (ArcsAllowReply);
  // 免内审
  rpc ArcsFreeAegis (ArcsFreeAegisReq) returns (ArcsFreeAegisReply);


  /****             ****/
  /****     审核     ****/
  /****             ****/
  // 审核回调
  rpc AuditJsonAudit(AuditJsonAuditReq) returns (AuditJsonAuditReply);
  // 转码回调
  rpc AuditTransCode(AuditTransCodeReq) returns (.google.protobuf.Empty);
  // 更新水印转码申请时间
  rpc AuditApplyPGC(AuditApplyPGCReq) returns (.google.protobuf.Empty);
  rpc AuditCallBack(AuditCallBackReq) returns (.google.protobuf.Empty);

  /****                ****/
  /****     外部媒资    ****/
  /****                ****/
  // 外部的season的基本信息,批量接口,只返回有效的数据，
  rpc ExternalSeasonMetas(ExternalSeasonMetaReq) returns (ExternalSeasonMetaReply);
  // 外部的ep的基本信息,批量接口
  rpc ExternalEpisodeMetas(ExternalEpisodeMetaReq) returns (ExternalEpisodeMetaReply);

}

// ogv部门相关字段
enum OgvValidType{
  PgcValid   = 0; //pgc上架的
  NotDeleted = 1; // 全部非删除的
  OttValid   = 2; // OTT 上架的
  AllValid   = 3; // 所有上架的
}

// 缓存级别
enum CacheLevel {
  DefaultCache  = 0; // 默认，走缓存
  NoCache       = 1; // 不走任何缓存
  NoLocalCache  = 2; // 不走本地缓存
}

enum EpStates { // ep的审核状态
  Default     = 0; // 默认值
  NotCommit   = 1; // 未提交
  Checking    = 2; // 审核中
  Checked     = 3; // 审核通过
  Online      = 4; // 已上线
  Offline     = 5; // 已下线
}

// 数据库直接支持的类型
message DbSupportType {
  oneof value {
    int64 int64_val = 1;
    string string_val = 2;
    google.protobuf.Timestamp time_val = 3;
  }
}



message ArcsAllowReq {
  repeated int64 aids = 1 [(gogoproto.moretags) = 'validate:"max=100,dive,gt=0,required"'];
  // 目前只区分手动提审（传1），其它情况无需传值
  int64 source = 2;
  // 是否需要返回过滤理由
  bool needReason = 3;
}

message ArcsAllowReply {
  map<int64, bool> items = 1;
  map<int64, string> reason = 2;  // 过滤理由
}

message ArcsFreeAegisReq {
  repeated int64 aids = 1 [(gogoproto.moretags) = 'validate:"max=100,dive,gt=0,required"'];
}

message ArcsFreeAegisReply {
  map<int64, bool> results = 1;
}

/****             ****/
/****     审核结构体     ****/
/****             ****/
message AuditCallBackReq {
  repeated string ids = 1 [(gogoproto.moretags) = 'form:"ids" validate:"required,mid=1,max=200"'];
  string id_type = 2 [(gogoproto.moretags) = 'form:"id_type" validate:"required"'];
  //审核结果
  int64 result = 3 [(gogoproto.moretags) = 'form:"result" validate:"required"'];
  string message = 4 [(gogoproto.moretags) = 'form:"message" '];
}

message AuditApplyPGCReq{
  int64 cid= 1 [(gogoproto.moretags) = 'form:"cid" validate:"required"'];
  int64 apply_time = 2 [(gogoproto.moretags) = 'form:"apply_time" validate:"required"'];
}
message AuditTransCodeReq{
  // content type: pgc/ugc
  string cont_type = 1 [(gogoproto.moretags) = 'form:"cont_type" validate:"required"'];
  int64 cid= 2 [(gogoproto.moretags) = 'form:"cid" validate:"required"'];
  // 2=转码失败，1=转码完成，0=未完成
  int64 action = 3 [(gogoproto.moretags) = 'form:"action" validate:"min=0,max=2"'];
  //1 = is 4k, 0 = not 4k
  int32 fourk = 4 [(gogoproto.moretags) = 'form:"fourk" '];
  // 1 = is 8k, 0 = not 8k
  int32 eightk= 5 [(gogoproto.moretags) = 'form:"eightk" '];
}

message AuditJsonAuditReq{
  string media_type = 1  [(gogoproto.moretags) = 'form:"media_type" validate:"required"'];
  string lic = 2  [(gogoproto.moretags) = 'form:"lic" validate:"required"'];
  string t_id = 3  [(gogoproto.moretags) = 'form:"t_id" validate:"required"'];
  string input_time = 4  [(gogoproto.moretags) = 'form:"input_time" validate:"required"'];
  string sign = 5  [(gogoproto.moretags) = 'form:"sign" validate:"required"'];
  AuditJsonAuditInfo data = 6  [(gogoproto.moretags) = 'form:"data" validate:"required"'];

}
message AuditJsonAuditInfo{
  string t_id = 1  [(gogoproto.moretags) = 'form:"t_id" '];
  string date = 2  [(gogoproto.moretags) = 'form:"date" '];
  string media_type = 3  [(gogoproto.moretags) = 'form:"media_type" '];
  repeated AuditJsonAuditItem data = 4  [(gogoproto.moretags) = 'form:"data" '];

}

message AuditJsonAuditItem{
  string id = 1  [(gogoproto.moretags) = 'form:"id" '];
  string check_msg= 2  [(gogoproto.moretags) = 'form:"check_msg" '];
  int32 classification= 3  [(gogoproto.moretags) = 'form:"classification" '];
  string sign = 4  [(gogoproto.moretags) = 'form:"sign" '];

}

message AuditJsonAuditReply{
  repeated string not_found_ids = 1;
}

// NoArgReq
message NoArgRequest {}

message TvArcTypesReply {
  map<int32, Type> types = 1;
}

message Type {
  // 分区id
  int32 id = 1 [(gogoproto.jsontag) = "id", json_name = "id"];
  // 该分区的父级id
  int32 pid = 2 [(gogoproto.jsontag) = "pid"];
  // 该分区对应的名称
  string name = 3 [(gogoproto.jsontag) = "name"];
}

message OttUpdateAllArcWhiteTenantReq {
  int64 aid = 1;
  int32 tenant_id = 2;
  map<string, DbSupportType> update_field = 3;  // 需要更新的字段
}

message OttUpdateAllVideoWhiteTenantReq {
  int64 cid = 1;
  int32 tenant_id = 2;
  map<string, DbSupportType> update_field = 3;  // 需要更新的字段
}

enum VideoPropertyOpType {
  Firsti= 0; // 首帧图和状态
  FirstiStatus = 1; //  只更新状态
}
message OttUpdateUgcVideoPropertyReq {
  int64 cid = 1 [(gogoproto.moretags) = 'form:"cid" validate:"required"'];
  int64 aid = 2 ;
  // 0:首帧图，1 属性
  VideoPropertyOpType p_type = 3;

  //首帧高清图地址，只处理非空情况
  string firsti = 4;
  //首帧高清图状态,只处理非0的情况
  int32 firsti_status = 5;
  // 属性，"或"的方式添加进去
  uint64 attribute = 6;
}

enum ArchivePropertyOpType {
  Attribute = 0; // 属性
}
message OttUpdateUgcArchivePropertyReq {
  int64 aid = 1 [(gogoproto.moretags) = 'form:"aid" validate:"required"'];
  ArchivePropertyOpType p_type = 2;
  // 属性，"或"的方式添加进去
  uint64 attribute = 3;
}
message OttUpdateUgcArchivePropertyResp{
  // 影响的行数
  int64 rows = 1 ;
}


message TvUpArcsReq {
  int64 mid = 1 [(gogoproto.moretags) = 'form:"mid" validate:"min=1"'];
  // pn 第几页
  int64 pn = 2 [(gogoproto.moretags) = 'form:"pn" validate:"min=1"'];
  // ps 分页大小
  int64 ps = 3 [(gogoproto.moretags) = 'form:"ps" validate:"min=1,max=200"'];
  // mobi_app
  string mobi_app = 4 [(gogoproto.moretags) = 'form:"mobi_app"'];
}

message TvUpArcsReply {
  repeated ArcMetaInfo list = 1;
  int64 total = 2;
}

message TvUpArcsV2Req {
  int64 mid = 1 [(gogoproto.moretags) = 'form:"mid" validate:"min=1"'];
  // pn 第几页
  int64 pn = 2 [(gogoproto.moretags) = 'form:"pn" validate:"min=1"'];
  // ps 分页大小
  int64 ps = 3 [(gogoproto.moretags) = 'form:"ps" validate:"min=1,max=1000"'];
  // mobi_app
  string mobi_app = 4 [(gogoproto.moretags) = 'form:"mobi_app"'];
}

message TvUpArcsV2Reply {
  repeated ArcMetaInfo list = 1;
  int64 total = 2;
}

enum OrderType {
  DESC   = 0;
  ASC    = 1;
}

enum UpArcsSearchOrder {
  // Pubtime 稿件发布时间
  Pubtime = 0;
  // click 稿件点击(播放)数
  Click = 1;
  // fav 稿件收藏数
  Fav = 2;
  // share 稿件分享数
  Share = 3;
  // reply 稿件评论数
  Reply = 4;
  // coin 稿件投币数
  Coin = 5;
  // dm 稿件弹幕数
  Dm = 6;
  // likes 稿件点赞数
  Likes = 7;
  // vt 稿件播放时长
  Vt = 8;
}

message TvUpArcsV3Req {
  int64 mid = 1 [(gogoproto.moretags) = 'form:"mid" validate:"min=1"'];
  // pn 第几页
  int64 pn = 2 [(gogoproto.moretags) = 'form:"pn" validate:"min=1"'];
  // ps 分页大小
  int64 ps = 3 [(gogoproto.moretags) = 'form:"ps" validate:"min=1,max=1000"'];
  // 排序字段
  OrderType sort = 4 [(gogoproto.moretags) = 'form:"sort"' ];
  // 排序方式
  UpArcsSearchOrder search_order = 5 [(gogoproto.moretags) = 'form:"search_order"' ];
  // mobi_app
  string mobi_app = 6 [(gogoproto.moretags) = 'form:"mobi_app"'];
}

message TvUpArcsV3Reply {
  repeated ArcMetaInfo list = 1;
  // 粉板返回总数量(包含粉板未过审数据总量)
  int64 total = 2;
  // 实际返回每页数量
  int64 actual_num = 3;
  // 是否还有下一页，依据该值决定是否继续请求
  bool has_more = 4;
}

message TvUpChargingArcsReq {
  int64 mid = 1 [(gogoproto.moretags) = 'form:"mid" validate:"min=1"'];
  // pn 第几页
  int64 pn = 2 [(gogoproto.moretags) = 'form:"pn" validate:"min=1"'];
  // ps 分页大小
  int64 ps = 3 [(gogoproto.moretags) = 'form:"ps" validate:"min=1,max=1000"'];
  // mobi_app
  string mobi_app = 4 [(gogoproto.moretags) = 'form:"mobi_app"'];
}

message TvUpChargingArcsReply {
  repeated ArcMetaInfo list = 1;
  int64 total = 2;
}

message OGVRankByCatReq {
  // 分区类目ID 	1: 番剧, 2: 电影, 3: 纪录片, 4: 国漫, 5: 电视剧, 7: 综艺
  int32 category = 1;
}

message OGVRankByCatResp {
  repeated OGVRankInfo results = 1;
}

message TvOgvRanksReq {
  repeated int64 rank_ids = 1;
}

message TvOgvRanksReply {
  map<int64,OGVRankInfo> list = 1;
}

message OGVRankBySidReq {
  // season id
  int64 sid = 1;
}

message OGVRankBySidResp {
  // 榜单信息
  OGVRankInfo rank = 1;
  // 排序
  int32 order = 2;
}

message OgvsModifyByCatReq {
  // 分区类目ID 	1: 番剧, 2: 电影, 3: 纪录片, 4: 国漫, 5: 电视剧, 7: 综艺
  int32 category = 1;
}

message OgvsModifyByCatResp {
  repeated OgvsModify modifies = 1;
}

message OgvsModify {
  // 自增主键
  int64 id = 1;
  // ogv分区类型 1: "番剧",2: "电影",3: "纪录片",4: "国漫",5: "电视剧",7: "综艺"
  int32 category = 2;
  // 片单类型: 1-专题片单, 2-榜单片单
  int32 ogvs_type = 3;
  // 片单id: 1-专题id, 2-榜单id
  int64 ogvs_id = 4;
  // 预览内容类型: 1-pgc剧集 2-pgc单p
  int32 preview_type = 5;
  // 预览内容id: 1-season_id 2-ep_id
  int64 preview_id = 6;
}


message TvTopicsReq {
  repeated int64 ids = 1 [(gogoproto.moretags) = 'form:"ids" validate:"required"'];
  string mobi_app = 2 [(gogoproto.moretags) = 'form:"mobi_app"'];
}

message TvTopicsReply {
  map<int64,TopicInfo> list = 1;
}

message TvHQTopicReq {
  int64  id = 1 [(gogoproto.moretags) = 'form:"id" validate:"required"'];
  string mobi_app = 2 [(gogoproto.moretags) = 'form:"mobi_app"'];
}

message TvHQTopicReply {
  TopicInfo topic = 1;
}

message TopicInfo {
  // 专题id
  int64 id = 1;
  // 专题类型，1：点播主题，2：轮播主题
  int32 type = 2;
  // 专题展示标题，可为空
  string title = 3;
  // 头图
  string head_img = 4;
  // 专题展示标题，可为空
  int32 flexible = 5;
  // 专题名称，统计使用
  string name = 6;
  // 是否删除
  int32 is_deleted = 7;
  // 修改时间
  int64 mtime = 8;
  // 创建时间
  int64 ctime = 9;
  // 绑定状态
  int32 binding_status = 10;
  // 渠道黑名单,按位处理
  int32 black_channel = 11;
  // 底图url，轮播主题时未空
  string background_img = 12;
  // 底色
  string color = 13;
  // 专题状态，0：下线，1：上线
  int32 status = 14;
  // 是否可展示
  bool can_play = 15;
  // 分组信息
  repeated GroupInfo groups = 16;
  // 无mobi_app时，各牌照方可播情况，第0位yst,第1位bst
  int64 offline_can_play = 17;
  bool is_new_demand = 18;
}

message GroupInfo {
  // 分组id
  int64 id = 1;
  // 分组栅格类型，1，横图列表、2,竖图1列表、3,竖图2列表
  int32 grid = 2;
  // 分组标题，可为空
  string title = 3;
  // 分组icon
  string icon = 4;
  // 是否可以折行
  int32 flexible = 5;
  // 上报标题
  string name = 6;
  // 是否删除
  int32 is_deleted = 7;
  // 创建时间
  int64 ctime = 8;
  // 修改时间
  int64 mtime = 9;
  int32 source = 10;
  int32 source_type = 11;
  // 容量
  int32 capacity = 12;
  // 标签 IDs
  string label_ids = 13;
  repeated GroupContent contents = 14;
}

message GroupContent {
  int32 type = 1;
  int64 id = 2;
  string title = 3;
  string cover = 4;
}

message OGVRankInfo {
  // 榜单id
  int64 rank_id = 1;
  // 榜单名称
  string title = 2;
  // 榜单内sids集合
  repeated int64 sids = 3;
  // 类别
  int32 category = 4;
  // 排序
  int32 order = 5;
  // 内部剧集信息,
  repeated OgvRankSeason season_cache = 6;
}

message OgvRankSeason {
  int64 season_id = 1;
  string title = 2;
  string cover = 3;
}


message TvOgvEpsReq {
  repeated int64 epids = 1 [(gogoproto.moretags) = 'form:"epids" validate:"max=5000"'];
  // 0=pgc上架的 1=全部非删除的 2=ott上架的 3=所有上架的  (默认2:ott上架的)
  repeated OgvValidType type = 2 [(gogoproto.moretags) = 'form:"type" validate:"max=1"'];
  // cids
  repeated int64 cids = 3 [(gogoproto.moretags) = 'form:"cids" validate:"max=5000"'];
}

message TvOgvEpsResp {
  // 对应TvOgvEpsReq.aids的查询
  map<int64,OgvEpInfo> list = 1;
  // 对应TvOgvEpsReq.cids的查询
  map<int64,OgvEpInfo> cids_list = 2;
}

message OgvEpInfo {
  int64 epid = 1;
  int64 aid = 2;
  int64 cid = 3;
  // 短标题
  string index_title = 4;
  // ep封面
  string cover = 5;
  // 长标题
  string long_title = 6;
  // 排序值 ord
  int32 index = 7;
  // badge
  string badge = 8;
  // badge_type
  int32 badge_type = 9;
  // ep时长
  int32 duration = 10;
  // season类型  1：番剧，2：电影，3：纪录片，4：国漫，5：电视剧
  int32 season_type = 11;
  // 第x集 xxx
  string show_title = 12;
  // season 标题
  string season_title = 13;
  //发布日期
  string release_date = 14;
  // 是否允许超前点播
  bool allow_demand = 15;
  // ep 付费状态
  int32 episode_status = 16;
  // season_id
  int32 season_id = 17;
  // 分节类别 0正片1预告片2其他
  int32 section_type = 18;
  // ep开播时间
  string pub_date_time = 19;
  // 是否已发布
  int32 published = 20;
  // 是否免费
  bool is_free = 21;
  // 备案号
  string record = 22;
  // 备案图标
  string record_icon = 23;
}

message TvOgvCardsReq {
  repeated int64 sids = 1 [(gogoproto.moretags) = 'form:"sids" validate:"required,max=5000"'];
}

message TvOgvCardsResp {
  map<int64,OgvCardInfo> list = 1;
}

message OgvCardInfo {
  int64 season_id = 1;
  // season类型  1：番剧，2：电影，3：纪录片，4：国创，5：电视剧 7: 综艺
  int32 season_type = 2;
  // 总集数
  int32 total_count = 3;
  // 第一话相关信息
  CardEpInfo first_ep_info = 4;
  // 最新一话相关信息
  CardEpInfo new_ep_info = 5;
  // 标题
  string title = 6;
  // 封面
  string cover = 7;
  // season付费状态
  int32 season_status = 8 [(gogoproto.jsontag) = "season_status", json_name = "season_status"];
  // season类型显示名  1：番剧，2：电影，3：纪录片，4：国创，5：电视剧
  string season_type_name = 9 [(gogoproto.jsontag) = "season_type_name", json_name = "season_type_name"];
  // 副标题(同9字副标题)
  string subtitle = 10 [(gogoproto.jsontag) = "subtitle", json_name = "subtitle"];
  // media_id
  int32 media_id = 11 [(gogoproto.jsontag) = "media_id", json_name = "media_id"];
  // 是否完结 1完结 0未完结
  int32 is_finish = 12 [(gogoproto.jsontag) = "is_finish", json_name = "is_finish"];
  // 是否开播 1开播 0未开播
  int32 is_started = 13 [(gogoproto.jsontag) = "is_started", json_name = "is_started"];
  // 角标文字，如“抢先”这种
  string badge = 14 [(gogoproto.jsontag) = "badge", json_name = "badge"];
  // 角标类型，0粉色，1蓝色，2橙色
  int32 badge_type = 15 [(gogoproto.jsontag) = "badge_type", json_name = "badge_type"];
  // 简介(全)
  string summary = 16;
}


message TvSeriesSeasonMapBySidReq {
  // 指定剧集id
  int64 sid = 1;
}

message  TvSeriesSeasonMapBySidResp {
  // 指定剧集id所在系列中所有剧集列表。 map<系列id, 剧集id列表>
  map<int64, TvSeriesSeasonList> series_seasons_map = 1;
}

message TvSeriesSeasonList {
  repeated int64 sids = 1;
}


message CardEpInfo {
  // epid
  int64 epid = 1;
  // 封面
  string cover = 2;
  // 短标题
  string title = 3;
  // 长标题
  string long_title = 4;
  // 上线时间
  string pubtime = 5;
  // 时长 单位毫秒
  int64 duration = 6;
  // 更新至xx话
  string index_show = 7;
}
message TvEpMetasReq {
  // epids
  repeated int64 epids = 1 [(gogoproto.moretags) = 'form:"epids" validate:"max=5000"'];
  // 平台
  string mobi_app = 2;
  // cids
  repeated int64 cids = 3 [(gogoproto.moretags) = 'form:"epids" validate:"max=1000"'];
  // sids(存在延迟)
  TvEpMetasSid sids_req = 4 [(gogoproto.moretags) = 'form:"sids_req"'];
  // 默认0=走缓存，1=不走任何缓存，2=仅跳过本地缓存。 对epids和cids查询有效
  CacheLevel cache_level = 5 [(gogoproto.moretags) = 'form:"cache_level" default:"0"'];
  // aids
  repeated  int64 aids = 6 [(gogoproto.moretags) = 'form:"aids" validate:"max=1000"'];
}

message TvEpMetasSid {
  // sids
  repeated int64 sids = 1 [(gogoproto.moretags) = 'form:"sids" validate:"max=100"'];
  // order type 返回最新或最老的一条结果
  OrderType order_type = 2;
  // cover exist 返回具有封面信息的结果
  bool cover_exist = 3;
  // state 状态 1=未提交, 2=审核中, 3=审核通过, 4=审核未通过, 5=已上线, 6=已下线，默认为3
  EpStates state = 4;
}

message TvEpMetasResp {
  // 对应TvEpMetasReq.epids的查询结果
  map<int64,EpMetaInfo> list = 1;
  // 对应TvEpMetasReq.cids的查询结果
  map<int64,EpMetaInfo> cids_list = 2;
  // 对应TvEpMetasReq.sids的查询结果
  map<int64,EpMetaInfo> sids_list = 3;
  // 对应TvEpMetasReq.aids的查询结果
  map<int64,EpMetaInfo> aids_list = 4;
}

message EpMetaInfo {
  int64 epid = 1;
  // 审核结果
  int32 state = 2;
  // 上下架
  int32 valid = 3;
  // 是否可播
  bool can_play = 5;
  int64 offline_can_play = 6;
  // 内容相关
  int32 mark = 7;
  string cover = 8;
  string title = 9;
  string subtitle = 10;
  int32 pay_status = 11;
  int64 season_id = 12;
  int64 id = 13;
  int64 ctime = 14;
  int64 mtime = 15;
  // 牌照审核原因
  string reason = 16;
  int64 cid = 17;
  // 数据源版本
  int64 version = 18;
  // 4K超分
  bool is_fourk_sr = 19;
  // ep的aid
  int64 aid = 20;
  // 4k
  bool is_fourk = 21;
  // 属性位
  uint64 attribute = 22;
  // 外部媒资bitmap标识；0=内部稿件; 1=南传
  uint64 external_source = 23;
}


message TvEpMetaListBySidsReq {
  // season_id
  repeated int64 sids = 1 [(gogoproto.moretags) = 'form:"sids" validate:"required,max=100"'];
  // 平台
  string mobi_app = 2;
  // 默认0=走缓存 1=不走任何缓存 2=仅跳过本地缓存
  CacheLevel cache_level = 3 [(gogoproto.moretags) = 'form:"cache_level" default:"0"'];
}

message TvEpMetaListBySidsResp {
  // {sid: []epMetaInfo}
  map<int64, EpMetaList> epListMap = 1;
}

message EpMetaList {
  repeated EpMetaInfo list = 1;
}

message TvEpQueryReq {
  // 第几页(默认为1=首页)
  int32 page_num = 1 [(gogoproto.moretags) = 'form:"page_num" default:"1"'];
  // 单页数据量
  int32 page_size = 2 [(gogoproto.moretags) = 'form:"page_size" default:"1" validate:"max=20"'];
  // tv_content.id，非epid
  repeated int64 ids = 3 [(gogoproto.moretags) = 'form:"ids" validate:"max=20"'];
  // SeasonId
  repeated int64 sids = 4 [(gogoproto.moretags) = 'form:"sids" validate:"max=20"'];
  // 上下架（为空则不作为查询条件）
  repeated int32 valid = 5 [(gogoproto.moretags) = 'form:"valid" validate:"max=1"'];
  // 审核状态
  repeated int32 states = 6;
  // 获取全部数据
  bool fetch_all = 7;
}

message TvEpQueryResp {
  repeated TvEpQueryItem list = 1;
}

message TvEpQueryItem {
  int64 id = 1;
  int64 epid = 2;
  int64 season_id = 3;
  // 内容相关
  // 封面
  string cover = 4;
  // 标题
  string title = 5;
  // 副标题
  string subtitle = 6;
  // 付费状态
  int32 pay_status = 7;
  // 上下架
  int32 valid = 8;
  // 审核状态
  int32 state = 9;
  // 转码结果 2=转码失败，1=转码完成，0=未完成
  int32 transcoded = 10;
  // 水印转码申请时间
  int64 apply_time = 11;
  // cid
  int64 cid = 12;
  // mtime
  int64 mtime = 13;
  // ctime
  int64 ctime = 14;
  // version
  int64 version = 15;
  // menuId
  int32 menu_id = 16;
  // auditState
  int32 audit_state = 17;
  // deleted
  int32 is_deleted = 18;
  // 属性
  uint64 attribute = 19;
}

message ListEpBySeasonIdReq {
  // seasonId 剧集Id
  int64 sid = 1;
  // ep状态查询参数
  // 0- pgc上架的；1- 全部非删除的；2- 同步给OTT的；3- 所有上架的
  int32 ep_status_query_type = 2;
  // 分节类型查询参数,支持传多个,不传默认查所有
  // 0-正片；1-预告片；2-其他；3-关联分节；4-活动中插；5-pugv；6-角色 此类型中无ep；7-合集大卡 此类型中无ep；2233-查所有
  repeated int32 section_query_type = 3;
  // 是否携带OTT数据
  bool withMeta = 4;
  // 在withMeta=true时有效，默认为android_tv_yst
  string mobi_app = 5 [(gogoproto.moretags) = 'form:"mobi_app" default:"android_tv_yst"'];
}

message OttUpdateAllSeasonWhiteTenantReq {
  int64 season_id = 1;
  int32 tenant_id = 2;
  map<string, DbSupportType> update_field = 3;  // 需要更新的字段
}

message OttUpdateAllEpisodeWhiteTenantReq {
  int64 epid = 1;
  int32 tenant_id = 2;
  map<string, DbSupportType> update_field = 3;  // 需要更新的字段
}


message ListEpBySeasonIdResp {
  // epid列表，若为空则无符合条件ep
  repeated EpInfo eps = 1;
}

message EpInfo {
  OgvEpInfo ogvInfo = 1;
  EpMetaInfo metaInfo = 2;
}

message TvSeasonMetasReq {
  // 剧集id
  repeated int64 sids = 1 [(gogoproto.moretags) = 'form:"sids" validate:"required,max=5000"'];
  // 平台
  string mobi_app = 2;
}

message TvSeasonMetasResp {
  map<int64,SsnMetaInfo> list = 1;
}

message SsnMetaInfo {
  int64 season_id = 1;
  /**** 鉴权相关  ****/
  int32 valid = 2;
  int32 check = 3;
  bool can_play = 4;
  int64 offline_can_play = 5;
  /****  内容相关  ****/
  int32 category = 6;
  string area = 7;
  int64 playtime = 8;
  string role = 9;
  string staff = 10;
  int32 newest_order = 11;
  int64 newest_epid = 12;
  // Deprecated  废弃字段
  int32 newest_nb = 13;
  int32 total_num = 14;
  string style = 15;
  string origin_name = 16;
  string alias = 17;
  int32 pay_status = 18;
  string show_index = 19;
  int64 gray_type = 20;
  int32 ott_only = 21;
  bool is_fourk = 22;
  string horizonal_cover = 23;
  string nine_subtitle = 24;
  int32 is_preview = 25;
  string marquee_desc = 26;
  string producer = 27;
  string award = 28;
  string cover = 29;
  string desc = 30;
  string title = 31;
  string up_info = 32;
  int64 ctime = 33;
  int64 mtime = 34;
  // 牌照审核原因
  string reason = 35;
  int32 status = 36;
  int32 is_deleted = 37;
  // 4K超分
  bool is_fourk_sr = 38;
  // 运营配置的看点标签
  string shining = 39;
  // 租户位存储字段。枚举详见info文档https://info.bilibili.co/pages/viewpage.action?pageId=827611585
  uint64 tenant_attribute = 40;
  // 备案号
  string license_key = 41;
  // 外部媒资bitmap标识；0=内部稿件; 1=南传
  uint64 external_source = 42;
}

// 根据seasonId获取payElement信息-请求
message TvSnPayCfgReq {
  int64 season_id = 1;
}

// 根据seasonId获取payElement信息-请求
message TvSnPayCfgResp {
  PayConfigElement result = 1;
}

// PayConfigElement
message PayConfigElement {
  // 观看时效
  int32 time_limit = 1;
  // 普通用户折扣
  int32 discount = 2;
  // 大会员折扣
  int32 vip_discount = 3;
  // 大会员折扣价,单位:分
  int32 vip_discount_price = 4;
}


message OttOnlySidsResp {
  repeated int64 sids = 1;
}

message TvOgvViewReq {
  int64 mid = 1;
  int64 guest_id = 2;
  string mode = 3;
  string mobi_app = 4;
  int64 build = 5;
  string platform = 6;
  int64 season_id = 7;
  bool is_proj = 8;
}

// 接口字段说明：https://info.bilibili.co/pages/viewpage.action?pageId=128113468#viewgatewayappv2%E6%8E%A5%E5%8F%A3%E6%96%87%E6%A1%A3-modules%E5%88%86%E8%8A%82%E6%A8%A1%E5%9D%97
message TvOgvViewResp {
  // season_id
  int64 season_id = 1;
  // media_id
  int32 media_id = 2;
  // title
  string title = 3;
  // 季度标题
  string season_title = 4;
  // cover
  string cover = 5;
  // 详情
  string detail = 6;
  // 别名
  string alias = 7;
  // origin_name
  string origin_name = 8;
  // season_type
  int32 type = 9;
  // 类型名称
  string type_name = 10;
  // 角标信息
  OgvBadgeInfo badge_info = 11;
  // 方图
  string square_cover = 12;
  // 精修长图
  string refine_cover = 13;
  // 剧集描述 年份｜频道｜风格
  string type_desc = 14;
  // 剧集模式 1单集 2多集
  int32 mode = 15;
  // 分享链接
  string share_url = 16;
  // 分享短链
  string short_link = 17;
  // 跳转链接
  string link = 18;
  // 简介
  string evaluate = 19;
  // record 贴片文案 播放正片时展示
  string record = 20;
  // 副标题
  string subtitle = 21;
  // 评分
  OgvRating rating = 22;
  // season付费状态
  int32 status = 23;
  // 总集数
  int32 total = 24;
  // is_new
  int32 is_new = 25;
}

message OgvBadgeInfo {
  // 角标文案
  string text = 1;
  // 角标色值
  string bg_color = 2;
  // 角标色值-夜间模式
  string bg_color_night = 3;
}

message OgvRating {
  // 评分数
  float score = 1;
  // 评分人数
  int32 count = 2;
}

message TvSeasonCanPlayReq {
  // 剧集id
  repeated int64 sids = 1 [(gogoproto.moretags) = 'form:"sids" validate:"required,min=1,max=100"'];
}

message TvSeasonCanPlayResp {
  // 剧集可播的牌照方
  map<int64, CanPlayList> can_play = 1;
}

message CanPlayList {
  // 枚举：["android_tv_yst", "android_tv_bst"]
  repeated string tenant = 1;
  // 可播牌照方ID，枚举：[0, 1]
  repeated int64 tenant_id = 2;
}

message TvVideosReply {
  map<int64,VideoInfo> list = 1;
}

message VideoInfo {
  // 分p id
  int64 cid = 1;
  // 分p标题
  string title = 2;
  // 所属稿件id
  int64 aid = 3;
  // 分p序号
  int32 index_order = 4;
  // 是否tv上架，0=下架 1=上架
  int32 valid = 5;
  // 是否tv删除，0=否 1=是
  int32 deleted = 6;
  // 牌照返回的审核结果 0=审核中 1=通过 2=不通过
  int32 result = 7;
  // 是否支持4k
  bool is_fourk = 8;
  // 首帧高清图地址
  string firsti = 9;
  // 是否可播
  bool can_play = 10;
  // 无mobi_app时，各牌照方可播情况，第0位yst,第1位bst
  int64 offline_can_play = 11;
  // 属性值，不可外露
  int64 attribute = 12;
  // 分P播放时长
  int32 duration = 13;
  // 创建时间
  int64 ctime = 14;
  // 修改时间
  int64 mtime = 15;
  // 牌照审核原因
  string reason = 16;
  // 4K超分
  bool is_fourk_sr = 17;
}

message TvVideosReq {
  repeated int64 cids = 1 [(gogoproto.moretags) = 'form:"cids" validate:"required,max=5000"'];
  string mobi_app = 2 [(gogoproto.moretags) = 'form:"mobi_app"'];
}

message TvArcsReq {
  repeated int64 aids = 1 [(gogoproto.moretags) = 'form:"aids" validate:"required,max=5000"'];
  // 是否包含分p数据
  bool with_videos = 2;
}

message TvArcsReply {
  map<int64,ArcInfo> list = 1;
}

message ArcInfo {
  // 稿件id
  int64 aid = 1;
  // 标题
  string title = 2;
  // 封面
  string cover = 3;
  // 发布时间
  int64 pubtime = 4;
  // 是否转载  1=原创  2=转载
  int32 copyright = 5;
  // 稿件状态，>=0为前台用户可见状态，其他值详见 http://info.bilibili.co/pages/viewpage.action?pageId=3686597#id-%E7%A8%BF%E4%BB%B6%E5%AD%97%E6%AE%B5%E6%9E%9A%E4%B8%BE%E5%80%BC%E5%B1%9E%E6%80%A7%E8%AF%B4%E6%98%8E-%E7%A8%BF%E4%BB%B6%E7%8A%B6%E6%80%81state&access
  int32 state = 6;
  // 稿件属性 详见State字段的文档（注意：该字段内部使用，请勿在对外接口中漏出！！）
  int32 attribute = 7;
  // 稿件总时长（所有分P加起来的） 单位=秒
  int64 duration = 8;
  // 稿件作者信息
  Author author = 9;
  // 稿件计数信息
  Stat stat = 10;
  // 稿件简介
  string desc = 11;
  // 分P信息, with_videos=true时有值
  repeated Page pages = 12;
  // 联合投稿信息，内部使用，详见StaffInfo说明
  repeated StaffInfo staff_info = 13;
  // 一级分区
  int32 type_id = 14;
  // 新版attibute属性，老业务的int32已经用完（注意：该字段内部使用，请勿在对外接口中漏出！！）
  int64 attribute_v2 = 15;
  // 第一个cid
  int64 first_cid = 16;
  // 稿件第一P的分辨率
  Dimension dimension = 17;
  // 封面
  string pic = 18;
  // 发布时间
  int64 pubdate = 19;
  // 一级分区 2.0
  int32 pid_v2 = 20;
}

message StaffInfo {
  // 联合投稿的成员Up主id
  int64 mid = 1;
  // 联合投稿的成员角色（如 声优、字幕）
  string title = 2;
  // 属性位（文档见https://info.bilibili.co/pages/viewpage.action?pageId=83773767）
  int64 attribute = 3;
}

message Page {
  // 视频的cid
  int64 cid = 1;
  // 视频的序号顺序，从小到大
  int32 page = 2;
  // 视频的来源，99%为vupload=B站自己的，少部分为腾讯（qq）、湖南tv(hunan)
  string from = 3;
  // 视频的标题
  string part = 4;
  // 视频的时长 单位=秒
  int64 duration = 5;
  // 视频的简介，多P视频才可填写简介
  string desc = 6;
  // 视频分辨率信息
  Dimension dimension = 7;
}

message Dimension {
  // 宽 如 1920
  int64 width = 1;
  // 高 如 1080
  int64 height = 2;
  // 是否翻转 0=否 1=是
  int64 rotate = 3;
}

message Stat {
  int32 view = 1;
}

message Author {
  // Up主mid
  int64 mid = 1;
  // Up主名称
  string name = 2;
  // Up主头像地址 绝对地址
  string face = 3;
}

message TvSimpleArcsReq {
  repeated int64 aids = 1 [(gogoproto.moretags) = 'form:"aids" validate:"required,max=5000"'];
}

message TvSimpleArcsReply {
  map<int64,SimpleArcInfo> list = 1;
}

message SimpleArcInfo {
  // 稿件id
  int64 aid = 1;
  // 稿件所有分p cid
  repeated int64 cids = 2;
  // 所属一级分区
  int32 type_id = 3;
  // 是否转载， 1=原创  2=转载 0=历史上可能遗留的脏数据
  int32 copyright = 4;
  // 稿件状态，>=0为前台用户可见状态，其他值详见 http://info.bilibili.co/pages/viewpage.action?pageId=3686597#id-%E7%A8%BF%E4%BB%B6%E5%AD%97%E6%AE%B5%E6%9E%9A%E4%B8%BE%E5%80%BC%E5%B1%9E%E6%80%A7%E8%AF%B4%E6%98%8E-%E7%A8%BF%E4%BB%B6%E7%8A%B6%E6%80%81state&access
  int32 state = 5;
  // 稿件访问属性 0=全员可见 10000=登录用户可见（极少)（注意：该字段内部使用，请勿在对外接口中漏出！！）
  int32 access = 6;
  // 稿件属性 详见State字段的文档（注意：该字段内部使用，请勿在对外接口中漏出！！）
  int32 attribute = 7;
  // 稿件总时长（所有分P加起来的） 单位=秒
  int64 duration = 8;
  // Up主id
  int64 mid = 9;
  // 新版attibute属性，老业务的int32已经用完（注意：该字段内部使用，请勿在对外接口中漏出！！）
  int32 attributeV2 = 10;
  // 发布时间戳
  int64 pubtime = 11;
  // 一级分区 2.0
  int32 pid_v2 = 12;
}

message TvArcMetasReq {
  repeated int64 aids = 1 [(gogoproto.moretags) = 'form:"aids" validate:"required,max=5000"'];
  string mobi_app = 2 [(gogoproto.moretags) = 'form:"mobi_app" default:"android_tv_yst"'];
}

message TvArcMetasReply {
  map<int64,ArcMetaInfo> list = 1;
}

message ArcMetaInfo {
  // 稿件id
  int64 aid = 1;
  // 稿件标题
  string title = 2;
  // 稿件描述
  string content = 3;
  // 稿件封面
  string cover = 4;
  // 所属一级分区
  int32 type_id = 5;
  // 发布时间戳
  int64 pubtime = 6;
  // 分p数
  int32 videos = 7;
  // 是否tv上架，0=下架 1=上架
  int32 valid = 8;
  // tv过审状态，0=审核中 1=通过 2=不通过
  int32 result = 9;
  // 是否tv本地删除
  int32 deleted = 10;
  // 灰度类型，或运算1|2|4|8|16|32：1全部可见,2禁止自动填充,4分区推荐不可见,8热门分区不可见,16搜索结果不可见,32动态结果不可见
  int32 gray_type = 11;
  // 是否支持4k
  bool is_fourk = 12;
  // 是否为dance稿件
  bool is_dance = 13;
  // 是否为仅收藏可见
  bool is_fav_view = 14;
  // 是否为8k
  bool is_eightk = 15;
  // 是否为六禁稿件
  bool is_flow = 16;
  // up主id
  int64 mid = 17;
  // 是否可播
  bool can_play = 18;
  // 无mobi_app时，各牌照方可播情况，第0位yst,第1位bst
  int64 offline_can_play = 19;
  // 属性值，不可外露
  int64 attribute = 20;
  // 稿件总时长（所有分P加起来的） 单位=秒
  int64 duration = 21;
  // 牌照审核原因
  string reason = 22;
  // Ctime
  int64 ctime = 23;
  // mtime
  int64 mtime = 24;
  // id
  int64 id = 25;
  // (疑似)主站上下架状态
  int32 state = 26;
  // 4K超分
  bool is_fourk_sr = 27;
  // 渠道租户属性 //https://info.bilibili.co/pages/viewpage.action?pageId=827611585
  uint64 tenant_attribute = 28;
  // 充电稿件
  bool isCharging = 29;
  // 短剧
  bool isPlaylet = 30;
  // 一级分区 2.0
  int32 pid_v2 = 31;
}

enum ForbidType {
  FEED = 0;     // 推荐
  POPULAR = 1;  // 热门
  SEARCH = 2;   // 搜索
  FAV = 3;      // 常看up主
  AI = 4;       // 自动填充
  Family = 5;  // 家庭模式
  WhiteShow = 6; // up露出白名单
  WhiteWatermark = 7; // 水印白名单
}

message TvUppersReq {
  // 用户id
  repeated int64 mids = 1 [(gogoproto.moretags) = 'form:"mids" validate:"required"'];
  // 是否包含tv干预信息
  bool has_meta = 2 [(gogoproto.moretags) = 'form:"has_meta"'];
  // 是否包含账号服务Card3数据
  bool has_official = 3 [(gogoproto.moretags) = 'form:"has_official"'];
}

message TvUppersReply {
  map<int64,UpperInfo> list = 1;
}

message UpperInfo {
  // 用户mid
  int64 mid = 1;
  // db数据
  UpMetaInfo meta_info = 2;
  // card3数据
  UpOfficial official = 3;
}

message UpOfficial {
  // 名称
  string name = 1;
  // 头像
  string face = 2;
  // 官方认证相关参数
  int32 role = 3;
  string title = 4;
  string desc = 5;
  int32 type = 6;
}

message LevelInfo {
  int32 cur = 1;
  int32 min = 2;
  int32 now_exp = 3;
  int32 next_exp = 4;
  int64 level_up = 5;
}


message UpMetaInfo {
  // 原始名称
  string ori_name = 1;
  // 干预名称
  string cms_name = 2;
  // 原始头像
  string ori_face = 3;
  // 干预头像
  string cms_face = 4;
  // 是否可露出
  bool can_show = 5;
  // 属性
  int32 attribute = 6;
  // 别名
  string alias = 7;
  // 是否免内审 0=非免内审，1=免内审
  int32 aegis_free = 8;// 是否免内审
  // 是否冻结 1=冻结 0=非冻结
  int32 freeze = 9;
}

message TvClassSeasonMetasReq {
  // 课堂id
  repeated int64 sids = 1 [(gogoproto.moretags) = 'form:"sids" validate:"required,max=5000"'];
  // 平台（暂可不填，目前默认且支持云视听）
  string mobi_app = 2 [(gogoproto.moretags) = 'form:"mobi_app" default:"android_tv_yst"'];;
}

message TvClassSeasonMetasResp {
  map<int64,ClassSnMetaInfo> list = 1;
}

message ClassSnMetaInfo {
  int64 season_id = 1;
  /** 鉴权相关 **/
  // 上下架
  int32 valid = 2;
  // 审核状态, 0=拒绝 1=通过 2=等待重新提审 3=重新提审中 4=默认待审核
  int32 check_status = 3;
  // 反映入参mobi_app牌照的可播状态
  bool can_play = 4;
  // 无mobi_app时，各牌照方可播情况，第0位yst,第1位bst
  int64 offline_can_play = 5;
  // ogv侧提审状态
  int32 ogv_status = 6;
  /** 内容相关 **/
  // 标题
  string title = 7;
  // 简介
  string description = 8;
  // 更新信息（长文案，带更新状态）
  string update_info = 9;
  // 更新信息（短文案，不带更新状态）
  string update_info1 = 10;
  // 更新信息（长文案，不带更新状态）
  string update_info2 = 11;
  // 竖图
  string cover = 12;
  // 横图
  string horizontal_cover = 13;
  // 总集数
  int64 total_num = 14;
  // 副标题
  string subtitle = 15;
  // up主id
  int64 up_id = 16;
  // 直播期数
  int64 live_ep_count = 17;
  // 有效期天数, 默认值0长期有效
  int64 expiry_day = 18;
  // 有效期统一到期时间
  int64 expire_at = 19;
  // 讲师信息
  string lecturer_info = 20;
  // 付费状态
  int32 pay_status = 21;
  // 是否完结； 1=更新中，2=已完结
  int32 state = 22;
}


message TvClassEpisodeMetasReq {
  // 课堂id
  repeated int64 epids = 1 [(gogoproto.moretags) = 'form:"epids" validate:"required,max=5000"'];
  // 平台（暂可不填，目前默认且支持云视听）
  string mobi_app = 2 [(gogoproto.moretags) = 'form:"mobi_app" default:"android_tv_yst"'];;
}

message TvClassEpisodeMetasResp {
  map<int64,ClassEpMetaInfo> list = 1;
}

message ClassEpMetaInfo {
  int64 epid = 1;
  int64 aid = 2;
  int64 cid = 3;
  int64 season_id = 4;
  /** 鉴权**/
  // 上下架
  int32 valid = 5;
  // 牌照审核状态 0=拒绝 1=通过 2=等待重新提审 3=重新提审中 4=默认待审核
  int32 check_status = 6;
  // 反映入参mobi_app牌照的可播状态
  bool can_play = 7;
  // 无mobi_app时，各牌照方可播情况，第0位yst,第1位bst
  int64 offline_can_play = 8;
  /** 基本信息 **/
  // 标题
  string title = 9;
  // 副标题
  string subtitle = 10;
  // 简介
  string description = 11;
  // 时长
  int64 duration = 12;
  // 封面
  string cover = 13;
  // 支付状态： 1=可试看， 2=不可试看 3=5分钟试看
  int32 pay_status = 14;
  // 编号
  int64 num = 15;
  // 批次ID
  int64 batch_id = 16;
  // 第几集
  string no_view = 17;
  // up主ID
  int64 up_id = 18;
  // ep类型 -1:ogv接口报错默认值, 0:正片, 1:预告片, 2:其他
  int32 ep_type = 19;
  // 属性
  int32 attribute = 20;
}

message ExternalSeasonMetaReq{
  // 批量的sid最多100
  repeated int64 sids = 1  [(gogoproto.moretags) = 'form:"sids" validate:"required,max=100"'];
  // 多租户apk，南传
  string mobi_app = 2  [(gogoproto.moretags) = 'form:"mobi_app" validate:"required"'];
}

message ExternalEpisodeMetaReq{
  // 批量的epid最多100
  repeated int64 epids = 1  [(gogoproto.moretags) = 'form:"epids" validate:"max=100"'];
  // 多租户apk，南传
  string mobi_app = 2  [(gogoproto.moretags) = 'form:"mobi_app"'];
  // 批量的epid最多100
  repeated int64 aids = 3  [(gogoproto.moretags) = 'form:"aids" validate:"max=100"'];
}

message ExternalSeasonMetaReply{
  // key 是season_id
  map<int64,ExternalSeasonMeta> list = 1;
}
message ExternalSeasonMeta{
  // 自增ID
  int64 id = 1;
  // 剧集ID
  int64 season_id = 2;
  // 媒资ID,内部使用
  int64 media_id = 3;
  // season类型
  int32 season_type = 4;
  // 外部媒资id
  string external_sid = 5;
  // 媒资提供方:snm
  string provider = 6;
  // 标题
  string title = 7;
  // 副标题
  string subtitle = 8;
  // 季度短标题
  string season_title = 9;
  // 9字副标题
  string nine_sub_title = 10;
  // 14字副标题
  string fourteen_sub_title = 11;
  // 25字副标题
  string twenty_five_sub_title = 12;
  // 封面640 * 480
  string season_cover = 13;
  // 16:9封面
  string img_rel_nine = 14;
  // 16:10封面
  string img_rel_ten = 15;
  // 别名
  string alias = 16;
  // 长描述，简介
  string season_evaluate = 17;
  // 演员/声优
  string actors = 18;
  // 制作方属性
  string staff = 19;
  // 上下架状态
  int32 season_status = 20;
  // season付费状态
  int32 pay_status = 21;
  // 剧集所属地区名
  string area = 22;
  // 总集数
  int32 episode_count = 23;
  // 是否完结
  int32 is_finish = 24;
  // 上映日期 yyyy-MM-dd
  string pdate = 25;
  // 开播时间 hh:mm:ss
  string ptime = 26;
  // 上映年份
  string pyear = 27;
  // 上映月份
  string pmonth = 28;
  // 更新周期 e.g. "每日10:00更新1集"
  string renewal_time = 29;
  // 周几开播 e.g. 7
  int32 pdayofweek = 30;
  // 第一集正片的上线日期
  string first_positive_ep_pub_time =  31;
  // 完结时间
  string finish_time = 32;
  // 完结时间
  string season_tag = 33;
  // 评分(100分制)
  int32 score = 34;
  // 风格
  string style = 35;
  // 区分：独家/版权/其他
  string copyright = 36;
  // 当前更新至第几话
  int64 season_online_ep_cnt = 37;
  // 第一个视频上线时间（可能是花絮，不一定是正片)
  string first_ep_pub_date = 38;
  // 最新一集正片的上线日期（注意，不一定是完结日期）
  string last_ep_pub_date = 39;
  // 搜索关键词
  string search_keywords = 40;
  // 剧集适用观看人群
  int64 crowd_type = 41;
  // 不允许观看地区
  string black_area = 42;
  // 允许观看地区
  string white_area = 43;
  // 语言
  string language = 44;
  // 版权开始时间
  string copyright_start_time = 45;
  // 版权结束时间
  string copyright_end_time = 46;
  // 最新的epid
  int64 new_epid = 47;
}

message ExternalEpisodeMetaReply{
  // key 是ep_id
  map<int64,ExternalEpisodeMeta> list = 1;
  // key 是aid
  map<int64,ExternalEpisodeMeta> aid_list = 2;
}
message ExternalEpisodeMeta{
  // 自增ID
  int64 id = 1;
  // epid
  int64 epid= 2;
  // 剧集ID
  int64 season_id = 3;
  // avid
  int64 aid = 4;
  // 视频ID
  int64 cid = 5;
  // 外部的epid
  string external_epid = 6;
  // 外部的epid
  string external_sid = 7;
  // 媒资提供方
  string provider = 8;
  // ep标题
  string ep_title = 9;
  // ep短标题
  string title = 10;
  // ep长标题
  string long_title = 11;
  // ep上下架状态
  int32 ep_status = 12;
  // ep付费状态
  int32 pay_status = 13;
  //预定上线时间
  string pubtime = 14;
  // ep本身的视频时长
  int64 duration = 15;
  // 顺序
  int32 ord = 16;
  // ep集数
  int32 ep_num = 17;
  // 不允许观看地区
  string black_area = 18;
  // 允许观看地区
  string white_area = 19;
  // ep封面
  string ep_cover = 20;
  // 每个ep的tag信息
  string ep_tag = 21;
  // 横竖版
  string horizontal_vertical_ver = 22;
  // 清晰度
  string video_qualities = 23;
  // 分节类型
  int32 section_type = 24;
  // 片头时间, e.g. [10, 40] 表示片头从第10s到第40s
  repeated int64 op_time = 25;
  // 片尾时间, e.g. [1000, 1020] 表示片尾从第1000s到第1020s
  repeated int64 ed_time = 26;
  // 水印遮标的坐标
  WatermarkPosition watermark_position = 27;
  // 视频画面比例 宽:高
  Dimension dimension = 28;
}

message WatermarkPosition {
  // 水印宽度占视频宽度的百分比（取值0.0<x<100.0）
  float width_percent = 1;
  // 水印高占视频高度的百分比（取值0.0<x<100.0）
  float height_percent = 2;
  // 水印右边距离视频右边的间距和视频宽度的占比（取值0.0<x<100.0）
  float right_margin_percent = 3;
  // 水印上边距离视频上边的间距和视频高度的占比（取值0.0<x<100.0）
  float top_margin_percent = 4;
}