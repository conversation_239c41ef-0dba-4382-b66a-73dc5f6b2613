syntax = "proto3";
package garb.service.model;
option go_package = "buf.bilibili.co/bapis/bapis-gen/garb/model;model";
option java_package = "com.bapis.garb.model";
option java_multiple_files = true;

import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "vas/common/collection.proto";

option (gogoproto.goproto_getters_all) = true;

message UserAssetHistory {
    int64 itemID = 1 [(gogoproto.jsontag) = "item_id", json_name = "item_id"];
    string image = 2 [(gogoproto.jsontag) = "image", json_name = "image"];
    string name = 3 [(gogoproto.jsontag) = "name", json_name = "name"];
    int64 buyTime = 4 [(gogoproto.jsontag) = "buy_time", json_name = "buy_time"];
    string payID = 5 [(gogoproto.jsontag) = "pay_id", json_name = "pay_id"];
    string cost = 6 [(gogoproto.jsontag) = "cost", json_name = "cost"];
    int64 timeLength = 7 [(gogoproto.jsontag) = "time_length", json_name = "time_length"];
}

message UserEquip {
    Item item = 1 [(gogoproto.jsontag) = "item", json_name = "item"];
    int64 index = 2 [(gogoproto.jsontag) = "index", json_name = "index"];
    int64 isDiy = 3 [(gogoproto.jsontag) = "is_diy", json_name = "is_diy"];
}

message UserAsset {
    int64 id = 1 [(gogoproto.jsontag) = "id", json_name = "id"];
    int64 expiredTime = 2 [(gogoproto.jsontag) = "expired_time", json_name = "expired_time"];
    string state = 3 [(gogoproto.jsontag) = "state", json_name = "state"];
    Item item = 4 [(gogoproto.jsontag) = "item", json_name = "item"];
    Grant grant = 5 [(gogoproto.jsontag) = "grant", json_name = "grant"];
    UserFanBase fan = 6 [(gogoproto.jsontag) = "fan", json_name = "fan"];
    int64 ownNum = 7 [(gogoproto.jsontag) = "own_num", json_name = "own_num"];
    int64 isDiy = 8 [(gogoproto.jsontag) = "is_diy", json_name = "is_diy"];
    bool isTrial = 9 [(gogoproto.jsontag) = "is_trial", json_name = "is_trial"];
    // 拓展字段
    map<string, string> extMap = 10 [(gogoproto.jsontag) = "ext_map", json_name = "ext_map"];
    // 评论背景信息
    CardBg card_bg = 11 [(gogoproto.jsontag) = "card_bg", json_name = "card_bg"];
}

message CardBg {
    int64 act_id = 1 [(gogoproto.jsontag) = "act_id", json_name = "act_id"];
    int64 level = 2 [(gogoproto.jsontag) = "level", json_name = "level"];
    int64 bg_no = 3 [(gogoproto.jsontag) = "bg_no", json_name = "bg_no"];
    string color = 4 [(gogoproto.jsontag) = "color", json_name = "color"];
    FanNumColorFormat color_format = 5 [(gogoproto.jsontag) = "color_format", json_name = "color_format"];
    string bg_no_prefix = 6 [(gogoproto.jsontag) = "bg_no_prefix", json_name = "bg_no_prefix"];
}

message UserAssetBase {
    int64 id = 1;
    int64 mid = 2;
    int64 item_id = 3;
    int64 expired_time = 4;
    string state = 5;
    int64 part_id = 6;
    int64 is_diy = 7;
    Item item = 8;
}

message UserAssetPreview {
    int64 item_id = 1;
    int64 is_diy = 2;
    string state = 3;
    string image_preview = 4;
    string image_avatar = 5;
    // 是否使用套装
    bool is_trial = 6;
    bool is_equip = 7;
    int64 asset_id = 8;
}

message UserPartPreview {
    int64 part_id = 1;
    int64 part_total = 2;
    int64 fan_suit_total = 3;
    int64 base_suit_total = 4;
    int64 trial_suit_total = 5;
    repeated UserAssetPreview list = 6;
    bool has_new = 7;
    // 是否拥有内容
    bool has_item = 8;
}

message UserAllAssetList {
    repeated UserAsset list = 1;
}

message UserFan {
    int64 number = 1 [(gogoproto.jsontag) = "number", json_name = "number"];             // 粉丝编号
    Item luckItem = 2 [(gogoproto.jsontag) = "luck_item", json_name = "luck_item"];      // 锦鲤装备
    string cardImage = 3 [(gogoproto.jsontag) = "card_image", json_name = "card_image"]; // 动态卡片URL
    int64 createTS = 4 [(gogoproto.jsontag) = "create_ts", json_name = "create_ts"];     // 获得时间戳
    int64 mid = 5 [(gogoproto.jsontag) = "mid", json_name = "mid"];
    string state = 7 [(gogoproto.jsontag) = "state", json_name = "state"];
    int64 buyMid = 8 [(gogoproto.jsontag) = "buy_mid", json_name = "buy_mid"];
    int64 PresentTokenID = 9 [(gogoproto.jsontag) = "present_token_id", json_name = "present_token_id"];
    int64 GetTime = 10 [(gogoproto.jsontag) = "get_time", json_name = "get_time"];
    string source = 11 [(gogoproto.jsontag) = "source", json_name = "source"];
    string fan_bg_color = 12 [(gogoproto.jsontag) = "fan_bg_color", json_name = "fan_bg_color"];
}

message UserFanRank {
    repeated UserFan rank = 1 [(gogoproto.jsontag) = "rank", json_name = "rank"]; // 粉丝排行
}

message UserWallet {
    double bpBalance = 2 [(gogoproto.jsontag) = "bcoin_balance", json_name = "bcoin_balance"];       // B币余额
    double couponBalance = 3 [(gogoproto.jsontag) = "coupon_balance", json_name = "coupon_balance"]; // 剩余B币券金额
}

message UserSetting {
    int64 id = 1 [(gogoproto.jsontag) = "id", json_name = "id"];
    int64 mid = 2 [(gogoproto.jsontag) = "mid", json_name = "mid"];
    string type = 3 [(gogoproto.jsontag) = "type", json_name = "type"];
    string value = 4 [(gogoproto.jsontag) = "value", json_name = "value"];
    int64 ctime = 5 [(gogoproto.jsontag) = "ctime", json_name = "ctime"];
    int64 mtime = 6 [(gogoproto.jsontag) = "mtime", json_name = "mtime"];
}

message Item {
    int64 id = 1 [(gogoproto.jsontag) = "id", json_name = "id"];
    int64 groupID = 2 [(gogoproto.jsontag) = "group_id", json_name = "group_id"];
    string name = 3 [(gogoproto.jsontag) = "name", json_name = "name"];
    int64 order = 4 [(gogoproto.jsontag) = "order", json_name = "order"];
    string state = 5 [(gogoproto.jsontag) = "state", json_name = "state"];
    int64 oid = 6 [(gogoproto.jsontag) = "oid", json_name = "oid"];
    int64 partID = 7 [(gogoproto.jsontag) = "part_id", json_name = "part_id"];
    int64 suitItemID = 8 [(gogoproto.jsontag) = "suit_item_id", json_name = "suit_item_id"];
    repeated Property properties = 9 [(gogoproto.jsontag) = "properties", json_name = "properties"];
    repeated Activity activities = 10 [(gogoproto.jsontag) = "activities", json_name = "activities"];
    repeated Source sources = 11 [(gogoproto.jsontag) = "sources", json_name = "sources"];
    int64 MTime = 12 [(gogoproto.jsontag) = "-", (gogoproto.casttype) = "go-common/library/time.Time"];
    int64 is_digital = 13;
    repeated Activity next_activities = 14 [(gogoproto.jsontag) = "next_activities", json_name = "next_activities"];
    CardBg card_bg = 15 [(gogoproto.jsontag) = "card_bg", json_name = "card_bg"];
    int64 CTime = 16 [(gogoproto.jsontag) = "-", (gogoproto.casttype) = "go-common/library/time.Time"];
    int64 bind_type = 17 [(gogoproto.jsontag) = "bind_type", json_name = "bind_type"];
}

message Property {
    string Type = 1 [(gogoproto.jsontag) = "type", json_name = "type"];
    string Value = 2 [(gogoproto.jsontag) = "value", json_name = "value"];
}

message Activity {
    int64 id = 1 [(gogoproto.jsontag) = "id", json_name = "id"];
    int64 item_id = 2 [(gogoproto.jsontag) = "item_id", json_name = "item_id"];
    string type = 3 [(gogoproto.jsontag) = "type", json_name = "type"];
    bool time_limit = 4 [(gogoproto.jsontag) = "time_limit", json_name = "time_limit"];
    int64 time_from = 5 [(gogoproto.jsontag) = "time_from", json_name = "time_from"];
    int64 time_to = 6 [(gogoproto.jsontag) = "time_to", json_name = "time_to"];
    int64 price_bp_month = 7 [(gogoproto.jsontag) = "price_bp_month", json_name = "price_bp_month"];
    int64 price_bp_forever = 8 [(gogoproto.jsontag) = "price_bp_forever", json_name = "price_bp_forever"];
    string tag = 9 [(gogoproto.jsontag) = "tag", json_name = "tag"];
    int64 freeDuration = 10 [(gogoproto.jsontag) = "free_duration", json_name = "free_duration"];
    int64 admin_id = 11 [(gogoproto.jsontag) = "admin_id", json_name = "admin_id"];
    string admin_name = 12 [(gogoproto.jsontag) = "admin_name", json_name = "admin_name"];
    string state = 13 [(gogoproto.jsontag) = "state", json_name = "state"];
    int64 ctime = 14 [(gogoproto.jsontag) = "ctime", json_name = "ctime"];
    string batch_token = 15 [(gogoproto.jsontag) = "batch_token", json_name = "batch_token"];
    // 渠道类型
    string channel_type = 16 [(gogoproto.jsontag) = "channel_type", json_name = "channel_type"];
    // 渠道值
    string channel_value = 17 [(gogoproto.jsontag) = "channel_value", json_name = "channel_value"];
    // 普通套装折扣
    ActivitySku month = 18 [(gogoproto.jsontag) = "month", json_name = "month"];
}

message ActivitySku {
    // 普通套装折扣类型
    string type = 1 [(gogoproto.jsontag) = "type", json_name = "type"];
    // 普通套装折扣标签
    string tag = 2 [(gogoproto.jsontag) = "tag", json_name = "tag"];
    int64 time_from = 3 [(gogoproto.jsontag) = "time_from", json_name = "time_from"];
    int64 time_to = 4 [(gogoproto.jsontag) = "time_to", json_name = "time_to"];
    int64 price = 5 [(gogoproto.jsontag) = "price", json_name = "price"];
    int64 sku_id = 6 [(gogoproto.jsontag) = "sku_id", json_name = "sku_id"];
}

message Grant {
    string id = 1 [(gogoproto.jsontag) = "grant_id", json_name = "grant_id"];
    repeated Property properties = 2 [(gogoproto.jsontag) = "properties", json_name = "properties"];
}

message Source {
    int64 id = 1 [(gogoproto.jsontag) = "id", json_name = "id"];
    string name = 2 [(gogoproto.jsontag) = "name", json_name = "name"];
    int64 time_from = 3 [(gogoproto.jsontag) = "time_from", json_name = "time_from"];
    int64 time_to = 4 [(gogoproto.jsontag) = "time_to", json_name = "time_to"];
    string desc = 5 [(gogoproto.jsontag) = "desc", json_name = "desc"];
    string jump_url = 6 [(gogoproto.jsontag) = "jump_url", json_name = "jump_url"];
    int64 source_type = 7 [(gogoproto.jsontag) = "source_type", json_name = "source_type"];
}

message UserFanShow {
    // 是否是粉丝套装 1是 0不是
    int64 isFan = 1 [(gogoproto.jsontag) = "is_fan", json_name = "is_fan"];
    // 粉丝编号，eg: 1234
    int64 number = 2 [(gogoproto.jsontag) = "number", json_name = "number"];
    // 编号颜色，eg: #BFC8D2
    string color = 3 [(gogoproto.jsontag) = "color", json_name = "color"];
    // 编号展示描述，eg: 洛天依十周年、洛天依收藏集
    string name = 4 [(gogoproto.jsontag) = "name", json_name = "name"];
    // 编号展示格式，通常为编号补0，eg: 001234
    string num_desc = 5 [(gogoproto.jsontag) = "num_desc", json_name = "num_desc"];
    // 编号的前缀，eg: "CD."、"NO."
    string num_prefix = 6 [(gogoproto.jsontag) = "num_prefix", json_name = "num_prefix"];
    // 编号颜色格式
    FanNumColorFormat color_format = 7 [(gogoproto.jsontag) = "color_format", json_name = "color_format"];
}

// FanNumColorFormat 粉丝编号颜色格式
message FanNumColorFormat {
    // 颜色渐变起始点-xy比例相对坐标，取值范围0-100，eg: 0,100
    string start_point = 1 [(gogoproto.jsontag) = "start_point", json_name = "start_point"];
    // 颜色渐变结束点-xy比例相对坐标，取值范围0-100，eg: 0,100
    string end_point = 2 [(gogoproto.jsontag) = "end_point", json_name = "end_point"];
    // 颜色渐变色值集合，RGBA格式，长度和gradients保持一致，eg: ["rgba(255,0,0,0)", "rgba(255,255,0,0)", "rgba(255,255,255,0)"]，
    repeated string colors = 3 [(gogoproto.jsontag) = "colors", json_name = "colors"];
    // 颜色渐变比例集合，取值范围0-100，eg: [0, 20, 50, 100]
    repeated int64 gradients = 4 [(gogoproto.jsontag) = "gradients", json_name = "gradients"];
}

message UserFanBase {
    bool isFan = 1 [(gogoproto.jsontag) = "is_fan", json_name = "is_fan"];
    int64 number = 2 [(gogoproto.jsontag) = "number", json_name = "number"];
    string token = 3 [(gogoproto.jsontag) = "token", json_name = "token"];
    int64 luckItemID = 4 [(gogoproto.jsontag) = "luck_item_id", json_name = "luck_item_id"];
    int64 mid = 5 [(gogoproto.jsontag) = "mid", json_name = "mid"];
    string name = 6 [(gogoproto.jsontag) = "name", json_name = "name"];
    string color = 7 [(gogoproto.jsontag) = "color", json_name = "color"];
    string date = 8 [(gogoproto.jsontag) = "date", json_name = "date"];
}

message UserPendant {
    int64 id = 1 [(gogoproto.jsontag) = "id", json_name = "id"];
    string name = 2 [(gogoproto.jsontag) = "name", json_name = "name"];
    string image = 3 [(gogoproto.jsontag) = "image", json_name = "image"];
    string jumpURL = 4 [(gogoproto.jsontag) = "jump_url", json_name = "jump_url"];
    string type = 5 [(gogoproto.jsontag) = "type", json_name = "type"];
    string image_enhance = 6 [(gogoproto.jsontag) = "image_enhance", json_name = "image_enhance"];
    string image_enhance_frame = 7 [(gogoproto.jsontag) = "image_enhance_frame", json_name = "image_enhance_frame"];
}

message UserCardBG {
    int64 id = 1 [(gogoproto.jsontag) = "id", json_name = "id"];
    string name = 2 [(gogoproto.jsontag) = "name", json_name = "name"];
    string image = 3 [(gogoproto.jsontag) = "image", json_name = "image"];
    string jumpURL = 4 [(gogoproto.jsontag) = "jump_url", json_name = "jump_url"];
    UserFanShow fan = 5 [(gogoproto.nullable) = false, (gogoproto.jsontag) = "fan", json_name = "fan"];
    string type = 6 [(gogoproto.jsontag) = "type", json_name = "type"];
    // 图片增强
    ImageGroup image_group = 7 [(gogoproto.jsontag) = 'image_group', json_name = 'image_group'];
}

message ImageGroup {
    // 视觉效果类型，0 无、1 本次需求定义的摇晃打光动效
    int64 type = 1 [(gogoproto.jsontag) = 'type', json_name = 'type'];
    // 视觉动效
    VisualEffect effect_visual = 2 [(gogoproto.jsontag) = 'effect_visual', json_name = 'effect_visual'];
    message VisualEffect {
        // 勋章图
        string medal_image = 1 [(gogoproto.jsontag) = 'medal_image', json_name = 'medal_image'];
        // 主题色系，用于渲染动态卡片背景
        string color_theme = 2 [(gogoproto.jsontag) = 'color_theme', json_name = 'color_theme'];
    }
}

message UserSailing {
    UserPendant pendant = 1 [(gogoproto.jsontag) = "pendant", json_name = "pendant"];
    UserCardBG cardBG = 2 [(gogoproto.jsontag) = "cardbg", json_name = "cardbg"];
    UserCardBG cardBGWithFocus = 3 [(gogoproto.jsontag) = "cardbg_with_focus", json_name = "cardbg_with_focus"];
}

message UserSkin {
    int64 id = 1 [(gogoproto.jsontag) = "id", json_name = "id", (gogoproto.customname) = "ID"];
    string name = 2 [(gogoproto.jsontag) = "name", json_name = "name"];
    string preview = 3 [(gogoproto.jsontag) = "preview", json_name = "preview"];
    int64 ver = 4 [(gogoproto.jsontag) = "ver", json_name = "ver"];
    string package_url = 5 [(gogoproto.jsontag) = "package_url", json_name = "package_url"];
    string package_md5 = 6 [(gogoproto.jsontag) = "package_md5", json_name = "package_md5"];
    UserSkinData data = 7 [(gogoproto.jsontag) = "data", json_name = "data"];
}

message UserSkinData {
    string colorMode = 1 [(gogoproto.jsontag) = "color_mode", json_name = "color_mode"];
    string color = 2 [(gogoproto.jsontag) = "color", json_name = "color"];
    string colorSecondPage = 3 [(gogoproto.jsontag) = "color_second_page", json_name = "color_second_page"];
    string sideBGColor = 4 [(gogoproto.jsontag) = "side_bg_color", json_name = "side_bg_color"];
    string tailColor = 5 [(gogoproto.jsontag) = "tail_color", json_name = "tail_color"];
    string tailColorSelected = 6 [(gogoproto.jsontag) = "tail_color_selected", json_name = "tail_color_selected"];
    bool tailIconAni = 7 [(gogoproto.jsontag) = "tail_icon_ani", json_name = "tail_icon_ani"];
    string tailIconAniMode = 8 [(gogoproto.jsontag) = "tail_icon_ani_mode", json_name = "tail_icon_ani_mode"];
    string headMyselfMp4Play = 9 [(gogoproto.jsontag) = "head_myself_mp4_play", json_name = "head_myself_mp4_play"];
    string pub_btn_shade_color_top = 10 [json_name = "pub_btn_shade_color_top"];
    string pub_btn_shade_color_bottom = 11 [json_name = "pub_btn_shade_color_bottom"];
    string pub_btn_plus_color = 12 [json_name = "pub_btn_plus_color"];
    string pub_btn_bg_start = 13 [json_name = "pub_btn_bg_start"];                           // 已弃用
    string pub_btn_bg_end = 14 [json_name = "pub_btn_bg_end"];                               // 已弃用
    string tail_icon_mode = 15 [json_name = "tail_icon_mode"];                               // 底栏icon类型  img/color
    string tail_icon_color = 16 [json_name = "tail_icon_color"];                             // 底栏icon色值 非选中 日间模式
    string tail_icon_color_selected = 17 [json_name = "tail_icon_color_selected"];           // 底栏icon色值 选中 日间模式
    string tail_icon_color_dark = 18 [json_name = "tail_icon_color_dark"];                   // 底栏icon色值 非选中 夜间模式
    string tail_icon_color_selected_dark = 19 [json_name = "tail_icon_color_selected_dark"]; // 底栏icon色值 选中 夜间模式
}

message SkinColor {
    int64 id = 1 [(gogoproto.jsontag) = "id", json_name = "id", (gogoproto.customname) = "ID"];
    string name = 2 [(gogoproto.jsontag) = "name", json_name = "name"];
    bool isFree = 3 [(gogoproto.jsontag) = "is_free", json_name = "is_free"];
    int64 price = 4 [(gogoproto.jsontag) = "price", json_name = "price"];
    bool isBought = 5 [(gogoproto.jsontag) = "is_bought", json_name = "is_bought"];
    int64 status = 6 [(gogoproto.jsontag) = "status", json_name = "status"];
    int64 buyTime = 7 [(gogoproto.jsontag) = "buy_time", json_name = "buy_time"];
    int64 dueTime = 8 [(gogoproto.jsontag) = "due_time", json_name = "due_time"];
    string color_name = 9 [(gogoproto.jsontag) = "color_name", json_name = "color_name"];
    bool is_overdue = 10 [(gogoproto.jsontag) = "is_overdue", json_name = "is_overdue"];
}

message ItemResource {
    int64 item_id = 1 [(gogoproto.jsontag) = "id", json_name = "id"];
    int64 total = 2 [(gogoproto.jsontag) = "total", json_name = "total"];
    int64 surplus = 3 [(gogoproto.jsontag) = "surplus", json_name = "surplus"];
    int64 sale = 4 [(gogoproto.jsontag) = "sale", json_name = "sale"];
}

message UserReserveQuery {
    int64 total = 1 [(gogoproto.jsontag) = "total", json_name = "total"];
    bool isReserve = 2 [(gogoproto.jsontag) = "is_reserve", json_name = "is_reserve"];
    bool ReserveState = 3 [(gogoproto.jsontag) = "reserve_state", json_name = "reserve_state"];
}

message UserLoading {
    int64 id = 1 [(gogoproto.jsontag) = "id", json_name = "id", (gogoproto.customname) = "ID"];
    string name = 2 [(gogoproto.jsontag) = "name", json_name = "name"];
    int64 ver = 3 [(gogoproto.jsontag) = "ver", json_name = "ver"];
    string loading_url = 4 [(gogoproto.jsontag) = "loading_url", json_name = "loading_url"];
}

message ItemUnlock {
    int64 num = 1 [(gogoproto.jsontag) = "num", json_name = "num"];
    int64 suit_id = 2 [(gogoproto.jsontag) = "suit_id", json_name = "suit_id"];
    int64 item_id = 3 [(gogoproto.jsontag) = "item_id", json_name = "item_id"];
    string state = 4 [(gogoproto.jsontag) = "state", json_name = "state"];
    int64 idx = 5 [(gogoproto.jsontag) = "idx", json_name = "idx"];
    int64 utype = 6 [(gogoproto.jsontag) = "utype", json_name = "utype"];
}

message ItemUnlockList {
    repeated ItemUnlock list = 1 [(gogoproto.jsontag) = "list", json_name = "list"];
}

message UserRegion {
    bool RegionState = 1 [(gogoproto.jsontag) = "region_state", json_name = "region_state"];
}

message UserInvestor {
    int64 mid = 1 [(gogoproto.jsontag) = "mid", json_name = "mid"];
    int64 buy_num = 2 [(gogoproto.jsontag) = "buy_num", json_name = "buy_num"];
    int64 last_buy_time = 3 [(gogoproto.jsontag) = "last_buy_time", json_name = "last_buy_time"];
}

message UserInvestorRank {
    repeated UserInvestor rank = 1 [(gogoproto.jsontag) = "rank", json_name = "rank"]; // 套装投资人排行榜
}

message UserMultbuy {
    int64 buy_num = 1 [(gogoproto.jsontag) = "buy_num", json_name = "buy_num"];
    int64 sale_buy_num_limit = 2 [(gogoproto.jsontag) = "sale_buy_num_limit", json_name = "sale_buy_num_limit"];
    int64 own_num = 3 [(gogoproto.jsontag) = "own_num", json_name = "own_num"];
    ActivityReceiveInfo activity = 4 [(gogoproto.jsontag) = "activity", json_name = "activity"];
}

message UserFanNumlist {
    repeated UserFan list = 1 [(gogoproto.jsontag) = "list", json_name = "list"];
}

message UserFanPresentToken {
    int64 id = 1 [(gogoproto.jsontag) = "id", json_name = "id", (gogoproto.customname) = "ID"];
    int64 mid = 2 [(gogoproto.jsontag) = "mid", json_name = "mid", (gogoproto.customname) = "MID"];
    string message = 3 [(gogoproto.jsontag) = "message", json_name = "message"];
    string present_token = 4 [(gogoproto.jsontag) = "present_token", json_name = "present_token"];
    int64 present_expire = 5 [(gogoproto.jsontag) = "present_expire", json_name = "present_expire"];
    int64 total = 6 [(gogoproto.jsontag) = "total", json_name = "total"];
    int64 surplus_count = 7 [(gogoproto.jsontag) = "surplus_count", json_name = "surplus_count"];
    string state = 8 [(gogoproto.jsontag) = "state", json_name = "state"];
    int64 ctime = 9 [(gogoproto.jsontag) = "ctime", json_name = "ctime"];
    int64 mtime = 10 [(gogoproto.jsontag) = "mtime", json_name = "mtime"];
    int64 to_mid = 11 [(gogoproto.jsontag) = "to_mid", json_name = "to_mid"];
}

message UserFanNumPresentResult {
    int64 presentMID = 1 [(gogoproto.jsontag) = "present_mid", json_name = "present_mid", (gogoproto.customname) = "PresentMID"]; // 赠送人mid
    int64 getMID = 2 [(gogoproto.jsontag) = "get_mid", json_name = "get_mid", (gogoproto.customname) = "GetMID"];                 // 被赠送人mid
    string message = 3 [(gogoproto.jsontag) = "message", json_name = "message"];
    int64 fanNum = 4 [(gogoproto.jsontag) = "fan_num", json_name = "fan_num"];
    int64 getTime = 5 [(gogoproto.jsontag) = "get_time", json_name = "get_time"];
}

message Banner {
    int64 id = 1 [(gogoproto.jsontag) = "id", json_name = "id", (gogoproto.customname) = "ID"];
    string title = 2 [(gogoproto.jsontag) = "title", json_name = "title"];
    string image_cover = 3 [(gogoproto.jsontag) = "image_cover", json_name = "image_cover"];
    string url = 4 [(gogoproto.jsontag) = "url", json_name = "url"];
    string image_bg = 5;
    string image_figure = 6;
    string image_btn = 7;
}

message IconData {
    string meta_json = 1 [(gogoproto.jsontag) = "meta_json", json_name = "meta_json"];    // meta.json
    string sprits_img = 2 [(gogoproto.jsontag) = "sprits_img", json_name = "sprits_img"]; // 雪碧图
}

message UserPlayIcon {
    int64 id = 1 [(gogoproto.jsontag) = "id", json_name = "id", (gogoproto.customname) = "ID"];                // 装扮id
    string name = 2 [(gogoproto.jsontag) = "name", json_name = "name"];                                        // 装扮名称
    int64 ver = 3 [(gogoproto.jsontag) = "ver", json_name = "ver"];                                            // 资源版本号 最后修改时间
    string icon = 4 [(gogoproto.jsontag) = "icon", json_name = "icon"];                                        // icon的正常状态
    string drag_icon = 5 [(gogoproto.jsontag) = "drag_icon", json_name = "drag_icon"];                         // icon的被拖拽状态
    string static_icon_image = 6 [(gogoproto.jsontag) = "static_icon_image", json_name = "static_icon_image"]; // icon静态图
    string squared_image = 7 [(gogoproto.jsontag) = "squared_image", json_name = "squared_image"];             // 方形预览图
    string icon_hash = 8 [(gogoproto.jsontag) = "icon_hash", json_name = "icon_hash"];                         // icon的正常状态hash
    string drag_icon_hash = 9 [(gogoproto.jsontag) = "drag_icon_hash", json_name = "drag_icon_hash"];          // icon的被拖拽状态hash
    string drag_left_png = 10 [(gogoproto.jsontag) = "drag_left_png", json_name = "drag_left_png"];            // icon左拖拽静态图
    string middle_png = 11 [(gogoproto.jsontag) = "middle_png", json_name = "middle_png"];                     // icon居中静态图
    string drag_right_png = 12 [(gogoproto.jsontag) = "drag_right_png", json_name = "drag_right_png"];         // icon右拖拽静态图
    IconData drag_data = 13 [(gogoproto.jsontag) = "drag_data", json_name = "drag_data"];                      // 拖拽meta.json & 雪碧图
    IconData nodrag_data = 14 [(gogoproto.jsontag) = "nodrag_data", json_name = "nodrag_data"];                // 非拖拽meta.json & 雪碧图
}

message ActivityReceiveInfo {
    ZodiacReceive zodiac = 1 [(gogoproto.jsontag) = "zodiac", json_name = "zodiac"];
}

message ZodiacReceive {
    bool can_receive = 1 [(gogoproto.jsontag) = "can_receive", json_name = "can_receive"];
    bool birthday = 2 [(gogoproto.jsontag) = "birthday", json_name = "birthday"];
}

message DynamicGarbInfo {
    string illustrate = 1 [(gogoproto.jsontag) = "illustrate", json_name = "illustrate"];
    string jump_url = 2 [(gogoproto.jsontag) = "jump_url", json_name = "jump_url"];
    string title = 3 [(gogoproto.jsontag) = "title", json_name = "title"];
    string cover = 4 [(gogoproto.jsontag) = "cover", json_name = "cover"];
    string extension1 = 5 [(gogoproto.jsontag) = "extension1", json_name = "extension1"];
    string extension2 = 6 [(gogoproto.jsontag) = "extension2", json_name = "extension2"];
    int64 status = 7 [(gogoproto.jsontag) = "status", json_name = "status"];
    bool is_reserve = 8 [(gogoproto.jsontag) = "is_reserve", json_name = "is_reserve"];
}

message UserThumbup {
    int64 ID = 1 [(gogoproto.jsontag) = "id", json_name = "id"];
    int64 ItemID = 2 [(gogoproto.jsontag) = "item_id", json_name = "item_id"];
    int64 ItemType = 3 [(gogoproto.jsontag) = "item_type", json_name = "item_type"];
    string Name = 4 [(gogoproto.jsontag) = "name", json_name = "name"];
    string URLImageDim = 5 [(gogoproto.jsontag) = "url_image_dim", json_name = "url_image_dim"];
    string URLImageBright = 6 [(gogoproto.jsontag) = "url_image_bright", json_name = "url_image_bright"];
    string URLImageAni = 7 [(gogoproto.jsontag) = "url_image_ani", json_name = "url_image_ani"];
    string URLImageAniCut = 8 [(gogoproto.jsontag) = "url_image_ani_cut", json_name = "url_image_ani_cut"];
}

message AnnualReport2020 {
    int64 user_distinct_buy_total = 1 [(gogoproto.jsontag) = "user_distinct_buy_total", json_name = "user_distinct_buy_total"];            // 本年度总计购买套装数
    string first_buy_time = 2 [(gogoproto.jsontag) = "first_buy_time", json_name = "first_buy_time"];                                      // 首次购买时间
    string first_buy_item_name = 3 [(gogoproto.jsontag) = "first_buy_item_name", json_name = "first_buy_item_name"];                       // 首次购买装扮名称
    repeated string most_buy_item_groups = 4 [(gogoproto.jsontag) = "most_buy_item_groups", json_name = "most_buy_item_groups"];           // 购买最多的套装类型
    int64 most_buy_item_total = 5 [(gogoproto.jsontag) = "most_buy_item_total", json_name = "most_buy_item_total"];                        // 购买最多的套装数量
    int64 all_buy_vsinger_flag = 6 [(gogoproto.jsontag) = "all_buy_vsinger_flag", json_name = "all_buy_vsinger_flag"];                     // 是否购买过禾念的所有vsinger
    string most_cost_item_name = 7 [(gogoproto.jsontag) = "most_cost_item_name", json_name = "most_cost_item_name"];                       // 花钱最多的装扮名称，多套为空
    string most_cost_item_fee = 8 [(gogoproto.jsontag) = "most_cost_item_fee", json_name = "most_cost_item_fee"];                          // 花钱最多装扮花费金额
    int64 longest_dress_item_days = 9 [(gogoproto.jsontag) = "longest_dress_item_days", json_name = "longest_dress_item_days"];            // 佩戴时间最长的装扮（单位:天）
    string longest_dress_item_name = 10 [(gogoproto.jsontag) = "longest_dress_item_name", json_name = "longest_dress_item_name"];          // 佩戴时间最长的装扮名称
    int64 longest_dress_item_part = 11 [(gogoproto.jsontag) = "longest_dress_item_part", json_name = "longest_dress_item_part"];           // 佩戴时间最长的装扮类型
    int64 kuji_join_flag = 12 [(gogoproto.jsontag) = "kuji_join_flag", json_name = "kuji_join_flag"];                                      // 是否参加烟火大会
    int64 kuji_draw_once_total = 13 [(gogoproto.jsontag) = "kuji_draw_once_total", json_name = "kuji_draw_once_total"];                    // 烟火大会只抽一次的数量
    int64 kuji_draw_several_total = 14 [(gogoproto.jsontag) = "kuji_draw_several_total", json_name = "kuji_draw_several_total"];           // 烟火大会只抽一次的数量
    int64 kuji_draw_all_flag = 15 [(gogoproto.jsontag) = "kuji_draw_all_flag", json_name = "kuji_draw_all_flag"];                          // 烟火大会是否有端盒行为
    int64 kuji_draw_ou_total = 16 [(gogoproto.jsontag) = "kuji_draw_ou_total", json_name = "kuji_draw_ou_total"];                          // 烟火大会抽中欧皇数量统计
    int64 diy_joint_total = 17 [(gogoproto.jsontag) = "diy_joint_total", json_name = "diy_joint_total"];                                   // diy只有拼接挂件数量
    repeated string diy_joint_first_images = 18 [(gogoproto.jsontag) = "diy_joint_first_images", json_name = "diy_joint_first_images"];    // diy拼接挂件最早拼接的素材
    string diy_joint_first_time = 19 [(gogoproto.jsontag) = "diy_joint_first_time", json_name = "diy_joint_first_time"];                   // diy拼接挂件的最早上传时间
    int64 diy_upload_total = 20 [(gogoproto.jsontag) = "diy_upload_total", json_name = "diy_upload_total"];                                // diy有过上传挂件的数量
    string diy_upload_first_image = 21 [(gogoproto.jsontag) = "diy_upload_first_image", json_name = "diy_upload_first_image"];             // diy上传挂件的最早素材链接
    int64 diy_flag = 22 [(gogoproto.jsontag) = "diy_flag", json_name = "diy_flag"];                                                        // 是否创作了diy挂件
    int64 most_present_user_mid = 23 [(gogoproto.jsontag) = "most_present_user_mid", json_name = "most_present_user_mid"];                 // 赠送关系最亲密的人-mid
    string most_present_user_url = 24 [(gogoproto.jsontag) = "most_present_user_url", json_name = "most_present_user_url"];                // 赠送关系最亲密的人-头像地址
    string most_present_user_nickname = 25 [(gogoproto.jsontag) = "most_present_user_nickname", json_name = "most_present_user_nickname"]; // 赠送关系最亲密的人-昵称
    int64 most_present_total = 26 [(gogoproto.jsontag) = "most_present_total", json_name = "most_present_total"];                          // 赠送最亲密总次数
    int64 new_user_flag = 27 [(gogoproto.jsontag) = "new_user_flag", json_name = "new_user_flag"];                                         // 是否是新用户
    string first_buy_origin_color = 28 [(gogoproto.jsontag) = "first_buy_origin_color", json_name = "first_buy_origin_color"];             // 初心颜色
    AnnualReportRewards2020 rewards = 29 [(gogoproto.jsontag) = "rewards", json_name = "rewards"];                                         // 奖励发放状态
}

message AnnualReportRewards2020 {
    int64 new_user = 1 [(gogoproto.jsontag) = "new_user", json_name = "new_user"];          // 新用户折扣券是否发放
    int64 garb_master = 2 [(gogoproto.jsontag) = "garb_master", json_name = "garb_master"]; // 装扮大师挂件是否发放
}

message AnnualReportShare2020 {
    int64 share_user_mid = 1 [(gogoproto.jsontag) = "share_user_mid", json_name = "share_user_mid"];                             // 分享人的mid
    string share_user_url = 2 [(gogoproto.jsontag) = "share_user_url", json_name = "share_user_url"];                            // 分享人的头像链接
    string share_user_nickname = 3 [(gogoproto.jsontag) = "share_user_nickname", json_name = "share_user_nickname"];             // 分享人的昵称
    int64 user_distinct_buy_total = 4 [(gogoproto.jsontag) = "user_distinct_buy_total", json_name = "user_distinct_buy_total"];  // 本年度总计购买套装数
    string first_buy_item_name = 5 [(gogoproto.jsontag) = "first_buy_item_name", json_name = "first_buy_item_name"];             // 首次购买装扮名称
    string longest_dress_item_name = 6 [(gogoproto.jsontag) = "longest_dress_item_name", json_name = "longest_dress_item_name"]; // 佩戴时间最长的装扮名称
    int64 longest_dress_item_part = 7 [(gogoproto.jsontag) = "longest_dress_item_part", json_name = "longest_dress_item_part"];  // 佩戴时间最长的装扮类型
}

message TrialSuitShareInfo {
    int64 id = 1;
    string share_token = 2;
    int64 mid = 3;
    int64 to_mid = 4;
    int64 suit_id = 5;
    int64 total = 6;
    int64 surplus = 7;
    int64 used = 8;
    string month = 9;
    int64 expire_time = 10;
    int64 ver = 11;
    int64 state = 12;
}

message TrialShareLog {
    int64 id = 1;
    string share_token = 2;
    int64 mid = 3;
    int64 to_mid = 4;
    int64 suit_id = 5;
    string month = 6;
    int64 ctime = 7;
    int64 mtime = 8;
}

message UserTrialCardInfo {
    string month = 1;
    int64 left_chance = 2;
}

message UserTrialSuit {
    int64 id = 1;
    int64 mid = 2;
    int64 suit_id = 3;
    int64 expire_time = 4;
    int64 state = 5;
    int64 ctime = 6;
    int64 mtime = 7;
}

message TrialShareCardHistory {
    string share_token = 1;
    int64 mid = 2;
    int64 to_mid = 3;
    int64 suit_id = 4;
    string month = 5;
    int64 state = 6;
    int64 ctime = 7;
    int64 mtime = 8;
}

message MallActivity {
    int64 id = 1;        // 活动id
    string name = 2;     // 活动名称
    string desc = 3;     // 描述
    string jump_url = 4; // 描述
    string image_bg = 5; // 背景图
    string image_figure = 6;
    string image_btn = 7;
    int64 state = 8;
    int64 sort = 9;
    int64 url = 10;
    int64 time_begin = 11;
    int64 time_end = 12;
}

message MallModule {
    int64 id = 1;
    string module_type = 2;
    int64 sort = 3;
    string state = 4;
    repeated MallModuleProp props = 5;
    string name = 6;
    string cardColor = 7;
    string onlineTime = 8;
    string offlineTime = 9;
}
message MallModuleProp {
    int64 module_id = 1;
    string type = 2;
    string value = 3;
    string state = 4;
}

message MallSubject {
    int64 id = 1;
    string name = 2;
    string image_cover = 3;
    string item_ids = 4;
    string state = 5;
    int64 subject_type = 6;
}

message MallEntrance {
    int64 id = 1;
    string name = 2;
    string icon = 3;
    int64 sort = 4;
    string jump_url = 5;
    int64 state = 6;
}

message UserInfo {
    int64 mid = 1;
    string platform = 2;
    string mobi_app = 3;
    string device = 4;
    int32 app_version = 5;
    string model_name = 6;
    int32 net_work_state = 7;
    string buvid = 8;
    string channel = 9;
    string os_version = 10;
    string session_id = 11;
}

message EventSplash {
    // 闪屏id
    int64 id = 1 [(gogoproto.jsontag) = "id", json_name = "id"];
    // 闪屏名称
    string splash_name = 2 [(gogoproto.jsontag) = "splash_name", json_name = "splash_name"];
    // 事件类型 0生日 1注册日 2运营事件
    int64 event_type = 3 [(gogoproto.jsontag) = "event_type", json_name = "event_type"];
    // 展示次数 如：3表示最多展示3次
    int64 show_times = 4 [(gogoproto.jsontag) = "show_times", json_name = "show_times"];
    // 是否有跳过按钮 1是 0否
    int64 show_skip = 5 [(gogoproto.jsontag) = "show_skip", json_name = "show_skip"];
    // 倒计时时长 单位ms
    int64 duration = 6 [(gogoproto.jsontag) = "duration", json_name = "duration"];
    // 生效时间
    int64 begin_time = 7 [(gogoproto.jsontag) = "begin_time", json_name = "begin_time"];
    // 结束时间
    int64 end_time = 8 [(gogoproto.jsontag) = "end_time", json_name = "end_time"];
    // 跳过按钮是否倒计时 1是 0否
    int64 show_countdown = 9 [(gogoproto.jsontag) = "show_countdown", json_name = "show_countdown"];
    // 闪屏相关资源列表
    repeated SplashResource resources = 10 [(gogoproto.jsontag) = "resources", json_name = "resources"];
    // 闪屏包含元素列表
    repeated SplashElement elements = 11 [(gogoproto.jsontag) = "elements", json_name = "elements"];
    // 是否仅wifi下载 1是 0否
    int64 wifi_download = 12 [(gogoproto.jsontag) = "wifi_download", json_name = "wifi_download"];
}

message SplashResource {
    // 资源id
    int64 id = 1 [(gogoproto.jsontag) = "id", json_name = "id"];
    // 资源类型 0主图片 1主视频 2ipad背景图片 3logo
    int64 resource_type = 2 [(gogoproto.jsontag) = "resource_type", json_name = "resource_type"];
    // 资源地址
    string resource_url = 3 [(gogoproto.jsontag) = "resource_url", json_name = "resource_url"];
    // 资源hash值
    string resource_hash = 4 [(gogoproto.jsontag) = "resource_hash", json_name = "resource_hash"];
    // 视频宽度
    int64 video_width = 5 [(gogoproto.jsontag) = "video_width", json_name = "video_width"];
    // 视频高度
    int64 video_height = 6 [(gogoproto.jsontag) = "video_height", json_name = "video_height"];
}

message SplashElement {
    // 元素id
    int64 id = 1 [(gogoproto.jsontag) = "id", json_name = "id"];
    // 元素类型  0空白类型 1纯文字 2富文本 3图片
    int64 element_type = 2 [(gogoproto.jsontag) = "element_type", json_name = "element_type"];
    // 交互类型 0无交互 1普通点击交互 2视频点击交互
    int64 interact_style = 3 [(gogoproto.jsontag) = "interact_style", json_name = "interact_style"];
    // 文案内容
    string text = 4 [(gogoproto.jsontag) = "text", json_name = "text"];
    // 背景图片
    string bg_image = 5 [(gogoproto.jsontag) = "bg_image", json_name = "bg_image"];
    // 背景颜色hex格式 例:#E79D13
    string bg_color = 6 [(gogoproto.jsontag) = "bg_color", json_name = "bg_color"];
    // 文字颜色hex格式 例:#E79D13
    string text_color = 7 [(gogoproto.jsontag) = "text_color", json_name = "text_color"];
    // 图片url
    string image_url = 8 [(gogoproto.jsontag) = "image_url", json_name = "image_url"];
    // 元素布局数据
    SplashLayout layout = 9 [(gogoproto.jsontag) = "layout", json_name = "layout"];
    // 元素点击触发行为
    SplashAction action = 10 [(gogoproto.jsontag) = "action", json_name = "action"];
    // 展示开始时间 ms
    int64 show_duration = 11 [(gogoproto.jsontag) = "show_duration", json_name = "show_duration"];
    // 展示结束时间 ms
    int64 hide_duration = 12 [(gogoproto.jsontag) = "hide_duration", json_name = "hide_duration"];
    // 背景图片 hash
    string bg_image_hash = 13 [(gogoproto.jsontag) = "bg_image_hash", json_name = "bg_image_hash"];
    // 图片url hash
    string image_url_hash = 14 [(gogoproto.jsontag) = "image_url_hash", json_name = "image_url_hash"];
    // 文本标题
    string title = 15 [(gogoproto.jsontag) = 'title', json_name = 'title'];
}

message SplashLayout {
    // 布局类型 0以屏幕左上角原点，元素的左，上边距离屏幕左、上边的距离
    // 1以屏幕左上角原点，元素的中心距离屏幕左、上边的距离
    // 2以屏幕左下角原点，元素的左，下边距离屏幕左、下边的距离
    // 3以屏幕左下角原点，元素的中心边距离屏幕左、下的距离
    // 4以视频左上角原点，元素的中心距离视频左、上边的距离
    int64 layout_type = 1 [(gogoproto.jsontag) = "layout_type", json_name = "layout_type"];
    // 元素距上百分比
    float top_percent = 2 [(gogoproto.jsontag) = "top_percent", json_name = "top_percent"];
    // 元素距左百分比
    float left_percent = 3 [(gogoproto.jsontag) = "left_percent", json_name = "left_percent"];
    // 元素距下百分比
    float bottom_percent = 4 [(gogoproto.jsontag) = "bottom_percent", json_name = "bottom_percent"];
    // 元素距上像素
    int64 top_px = 5 [(gogoproto.jsontag) = "top_px", json_name = "top_px"];
    // 元素距上像素
    int64 left_px = 6 [(gogoproto.jsontag) = "left_px", json_name = "left_px"];
    // 元素距上像素
    int64 bottom_px = 7 [(gogoproto.jsontag) = "bottom_px", json_name = "bottom_px"];
    // 元素占屏宽度百分比
    float width_percent = 8 [(gogoproto.jsontag) = "width_percent", json_name = "width_percent"];
    // 元素占屏高度百分比
    float height_percent = 9 [(gogoproto.jsontag) = "height_percent", json_name = "height_percent"];
    // 元素占屏宽度px
    int64 width_px = 10 [(gogoproto.jsontag) = "width_px", json_name = "width_px"];
    // 元素占屏高度px
    int64 height_px = 11 [(gogoproto.jsontag) = "height_px", json_name = "height_px"];
}

message SplashAction {
    // 点击事件视频跳转指定时间
    int64 video_seek_to = 1 [(gogoproto.jsontag) = "video_seek_to", json_name = "video_seek_to"];
    // 点击跳转url
    string jump_url = 2 [(gogoproto.jsontag) = "jump_url", json_name = "jump_url"];
    // 倒计时时长 ms
    int64 duration = 3 [(gogoproto.jsontag) = "duration", json_name = "duration"];
    // 多长时间以后可以点击 ms
    int64 interact_time = 4 [(gogoproto.jsontag) = "interact_time", json_name = "interact_time"];
    // 是否显示倒计时 1是 0否
    int64 show_countdown = 5 [(gogoproto.jsontag) = "show_countdown", json_name = "show_countdown"];
    // 子元素
    repeated SplashElement elements = 6 [(gogoproto.jsontag) = "elements", json_name = "elements"];
}

message OrderRefund {
    string order_id = 1;
    int64 mid = 2;
    int64 refund_fee = 3;
}

message UserOrder {
    // 订单id
    string order_id = 1;
    // 订单状态
    int64 state = 2;
    // 剩余支付时间
    int64 remain_pay_time = 3;
    // 购买数量
    int64 buy_num = 4;
    // 支付金额
    int64 paid_amount = 5;
    // 优惠券减免金额
    int64 allowance = 6;
    // 装扮part_id (装扮类型)
    int64 part_id = 7;
    // 支付时间
    string pay_time = 8;
    // 关联装扮item_id
    int64 item_id = 9;
    // 直播侧订单号
    string live_order_id = 10;
    // -1代表永久，否则为新增时长(秒)
    int64 add_second = 11;
}

message UserOrderBase {
    int64 mid = 1;
    string order_id = 2;
    string platform = 3;
    int64 item_id = 4;
    int64 fee = 5;
    string currency = 6;
    string state = 7;
    int64 add_second = 8;
    int64 part_id = 9;
    int64 buy_num = 10;
    int64 allowance = 11;
    int64 overdue_time = 12;
}

message MallLotteryCalendar {
    int64 id = 1;
    string item_name = 2;
    string image_preview = 3;
    int64 start_time = 4;
    int64 inventory = 5;
    string jump_url = 6;
    string tag_text = 7;
}

message DigitalPreviewImage {
    string type = 1 [(gogoproto.jsontag) = "type", json_name = "type"];
    string url = 2 [(gogoproto.jsontag) = "url", json_name = "url"];
}

message ItemActivity {
    string type = 1;
    bool time_limit = 2;
    int64 time_from = 3;
    int64 time_to = 4;
    int64 price_bp_forever = 5;
    int64 price_bp_month = 6;
    int64 id = 7;
    string state = 8;
}

message CollectCard {
    int64 act_id = 1 [(gogoproto.jsontag) = "act_id", json_name = "act_id"];                    // 活动id
    string act_name = 2 [(gogoproto.jsontag) = "act_name", json_name = "act_name"];             // 活动名称
    string act_link = 3 [(gogoproto.jsontag) = "act_link", json_name = "act_link"];             // 活动跳转链接
    string act_pic = 4 [(gogoproto.jsontag) = "act_pic", json_name = "act_pic"];                // 活动图片-背景图
    string button_desc = 5 [(gogoproto.jsontag) = "button_desc", json_name = "button_desc"];    // 按钮描述
    string tag = 6 [(gogoproto.jsontag) = "tag", json_name = "tag"];                            // 活动标签  预约中 进行中
    int64 sale_price = 7 [(gogoproto.jsontag) = "sale_price", json_name = "sale_price"];        // 售价 精确到分
    string act_desc = 8 [(gogoproto.jsontag) = "act_desc", json_name = "act_desc"];             // 活动描述
    string bg_color = 9 [(gogoproto.jsontag) = "bg_color", json_name = "bg_color"];             // 背景色
    int64 lottery_id = 10 [(gogoproto.jsontag) = "lottery_id", json_name = "lottery_id"];       // 卡池id
    int64 lottery_type = 11 [(gogoproto.jsontag) = "lottery_type", json_name = "lottery_type"]; // 卡池类型 1-常驻奖池 2-限定奖池
    int64 act_status = 12 [(gogoproto.jsontag) = "act_status", json_name = "act_status"];       // 卡池类型 0-未开始 1-预约中 2-进行中 3-已结束
    string act_y_img = 13 [(gogoproto.jsontag) = "act_y_img", json_name = "act_y_img"];         // 收藏集封面图
    // 卡牌信息
    repeated CardItem card_list = 14 [(gogoproto.jsontag) = "card_list", json_name = "card_list"];
    message CardItem {
        // 卡牌ID
        int64 card_type_id = 1 [(gogoproto.jsontag) = "card_type_id", json_name = "card_type_id"];
        // 卡牌图片
        string card_img = 2 [(gogoproto.jsontag) = "card_img", json_name = "card_img"];
    }
}

message CardActivityPreview {
    int64 act_id = 1;   // 活动id
    string act_pic = 2; // 活动图片-封面图
}

message CardCommentBgPic {
    // 集卡活动分数映射，key为mid_actId的拼接，value为评论背景图
    map<string, CommentBg> CommentBgPicMap = 1;
}

message CommentBg {
    // 评论背景图
    string pic = 1;
}

message UserActivityScoreReq {
    repeated int64 mids = 1; // 用户ID
    int64 act_id = 2;        // 活动id
}

message UserLevel {
    int64 mid = 1;
    // 用户等级
    int64 level = 2;
}

message ActivityLevel {
    int64 act_id = 1;
    // 用户等级
    int64 level = 2;
}

message CardWithDraw {
    int64 item_id = 1; // item_id
    int64 act_id = 2;  // 活动id
}

message Material {
    int64 material_id = 1;       // material_id
    int64 material_type = 2;     // 素材类型
    string material_name = 3;    // 素材名称
    int64 creator = 4;           // 创作者
    int64 state = 5;             // 状态
    repeated Property props = 6; // 属性枚举
    int64 bind_good_id = 7;      // 绑定商品id
    int64 mtime = 8;             // 最后修改时间
}

message UserAssetLogScene {
    int64 expired_time_start = 1;
    int64 expired_time_end = 2;
    int64 scene = 3;
}

message ItemPartGroup {
    int64 id = 1;        // id
    int64 part_id = 2;   // 类型id
    string name = 3;     // 名称
    int64 order = 4;     // 排序字段
    string state = 5;    // 状态
    string desc = 6;     // 描述
    int64 parent_id = 7; // 上一级分组id
    int64 pid_1 = 8;     // 第一集分组id
    int64 pid_2 = 9;     // 第二级分组id
    int64 pid_3 = 10;    // 第三极分组id
}

message ItemGroup {
    int64 group_id = 1;
    int64 node_group_id = 2;
    string group_ext = 3;
}

message ItemGroupLevels {
    garb.service.model.ItemPartGroup item = 1;
    repeated ItemGroupLevelChild child = 2;
}

message ItemGroupLevel {
    garb.service.model.ItemPartGroup item = 1;
    ItemGroupLevelChild child = 2;
}

message ItemGroupLevelChild {
    garb.service.model.ItemPartGroup item = 1;
    garb.service.model.ItemPartGroup child = 2;
}

message GoodGroupExt {
    int64 node_g_id_1 = 1 [(gogoproto.jsontag) = "node_g_id_1", json_name = "node_g_id_1"];
    int64 node_g_id_2 = 2 [(gogoproto.jsontag) = "node_g_id_2", json_name = "node_g_id_2"];
    int64 node_g_id_3 = 3 [(gogoproto.jsontag) = "node_g_id_3", json_name = "node_g_id_3"];
}

message GoodsAssetInfo {
    int64 time = 1 [(gogoproto.jsontag) = "time"];
    repeated GoodsAsset data = 2 [(gogoproto.jsontag) = "data"];
}

message GoodsAsset {
    int64 mid = 1 [(gogoproto.jsontag) = "mid"];
    // 定义资产事件，参考goods_asset.go的AssetOpXXX枚举
    // add：新增数据、update：更新数据、remove：删除、expire：过期
    string op = 2 [(gogoproto.jsontag) = "op"];
    // 仅 op 为 update 时有效
    // all 为 biz_sub_type,fetch_time,show_number,expire
    repeated string fields = 3 [(gogoproto.jsontag) = "fields"];
    vas.common.BizType biz_type = 4 [(gogoproto.jsontag) = "biz_type"];
    int64 biz_id = 5 [(gogoproto.jsontag) = "biz_id"];
    string biz_sub_type = 6 [(gogoproto.jsontag) = "biz_sub_type"];
    int64 fetch_time = 7 [(gogoproto.jsontag) = "fetch_time"];
    int64 show_number = 8 [(gogoproto.jsontag) = "show_number"];
    int64 sort = 9 [(gogoproto.jsontag) = "sort"];
    string item_id = 10 [(gogoproto.jsontag) = "item_id"];
    int64 expire = 11 [(gogoproto.jsontag) = "expire"];
    string extra = 12 [(gogoproto.jsontag) = "extra"];
    int64 state = 13 [(gogoproto.jsontag) = "state"];
    bool hidden = 14 [(gogoproto.jsontag) = 'hidden', json_name = 'hidden'];
}

message CardBgV2 {
    string img = 1;
    int64 bg_no = 2;
    string bg_no_color = 3;
    // 其他元素增加
}

message CacheEquip {
    int64 item_id = 1;
    int64 idx = 2;
    int64 is_diy = 3;
}

enum ItemPart {
    // 默认值
    Unknown = 0;
    // 头像挂件
    Pendant = 1;
    // 动态卡片
    DynamicCard = 2;
    // 点赞动效
    Thumbup = 3;
    // 表情
    Emoji = 4;
    // 表情包
    EmojiPackage = 5;
    // 套装
    Suit = 6;
    // 空间背景
    SpaceBG = 7;
    // 评论卡片
    CommentCard = 8;
    // 个性主题
    Skin = 9;
    // 加载动画
    Loading = 10;
    // 进度条
    PlayIcon = 11;
    // 闪屏
    Splash = 12;
    // 头像
    Profile = 13;
}
