package com.bilibili.adp.web.framework.core;

import com.bilibili.adp.common.exception.IExceptionCode;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by fan<PERSON><PERSON> on 16/9/14.
 */
@Data
@NoArgsConstructor
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class OpenResponse<E> {
    private Integer ret;
    private String msg;
    private E result;


    private OpenResponse(Integer ret, String msg) {
        this.ret = ret;
        this.msg = msg;
    }


    public final static <E> OpenResponse<E> Success() {
        return new OpenResponse(0, "success");
    }

    public final static <E> OpenResponse<E> FAIL(IExceptionCode exceptionCode) {
        Integer error_code = exceptionCode.getCode() == 0 ? 100 : exceptionCode.getCode();

        return new OpenResponse(error_code, exceptionCode.getMessage());
    }

    public final static <E> OpenResponse<E> FAIL(Integer error_code, String error_msg) {
        if (error_code == 0) {
            error_code = 100;
        }
        return new OpenResponse(error_code, error_msg);
    }

    @Override
    public String toString() {
        return "Response{" +
                " ret=" + ret +
                ", msg='" + msg + '\'' +
                '}';
    }
}