package com.bilibili.adp.web.framework.interceptor;

import com.bilibili.adp.common.Constants;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.passport.api.dto.BidAuthInfoDto;
import com.bilibili.adp.passport.api.service.IPassportService;
import com.dianping.cat.Cat;
import com.google.common.base.Strings;
import io.vavr.Tuple2;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Created by fan<PERSON><PERSON> on 2017/3/2.
 */
public class PassportInterceptor extends HandlerInterceptorAdapter {
    final static Logger logger = LoggerFactory.getLogger(PassportInterceptor.class);
    @Value("${passport.multi.domain.access.suffix:@}")
    private String multiDomainAccessSuffix;

    @Value("#{'${origin.filter.urls:@}'.split(',')}")
    private List<String> originFilterUrlList;

    @Value("${passport.free.login.url.method.map:}")
    private String PASSPORT_FREE_LOGIN_URL_METHOD_MAP;

    @Autowired
    private IPassportService passportService;
    @Setter
    private String notFilterUrl;
    @Setter
    private String referUrl;
    public static final String HTTP_ACCESS_TOKEN = "HTTP-ACCESS-TOKEN";

    private static final List<String> NOT_FILTER_SUFFIXS = new ArrayList<>();

    static {
        NOT_FILTER_SUFFIXS.add("/login/check");
        NOT_FILTER_SUFFIXS.add("/fly_pro/session/logout");
        NOT_FILTER_SUFFIXS.add("/effect_ad/session/logout");
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {

        if (!Strings.isNullOrEmpty(PASSPORT_FREE_LOGIN_URL_METHOD_MAP)) {
            String[] entries = PASSPORT_FREE_LOGIN_URL_METHOD_MAP.split(";");
            for (String entry : entries) {
                String[] split = entry.split(",");
                String uri = split[0];
                String method = split[1];
                if (validLoginFreeUrl(request, new Tuple2<>(uri, method))) {
                    return true;
                }
            }
        }

        if (null != notFilterUrl) {
            final String serverName = request.getServerName();

            String[] urls = notFilterUrl.split(",");
            List<String> urlList = Arrays.asList(urls);

            if ("127.0.0.1".equals(serverName)
                    || "localhost".equals(serverName)
                    || urlList.contains(request.getServerName())
                    || serverName.contains(multiDomainAccessSuffix)) {
                return true;
            }
        }

        for (String suffix : NOT_FILTER_SUFFIXS) {
            if (StringUtils.isNotBlank(suffix) && request.getRequestURI().endsWith(suffix)) {
                return true;
            }
        }

        String origin = request.getHeader("Origin");
        String token = request.getHeader(HTTP_ACCESS_TOKEN);
        logger.info("origin is {},token is {}", origin, token);
        if (StringUtils.isNotBlank(origin)) {
            for (String origin_url : originFilterUrlList) {
                if (origin.contains(origin_url) && StringUtils.isNotBlank(token)) {
                    return true;
                }
            }
        }

        try {
            BidAuthInfoDto bidAuthInfoDto = passportService.validateBidCookie(request.getHeader("Cookie"));
            request.setAttribute(Constants.PASSPORT_AUTHINFO, bidAuthInfoDto);
        } catch (ServiceException e) {
            logger.error("validateCookie.error:{}, cookie:{}", e, request.getHeader("Cookie"));
            response.setStatus(HttpServletResponse.SC_MOVED_TEMPORARILY);
            response.setHeader("Location", referUrl);
            Cat.logEvent("AD_PLATFORM_COOKIE_ERROR", "validateCookie.error");
            return false;
        }
        return true;
    }

    private boolean validLoginFreeUrl(HttpServletRequest httpRequest, Tuple2<String, String> entry) {
        String value = entry._2();

        if (!httpRequest.getMethod().equals(value)) {
            return false;
        }

        String[] filterUrl = entry._1().split("/");

        String url = httpRequest.getRequestURI().replace(httpRequest.getContextPath(), "").replace("//", "/");
        String[] httpUrl = url.split("/");

        if (!match(filterUrl, httpUrl)) {
            return false;
        }
        return true;
    }

    private boolean match(String[] filterUrl, String[] httpUrl) {
        if (filterUrl.length != httpUrl.length) {
            return false;
        }
        for (int i = 0; i < filterUrl.length; i++) {
            if (filterUrl[i].startsWith("{") && filterUrl[i].endsWith("}")) {
                continue;
            }
            if (!filterUrl[i].equals(httpUrl[i])) {
                return false;
            }
        }
        return true;
    }

    public String getPASSPORT_FREE_LOGIN_URL_METHOD_MAP() {
        return PASSPORT_FREE_LOGIN_URL_METHOD_MAP;
    }

    public void setPASSPORT_FREE_LOGIN_URL_METHOD_MAP(String PASSPORT_FREE_LOGIN_URL_METHOD_MAP) {
        this.PASSPORT_FREE_LOGIN_URL_METHOD_MAP = PASSPORT_FREE_LOGIN_URL_METHOD_MAP;
    }
}