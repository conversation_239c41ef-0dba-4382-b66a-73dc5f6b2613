syntax = "proto3";
package crm.service.copyrightprotect.v1;

import "extension/wdcli/wdcli.proto";
import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "google/protobuf/empty.proto";
// import "google/api/annotations.proto";

option (wdcli.appid) = "main.crm.copyright-protect-service";
option go_package = "buf.bilibili.co/bapis/bapis-gen/crm/service.copyright.protect;api";
option java_package = "com.bapis.crm.service.copyright.protect";
option java_multiple_files = true;
//option (gogoproto.goproto_getters_all) = false;

service CopyrightProtect {
  rpc Ping(.google.protobuf.Empty) returns (.google.protobuf.Empty);

  // 添加监测任务：mid和aid唯独
  rpc AddMonitorTask(AddMonitorTaskReq) returns (.google.protobuf.Empty);
  // 停止监测任务(只停止任务监控，且不停止已监测中的稿件)：mid维度和aid维度
  rpc StopMonitorTask(StopMonitorTaskReq) returns (.google.protobuf.Empty);
  //通过avid查看监测中的稿件
  rpc GetProtectedArc(GetProtectedArcReq) returns (GetProtectedArcReply);
  //判断稿件来源
  rpc ProtectedArcsSource(ProtectedArcsSourceReq) returns (ProtectedArcsSourceReply);
}

message AddMonitorTaskReq {
  int32 business = 1 [(gogoproto.moretags) = 'json:"business" form:"business" validate:"required"'];
  repeated int64 mids = 2 [(gogoproto.moretags) = 'json:"mids" form:"mids,split" validate:"max=500,dive,gt=0,required"'];
  repeated int64 avids = 3 [(gogoproto.moretags) = 'json:"avids" form:"avids,split" validate:"max=500,dive,gt=0,required"'];
}

message StopMonitorTaskReq {
  int64 business = 1 [(gogoproto.moretags) = 'json:"business" form:"business" validate:"required"'];
  repeated int64 mids = 2 [(gogoproto.moretags) = 'json:"mids" form:"mids,split" validate:"max=100,dive,gt=0,required"'];
  repeated int64 avids = 3 [(gogoproto.moretags) = 'json:"avids" form:"avids,split" validate:"max=100,dive,gt=0,required"'];
}

message GetProtectedArcReq {
  repeated int64 avids = 1 [(gogoproto.moretags) = 'json:"avids" form:"avids,split" validate:"max=500,dive,gt=0,required"'];
}


message ProtectedArcsSourceReq {
  repeated int64 avids = 1 [(gogoproto.moretags) = 'json:"avids" form:"avids,split" validate:"max=500,dive,gt=0,required"'];
  int32 business = 2 [(gogoproto.moretags) = 'json:"business" form:"business" validate:"required"'];
}

message ProtectedArcsSourceReply {
  repeated int64 avids = 1 [(gogoproto.jsontag) = "avids", json_name = "avids"];
}

message GetProtectedArcReply {
  map<int64, ProtectedArcInfo> protected_arcs = 2;
}

message ProtectedArcInfo {
  int64 avid = 1 [(gogoproto.jsontag) = "avid", json_name = "avid"];
  int64 mid = 2 [(gogoproto.jsontag) = "mid", json_name = "mid"];
  repeated int32 sources = 3 [(gogoproto.jsontag) = "sources", json_name = "sources"];
}