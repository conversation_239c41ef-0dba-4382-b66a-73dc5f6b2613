// 定义项目 API 的 proto 文件 可以同时描述 gRPC 和 HTTP API
// protobuf 文件参考:
//  - https://developers.google.com/protocol-buffers/
syntax = "proto3";

import "extension/wdcli/wdcli.proto";
option (wdcli.appid) = "main.crm.kol-service";
import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "google/protobuf/empty.proto";
import "google/api/annotations.proto";

// package 命名使用 {appid}.{version} 的方式, version 形如 v1, v2 ..
package main.crm.kol.service.v1;

// NOTE: 最后请删除这些无用的注释 (゜-゜)つロ

option go_package = "buf.bilibili.co/bapis/bapis-gen/crm/service.kol;api";
option java_package = "com.bapis.crm.service.kol";
option java_multiple_files = true;
option (gogoproto.goproto_getters_all) = false;

service Kol {
  rpc Ping(.google.protobuf.Empty) returns (.google.protobuf.Empty);

  // 查询UP主社区审核状态
  rpc GetUpCommunityConclusion(GetUpCommunityConclusionReq) returns (GetUpCommunityConclusionResp) {
    option (google.api.http) = {get: "/x/internal/up/community-conclusion"};
  }
}

message GetUpCommunityConclusionReq {
  // UP主MID
  int64 mid = 1 [(gogoproto.moretags) = 'validate:"required"'];
}

message GetUpCommunityConclusionResp {
  // 社区审核结论
  repeated KolCommunityConclusion conclusions = 1;
}

message KolCommunityConclusion {
  // 平台
  PlatformEnum platform = 1;
  // KOL站外ID
  string up_id = 2;
  // 审核状态
  MarkStatusEnum mark_status = 3;
  // 社区结论
  CommunityConclusionEnum community_conclusion = 4;
}

// PlatformEnum 平台
// swagger:enum PlatformEnum
enum PlatformEnum {
  // 0 - 无效类型
  PlatformNone = 0;
  // 1 - 西瓜
  PlatformXiGua = 1;
  // 2 - 小红书
  PlatformXiaoHongShu = 2;
  // 3 - 抖音
  PlatformTicTok = 3;
  // 4 - youtube
  PlatformYouTube = 4;
  // 5 - 快手
  PlatformKuaiShou = 5;
  // 6 - instagram
  PlatformInstagram = 6;
  // 7 - 知乎
  PlatformZhiHu = 7;
  // 8 - 微信公众号
  PlatformWechat = 8;
  // 9 - 其他平台
  PlatformOthers = 9;
  // 10 - TikTok
  PlatformTikTok = 10;
  // 11 - 今日头条
  PlatformTouTiao = 11;
  // 12 - Facebook
  PlatformFacebook = 12;
  // 13 - 微博
  PlatformWeiBo = 13;
  // 14 - Twitter
  PlatformTwitter = 14;
  // 15 - twitch
  PlatformTwitch = 15;
  // 16 - 斗鱼
  PlatformDouYu = 16;
  // 17 - 好看视频
  PlatformHaoKan = 17;
  // 18 - linkedin
  PlatformLinkedIn = 18;
  // 19 - niconico
  PlatformNiconico = 19;
}

// MarkStatusEnum 审核状态
// swagger:enum MarkStatusEnum
enum MarkStatusEnum {
  // 0 - 无效类型
  MarkStatusNone = 0;
  // 1 - 未推送
  MarkStatusNotPush = 1;
  // 2 - 一审判定中
  MarkStatusInFirstReview = 2;
  // 3 - 社区判定中
  MarkStatusInCommunityReview = 3;
  // 4 - 已审核
  MarkStatusFinished = 4;
}

// CommunityConclusionEnum 社区结论
// swagger:enum CommunityConclusionEnum
enum CommunityConclusionEnum {
  // 0 - 无效类型
  CommunityConclusionNone = 0;
  // 1 - 可以入驻
  CommunityConclusionAllowed = 1;
  // 2 - 不可入驻
  CommunityConclusionNotAllowed = 2;
  // 3 - 谨慎入驻
  CommunityConclusionCautious = 3;
  // 100 - 空
  CommunityConclusionNull = 100;
}