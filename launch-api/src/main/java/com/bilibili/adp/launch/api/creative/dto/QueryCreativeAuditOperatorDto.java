/** 
* <AUTHOR> 
* @date  2018年4月25日
*/ 

package com.bilibili.adp.launch.api.creative.dto;

import java.util.List;

import com.bilibili.adp.common.util.Page;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryCreativeAuditOperatorDto {
	private Integer id;
	private List<Integer> ids;
	
	private String operatorName;
	private List<String> operatorNames;
	
	private Integer status;
	private List<Integer> statusList;
	
	private Page page;
	private String orderBy;
}
