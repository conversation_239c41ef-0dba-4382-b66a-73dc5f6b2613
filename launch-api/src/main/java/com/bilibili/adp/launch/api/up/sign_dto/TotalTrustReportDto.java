package com.bilibili.adp.launch.api.up.sign_dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TotalTrustReportDto {

    private Integer show_growth;

    private Integer play_growth;

    private Integer fans_growth;

    private Integer can_show_state;

    public static TotalTrustReportDto getEmptyInstance(){
        return TotalTrustReportDto.builder()
                .fans_growth(0)
                .show_growth(0)
                .play_growth(0)
                .can_show_state(0)
                .build();
    }
}
