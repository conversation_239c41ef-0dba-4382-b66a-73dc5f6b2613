package com.bilibili.adp.launch.api.soa;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.launch.api.creative.dto.*;

import java.sql.Timestamp;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2018/1/4
 **/
public interface ISoaCreativeService {
    long getTodayLaunchingCreativeCount(Integer accountId);

    CpcCreativeDto getCreativeDtoById(Integer creativeId);

    ImageVideoDto checkUploadMaterial(Integer templateId, Integer type, String url, String suffix) throws ServiceException;

    int create(Operator operator, NewCreativeDto newCreativeDto) throws ServiceException;

    void update(Operator operator, CpcUnitCreativeDto updateDto) throws ServiceException;

    Set<Integer> batchDelete(Operator operator, List<Integer> creativeIds) throws ServiceException;

    void auditPass(Operator operator, Integer creativeId) throws ServiceException;

    void auditReject(Operator operator, Integer creativeId, String reason) throws ServiceException;

    void batchStart(Operator operator, List<Integer> creativeIds) throws ServiceException;

    void batchStop(Operator operator, List<Integer> creativeIds) throws ServiceException;

    List<CpcCreativeDto> queryCreative(QueryCpcCreativeDto query);

    List<Integer> queryCpcCreativeIds(QueryCpcCreativeDto query);

    List<CpcCreativeDto> queryLightCpcCreativeDto(QueryCpcCreativeDto query);

    PageResult<CpcCreativeDto> queryCreativeByPage(QueryCpcCreativeDto query);

    ImageVideoDto checkUploadMaterial(Integer templateId, Integer type, byte[] bytes, String suffix)
            throws ServiceException;

    /**
     * 此soa方法已废弃，请使用cpm-adp中的grpc方法
     * @param operator
     * @param mgkPageIds
     */
    @Deprecated
    void auditRejectByMgkPageIds(Operator operator, List<Long> mgkPageIds);

    List<SimpleCreativeDto> getSimpleCreativeDtosInCreativeIds(List<Integer> creativeIds);

    List<CpcSamePageCreativeDto> getSameScreenImageCreativesInPageIds (List<Integer> pageIds, Timestamp beginTime, Timestamp endTime);

    Set<Integer> batchEnable(Operator operator, List<Integer> creativeIds) throws ServiceException;

    Set<Integer> batchPause(Operator operator, List<Integer> creativeIds) throws ServiceException;

    Set<Integer> batchEnd(Operator operator, List<Integer> creativeIds) throws ServiceException;
}
