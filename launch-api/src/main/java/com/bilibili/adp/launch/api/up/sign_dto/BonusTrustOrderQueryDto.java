package com.bilibili.adp.launch.api.up.sign_dto;

import com.bilibili.adp.common.enums.SortTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BonusTrustOrderQueryDto {

    private Integer id;

    private Integer accountId;

    private Long avid;

    private List<Long> avids;

    private Integer orderStatus;

    private List<Integer> orderStatusList;

    /**
     * 托管订单的最小投放开始时间
     */
    private Timestamp minLaunchBeginTime;

    /**
     * 托管订单的最大投放开始时间
     */
    private Timestamp maxLaunchBeginTime;
    
    private Integer creativeId;

    /**
     * 投放数据开始时间
     */
    private Timestamp statBeginTime;

    /**
     * 投放数据结束时间
     */
    private Timestamp statEndTime;

    private String nameLike;

    private String sortField;

    private SortTypeEnum sortType;

    private Timestamp ctimeBefore;

}
