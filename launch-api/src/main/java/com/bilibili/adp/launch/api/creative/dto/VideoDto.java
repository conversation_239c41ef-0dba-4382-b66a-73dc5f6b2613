package com.bilibili.adp.launch.api.creative.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2021/05/16
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class VideoDto {

    /**
     * 视频id
     */
    private Long video_id;

    /**
     * 视频url
     */
    private String video_url;

    /**
     * 视频封面图url
     * 作用：原生内容投放模式下，播放页tab，视频url
     */
    private String video_cover_image_url;
}
