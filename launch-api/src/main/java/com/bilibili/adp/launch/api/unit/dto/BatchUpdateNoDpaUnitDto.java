/**
 * <AUTHOR>
 * @date 2018年1月9日
 */

package com.bilibili.adp.launch.api.unit.dto;

import com.google.common.base.Strings;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class BatchUpdateNoDpaUnitDto implements Serializable {
    private static final long serialVersionUID = 6404637577638213302L;
    private List<Integer> unitIds;
    private Long budget;
    private Integer dailyBudgetType;
    /**
     * 当日预算/次日预算
     * @see com.bilibili.adp.launch.api.common.BudgetEffectType
     */
    private Integer effectType;

    /**
     * 是否每次重复
     * 仅仅针对次日预算生效
     */
    private Integer isRepeat;
    private Integer costPrice;
    private Integer twoStageBid;
    // 第二目标出价(分)
    private Integer targetTwoBid;
    private String launchEndDate;
    private String launchTime;

    /**
     * 操作的账户拥有的账号标签
     */
    private List<Integer> accountLabelIds;

    private List<Integer> needQueryTodayCostUnitIds;

    public boolean isNoModified() {
        return (budget == null
                && dailyBudgetType == null
                && effectType == null
                && isRepeat == null
                && costPrice == null
                && twoStageBid == null
                && Objects.isNull(targetTwoBid)
                && Strings.isNullOrEmpty(launchTime)
                && Strings.isNullOrEmpty(launchEndDate));
    }
}
