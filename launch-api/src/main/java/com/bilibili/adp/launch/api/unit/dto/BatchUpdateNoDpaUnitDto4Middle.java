package com.bilibili.adp.launch.api.unit.dto;

import com.google.common.base.Strings;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class BatchUpdateNoDpaUnitDto4Middle {
    private static final long serialVersionUID = 6404637577638213302L;
    private List<Integer> unitIds;
    private Long budget;
    private Integer dailyBudgetType;
    private Integer costPrice;
    private Integer twoStageBid;
    // 第二目标出价(页面值)
    private BigDecimal targetTwoBid;
    // 第二目标出价(数据库值)
    private Integer targetTwoBidDb;
    private String launchEndDate;
    private String launchTime;

    public boolean isNoModified() {
        return (budget == null
                && dailyBudgetType == null
                && costPrice == null
                && twoStageBid == null
                && Objects.isNull(targetTwoBid)
                && Strings.isNullOrEmpty(launchTime)
                && Strings.isNullOrEmpty(launchEndDate));
    }
}
