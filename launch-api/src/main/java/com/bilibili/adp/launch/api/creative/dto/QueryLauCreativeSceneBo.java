package com.bilibili.adp.launch.api.creative.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryLauCreativeSceneBo implements Serializable {

    private List<Integer> accountIdList;

    private List<Long> creativeIdList;

    private List<Integer> unitIdList;

    private static final long serialVersionUID = 1L;
}

