package com.bilibili.adp.launch.api.flyPro.dto.v2;

import com.bilibili.adp.resource.api.slot_group.ResSlotGroupTemplateMappingDto;
import com.bilibili.location.api.template.dto.TemplateDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class VirtualTemplateGroupDto {
    private List<ResSlotGroupTemplateMappingDto> resSlotGroupTemplateMappingDtos;
    private List<TemplateDto> templateDtos;
    private List<Integer> cardTypes;

    private Boolean isFillTitle;
    private Integer titleMinLength;
    private Integer titleMaxLength;

    private Boolean isFillDesc;
    private Integer descMinLength;
    private Integer descMaxLength;

    private Boolean isFillExtDesc;
    private Integer extDescMinLength;
    private Integer extDescMaxLength;

    private Boolean isSupportImage;
    private Integer imageWidth;
    private Integer imageHeight;
    private Integer imageKbLimit;
    private Boolean isSupportExtImage;

    private Boolean isSupportVideoId;
    private Boolean isSupportGif;

}
