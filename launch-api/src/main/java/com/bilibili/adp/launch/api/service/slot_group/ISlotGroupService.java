package com.bilibili.adp.launch.api.service.slot_group;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.launch.api.service.slot_group.bos.*;

import java.util.Collection;
import java.util.List;

public interface ISlotGroupService {
    List<SlotGroupChannelBo> listSlotGroupChannels();
    List<SlotGroupSceneBo> listSlotGroupScenes(Collection<Integer> channelIds);
    List<SlotGroupSceneBo> listUpgradeChannelScenes(Collection<Integer> slotGroupIds);
    void insertOrUpdateSlotGroupScene(SlotGroupSceneBo sceneBo, Operator operator);
    void deleteSlotGroupScene(Integer sceneId, Operator operator);
    List<SlotGroupFullBo> query(QuerySlotGroupBo queryBo);
    Integer saveSlotGroup(SlotGroupBo slotGroup, Operator operator);
}
