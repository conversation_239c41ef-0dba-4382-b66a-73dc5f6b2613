package com.bilibili.adp.launch.api.account.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LauAccountInfoDto {
    private Integer id;

    private Integer mid;

    private Integer accountId;

    private String brandIdentity;

    private String brandName;

    private LauAccountFaceDto face;

    private String space;

    private Integer status;

    private Long longMid;
}
