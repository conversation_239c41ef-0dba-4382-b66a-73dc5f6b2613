syntax = "proto3";
option java_outer_classname = "AdLogAggregatePb";

package com.bilibili.bcg.shuttle.online.biz.proto;

option go_package = "buf.bilibili.co/bapis/bapis-gen/bcg/stat.shuttle.shuttle.online.biz;api";
option java_package = "com.bapis.bcg.stat.shuttle.shuttle.online.biz";
option java_multiple_files = false;

message AdLogAggregate {

    int64 accountId = 1;
    int64 orderId = 2;
    int64 scheduleId = 3;
    int64 campaignId = 4;
    int64 unitId = 5;
    int64 creativeId = 6;
    int64 screenId = 7;
    int64 sourceId = 8;
    string publisher = 9;
    int64 unitDailyBudget = 10; //单元日预算, 单位:分
    int64 campaignDailyBudget = 11;
    int64 accountDailyBudget = 12;
    int64 unitTotalBudget = 13;
    int64 campaignTotalBudget = 14;
    int64 eventTimeSlice = 15;
    string adType = 16;
    int32 salesType = 17;
    bool show = 18;
    bool click = 19;
    int64 avid = 20;
    int32 templateId = 21;
    bool cheat = 22;
    int32 showCount = 23;
    int32 clickCount = 24;
    int64 costPriceSum = 25;
    int64 bidPriceSum = 26;
    int32 unloginShowCount = 27;
    int32 unloginClickCount = 28;
    int32 unloginClickAcClickCount = 29;
    bool unloginClickCheat = 30; // 是否为未登录点击作弊 ==> 单独统计输出 未登录点击反作弊的点击数
    int64 earliestEventTime = 31;
    int64 latestEventTime = 32;
    int64 earliestRequestTime = 33;
    int64 latestRequestTime = 34;
    int64 latestPublisherSideMessageId = 35;
    int32 promotionPurposeType = 36;
    int64 seasonId = 37;
    int64 epId = 38;
    int64 upMid = 39;
    int32 allowOverChargePercent = 40; // 允许超播比例（102% ==> allowOverChargePercent = 2 ）
    int32 extraBudgetChargeRebateThreshold = 41; //超预算扣款返款比例阈值 (110% ==> extraBudgetChargeRebateThreshold =10)，表示扣款超过预算110% 的部分需要 T+N之后返款
    int32 deductionSign = 42;
}

message AdLogAggregateList {
    repeated AdLogAggregate adLogAggregates = 1;
}