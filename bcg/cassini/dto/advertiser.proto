syntax = "proto3";

package bcg.cassini.dto;
// import "extension/wdcli/wdcli.proto";

option go_package = "buf.bilibili.co/bapis/bapis-gen/bcg/cassini.dto;v1";

option java_package = "com.bapis.bcg.cassini.dto";
option java_multiple_files = true;  

message AdvertiserIndexEntity {
    repeated Advertiser advertisers = 1;
}

message Advertiser {
    /**
     * 账户ID
     */
    int64 account_id = 1;

    /**
     * 余额, 单位: 分
     */
    // deprecated: it's realtime data
    int64 balance = 2;

    /**
     * 后门mid
     */
    repeated int64 backdoor_mids = 3;

    map<int64, UserThresholdConfig> user_threshold_config_map = 4;

    bool use_ad_web = 5;

    repeated string open_apps = 6;

    /**
     * 账户最低ecpm限制, for cpc
     */
    int32 min_ecpm = 7;

    /**
     * 账户点击率限制, for cpc
     */
    int32 min_ctr = 8;

    /**
     * 一级类目ID
     */
    int32 category_first_id = 9;

    /**
     * 二级类目ID
     */
    int32 category_second_id = 10;

    /**
     * 所属公司id
     */
    int64 company_group_id = 11;

    /**
     * 预算
     */
    // budget = 0 means unlimited budget
    int64 budget = 12;
    /**
     * 支持dpa
     */
    bool dpa_support = 13;

    /**
     * 是否需要展示风险行业提示
     */
    bool special_industry = 14;


    /**
     * 风险行业提示
     */
    string special_industry_tips = 15;

    /**
     * 是否内部账号
     */
    bool is_inner_account = 16;

    /**
     * 是否内部账号
     */
    bool is_db_inner_account = 17;

    /**
     * 帐号下广告所属 产品
     */
    int32 product_id = 18;

    /**
     * 账号上打的标签id
     */
    repeated int32 acc_tag_ids = 19;

    /*
     * 用户属性 0个人用户 1机构用户 2个人起飞用户
     */
    int32 user_type = 20;

    /**
     * 账号组id，相当于手动标签
     */
    repeated int64 acc_group_ids = 21;

    bool rtaAccount = 22;

    //行业一级类目ID
    int32 category_first_id_v2 = 23;

    //行业二级类目ID
    int32 category_second_id_v2 = 24;

    //集团
    int32 group_id = 25;

    // 客户id
    int32 customer_id = 26;

    // 托管签约金余额
    int64 mandate_fund = 27;

    // 签约金余额
    int64 bonus_fund = 28;

    // 个人起飞现金托管余额
    int64 mandate_cash_fund = 29;

    // 个人起飞起飞币托管余额
    int64 mandate_flycoin_fund = 30;

    // 个人起飞激励金托管余额
    int64 mandate_incentive_fund = 31;

    // 部门id
    int32 department_id = 32;

    int64 tomorrow_budget = 33;

    int64 tomorrow_budget_effective_time = 34;

    // 托管红包余额
    int64 red_packet = 35;

    // 商品消耗
    int64 item_cost = 36;

    //新行业一级类目ID
    int32 category_first_id_v3 = 37;

    //新行业二级类目ID
    int32 category_second_id_v3 = 38;
    //新行业三级类目ID
    int32 category_third_id_v3 = 39;

    bool is_delete = 40;
    string user_name = 41;
    string customer_user_name = 42;

    // iaa流量控制开关
    bool use_control =43;
    // iaa 流控阈值,万分位
    int32 control_scale=44;
}

message UserThresholdConfig {
    int64 user_id = 1;

    int32 min_ecpm = 2;

    int32 min_ctr = 3;
}