package com.bilibili.adp.advertiser.portal.webapi.effect_ad.danmaku.vos;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName SelfDanmakuPicVo
 * <AUTHOR>
 * @Date 2024/1/4 3:11 下午
 * @Version 1.0
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class SelfDanmakuPicVo {

    private String picUrl;
    private Integer width;
    private Integer height;
}
