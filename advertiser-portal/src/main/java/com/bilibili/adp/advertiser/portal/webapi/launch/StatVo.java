/**
 * <AUTHOR>
 * @date 2017年10月13日
 */

package com.bilibili.adp.advertiser.portal.webapi.launch;

import com.bilibili.adp.advertiser.portal.webapi.launch.cpc.vo.CpcReportCommonColumnVo;
import com.bilibili.adp.web.framework.annotations.ExcelResources;
import com.fasterxml.jackson.annotation.JsonUnwrapped;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StatVo {
    @ApiModelProperty(notes = "展示数量")
    private Long show_count;

    @ApiModelProperty(notes = "点击次数")
    private Long click_count;

    @ApiModelProperty(notes = "点击率")
    private String click_rate;

    @ApiModelProperty(notes = "平均千次展现费用(元)")
    private BigDecimal average_cost_per_thousand;

    @ApiModelProperty(notes = "平均点击费用(元)")
    private BigDecimal cost_per_click;

    @ApiModelProperty(notes = "消费(元)")
    private BigDecimal cost;

    @ApiModelProperty(notes = "提交订单数")
    private Integer order_add_count;
    @ApiModelProperty(notes = "提交订单金额")
    private BigDecimal order_add_price;
    @ApiModelProperty(notes = "支付订单数")
    private Integer order_count;
    @ApiModelProperty(notes = "支付订单金额")
    private BigDecimal order_payment;

    @ApiModelProperty(notes = "注册数")
    private Integer register_count;

    private String goods_conversion_rate;
    private BigDecimal goodsConversionRate;

    private BigDecimal goods_roi;

    @ApiModelProperty(notes = "游戏激活数")
    private Integer activate_count;

    @ApiModelProperty(notes = "游戏预约数")
    private Integer reserve_count;

    private Integer fan_follow_count;

    private Integer fan_whisper_count;

    @ApiModelProperty(notes = "游戏ios激活数")
    private Integer ios_activate_count;

    @ApiModelProperty(notes = "游戏安卓激活数")
    private Integer android_activate_count;

    @ApiModelProperty(notes = "涨粉成本(元)")
    private String cost_per_increase_fans = "--";
    @ApiModelProperty(notes = "激活成本(元)")
    private String cost_per_app_activate = "--";
    @ApiModelProperty(notes = "游戏预约成本(元)")
    private String cost_per_game_reserve = "--";
    @ApiModelProperty(notes = "游戏激活成本(元)")
    private String cost_per_game_activate = "--";
    @ApiModelProperty(notes = "注册成本(元)")
    private String cost_per_register = "--";

    @ApiModelProperty(notes = "涨粉率")
    private String fans_follow_rate;
    @ApiModelProperty(notes = "激活率")
    private String app_activate_rate;
    @ApiModelProperty(notes = "游戏预约率")
    private String game_reserve_rate;
    @ApiModelProperty(notes = "游戏激活率")
    private String game_activate_rate;
    @ApiModelProperty(notes = "注册率")
    private String register_rate;

    @ApiModelProperty(notes = "表单提交数")
    private Integer form_submit_count;
    @ApiModelProperty(notes = "表单提交成本(元)")
    private String cost_per_form_submit = "--";
    @ApiModelProperty(notes = "表单提交率")
    private String form_submit_rate;

    @ApiModelProperty(notes = "安卓下载数")
    private Integer android_download_count;
    @ApiModelProperty(notes = "安卓下载成本(元)")
    private String cost_per_android_download = "--";
    @ApiModelProperty(notes = "安卓下载率")
    private String android_download_rate;

    @ApiModelProperty(notes = "安卓安装数")
    private Integer android_install_count;
    @ApiModelProperty(notes = "安卓安装成本(元)")
    private String cost_per_android_install = "--";
    @ApiModelProperty(notes = "安卓安装率")
    private String android_install_rate;

    @JsonUnwrapped
    private CpcReportCommonColumnVo cpcReportCommonColumnVo;

    @ApiModelProperty(notes = "成功调起数")
    private Integer app_callup_count;
    @ApiModelProperty(notes = "调起成功成本")
    private BigDecimal app_callup_cost;
    @ApiModelProperty(notes = "调起成功率")
    private String app_callup_ratio;

    @ApiModelProperty(notes = "落地页调起店铺数")
    private Integer lp_callup_count;
    @ApiModelProperty(notes = "落地页调起店铺成本")
    private BigDecimal lp_callup_cost;
    @ApiModelProperty(notes = "落地页调起店铺率")
    private String lp_callup_rate;
    @ApiModelProperty(notes = "提交订单成本")
    private BigDecimal order_submit_cost;
    @ApiModelProperty(notes = "提交订单率")
    private String order_submit_rate;

    public static StatVo getEmptyVo() {
        return StatVo
                .builder()
                .average_cost_per_thousand(BigDecimal.ZERO)
                .click_count(0L)
                .click_rate("0.00%")
                .cost(BigDecimal.ZERO)
                .cost_per_click(BigDecimal.ZERO)
                .show_count(0L)
                .order_add_count(0)
                .order_add_price(BigDecimal.ZERO)
                .order_count(0)
                .order_payment(BigDecimal.ZERO)
                .register_count(0)
                .goods_conversion_rate("0.00%")
                .goods_roi(BigDecimal.ZERO)
                .activate_count(0)
                .reserve_count(0)
                .fan_follow_count(0)
                .fan_whisper_count(0)
                .ios_activate_count(0)
                .android_activate_count(0)
                .cost_per_increase_fans("--")
                .cost_per_app_activate("--")
                .cost_per_game_reserve("--")
                .cost_per_game_activate("--")
                .cost_per_register("--")
                .fans_follow_rate("0.00%")
                .app_activate_rate("0.00%")
                .game_reserve_rate("0.00%")
                .game_activate_rate("0.00%")
                .register_rate("0.00%")
                .form_submit_count(0)
                .form_submit_rate("0.00%")
                .cost_per_form_submit("--")
                .android_download_count(0)
                .android_download_rate("0.00%")
                .cost_per_android_download("--")
                .android_install_count(0)
                .android_install_rate("0.00%")
                .cost_per_android_install("--")
                .cpcReportCommonColumnVo(new CpcReportCommonColumnVo())
                .app_callup_count(0)
                .app_callup_cost(BigDecimal.ZERO)
                .app_activate_rate("0.00%")
                .lp_callup_count(0)
                .lp_callup_cost(BigDecimal.ZERO)
                .lp_callup_rate("0.00%")
                .order_submit_cost(BigDecimal.ZERO)
                .order_submit_rate("0.00%")
                .build();
    }
}
