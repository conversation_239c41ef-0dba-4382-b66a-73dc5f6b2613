package com.bilibili.adp.advertiser.portal.webapi.middleground.vo.cpc;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.FieldDefaults;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class UnitNameUpdateVo {

    @ApiModelProperty(value = "单元id")
    private Integer unitId;

    @ApiModelProperty(value = "单元名称")
    private String unitName;

}
