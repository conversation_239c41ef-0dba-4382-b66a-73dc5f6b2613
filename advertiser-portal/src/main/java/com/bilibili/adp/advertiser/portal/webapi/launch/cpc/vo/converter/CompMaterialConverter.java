package com.bilibili.adp.advertiser.portal.webapi.launch.cpc.vo.converter;

import com.bilibili.adp.advertiser.portal.webapi.v2.vo.common.CompMaterialV2Vo;
import com.bilibili.adp.advertiser.portal.webapi.v2.vo.common.CompMaterialVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper
public interface CompMaterialConverter {
    CompMaterialConverter MAPPER = Mappers.getMapper(CompMaterialConverter.class);

    @Mapping(target = "archiveVideoVo", expression = "java(ArchiveVideoConverter.MAPPER.toV2(vo.getArchiveVideoVo()))")
    CompMaterialV2Vo toV2(CompMaterialVo vo);
}
