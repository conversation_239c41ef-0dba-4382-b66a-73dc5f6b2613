package com.bilibili.adp.advertiser.portal.service.stat;

import com.bilibili.adp.advertiser.portal.webapi.statistic.vo.CreativeDateVo;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.cpc.biz.services.campaign.api.ILauCampaignService;
import com.bilibili.adp.cpc.biz.services.unit.api.ILauUnitService;
import com.bilibili.adp.cpc.dto.LauUnitBaseDto;
import com.bilibili.adp.legacy.LauCampaignDto;
import com.bilibili.report.platform.api.dto.StatCreativeDto;
import com.bilibili.report.platform.api.soa.ISoaStatCreativeService;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by fanwen<PERSON> on 16/9/29.
 */
@Service
public class WebStatCreativeService extends WebStatBaseService {
    @Autowired
    private ILauUnitService lauUnitService;
    @Autowired
    private ILauCampaignService lauCampaignService;
    @Autowired
    private ISoaStatCreativeService statCreativeService;

    @Override
    protected List<CreativeDateVo> groupByTime(Integer accountId, Timestamp fromTime, Timestamp toTime, Integer salesType) throws ServiceException {
        return convertVosToDtos(statCreativeService.getByAccountIdGroupByTime(accountId, fromTime, toTime, salesType));
    }

    @Override
    protected List<CreativeDateVo> groupByDay(Integer accountId, Timestamp fromTime, Timestamp toTime, Integer salesType) throws ServiceException {
        return convertVosToDtos(statCreativeService.getByAccountIdGroupByDay(accountId, fromTime, toTime, salesType));
    }

    @Override
    protected List<CreativeDateVo> groupByWeek(Integer accountId, Timestamp fromTime, Timestamp toTime, Integer salesType) throws ServiceException {
        return convertVosToDtos(statCreativeService.getByAccountIdGroupByWeek(accountId, fromTime, toTime, salesType));
    }

    @Override
    protected List<CreativeDateVo> groupByMonth(Integer accountId, Timestamp fromTime, Timestamp toTime, Integer salesType) throws ServiceException {
        return convertVosToDtos(statCreativeService.getByAccountIdGroupByMonth(accountId, fromTime, toTime, salesType));
    }

    private List<CreativeDateVo> convertVosToDtos(List<StatCreativeDto> dtos) throws ServiceException {
        if (CollectionUtils.isEmpty(dtos)) {
            return Collections.emptyList();
        }

        Set<Integer> campaignIdSet = new HashSet<>();
        Set<Integer> unitIdSet = new HashSet<>();
        dtos.forEach(dto -> {
            campaignIdSet.add(dto.getCampaignId());
            unitIdSet.add(dto.getUnitId());
        });

        Map<Integer, LauCampaignDto> campaignMap = lauCampaignService.getCampaignDtoMapInIds(Lists.newArrayList(campaignIdSet));
        Map<Integer, LauUnitBaseDto> unitMap = lauUnitService.getBaseDtoMapInIds(Lists.newArrayList(unitIdSet));

        List<CreativeDateVo> result = new ArrayList<>(dtos.size());
        result.addAll(dtos.stream().sorted(Comparator.comparing(StatCreativeDto::getDate))
                .map(dto -> CreativeDateVo.builder()
                        .show_count(dto.getShowAccount())
                        .cost_per_click(dto.getCostPerClick())
                        .cost(dto.getCost())
                        .average_cost_per_thousand(dto.getAverageCostPerThousand())
                        .click_rate(dto.getClickRate())
                        .click_count(dto.getClickCount())
                        .date(dto.getDate())
                        .campaign_id(dto.getCampaignId())
                        .campaign_name(campaignMap.containsKey(dto.getCampaignId()) ? campaignMap.get(dto.getCampaignId()).getCampaignName() : null)
                        .unit_id(dto.getUnitId())
                        .unit_name(unitMap.containsKey(dto.getUnitId()) ? unitMap.get(dto.getUnitId()).getUnitName() : null)
                        .creative_id(dto.getCreativeId())
                        .build()).collect(Collectors.toList()));
        return result;
    }
}
