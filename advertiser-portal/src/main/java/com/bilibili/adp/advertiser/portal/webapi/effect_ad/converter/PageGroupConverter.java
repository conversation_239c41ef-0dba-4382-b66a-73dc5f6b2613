package com.bilibili.adp.advertiser.portal.webapi.effect_ad.converter;

import com.bilibili.adp.advertiser.portal.webapi.v2.vo.responses.LaunchPageGroupReportVo;
import com.bilibili.adp.cpc.biz.services.page_group.report.bos.LaunchPageGroupReportBo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * @ClassName PageConverter
 * <AUTHOR>
 * @Date 2023/6/14 8:43 下午
 * @Version 1.0
 **/
@Mapper
public interface PageGroupConverter {
    PageGroupConverter MAPPER = Mappers.getMapper(PageGroupConverter.class);

    @Mapping(target = "cost", expression = "java(java.math.BigDecimal.valueOf(bo.getCost()))")
    LaunchPageGroupReportVo bo2Vo(LaunchPageGroupReportBo bo);

}
