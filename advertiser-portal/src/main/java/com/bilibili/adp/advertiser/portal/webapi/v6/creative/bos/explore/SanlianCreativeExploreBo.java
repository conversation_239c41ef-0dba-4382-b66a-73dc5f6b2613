package com.bilibili.adp.advertiser.portal.webapi.v6.creative.bos.explore;

import com.bilibili.adp.advertiser.portal.webapi.v6.resource.creative.bos.SanlianCreativeExploreExtraConditionBo;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName SanlianCreativeExploreQueryBo
 * <AUTHOR>
 * @Date 2025/3/13 9:44 下午
 * @Version 1.0
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class SanlianCreativeExploreBo {

    private SanlianCreativeExploreUnitBo unitInfo;
    private List<SanlianCreativeExploreCreativeBo> creativeInfoList;
    private SanlianCreativeExploreExtraConditionBo extraCondition;
}
