package com.bilibili.adp.advertiser.portal.webapi.v6.resource.archive.vos;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/9/3
 * @description 合伙人授权的稿件信息
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PartnerArchiveVo {
    @ApiModelProperty(value = "aid")
    private String aid;
    @ApiModelProperty(value = "bvid")
    private String bvid;
    @ApiModelProperty(value = "稿件第一P的cid")
    private String firstCid;
    @ApiModelProperty(value = "封面")
    private String pic;
    @ApiModelProperty(value = "标题")
    private String title;
    @ApiModelProperty(value = "稿件状态")
    private Integer state;
    @ApiModelProperty(value = "宽度")
    private Integer width;
    @ApiModelProperty(value = "高度")
    private Integer height;
    @ApiModelProperty(value = "稿件总时长")
    private Integer duration;
    @ApiModelProperty(value = "Up主mid")
    private String mid;
    @ApiModelProperty(value = "Up主名称")
    private String name;
    @ApiModelProperty(value = "Up主头像地址")
    private String face;
    @ApiModelProperty(value = "是否有锚点")
    private Integer hasAnchor;
    @ApiModelProperty(value = "是否绑定过评论组件")
    private Integer hasBindComment;
    /**
     * 是否pgc 根据 attribute 计算
     */
    @ApiModelProperty(value = "是否pgc")
    private Boolean isPgc;

}
