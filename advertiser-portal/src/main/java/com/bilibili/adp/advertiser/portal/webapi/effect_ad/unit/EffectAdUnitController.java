package com.bilibili.adp.advertiser.portal.webapi.effect_ad.unit;

import com.bilibili.adp.account.dto.AccountBaseDto;
import com.bilibili.adp.account.service.IQueryAccountService;
import com.bilibili.adp.advertiser.portal.common.BasicController;
import com.bilibili.adp.advertiser.portal.common.Context;
import com.bilibili.adp.advertiser.portal.common.SimpleUnitVo;
import com.bilibili.adp.advertiser.portal.webapi.effect_ad.unit.vo.PromotionPurposeTypeOptionVo;
import com.bilibili.adp.advertiser.portal.webapi.effect_ad.unit.vo.UnitSupportDualBidTwoStageOptimizationVo;
import com.bilibili.adp.advertiser.portal.webapi.effect_ad.vo.unit.GoodsVo;
import com.bilibili.adp.advertiser.portal.webapi.launch.cpc.converter.unit.GoodsConverter;
import com.bilibili.adp.common.Constants;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.bos.unit.QueryUnitBo;
import com.bilibili.adp.cpc.biz.services.account.config.AccountConfig;
import com.bilibili.adp.cpc.biz.services.campaign.CpcCampaignService;
import com.bilibili.adp.cpc.biz.services.campaign.dto.CpcCampaignDto;
import com.bilibili.adp.cpc.biz.services.campaign.dto.QueryCpcCampaignDto;
import com.bilibili.adp.cpc.biz.services.creative.config.AccLabelConfig;
import com.bilibili.adp.cpc.biz.services.unit.AdpCpcUnitService;
import com.bilibili.adp.cpc.biz.services.unit.bos.GoodsBo;
import com.bilibili.adp.cpc.biz.services.unit.dto.CpcUnitDto;
import com.bilibili.adp.cpc.core.LaunchUnitExtraService;
import com.bilibili.adp.cpc.core.LaunchUnitGoodsService;
import com.bilibili.adp.cpc.core.LaunchUnitV1Service;
import com.bilibili.adp.cpc.enums.AdpVersion;
import com.bilibili.adp.cpc.enums.ad.PromotionContentTypeEnum;
import com.bilibili.adp.cpc.enums.ad.PromotionPurposeType;
import com.bilibili.adp.cpc.wrapper.LaunchBatchOperationService;
import com.bilibili.adp.cpc.wrapper.bos.BatchUnitWrapperBo;
import com.bilibili.adp.launch.api.common.UnitStatus;
import com.bilibili.adp.resource.api.account.label.IAccountLabelService;
import com.bilibili.adp.resource.api.slot_group.IResSlotGroupService;
import com.bilibili.adp.resource.api.slot_group.QueryTemplateLaunchTypeMappingDto;
import com.bilibili.adp.resource.api.slot_group.ResSlotGroupTemplateMappingDto;
import com.bilibili.adp.web.framework.core.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/web_api/v1/effect_ad/unit")
@Api(value = "/effect_ad/unit")
@Slf4j
@RequiredArgsConstructor
public class EffectAdUnitController extends BasicController {

    private static final List<Integer> EFFECT_AD_ADP_VERSION_LIST = new ArrayList<>(Arrays.asList(
            AdpVersion.FLY_BANNER.getKey(),
            AdpVersion.FLY_BANNER_NEW.getKey(),
            AdpVersion.MIDDLE.getKey(),
            AdpVersion.CPC_FLY_MERGE.getKey())
    );

    private final CpcCampaignService cpcCampaignService;
    private final AdpCpcUnitService adpCpcUnitService;
    private final IResSlotGroupService resSlotGroupService;
    private final IAccountLabelService accountLabelService;
    private final LaunchBatchOperationService launchBatchOperationService;
    private final LaunchUnitV1Service launchUnitV1Service;
    private final LaunchUnitGoodsService launchUnitGoodsService;
    private final IQueryAccountService queryAccountService;
    private final AccountConfig accountConfig;
    private final LaunchUnitExtraService launchUnitExtraService;
    private final AccLabelConfig accLabelConfig;

    @Value("${platform.effect_ad.support.adp2.label.id:398}")
    private Integer effectAdSupportAdp2LabelId;

    @ApiOperation("获取单元历史数据")
    @GetMapping("/batch_history")
    public Response<List<BatchUnitWrapperBo>> batchHistory(@ApiIgnore Context context,
                                                           @RequestParam("unit_ids") List<Integer> unitIds,
                                                           @RequestParam("dim") String dim) {
        return Response.SUCCESS(launchBatchOperationService.listUnitTarget(unitIds, dim, context.getAccountId()));
    }

    @ApiOperation("获取单元历史数据")
    @GetMapping("/batch_history_v6")
    public Response<List<BatchUnitWrapperBo>> batchHistoryV6(@ApiIgnore Context context,
                                                             @RequestParam("unit_ids") List<Integer> unitIds,
                                                             @RequestParam("dim") String dim) {
        return Response.SUCCESS(launchBatchOperationService.listUnitTargetV6(unitIds, dim, context.getAccountId()));
    }

    @ApiOperation(value = "获取单元下拉列表")
    @RequestMapping(value = "/drop_box", method = RequestMethod.GET)
    public Response<List<SimpleUnitVo>> getUnitDropBox(
            @ApiIgnore Context context,
            @RequestParam(value = "campaign_id") List<Integer> campaignIdList,
            @RequestParam(required = false, value = "template_id") Integer templateId,
            @RequestParam(required = false, value = "no_programmatic", defaultValue = "false") boolean noProgrammatic,
            @RequestParam(value = "source", required = false, defaultValue = "0") Integer source) {
        List<Integer> adpVersions = AdpVersion.MIDDLE_AND_MERGE_LIST;
        final AccountBaseDto accountBaseDto = queryAccountService.getAccountBaseDtoByIdFromCache(context.getAccountId());
        Assert.notNull(accountBaseDto, "账号不能为空");
        boolean effectAdSupportAdp2 = accountLabelService.isAccountIdsInLabels(Collections.singletonList(context.getAccountId()), Collections.singletonList(effectAdSupportAdp2LabelId));
        // 如果传了1 代表是新建的时候 获取下拉列表，不允许新建2 4，只展现5
        if (Utils.isPositive(accountBaseDto.getIsSupportContent()) || effectAdSupportAdp2 && Objects.equals(source, 0)) {
            adpVersions = EFFECT_AD_ADP_VERSION_LIST;
        }
//        QueryCpcUnitDto query = QueryCpcUnitDto
//                .builder()
//                .accountId(context.getAccountId())
//                .campaignIds(campaignIdList)
//                .unitStatusList(UnitStatus.NON_DELETED_UNIT_STATUS_LIST)
//                .orderBy(Constants.MTIME_DESC)
//                .adpVersions(adpVersions)
//                .build();
//
//        List<CpcUnitDto> dtos = cpcUnitService.queryCpcUnit(query);
        QueryUnitBo query = QueryUnitBo
                .builder()
                .accountIds(Collections.singletonList(context.getAccountId()))
                .campaignIds(campaignIdList)
                .unitStatusList(UnitStatus.NON_DELETED_UNIT_STATUS_LIST)
                .orderBy(Constants.MTIME_DESC)
                .adpVersions(adpVersions)
                .build();
        List<CpcUnitDto> dtos = launchUnitV1Service.listUnits(query);
        if (noProgrammatic) {
            dtos = dtos.stream()
                    .filter(x -> !adpCpcUnitService.isProgrammatic(x.getUnitId()))
                    .collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(dtos)) {
            Response.SUCCESS(Collections.emptyList());
        }

        if (!Utils.isPositive(templateId)) {
            return Response.SUCCESS(dtos.stream()
                    .map(EffectAdUnitMapper.INSTANCE::mapToSimpleUnitVo)
                    .collect(Collectors.toList()));
        }

        Map<Integer, List<SimpleUnitVo>> unitPerSlotGroupId = dtos.stream()
                .collect(Collectors.groupingBy(CpcUnitDto::getSlotGroup,
                        Collectors.mapping(EffectAdUnitMapper.INSTANCE::mapToSimpleUnitVo, Collectors.toList())));

        List<Integer> slotGroupIds = dtos
                .stream()
                .map(CpcUnitDto::getSlotGroup)
                .distinct()
                .collect(Collectors.toList());

        QueryTemplateLaunchTypeMappingDto qtlm = QueryTemplateLaunchTypeMappingDto
                .builder()
                .slotGroupIds(slotGroupIds)
                .build();

        if (!CollectionUtils.isEmpty(campaignIdList)
                && campaignIdList.stream().anyMatch(Utils::isPositive)) {
            List<CpcCampaignDto> campaignList = cpcCampaignService.queryCpcCampaign(QueryCpcCampaignDto.builder()
                    .campaignIds(campaignIdList)
                    .build());
            qtlm.setPromotionPurposeTypes(campaignList.stream().map(CpcCampaignDto::getPromotionPurposeType)
                    .distinct()
                    .collect(Collectors.toList()));
        }

        List<ResSlotGroupTemplateMappingDto> validSlotGroups = resSlotGroupService.querySlotGroupTemplateMappingInSlotGroupIds(qtlm);

        if (CollectionUtils.isEmpty(validSlotGroups)) {
            return Response.SUCCESS(Collections.emptyList());
        }

        List<Integer> validSlotGroupIds = validSlotGroups
                .stream()
                .filter(sg -> !CollectionUtils.isEmpty(sg.getTemplates()) && sg.getTemplates().stream().filter(t -> templateId.equals(t.getTemplateId())).count() > 0)
                .map(ResSlotGroupTemplateMappingDto::getSlotGroupId)
                .collect(Collectors.toList());

        return Response.SUCCESS(
                unitPerSlotGroupId
                        .entrySet()
                        .stream()
                        .filter(e -> validSlotGroupIds.contains(e.getKey()))
                        .flatMap(e -> e.getValue().stream())
                        .collect(Collectors.toList()));
    }

    @RequestMapping(value = "/goods")
    public Response<GoodsVo> goodsList(@ApiIgnore Context context,
                                       @RequestParam(value = "item_id") String itemId) {
        Assert.notNull(context, "context should not be null");
        Assert.isTrue(Utils.isPositive(context.getAccountId()), "account id should be positive");
        Assert.isTrue(StringUtils.isNotEmpty(itemId), "item id 不能为空");
        final GoodsBo goods = launchUnitGoodsService.getGoods(context.getAccountId(), Long.valueOf(itemId));
        return Response.SUCCESS(GoodsConverter.MAPPER.bo2Vo(goods));
    }

    @GetMapping(value = "/promotion_purpose_type_options")
    public Response<List<PromotionPurposeTypeOptionVo>> promotionPurposeTypeOptions(@ApiIgnore Context context) {

        List<Integer> accountLabelIds = accountLabelService.getLabelIdsByAccountId(context.getAccountId());
        Boolean supportAdp6 = accountConfig.supportSanlianMergeAdp6(accountLabelIds);
        if (supportAdp6) {
            List<PromotionPurposeTypeOptionVo> adp6ResultList = Arrays.stream(PromotionContentTypeEnum.values())
                    .filter(type -> Utils.isPositive(type.getCode()))
                    .map(type -> {
                        return PromotionPurposeTypeOptionVo.builder()
                                .code(type.getCode())
                                .desc(type.getName())
                                .build();
                    }).collect(Collectors.toList());
            return Response.SUCCESS(adp6ResultList);
        }

        final List<PromotionPurposeType> unitPromotionPurposeTypes = Arrays.asList(
                PromotionPurposeType.ARCHIVE_CONTENT,
                PromotionPurposeType.DYNAMIC,
                PromotionPurposeType.GOODS_LIVE,
                PromotionPurposeType.GOODS,
                PromotionPurposeType.OGV
        );
        return Response.SUCCESS(
                unitPromotionPurposeTypes
                        .stream()
                        .map(promotionPurposeType -> PromotionPurposeTypeOptionVo.builder()
                                .code(promotionPurposeType.getCode())
                                .desc(promotionPurposeType.getDesc())
                                .build())
                        .collect(Collectors.toList())
        );
    }

    @ApiOperation(value = "(单个单元)是否支持开启双出价两阶段优化")
    @GetMapping(value = "/is_support_dual_bid_two_stage_optimization")
    public Response<Boolean> isSupportDualBidTwoStageOptimization(@ApiIgnore Context context,
                                                                  @RequestParam(value = "dual_bid_type") Integer dualBidType,
                                                                  @RequestParam(value = "sales_type") Integer salesType,
                                                                  @RequestParam(value = "ocpx_target") Integer ocpxTarget,
                                                                  @RequestParam(value = "ocpx_target_two") Integer ocpxTargetTwo) {
        final Boolean support = launchUnitV1Service.isSupportDualBidTwoStageOptimization(context.getAccountId(), dualBidType, salesType, ocpxTarget, ocpxTargetTwo);
        return Response.SUCCESS(support);
    }

    @ApiOperation(value = "(多个单元)是否支持开启双出价两阶段优化")
    @GetMapping(value = "/are_support_dual_bid_two_stage_optimization")
    public Response<List<UnitSupportDualBidTwoStageOptimizationVo>> areSupportDualBidTwoStageOptimization(@ApiIgnore Context context,
                                                                                                          @RequestParam(value = "unit_ids") List<Integer> unitIds) {
        final Map<Integer, Boolean> support = launchUnitV1Service.areSupportDualBidTwoStageOptimization(getOperator(context).getOperatorId(), unitIds);
        final List<UnitSupportDualBidTwoStageOptimizationVo> response = support.entrySet()
                .stream()
                .map(entry -> UnitSupportDualBidTwoStageOptimizationVo.builder()
                        .unitId(entry.getKey())
                        .isSupport(entry.getValue())
                        .build())
                .collect(Collectors.toList());
        return Response.SUCCESS(response);
    }
}
