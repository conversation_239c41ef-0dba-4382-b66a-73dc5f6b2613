package com.bilibili.adp.advertiser.portal.webapi.launch.cpc.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShopGoodsVo {

    private Integer id;

    private String name;

    private Integer status;

    private String status_desc;

    private BigDecimal price;

    private String image_url;

    private String jump_url;

    private String category_name;

    private String product_type_name;

}