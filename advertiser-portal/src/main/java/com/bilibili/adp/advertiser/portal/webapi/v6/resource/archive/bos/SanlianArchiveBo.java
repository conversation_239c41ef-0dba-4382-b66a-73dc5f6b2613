package com.bilibili.adp.advertiser.portal.webapi.v6.resource.archive.bos;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * @ClassName SanlianArchiveBo
 * <AUTHOR>
 * @Date 2024/4/1 9:47 下午
 * @Version 1.0
 **/
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SanlianArchiveBo {
    /**
     * aid
     */
    @ApiModelProperty(value = "aid")
    private String aid;
    /**
     * bvid
     */
    @ApiModelProperty(value = "bvid")
    private String bvid;
    /**
     * 稿件第一P的cid
     */
    @ApiModelProperty(value = "稿件第一P的cid")
    private String firstCid;
    /**
     * 是否转载 1-原创 2-转载 0-历史遗留
     */
    @ApiModelProperty(value = "是否转载 1-原创 2-转载 0-历史遗留")
    private Integer copyright;
    @ApiModelProperty(value = "autoplay")
    private Integer autoplay;
    /**
     * 封面
     */
    @ApiModelProperty(value = "封面")
    private String pic;
    /**
     * 稿件发布时间
     */
    @ApiModelProperty(value = "稿件发布时间")
    private Long pubDate;
    /**
     * 用户提交时间
     */
    @ApiModelProperty(value = "用户提交时间")
    private Long ctime;
    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String title;
    /**
     * 稿件简介
     */
    @ApiModelProperty(value = "稿件简介")
    private String desc;
    /**
     * 稿件状态
     */
    @ApiModelProperty(value = "稿件状态")
    private Integer state;
    /**
     * 稿件状态详情
     */
    @ApiModelProperty(value = "稿件状态详情")
    private String stateDesc;
    /**
     * 稿件属性
     */
    @ApiModelProperty(value = "稿件属性")
    private Integer attribute;
    /**
     * 是否pgc 根据 attribute 计算
     */
    @ApiModelProperty(value = "是否pgc")
    private Boolean isPgc;
    /**
     * 稿件总时长
     */
    @ApiModelProperty(value = "稿件总时长")
    private Integer duration;
    //region 稿件作者信息
    /**
     * Up主mid
     */
    @ApiModelProperty(value = "Up主mid")
    private String mid;
    /**
     * Up主名称
     */
    @ApiModelProperty(value = "Up主名称")
    private String name;
    /**
     * Up主头像地址 绝对地址
     */
    @ApiModelProperty(value = "Up主头像地址")
    private String face;
    //endregion
    //region 统计信息
    /**
     * 播放数
     */
    @ApiModelProperty(value = "播放数")
    private Integer view;
    /**
     * 弹幕数
     */
    @ApiModelProperty(value = "弹幕数")
    private Integer danmaku;
    /**
     *  评论数
     */
    @ApiModelProperty(value = "评论数")
    private Integer reply;
    /**
     * 收藏数
     */
    @ApiModelProperty(value = "收藏数")
    private Integer fav;
    /**
     * 投币数
     */
    @ApiModelProperty(value = "投币数")
    private Integer coin;
    /**
     *  分享数
     */
    @ApiModelProperty(value = "分享数")
    private Integer share;
    /**
     * 当前排名
     */
    @ApiModelProperty(value = "当前排名")
    private Integer nowRank;
    /**
     * 历史最高排名
     */
    @ApiModelProperty(value = "历史最高排名")
    private Integer hisRank;
    /**
     * 点赞数
     */
    @ApiModelProperty(value = "点赞数")
    private Integer like;
    //endregion
    //region 视频分辨率
    /**
     * 宽度
     */
    @ApiModelProperty(value = "宽度")
    private Integer width;
    /**
     * 高度
     */
    @ApiModelProperty(value = "高度")
    private Integer height;
    /**
     * 是否翻转
     */
    @ApiModelProperty(value = "是否翻转")
    private Integer rotate;
    //endregion
    /**
     * 投放视频类型 1-商业内容 3-其他
     */
    @ApiModelProperty(value = "投放视频类型 1-商业内容 3-其他")
    private Integer launchVideoType;
    /**
     * 是否是商单稿件 0-非商业 1-商业
     */
    @ApiModelProperty("是否是商单稿件 0-非商业 1-商业")
    private Integer isBusinessVideo;
    /**
     * 是否ott稿件
     */
    @ApiModelProperty("是否ott稿件")
    private Boolean isOtt;

    /**
     * 是否逃单
     */
    @ApiModelProperty(value = "是否逃单 0-非逃单 1-逃单")
    private Integer isEscape;

    @ApiModelProperty(value = "是否支持投放 0-支持投放 1-不支持投放")
    private Integer isSupportAdvertising;

    @ApiModelProperty(value = "稿件直播预约id")
    private Long sid;

    // 三连推广新字段
    @ApiModelProperty(value = "支持的黄车组件类型")
    private List<SanlianSupportYellowCarCpTypeBo> supportYellowCarTypeList;
    @ApiModelProperty("是否带货内容")
    private Integer isGoodsArchive;
    @ApiModelProperty("cid稿件非法")
    private Integer isCidArcInvalid;
    @ApiModelProperty("带货稿件非法")
    private Integer isGoodsArcInvalid;
    @ApiModelProperty("是否非法原生投放")
    private Integer isBiliNativeInvalid;
    @ApiModelProperty("是否部分自动播放场景异常")
    private Integer isPartAutoPlayInvalid;
    @ApiModelProperty("是否全部自动播放场景异常")
    private Integer isAllAutoPlayInvalid;
    @ApiModelProperty("稿件原生状态")
    private Integer nativeStatus;
    @ApiModelProperty("稿件原生状态描述")
    private String nativeStatusDesc;
    @ApiModelProperty(value = "是否有锚点")
    private Integer hasAnchor;
    @ApiModelProperty("原生锚点状态")
    private Integer nativeAnchorStatus;
    @ApiModelProperty("绑定评论组件状态 0-未绑定 1-已绑定")
    private Integer hasBindComment;
    @ApiModelProperty("oCPX转化目标")
    private Integer ocpcTarget;
    @ApiModelProperty("线索类型")
    private Integer clueType;


    private Integer hasSanlianTopComment;
    // 以下目前暂时京东专用 目前暂时仅支持新版商单查询
    @ApiModelProperty("是否带货置顶")
    private Integer hasGoodsTopComment;
    @ApiModelProperty("是否有非京东带货评论")
    private Integer hasNot_jd_GoodsComment;
    @ApiModelProperty("是否有花火评论")
    private Integer hasHuahuoComment;
    // 三连避让邀约  是否有符合状态的邀约评论
    @ApiModelProperty("三连避让邀约  是否有符合状态的邀约评论")
    private Integer hasInviteComment;

    @ApiModelProperty("是否是课堂稿件， 用于选择锚点")
    private Boolean isPugv;
    @ApiModelProperty("课堂稿件非法")
    private Integer isPugvSelect;

    @ApiModelProperty("授权稿件标题，用于展示或搜索")
    private String authTitle;


    @ApiModelProperty("授权来源")
    private Integer authSource;
}
