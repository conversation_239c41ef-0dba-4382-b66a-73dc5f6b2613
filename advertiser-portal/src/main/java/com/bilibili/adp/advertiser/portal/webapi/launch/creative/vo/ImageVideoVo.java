package com.bilibili.adp.advertiser.portal.webapi.launch.creative.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2016年10月1日
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ImageVideoVo {
	
    private String image_url;
    private String video_url;
    private String ext_image_url;
    private String image_hash;
    private String video_hash;
    private String ext_image_hash;
    private Integer flow_weight_state;
    private String image_md5;
    private ImageInfoVo imageInfo;

}
