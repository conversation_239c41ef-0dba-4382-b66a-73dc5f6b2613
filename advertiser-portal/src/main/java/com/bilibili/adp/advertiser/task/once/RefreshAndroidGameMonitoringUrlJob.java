package com.bilibili.adp.advertiser.task.once;

import com.bapis.ad.pandora.resource.MonitorType;
import com.bilibili.adp.cpc.core.LaunchUnitGameV1Service;
import com.bilibili.adp.cpc.dao.querydsl.pos.LauArchiveCommentConversionComponentPo;
import com.bilibili.adp.cpc.dao.querydsl.pos.LauCreativeMonitoringPo;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitGamePo;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitMonitoringPo;
import com.bilibili.bjcom.querydsl.BaseQueryFactory;
import com.bilibili.crm.platform.api.account.dto.AccountBaseDto;
import com.bilibili.crm.platform.soa.ISoaQueryAccountService;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.bilibili.adp.cpc.biz.constants.Constants.INT_FALSE;
import static com.bilibili.adp.cpc.biz.constants.Constants.INT_TRUE;
import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.AD_BQF;
import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.AD_CORE_BQF;
import static com.bilibili.adp.cpc.dao.querydsl.QLauArchiveCommentConversionComponent.lauArchiveCommentConversionComponent;
import static com.bilibili.adp.cpc.dao.querydsl.QLauCreativeMonitoring.lauCreativeMonitoring;
import static com.bilibili.adp.cpc.dao.ad_core.querydsl.QLauUnitGame.lauUnitGame;
import static com.bilibili.adp.cpc.dao.ad_core.querydsl.QLauUnitMonitoring.lauUnitMonitoring;


@Component
@Slf4j
@JobHandler("RefreshAndroidGameMonitoringUrlJob")
public class RefreshAndroidGameMonitoringUrlJob extends IJobHandler {

    private final Integer batchSize = 100;
    private final long interval = 500L;

    @Autowired
    private ISoaQueryAccountService soaQueryAccountService;
    @Autowired
    private LaunchUnitGameV1Service launchUnitGameV1Service;
    @Resource(name = AD_CORE_BQF)
    private BaseQueryFactory adCoreBqf;
    @Resource(name = AD_BQF)
    private BaseQueryFactory adBqf;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        XxlJobLogger.log("********  start job: RefreshAndroidGameMonitoringUrlJob....param={}  ******", param);
        List<String> params = new ArrayList<>();
        if (!param.isEmpty()) {
            params = Arrays.stream(Strings.split(param, ',')).map(String::trim).filter(r -> !r.isEmpty())
                    .collect(Collectors.toList());
        }
        String scene = params.isEmpty() ? "" : params.get(0);
        List<String> ids = params.isEmpty() ? new ArrayList<>() : params.subList(1, params.size());

        ArrayList<Integer> failedIds = new ArrayList<>();
        if (!ids.isEmpty()) {
            Lists.partition(ids, batchSize).forEach(idChunk -> {
                ArrayList<Integer> list = refreshAndroidGameMonitoringUrlService(scene, idChunk);
                failedIds.addAll(list);
            });
        } else {
            failedIds.addAll(refreshAndroidGameMonitoringUrlService(scene, ids));
        }

        XxlJobLogger.log("********  end job: RefreshAndroidGameMonitoringUrlJob....  ******");
        if (!failedIds.isEmpty()) {
            return new ReturnT<>(ReturnT.FAIL_CODE, failedIds.toString());
        }
        return SUCCESS;
    }

    private ArrayList<Integer> refreshAndroidGameMonitoringUrlService(String scene, List<String> ids) {
        ArrayList<Integer> failedIds = new ArrayList<>();
        switch (RefreshAndroidGameMonitoringUrlScene.getByString(scene)) {
            case LAU_UNIT_MONITORING:
                failedIds = refreshUnitMonitoring(ids);
                break;
            case LAU_CREATIVE_MONITORING:
                failedIds = refreshCreativeMonitoring(ids);
                break;
            case LAU_ARCHIVE_COMMENT_CONVERSION_COMPONENT:
                failedIds = refreshCommentComponent(ids);
                break;
        }
        return failedIds;
    }

    private ArrayList<Integer> refreshCreativeMonitoring(List<String> ids) {
        ArrayList<Integer> failedUnitIds = new ArrayList<>();
        List<Integer> unitIds = ids.stream().map(Integer::parseInt).collect(Collectors.toList());
        Integer anchorId = 0;
        while (true) {
            List<LauUnitGamePo> lauUnitGamePos = adCoreBqf.selectFrom(lauUnitGame)
                    .whereIfNotEmpty(unitIds, lauUnitGame.unitId::in).where(lauUnitGame.isDeleted.eq(INT_FALSE))
                    .where(lauUnitGame.id.gt(anchorId)).orderBy(lauUnitGame.id.asc()).limit(batchSize).fetch();
            if (lauUnitGamePos.isEmpty()) {
                break;
            }
            anchorId = lauUnitGamePos.get(lauUnitGamePos.size() - 1).getId();

            List<Integer> unitIdList = lauUnitGamePos.stream().map(LauUnitGamePo::getUnitId).distinct()
                    .collect(Collectors.toList());
            List<LauCreativeMonitoringPo> lauCreativeMonitoringPos = adBqf.selectFrom(lauCreativeMonitoring)
                    .whereIfNotEmpty(unitIdList, lauCreativeMonitoring.unitId::in)
                    .where(lauCreativeMonitoring.type.eq(MonitorType.MONITOR_GAME_CLICK_VALUE))
                    .where(lauCreativeMonitoring.isDeleted.eq(INT_FALSE)).fetch();
            List<Integer> accountIds = lauUnitGamePos.stream().map(LauUnitGamePo::getAccountId).distinct()
                    .collect(Collectors.toList());
            Map<Integer, AccountBaseDto> accountBaseDtoMapInIds = soaQueryAccountService.getAccountBaseDtoMapInIds(
                    accountIds);
            Map<Integer, String> unitUrlMap = lauUnitGamePos.stream().collect(Collectors.toMap(LauUnitGamePo::getUnitId,
                    p -> launchUnitGameV1Service.genGameHiddenMonitoringUrl(p.getGameBaseId(), p.getPlatformType(),
                            p.getSubPkg(), Objects.equals(accountBaseDtoMapInIds.getOrDefault(p.getAccountId(),
                                    AccountBaseDto.builder().build()).getIsInner(), INT_TRUE))));
            lauCreativeMonitoringPos.forEach(lauUnitMonitoringPo -> {
                if (legal(lauUnitMonitoringPo.getUrl(), unitUrlMap.get(lauUnitMonitoringPo.getUnitId()),
                        Long.valueOf(lauUnitMonitoringPo.getUnitId()))) {
                    lauUnitMonitoringPo.setUrl(unitUrlMap.get(lauUnitMonitoringPo.getUnitId()));
                } else {
                    failedUnitIds.add(lauUnitMonitoringPo.getUnitId());
                }
            });
            adBqf.update(lauCreativeMonitoring).updateBeans(lauCreativeMonitoringPos);
            try {
                Thread.sleep(interval);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
        return failedUnitIds;
    }

    private ArrayList<Integer> refreshCommentComponent(List<String> ids) {
        ArrayList<Integer> failedIds = new ArrayList<>();
        List<Long> componentIds = ids.stream().map(Long::parseLong).collect(Collectors.toList());
        Long anchorId = 0L;
        while (true) {
            List<LauArchiveCommentConversionComponentPo> lauArchiveCommentConversionComponentPos = adBqf.selectFrom(
                            lauArchiveCommentConversionComponent)
                    .whereIfNotEmpty(componentIds, lauArchiveCommentConversionComponent.id::in)
                    .where(lauArchiveCommentConversionComponent.gameBaseId.ne(0))
                    .where(lauArchiveCommentConversionComponent.id.gt(anchorId))
                    .orderBy(lauArchiveCommentConversionComponent.id.asc()).limit(batchSize).fetch();
            if (lauArchiveCommentConversionComponentPos.isEmpty()) {
                break;
            }
            anchorId = lauArchiveCommentConversionComponentPos.get(lauArchiveCommentConversionComponentPos.size() - 1)
                    .getId();

            List<Integer> accountIds = lauArchiveCommentConversionComponentPos.stream()
                    .map(LauArchiveCommentConversionComponentPo::getAccountId).distinct().collect(Collectors.toList());
            Map<Integer, AccountBaseDto> accountBaseDtoMapInIds = soaQueryAccountService.getAccountBaseDtoMapInIds(
                    accountIds);
            lauArchiveCommentConversionComponentPos.forEach(lauArchiveCommentConversionComponentPo -> {
                String monitoringUrl = launchUnitGameV1Service.genGameHiddenMonitoringUrl(
                        lauArchiveCommentConversionComponentPo.getGameBaseId(),
                        lauArchiveCommentConversionComponentPo.getGamePlatformType(),
                        lauArchiveCommentConversionComponentPo.getSubPkg(), Objects.equals(
                                accountBaseDtoMapInIds.getOrDefault(
                                        lauArchiveCommentConversionComponentPo.getAccountId(),
                                        AccountBaseDto.builder().build()).getIsInner(), INT_TRUE));
                if (legal(lauArchiveCommentConversionComponentPo.getGameMonitoringUrl(), monitoringUrl,
                        lauArchiveCommentConversionComponentPo.getId())) {
                    lauArchiveCommentConversionComponentPo.setGameMonitoringUrl(monitoringUrl);
                } else {
                    failedIds.add(Math.toIntExact(lauArchiveCommentConversionComponentPo.getId()));
                }
            });
            adBqf.update(lauArchiveCommentConversionComponent).updateBeans(lauArchiveCommentConversionComponentPos);
            try {
                Thread.sleep(interval);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
        return failedIds;
    }

    private ArrayList<Integer> refreshUnitMonitoring(List<String> ids) {
        ArrayList<Integer> failedUnitIds = new ArrayList<>();
        List<Integer> unitIds = ids.stream().map(Integer::parseInt).collect(Collectors.toList());
        Integer anchorId = 0;
        while (true) {
            List<LauUnitGamePo> lauUnitGamePos = adCoreBqf.selectFrom(lauUnitGame)
                    .whereIfNotEmpty(unitIds, lauUnitGame.unitId::in).where(lauUnitGame.isDeleted.eq(INT_FALSE))
                    .where(lauUnitGame.id.gt(anchorId)).orderBy(lauUnitGame.id.asc()).limit(batchSize).fetch();
            if (lauUnitGamePos.isEmpty()) {
                break;
            }
            anchorId = lauUnitGamePos.get(lauUnitGamePos.size() - 1).getId();

            List<Integer> unitIdList = lauUnitGamePos.stream().map(LauUnitGamePo::getUnitId).distinct()
                    .collect(Collectors.toList());
            List<LauUnitMonitoringPo> lauUnitMonitoringPos = adCoreBqf.selectFrom(lauUnitMonitoring)
                    .whereIfNotEmpty(unitIdList, lauUnitMonitoring.unitId::in)
                    .where(lauUnitMonitoring.type.eq(MonitorType.MONITOR_GAME_CLICK_VALUE)).fetch();
            List<Integer> accountIds = lauUnitGamePos.stream().map(LauUnitGamePo::getAccountId).distinct()
                    .collect(Collectors.toList());
            Map<Integer, AccountBaseDto> accountBaseDtoMapInIds = soaQueryAccountService.getAccountBaseDtoMapInIds(
                    accountIds);
            Map<Integer, String> unitUrlMap = lauUnitGamePos.stream().collect(Collectors.toMap(LauUnitGamePo::getUnitId,
                    p -> launchUnitGameV1Service.genGameHiddenMonitoringUrl(p.getGameBaseId(), p.getPlatformType(),
                            p.getSubPkg(), Objects.equals(accountBaseDtoMapInIds.getOrDefault(p.getAccountId(),
                                    AccountBaseDto.builder().build()).getIsInner(), INT_TRUE))));
            lauUnitMonitoringPos.forEach(lauUnitMonitoringPo -> {
                if (legal(lauUnitMonitoringPo.getUrl(), unitUrlMap.get(lauUnitMonitoringPo.getUnitId()),
                        Long.valueOf(lauUnitMonitoringPo.getUnitId()))) {
                    lauUnitMonitoringPo.setUrl(unitUrlMap.get(lauUnitMonitoringPo.getUnitId()));
                } else {
                    failedUnitIds.add(lauUnitMonitoringPo.getUnitId());
                }
            });
            adCoreBqf.update(lauUnitMonitoring).updateBeans(lauUnitMonitoringPos);
            try {
                Thread.sleep(interval);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
        return failedUnitIds;
    }

    private boolean legal(String url1, String url2, Long id) {
        UriComponents build = UriComponentsBuilder.fromUriString(url1).build();
        if (!Objects.equals(build.getHost(), "ad-bili-data.biligame.com")) {
            XxlJobLogger.log("path not match at id = {}", id);
            return false;
        }
        MultiValueMap<String, String> params1 = build.getQueryParams();
        MultiValueMap<String, String> params2 = UriComponentsBuilder.fromUriString(url2).build().getQueryParams();
        if (!params1.get("game_base_id").equals(params2.get("game_base_id"))) {
            XxlJobLogger.log("game_base_id not match at id = {}", id);
            return false;
        }
        if (!params1.get("platform_type").equals(params2.get("platform_type"))) {
            XxlJobLogger.log("platform_type not match at id = {}", id);
            return false;
        }
        if (!Objects.equals(params1.get("sycp_pkg_type"), (params2.get("sycp_pkg_type")))) {
            XxlJobLogger.log("sycp_pkg_type not match at id = {}", id);
            return false;
        }
        return true;
    }

    @Getter
    @AllArgsConstructor
    private enum RefreshAndroidGameMonitoringUrlScene {
        LAU_UNIT_MONITORING("lau_unit_monitoring"),
        LAU_CREATIVE_MONITORING("lau_creative_monitoring"),
        LAU_ARCHIVE_COMMENT_CONVERSION_COMPONENT("lau_archive_comment_conversion_component"),
        ;

        private final String scene;

        public static RefreshAndroidGameMonitoringUrlScene getByString(String s) {
            for (RefreshAndroidGameMonitoringUrlScene scene : values()) {
                if (scene.getScene().equals(s)) {
                    return scene;
                }
            }
            throw new IllegalArgumentException(s);
        }
    }
}