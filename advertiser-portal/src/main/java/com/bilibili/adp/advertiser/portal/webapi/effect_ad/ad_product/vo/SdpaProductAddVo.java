package com.bilibili.adp.advertiser.portal.webapi.effect_ad.ad_product.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@ApiModel
public class SdpaProductAddVo {

    @ApiModelProperty(name = "产品库id")
    private Long libraryId;

    @ApiModelProperty(name = "教育产品列表")
    private List<SdpaProductEducationVo> educationVos;

    @ApiModelProperty(name = "借贷产品列表")
    private List<SdpaProductLoanVo> loanVos;

    @ApiModelProperty(name = "小说产品列表")
    private List<SdpaProductNovelVo> novelVos;

    @ApiModelProperty(name = "短剧列表")
    private List<SdpaShortFilmVo> shortFilmVos;

    @ApiModelProperty(name = "汽车产品列表")
    private List<SdpaProductCarVo> carVos;

    @ApiModelProperty(name = "电商产品列表")
    private List<SdpaProductECommerceVo> eCommerceVos;

    @ApiModelProperty(name = "医疗产品列表")
    private List<SdpaProductMedicalVo> medicalVos;

    @ApiModelProperty(name = "家具企业列表")
    private List<SdpaProductHouseVo> houseVos;
}
