package com.bilibili.adp.advertiser.portal.webapi.v6.campaign.mapper;

import com.bilibili.adp.advertiser.portal.webapi.v6.campaign.bos.SanlianGetCampaignRespBo;
import com.bilibili.adp.advertiser.portal.webapi.v6.campaign.bos.info.CampaignInfoBo;
import com.bilibili.adp.cpc.enums.sanlian.SanlianPromotionPurposeTypeEnum;
import com.bilibili.adp.v6.campaign.bo.CampaignBo;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.factory.Mappers;

import java.util.function.Function;

@Mapper(collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CampaignMapper {
    CampaignMapper MAPPER = Mappers.getMapper(CampaignMapper.class);

    @Mapping(target = "campaign", source = "x")
    @Mapping(target = "campaignInfo", source = ".")
    SanlianGetCampaignRespBo toCompoundBo(CampaignBo x);

    @Mapping(target = "promotionPurposeTypeDesc",
        expression = "java(CONVERT_PPT_DESC.apply(x.getPromotionPurposeType()))")
    CampaignInfoBo toExtBo(CampaignBo x);

    Function<Integer, String> CONVERT_PPT_DESC = type ->
            SanlianPromotionPurposeTypeEnum.getByCode(type).getName();
}
