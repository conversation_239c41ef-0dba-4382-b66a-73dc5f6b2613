package com.bilibili.adp.advertiser.portal.webapi.v6.unit.mapper;

import com.bilibili.adp.advertiser.portal.webapi.launch.cpc.converter.unit.GoodsConverter;
import com.bilibili.adp.advertiser.portal.webapi.v6.unit.bos.info.SanlianGoodsInfoBo;
import com.bilibili.adp.cpc.biz.services.unit.bos.GoodsBo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.function.Function;

/**
 * @see GoodsConverter
 * @ClassName GoodsMapper
 * <AUTHOR>
 * @Date 2024/3/6 10:02 下午
 * @Version 1.0
 **/
@Mapper
public interface GoodsMapper {
    GoodsMapper MAPPER = Mappers.getMapper(GoodsMapper.class);

    Function<Long, BigDecimal> FEN_2_YUAN_FUNCTION = fen -> BigDecimal.valueOf(fen).divide(BigDecimal.valueOf(100), RoundingMode.HALF_UP);

    @Mapping(target = "price", expression = "java(FEN_2_YUAN_FUNCTION.apply(goods.getPrice()))")
    @Mapping(target = "reservePrice", expression = "java(FEN_2_YUAN_FUNCTION.apply(goods.getReservePrice()))")
    SanlianGoodsInfoBo bo2Vo(GoodsBo goods);

}
