package com.bilibili.adp.advertiser.portal.webapi.v2.vo.common;

import com.bilibili.adp.advertiser.portal.openapi.valid.annotation.SerializeFilter;
import com.bilibili.adp.advertiser.portal.webapi.launch.cpc.vo.ArchiveVideoV2Vo;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.FieldDefaults;

/**
 * @file: CompMaterialVo
 * @author: gaoming
 * @date: 2021/12/31
 * @version: 1.0
 * @description:
 **/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CompMaterialV2Vo {
    @ApiModelProperty(value = "标题")
    TitleVo titleVo;

    @ApiModelProperty(value = "小图/GIF对象")
    MonoMediaVo monoMediaVo;

    @SerializeFilter
    @ApiModelProperty(value = "视频")
    MgkVideoMediaVo videoMediaVo;

    @ApiModelProperty(value = "稿件")
    ArchiveVideoV2Vo archiveVideoVo;
}
