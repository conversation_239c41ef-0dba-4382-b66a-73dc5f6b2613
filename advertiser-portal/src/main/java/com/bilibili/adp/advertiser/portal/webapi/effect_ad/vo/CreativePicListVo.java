package com.bilibili.adp.advertiser.portal.webapi.effect_ad.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/1/26
 **/

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CreativePicListVo {
    private String imageUrl;
    private String imageMd5;
    private String materialCenterId;
}
