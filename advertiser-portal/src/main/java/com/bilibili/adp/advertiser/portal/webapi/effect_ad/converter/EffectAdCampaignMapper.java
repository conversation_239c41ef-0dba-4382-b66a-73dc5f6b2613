package com.bilibili.adp.advertiser.portal.webapi.effect_ad.converter;

import com.bilibili.adp.advertiser.portal.webapi.effect_ad.vo.SimpleCampaignBo;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauCampaignPo;
import com.bilibili.adp.cpc.enums.ad.PromotionPurposeType;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(imports = {PromotionPurposeType.class, Utils.class})
public interface EffectAdCampaignMapper {
    EffectAdCampaignMapper INSTANCE = Mappers.getMapper(EffectAdCampaignMapper.class);

    @Mapping(target = "id", source = "campaignId")
    @Mapping(target = "name", source = "campaignName")
    @Mapping(target = "promotionPurposeTypeDesc", expression = "java(PromotionPurposeType.getByCode(x.getPromotionPurposeType()).getDesc())")
    @Mapping(target = "budget", expression = "java(Utils.fromFenToYuan(x.getBudget()))")
    SimpleCampaignBo poToSimpleBo(LauCampaignPo x);
}
