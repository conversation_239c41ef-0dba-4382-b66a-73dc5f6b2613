/**
 * <AUTHOR>
 * @date 2018年1月23日
 */

package com.bilibili.adp.advertiser.portal.webapi.launch.cpc.vo.converter;

import com.bilibili.adp.account.dto.AccountBaseDto;
import com.bilibili.adp.account.service.IAccountGroupService;
import com.bilibili.adp.advertiser.portal.common.DropBoxItemVo;
import com.bilibili.adp.advertiser.portal.converter.SdpaProductWebMapper;
import com.bilibili.adp.advertiser.portal.service.LaunchCommonService;
import com.bilibili.adp.advertiser.portal.service.launch.WebAppPackageService;
import com.bilibili.adp.advertiser.portal.webapi.flyPro.vo.FlyProTemplateVo;
import com.bilibili.adp.advertiser.portal.webapi.launch.cpc.common.CpcLaunchWebUtil;
import com.bilibili.adp.advertiser.portal.webapi.launch.cpc.converter.ITargetVoConvertor;
import com.bilibili.adp.advertiser.portal.webapi.launch.cpc.converter.game.GameConverter;
import com.bilibili.adp.advertiser.portal.webapi.launch.cpc.converter.unit.GameCardUnitTargetConverter;
import com.bilibili.adp.advertiser.portal.webapi.launch.cpc.converter.unit.GoodsConverter;
import com.bilibili.adp.advertiser.portal.webapi.launch.cpc.vo.*;
import com.bilibili.adp.advertiser.portal.webapi.middleground.vo.cpc.MiddleCpcUnitBaseVo;
import com.bilibili.adp.advertiser.portal.webapi.resource.vo.BusinessInterestVo;
import com.bilibili.adp.advertiser.portal.webapi.resource.vo.CrowdPackVo;
import com.bilibili.adp.common.Constants;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.TargetRule;
import com.bilibili.adp.common.enums.*;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.bos.archive.*;
import com.bilibili.adp.cpc.biz.bos.unit.targets.LauStartUpCrowdsBo;
import com.bilibili.adp.cpc.biz.services.AdpCpcLauStartUpCrowdsService;
import com.bilibili.adp.cpc.biz.services.app.strategy.AppPackageServiceDelegate;
import com.bilibili.adp.cpc.biz.services.archive.ArchiveService;
import com.bilibili.adp.cpc.biz.services.campaign.api.ICpcCampaignService;
import com.bilibili.adp.cpc.biz.services.campaign.dto.CpcCampaignDto;
import com.bilibili.adp.cpc.biz.services.misc.AdpCpcOcpxService;
import com.bilibili.adp.cpc.biz.services.pickup.PickupOrderQuerier;
import com.bilibili.adp.cpc.biz.services.pickup.dto.PickupOrderDto;
import com.bilibili.adp.cpc.biz.services.resource.AdpCpcResourceService;
import com.bilibili.adp.cpc.biz.services.style.AdpCpcStyleService;
import com.bilibili.adp.cpc.biz.services.target_package.api.ICrowdPackService;
import com.bilibili.adp.cpc.biz.services.target_package.api.IResProfessionInterestService;
import com.bilibili.adp.cpc.biz.services.target_package.api.IResTargetItemService;
import com.bilibili.adp.cpc.biz.services.unit.api.IHystrixDmpService;
import com.bilibili.adp.cpc.biz.services.unit.bos.GoodsLiveBo;
import com.bilibili.adp.cpc.biz.services.unit.dto.CpcUnitDto;
import com.bilibili.adp.cpc.biz.services.unit.dto.NewCpcUnitDto;
import com.bilibili.adp.cpc.biz.services.unit.dto.UpdateCpcUnitDto;
import com.bilibili.adp.cpc.biz.validator.CpcCreativeValidator;
import com.bilibili.adp.cpc.biz.validator.CpcUnitValidator;
import com.bilibili.adp.cpc.dto.CpcUnitExtraTargetDto;
import com.bilibili.adp.cpc.dto.DpaShopGoodsDto;
import com.bilibili.adp.cpc.dto.UnitTargetDto;
import com.bilibili.adp.cpc.dto.UnitTargetInstalledUserFilterDto;
import com.bilibili.adp.cpc.enums.AdpVersion;
import com.bilibili.adp.cpc.enums.InstalledUserFilterEnum;
import com.bilibili.adp.cpc.enums.UnitExtraStatus;
import com.bilibili.adp.cpc.enums.ad.OcpcTargetEnum;
import com.bilibili.adp.cpc.enums.ad.PromotionContentTypeEnum;
import com.bilibili.adp.cpc.enums.ad.PromotionPurposeType;
import com.bilibili.adp.cpc.enums.ad.PromotionPurposeTypeToButtonCopyTypeMapping;
import com.bilibili.adp.cpc.enums.archive.ArchiveAttributeEnum;
import com.bilibili.adp.cpc.enums.archive.ArchiveState;
import com.bilibili.adp.cpc.po.ad.ResProfessionInterestCrowdsPo;
import com.bilibili.adp.cpc.utils.CommonFuncs;
import com.bilibili.adp.cpc.utils.OcpxTargetBidUtil;
import com.bilibili.adp.cpc.utils.TreeUtils;
import com.bilibili.adp.launch.api.common.*;
import com.bilibili.adp.launch.api.creative.dto.LauSubjectDto;
import com.bilibili.adp.launch.api.unit.dto.BatchUpdateDpaUnitDto;
import com.bilibili.adp.launch.api.unit.dto.BatchUpdateNoDpaUnitDto;
import com.bilibili.adp.launch.api.unit.dto.UnitTargetTagDto;
import com.bilibili.adp.launch.biz.common.LaunchUtil;
import com.bilibili.adp.legacy.LauCampaignDto;
import com.bilibili.adp.passport.api.dto.ArchiveBase;
import com.bilibili.adp.passport.api.dto.ArchiveDetail;
import com.bilibili.adp.passport.api.dto.GameDto;
import com.bilibili.adp.passport.biz.manager.ArchiveManager;
import com.bilibili.adp.resource.api.account.label.IAccountLabelService;
import com.bilibili.adp.resource.api.crowd_pack.CrowdPackDto;
import com.bilibili.adp.resource.api.crowd_pack.ThirdCrowdPackDto;
import com.bilibili.adp.resource.api.slot_group.ResSlotGroupTemplateMappingDto;
import com.bilibili.adp.resource.api.target_lau.dto.ResTargetItemDto;
import com.bilibili.adp.resource.api.target_lau.dto.TargetTreeDto;
import com.bilibili.adp.resource.api.targetmeta.TargetType;
import com.bilibili.dmp.dto.SoaBusinessCategoryDto;
import com.bilibili.dmp.dto.SoaTwoLevelBusinessCategoryDto;
import com.bilibili.dpa.product.api.enums.OpenPlatformBusinessType;
import com.bilibili.location.api.cardtype.dto.CreativeStyle;
import com.bilibili.location.api.service.ICreativeTypeService;
import com.bilibili.location.api.template.dto.ButtonCopyDto;
import com.bilibili.location.api.template.dto.TemplateDto;
import com.bilibili.location.api.template.dto.TemplateGroupBo;
import com.bilibili.location.common.ButtonCopyTypeEnum;
import com.bilibili.mas.common.utils.Values;
import com.dianping.cat.Cat;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

@Slf4j
@Component
public class UnitConverter {

    private static final Logger LOGGER = LoggerFactory.getLogger(UnitConverter.class);

    public static final String UNIT_TARGET_CROWD_PACK_KEY = "crowd_pack"; // 人群包定向
    public static final String UNIT_TARGET_BUSINESS_INTEREST_KEY = "business_interest"; // 兴趣定向

    public static final String UNIT_TARGET_PROFESSION_INTEREST_KEY = "profession_interest"; // 兴趣定向
    // 操作系统版本
    public static final String OS_VERSION = "os_version";

    private static final NewCpcUnitConverter newUnitConverter = new NewCpcUnitConverter();
    private static final UpdateCpcUnitConverter updateUnitConverter = new UpdateCpcUnitConverter();
    private static final CpcTemplateConverter templateConverter = new CpcTemplateConverter();

    private static final int BUSINESS_INTEREST_NEW = 2;

    @Value("${unit.target.video.partition.label.id:378}")
    private Integer UnitTragetVideoPartitionLabelId;

    private static final String GREEN_ORDER_WARN = "该视频属于绿洲计划";
    private static final String SELF_ORDER_WARN = "该视频包含商业内容";
    private static final String FLOWER_FIRE_WARN = "该视频属于花火计划";

    @Autowired
    private ArchiveManager archiveManager;
    @Autowired
    private ICrowdPackService crowdPackService;
    @Autowired
    private ICpcCampaignService cpcCampaignService;
    @Autowired
    private WebAppPackageService webAppPackageService;
    @Autowired
    private CpcUnitValidator cpcUnitValidator;
    @Autowired
    private CpcCreativeValidator cpcCreativeValidator;
    @Autowired
    private ICreativeTypeService creativeTypeService;
    @Autowired
    private IHystrixDmpService hystrixDmpService;
    @Autowired
    private IAccountGroupService accountGroupService;
    @Autowired
    private AdpCpcLauStartUpCrowdsService adpCpcLauStartUpCrowdsService;
    @Autowired
    private LaunchCommonService launchCommonService;
    @Autowired
    private ThreadPoolTaskExecutor taskExecutor;
    @Autowired
    private AdpCpcOcpxService ocpxService;
    @Autowired
    private AdpCpcResourceService slotGroupService;
    @Autowired
    private IAccountLabelService accountLabelService;
    @Autowired
    private IResTargetItemService resTargetItemService;
    @Autowired
    private AdpCpcStyleService styleService;
    @Autowired
    private ArchiveService archiveService;
    @Autowired
    private AppPackageServiceDelegate appPackageServiceDelegate;
    @Autowired
    private PickupOrderQuerier pickupOrderQuerier;
    @Autowired
    private IResProfessionInterestService professionInterestService;

    @Value("${launch.unit.target.overseas.account.group.id:34}")
    private Integer targetOverseasAccountGroupId;
    @Value("${game.subscribe.lowest.two.stage.bid:5}")
    private Integer gameSubscribeLowestTwoStageBid;
    @Value("${launch.unit.target.overseas.item.id:422}")
    private Integer targetOverseasItemId;

    @Value("#{PropertySplitter.listInt('${platform.unit.play.page.page_ids:151,152,153}')}")
    private List<Integer> playPagePageIds;
    @Value("#{PropertySplitter.listInt('${platform.unit.infoFeedsSourceIds:1891,1898,2003}')}")
    private List<Integer> infoFeedsSourceIds;
    @Value("#{'${only.gif.template.id:312,313}'.split(',')}")
    private List<Integer> onlyGifTemplateIds;

    /**
     * 智能放量定向和账号标签的映射关系 由于智能放量的定向需要由账号标签控制
     * map<itemId, labelId>
     * <p>
     * 定向 -> item_id: 账号标签
     * 年龄 -> 486 : 165
     * 性别 -> 487 : 169
     * 地域 -> 488 : 170
     * (商业)兴趣定向 -> 489 : 171 目前全量开放, 不需要账号标签控制
     * 人群包 -> 490 : 172
     * 扩展种子人群 -> 491 : 173
     * 设备 -> 635 : 326
     * 设备品牌 -> 636 : 327
     */
    @Value("#{PropertySplitter.map('${paltform.intlligent_mass.target:486:165,487:169,488:170,490:172,491:173,635:326,636:327}')}")
    private Map<Integer, Integer> intelligentMassTragetMap;

    /**
     * 智能放量的推广目的:APP_DOWNLOAD, ON_SHELF_GAME
     */
    private static final Set<Integer> INTELLIGENT_MASS_PPT_CHECK_SET = new HashSet<>(Arrays.asList(PromotionPurposeType.APP_DOWNLOAD.getCode(), PromotionPurposeType.ON_SHELF_GAME.getCode()));
    // 设备与设备品牌
    private static final Set<Integer> INTELLIGENT_MASS_ITEM_CHECK_SET = new HashSet<>(Arrays.asList(635, 636));

    public NewCpcUnitDto newUnitVoToDto(NewCpcUnitVo vo) {
        return newUnitConverter.doForward(vo);
    }

    public UpdateCpcUnitDto updateUnitVoToDto(UpdateCpcUnitVo vo) {
        return updateUnitConverter.doForward(vo);
    }

    public CpcUnitVo dtoToVo(CpcUnitDto dto) throws ServiceException {
        CpcUnitVo unitVo = CpcUnitVo
                .builder()
                .campaign_id(dto.getCampaignId())
                .unit_id(dto.getUnitId())
                .unit_name(dto.getUnitName())
                .slot_group_id(dto.getSlotGroup())
                // 定向
                .targets(this.buildTargetVo(dto))
                .target_package_id(dto.getTargetPackageId())
                .target_package_name(dto.getTargetPackageName())
                .begin_date(dto.getLaunchBeginDate())
                .end_date(Strings.isNullOrEmpty(dto.getLaunchEndDate()) ||
                        LaunchUtil.getDays(dto.getLaunchEndDate(), dto.getLaunchBeginDate()) > 50 * 365 ? "" : dto.getLaunchEndDate())
                .launch_time(CpcLaunchWebUtil.buildLaunchTimeForVo(dto.getLaunchTime()))
                .frequency_unit(dto.getFrequencyUnit())
                .frequency_limit(dto.getFrequencyLimit() != null && dto.getFrequencyLimit().equals(Integer.MAX_VALUE) ?
                        null : dto.getFrequencyLimit())
                .daily_budget_type(dto.getDailyBudgetType())
                .budget(Utils.fromFenToYuan(dto.getBudget()))
                .sales_type(dto.getSalesType())
                .sales_type_desc(SalesType.getByCode(dto.getSalesType()).getName())
                .cost_price(Utils.fromFenToYuan(dto.getCostPrice()))
                .no_bid_max(Utils.fromFenToYuan(dto.getNoBidMax()))
                .promotion_purpose_type(dto.getPromotionPurposeType())
                .app_package(dto.getAppInfo() == null ? null : webAppPackageService.dtoToVo(dto.getAppInfo()))
                .app_package_id(dto.getAppPackageId())
                .is_history(dto.getIsHistory())
                .promotion_purpose_type_desc(
                        // 不认识到新版枚举中找找
                        PromotionPurposeType.UNKNOWN.equals(PromotionPurposeType.getByCode(dto.getPromotionPurposeType()))
                                ? PromotionContentTypeEnum.getByCode(dto.getPromotionPurposeType()).getName()
                                : PromotionPurposeType.getByCode(dto.getPromotionPurposeType()).getDesc()
                )
                .status(dto.getUnitStatus()).status(dto.getUnitStatus())
                .status_desc(UnitStatus.getByCode(dto.getUnitStatus()).getDesc())
                .shop_goods(this.buildShopGoods(dto.getShopGoodsDto()))
                .gameBaseId(dto.getGameBaseId())
                .gamePlatformType(dto.getGamePlatformType())
                .gameIsOnline(dto.getGameIsOnline())
                .subPkg(dto.getSubPkg())
                .game_detail(GameConverter.MAPPER.dto2Vo(dto.getGamePlatformType(), dto.getGameDto()))
                .mid(dto.getLongMid())
                .video_id(dto.getVideoId() == null ? "0" : dto.getVideoId().toString())
                .launch_ad_type(dto.getLaunchVideoType())
                .video_info(this.buildVideoInfo(dto.getPromotionPurposeType(), dto.getVideoId()))
                .enable_ocpc(dto.getOcpcTarget() > 0)
                .ocpc_target(dto.getOcpcTarget())
                // PAID_IN_24H_ROI 可以作为一级优化目标，PAID_IN_24H_ROI的单位是万分之一，前端展示的单位是 个
                .two_stage_bid(
                        OcpcTargetEnum.ROI_24H_VALUE_LIST.contains(dto.getOcpcTarget()) ?
                                new BigDecimal(dto.getTwoStageBid()).divide(BigDecimal.valueOf(10000), 4, BigDecimal.ROUND_HALF_UP) :
                                Utils.fromFenToYuan(dto.getTwoStageBid()))
                .ocpxTargetTwo(dto.getOcpxTargetTwo())
                .ocpxTargetTwoBid(OcpxTargetBidUtil.dto2Vo(dto.getOcpxTargetTwoBid(), dto.getOcpxTargetTwo()))
                .subject_id(dto.getSubjectId())
                .smart_increase(dto.getSmartIncrease())
                .adp_version(dto.getAdpVersion())
                .adpVersionDesc(AdpVersion.getSanlianAdpVersionDesc(dto.getAdpVersion()))
                .speedMode(dto.getSpeedMode())
                .isStoreDirectLaunch(dto.getIsStoreDirectLaunch())
                .enterprise_mid(dto.getEnterpriseMid())
                .isHighPriority(dto.getIsHighPriority())
                .gameCardUnitTargets(GameCardUnitTargetConverter.MAPPER.dto2Vo(dto.getGameCardUnitTargets()))
                .business_domain(dto.getBusinessDomain())
                .is_no_bid(dto.getIsNoBid())
                .has_no_bid_max_bid(dto.getHasNoBidMaxBid())
                .target_package_id(dto.getTargetPackageId())
                .goodsLiveLaunchType(Optional.ofNullable(dto.getGoodsLive()).map(GoodsLiveBo::getLaunchType).orElse(0))
                .goods(GoodsConverter.MAPPER.bo2Vo(dto.getGoods()))
                .dualBidTwoStageOptimization(dto.getDualBidTwoStageOptimization())
                .ctime(dto.getCtime().getTime())
                .sdpa_products(SdpaProductWebMapper.MAPPER.baseInfoDto2Vos(dto.getSdpaProducts()))
                .searchPriceCoefficient(Utils.fromYuanToBigDecimalFen(new BigDecimal(dto.getSearchPriceCoefficient())))
                .isAllAdSearchUnit(dto.getIsAllAdSearchUnit())
                .targetExpand(dto.getTargetExpand())
                .smart_key_word(dto.getSmartKeyWord())
                .build();

        if (dto.getSearchFirstPriceCoefficient() != null) {
            unitVo.setSearchPriceCoefficient(Utils.fromYuanToBigDecimalFen(new BigDecimal(dto.getSearchFirstPriceCoefficient())));
        }
        if (Utils.isPositive(dto.getIsNoBid())) {
            unitVo.setHas_no_bid_max_bid(dto.getHasNoBidMaxBid());
        } else {
            unitVo.setHas_no_bid_max_bid(0);
        }

        LauSubjectDto subjectDto = dto.getLauSubject();
        if (subjectDto != null) {
            unitVo.setSubject_id(subjectDto.getId());
            unitVo.setSubject_type(subjectDto.getType());
            unitVo.setMaterial_id(subjectDto.getMaterialId());
        }

        final OcpxStageEnum ocpxStage;
        if (dto.getSalesType() == SalesType.CPM.getCode()) {
            // ocpm走老逻辑
            final Map<Integer, Integer> ocpmStage2UnitMap = launchCommonService.ocpxStage2MapByType(Collections.singleton(dto.getUnitId()), Constants.OCPX_CONFIG_TYPE_UNIT, Constants.OCPM_CONFIG_EXP_KEY);
            final Map<Integer, Integer> ocpmStage2AccountMap = launchCommonService.ocpxStage2MapByType(Collections.singleton(dto.getAccountId()), Constants.OCPX_CONFIG_TYPE_ACCOUNT, Constants.OCPM_CONFIG_EXP_KEY);
            ocpxStage = launchCommonService.getOcpxStage(ocpmStage2UnitMap, ocpmStage2AccountMap, dto);
        } else if (Objects.equals(dto.getSalesType(), SalesType.CPC.getCode()) && AdpVersion.isMergedInGeneral(dto.getAdpVersion())) {
            // 新版cpc走新逻辑
            final Map<Integer, Set<Integer>> ocpxConfigAccountMap = ocpxService.getOcpxConfigAccountMap(dto.getAccountId());
            final Map<Integer, Map<Integer, Set<Integer>>> ocpxConfigUnitMap = ocpxService.getOcpxConfigUnitMap(Collections.singletonList(dto.getUnitId()));
            final Map<Integer, List<Integer>> unitScenesMap = slotGroupService.getUnitScenesMap(Collections.singletonList(dto.getUnitId()));
            ocpxStage = ocpxService.getOcpxStage(ocpxConfigUnitMap, ocpxConfigAccountMap, dto.getUnitId(), dto.getOcpcTarget(), unitScenesMap.get(dto.getUnitId()));
        } else if (Objects.equals(dto.getSalesType(), SalesType.CPC.getCode())) {
            // 旧版cpc走老逻辑
            final Map<Integer, Integer> ocpcStage2UnitMap = launchCommonService.ocpxStage2MapByType(Collections.singleton(dto.getUnitId()), Constants.OCPX_CONFIG_TYPE_UNIT, Constants.OCPX_CONFIG_EXP_KEY);
            final Map<Integer, Integer> ocpcStage2AccountMap = launchCommonService.ocpxStage2MapByType(Collections.singleton(dto.getAccountId()), Constants.OCPX_CONFIG_TYPE_ACCOUNT, Constants.OCPX_CONFIG_EXP_KEY);
            ocpxStage = launchCommonService.getOcpxStage(ocpcStage2UnitMap, ocpcStage2AccountMap, dto);
        } else {
            ocpxStage = OcpxStageEnum.NON;
        }

        unitVo.setOcpx_stage(ocpxStage.getCode());
        unitVo.setOcpx_stage_desc(ocpxStage.getDesc());
        return unitVo;
    }

    /**
     * 注意：
     * 历史逻辑 此方法只会返回 platform_type = 1 安卓
     * 由于游戏中心 一个game_base_id 对应双平台
     * 如果需要返回正确的双平台信息可以使用
     *
     * @see com.bilibili.adp.advertiser.portal.webapi.launch.cpc.converter.game.GameConverter#dto2Vo(Integer, GameDto)
     */
    public GameDetailVo buildGameDetail(GameDto dto) {
        if (dto == null || !dto.getIsOnline()) {
            return null;
        }
        GameStatusEnum gameStatusEnum = GameStatusEnum.getByCode(dto.getGameStatus());
        GameGradeStatusEnum gameGradeStatusEnum = GameGradeStatusEnum.getByCode(dto.getGradeStatus());
        return GameDetailVo.builder()
                .gameBaseId(dto.getGameBaseId())
                .isOnline(dto.getIsOnline())
                .gameName(dto.getGameName())
                .gameIcon(dto.getGameIcon())
                .gameStatus(dto.getGameStatus())
                .gameStatusDesc(gameStatusEnum == null ? "未知" : gameStatusEnum.getDesc())
                .gameLink(dto.getGameLink())
                .grade(dto.getGrade())
                .gradeStatus(dto.getGradeStatus())
                .gradeStatusDesc(gameGradeStatusEnum == null ? "未知" : gameGradeStatusEnum.getDesc())
                .bookNum(dto.getBookNum())
                .gameTags(dto.getGameTags())
                .noticeTitle(dto.getNoticeTitle())
                .notice(dto.getNotice())
                .giftTitle(dto.getGiftTitle())
                .giftUrl(dto.getGiftUrl())
                .summary(dto.getSummary())
                .platformType(GamePlatformTypeEnum.ANDROID.getCode())
                .platformTypeDesc(GamePlatformTypeEnum.ANDROID.getDesc())
                .accessUrl(LaunchUtil.getAcutalLaunchUrl(dto.getGameLink()))
                .build();
    }

    private ShopGoodsVo buildShopGoods(DpaShopGoodsDto dto) {
        if (dto == null) {
            return null;
        }
        return ShopGoodsVo.builder()
                .id(dto.getGoodsId())
                .name(dto.getName())
                .price(dto.getPrice())
                .image_url(dto.getImageUrl())
                .jump_url(dto.getJumpUrl())
                .status(dto.getStatus())
                .category_name(dto.getCategoryName())
                .product_type_name(dto.getProductType() == null ? "未知" : OpenPlatformBusinessType.getBusinessType(dto.getProductType()).getName())
                .status_desc(ShopGoodsStatusEnum.getByCode(dto.getStatus()) == null ? "未知" : ShopGoodsStatusEnum.getByCode(dto.getStatus()).getDesc())
                .build();
    }


    public List<UnitSlotGroupInfo> buildUnitSlotGroupInfo(List<ResSlotGroupTemplateMappingDto> validSlotGroups,
                                                          AccountBaseDto account, boolean isUpdate) {
        if (CollectionUtils.isEmpty(validSlotGroups)) {
            return Collections.emptyList();
        }

        List<Integer> creativeTypeIds = validSlotGroups
                .stream()
                .filter(sg -> !CollectionUtils.isEmpty(sg.getTemplates()))
                .flatMap(sg -> sg.getTemplates().stream())
                .map(TemplateDto::getCreativeType)
                .distinct()
                .collect(Collectors.toList());

        Map<Integer, String> creativeTypeMap = CollectionUtils.isEmpty(creativeTypeIds) ?
                Collections.emptyMap() : creativeTypeService.getCreativeTypeMapInIds(creativeTypeIds);

        return validSlotGroups
                .stream()
                .map(dto -> UnitSlotGroupInfo
                        .builder()
                        .slot_group_id(dto.getSlotGroupId())
                        .slot_group_name(dto.getSlotGroup().getSlotGroupName())
                        .support_nobid(dto.getSlotGroup().getSupportNobid())
                        .is_playback_page(cpcUnitValidator.isSupportContentLabelAndVideoIdTarget(dto.getSlotGroupId()))
                        .template(templateDtoToVo(dto.getTemplates().get(0), creativeTypeMap))
                        .sales_types(filterSalesTypes(dto.getSlotGroup().getSalesTypes(), isUpdate))
                        .is_mobile(dto.getSlotGroup().getChannelId().equals(LaunchConstant.CHANNEL_MOBILE_ID))
                        .channel_id(dto.getSlotGroup().getChannelId())
                        .channel_name(dto.getSlotGroup().getChannelName())
                        .convert_types(dto.getConvertTypes())
                        .is_video_page(!CollectionUtils.isEmpty(playPagePageIds) && playPagePageIds.contains(dto.getSlotGroupId()))
                        .is_info_feeds(!CollectionUtils.isEmpty(infoFeedsSourceIds) && infoFeedsSourceIds.stream()
                                .anyMatch(dto.getSlotGroup().getSlotIds()::contains))
                        .build())
                .collect(Collectors.toList());
    }

    public static List<Integer> filterSalesTypes(List<Integer> salesTypes, boolean isUpdate) {
        if (CollectionUtils.isEmpty(salesTypes)) {
            return Collections.emptyList();
        }

        if (isUpdate) {
            return salesTypes;
        }

        return salesTypes.stream()
                .filter(SalesType.PLATFORM_SALES_TYPES::contains)
                .sorted(Comparator.reverseOrder())
                .collect(Collectors.toList());
    }

    public CpcTemplateGroupVo buildTemplateDetailVo(TemplateGroupBo template, Integer launchType, Integer accountId, List<CreativeStyle> supportStyle) {
        CpcTemplateGroupVo vo = templateConverter.doBackward(template);
        vo.set_support_scheme(styleService.isSupportScheme(accountId));
        vo.set_support_page_id(isSupportPageId(launchType, vo.is_support_video_id()));
        vo.setButton_copys(buttonCopyDtos2Vos(template.getButtonCopyDtos(), PromotionPurposeType.getByCode(launchType)));
        vo.set_support_dynamic_layout(vo.is_support_dynamic_layout() && vo.is_support_animation());
        vo.setSupport_styles(supportStyle.stream().map(x -> DropBoxItemVo.builder().id(x.getCode()).name(x.getName()).build()).collect(Collectors.toList()));
        vo.setDynamic_area(template.getIsDynamicArea());
        return vo;
    }

    public List<CrowdPackVo> getCrowdPackVosByAccountId(Integer accountId) throws ServiceException {
        List<CrowdPackDto> crowdPackDtos = crowdPackService.getCrowdPackDtosByAccountId(accountId);
        final Set<Integer> startUpCrowdsIdSet = adpCpcLauStartUpCrowdsService.list()
                .stream()
                .map(LauStartUpCrowdsBo::getCrowdId)
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(crowdPackDtos)) {
            return Collections.emptyList();
        }
        // 人群列表里不能选择冷启人群包
        return crowdPackDtos.stream()
                .filter(x -> !startUpCrowdsIdSet.contains(x.getId()))
                .map(dto -> CrowdPackVo.builder()
                        .id(dto.getId())
                        .name(dto.getName())
                        .combined(dto.getIsGroupSet() != null && dto.getIsGroupSet())
                        .build()).distinct()
                .collect(Collectors.toList());
    }

    /**
     * @param accountId      账户 id
     * @param target2ItemMap 全部可用定向 map
     * @param campaignId     计划 id
     * @param operator
     * @return
     * @throws InterruptedException
     */
    public Map<String, Object> buildTargetMap(Integer accountId, Map<TargetType, List<TargetTreeDto>> target2ItemMap,
                                              Integer campaignId, Integer cppt, Operator operator) throws InterruptedException {
        Long timeCost1 = System.currentTimeMillis();
        if (CollectionUtils.isEmpty(target2ItemMap)) {
            return Collections.emptyMap();
        }

        // 获取账户 groupIds, acc_account_group_mapping
        List<Integer> groupIds = accountGroupService.getValidGroupIdsByAccountId(accountId);
        // 如果 groupIds 为空 || groupIds 不包含 targetOverseasAccountGroupId，则移除 area 定向
        if (groupIds == null || !groupIds.contains(targetOverseasAccountGroupId)) {
            List<TargetTreeDto> targets = target2ItemMap.get(TargetType.AREA);
            if (targets != null && !targets.isEmpty()) {
                targets.removeIf(targetTreeDto -> targetTreeDto.getId().equals(targetOverseasItemId));
            }
        }

        /**
         * 智能放量需要 根据账号标签进行筛选定向
         */
        List<Integer> accountLabelIds = accountLabelService.getLabelIdsByAccountId(accountId);
        // 获取智能放量的定向 items
        List<ResTargetItemDto> intelligentMassItems = resTargetItemService.getItemByTargetType(TargetType.INTELLIGENT_MASS.getCode());

        // 智能放量需要 根据推广目的进行筛选定向
        Optional.ofNullable(campaignId).ifPresent(id -> {
            CpcCampaignDto campaignDto = cpcCampaignService.loadCpcCampaignDto(id);
            // 推广目的是: 智能放量的推广目的: APP_DOWNLOAD, ON_SHELF_GAME，则移除 设备与设备品牌 定向
            if (INTELLIGENT_MASS_PPT_CHECK_SET.contains(campaignDto.getPromotionPurposeType())) {
                intelligentMassItems.removeIf(item -> INTELLIGENT_MASS_ITEM_CHECK_SET.contains(item.getId()));
            }
        });

        Optional.ofNullable(cppt).ifPresent(ppt -> {
            // 推广目的是: 智能放量的推广目的: APP_DOWNLOAD, ON_SHELF_GAME，则移除 设备与设备品牌 定向
            if (INTELLIGENT_MASS_PPT_CHECK_SET.contains(ppt)) {
                intelligentMassItems.removeIf(item -> INTELLIGENT_MASS_ITEM_CHECK_SET.contains(item.getId()));
            }
        });

        // 根据智能放量定向与账号标签 mapping 和账号标签获取支持的智能放量定向
        List<Integer> intelligentMassRes = intelligentMassItems.stream().filter(
                item -> {
                    // 智能放量定向对应的标签
                    Integer labelId = intelligentMassTragetMap.get(item.getId());
                    // 根据配置的map找不到标签id 说明不需要权限控制，全量展示
                    if (Objects.isNull(labelId)) {
                        return true;
                    }
                    return accountLabelIds.contains(labelId);
                }
        ).map(ResTargetItemDto::getId).collect(Collectors.toList());

        // 不支持的智能放量移除掉
        target2ItemMap.get(TargetType.INTELLIGENT_MASS).removeIf(
                x -> !intelligentMassRes.contains(x.getId())
        );

        // 没有账号标签就不展示二级分区
        if (!accountLabelIds.contains(UnitTragetVideoPartitionLabelId)) {
            target2ItemMap.put(TargetType.VIDEO_PARTITION, Collections.emptyList());
        }

        // 没有账号标签就不展示自定义年龄定向(不需要，前端根据权限自己控制了)
//        if (!accountLabelIds.contains(accountLabelConfig.getAgeTargetCustomizeLabelId())) {
//            target2ItemMap.put(TargetType.AGE_CUSTOMIZE, Collections.emptyList());
//        }

        Map<String, Object> targetMap = Maps.newHashMap();
        target2ItemMap.keySet().forEach(k -> {
            targetMap.put(k.getByName(), target2ItemMap.get(k));
        });
        Long timeCost2 = System.currentTimeMillis();
        LOGGER.info("target time [getValidGroupIdsByAccountId] " + campaignId + " " + (timeCost2 - timeCost1));

        // 获取其他的定向，多线程
        final Map<TargetType, List<TargetTreeDto>> coldMap = Maps.newHashMap(); // 起飞 cold 定向
        final List<CrowdPackVo> crowdPackVos = Lists.newArrayList(); // 人群包
        final List<BusinessInterestVo> businessInterestVos = Lists.newArrayList(); // 兴趣定向

        CountDownLatch countDownLatch = new CountDownLatch(3);
        taskExecutor.execute(() -> {
            if (campaignId != null && campaignId > 0) {
                coldMap.putAll(cpcCampaignService.getTmpFlyCold(campaignId, operator));
            }
            countDownLatch.countDown();
            Long timeCost3 = System.currentTimeMillis();
            LOGGER.info("target time [getTmpFlyCold] " + campaignId + " " + (timeCost3 - timeCost2));
        });

        taskExecutor.execute(() -> {
            try {
                crowdPackVos.addAll(getCrowdPackVosByAccountId(accountId));
            } catch (ServiceException e) {
                e.printStackTrace();
            } finally {
                countDownLatch.countDown();
            }
            Long timeCost3 = System.currentTimeMillis();
            LOGGER.info("target time [getCrowdPackVosByAccountId] " + campaignId + " " + (timeCost3 - timeCost2));
        });

        taskExecutor.execute(() -> {
            businessInterestVos.addAll(getBusinessInterestVos());
            countDownLatch.countDown();
            Long timeCost3 = System.currentTimeMillis();
            LOGGER.info("target time [getBusinessInterestVos] " + campaignId + " " + (timeCost3 - timeCost2));
        });
        countDownLatch.await();

        Long timeCost3 = System.currentTimeMillis();
        LOGGER.info("target time [taskExecutor] " + campaignId + " " + (timeCost3 - timeCost2));

        coldMap.keySet().forEach(k -> {
            targetMap.put(k.getByName(), coldMap.get(k));
        });
        targetMap.put(UNIT_TARGET_CROWD_PACK_KEY, crowdPackVos); // 人群包
        targetMap.put(UNIT_TARGET_BUSINESS_INTEREST_KEY, businessInterestVos); // 兴趣定向
        targetMap.put(UNIT_TARGET_PROFESSION_INTEREST_KEY, getProfessionInterest()); // 行业兴趣定向

        return targetMap;
    }

    public Map<String, Object> buildTargetTreeMap() {
        return Collections.emptyMap();
    }

    public List<TreeUtils<Integer, String>> getProfessionInterest() {
        List<ResProfessionInterestCrowdsPo> pos = professionInterestService.allValidProfessionInterest();
        final TreeUtils<Integer, String> root = TreeUtils.root();
        pos.forEach(x -> {
            TreeUtils<Integer, String> tn = null;
            Integer levelOneId = x.getLevelOneId();
            Integer levelTwoId = x.getLevelTwoId();
            Integer levelThreeId = x.getLevelThreeId();
            Integer levelFourId = x.getLevelFourId();
            Integer crowdId = x.getCrowdId();
            if (levelOneId == 0) return;
            tn = root.grow(levelTwoId == 0 ? crowdId : levelOneId, x.getLevelOneName());

            if (levelTwoId == 0) return;
            tn = tn.grow(levelThreeId == 0 ? crowdId : levelTwoId, x.getLevelTwoName());

            if (levelThreeId == 0) return;
            tn = tn.grow(levelFourId == 0 ? crowdId : levelThreeId, x.getLevelThreeName());

            if (levelFourId == 0) return;
            tn.grow(crowdId, x.getLevelFourName());
        });
        TreeUtils.update(root);
        return root.getChildren();
    }

    /**
     * @param accountId 账号
     * @return List<BusinessInterestVo>
     * @description 包含第三方商业兴趣
     * <AUTHOR>
     * @date 2021-10-09 3:28 下午
     */
    public List<BusinessInterestVo> getBusinessInterestVos(Integer accountId) {
        List<BusinessInterestVo> businessInterestVos = getBusinessInterestVos();
        businessInterestVos.addAll(getThirdBusinessInterestVos(accountId));
        return businessInterestVos;
    }


    /**
     * @return List<BusinessInterestVo>
     * @description 不包含第三方商业兴趣
     * <AUTHOR>
     * @date 2021-10-09 3:31 下午
     */
    public List<BusinessInterestVo> getBusinessInterestVos() {

        List<SoaBusinessCategoryDto> newInterestedCategories = null;

        try {
            newInterestedCategories = hystrixDmpService.getBusinessCategoryTree();
        } catch (Exception e) {
            Cat.logEvent("Exception_SoaBusinessCategoryService", e.getMessage());
            LOGGER.error("invoker soaBusinessCategoryService.getBusinessCategoryTree failed", e);
        }

        LOGGER.info("soaBusinessCategoryService.getBusinessCategoryTree: [{}]", newInterestedCategories);

        List<BusinessInterestVo> result = Lists.newArrayList();

        if (!CollectionUtils.isEmpty(newInterestedCategories)) {
            result.addAll(newInterestedCategories.stream()
                    .filter(pc -> pc != null && pc.getId() != null && !CollectionUtils.isEmpty(pc.getChildList()))
                    .map(pc -> BusinessInterestVo.builder()
                            // 一级目录使用不同的ID空间，加一亿，仅用于前端元素区分
                            .id(pc.getId() + Constants.ONE_HUNDRED_MILLION)
                            .name(pc.getName())
                            .type(BUSINESS_INTEREST_NEW)
                            .children(buildBusinessCategory(pc.getChildList()))
                            .build()).collect(Collectors.toList()));
        }

        return result;
    }

    public List<BusinessInterestVo> getThirdBusinessInterestVos(Integer accountId) {
        List<ThirdCrowdPackDto> thirdPeopleGroupDtos = crowdPackService.getThirdCrowdPack(accountId);

        Map<Integer, Map<Integer, List<ThirdCrowdPackDto>>> groupMap = thirdPeopleGroupDtos.stream()
                .filter(crowdPackDto -> crowdPackDto.getThirdGroupType() != null && crowdPackDto.getCategoryId() != null)
                .collect(Collectors.groupingBy(ThirdCrowdPackDto::getThirdGroupType,
                        Collectors.groupingBy(ThirdCrowdPackDto::getCategoryId)));

        List<BusinessInterestVo> thirdBusinessInterestVos = new ArrayList<>(groupMap.size());

        groupMap.forEach((thirdType, map) -> map.forEach((categoryId, crowdPackDtos) -> {
            // 补丁: 热云的数据, 因为热云的数据已经上线, 不能再更改id, 在这里改一下显示
            final String categoryName = "热云".equals(crowdPackDtos.get(0).getThirdGroupTypeDesc()) ? "游戏细分兴趣" : crowdPackDtos.get(0).getCategoryName();
            thirdBusinessInterestVos.add(BusinessInterestVo.builder()
                    // 一级目录使用不同的ID空间，加两亿，仅用于前端元素区分
                    .id(categoryId + Constants.TWO_HUNDRED_MILLION)
                    .name(categoryName)
                    .type(BUSINESS_INTEREST_NEW)
                    .third(true)
                    .third_id(thirdType)
                    .third_name(crowdPackDtos.get(0).getThirdGroupTypeDesc())
                    .children(crowdPackDtos.stream()
                            .map(crowdPackDto -> BusinessInterestVo.builder()
                                    // DMP统一用兴趣人群ID，此处就是id
                                    .id(crowdPackDto.getId())
                                    .name(crowdPackDto.getName())
                                    .type(BUSINESS_INTEREST_NEW)
                                    .third(true)
                                    .third_id(thirdType)
                                    .third_name(crowdPackDtos.get(0).getThirdGroupTypeDesc())
                                    .build()).collect(Collectors.toList()))
                    .build());
        }));
        return thirdBusinessInterestVos;
    }

    public List<BusinessInterestVo> buildBusinessCategory(List<SoaTwoLevelBusinessCategoryDto> childInterestedCategory) {
        if (CollectionUtils.isEmpty(childInterestedCategory)) {
            return Collections.emptyList();
        }

        // DMP统一用兴趣人群ID，此处是groupId
        return childInterestedCategory.stream().map(dto -> BusinessInterestVo.builder()
                .id(dto.getGroupId())
                .name(dto.getName())
                .type(BUSINESS_INTEREST_NEW)
                .build()).collect(Collectors.toList());
    }

    private List<CpcButtonCopyVo> buttonCopyDtos2Vos(List<ButtonCopyDto> dtos, PromotionPurposeType launchType) {
        if (CollectionUtils.isEmpty(dtos) || launchType == null) {
            return Collections.emptyList();
        }
        EnumSet<ButtonCopyTypeEnum> buttonCopyTypeEnumSet = PromotionPurposeTypeToButtonCopyTypeMapping.getByPType(launchType);
        return dtos.stream()
                .filter(dto -> dto != null && buttonCopyTypeEnumSet.contains(ButtonCopyTypeEnum.getByCode(dto.getType())))
                .sorted(Comparator.comparing(ButtonCopyDto::getId))
                .map(dto -> CpcButtonCopyVo.builder()
                        .id(dto.getId())
                        .name(dto.getContent())
                        .type(dto.getType())
                        .type_desc(ButtonCopyTypeEnum.getByCode(dto.getType()).getDesc())
                        .build())
                .collect(Collectors.toList());
    }

    private CpcUnitTargetVo buildTargetVo(CpcUnitDto unit) throws ServiceException {
        List<TargetRule> targetRules = unit.getTargetRules();
        CpcUnitTargetVo target = new CpcUnitTargetVo();

        for (TargetRule targetRule : targetRules) {
            switch (TargetType.getByCode(targetRule.getRuleType())) {
                case AREA:
                    target.setArea(CpcLaunchWebUtil.processTargeting(targetRule.getValueIds()));
                    break;
                case GENDER:
                    target.setGender(CpcLaunchWebUtil.processTargeting(targetRule.getValueIds()));
                    break;
                case NETWORK:
                    target.setNetwork(CpcLaunchWebUtil.processTargeting(targetRule.getValueIds()));
                    break;
                case OS:
                    target.setOs(CpcLaunchWebUtil.processTargeting(targetRule.getValueIds()));
                    break;
                case AGE:
                    target.setAge(CpcLaunchWebUtil.processTargeting(targetRule.getValueIds()));
                    break;
                case AGE_CUSTOMIZE:
                    target.setAge_customize(CpcLaunchWebUtil.processTargeting(targetRule.getValueIds()));
                    break;
                case CATEGORY:
                    target.setCategory(CpcLaunchWebUtil.processTargeting(targetRule.getValueIds()));
                    break;
                case DEVICE_BRAND:
                    target.setDevice_brand(CpcLaunchWebUtil.processTargeting(targetRule.getValueIds()));
                    break;
                case APP_CATEGORY:
                    target.setApp_category(CpcLaunchWebUtil.processTargeting(targetRule.getValueIds()));
                    break;
                case CONVERTED_USER_FILTER:
                    target.setConverted_user_filter(CpcLaunchWebUtil.processTargeting(targetRule.getValueIds()));
                    break;

                case INTELLIGENT_MASS:
                    target.setIntelligentMass(
                            CpcUnitTargetIntelligentMassVo.builder()
                                    .targets(CpcLaunchWebUtil.processTargeting(targetRule.getValueIds()))
                                    .extraCrowdPackIds(unit.getExtraCrowdPackIds())
                                    .build());
                    break;
                case VIDEO_PARTITION:
                    target.setVideoPartition(CpcLaunchWebUtil.processTargeting(targetRule.getValueIds()));
                case AREA_TYPE:
                    target.setArea_type(CpcLaunchWebUtil.processTargeting(targetRule.getValueIds()));
                    break;
                case PHONE_PRICE:
                    target.setPhone_price(CpcLaunchWebUtil.processTargeting(targetRule.getValueIds()));
                case AREA_LEVEL:
                    target.setArea_level(CpcLaunchWebUtil.processTargeting(targetRule.getValueIds()));
                    break;
                default:
                    break;
            }
        }

        target.setTag(CollectionUtils.isEmpty(unit.getTags()) ? CpcUnitTargetTagVo.getEmpty() : CpcUnitTargetTagVo.builder().tags(unit.getTags()).is_fuzzy_tags(unit.getIsFuzzyTags()).build());
        target.setVideo_tag(unit.getVideoTag() == null || CollectionUtils.isEmpty(unit.getVideoTag().getTags()) ? CpcUnitTargetTagVo.getEmpty() : CpcUnitTargetTagVo.builder().tags(unit.getVideoTag().getTags()).is_fuzzy_tags(unit.getVideoTag().getIsFuzzyTags()).build());
        target.setCrowd_pack(this.buildCrowdPackVo(unit));
        target.setContent(this.buildTargetContentVo(unit.getTargetVideoIds(), unit.getTargetContentLabels()));
        target.setBusiness_interest(unit.getBusinessInterestIds());
        target.setArchive_content(buildArchiveContent(unit.getExtraTarget()));
        target.setInstalled_user_filter(buildInstalledUserFilter(unit.getInstalledUserFilter()));
        target.setOs_version(ITargetVoConvertor.INSTANCE.convertTargetOsVersionDto2Vo(unit.getOsVersion()));
        target.setBili_client_version(ITargetVoConvertor.INSTANCE.convertTargetBiliClientVersionDto2Vo(unit.getBiliClientVersion()));
        target.setProfession_auto(unit.getProfessionInterestAuto());
        target.setProfession_interest(unit.getProfessionInterestIds());
        return target;
    }

    private CpcUnitTargetContentVo buildTargetContentVo(List<Long> targetVideoIds, List<String> targetContentLabels) throws ServiceException {
        CpcUnitTargetContentVo contentTargetVo = CpcUnitTargetContentVo.builder()
                .video_ids(targetVideoIds)
                .content_labels(targetContentLabels)
                .build();
        if (!CollectionUtils.isEmpty(targetVideoIds)) {
            Map<Long, ArchiveDetail> archiveMap = archiveManager.getArchivesByAids(targetVideoIds);
            List<CpcVideoVo> cpcVideoVos = targetVideoIds.stream()
                    .filter(avid -> archiveMap.containsKey(avid) && archiveMap.get(avid).getArchive() != null)
                    .map(avid -> {
                        ArchiveBase archiveBase = archiveMap.get(avid).getArchive();
                        return CpcVideoVo.builder()
                                .id(archiveBase.getAid() == null ? "0" : archiveBase.getAid().toString())
                                .name(archiveBase.getTitle())
                                .tag(archiveBase.getTag())
                                .desc(archiveBase.getDesc())
                                .status(archiveBase.getState())
                                .status_desc(ArchiveState.getByCode(archiveBase.getState()).getDesc())
                                .build();
                    }).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(cpcVideoVos)) {
                contentTargetVo.setVideo_infos(cpcVideoVos);
            }
        }

        return contentTargetVo;
    }

    private CpcUnitTargetCrowdPackVo buildCrowdPackVo(CpcUnitDto unit) throws ServiceException {
        CpcUnitTargetCrowdPackVo crowdPack = CpcUnitTargetCrowdPackVo.getEmpty();
        if (unit == null || (CollectionUtils.isEmpty(unit.getCrowdPackIds()) && CollectionUtils.isEmpty(unit.getExcludeCrowdPackIds()))) {
            return crowdPack;
        }

        final Set<Integer> cpIdSet = crowdPackService.getCrowdPackDtosByAccountId(unit.getAccountId())
                .stream()
                .map(CrowdPackDto::getId)
                .collect(Collectors.toSet());

        return CpcUnitTargetCrowdPackVo
                .builder()
                .crowd_pack_ids(unit.getCrowdPackIds().stream().filter(cpIdSet::contains).collect(Collectors.toList()))
                .exclude_crowd_pack_ids(unit.getExcludeCrowdPackIds().stream().filter(cpIdSet::contains).collect(Collectors.toList()))
                .extra_crowd_packs(Collections.emptyList())
                .extra_exclude_crowd_packs(Collections.emptyList())
                .extra_crowd_pack_ids(Collections.emptyList())
                .extra_exclude_crowd_pack_ids(Collections.emptyList())
                .other_crowd_pack_ids_group(Collections.emptyList())
                .build();
    }

    public CpcTemplateVo templateDtoToVo(TemplateDto template, Map<Integer, String> creativeTypeMap) {
        return CpcTemplateVo.builder()
                .id(template.getTemplateId())
                .name(template.getTemplateName())
                .card_type(template.getCardType())
                ._support_image(template.getIsSupportImage())
                ._support_title(template.getIsFillTitle())
                ._support_desc(template.getIsFillDesc())
                .image_width(template.getImageWidth())
                .image_height(template.getImageHeight())
                .image_kb_limit(template.getImageKbLimit())
                .desc_min_length(template.getDescMinLength())
                .desc_max_length(template.getDescMaxLength())
                .title_min_length(template.getTitleMinLength())
                .title_max_length(template.getTitleMaxLength())
                .review_examples(templateConverter.buildTemplateReviewExample(template.getReviewExampleDtos()))
                .creative_type(template.getCreativeType())
                .creative_type_desc(creativeTypeMap.getOrDefault(template.getCreativeType(), "--"))
                .build();
    }

    private Boolean generateSupportImage(TemplateDto template) {
        //只支持gif的模板，不支持静态image
        if (!CollectionUtils.isEmpty(onlyGifTemplateIds) && onlyGifTemplateIds.contains(template.getTemplateId())) {
            return false;
        }
        return template.getIsSupportImage();
    }

    public FlyProTemplateVo templateDtoToFlyVo(TemplateDto template, Map<Integer, String> creativeTypeMap, boolean isGdPlus) {
        return FlyProTemplateVo.builder()
                .id(template.getTemplateId())
                .name(template.getTemplateName())
                .card_type(template.getCardType())
                ._support_image(this.generateSupportImage(template))
                ._support_title(template.getIsFillTitle())
                ._support_desc(template.getIsFillDesc())
                .image_width(template.getImageWidth())
                .image_height(template.getImageHeight())
                .image_kb_limit(template.getImageKbLimit())
                .desc_min_length(template.getDescMinLength())
                .desc_max_length(template.getDescMaxLength())
                .title_min_length(template.getTitleMinLength())
                .title_max_length(template.getTitleMaxLength())
                .review_examples(templateConverter.buildTemplateReviewExample(template.getReviewExampleDtos()))
                .creative_type(template.getCreativeType())
                .creative_type_desc(creativeTypeMap.getOrDefault(template.getCreativeType(), "--"))
                ._support_gif(template.getIsSupportAnimation() != null && template.getIsSupportAnimation() && !isGdPlus)
                .gif_milli_second_limit(template.getAnimationMaxDuration())
                .gif_kb_limit(template.getAnimationKbLimit())
                .gif_height(template.getIsSupportAnimation() != null && template.getIsSupportAnimation() && !isGdPlus ? template.getImageHeight() : 0)
                .gif_width(template.getIsSupportAnimation() != null && template.getIsSupportAnimation() && !isGdPlus ? template.getImageWidth() : 0)
                .build();
    }

    public UnitExtraStatus getExtraStatus(CpcUnitDto dto, boolean notEnoughMoney, Long creativeCount) {
        // 单元状态不存在 -> 无
        if (dto.getUnitStatus() == null) {
            return UnitExtraStatus.NONE;
        }
        // 单元状态非有效 -> 无
        if (!dto.getUnitStatus().equals(UnitStatus.VALID.getCode())) {
            return UnitExtraStatus.NONE;
        }
        // 账户余额不足
        if (notEnoughMoney) {
            return UnitExtraStatus.NOT_ENOUGH_MONEY;
        }

        CpcCampaignDto campaign = dto.getCampaign();
        // 没有计划 -> 无
        if (campaign == null) {
            return UnitExtraStatus.NONE;
        }

        // 计划预算超限 -> 计划超过预算
        if (campaign.getCampaignStatus().equals(CampaignStatus.BUDGET_EXCEED.getCode())) {
            return UnitExtraStatus.CAMPAIGN_EXCEED_BUDGET;
        }

        // 计划暂停 => 计划已暂停
        if (campaign.getCampaignStatus().equals(CampaignStatus.PAUSED.getCode())) {
            return UnitExtraStatus.CAMPAIGN_PAUSED;
        }

        // 没有创意 -> 没有可投放的创意
        if (creativeCount.compareTo(0L) <= 0) {
            return UnitExtraStatus.NO_VALID_CREATIVE;
        }

        // 单元状态有效 => 投放中
        if (dto.getUnitStatus().equals(UnitStatus.VALID.getCode())) {
            return UnitExtraStatus.RUNNING;
        }

        // 投放中
        return UnitExtraStatus.RUNNING;
    }


    public UnitTargetDto unitTargetVoToDto(CpcUnitTargetVo vo, Integer appId) {
        UnitTargetDto dto = UnitTargetDto.builder()
                .tags(vo.getTag() == null || CollectionUtils.isEmpty(vo.getTag().getTags()) ? Collections.emptyList() : vo.getTag().getTags())
                .isFuzzyTags(vo.getTag() == null || Objects.isNull(vo.getTag().getIs_fuzzy_tags()) ?
                        0 : vo.getTag().getIs_fuzzy_tags())
                .videoTag(vo.getVideo_tag() == null || CollectionUtils.isEmpty(vo.getVideo_tag().getTags()) ? UnitTargetTagDto.getEmpty() : UnitTargetTagDto.builder().tags(vo.getVideo_tag().getTags()).isFuzzyTags(vo.getVideo_tag().getIs_fuzzy_tags()).build())
                // 定向规则
                .targetRules(CpcLaunchWebUtil.buildTargetRules(vo))
                .osVersion(ITargetVoConvertor.INSTANCE.convertTargetOsVersionVo2Dto(vo.getOs_version()))
                .biliClientVersion(ITargetVoConvertor.INSTANCE.convertTargetBiliClientVersionVo2Dto(vo.getBili_client_version()))
                .crowdPackIds(vo.getCrowd_pack() != null && !CollectionUtils.isEmpty(vo.getCrowd_pack().getCrowd_pack_ids()) ? vo.getCrowd_pack().getCrowd_pack_ids() : Collections.emptyList())
                .excludeCrowdPackIds(vo.getCrowd_pack() != null && !CollectionUtils.isEmpty(vo.getCrowd_pack().getExclude_crowd_pack_ids()) ? vo.getCrowd_pack().getExclude_crowd_pack_ids() : Collections.emptyList())
                .professionInterestIds(vo.getProfession_interest())
                .targetVideoIds(Collections.emptyList())
                .targetContentLabels(Collections.emptyList())
                .slotGroupId(vo.getSlot_group_id())
                .categorys(vo.getCategory())
                .includeTheirsFans(vo.getArchive_content() == null ? Collections.emptyList() : vo.getArchive_content().getInclude_theirs_fans())
                .excludeTheirsFans(vo.getArchive_content() == null ? Collections.emptyList() : vo.getArchive_content().getExclude_theirs_fans())
                .videoPartition(!CollectionUtils.isEmpty(vo.getVideoPartition()) ? vo.getVideoPartition() : Collections.emptyList())
                .videoSecondPartition(vo.getArchive_content() == null || vo.getArchive_content().getVideo_second_partition() == null ? Collections.emptyList() : vo.getArchive_content().getVideo_second_partition())
                .recommendType((vo.getArchive_content() == null || vo.getArchive_content().getRecommend_type() == null)
                        ? 0 : vo.getArchive_content().getRecommend_type())
                .build();
        if (vo.getInstalled_user_filter() != null
                && InstalledUserFilterEnum.getFilterUserEnums().contains(vo.getInstalled_user_filter().getFilter_type())) {
            Integer dmpAppId = appPackageServiceDelegate.getDmpAppIdById(appId);
            if (dmpAppId != null && dmpAppId > 0) {
                dto.setInstalledUserFilter(UnitTargetInstalledUserFilterDto.builder()
                        .filterType(vo.getInstalled_user_filter().getFilter_type())
                        .targetContent(Arrays.asList(dmpAppId))
                        .build());
            }
        }
        if (vo.getInstalled_user_filter() != null
                && InstalledUserFilterEnum.TARGET.getCode().equals(vo.getInstalled_user_filter().getFilter_type())) {
            dto.setInstalledUserFilter(UnitTargetInstalledUserFilterDto.builder()
                    .filterType(vo.getInstalled_user_filter().getFilter_type())
                    .targetContent(vo.getInstalled_user_filter().getTarget_content())
                    .build());

        }

        return dto;
    }

    public static void main(String[] args) {
        CpcUnitTargetVo vo = new CpcUnitTargetVo();
        System.out.println(vo.getTag() == null || CollectionUtils.isEmpty(vo.getTag().getTags()) ? Collections.emptyList() : vo.getTag().getTags());
    }

    private CpcVideoVo buildVideoInfo(Integer promotionPurposeType, Long videoId) throws ServiceException {
        if (!Utils.isPositive(videoId)) {
            return CpcVideoVo.builder().id(videoId == null ? "0" : videoId.toString()).name("--").build();
        }
        List<PickupOrderDto> pickupOrderDtos = pickupOrderQuerier.searchPickupOrder(Collections.singletonList(videoId));
        Collection<Long> flowerFireAids = CollectionUtils.isEmpty(pickupOrderDtos) ? Collections.emptyList() :
                pickupOrderDtos.stream().map(PickupOrderDto::getAvId).collect(Collectors.toList());
        String title;
        String cover;
        ArchiveBo archiveInfo;
        // ogv推广 选的pgc稿件 有部分信息与普通稿件不同
        // 1.标题：标题不采用稿件标题，采用 season title + index title + long title拼接而成
        // 2.封面：封面不采用稿件封面，采用 episode 的封面
        if (Objects.equals(promotionPurposeType, PromotionPurposeType.OGV.getCode())) {
            Map<Long, PgcArchiveBo> pgcArchivesByAids = archiveService.getPgcArcsByAids(Collections.singletonList(videoId));
            PgcArchiveBo pgcArchive = pgcArchivesByAids.get(videoId);
            title = Optional.ofNullable(pgcArchive).map(archive -> combineStringWithBlank(archive.getSeasonTitle(), archive.getIndexTitle(), archive.getLongTitle())).orElse(StringUtils.EMPTY);
            cover = Optional.ofNullable(pgcArchive).map(PgcArchiveBo::getCover).orElse(StringUtils.EMPTY);
            archiveInfo = Optional.ofNullable(pgcArchive).orElse(PgcArchiveBo.builder().build());
        } else {
            Map<Long, ArchiveBo> archivesByAids = archiveService.getArcsByAids(Collections.singletonList(videoId));
            ArchiveBo archive = archivesByAids.get(videoId);
            title = Optional.ofNullable(archive).map(ArchiveBo::getTitle).orElse(StringUtils.EMPTY);
            cover = Optional.ofNullable(archive).map(ArchiveBo::getPic).orElse(StringUtils.EMPTY);
            archiveInfo = Optional.ofNullable(archive).orElse(ArchiveBo.builder().build());
        }

        CpcVideoVo.CpcVideoVoBuilder builder = CpcVideoVo.builder();
        builder.id(Optional.ofNullable(archiveInfo.getAid()).map(String::valueOf).orElse(StringUtils.EMPTY));
        builder.aid(archiveInfo.getAid());
        builder.desc(archiveInfo.getDesc());
        builder.name(title);
        builder.status(archiveInfo.getState());
        builder.cover(cover);
        builder.status_desc(ArchiveState.getByCode(archiveInfo.getState()).getDesc());
        builder.p_time(Optional.ofNullable(archiveInfo.getPubDate()).map(p -> p * 1000).orElse(0L));
        builder.pub_time(Optional.ofNullable(archiveInfo.getPubDate()).map(p -> p * 1000).map(l -> Instant.ofEpochMilli(l).atZone(ZoneId.systemDefault()).toLocalDateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))).orElse(StringUtils.EMPTY));
        String warn = StringUtils.EMPTY;
        Integer businessType = VideoBusinessType.NOT_BUSINESS.getCode();
        if (Values.zeroIfNull(archiveInfo.getOrderID()) > 0) {
            warn = GREEN_ORDER_WARN;
            businessType = VideoBusinessType.GREEN_ORDER.getCode();
        } else if (Objects.nonNull(archiveInfo.getAttribute()) && Utils.getIntegerBit(archiveInfo.getAttribute(), ArchiveAttributeEnum.IS_PORDER.getIndex()) == 1) {
            warn = SELF_ORDER_WARN;
            businessType = VideoBusinessType.SELF_ORDER.getCode();
        } else {
            if (flowerFireAids.contains(videoId)) {
                warn = FLOWER_FIRE_WARN;
                businessType = VideoBusinessType.FLOWER_FIRE.getCode();
            }
        }
        builder.warn(warn);
        builder.business_type(businessType);
        builder.play_count(Optional.ofNullable(archiveInfo.getStat()).map(StatBo::getView).orElse(0));
        builder.like_count(Optional.ofNullable(archiveInfo.getStat()).map(StatBo::getLike).orElse(0));
        builder.nickname(Optional.ofNullable(archiveInfo.getAuthor()).map(AuthorBo::getName).orElse(StringUtils.EMPTY));
        builder.face(Optional.ofNullable(archiveInfo.getAuthor()).map(AuthorBo::getFace).orElse(StringUtils.EMPTY));
        builder.duration(archiveInfo.getDuration());
        builder.height(Optional.ofNullable(archiveInfo.getDimension()).map(DimensionBo::getHeight).orElse(0L));
        builder.width(Optional.ofNullable(archiveInfo.getDimension()).map(DimensionBo::getWidth).orElse(0L));
        builder.rotate(Optional.ofNullable(archiveInfo.getDimension()).map(DimensionBo::getRotate).orElse(0L));
        builder.cid(archiveInfo.getFirstCid());
        return builder.build();
    }

    private String combineStringWithBlank(String... strs) {
        return String.join(" ", strs);
    }

    private CpcUnitArchiveContentTargetVo buildArchiveContent(CpcUnitExtraTargetDto extraTarget) {
        if (extraTarget == null) {
            return CpcUnitArchiveContentTargetVo.getEmpty();
        }

        return CpcUnitArchiveContentTargetVo
                .builder()
                .browse(extraTarget.getBrowse())
                .fans_relation(extraTarget.getFansRelation())
                .interaction(extraTarget.getInteraction())
                .video_second_partition(extraTarget.getVideoSecondPartition())
                .include_theirs_fans(extraTarget.getIncludeTheirsFans())
                .exclude_theirs_fans(extraTarget.getExcludeTheirsFans())
                .recommend_type(extraTarget.getRecommendType())
                .build();
    }

    private CpcUnitTargetInstalledUserFilterVo buildInstalledUserFilter(UnitTargetInstalledUserFilterDto filterDto) {
        if (filterDto == null) {
            return CpcUnitTargetInstalledUserFilterVo.getEmpty();
        }

        return CpcUnitTargetInstalledUserFilterVo
                .builder()
                .filter_type(filterDto.getFilterType())
                .target_content(filterDto.getTargetContent())
                .build();
    }

    public BatchUpdateDpaUnitDto batchUpdateUnitVo2Dto(BatchUpdateUnitVo vo) {
        Assert.notNull(vo, "批量修改单元信息不可为空");
        if (vo.getIs_update_daily_budget_type()) {
            DailyBudgetType dailyBudgetType = DailyBudgetType.getByCode(vo.getDaily_budget_type());
            Assert.notNull(dailyBudgetType, "日预算类型不合法");
        } else {
            Assert.isNull(vo.getDaily_budget_type(), "日预算类型不可更改");
            Assert.isNull(vo.getBudget(), "单元预算不可更改");
        }
        if (vo.getIs_update_frequency_unit()) {
            Assert.notNull(vo.getFrequency_unit(), "频次限制类型不可为空");
        } else {
            Assert.isNull(vo.getFrequency_unit(), "频次限制类型不可更改");
            Assert.isNull(vo.getFrequency_limit(), "频次限制不可更改");
        }
        if (vo.getIs_update_cost_price()) {
            UpdateCostPriceStyleEnum.getByCode(vo.getUpdate_cost_price_style());
            Assert.isTrue(Utils.isPositive(vo.getCost_price_val()), "出价修改值不合法");
        } else {
            Assert.isNull(vo.getUpdate_cost_price_style(), "出价调整方式不可更改");
            Assert.isNull(vo.getCost_price_val(), "出价不可更改");
        }
        if (vo.getIs_update_launch_time()) {
            Assert.isTrue(!CollectionUtils.isEmpty(vo.getLaunch_time()), "投放时间不可为空");
        } else {
            Assert.isTrue(CollectionUtils.isEmpty(vo.getLaunch_time()), "投放时间不可更改");
        }
        return BatchUpdateDpaUnitDto.builder()
                .campaignIds(vo.getCampaign_ids())
                .dailyBudgetType(vo.getIs_update_daily_budget_type() ? vo.getDaily_budget_type() : null)
                .budget(vo.getIs_update_daily_budget_type() ? (vo.getBudget() == null ? null : Utils.fromYuanToFen(vo.getBudget())) : null)
                .frequencyUnit(vo.getIs_update_frequency_unit() ? vo.getFrequency_unit() : null)
                .frequencyLimit(vo.getIs_update_frequency_unit() ? (Utils.isPositive(vo.getFrequency_limit()) ? vo.getFrequency_limit() : Integer.MAX_VALUE) : null)
                .launchTime(vo.getIs_update_launch_time() ? Utils.buildLaunchTime(vo.getLaunch_time()) : null)
                .updateCostPriceStyle(vo.getIs_update_cost_price() ? vo.getUpdate_cost_price_style() : null)
                .costPriceVal(vo.getIs_update_cost_price() ? Utils.fromYuanToIntFen(vo.getCost_price_val()) : null)
                .build();
    }

    public BatchUpdateNoDpaUnitDto batchUpdateNoDpaUnitVo2Dto(UpdateNoDpaUnitVo vo) {
        Assert.notNull(vo, "批量修改单元信息不可为空");
        boolean isUpdateDailyBudgetType = Boolean.TRUE.equals(vo.getIs_update_daily_budget_type()),
                isUpdateCostPrice = Boolean.TRUE.equals(vo.getIs_update_cost_price()),
                isUpdateTwoStageBid = Boolean.TRUE.equals(vo.getIs_update_two_stage_bid()),
                isUpdateTargetTwoBid = Boolean.TRUE.equals(vo.getIs_update_target_two_bid()),
                isUpdateLaunchEndDate = Boolean.TRUE.equals(vo.getIs_update_launch_end_date()),
                isUpdateLaunchTime = Boolean.TRUE.equals(vo.getIs_update_launch_time());

        if (isUpdateDailyBudgetType) {
            DailyBudgetType dailyBudgetType = DailyBudgetType.getByCode(vo.getDaily_budget_type());
            Assert.notNull(dailyBudgetType, "日预算类型不合法");
        } else {
            Assert.isNull(vo.getDaily_budget_type(), "日预算类型不可更改");
            Assert.isNull(vo.getBudget(), "单元预算不可更改");
        }
        if (isUpdateCostPrice) {
            Assert.isTrue(Utils.isPositive(vo.getCost_price()), "第一阶段出价修改值不合法");
        } else {
            Assert.isNull(vo.getCost_price(), "第一阶段出价不可更改");
        }
        if (isUpdateTwoStageBid) {
            Assert.isTrue(Utils.isPositive(vo.getTwo_stage_bid()), "第二阶段出价修改值不合法");
        } else {
            Assert.isTrue(!Utils.isPositive(vo.getTwo_stage_bid()), "第二阶段出价不可更改");
        }
        if (isUpdateTargetTwoBid) {
            Assert.isTrue(Utils.isPositive(vo.getTarget_two_bid()), "第二目标出价修改值不合法");
        } else {
            vo.setTarget_two_bid(BigDecimal.ZERO);
        }
        // launch_end_date = "" 表示长期投放
        if (isUpdateLaunchEndDate) {
            Assert.notNull(vo.getLaunch_end_date(), "投放结束时间修改值不合法");
        } else {
            Assert.isNull(vo.getLaunch_end_date(), "投放结束时间不可更改");
        }
        if (isUpdateLaunchTime) {
            Assert.isTrue(!CollectionUtils.isEmpty(vo.getLaunch_time()), "投放时段不可为空");
        } else {
            Assert.isTrue(CollectionUtils.isEmpty(vo.getLaunch_time()), "投放时段不可更改");
        }

        return BatchUpdateNoDpaUnitDto.builder()
                .unitIds(vo.getUnit_ids())
                .dailyBudgetType(isUpdateDailyBudgetType ? vo.getDaily_budget_type() : null)
                .effectType(isUpdateDailyBudgetType ? vo.getEffect_type() : null)
                .isRepeat(isUpdateDailyBudgetType ? vo.getIs_repeat() : null)
                .budget(isUpdateDailyBudgetType ? (vo.getBudget() == null ? null : Utils.fromYuanToFen(vo.getBudget())) : null)
                .costPrice(isUpdateCostPrice ? Utils.fromYuanToIntFen(vo.getCost_price()) : null)
                .twoStageBid(isUpdateTwoStageBid ? Utils.fromYuanToIntFen(vo.getTwo_stage_bid()) : null)
                .targetTwoBid(isUpdateTargetTwoBid ?
                        CommonFuncs.bigDecimalYuan2IntegerFenOptional(vo.getTarget_two_bid()).orElse(0) : null)
                .launchEndDate(isUpdateLaunchEndDate ?
                        (Strings.isNullOrEmpty(vo.getLaunch_end_date()) ? LaunchUtil.getStringDate(1, 100) : vo.getLaunch_end_date()) : null)
                .launchTime(isUpdateLaunchTime ? Utils.buildLaunchTime(vo.getLaunch_time()) : null)
                .build();
    }

    public List<DpaCampaignVo> lauCampaignDtos2Vos(List<LauCampaignDto> campaignDtos) {
        if (CollectionUtils.isEmpty(campaignDtos)) {
            return Collections.emptyList();
        }
        return campaignDtos.stream().map(dto -> DpaCampaignVo.builder()
                .id(dto.getCampaignId())
                .name(dto.getCampaignName())
                .count(dto.getUnitCount())
                .build()).collect(Collectors.toList());
    }

    public boolean isSupportPageId(@NonNull Integer launchType, boolean isSupportVideoId) {
        return !isSupportVideoId && !Objects.equals(PromotionPurposeType.SHOP_GOODS.getCode(), launchType);
    }

    public List<MiddleCpcUnitBaseVo> convertCpcUnitDtos2MiddleBaseVos(List<CpcUnitDto> unitDtos) {
        return unitDtos.stream()
                .map(this::convertCpcUnitDto2MiddleBaseVo)
                .collect(Collectors.toList());
    }

    private MiddleCpcUnitBaseVo convertCpcUnitDto2MiddleBaseVo(CpcUnitDto unitDto) {
        return MiddleCpcUnitBaseVo.builder()
                .unit_id(unitDto.getUnitId())
                .unit_name(unitDto.getUnitName())
                .is_managed(unitDto.getIsManaged())
                .adp_version(unitDto.getAdpVersion())
                .sales_type(unitDto.getSalesType())
                .ocpc_target(unitDto.getOcpcTarget())
                .target_package_id(unitDto.getTargetPackageId())
                .build();

    }
}
