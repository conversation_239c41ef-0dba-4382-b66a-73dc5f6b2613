/**
 * <AUTHOR>
 * @date 2017年8月17日
 */

package com.bilibili.adp.advertiser.task.job;

import com.bilibili.adp.advertiser.portal.webapi.resource.AppPackageController;
import com.bilibili.adp.advertiser.task.service.GameInfoRefreshService;
import com.bilibili.adp.common.enums.AppPackagePlatformStatus;
import com.bilibili.adp.common.enums.AppPackageStatus;
import com.bilibili.adp.common.enums.AppPlatformType;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.cpc.biz.services.app.AppPackageService;
import com.bilibili.adp.resource.biz.pojo.ResAppPackagePo;
import com.bilibili.adp.resource.biz.pojo.ResAppPackagePoExample;
import com.bilibili.adp.resource.biz.res_dao.ResAppPackageDao;
import com.bilibili.adp.util.common.AdpCatUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 0 0 0/1 * * ?
 */
@Component
@JobHandler("AndroidAppRefreshJob")
@Slf4j
public class AndroidAppRefreshJob extends IJobHandler {
    private final Logger JOB_LOGGER = LoggerFactory.getLogger(getClass());

    @Autowired
    private AppPackageService appPackageService;

    @Resource
    private ResAppPackageDao resAppPackageDao;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        JOB_LOGGER.info("********  start job: refreshGameInfo....  ******");

        AdpCatUtils.newTransaction("JOB", "refreshGameInfo", transaction -> {
            int startId = 0;
            if (!StringUtils.isEmpty(s)) {
                startId = Integer.parseInt(s);
            }
            refreshAndroidApp(startId);
        });

        JOB_LOGGER.info("********  finish job: refreshGameInfo!!!  ******\n\n");
        return SUCCESS;
    }

    public void refreshAndroidApp(Integer startId) {
        if (Objects.isNull(startId) || startId < 0) {
            startId = 0;
        }

        List<ResAppPackagePo> appPackagePos = new ArrayList<>();
        do {
            appPackagePos = get(startId);
            if (CollectionUtils.isEmpty(appPackagePos)) {
                break;
            }
            appPackagePos.forEach(androidApp -> {
                try {
                    appPackageService.refreshApk(androidApp);
                } catch (Exception e) {
                    log.error("refreshAndroidApp e={}", e);
                }
            });
            appPackageService.refreshApksInIds(appPackagePos.stream().map(ResAppPackagePo::getId).collect(Collectors.toList()));
            startId = appPackagePos.get(appPackagePos.size() - 1).getId();
        } while (!CollectionUtils.isEmpty(appPackagePos));

    }

    public List<ResAppPackagePo> get(Integer startId){
        ResAppPackagePoExample example = new ResAppPackagePoExample();

        example.or()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andStatusEqualTo(AppPackageStatus.VALID.getCode())
                .andPlatformStatusEqualTo(AppPackagePlatformStatus.VALID.getCode())
                .andPlatformEqualTo(AppPlatformType.ANDROID.getCode())
                .andIdGreaterThan(startId);
        example.setOrderByClause("id asc");
        List<ResAppPackagePo> pos = resAppPackageDao.selectByExample(example);
        return pos;
    }
}
