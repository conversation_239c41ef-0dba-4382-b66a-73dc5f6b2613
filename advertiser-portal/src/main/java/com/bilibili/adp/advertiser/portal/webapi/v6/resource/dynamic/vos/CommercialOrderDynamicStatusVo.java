package com.bilibili.adp.advertiser.portal.webapi.v6.resource.dynamic.vos;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/7/1
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CommercialOrderDynamicStatusVo {

    @ApiModelProperty("授权次数")
    private Integer authTimes;

    @ApiModelProperty("动态信息")
    private CommercialOrderAuthDynamicVo dynamicInfo;
}
