package com.bilibili.adp.advertiser.task.service.unit_status;

import com.bilibili.adp.cpc.biz.bos.WarpDatabusProperty;
import com.bilibili.adp.cpc.enums.ad.UnitStatusChangeEventType;
import com.bilibili.adp.launch.api.common.UnitStatus;
import com.bilibili.warp.databus.DatabusTemplate;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.springframework.stereotype.Service;

import java.util.Collections;


@Service
public class OutOfBudgetSceneHandler extends BasicSceneHandler implements UnitStatusChangeScene {

    public OutOfBudgetSceneHandler(UnitStatusESService unitStatusESService, WarpDatabusProperty warpDatabusProperty,
            DatabusTemplate template) {
        super(unitStatusESService, warpDatabusProperty, template);
    }

    @Override
    public boolean jumpAtTime(long timestamp) {
        return false;
    }

    @Override
    QueryBuilder genQuery(long timestamp) {
        BoolQueryBuilder queryBuilder = unitStatusESService.getSearchQuery();
        queryBuilder.must(unitStatusESService.launchBeginTime().lte(timestamp))
                .must(unitStatusESService.launchEndTime().gte(timestamp))
                .must(unitStatusESService.launchTime(Helper.getLaunchTime(timestamp)))
                .must(unitStatusESService.unitStatus(Collections.singletonList(UnitStatus.VALID.getCode())));
        return queryBuilder;
    }

    @Override
    UnitStatusChangeEventType myTyp() {
        return UnitStatusChangeEventType.OutOfBudget;
    }

}
