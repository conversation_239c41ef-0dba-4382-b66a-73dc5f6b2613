package com.bilibili.adp.advertiser.portal.soa;

import com.bilibili.adp.launch.api.soa.ISoaBrandCreativeDynamicService;
import com.bilibili.adp.passport.api.dto.ReplySubjectDto;
import com.bilibili.adp.passport.api.enums.ReplySubjectMonitorEnum;
import com.bilibili.adp.passport.api.enums.ReplySubjectStateEnum;
import com.bilibili.adp.passport.api.param.QueryReplySubjectParam;
import com.bilibili.adp.passport.api.param.ReplySubjectRegisterParam;
import com.bilibili.adp.passport.api.param.SetReplySubjectStateParam;
import com.bilibili.adp.passport.api.service.IReplyService;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.Collections;
import java.util.Map;
import java.util.Objects;

/**
 * @ClassName com.bilibili.adp.advertiser.portal.soa.BrandCreativeDyanmicService
 * @Description
 * @Version 1.0.0
 * <AUTHOR>
 * Created by user on 2021-08-24 15:11:56
 **/
@Service
public class SoaBrandCreativeDynamicService implements ISoaBrandCreativeDynamicService {

    private final IReplyService replyService;

    public SoaBrandCreativeDynamicService(IReplyService replyService){
        this.replyService = replyService;
    }

    @Override
    public Long registerOrUpdateDynamic(Long creativeId, Long mid, Integer accountId, String title, Integer forwardState) {
        return 0L;
    }

    // 注册或更新评论区
    @Override
    public void registerOrUpdateReplySubject(Long creativeId, Integer replySubjectState, Integer replyMonitorState) {
        if (Objects.isNull(replySubjectState)) return;

        Assert.isTrue(ReplySubjectStateEnum.commonEnumKV.containsKey(replySubjectState), "评论状态非法");
        Assert.isTrue(ReplySubjectMonitorEnum.commonEnumKV.containsKey(replyMonitorState), "评论监控状态非法");

        final long longCreativeId = creativeId;
        final Map<Long, ReplySubjectDto> map = getSubjectMapInCreativeIds(longCreativeId);
        if (map.containsKey(longCreativeId)) {
            replyService.setReplySubjectState(SetReplySubjectStateParam.builder()
                    .oid(longCreativeId)
                    .state(replySubjectState)
                    .build());
        } else {
            replyService.registerReplySubject(ReplySubjectRegisterParam.builder()
                    .oid(longCreativeId)
                    .mid(0L)
                    .state(replySubjectState)
                    .monitor(replyMonitorState)
                    .build());
        }
    }

    private Map<Long, ReplySubjectDto> getSubjectMapInCreativeIds(Long creativeId) {
        final QueryReplySubjectParam param = QueryReplySubjectParam.builder()
                .oids(Collections.singletonList(creativeId))
                .build();
        return replyService.queryReplySubjectsMap(param);
    }

}
