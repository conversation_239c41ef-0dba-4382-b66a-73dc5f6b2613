package com.bilibili.adp.advertiser.portal.openapi;

import com.bilibili.adp.advertiser.portal.openapi.vo.BiliCorpVideoInfoVo;
import com.bilibili.adp.cpc.biz.services.misc.AdpCpcBiliVideoService;
import com.bilibili.adp.cpc.biz.services.misc.bos.LauMaterialBilibiliVideoBo;
import com.bilibili.adp.cpc.biz.services.misc.bos.QueryBiliVideoBo;
import com.bilibili.adp.cpc.utils.SimpleResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.sql.Timestamp;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/open_api/v1/sync_bili_corp_video")
public class SyncBiliCorpVideoController {
    private final AdpCpcBiliVideoService biliVideoService;

    public SyncBiliCorpVideoController(AdpCpcBiliVideoService biliVideoService) {
        this.biliVideoService = biliVideoService;
    }

    @GetMapping("/list")
    public SimpleResponse list(@RequestParam("start_ts") Long startTsValue,
                               @RequestParam(value = "end_ts", required = false) Long endTsValue) {
        try {
            final Timestamp startTs = new Timestamp(startTsValue);
            final Timestamp endTs;
            if (Objects.isNull(endTsValue)) {
                endTs = null;
            } else {
                endTs = new Timestamp(endTsValue);
            }

            final List<LauMaterialBilibiliVideoBo> bos = biliVideoService.getBiliVideo(QueryBiliVideoBo.builder()
                    .startTs(startTs)
                    .endTs(endTs)
                    .build());
            final List<BiliCorpVideoInfoVo> vos = bos.stream()
                    .map(x -> BiliCorpVideoInfoVo.builder()
                                .avid(x.getAvid())
                                .cid(x.getCid())
                                .mid(x.getMid())
                                .coverUrl(x.getCoverUrl())
                                .coverMd5(x.getCoverMd5())
                                .build())
                    .collect(Collectors.toList());
            log.info("获取企业号视频列表成功: " + vos.size());
            return SimpleResponse.ok(vos);
        } catch (IllegalArgumentException ie) {
            log.error("获取企业号视频列表参数错误", ie);
            return SimpleResponse.fail(HttpStatus.BAD_REQUEST, ie.getMessage());
        } catch (Exception e) {
            log.error("获取企业号视频列表结果错误", e);
            return SimpleResponse.fail(HttpStatus.INTERNAL_SERVER_ERROR, e.getMessage());
        }
    }
}
