package com.bilibili.adp.advertiser.portal.webapi.v6.resource.mid.converters;

import com.bilibili.adp.advertiser.portal.webapi.v6.resource.mid.vos.AuthorizedMidInfoVo;
import com.bilibili.adp.advertiser.portal.webapi.v6.resource.mid.vos.MidBindInfoVo;
import com.bilibili.adp.advertiser.portal.webapi.v6.resource.mid.vos.MidBindReqVo;
import com.bilibili.adp.v6.enums.mid.AuthModeEnum;
import com.bilibili.adp.v6.enums.mid.AuthShareScopeEnum;
import com.bilibili.adp.v6.enums.mid.AuthSourceEnum;
import com.bilibili.adp.v6.enums.mid.AuthTimeTypeEnum;
import com.bilibili.adp.v6.resource.mid.bos.MidAuthInfoBo;
import com.bilibili.adp.v6.resource.mid.bos.MidBindBo;
import com.bilibili.adp.v6.resource.mid.bos.MidBindInfoBo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface SanlianMidAuthorizedControllerConverter {
    SanlianMidAuthorizedControllerConverter MAPPER = Mappers.getMapper(SanlianMidAuthorizedControllerConverter.class);

    @Mapping(target = "authMode", source = "authMode.code")
    @Mapping(target = "status", source = "status.code")
    @Mapping(target = "source", source = "source.code")
    @Mapping(target = "shareScope", source = "shareScope.code")
    @Mapping(target = "space", source = "spaceUrl")
    @Mapping(target = "authTimeType", source = "authTimeType.code")
    @Mapping(target = "midType", source = "midType.code")
    @Mapping(target = "renewalStatus", source = "renewalStatus.code")
    @Mapping(target = "updateStatus", source = "updateStatus.code")
    AuthorizedMidInfoVo toVo(MidAuthInfoBo authInfoBo);

    List<AuthorizedMidInfoVo> toVos(List<MidAuthInfoBo> records);

    @Mapping(target = "hasMidAuth", expression = "java(midBindInfoBo.getHasMidAuth() ? 1 : 0)")
    MidBindInfoVo toVo(MidBindInfoBo midBindInfoBo);

    MidBindBo toBo(MidBindReqVo midBindVo, Integer accountId, AuthSourceEnum source, String userName);

    default AuthModeEnum Code2AuthModeEnum(Integer value) {
        if (value == null) {
            return null;
        }
        return AuthModeEnum.getByCode(value);
    }

    default AuthShareScopeEnum Code2AuthShareScopeEnum(Integer value) {
        if (value == null) {
            return null;
        }
        return AuthShareScopeEnum.getByCode(value);
    }

    default AuthTimeTypeEnum Code2AuthTimeTypeEnum(Integer value) {
        if (value == null) {
            return null;
        }
        return AuthTimeTypeEnum.getByCode(value);
    }
}
