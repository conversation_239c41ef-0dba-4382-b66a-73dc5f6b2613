package com.bilibili.adp.advertiser.portal.webapi.flyPro.v2.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FlyProSupportScenesReqVo {
    @ApiModelProperty("支持优选场景")
    private Boolean can_prefer_scenes;
    @ApiModelProperty("支持指定场景")
    private Boolean can_specific_scenes;
    @ApiModelProperty("支持指定场景列表")
    private List<Integer> specific_scenes_list;
}
