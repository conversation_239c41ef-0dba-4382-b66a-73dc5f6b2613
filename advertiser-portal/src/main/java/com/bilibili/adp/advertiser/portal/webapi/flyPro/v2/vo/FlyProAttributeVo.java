package com.bilibili.adp.advertiser.portal.webapi.flyPro.v2.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class FlyProAttributeVo {
    @ApiModelProperty("封面尺寸编码 1-16比9 2-16比10")
    private Integer key;
    @ApiModelProperty("封面尺寸描述")
    private String value;

    private Boolean  _support_image;
    private Integer image_height;
    private Integer image_width;
    private Integer image_kb_limit;

    private Boolean _support_gif;
    private Integer gif_height;
    private Integer gif_kb_limit;
    private Integer gif_milli_second_limit;
    private Integer gif_width;

    private Boolean _support_title;
    private Integer title_min_length;
    private Integer title_max_length;

    private Boolean _support_desc;
    private Integer desc_min_length;
    private Integer desc_max_length;

    private Boolean _support_yellow_car;
    private Boolean _support_comment;
    private Boolean _support_optional;
    private Boolean _support_extra_under_box;

    private Boolean _total_show;
    private Boolean _forbid_not_module;

    private String label;

    private Long videoId;

    public static FlyProAttributeVo create() {
        return FlyProAttributeVo.builder()
                ._support_image(true)
                ._support_gif(true)
                ._support_title(true)
                ._support_desc(true)
                ._support_yellow_car(true)
                ._support_comment(true)
                ._support_optional(true)
                ._support_extra_under_box(true)
                ._total_show(true)
                ._forbid_not_module(true)
                .image_height(Integer.MAX_VALUE)
                .image_width(Integer.MAX_VALUE)
                .image_kb_limit(Integer.MAX_VALUE)
                .gif_height(Integer.MAX_VALUE)
                .gif_kb_limit(Integer.MAX_VALUE)
                .gif_milli_second_limit(Integer.MAX_VALUE)
                .gif_width(Integer.MAX_VALUE)
                .title_min_length(Integer.MAX_VALUE)
                .title_max_length(Integer.MAX_VALUE)
                .desc_min_length(Integer.MAX_VALUE)
                .desc_max_length(Integer.MAX_VALUE)
                .build();
    }
}
