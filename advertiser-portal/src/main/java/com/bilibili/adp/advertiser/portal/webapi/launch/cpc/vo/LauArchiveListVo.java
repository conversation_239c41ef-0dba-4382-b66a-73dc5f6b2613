package com.bilibili.adp.advertiser.portal.webapi.launch.cpc.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName ArchiveListVo
 * <AUTHOR>
 * @Date 2022/1/19 4:37 下午
 * @Version 1.0
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LauArchiveListVo {

    @ApiModelProperty(value = "稿件信息")
    private BilibiliVideoVo bili_video;

    @ApiModelProperty(value = "关联创意id")
    private List<Integer> creative_ids;

    @ApiModelProperty(value = "评论状态 1-正常 0-关闭")
    private Integer reply_state;

    @ApiModelProperty(value = "所有评论和回复总数，删除的不计在内")
    private Long curr_count;

    @ApiModelProperty(value = "待精选评论数")
    private Long pending_count;
}
