package com.bilibili.adp.advertiser.portal.webapi.flyPro.v2.vo;

import com.bilibili.adp.advertiser.portal.webapi.launch.cpc.vo.CpcUnitTargetTagVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class FlyProTargetVo {

    @ApiModelProperty("年龄")
    private List<Integer> age;

    @ApiModelProperty("性别")
    private List<Integer> gender;

    @ApiModelProperty("地域")
    private List<Integer> area;

    @ApiModelProperty("设备")
    private List<Integer> os;

    @ApiModelProperty("tag定向(视频关键词兴趣)")
    private CpcUnitTargetTagVo tag;

    @ApiModelProperty("vidoTag定向(视频关键词兴趣)")
    private CpcUnitTargetTagVo video_tag;

    @ApiModelProperty("感兴趣的二级分区(视频分区兴趣)")
    private List<Integer> video_second_partition;

    @ApiModelProperty("粉丝关系")
    private FlyProUnitFanRelationVo fan_relation;

    @ApiModelProperty("网络")
    private List<Integer> network;

    @ApiModelProperty("人群包")
    private FlyProUnitTargetCrowdPackVo crowd_pack;


//    @ApiModelProperty("分区")
//    private List<Integer> category;
//
//    @ApiModelProperty("设备品牌定向")
//    private List<Integer> device_brand;
//
//    @ApiModelProperty("APP分类定向")
//    private List<Integer> app_category;
//
//    @ApiModelProperty("商业兴趣")
//    private List<Integer> business_interest;
//
//    @ApiModelProperty("视频tag定向(视频关键词)")
//    private CpcUnitTargetTagVo video_tag;
//
//    @ApiModelProperty("冷启人群包")
//    private LauStartUpCrowdsBo startUpCrowd;
}
