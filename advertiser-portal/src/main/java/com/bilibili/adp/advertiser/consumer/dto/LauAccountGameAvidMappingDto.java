package com.bilibili.adp.advertiser.consumer.dto;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;

@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class LauAccountGameAvidMappingDto implements Serializable {
    private Integer accountId;
    private Integer agentId;
    private String authEffectiveTime;
    private String authExpireTime;
    private Long authMid;
    private Integer authMode;
    private Integer authStatus;
    private Integer authTimeType;
    private Long avid;
    private String ctime;
    private Integer customerId;
    private Long gameBaseId;
    private Long id;
    private Integer isDeleted;
    private String mtime;
    private Integer renewalStatus;
}
