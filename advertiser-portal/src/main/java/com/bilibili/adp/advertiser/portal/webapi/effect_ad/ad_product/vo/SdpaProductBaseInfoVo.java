package com.bilibili.adp.advertiser.portal.webapi.effect_ad.ad_product.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@ApiModel
public class SdpaProductBaseInfoVo {

    @ApiModelProperty(name = "产品id")
    private Long productId;

    @ApiModelProperty(name = "产品名称")
    private String name;

    @ApiModelProperty(name = "产品库id")
    private Long libraryId;

    /**
     * 产品类型1-课程库, 2-借贷产品库
     */
    @ApiModelProperty(name = "产品库类型1-课程库, 2-借贷产品库")
    private Integer type;

    private String mainImgUrl;
}
