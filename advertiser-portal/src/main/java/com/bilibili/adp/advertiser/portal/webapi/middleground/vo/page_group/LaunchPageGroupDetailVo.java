package com.bilibili.adp.advertiser.portal.webapi.middleground.vo.page_group;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.List;

/**
 * @ClassName PageGroupDetailVo
 * <AUTHOR>
 * @Date 2023/5/26 3:37 下午
 * @Version 1.0
 **/
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class LaunchPageGroupDetailVo {
    @ApiModelProperty("落地页组id")
    private String groupId;
    @ApiModelProperty("落地页组名称")
    private String name;
    @ApiModelProperty("落地页组类型 0-建站来源 1-三方来源")
    private Integer groupSource;
    @ApiModelProperty("落地页组类型描述")
    private String groupSourceDesc;
    @ApiModelProperty("0-正常 1-无关联创意 2-待审核 3-审核拒绝 4-已删除 引擎不感知")
    private Integer groupStatus;
    @ApiModelProperty("落地页组状态描述")
    private String groupStatusDesc;
    @ApiModelProperty("落地页内落地页列表")
    private List<LaunchPageGroupMappingVo> mappingList;
    @ApiModelProperty("创建时间")
    private String ctime;
    @ApiModelProperty("更新时间")
    private String mtime;

}
