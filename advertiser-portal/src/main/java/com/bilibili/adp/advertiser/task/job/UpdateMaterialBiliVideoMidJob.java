package com.bilibili.adp.advertiser.task.job;

import com.bapis.archive.service.SimpleArc;
import com.bilibili.adp.advertiser.helper.CatMonitorPointcut;
import com.bilibili.adp.cpc.biz.services.material.AdpCpcLauMaterialBiliVideoService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.text.MessageFormat;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
@JobHandler("UpdateMaterialBiliVideoMidJob")
public class UpdateMaterialBiliVideoMidJob extends IJobHandler {
    private final String PREFIX = "更新稿件mid";
    private final AdpCpcLauMaterialBiliVideoService adpCpcLauMaterialBiliVideoService;

    public UpdateMaterialBiliVideoMidJob(AdpCpcLauMaterialBiliVideoService adpCpcLauMaterialBiliVideoService) {
        this.adpCpcLauMaterialBiliVideoService = adpCpcLauMaterialBiliVideoService;
    }

    @Override
    @CatMonitorPointcut(transactionName = "UpdateArchiveCategoryJob")
    public ReturnT<String> execute(String s) throws Exception {
        log.info("{}: 开始", PREFIX);
        try {
            final List<Long> avids = adpCpcLauMaterialBiliVideoService.fetchMissingMidAvids();
            if (CollectionUtils.isEmpty(avids)) return SUCCESS;

            log.info("{}: 需要更新的avid数 -> {}", PREFIX, avids.size());
            final Map<Long, Long> avid2Mid = adpCpcLauMaterialBiliVideoService.fetchExistingAvid2Mid(avids);
            final List<Long> needLookUpAvids = new LinkedList<>();
            avids.forEach(x -> {
                final Long mid = avid2Mid.get(x);
                if (Objects.isNull(mid)) {
                    needLookUpAvids.add(x);
                    return;
                }

                adpCpcLauMaterialBiliVideoService.updateMid(x, mid);
            });
            if (CollectionUtils.isEmpty(needLookUpAvids)) return SUCCESS;

            log.info("{}: 需要查询主站的avid数 -> {}", PREFIX, needLookUpAvids.size());
            int failures = 0;
            for (Long avid : needLookUpAvids) {
                try {
                    final SimpleArc simpleArc = adpCpcLauMaterialBiliVideoService.getSimpleArc(avid);
                    adpCpcLauMaterialBiliVideoService.updateMid(avid, simpleArc.getMid());
                    // 不要做得太快，否则会触发主站限流
                    TimeUnit.MILLISECONDS.sleep(100L);
                } catch (Throwable t) {
                    // 失败了就算了，跳过做下一个
                    failures++;
                    log.error(MessageFormat.format("{0}: 查询avid({1,number,#})的信息失败", PREFIX, avid), t);
                }
            }
            if (failures > 0) {
                log.info("{}: 失败的avid数量 -> {}", PREFIX, failures);
            }
            return SUCCESS;
        } catch (Throwable t) {
            log.error(MessageFormat.format("{0}: 失败", PREFIX), t);
            return FAIL;
        } finally {
            log.info("{}: 结束", PREFIX);
        }
    }
}
