package com.bilibili.adp.advertiser.portal.webapi.effect_ad.vo;


import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ArchiveV2Vo {
    /**
     * aid
     */
    @ApiModelProperty(value = "aid")
    private String aid;
    /**
     * bvid
     */
    @ApiModelProperty(value = "bvid")
    private String bvid;
    /**
     * 稿件第一P的cid
     */
    @ApiModelProperty(value = "稿件第一P的cid")
    private String firstCid;
    /**
     * 是否转载 1-原创 2-转载 0-历史遗留
     */
    @ApiModelProperty(value = "是否转载 1-原创 2-转载 0-历史遗留")
    private Integer copyright;
    @ApiModelProperty(value = "autoplay")
    private Integer autoplay;
    /**
     * 封面
     */
    @ApiModelProperty(value = "封面")
    private String pic;
    /**
     * 稿件发布时间
     */
    @ApiModelProperty(value = "稿件发布时间")
    private Long pubDate;
    /**
     * 用户提交时间
     */
    @ApiModelProperty(value = "用户提交时间")
    private Long ctime;
    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String title;
    /**
     * 稿件简介
     */
    @ApiModelProperty(value = "稿件简介")
    private String desc;
    /**
     * 稿件状态
     */
    @ApiModelProperty(value = "稿件状态")
    private Integer state;
    /**
     * 稿件状态详情
     */
    @ApiModelProperty(value = "稿件状态详情")
    private String stateDesc;
    /**
     * 稿件属性
     */
    @ApiModelProperty(value = "稿件属性")
    private Integer attribute;
    /**
     * 是否pgc 根据 attribute 计算
     */
    @ApiModelProperty(value = "是否pgc")
    private Boolean isPgc;
    /**
     * 稿件总时长
     */
    @ApiModelProperty(value = "稿件总时长")
    private Integer duration;
    //region 稿件作者信息
    /**
     * Up主mid
     */
    @ApiModelProperty(value = "Up主mid")
    private Long mid;
    /**
     * Up主名称
     */
    @ApiModelProperty(value = "Up主名称")
    private String name;
    /**
     * Up主头像地址 绝对地址
     */
    @ApiModelProperty(value = "Up主头像地址")
    private String face;
    //endregion
    //region 统计信息
    /**
     * 播放数
     */
    @ApiModelProperty(value = "播放数")
    private Integer view;
    /**
     * 弹幕数
     */
    @ApiModelProperty(value = "弹幕数")
    private Integer danmaku;
    /**
     *  评论数
     */
    @ApiModelProperty(value = "评论数")
    private Integer reply;
    /**
     * 收藏数
     */
    @ApiModelProperty(value = "收藏数")
    private Integer fav;
    /**
     * 投币数
     */
    @ApiModelProperty(value = "投币数")
    private Integer coin;
    /**
     *  分享数
     */
    @ApiModelProperty(value = "分享数")
    private Integer share;
    /**
     * 当前排名
     */
    @ApiModelProperty(value = "当前排名")
    private Integer nowRank;
    /**
     * 历史最高排名
     */
    @ApiModelProperty(value = "历史最高排名")
    private Integer hisRank;
    /**
     * 点赞数
     */
    @ApiModelProperty(value = "点赞数")
    private Integer like;
    //endregion
    //region 视频分辨率
    /**
     * 宽度
     */
    @ApiModelProperty(value = "宽度")
    private Integer width;
    /**
     * 高度
     */
    @ApiModelProperty(value = "高度")
    private Integer height;
    /**
     * 是否翻转
     */
    @ApiModelProperty(value = "是否翻转")
    private Integer rotate;
    //endregion
    /**
     * 投放视频类型 1-商业内容 3-其他
     */
    @ApiModelProperty(value = "投放视频类型 1-商业内容 3-其他")
    private Integer launchVideoType;
    /**
     * 是否是商单稿件 0-非商业 1-商业
     */
    @ApiModelProperty("是否是商单稿件 0-非商业 1-商业")
    private Integer isBusinessVideo;
    /**
     * 是否ott稿件
     */
    @ApiModelProperty("是否ott稿件")
    private Boolean isOtt;

    /**
     * 是否逃单
     */
    @ApiModelProperty(value = "是否逃单 0-非逃单 1-逃单")
    private Integer isEscape;

    @ApiModelProperty(value = "是否带货稿件 0-非带货稿件 1-带货稿件")
    private Integer isGoodsArchive;

    @ApiModelProperty(value = "是否支持投放 0-支持投放 1-不支持投放")
    private Integer isSupportAdvertising;

    @ApiModelProperty(value = "是否支持ocpm投评论链接点击 0-不支持 1-支持")
    private Integer isSupportCommentClick;

    @ApiModelProperty(value = "是否支持ocpm投小黄车或应用唤起 0-不支持 1-支持")
    private Integer isSupportYellowCarOrCallUp;

    @ApiModelProperty(value = "稿件直播预约id")
    private Long sid;

    @ApiModelProperty(value = "是否有锚点")
    private Integer hasAnchor;
    @ApiModelProperty(value = "三连锚点存在的提示消息")
    private String anchorExistMsg;

    @ApiModelProperty("稿件原生状态")
    private Integer nativeStatus;
    @ApiModelProperty("稿件原生状态描述")
    private String nativeStatusDesc;
    @ApiModelProperty("绑定评论组件状态 0-未绑定 1-已绑定")
    private Integer hasBindComment;

    @ApiModelProperty("是否课堂稿件")
    private Boolean isPugv;
}
