package com.bilibili.adp.advertiser.portal.webapi.middleground;

import com.bapis.ad.mgk.page.group.PageGroupSource;
import com.bapis.ad.mgk.page.group.PageGroupStatus;
import com.bilibili.adp.advertiser.helper.cpc.CpcHelper;
import com.bilibili.adp.advertiser.portal.common.BasicController;
import com.bilibili.adp.advertiser.portal.common.Context;
import com.bilibili.adp.advertiser.portal.webapi.middleground.converter.LaunchPageGroupConverter;
import com.bilibili.adp.advertiser.portal.webapi.middleground.vo.page_group.*;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.cpc.enums.ad.PromotionPurposeType;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.services.page_group.api.ILaunchMgkPageGroupService;
import com.bilibili.adp.cpc.biz.services.page_group.bos.*;
import com.bilibili.adp.web.framework.core.Response;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * @ClassName MiddleLandingPageGroupController
 * <AUTHOR>
 * @Date 2023/5/25 9:50 下午
 * @Version 1.0
 **/
@RestController
@RequestMapping("/web_api/v1/middle/landing_page/group")
@Api(value = "/middle/landing_page/group", description = "广告中台【落地页组】")
@Slf4j
public class MiddleLaunchPageGroupController extends BasicController {

    @Autowired
    private ILaunchMgkPageGroupService launchMgkPageGroupService;
    @Autowired
    private CpcHelper cpcHelper;

    @ApiOperation(value = "查询落地页组列表")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public Response<PageResult<LaunchPageGroupDetailVo>> queryLaunchPageGroup(@ApiIgnore Context context,
                                                                              QueryLaunchPageGroupDetailVo queryVo) {
        QueryLaunchPageGroupDetailBo queryBo = LaunchPageGroupConverter.convertQueryMgkPageGroupVo2VBo(queryVo, context.getAccountId());
        PageResult<LaunchPageGroupDetailBo> boPageResult = launchMgkPageGroupService.queryPageGroupDetail(queryBo);
        PageResult<LaunchPageGroupDetailVo> pageResult = PageResult.<LaunchPageGroupDetailVo>builder()
                .total(boPageResult.getTotal())
                .records(LaunchPageGroupConverter.convertPageGroupDetailBoList2VoList(boPageResult.getRecords()))
                .build();
        return Response.SUCCESS(pageResult);
    }

    @ApiOperation(value = "查询落地页组基本列表")
    @RequestMapping(value = "/base/list", method = RequestMethod.GET)
    public Response<List<LaunchPageGroupVo>> queryBaseLaunchPageGroup(@ApiIgnore Context context,
                                                                      @ApiParam(value = "单元推广目的") @RequestParam(value = "promotion_purpose_type") Integer unitPpt,
                                                                      @ApiParam(value = "视频落地页 0-非视频 1-视频") @RequestParam(value = "has_video_page", defaultValue = "") List<Integer> hasVideoPageList,
                                                                      @ApiParam(value = "advertising_mode=1是内容投放，不传或者传0非内容投放") @RequestParam(value = "advertising_mode", defaultValue = "") Integer advertisingMode,
                                                                      @ApiParam(value = "落地页组类型 0-建站落地页组 1-三方落地页组") @RequestParam(value = "group_source") List<Integer> groupSource) {
        QueryLaunchPageGroupBo queryBo = QueryLaunchPageGroupBo.builder()
                .accountId(context.getAccountId())
                .unitPpt(unitPpt)
                .advertisingMode(advertisingMode)
                .hasVideoPageList(hasVideoPageList)
                .groupSourceList(groupSource)
                .statusList(Lists.newArrayList(PageGroupStatus.NO_CREATIVE.getNumber(),
                        PageGroupStatus.VALID.getNumber(),
                        PageGroupStatus.WAIT_AUDIT.getNumber()))
                .orderBy("mtime desc")
                .build();
        if (Lists.newArrayList(PageGroupSource.THIRD_PARTY_SOURCE_VALUE).containsAll(queryBo.getGroupSourceList())) {
            queryBo.setHasVideoPageList(Collections.emptyList());
        }

        Integer promotionPurposeType = queryBo.getUnitPpt();
        if (!Utils.isPositive(queryBo.getAdvertisingMode())) {
            Assert.isTrue(!Objects.equals(promotionPurposeType, PromotionPurposeType.ON_SHELF_GAME.getCode())
                    || Lists.newArrayList(PageGroupSource.THIRD_PARTY_SOURCE_VALUE).containsAll(queryBo.getGroupSourceList()),
                    "建站落地页组不支持安卓游戏推广目的");
        }
        List<LaunchPageGroupBo> boList = launchMgkPageGroupService.queryPageGroup(queryBo);
        List<LaunchPageGroupVo> voList = LaunchPageGroupConverter.convertBoList2VoList(boList);
        return Response.SUCCESS(voList);
    }

    @ApiOperation(value = "创建落地页组")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public Response<Long> createPageGroup(@ApiIgnore Context context,
                                          @RequestBody LaunchPageGroupCreateVo createVo) {
        validatePageUrl(createVo);
        LaunchPageGroupCreateBo createBo = LaunchPageGroupConverter.convertCreateVo2Bo(createVo);
        long pageGroupId = launchMgkPageGroupService.createPageGroup(createBo, getOperator(context));
        return Response.SUCCESS(pageGroupId);
    }

    public void validatePageUrl(LaunchPageGroupCreateVo vo){
        if(Integer.valueOf(1).equals(vo.getGroupSource())){
            if(!CollectionUtils.isEmpty(vo.getMappingList())){
                for(LaunchPageGroupMappingSaveVo saveVo : vo.getMappingList()){
                    String pageUrl = saveVo.getPageUrl();
                    // 不能是http://的链接
                    Assert.isTrue(!StringUtils.startsWith(pageUrl, "http://"),"请使用https开头的落地页链接");
                }
            }
        }
    }

    @ApiOperation(value = "更新落地页组")
    @RequestMapping(value = "/update", method = RequestMethod.PUT)
    public Response<Long> updatePageGroup(@ApiIgnore Context context,
                                          @RequestBody LaunchPageGroupUpdateVo updateVo) {
        LaunchPageGroupUpdateBo updateBo = LaunchPageGroupConverter.convertUpdateVo2Bo(updateVo);
        int auditCreativeId = launchMgkPageGroupService.updatePageGroup(updateBo, getOperator(context));
        if (Utils.isPositive(auditCreativeId)) {
            cpcHelper.launchPageGroupCreativeAuditPub(auditCreativeId);
        }
        return Response.SUCCESS(updateVo.getGroupId());
    }

    @ApiOperation(value = "删除落地页组")
    @RequestMapping(value = "/delete/{pageGroupId}", method = RequestMethod.DELETE)
    public Response<Object> deletePageGroup(@ApiIgnore Context context,
                                            @PathVariable Long pageGroupId) {
        launchMgkPageGroupService.deletePageGroup(pageGroupId, getOperator(context));
        return Response.SUCCESS();
    }

    @ApiOperation(value = "落地页组历史")
    @RequestMapping(value = "/log", method = RequestMethod.GET)
    public Response<PageResult<PageGroupLogVo>> queryPageGroupLog(@ApiIgnore Context context,
                                                                  @RequestParam(value = "page_group_id") Long pageGroupId,
                                                                  @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
                                                                  @RequestParam(value = "page_size", required = false, defaultValue = "20") Integer pageSize) {
        QueryLaunchPageGroupLogBo queryBo = QueryLaunchPageGroupLogBo.builder()
                .accountId(context.getAccountId())
                .pageGroupId(pageGroupId)
                .page(page)
                .pageSize(pageSize)
                .build();
        PageResult<LaunchPageGroupLogBo> result = launchMgkPageGroupService.queryPageGroupLog(queryBo);
        PageResult<PageGroupLogVo> resultVo = PageResult.<PageGroupLogVo>builder()
                .total(result.getTotal())
                .records(LaunchPageGroupConverter.convertLogBoList2VoList(result.getRecords()))
                .build();
        return Response.SUCCESS(resultVo);
    }

    @ApiOperation(value = "落地页组内落地页列表")
    @RequestMapping(value = "/page/list", method = RequestMethod.GET)
    public Response<PageResult<LaunchPageGroupPageVo>> queryPageList(@ApiIgnore Context context,
                                                                     @RequestParam(value = "name", required = false, defaultValue = "") String name,
                                                                     @RequestParam(value = "page_id", required = false) List<Long> pageIdList,
                                                                     @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
                                                                     @RequestParam(value = "page_size", required = false, defaultValue = "20") Integer pageSize) {
        QueryLaunchPageGroupPageBo queryBo = QueryLaunchPageGroupPageBo.builder()
                .accountId(context.getAccountId())
                .name(name)
                .pageIdList(pageIdList)
                .page(page)
                .pageSize(pageSize)
                .build();
        PageResult<LaunchPageGroupPageBo> pageResult = launchMgkPageGroupService.queryPage(queryBo);
        PageResult<LaunchPageGroupPageVo> voPageResult = PageResult.<LaunchPageGroupPageVo>builder()
                .total(pageResult.getTotal())
                .records(LaunchPageGroupConverter.convertPageBoList2VoList(pageResult.getRecords()))
                .build();
        return Response.SUCCESS(voPageResult);
    }

}
