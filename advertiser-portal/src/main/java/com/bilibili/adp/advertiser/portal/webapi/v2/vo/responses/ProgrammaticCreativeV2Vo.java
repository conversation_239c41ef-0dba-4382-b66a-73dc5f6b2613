package com.bilibili.adp.advertiser.portal.webapi.v2.vo.responses;

import com.bilibili.adp.advertiser.portal.openapi.valid.annotation.SerializeFilter;
import com.bilibili.adp.advertiser.portal.webapi.launch.cpc.vo.BilibiliVideoV2Vo;
import com.bilibili.adp.advertiser.portal.webapi.launch.cpc.vo.CpcCreativeMonitoringVo;
import com.bilibili.adp.advertiser.portal.webapi.v2.misc.bos.QualificationSimpleVo;
import com.bilibili.adp.advertiser.portal.webapi.v2.vo.common.MediaGroupV2Vo;
import com.bilibili.adp.advertiser.portal.webapi.v2.vo.common.TitleVo;
import com.bilibili.adp.launch.api.creative.dto.CreativeComponentBo;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ProgrammaticCreativeV2Vo {
    @ApiModelProperty("单元ID")
    private Integer unitId;
    @ApiModelProperty("创意ID")
    private Integer creativeId;
    @ApiModelProperty(value = "展示监控")
    private String customizedImpUrl;
    @ApiModelProperty(value = "点击监控")
    private String customizedClickUrl;

    @SerializeFilter
    @ApiModelProperty("旧版商业标id")
    private Integer cmMark;

    @SerializeFilter
    @ApiModelProperty("新版商业标id")
    private Integer busMarkId;
    @ApiModelProperty("创意标签")
    private List<String> tags;

    @SerializeFilter
    @ApiModelProperty("场景类型 1-优选广告位 0-指定场景")
    private Integer preferScene;

    @SerializeFilter
    @ApiModelProperty("指定的场景")
    private List<Integer> scenes;

    @SerializeFilter
    @ApiModelProperty("创意名称(由前端自动生成)")
    private String creativeName;
    @ApiModelProperty("跳转类型")
    private Integer jumpType;
    @ApiModelProperty("跳转链接")
    private String promotionPurposeContent;
    @ApiModelProperty("落地页组id")
    private String pageGroupId;
    @ApiModelProperty("是否绑定落地页组")
    private Integer isPageGroup;
    @ApiModelProperty("素材描述")
    private String description;
    @ApiModelProperty("素材长描述")
    private String extDescription;
    @ApiModelProperty("创意分类一级类目ID")
    private Integer buFirstCategoryId;
    @ApiModelProperty("创意分类二级类目ID")
    private Integer buSecondCategoryId;
    @ApiModelProperty("创意分类三级类目ID")
    private Integer buThirdCategoryId;
    @ApiModelProperty("微信小游戏id")
    private Integer miniGameId;
    @ApiModelProperty("微信小游戏url")
    private String miniGameUrl;
    @ApiModelProperty("是否含有微信小游戏 0-没有 1-有")
    private Integer hasLauMiniGame;

    @SerializeFilter
    @ApiModelProperty("是否允许分享：0-关闭 1-打开")
    private Integer shareState;

    @SerializeFilter
    @ApiModelProperty("创意配置方式：0-手动更新 1-自动更新")
    private Integer refreshType;

    @SerializeFilter
    @ApiModelProperty("是否使用按钮")
    private Integer attachType;

    @SerializeFilter
    @ApiModelProperty("按钮id")
    private Integer buttonCopyId;

    @SerializeFilter
    @ApiModelProperty("按钮唤起连接")
    private String buttonCopyUrl;

    @SerializeFilter
    @ApiModelProperty("是否自动填写")
    private Integer isAutoFill;
    @ApiModelProperty("卡片唤起连接")
    private String schemeUrl;

    @SerializeFilter
    @ApiModelProperty(notes = "动态配文链接")
    private String titleLink;

    @SerializeFilter
    @ApiModelProperty(notes = "动态配文描述")
    private String titleLinkName;

    @SerializeFilter
    @ApiModelProperty("动态转发：0-打开 1-关闭")
    private Integer forwardState;

    @SerializeFilter
    @ApiModelProperty("动态评论：0-打开 1-关闭")
    private Integer replyState;

    @SerializeFilter
    @ApiModelProperty("动态评论审核设置：1-先审后发 2-先发后审 默认先审后发")
    private Integer replyMonitor;

    @ApiModelProperty("额外监控链接/视频播放监控")
    private List<CpcCreativeMonitoringVo> creativeMonitoring;

    @ApiModelProperty("标题(副元素)组")
    private List<TitleVo> titles;
    @ApiModelProperty("媒体(主元素)组")
    private List<MediaGroupV2Vo> mediaGroups;

    @SerializeFilter
    @ApiModelProperty("绑定的资质ID列表")
    private List<Integer> qualificationIds;
    @SerializeFilter
    @ApiModelProperty("绑定的资质列表")
    private List<QualificationSimpleVo> qualificationSimpleVos;

    @ApiModelProperty("创意资质包ID")
    private Integer qualificationPackageId;

    @ApiModelProperty("品牌标ID")
    private Integer brandInfoId;

    @SerializeFilter
    @ApiModelProperty("流量类型")
    private Integer channelId;

    @SerializeFilter
    @ApiModelProperty("新版场景")
    private List<Integer> sceneIds;

    @SerializeFilter
    @ApiModelProperty("单元绑定的mid")
    private Long spaceMid;

    @SerializeFilter
    @ApiModelProperty("是否启用空间")
    private Integer enableSpace;

    @SerializeFilter
    @ApiModelProperty("投放模式")
    private Integer advertisingMode;

    @SerializeFilter
    @ApiModelProperty(value = "行业id")
    private Integer industryId;

    @SerializeFilter
    @ApiModelProperty(value = "tabid")
    private Integer tabId;

    @SerializeFilter
    @ApiModelProperty("稿件信息")
    private BilibiliVideoV2Vo bilibiliVideo;

    @ApiModelProperty("组件")
    private List<CreativeComponentBo> components;
    private Integer isSmartMaterial;

    @ApiModelProperty("弹幕组id")
    private String danmakuGroupId;
}
