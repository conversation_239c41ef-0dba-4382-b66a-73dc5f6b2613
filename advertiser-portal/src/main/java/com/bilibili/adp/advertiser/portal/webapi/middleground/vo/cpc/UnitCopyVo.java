package com.bilibili.adp.advertiser.portal.webapi.middleground.vo.cpc;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class UnitCopyVo {
    private Integer campaignId;
    private Integer unitId;
    private Integer count;
    // 用户未修改标题 && 前端替换在模板单元创意的标题替换成功了，这个字段才是true
    private Boolean needAiReplaceTitle;
}
