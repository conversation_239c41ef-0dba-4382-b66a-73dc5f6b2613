package com.bilibili.adp.advertiser.portal.webapi.v6.mapi.vos;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName MapiScrollCreativeResultVo
 * <AUTHOR>
 * @Date 2024/11/4 2:00 下午
 * @Version 1.0
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class MapiScrollCreativeResultVo {
    private List<MapiScrollCreativeVo> creativeList;
}
