package com.bilibili.adp.advertiser.portal.webapi.v2.account;

import com.bilibili.adp.advertiser.portal.common.BasicController;
import com.bilibili.adp.advertiser.portal.common.Context;
import com.bilibili.adp.advertiser.portal.service.launch.BvidSwitchService;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.services.account.GeneralVideoAuthService;
import com.bilibili.adp.cpc.biz.services.account.dto.LauGeneralAvidMappingDto;
import com.bilibili.adp.cpc.biz.services.account.vo.*;
import com.bilibili.adp.cpc.enums.GeneralVideoAuthStatusEnum;
import com.bilibili.adp.cpc.enums.GeneralVideoEnum;
import com.bilibili.adp.cpc.utils.Response;
import com.bilibili.adp.web.framework.core.Pagination;
import com.bilibili.bvid.BVIDUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.Range;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.constraints.NotNull;
import java.sql.Timestamp;
import java.util.Calendar;
import java.util.List;
import java.util.Objects;

import static com.bilibili.adp.cpc.biz.constants.account.GeneralAuthStatus.GENERAL_VIDEO_API_VERSION;

@Api(tags = GENERAL_VIDEO_API_VERSION, value = "/v2/middle/general_video", description = "非商单授权")
@RestController
@RequestMapping("/web_api/v2/middle/general_video")
@Slf4j
@Validated
public class GeneralVideoAuthController extends BasicController {

    @Autowired
    GeneralVideoAuthService generalVideoAuthService;


    @ApiOperation("非商单授权-查询视频绑定状态")
    @GetMapping("/state")
    public Response<GeneralVideoInfoVo> GeneralVideoBindState(@ApiIgnore Context context,
                                                              @ApiParam("视频bvid") @RequestParam(value = "bvid") @NotBlank(message = "查询mid列表的账号id不能为空") String bvid,
                                                              @ApiParam("操作类型 1发起授权 2再次申请 3续期") @RequestParam(value = "operator_type") @NotNull(message = "操作类型不能为空") @Range(min = 1, max = 3, message = "操作类型不符合约定值")
                                                                      Integer operatorType
    ) throws ServiceException {

        Integer accountId = context.getAccountId();
        Assert.isTrue(Utils.isPositive(accountId), "查询的账号id不能为空");
//        Assert.notNull(bvid, "bvid不能为空");
        long avid = BvidSwitchService.convert2Avid(bvid);
        Integer requestType = GeneralVideoEnum.OperatorEnum.AUTH_SET.contains(operatorType) ? GeneralVideoEnum.RequestEnum.AUTH.getCode()
                : GeneralVideoEnum.RequestEnum.RENEWAL.getCode();

        GeneralVideoInfoVo generalVideoInfoVo = generalVideoAuthService.getGeneralVideoInfoVo(accountId, avid, requestType, bvid);

        return Response.ok(generalVideoInfoVo);
    }


    @ApiOperation("非商单授权-新增视频绑定")
    @PostMapping("/bind")
    public Response<Void> bindOrderVideo(@ApiIgnore Context context,
                                         @RequestBody @Validated GeneralVideoAuthRequestVo requestVo) throws ServiceException {

        Integer accountId = context.getAccountId();
        Assert.isTrue(Utils.isPositive(accountId), "查询的账号id不能为空");
        long avid = BvidSwitchService.convert2Avid(requestVo.getBvid());

        Integer requestType = GeneralVideoEnum.RequestEnum.AUTH.getCode();

        generalVideoAuthService.bindGeneralVideo(accountId, avid, requestType, requestVo);

        return Response.ok();
    }

    @ApiOperation("非商单授权-撤回")
    @PostMapping("/cancel")
    public Response<Void> cancel(@ApiIgnore Context context,
                                 @RequestBody @Validated GeneralVideoCancelRequestVo requestVo) throws ServiceException {
        Integer accountId = context.getAccountId();
        Assert.isTrue(Utils.isPositive(accountId), "查询的账号id不能为空");

        // 根据 mappingId 撤回
        generalVideoAuthService.cancelGeneralVideo(accountId, requestVo.getMappingId());

        return Response.ok();
    }


    @ApiOperation("非商单授权-续期")
    @PostMapping("/renewal")
    public Response<Void> renewal(@ApiIgnore Context context,
                                  @RequestBody @Validated GeneralVideoRenewalReqeustVo requestVo) throws ServiceException {

        Integer accountId = context.getAccountId();
        Assert.isTrue(Utils.isPositive(accountId), "查询的账号id不能为空");

        Integer requestType = GeneralVideoEnum.RequestEnum.RENEWAL.getCode();

        generalVideoAuthService.renewalGeneralVideo(accountId, requestVo, requestType);

        return Response.ok();
    }

    @ApiOperation("非商单授权-视频列表")
    @GetMapping("/list")
    @Validated
    public Response<Pagination<List<GeneralVideoMappingVo>>> list(
            @ApiIgnore Context context,
            @RequestParam(value = "auth_status", required = false) @ApiParam("授权状态(可选)，1：授权待确认，2：授权待生效，3：授权中，4：授权失效，5：授权已拒绝，6：授权已撤回") Integer authStatus,
            @RequestParam(value = "page", defaultValue = "1") @ApiParam("页码，默认1") Integer pageNo,
            @RequestParam(value = "page_size", defaultValue = "20") @ApiParam("每页大小，默认20") Integer pageSize) throws ServiceException {


        Integer accountId = context.getAccountId();
        Assert.isTrue(Utils.isPositive(accountId), "查询的账号id不能为空");

        if (Objects.nonNull(authStatus)) {
            Assert.isTrue(GeneralVideoAuthStatusEnum.ALL_AUTH_CODE_SET.contains(authStatus), "不存在此种授权状态");
        }

        PageResult<GeneralVideoMappingVo> listPageResult = generalVideoAuthService.listByPage(accountId, authStatus, pageNo, pageSize);

        return Response.ok(new Pagination<>(pageNo, listPageResult.getTotal(), listPageResult.getRecords()));
    }


    //todo 下个版本移除接口
    @ApiOperation("非商单授权-获取通栏消息")
    @GetMapping("/banner_msg/state")
    public Response<Boolean> getBannerMsg(@ApiIgnore Context context) throws ServiceException {

//        Integer accountId = context.getAccountId();
//        Assert.isTrue(Utils.isPositive(accountId), "查询的账号id不能为空");
//        boolean isAboutToExpired = generalVideoAuthService.bannerState(accountId);
        return Response.ok(false);
    }


    @ApiOperation("非商单授权-关闭通栏消息")
    @PostMapping("/banner_msg/close")
    public Response<Void> closeBannerMsg(@ApiIgnore Context context) throws ServiceException {
        Integer accountId = context.getAccountId();
        Assert.isTrue(Utils.isPositive(accountId), "查询的账号id不能为空");
        generalVideoAuthService.bannerClose(accountId);
        return Response.ok();
    }


    @GetMapping("/debug/refreshAuthStatus")
    public Response<Long> refreshDebug(@ApiIgnore Context context) {
        long l = generalVideoAuthService.refreshAuthStatus();
        return Response.ok(l);
    }

    @GetMapping("/debug/listAuth")
    public Response<Pagination<List<LauGeneralAvidMappingDto>>> listAuthDebug(@ApiIgnore Context context,
                                                                              @RequestParam(value = "auth_status", required = false, defaultValue = "3") Integer authStatus,
                                                                              @RequestParam(value = "auth_model", required = false, defaultValue = "1") Integer authModel,
                                                                              @RequestParam(value = "page_no", required = false, defaultValue = "1") Integer pageNo,
                                                                              @RequestParam(value = "page_size", required = false, defaultValue = "15") Integer pageSize) throws ServiceException {
        Integer accountId = context.getAccountId();
        Assert.isTrue(Utils.isPositive(accountId), "查询的账号id不能为空");
        PageResult<LauGeneralAvidMappingDto> pageResult = generalVideoAuthService.listByAuthPage(accountId, authStatus, authModel, pageNo, pageSize);
        return Response.ok(new Pagination<>(pageNo, pageResult.getTotal(), pageResult.getRecords()));
    }

    public static void main(String[] args) {
        Long avid = 48000750L;
        System.out.println(BVIDUtils.avToBv(avid));

        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.YEAR, 100);
        Timestamp timestamp = new Timestamp(calendar.getTime().getTime());
        System.out.println(timestamp);

    }
}