package com.bilibili.adp.advertiser.portal.webapi.flyPro.v2.conveter;

import com.bilibili.adp.advertiser.portal.webapi.flyPro.v2.vo.FlyProManagedCampaignDetailVo;
import com.bilibili.adp.advertiser.portal.webapi.flyPro.v2.vo.FlyProManagedCreatingDetailVo;
import com.bilibili.adp.advertiser.portal.webapi.flyPro.v2.vo.FlyProManagedOcpxTargetVo;
import com.bilibili.adp.advertiser.portal.webapi.flyPro.v2.vo.FlyProNewManagedCampaignVo;
import com.bilibili.adp.advertiser.portal.webapi.flyPro.v2.vo.FlyProUpdateManagedCampaignVo;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.services.campaign.bos.ManagedCampaignDetailDto;
import com.bilibili.adp.cpc.biz.services.campaign.bos.ManagedCampaignDto;
import com.bilibili.adp.cpc.biz.services.campaign.bos.ManagedCreatingDetailDto;
import com.bilibili.adp.cpc.biz.services.campaign.bos.ManagedOcpxTargetDto;
import com.bilibili.adp.launch.api.action.dto.FlyActionDto;
import com.bilibili.adp.launch.biz.common.LaunchUtil;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Calendar;
import java.util.List;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @date 2021/6/30
 */
@Mapper
public interface FlyManagedCampaignConverter {

    FlyManagedCampaignConverter MAPPER = Mappers.getMapper(FlyManagedCampaignConverter.class);

    @Mappings({
            @Mapping(target = "budget", expression = "java(transferBudgetToFen.apply(request.getBudget()))"),
            @Mapping(target = "costPrice", expression = "java(transferCost.apply(request.getCostPrice()))"),
            @Mapping(target = "actionId", source = "action_id"),
            @Mapping(target = "managedEndTime", expression = "java(defaultEndTime.apply(request.getManagedEndTime()))")
    })
    ManagedCampaignDto toManagedCampaign(FlyProNewManagedCampaignVo request);

    @Mappings({
            @Mapping(target = "budget",expression = "java(transferBudgetToFen.apply(request.getBudget()))"),
            @Mapping(target = "costPrice", expression = "java(transferCost.apply(request.getCostPrice()))"),
            @Mapping(target = "managedBeginTime" ,expression = "java(defaultBeginTime.apply(request" +
                    ".getManagedBeginTime()))")
    })
    ManagedCampaignDto toManagedCampaign(FlyProUpdateManagedCampaignVo request);

    @Mappings({
            @Mapping(target = "budget", expression = "java(transferLongBudgetToYuan.apply(detailInfo.getBudget()))"),
            @Mapping(target = "costPrice", expression = "java(transferIntBudgetToYuan.apply(detailInfo.getCostPrice()))"),
            @Mapping(target = "action_id", source = "detailInfo.actionId"),
            @Mapping(target = "action_name", source = "actionDto.actionName"),
            @Mapping(target = "archive_from_type", expression = "java(com.bilibili.adp.advertiser.portal.webapi.flyPro.v2.vo.FlyProManagedCampaignDetailVo.buildArchiveFromType(detailInfo.getActionId()))")
    })
    FlyProManagedCampaignDetailVo toDetailVo(ManagedCampaignDetailDto detailInfo, BigDecimal modifiableMinimumBudget, FlyActionDto actionDto);

    List<FlyProManagedOcpxTargetVo> toOcpxTargets(List<ManagedOcpxTargetDto> ocpxTargetList);

    List<FlyProManagedCreatingDetailVo> toCreatingDetailVo(List<ManagedCreatingDetailDto> details);

    @Mappings({
            @Mapping(target = "minCostPrice",expression = "java(transferLongBudgetToYuan.apply(ocpxTarget.getMinCostPrice()))"),
    })
    FlyProManagedOcpxTargetVo toOcpxTarget(ManagedOcpxTargetDto ocpxTarget);

    Function<BigDecimal,Long> transferBudgetToFen = Utils::fromYuanToFen;
    Function<BigDecimal,Integer> transferCost = Utils::fromYuanToIntFen;
    Function<Long,BigDecimal> transferLongBudgetToYuan = Utils::fromFenToYuan;
    Function<Integer,BigDecimal> transferIntBudgetToYuan = Utils::fromFenToYuan;
    Function<String,String> defaultBeginTime = beginTime->{
        if (StringUtils.isBlank(beginTime)) {
            return LocalDate.now().toString();
        }else {
            return beginTime;
        }
    };

    Function<String,String> defaultEndTime = endTime ->{
        if (StringUtils.isBlank(endTime)){
            return LaunchUtil.getStringDate(Calendar.YEAR, 100);
        }else {
            return endTime;
        }
    };
}
