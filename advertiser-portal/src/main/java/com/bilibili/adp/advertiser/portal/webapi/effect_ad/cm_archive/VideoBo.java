/*
 * Copyright (c) 2015-2021 BiliBili Inc.
 */

package com.bilibili.adp.advertiser.portal.webapi.effect_ad.cm_archive;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class VideoBo {
    private Integer duration;
    private Integer width;
    private Integer height;
    private Integer size;
    private String md5;
    private String name;
    private Long ctime;

    private String materialId;
    public Integer archiveSource;
}
