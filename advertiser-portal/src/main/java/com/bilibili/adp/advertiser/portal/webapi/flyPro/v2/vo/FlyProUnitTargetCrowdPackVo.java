package com.bilibili.adp.advertiser.portal.webapi.flyPro.v2.vo;

import com.bilibili.adp.advertiser.portal.webapi.launch.cpc.vo.CpcUnitTargetCrowdPackVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class FlyProUnitTargetCrowdPackVo {
    @ApiModelProperty("人群包（包含）")
    private List<Integer> crowd_pack_ids;

    @ApiModelProperty("人群包（排除）")
    private List<Integer> exclude_crowd_pack_ids;

    public static FlyProUnitTargetCrowdPackVo getEmpty() {
        return FlyProUnitTargetCrowdPackVo
                .builder()
                .crowd_pack_ids(Collections.emptyList())
                .exclude_crowd_pack_ids(Collections.emptyList())
                .build();
    }
}
