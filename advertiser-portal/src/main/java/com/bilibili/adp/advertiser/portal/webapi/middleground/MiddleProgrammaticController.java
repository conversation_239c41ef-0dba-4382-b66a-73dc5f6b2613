package com.bilibili.adp.advertiser.portal.webapi.middleground;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.advertiser.portal.common.BasicController;
import com.bilibili.adp.advertiser.portal.common.Context;
import com.bilibili.adp.advertiser.portal.webapi.launch.cpc.vo.CpcSaveCreativeVo;
import com.bilibili.adp.advertiser.portal.webapi.v2.cpc.AdpCpcCreativeController;
import com.bilibili.adp.advertiser.portal.webapi.v2.vo.requests.SaveProgrammaticCreativeVo;
import com.bilibili.adp.advertiser.portal.webapi.v2.vo.responses.MaterialInfoVo;
import com.bilibili.adp.advertiser.portal.webapi.v2.vo.responses.ProgrammaticCreativeVo;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.cpc.enums.AdpVersion;
import com.bilibili.adp.cpc.utils.Response;
import com.bilibili.mgk.platform.common.WhetherEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/web_api/v1/middle/programmatic")
@Api(value = "/middle/programmatic",description = "广告中台【程序化创意相关】")
public class MiddleProgrammaticController extends BasicController {

    @Autowired
    private AdpCpcCreativeController adpCpcCreativeController;

    /**
     * 必选程序化创意保存
     *
     * @param context
     * @param vo
     * @return
     * @throws ServiceException
     */
    @ApiOperation("保存程序化创意")
    @PostMapping("/programmatic/save")
    public Response<CpcSaveCreativeVo> save(
            @ApiIgnore Context context,
            @RequestBody SaveProgrammaticCreativeVo vo) throws ServiceException {
        vo.setIsMiddleAd(WhetherEnum.YES.getCode());
        vo.setAdpVersion(AdpVersion.MIDDLE.getKey());
        return adpCpcCreativeController.save(context,vo);
    }

    @ApiOperation("获取程序化创意")
    @GetMapping("/programmatic/unit/{unitId}/get")
    public Response<ProgrammaticCreativeVo> get(
            @ApiIgnore Context context,
            @PathVariable("unitId") Integer unitId) {
        return adpCpcCreativeController.get(context,unitId);
    }

    @ApiOperation("程序化创意元素列表")
    @GetMapping("/programmatic/{creativeId}/list")
    public Response<List<MaterialInfoVo>> listMaterials(
            @ApiIgnore Context context,
            @PathVariable("creativeId") Integer creativeId) {
        return adpCpcCreativeController.listMaterials(context,creativeId);
    }
}
