package com.bilibili.adp.advertiser.task.service.unit_status;

import java.util.Calendar;

public class Helper {
    private static final String launchTimeFormat = "%d-%02d";

    public static String getLaunchTime(long timestamp) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(timestamp);
        // 周计数从1-7
        int dow = (calendar.get(Calendar.DAY_OF_WEEK) + 7 - Calendar.MONDAY) % 7 + 1;
        int hod = calendar.get(Calendar.HOUR_OF_DAY);
        return String.format(launchTimeFormat, dow, hod);
    }
}
