package com.bilibili.adp.advertiser.portal.webapi.v6.resource.mapper;

import com.bapis.ad.pandora.resource.*;
import com.bilibili.adp.advertiser.portal.webapi.v6.resource.common.SanlianAccessibilityBo;
import com.bilibili.adp.advertiser.portal.webapi.v6.resource.common.SanlianFilterConditionBo;
import com.bilibili.adp.advertiser.portal.webapi.v6.resource.creative.bos.*;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.enums.sanlian.SanlianAccessibilityTypeEnum;
import com.bilibili.adp.cpc.enums.sanlian.SanlianFilterConditionTypeEnum;
import com.bilibili.adp.cpc.enums.sanlian.SanlianImageRatioTypeEnum;
import com.bilibili.adp.cpc.enums.sanlian.SanlianYellowCarTypeEnum;
import com.google.protobuf.ProtocolMessageEnum;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.factory.Mappers;

import java.util.Objects;
import java.util.function.Function;

/**
 * @ClassName ResourceCreativeMapper
 * <AUTHOR>
 * @Date 2024/3/30 10:01 下午
 * @Version 1.0
 **/
@Mapper(collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ResourceCreativeMapper {

    ResourceCreativeMapper MAPPER = Mappers.getMapper(ResourceCreativeMapper.class);

    @Mapping(target = "channelList", source = "channelSet")
    @Mapping(target = "sceneList", source = "sceneSet")
    @Mapping(target = "templateGroupList", source = "templateGroupSet")
    SanlianCreativeLocationBo fromRpcBo(ListNestedTemplateGroupResp info);

    SanlianCreativeChannelBo fromRpcBo(ChannelInfo info);

    @Mapping(target = "previewImageRatioTypeDesc",
            expression = "java(CONVERT_IMAGE_RATIO_TYPE_DESC.apply(info.getPreviewImageRatioType()))")
    SanlianCreativeSceneBo fromRpcBo(SceneInfo info);

    @Mapping(target = "imageInfoList", source = "templateGroupImageSet")
    @Mapping(target = "gifInfoList", source = "templateGroupGifSet")
    @Mapping(target = "busMarkList", source = "busMarkSet")
    @Mapping(target = "buttonList", source = "buttonSet")
    SanlianCreativeTemplateGroupBo fromRpcBo(TemplateGroupInfo info);

    SanlianCreativeTemplateGroupImageBo fromRpcBo(TemplateGroupImageInfo info);

    @Mapping(target = "busMarkStyleList", source = "busMarkStyle")
    SanlianBusMarkInfoBo fromRpcBo(BusMarkInfo info);

    SanlianCreativeContentBo fromRpcBo(CreativeContentConfigInfo info);

    SanlianCreativeSupportNativeBo fromRpcBo(GetCreativeSupportBiliNativeResp info);

    static SanlianCreativeYellowCarBo fromRpcBo(GoodsStyle goodsStyle) {
        SanlianYellowCarTypeEnum typeEnum = SanlianYellowCarTypeEnum.getByCode(goodsStyle.getNumber());
        return SanlianCreativeYellowCarBo.builder()
                .type(typeEnum.getCode())
                .name(typeEnum.getDesc())
                .build();
    }

    static CreativeConfigCampaignUnitInfo generateCreativeConfigCampaignUnitInfo(CreativeConfigCampaignUnitInfoInputBo inputBo) {

        Integer adType = inputBo.getAdType();
        Integer promotionPurposeType = inputBo.getPromotionPurposeType();
        Integer promotionContentType = inputBo.getPromotionContentType();
        Integer baseTarget = inputBo.getBaseTarget();
        Integer cpaTarget = inputBo.getCpaTarget();
        Integer deepCpaTarget = inputBo.getDeepCpaTarget();
        Integer isNoBid = inputBo.getIsNoBid();

        boolean hasSalesModesParam = Utils.isPositive(baseTarget) || Utils.isPositive(cpaTarget);
        if (Objects.nonNull(adType)
                && Objects.nonNull(promotionPurposeType)
                && Objects.nonNull(promotionContentType)
                && hasSalesModesParam
                && Objects.nonNull(isNoBid)) {
            return CreativeConfigCampaignUnitInfo.newBuilder()
                    .setAdTypeValue(adType)
                    .setPromotionPurposeTypeValue(promotionPurposeType)
                    .setPromotionContentTypeValue(promotionContentType)
                    .setBaseTargetValue(baseTarget)
                    .setCpaTargetValue(cpaTarget)
                    .setDeepCpaTargetValue(deepCpaTarget)
                    .setIsNoBid(isNoBid)
                    .build();
        }

        return null;
    }

    static SanlianAccessibilityBo fromRpcAccessibility(Accessibility accessibility) {
        return SanlianAccessibilityBo.builder()
                .code(accessibility.getNumber())
                .desc(SanlianAccessibilityTypeEnum.getByCode(accessibility.getNumber()).getName())
                .build();
    }

    Function<ImageRatioType, String> CONVERT_IMAGE_RATIO_TYPE_DESC = imageRatioType ->
            SanlianImageRatioTypeEnum.getByCode(imageRatioType.getNumber()).getDesc();

    Function<Integer, String> CONVERT_YELLOW_CAR_TYPE_NAME =
            type -> SanlianYellowCarTypeEnum.getByCode(type).getDesc();

    static Integer map(ProtocolMessageEnum x) { return x.getNumber(); }

}
