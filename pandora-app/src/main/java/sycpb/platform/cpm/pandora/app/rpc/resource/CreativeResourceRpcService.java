package sycpb.platform.cpm.pandora.app.rpc.resource;

import com.bapis.ad.pandora.resource.*;
import com.google.common.collect.Lists;
import io.grpc.stub.StreamObserver;
import lombok.RequiredArgsConstructor;
import pleiades.venus.starter.rpc.server.RPCService;
import sycpb.platform.cpm.pandora.app.aspect.RpcServiceAspect;
import sycpb.platform.cpm.pandora.infra.enums.ErrorCodeEnum;
import sycpb.platform.cpm.pandora.infra.err.PandoraAssert;
import sycpb.platform.cpm.pandora.infra.err.PandoraDataErr;
import sycpb.platform.cpm.pandora.infra.utils.NumberUtils;
import sycpb.platform.cpm.pandora.service.api.core.v6.campaign.ICampaignService;
import sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos.LauCreativeImageBo;
import sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos.LauCreativeTitleBo;
import sycpb.platform.cpm.pandora.service.api.resource.account.IAccountLabelService;
import sycpb.platform.cpm.pandora.service.api.resource.creative.IResCreativeService;
import sycpb.platform.cpm.pandora.service.api.resource.creative.ResCreativeApiMapper;
import sycpb.platform.cpm.pandora.service.api.resource.creative.bos.CreativeConfigCampaignUnitInfoBo;
import sycpb.platform.cpm.pandora.service.api.resource.creative.bos.GetSupportBiliNativeBo;
import sycpb.platform.cpm.pandora.service.api.resource.goods.IResCreativeGoodsContentService;
import sycpb.platform.cpm.pandora.service.api.resource.goods.ResCreativeGoodsContentMapper;
import sycpb.platform.cpm.pandora.service.impl.core.v6.creative.CreativeImageService;
import sycpb.platform.cpm.pandora.service.impl.core.v6.creative.CreativeTitleService;
import sycpb.platform.cpm.pandora.service.impl.core.v6.creative.UnitCreativeExtService;
import sycpb.platform.cpm.pandora.service.impl.core.v6.creative.UnitCreativeImplMapper;
import sycpb.platform.cpm.pandora.service.impl.core.v6.creative.bos.GeneralVersionContextBo;
import sycpb.platform.cpm.pandora.service.impl.core.v6.misc.GeneralVersionService;
import sycpb.platform.cpm.pandora.service.impl.core.v6.unit.UnitExtService;
import sycpb.platform.cpm.pandora.service.impl.core.v6.unit.UnitExtraService;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@RpcServiceAspect(domainType = ErrorCodeEnum.DomainType.CREATIVE)
@RPCService
@RequiredArgsConstructor
public class CreativeResourceRpcService extends CreativeResourceServiceGrpc.CreativeResourceServiceImplBase {
    private final IResCreativeService resCreativeService;
    private final IResCreativeGoodsContentService resCreativeContentGoodsService;
    private final CreativeTitleService creativeTitleService;
    private final CreativeImageService creativeImageService;
    private final UnitExtraService unitExtraService;
    private final GeneralVersionService generalVersionService;
    private final UnitCreativeExtService unitCreativeExtService;
    private final UnitExtService unitExtService;
    private final IAccountLabelService accountLabelService;
    private final ICampaignService campaignService;

    @Override
    public void listNestedTemplateGroup(ListNestedTemplateGroupReq request, StreamObserver<ListNestedTemplateGroupResp> responseObserver) {
        final var queryTemplateBo = ResCreativeApiMapper.MAPPER.fromRo(request);
        final var accountLabelIds = accountLabelService.list(request.getAccountId());
        CreativeConfigCampaignUnitInfoBo creativeConfigCampaignUnitInfoBo = queryTemplateBo.getCreativeConfigCampaignUnitInfoBo();

        PandoraAssert.isTrue(Objects.nonNull(queryTemplateBo.getCreativeConfigCampaignUnitInfoBo())
                || NumberUtils.isPositive(queryTemplateBo.getUnitId()), "单元id和计划单元信息对象在查询位置信息时不可同时为空",
                ErrorCodeEnum.DomainType.CONFIG, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);

        boolean hasUnitId = NumberUtils.isPositive(request.getUnitId());
        // 从request里拿的两个字段都是由于和后面的召回模板体系没有关系 只是为了计算generalVersion
        // 我觉得不应该放在queryTemplateBo里 并且未来模板体系也会整体下掉
        var generalVersionCtx = GeneralVersionContextBo.builder()
                .accountLabelIds(accountLabelIds)
                .operatorType(request.getOperator().getOperatorType())
                .currentGeneralVersion(request.getCurrentGeneralVersion())
                .isBiliNative(queryTemplateBo.getIsBiliNative())
                .scene(GeneralVersionContextBo.SCENE_GET_LOCATION)
                .build();

        // 传了单元id的时候 还是保持原逻辑 需要保留兜底
        if (hasUnitId) {
            final var unitBo = unitExtService.getWithCheck(request.getUnitId(), request.getAccountId());
            final var unitExtraBo = unitExtraService.get(request.getUnitId());
            final var campaignBo = campaignService.get(request.getAccountId(), unitBo.getCampaignId());

            generalVersionCtx.setAdType(campaignBo.getAdType());
            generalVersionCtx.setPromotionContentType(unitBo.getPromotionPurposeType());
            generalVersionCtx.setCurrentGeneralVersion(unitExtraBo.getGeneralVersion());
            generalVersionCtx.setParentUnitId(unitExtraBo.getParentUnitId());
        } else {
            generalVersionCtx.setAdType(creativeConfigCampaignUnitInfoBo.getAdType());
            generalVersionCtx.setPromotionContentType(creativeConfigCampaignUnitInfoBo.getPromotionContentType());
        }

        final var generalVersion = generalVersionService.calculateGeneralVersion(generalVersionCtx);
        queryTemplateBo.setGeneralVersion(generalVersion);
        final var bo = resCreativeService.getCompoundTemplateGroup(queryTemplateBo);
        responseObserver.onNext(ResCreativeApiMapper.MAPPER.toRo(bo));
    }

    @Override
    public void getCreativeContent(GetCreativeContentReq request, StreamObserver<GetCreativeContentResp> responseObserver) {
        final var bo = resCreativeService.getCreativeContent(ResCreativeApiMapper.MAPPER.fromRo(request));
        if (Objects.isNull(bo)) throw PandoraDataErr.noMatched("创意内容规则");

        responseObserver.onNext(GetCreativeContentResp.newBuilder()
                .setCreativeContentConfig(ResCreativeApiMapper.MAPPER.toRo(bo))
                .build());
    }

    @Override
    public void getCreativeGoodsContent(GetCreativeGoodsContentReq request, StreamObserver<GetCreativeGoodsContentResp> responseObserver) {
        final var bos = resCreativeContentGoodsService.getGoodsContent(ResCreativeGoodsContentMapper.MAPPER.fromRpcBo(request));
        responseObserver.onNext(GetCreativeGoodsContentResp.newBuilder()
                .addAllEntity(ResCreativeGoodsContentMapper.MAPPER.toRpcBos(bos))
                .build());
    }

    @Override
    public void getCreativeSupportBiliNative(GetCreativeSupportBiliNativeReq request, StreamObserver<GetCreativeSupportBiliNativeResp> responseObserver) {
        GetSupportBiliNativeBo getSupportBiliNativeBo = ResCreativeApiMapper.MAPPER.fromRo(request);
        final var supportBiliNative = resCreativeService.getSupportBiliNative(getSupportBiliNativeBo);
        responseObserver.onNext(GetCreativeSupportBiliNativeResp.newBuilder()
                .setIsSupportBiliNative(supportBiliNative)
                .build());
    }

    @Override
    public void listCreativeTitle(ListCreativeTitleReq request, StreamObserver<ListCreativeTitleResp> responseObserver) {
        final List<LauCreativeTitleBo> lauCreativeTitleBos = creativeTitleService.listByCreativeIds(request.getCreativeIdsList());

        responseObserver.onNext(ListCreativeTitleResp.newBuilder()
                .addAllData(UnitCreativeImplMapper.MAPPER.fromCreativeTitleBos(lauCreativeTitleBos))
                .build());
    }

    @Override
    public void listCreativeImage(ListCreativeImageReq request, StreamObserver<ListCreativeImageResp> responseObserver) {
        List<LauCreativeImageBo> creativeImageBos = creativeImageService.listByCreativeIds(request.getCreativeIdsList());
        responseObserver.onNext(ListCreativeImageResp.newBuilder()
                .addAllData(UnitCreativeImplMapper.MAPPER.fromCreativeImageBos(creativeImageBos))
                .build());
    }
}
