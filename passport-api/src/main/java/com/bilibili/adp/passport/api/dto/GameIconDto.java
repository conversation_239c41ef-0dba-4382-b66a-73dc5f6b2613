package com.bilibili.adp.passport.api.dto;

import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2018/5/23
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GameIconDto {
    @SerializedName("game_base_id")
    private Integer gameBaseId;

    @SerializedName("game_name")
    private String gameName;

    @SerializedName("icon")
    private String gameIcon;

}
