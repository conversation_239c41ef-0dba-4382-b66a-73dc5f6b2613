package com.bilibili.adp.passport.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 动态用户类型枚举。
 *
 * <AUTHOR>
 * @since 2019年04月25日
 */
@Getter
@AllArgsConstructor
public enum DynamicUidTypeEnum {

    BILIBILI(1, "B站用户"),
    CUSTOMER(3, "外部用户");

    private Integer code;
    private String desc;

    public static DynamicUidTypeEnum getByCode(Integer code) {
        for (DynamicUidTypeEnum bean : values()) {
            if (bean.getCode().equals(code)) {
                return bean;
            }
        }
        throw new IllegalArgumentException("动态用户类型枚举编码");
    }

}
