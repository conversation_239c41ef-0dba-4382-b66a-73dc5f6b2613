package com.bilibili.adp.passport.api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @file: ThumbUpLikeStatsReplyDto
 * @author: gaoming
 * @date: 2021/05/17
 * @version: 1.0
 * @description:
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ThumbUpLikeStatsReplyDto implements Serializable {
    private static final long serialVersionUID = -8814540377135444240L;

    /**
     * 来源Id
     */
    private Long originId;

    /**
     * messageId
     */
    private List<Long> messageIds;
    /**
     * biz id
     */
    private Long bizId;

    /**
     * 点赞数
     */
    private Long likeNumber;

    /**
     * 点踩数
     */
    private Long dislikeNumber;


    @Override
    public String toString() {
        return "ThumbUpLikeStatsReqDto{" +
                "originId=" + originId +
                ", likeNumber=" + likeNumber +
                '}';
    }

}
