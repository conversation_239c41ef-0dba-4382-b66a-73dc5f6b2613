package com.bilibili.adp.passport.api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
 * @file: ThumbUpHasLikeNoLoginReplyDto
 * @author: gaoming
 * @date: 2021/06/28
 * @version: 1.0
 * @description:
 **/

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class ThumbUpHasLikeNoLoginReplyDto implements Serializable {

    private static final long serialVersionUID = 5919831782096957410L;
    /**
     * buvid
     */
    private String buvid;

    /**
     * 状态 0-未点赞 1-点赞
     */
    private Integer hasLike;

    /**
     * 状态 0-未点踩 1-点踩
     */
    private Integer hasDislike;
}
