package com.bilibili.risk.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MaterialAuditRuleInfoVo implements Serializable {
    /**
     * 自增主键ID
     */
    @Schema(description = "规则id")
    private String id;

    /**
     * 名称
     */
    @Schema(description = "规则名称")
    private String name;

    /**
     * 拉取方式1逐个队列领取2时间正序领取
     */
    @Schema(description = "拉取方式1逐个队列领取2时间正序领取")
    private Integer pullType;

    /**
     * 顺序
     */
    @Schema(description = "顺序")
    private Integer seq;

    /**
     * 角色列表
     */
    @Schema(description = "角色列表")
    private List<Integer> rolesIds;
    private List<RoleVo> roleBos;
    private List<LauMaterialAuditQueueVo> bindQueues;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 扩展字段
     */
    private String extra;

    /**
     * 是否删除
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    private static final long serialVersionUID = 1L;
}