package com.bilibili.risk.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SubscribeVo {

    @Schema(description = "订阅ID")
    private Long queueId;
    @Schema(description = "订阅类型 1:订阅 2:取消订阅")
    private Integer subscribeType;
}
