package com.bilibili.risk.interceptor;

import com.bilibili.risk.accessLog.policy.impl.HttpPolicy;
import com.bilibili.risk.accessLog.process.AccessLogContext;
import com.bilibili.risk.accessLog.process.AccessLogProcess;
import com.bilibili.risk.accessLog.utils.AccessLogUtil;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.util.ContentCachingRequestWrapper;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025-04-30 14:12
 * 请求日志拦截器
 */
@Component
@Slf4j
public class AccessLogInterceptor implements HandlerInterceptor {

    // 在请求处理之前进行拦截
    @Override
    public boolean preHandle(@NotNull final HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {

        // 初始化http访问日志策略，启动日志处理器
        AccessLogUtil.ignoreEx(() -> initProcess(request));
        return true;
    }

    // 在整个请求完成之后进行拦截
    @Override
    public void afterCompletion(@NotNull HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {

        // 获取当前线程的访问日志处理器
        AccessLogProcess process = AccessLogContext.getProcess();
        if (Objects.isNull(process)) {
            return;
        }

        // 优先从入参ex中获取异常，如果没有，则从上下文中获取
        Throwable e = Objects.nonNull(ex) ? ex : AccessLogContext.getThrowable();
        AccessLogUtil.ignoreEx(() -> process.catchError(e));

        //执行after处理
        AccessLogUtil.ignoreEx(process::after);
    }

    private void initProcess (HttpServletRequest request) {

        HttpServletRequest servletRequest = new ContentCachingRequestWrapper(request);

        // 初始化http访问日志策略，启动日志处理器
        AccessLogProcess process = HttpPolicy.builder()
                .request(servletRequest)
                .build().initProcess();

        // 将 process 设置到上下文
        AccessLogContext.setProcess(process);

        // 执行before处理
        process.before();
    }

}
