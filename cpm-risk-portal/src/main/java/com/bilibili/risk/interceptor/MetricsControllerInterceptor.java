package com.bilibili.risk.interceptor;

import com.bilibili.risk.accessLog.process.AccessLogContext;
import com.bilibili.risk.enums.MetricsCodeEnum;
import com.bilibili.risk.metrics_report.metrics.MetricDataHolder;
import com.bilibili.risk.metrics_report.metrics.RiskMetricsImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * MetricsInterceptor 是一个 Spring MVC 拦截器，实现了 HandlerInterceptor 接口，主要用于监控和记录 HTTP 请求的性能指标和业务状态。
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MetricsControllerInterceptor implements HandlerInterceptor {

    private final RiskMetricsImpl riskMetrics;

    private static final String START_TIME = "startTime";

    @Override
    public boolean preHandle(@NotNull HttpServletRequest request, @NotNull HttpServletResponse response, @NotNull Object handler) {
        // 记录请求开始时间
        request.setAttribute(START_TIME, System.currentTimeMillis());
        return true;
    }

    @Override
    public void afterCompletion(@NotNull HttpServletRequest request, @NotNull HttpServletResponse response, @NotNull Object handler, Exception ex) {
        Long startTime = (Long) request.getAttribute(START_TIME);
        if (startTime == null) {
            return;
        }

        long durationMs = System.currentTimeMillis() - startTime;

        // 构造 MetricDataHolder
        MetricDataHolder data = new MetricDataHolder();
        data.methodName = request.getRequestURI();
        data.domainType = MetricsCodeEnum.DomainType.OTHER.name();

        // 尝试从 AccessLogContext 获取异常信息，这样可以获取到 CustomExceptionHandler 可能记录的同一个异常
        Throwable contextException = AccessLogContext.getThrowable();
        Exception exception = ex != null ? ex : (contextException instanceof Exception ? (Exception) contextException : null);

        int status = response.getStatus();

        // 处理成功情况
        if (exception == null && status < 400) {
            data.type = MetricsCodeEnum.Type.SYS_ERR.name(); // 注意：这里应该是SUCCESS，而不是BIZ_ERR
            data.code = MetricsCodeEnum.SubCode.SUCCESS.getCode();
            data.msg = MetricsCodeEnum.SubCode.SUCCESS.getDesc();
        } else {

            // 处理异常情况
            if (exception != null) {
                // 根据异常类型进行分类，保持与 CustomExceptionHandler 分类逻辑一致
                if (exception instanceof IllegalArgumentException) {
                    data.type = MetricsCodeEnum.Type.BIZ_ERR.name();
                    data.code = MetricsCodeEnum.SubCode.PARAM_INVALID.getCode();
                    data.msg = MetricsCodeEnum.SubCode.PARAM_INVALID.getDesc();
                } else if (exception instanceof NullPointerException) {
                    data.type = MetricsCodeEnum.Type.SYS_ERR.name();
                    data.code = MetricsCodeEnum.SubCode.NULL_POINTER_ERR.getCode();
                    data.msg = MetricsCodeEnum.SubCode.NULL_POINTER_ERR.getDesc();
                } else if (exception instanceof RuntimeException) {
                    data.type = MetricsCodeEnum.Type.BIZ_ERR.name();
                    data.code = MetricsCodeEnum.SubCode.BUSINESS_ERROR.getCode();
                    data.msg = "业务异常: " + exception.getMessage();
                } else {
                    data.type = MetricsCodeEnum.Type.SYS_ERR.name();
                    data.code = MetricsCodeEnum.SubCode.SYSTEM_ERROR.getCode();
                    data.msg = "服务器异常";
                }
            } else {
                // 根据 HTTP 状态码分类
                if (status == 404) {
                    data.type = MetricsCodeEnum.Type.BIZ_ERR.name();
                    data.code = MetricsCodeEnum.SubCode.DATA_NOT_FOUND.getCode();
                    data.msg = MetricsCodeEnum.SubCode.DATA_NOT_FOUND.getDesc();
                } else if (status == 403) {
                    data.type = MetricsCodeEnum.Type.BIZ_ERR.name();
                    data.code = MetricsCodeEnum.SubCode.PERMISSION_DENIED.getCode();
                    data.msg = MetricsCodeEnum.SubCode.PERMISSION_DENIED.getDesc();
                } else if (status >= 400 && status < 500) {
                    data.type = MetricsCodeEnum.Type.BIZ_ERR.name();
                    data.code = String.valueOf(status);
                    data.msg = "客户端错误";
                } else {
                    data.type = MetricsCodeEnum.Type.SYS_ERR.name();
                    data.code = String.valueOf(status);
                    data.msg = "服务器错误";
                }
            }
        }

        // 获取请求来源信息
        String requestSource = request.getHeader("x-adp-request-source");
        requestSource = requestSource == null ? RiskMetricsImpl.DEFAULT_REQUEST_SOURCE : requestSource;

        // 检查是否为MAPI请求
        data.isMapi = checkIfMapiRequest(request);

        // 上报指标
        riskMetrics.reportMetricsCount(RiskMetricsImpl.METRIC_KEY_CONTROLLER_REQUEST_COUNT, data, requestSource);
        riskMetrics.reportMetricsHistogram(RiskMetricsImpl.METRIC_KEY_CONTROLLER_LATENCY, data, requestSource, durationMs);

        log.info("MetricsInterceptor | data: {} | Duration: {}ms | Status: {} | Type: {} | Code: {} | Msg: {}",
                data, durationMs, status, data.type, data.code, data.msg);
    }

    /**
     * 判断请求是否为MAPI请求
     * 可以根据实际业务逻辑调整判断标准
     */
    private boolean checkIfMapiRequest(HttpServletRequest request) {
        // 例如：通过特定的请求头或参数判断
        String mapiFlag = request.getHeader("x-mapi-flag");
        if (mapiFlag != null && "1".equals(mapiFlag)) {
            return true;
        }

        // 或者通过请求路径判断
        String uri = request.getRequestURI();
        return uri != null && uri.contains("/mapi/");
    }

}
