package com.bilibili.adp.resource.api.targetmeta;

import java.io.Serializable;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by walker on 16/9/13.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TargetItemTree implements Serializable {

    private static final long serialVersionUID = 6029392030152940066L;

    private TargetItem targetItem;

    private List<TargetItemTree> sons;

}
