package com.bilibili.adp.resource.api.app_package;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.resource.api.app_package.dto.*;

import java.util.List;
import java.util.Map;

@Deprecated
public interface IAppPackageService {

    /**
     * 添加 apk 信息
     *
     * @param dto
     * @param operator
     * @return
     */
    int create(AppPackageDto dto, Operator operator);

    /**
     * 修改 apk 信息
     *
     * @param dto
     * @param operator
     */
    void update(AppPackageDto dto, Operator operator);

    /**
     * 删除 apk 信息
     *
     * @param id
     * @param operator
     */
    void delete(Integer id, Operator operator);

    /**
     * 根据 id 获取 apk 信息
     * 从 db 获取
     *
     * @param id
     * @return
     */
    AppPackageDto load(Integer id);

    List<AppPackageDto> query(QueryAppPackageDto query);

    PageResult<AppPackageDto> queryByPage(QueryAppPackageDto query);

    Map<Integer, String> getIconUrlInPrimaryKeys(List<Integer> ids);

    Map<Integer, AppPackageDto> getAllMapInPrimaryKeys(List<Integer> ids);

    /**
     * 根据任务ID获取apk下载信息
     *
     * @param taskId
     * @return
     */
    ApkDownloadTaskInfo getTask(String taskId);

    /**
     * 根据 apk url 提交任务
     *
     * @param url 应用包链接
     * @param operator 操作人
     * @param applyToJob 此次操作是否应用于刷新应用包信息的定时任务
     * @param appPackageId 应用包Id,刷新应用包信息的定时任务需要
     */
    String addTask(String url, Operator operator, boolean applyToJob, Integer appPackageId);

    void enable(Integer id, Operator operator);

    void disable(Integer id, Operator operator);

    /**
     * 刷新 apk 信息
     *
     * @param appPackageId
     * @param taskId
     * @param operator
     * @return
     * @throws ServiceException
     */
    int refresh(Integer appPackageId, String taskId, Operator operator) throws ServiceException;

    /**
     * 根据任务id获取 apk 信息
     * 会保存 icon
     *
     * @param taskId
     * @param appAlias
     * @param operator
     * @return
     * @throws ServiceException
     */
    int upload(String taskId, String appAlias, Operator operator) throws ServiceException;

    FlyProPackageDto newFlyUpload(String taskId, String appAlias, Operator operator) throws ServiceException;

    String refreshExistApks();

    void enableByAdmin(Integer id, Operator operator);

    void disableByAdmin(Integer id, Operator operator);

    String getInternalUrl(String md5, String url);

    Map<Integer, String> getAppNameMapInPrimaryKeys(List<Integer> ids);

    void addIosAppPackage(AppPackageDto dto, Operator operator);

    void refreshApksInIds(List<Integer> ids);

    void refreshApk(Integer appId);

    /**
     * 根据包名获取App的开发商和隐私政策地址
     * example: https://app.mi.com/details?id=com.xiaomi.loan
     *
     * @param packageName 应用包名
     */
    AppOutSideSpiderInfoDto getDeveloperName(String packageName);

    Integer getDmpAppIdByAppName(String appName);

    void refreshResAppPackageDmpId();

    /**
     * 定时任务将refreshApkForJob刷新的apk信息更新到数据库
     * https://www.tapd.bilibili.co/67874887/prong/stories/view/1167874887002869915
     */
    void refreshResAppPackageJob() throws InterruptedException;
}
