package com.bilibili.adp.resource.api.app_package.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FlyProPackageDto {
    private Integer id;
    private String appName;
    private String link;
    private String platform;
    private String title;
    private String version;
}
