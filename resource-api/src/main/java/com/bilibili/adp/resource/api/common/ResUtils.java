package com.bilibili.adp.resource.api.common;

import com.bilibili.adp.common.enums.PreferScene;
import com.bilibili.adp.common.enums.Scene;
import com.bilibili.adp.common.util.CollectionHelper;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.resource.api.slot_group.ResSlotGroupTemplateMappingDto;
import com.bilibili.adp.resource.api.slot_group.SceneDto;
import com.bilibili.location.api.template.dto.TemplateDto;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 资源工具类。
 *
 * <AUTHOR>
 * @since 2020年06月12日
 */

public final class ResUtils {
    private ResUtils() {
        throw new UnsupportedOperationException();
    }

    /**
     * 从广告位组列表提取场景列表
     *
     * @param validSlotGroups
     * @param unitIsNobid 单元是否是 nobid
     * @return
     */
    public static List<SceneDto> getScenesOfSlotGroupTemplateMappings(List<ResSlotGroupTemplateMappingDto> validSlotGroups, Integer unitIsNobid) {
        if (CollectionHelper.isEmpty(validSlotGroups)) {
            return Collections.emptyList();
        }

        Map<Integer, Optional<SceneDto>> sceneDtoMap = validSlotGroups.stream()
                // slotGroup != null
                .filter(dto -> dto.getSlotGroup() != null)
                // 存在模板
                .filter(dto -> CollectionHelper.isNotEmpty(dto.getTemplates()))
                // 过滤出指定场景的广告位组
                .filter(dto -> Utils.isPositive(dto.getSlotGroup().getScene()))
                .filter(dto -> {
                    // 单元不是 nobid 不作处理
                    if (!Utils.isPositive(unitIsNobid)) {
                        return true;
                    }
                    // 单元 nobid, 要求模板组也支持 nobid
                    if (dto.getSlotGroup() != null && Utils.isPositive(dto.getSlotGroup().getSupportNobid())) {
                        return true;
                    }
                    return false;
                })
                .map(dto -> SceneDto.builder()
                        .preferScene(dto.getSlotGroup().getPreferScene())
                        .scene(dto.getSlotGroup().getScene())
                        .sceneName(Scene.getByCode(dto.getSlotGroup().getScene()).getDesc())
                        .validSlotGroupTemplates(newSmallList(dto))
                        .build())
                .collect(Collectors.groupingBy(SceneDto::getScene,
                        Collectors.reducing((sceneDto, sceneDtoOther) -> {
                            sceneDto.getValidSlotGroupTemplates()
                                    .addAll(sceneDtoOther.getValidSlotGroupTemplates());
                            return sceneDto;
                        })));

        return sceneDtoMap.values().stream().map(Optional::get).collect(Collectors.toList());
    }

    public static List<ResSlotGroupTemplateMappingDto> getPreferSlotGroupTemplateMappings(List<ResSlotGroupTemplateMappingDto> validSlotGroups) {
        if (CollectionHelper.isEmpty(validSlotGroups)) {
            return Collections.emptyList();
        }
        return validSlotGroups.stream()
                .filter(dto -> dto.getSlotGroup() != null)
                .filter(dto -> CollectionHelper.isNotEmpty(dto.getTemplates()))
                // 过滤出优选广告位组
                .filter(dto -> Objects.equals(PreferScene.PREFER_SCENE.getCode(), dto.getSlotGroup().getPreferScene()))
                .collect(Collectors.toList());
    }

    public static List<ResSlotGroupTemplateMappingDto> getSceneSlotGroupTemplateMappings(List<ResSlotGroupTemplateMappingDto> validSlotGroups, List<Integer> scenes) {
        if (CollectionHelper.isEmpty(validSlotGroups)) {
            return Collections.emptyList();
        }
        return validSlotGroups.stream()
                .filter(dto -> dto.getSlotGroup() != null)
                .filter(dto -> CollectionHelper.isNotEmpty(dto.getTemplates()))
                // 过滤出指定场景的广告位组
                .filter(dto -> Utils.isPositive(dto.getSlotGroup().getScene()))
                .filter(dto -> CollectionHelper.emptyOrContains(scenes, dto.getSlotGroup().getScene()))
                .collect(Collectors.toList());
    }

    public static List<Integer> getSlotGroupMappingTemplateIds(List<ResSlotGroupTemplateMappingDto> validSlotGroups) {
        if (CollectionHelper.isEmpty(validSlotGroups)) {
            return Collections.emptyList();
        }
        return validSlotGroups.stream()
                .filter(dto -> dto.getSlotGroup() != null)
                .map(ResSlotGroupTemplateMappingDto::getTemplates)
                .filter(CollectionHelper::isNotEmpty)
                .flatMap(Collection::stream)
                .map(TemplateDto::getTemplateId)
                .distinct()
                .collect(Collectors.toList());
    }

    private static <E> List<E> newSmallList(E e) {
        List<E> list = new ArrayList<>(5);
        list.add(e);
        return list;
    }

}
