package com.bilibili.adp.resource.api.sen_rule.upgrade;

import com.bilibili.adp.account.dto.CmCenterCategoryDto;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.resource.api.sen_rule.dto.SensitiveUserDto;
import com.bilibili.adp.resource.api.sen_rule.upgrade.dto.*;

import java.util.List;
import java.util.Set;

/**
 * @ClassName ISensitiveRuleUpgradeService
 * <AUTHOR>
 * @Date 2022/4/13 12:51 上午
 * @Version 1.0
 **/
public interface ISenRuleUpgradeService {

    PageResult<SenRuleUpgradeDto> queryRulesByPage(QuerySenRuleUpgradeDto queryParam);

    List<SenRuleUpgradeDto> queryRules(QuerySenRuleUpgradeDto queryParam);

    SenRuleUpgradeDto getRuleById(Integer ruleId);

    SenRuleUpgradeDto getRuleBaseInfoById(Integer ruleId);

    int createRule(Operator operator, SaveSenRuleUpgradeDto createRuleDto);

    void updateRule(Operator operator, SaveSenRuleUpgradeDto updateRuleDto);

    List<Integer> getExistRules(SaveSenRuleUpgradeDto dto);

    List<SenRuleUpgradeDto> getExistBaseRulesByName(String ruleName);

    void enableRule(Operator operator, Integer ruleId);

    void disableRule(Operator operator, Integer ruleId);

    void addMid(Operator operator, SensitiveUserDto userDto) throws ServiceException;

    void removeMid(Operator operator, SensitiveUserDto userDto) throws ServiceException;

    Set<Long> getExistMids(Integer ruleId, Integer type);

    List<CmCenterCategoryDto> getAllCmCenterCategories();

    List<ArcCategoryDto> getArcCategories();

}
