syntax = "proto3";

import "extension/wdcli/wdcli.proto";
import "bilibili/intl/app/opus/common/opus.proto";

package intl.opus.service.v1;
option go_package = "buf.bilibili.co/bapis/bapis-gen/intl/opus.service;v1";
option java_package = "com.bapis.intl.opus.service";
option java_multiple_files = true;
option (wdcli.appid) = "intl.opus.service";

// 图文内网基础服务
service opus {
  // 创建图文内容
  rpc CreateOpus(CreateOpusReq) returns (CreateOpusRsp);
  // 删除图文，该接口不对外开放
  rpc RemoveOpus(RemoveOpusReq) returns (RemoveOpusRsp);
  // 获取图文基本信息，无视可见状态
  rpc FetchDesc(FetchDescReq) returns (FetchDescRsp);
  // following-图文列表 星辰feed调用(offset翻页，一直翻到空页)
  rpc OpusFeed(OpusFeedReq) returns (OpusFeedRsp);
  // following-up图文列表 星辰feed调用，查询用户的图文列表
  rpc UsersOpusList(UsersOpusListReq) returns (UsersOpusListRsp);
  // 通过图文ID查询图文详情 图文网关调用
  rpc Opus(OpusReq) returns (OpusRsp);
  // 【后台接口】通过图文ID查询图文详情 图文网关调用 无视可见状态
  rpc IOpus(IOpusReq) returns (OpusRsp);
  // 通过图文ID批量获取图文内容 收藏列表图文网关调用、天马调用
  rpc BatchGetOpuses(BatchGetOpusesReq) returns (BatchGetOpusesRsp);
  // 【后台接口】批量获取图文内容 无视可见状态
  rpc IBatchGetOpuses(IBatchGetOpusesReq) returns (IBatchGetOpusesRsp);
  // 创作中心-我的图文列表 图文网关调用(offset翻页，一直翻到空页)
  rpc CreationOpusList(CreationOpusListReq) returns (CreationOpusListRsp);
  // 图文状态变更
  rpc ModifyOpusState(ModifyOpusStateReq) returns (ModifyOpusStateRsp);
  // 获取图文浏览数
  rpc GetOpusView(GetOpusViewReq) returns (GetOpusViewRsp);
  // 判断用户是否发布过图文 图文网关调用
  rpc IsPublishOpus(IsPubOpusReq) returns (IsPubOpusRes);
  // 绑定opus和activity id
  rpc BindActivityToOpus(BindActReq) returns(BindActRes);
}

// 图文索引
message OpusIndex {
  // 图文id
  int64 opus_id = 1;
  // 用户uid
  int64 uid = 2;
  // 图文类型：1-图文（上图下文） 2-专栏（图文混排）
  int32 opus_type = 3;
  // acl 权限控制位
  int64 acl = 4;
  // i18n json.Marshal后的字符串，避免字段超过长度，只保留 app_lang_id, app_region_id, ip_region_id
  string i18n = 5;
  // 是否原创内容 0-否 1-是
  int32 is_original = 6;
}

message CreateOpusReq {
  // 投稿用户id
  int64 uid = 1;
  // 投稿内容
  bilibili.intl.app.opus.common.Opus post_data = 2;
  // 是否原创内容
  bool is_original = 3;
  // i18n json.Marshal后的字符串
  string i18nStr = 4;
}

message CreateOpusRsp {
  // 创建成功生成的图文id
  int64 opus_id = 1;
  // 创建成功生成的图文id字符串
  string opus_id_str = 2;
}

enum RmAction {
  MIN = 0;  // 下限 用于参数校验
  USER_REMOVE = 1;  // 用户操作,彻底删除
  FORBID = 2;  // 驳回，来自审核或管理后台操作
}

message RemoveOpusReq {
  // 图文id
  int64 opus_id = 1;
  // 用户uid
  int64 uid = 2;
  // 移除动作
  RmAction action = 3;
  // 删除原因
  string reason = 4;
  // 操作人
  string auditor = 5;
}

message RemoveOpusRsp {
}

message FetchDescReq {
  // 图文ID列表
  repeated int64 opus_ids = 1;
  // 是否弱依赖redis：0强依赖，1弱依赖
  int32 weakDependRedis = 2;
}

message FetchDescRsp {
  // 图文ID -> 图文基本信息
  map<int64, OpusIndex> desc = 1;
}

// 图文的访问权限控制信息
message OpusACL {
  bool visible = 1;  // 是否可见；true可见，false不可见
  bool auditing = 2;  // 是否审核中；true审核中，false非审核中
  bool rejected = 3;  // 是否驳回；true驳回，false非驳回
}

message OpusListCard {
  // 图文ID
  int64 opus_id = 1;
  // 图文内容
  bilibili.intl.app.opus.common.Opus opus = 2;
  // 访问控制标记位
  OpusACL acl = 3;
}

message OpusFeedLister {
  // 用户id
  int64 uid = 1;
}

message OpusFeedReq {
  // 拉取上限，拉取小于该上限的一页图文，第一页不用传
  int64 up_bound = 1;
  // 用户登录id
  int64 login_uid = 2;
  // 用户列表
  repeated OpusFeedLister lister_list = 3;
  // 每页几条
  int64 page_size = 4;
}

message OpusFeedItem {
  // 图文ID
  int64 opus_id = 1;
  // 图文发布时间
  int64 pub_time = 2;
}

// 只下发当前登录用户可见的图文资源
message OpusFeedRsp {
  // 最大图文id
  int64 max_opus_id = 1;
  // 最小图文ID（作为下一页的拉取上限）
  int64 offset_opus_id = 2;
  // 图文列表
  repeated OpusFeedItem list = 3;
}

message UsersOpusListReq {
  // 用户id列表
  repeated int64 uids = 1;
  // 登录用户id
  int64 login_uid = 2;
  // page_size
  int64 page_size = 3;
  // page_num
  int64 page_num = 4;
}

message OpusList {
  repeated OpusFeedItem list = 1;
}

message UsersOpusListRsp {
  // 用户图文列表
  map<int64, OpusList> opus_list = 1;
}

message OpusReq {
  // 图文id
  int64 opus_id = 1;
  // 登录用户id
  int64 login_uid = 2;
}

message OpusRsp {
  // 图文内容
  bilibili.intl.app.opus.common.Opus opus = 1;
}

message IOpusReq {
  // 图文id
  int64 opus_id = 1;
}

message IBatchGetOpusesReq {
  // 图文id列表
  repeated int64 opus_ids = 1;
}

// 图文简单内容信息
message OpusSimpleContent {
  // 图文标题
  string title = 1;
  // 图文封面图片（仅专栏）
  string cover = 2;
  // 图文正文
  string content = 3;
  // 正文图片url列表
  repeated string pics = 4;
}

// 图文简单信息
message OpusSimpleInfo {
  // 图文id
  int64 opus_id = 1;
  // 用户uid
  int64 uid = 2;
  // 图文类型：1-图文（上图下文） 2-专栏（图文混排）
  int32 opus_type = 3;
  // 图文状态: 0-已通过, 1-审核中, 2-已驳回, 3-已删除
  int32 state = 4;
  // 是否仅自见
  bool self_visible = 5;
  // 发布时间戳
  int64 pub_time = 6;
  // 投稿region_id
  int64 region_id = 7;
  // 图文简单内容信息
  OpusSimpleContent simple_content = 8;
}

message IBatchGetOpusesRsp {
  // 图文简单信息
  map<int64, OpusSimpleInfo> simple_infos = 1;
}

message BatchGetOpusesReq {
  // 图文id列表
  repeated int64 opus_ids = 1;
  // 登录用户id
  int64 login_uid = 2;
}

message BatchGetOpusesRsp {
  // 图文内容列表
  map<int64, bilibili.intl.app.opus.common.Opus> opuses = 1;
}

enum CreationFilterType {
  // 全部
  ALL = 0;
  // 公开可见
  PUBLIC = 1;
  // 审核中
  AUDITING = 2;
  // 未通过
  REJECTED = 3;
  // 公开可见 去除自见数据
  PUBLIC_VISIBLE = 4;
}

enum CreationSortType {
  // 默认排序 时间序由新到旧
  TIME_DESC = 0;
  // 时间序 由旧到新
  TIME_ASC = 1;
}

message CreationOption {
  // 筛选项
  CreationFilterType filter_type = 1;
  // 排序项
  CreationSortType sort_type = 2;
}

message CreationOpusListReq {
  // 用户登录id
  int64 login_uid = 1;
  // 偏移offset
  string offset = 2;
  // 每页几条
  int64 page_size = 3;
  // 筛选项
  CreationOption option = 4;
}

message CreationOpusListRsp {
  // 列表
  repeated OpusListCard cards = 1;
  // 偏移offset
  string offset = 2;
}

message ModifyOpusStateReq {
  int64 opus_id = 1;  // 图文id
  int32 action = 2;  // 操作类型：0-通过, 1-驳回, 2-自见
  string auditor = 3;  // 操作人
  string extra = 4;  // 额外信息(驳回原因等)
}

message ModifyOpusStateRsp {
}

message GetOpusViewReq {
  // 图文id
  int64 opus_id = 1;
}

message GetOpusViewRsp {
  // 浏览数
  int64 view_cnt = 1;
}

message IsPubOpusReq {
  // 用户uid
  int64 uid = 1;
}

message IsPubOpusRes {
  // 是否发布
  bool is_publish = 1;
}

message BindActReq {
  int64 uid = 1; //用户id
  int64 opus_id = 2; //图文id
  int64 act_id = 3; //activity id
}

message BindActRes {
  bool is_bind = 1;
}