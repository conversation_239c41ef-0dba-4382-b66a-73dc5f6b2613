#!/usr/bin/env python3
import os
import requests
import subprocess

CLONE_REPO_DIR = '/tmp/bapis'


def commit_sha():
    return os.getenv('AGILEFLOW_COMMIT_SHA', 'origin/master')


COMMIT_SHA = commit_sha()


def get_git_token_secret():
    return os.getenv('GIT_TOKEN_SECRET', '')


def bapis_repo_url():
    return 'https://bapisbot:{GIT_TOKEN_SECRET}@git.bilibili.co/bapis/bapis.git'.format(
        GIT_TOKEN_SECRET=get_git_token_secret(),
    )


def get_agile_mr_commit_branch():
    return os.getenv('AGILEFLOW_COMMIT_BRANCH', '')


def get_agile_mr_id():
    return os.getenv('AGILEFLOW_MR_ID', '')


def clone_bapis_repo():
    subprocess.run(['git', 'clone', '--branch', get_agile_mr_commit_branch(),
                    bapis_repo_url(), CLONE_REPO_DIR], check=True)


def generate_buf_yaml_all():
    subprocess.run(['buf-yaml-gen', 'gen', '-r',
                    '-f', CLONE_REPO_DIR], check=True)


def git_branch_outdated():
    cmd = ['git', 'rev-list', '--left-right', '--count',
           'origin/master...origin/{AGILE_MR_COMMIT_BRANCH}'.format(AGILE_MR_COMMIT_BRANCH=get_agile_mr_commit_branch()), '--']
    print(cmd)
    p = subprocess.run(cmd, check=True, capture_output=True)
    if p.returncode != 0:
        return False
    parts = [int(i) for i in p.stdout.split() if i]
    behind, ahead = parts[0], parts[1]
    print("The source branch is {} commits behind and {} commits ahead the target branch".format(
        behind, ahead))
    if behind >= 100:
        return True
    return False


def git_status():
    subprocess.run(['git', 'status'], check=True)


def git_changed_files():
    p = subprocess.run(['git', 'status', '--porcelain'],
                       check=True, capture_output=True)
    if not p.stdout.strip():
        return []
    lines = [i.decode('utf-8').split()[-1] for i in p.stdout.splitlines()]
    lines.sort()
    return lines


def git_config_user():
    subprocess.run(['git', 'config', '--global', 'user.email',
                    '<EMAIL>'], check=True)
    subprocess.run(['git', 'config', '--global',
                    'user.name', 'bapisbot'], check=True)


def commit_message():
    return '自动生成 buf.yaml，分支: {AGILE_MR_COMMIT_BRANCH}, 版本号: {COMMIT_SHA}'.format(
        AGILE_MR_COMMIT_BRANCH=get_agile_mr_commit_branch(),
        COMMIT_SHA=COMMIT_SHA,
    )


def commit_and_push():
    subprocess.run(['git', 'add', '.'], check=True)
    subprocess.run(['git', 'commit', '-m', commit_message()], check=True)
    subprocess.run(['git', 'push'], check=True)


def push_directly():
    subprocess.run(['git', 'push'], check=True)


def merge_origin_master():
    subprocess.run(['git', 'merge', '--no-edit', 'origin/master'], check=True)


def stop_current_ci_job():
    raise Exception('因文件变动，将跳过本次 CI，直接等待下次 CI 即可')


def create_mr_note(changed):
    url = 'https://git.bilibili.co/api/v4/projects/9668/merge_requests/{AGILE_MRID}/notes'.format(
        AGILE_MRID=get_agile_mr_id(),
    )
    changed_files_text = '\n'.join(['- {}'.format(i) for i in changed])
    message = """
#### 已自动生成 buf.yaml 文件，如果包含非自身项目可以尝试先合并 master。

合并过程中遇到任何问题可以联系 @zhoujiahui。

变更文件：
{}
    """.format(changed_files_text)
    body = {
        'body': message,
    }
    headers = {
        'PRIVATE-TOKEN': '**************************',
    }
    return requests.post(url, headers=headers, data=body)


def main():
    clone_bapis_repo()
    os.chdir(CLONE_REPO_DIR)

    git_config_user()
    merge_origin_master()
    if git_branch_outdated():
        push_directly()
        stop_current_ci_job()
        return

    generate_buf_yaml_all()
    changed = git_changed_files()
    if changed:
        commit_and_push()
        create_mr_note(changed)
        stop_current_ci_job()


if __name__ == "__main__":
    main()
