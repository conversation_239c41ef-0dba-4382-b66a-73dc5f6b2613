# 推荐使用 go.buf.gen.yaml
version: v1
managed:
  enabled: true
  go_package_prefix:
    default: git.bilibili.co/bapis/bapis-go
    except:
      - buf.bilibili.co/google/api
      - buf.bilibili.co/google/protobuf
      - buf.bilibili.co/google/rpc
      - buf.bilibili.co/gogo/protobuf
plugins:
  - name: gofast
    out: bapis-go
    opt:
      - Mgoogle/protobuf/any.proto=github.com/gogo/protobuf/types
      - Mgoogle/protobuf/duration.proto=github.com/gogo/protobuf/types
      - Mgoogle/protobuf/struct.proto=github.com/gogo/protobuf/types
      - Mgoogle/protobuf/timestamp.proto=github.com/gogo/protobuf/types
      - Mgoogle/protobuf/wrappers.proto=github.com/gogo/protobuf/types
      - plugins=grpc
      - paths=source_relative
  - name: wdcli
    out: bapis-go
    opt: paths=source_relative
  - name: bm
    out: bapis-go
    opt:
      - explicit_http=true
      - paths=source_relative
  - name: ecode
    out: bapis-go
    opt:
      - paths=source_relative
