syntax = "proto3";

package live.artemis.v1;

import "extension/wdcli/wdcli.proto";
import "github.com/gogo/protobuf/gogoproto/gogo.proto";

option go_package = "buf.bilibili.co/bapis/bapis-gen/live/artemis.v1";
option java_package = "com.bapis.live.artemis.v1";
option java_multiple_files = true;
option (wdcli.appid) = "live.artemis";

service Audit {
    // 批量通过项目id获取项目
    rpc GetMultAppDetailById(GetMultAppByIdReq) returns (GetMultAppByIdResp);
}


message GetMultAppByIdReq {
  repeated int64 app_ids = 1 [(gogoproto.moretags) = 'form:"app_ids" validate:"required"'];
}

message GetMultAppByIdResp {
  // key: 项目id
  map<int64, APP> apps = 1 [(gogoproto.jsontag) = "apps"];
}


message APP {
  enum Level {
    INVALID = 0;
    S = 1;
    A = 2;
    B = 3;
    C = 4;
    D = 5;
  }

  // App名称
  string app_name = 1 [(gogoproto.jsontag) = "app_name"];
  // App分类(0:互动游戏 1:插件 2:直播工具)
  int32 app_type = 2 [(gogoproto.jsontag) = "app_type"];
  // App图标
  string app_icon = 3 [(gogoproto.jsontag) = "app_icon"];
  // App线上版本
  string app_version = 4 [(gogoproto.jsontag) = "app_version"];
  // App创建时间
  string create_time = 5 [(gogoproto.jsontag) = "create_time"];
  // App更新时间
  string update_time = 6 [(gogoproto.jsontag) = "update_time"];
  // AppId
  int64 app_id = 7 [(gogoproto.jsontag) = "app_id"];
  // AppOwnerId
  int64 app_owner_id = 8 [(gogoproto.jsontag) = "app_owner_id"];
  // App版本状态 0:无状态 1:开发中 2:测试中 3:审核中 4:审核失败 5:待上架 6:已上架 7:已下架
  int32 app_status = 9 [(gogoproto.jsontag) = "app_status"];
  // App版本上线状态 0:未上架 1:已上架 2:已下架
  int32 app_release_status = 10 [(gogoproto.jsontag) = "app_release_status"];
  // 项目等级
  APP.Level level = 11 [(gogoproto.jsontag) = "level"];
  // 等级更新时间
  string level_update_time = 12 [(gogoproto.jsontag) = "level_update_time"];
  // 是否可以删除: 1可以 0不可以
  int32 can_delete = 13 [(gogoproto.jsontag) = "can_delete"];
  // 是否可以重名名: 1可以 0不可以
  int32 can_rename = 14 [(gogoproto.jsontag) = "can_rename"];
}

