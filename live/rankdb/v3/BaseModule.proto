syntax = "proto3";

package live.rankdb.v3;
option go_package = "buf.bilibili.co/bapis/bapis-gen/live/rankdb.v3;v3";

option java_multiple_files = true;
option java_package = "com.bapis.live.rankdb.v3";

option (wdcli.appid) = "live.live.rankdb";
import "extension/wdcli/wdcli.proto";

import "github.com/gogo/protobuf/gogoproto/gogo.proto";

service BaseModule {
  rpc DeleteModule (DeleteModuleReq) returns (BaseModuleResp);
}

message DeleteModuleReq {
  int64 uid = 1 [(gogoproto.moretags) = 'form:"uid" validate:"required"'];
  int64 ruid = 2 [(gogoproto.moretags) = 'form:"ruid" validate:"required"'];
  int64 roomId = 3 [(gogoproto.jsontag) = "room_id"];
  //榜单业务Id
  string businessId = 4 [(gogoproto.jsontag) = "business_id", (gogoproto.moretags) = 'form:"business_id" validate:"required"'];
  // 踢榜原因
  string reason = 5 [(gogoproto.moretags) = 'form:"reason"'];
  // 分区信息
  int64 parentAreaId = 6 [(gogoproto.jsontag) = "parent_area_id"];
  int64 areaId = 7 [(gogoproto.jsontag) = "area_id"];
}

message BaseModuleResp {
  //处理结果 0:成功 1:失败不重试 2:失败重试
  int64 status = 1 [(gogoproto.jsontag) = "status"];
}
