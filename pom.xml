<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>sycpb.platform.cpm.risk</groupId>
    <artifactId>cpm-risk</artifactId>
    <version>1.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>cpm-risk</name>
    <description>cpm-risk</description>

    <modules>
        <module>cpm-risk-common</module>
        <module>cpm-risk-biz</module>
        <module>cpm-risk-portal</module>
        <module>cpm-risk-job</module>
    </modules>

    <properties>
        <java.source.version>1.8</java.source.version>
        <java.target.version>1.8</java.target.version>
        <file.encoding>UTF-8</file.encoding>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <maven.compiler.compilerVersion>8</maven.compiler.compilerVersion>
        <maven-compiler-plugin.version>3.8.1</maven-compiler-plugin.version>
        <warp-spring.version>1.2.5-RC4</warp-spring.version>
        <databus.verson>1.2.5-RC4</databus.verson>
        <autoconfigure.version>1.2.5-RC4</autoconfigure.version>
        <shardingjdbc.version>*******</shardingjdbc.version>
        <pleiades.version>1.3.5</pleiades.version>
        <mybatis-spring-boot.version>2.3.2</mybatis-spring-boot.version>
        <mybatis-spring.version>2.0.5</mybatis-spring.version>
        <mybatis.version>3.5.5</mybatis.version>
        <mybatis-plus.version>3.5.3.1</mybatis-plus.version>
        <mysql.version>8.0.33</mysql.version>
        <lombok.version>1.18.16</lombok.version>
        <jackson.version>2.14.0</jackson.version>
        <rbac.version>2.1.3-SNAPSHOT</rbac.version>
        <adp.version>8.1.45.9-SNAPSHOT</adp.version>
        <mapstruct.version>1.5.3.Final</mapstruct.version>
        <!-- 需要用1.59.1.1-->
        <bapi.version>1.59.1.1.master.17502322240000.715bf1c66711</bapi.version>
        <cat.version>1.1.0-SNAPSHOT</cat.version>
        <bjcom-sso.version>1.1.1-SNAPSHOT</bjcom-sso.version>
        <pleiades.verson>1.1.11.RELEASE</pleiades.verson>
<!--        <config-refresh.version>3.0.5-RELEASE</config-refresh.version>-->
        <paladin-client.version>2.0.6</paladin-client.version>
        <spring.version>5.3.31</spring.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.bilibili</groupId>
                <artifactId>warp-bom</artifactId>
                <version>${warp-spring.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>pleiades.component.datasource</groupId>
                <artifactId>mysql</artifactId>
                <version>${pleiades.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>${mybatis-spring-boot.version}</version>
            </dependency>
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.version}</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <optional>true</optional>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-annotations</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.datatype</groupId>
                <artifactId>jackson-datatype-jdk8</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.datatype</groupId>
                <artifactId>jackson-datatype-jsr310</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.module</groupId>
                <artifactId>jackson-module-parameter-names</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-classic</artifactId>
                <version>1.3.15</version> <!-- 请根据需要选择版本 -->
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-core</artifactId>
                <version>1.3.15</version> <!-- 确保使用最新稳定版本 -->
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-pool2</artifactId>
                <version>2.11.1</version>
            </dependency>
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson</artifactId>
                <version>3.16.8</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.shardingsphere</groupId>
                <artifactId>sharding-jdbc-core</artifactId>
                <version>${shardingjdbc.version}</version>
            </dependency>
            <!-- software.amazon.awssdk 相关依赖版本统一管理 -->
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>bom</artifactId>
                <version>2.26.8</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.bilibili.bjcom</groupId>
                <artifactId>bjcom-cat-spring</artifactId>
                <version>${cat.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bilibili.bjcom</groupId>
                <artifactId>bjcom-cat-mybatis</artifactId>
                <version>${cat.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bilibili.bjcom</groupId>
                <artifactId>bjcom-sso</artifactId>
                <version>${bjcom-sso.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.fasterxml.jackson.core</groupId>
                        <artifactId>jackson-databind</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping.cat</groupId>
                <artifactId>cat-client</artifactId>
                <version>1.5.3-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.bilibili.config</groupId>
                <artifactId>config</artifactId>
                <version>2.3.3</version>
            </dependency>
<!--            <dependency>-->
<!--                <groupId>com.bilibili.config</groupId>-->
<!--                <artifactId>config-refresh</artifactId>-->
<!--                <version>${config-refresh.version}</version>-->
<!--                <exclusions>-->
<!--                    <exclusion>-->
<!--                        <groupId>org.springframework.boot</groupId>-->
<!--                        <artifactId>spring-boot</artifactId>-->
<!--                    </exclusion>-->
<!--                    <exclusion>-->
<!--                        <groupId>com.bilibili</groupId>-->
<!--                        <artifactId>paladin-client</artifactId>-->
<!--                    </exclusion>-->
<!--                    <exclusion>-->
<!--                        <groupId>ch.qos.logback</groupId>-->
<!--                        <artifactId>logback-classic</artifactId>-->
<!--                    </exclusion>-->
<!--                </exclusions>-->
<!--            </dependency>-->
            <!--<dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-core</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bilibili</groupId>
                <artifactId>paladin-client</artifactId>
                <version>${paladin-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bilibili</groupId>
                <artifactId>warp-databus</artifactId>
                <version>${databus.verson}</version>
            </dependency>
            <dependency>
                <groupId>pleiades.venus</groupId>
                <artifactId>starter</artifactId>
                <version>${pleiades.verson}</version>
            </dependency>
            <dependency>
                <groupId>com.bilibili</groupId>
                <artifactId>warp-spring-boot-autoconfigure</artifactId>
                <version>${autoconfigure.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bilibili</groupId>
                <artifactId>warp-paladin</artifactId>
                <version>${autoconfigure.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bilibili</groupId>
                <artifactId>logback-autoconfigure</artifactId>
                <version>1.2.6</version>
            </dependency>-->
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>com.bilibili</groupId>
            <artifactId>warp-bom</artifactId>
            <type>pom</type>
            <version>${warp-spring.version}</version>
            <scope>import</scope>
        </dependency>
    </dependencies>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>2.7.18</version>
                </plugin>
                <plugin>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${maven-compiler-plugin.version}</version>
                    <configuration>
                        <source>${java.source.version}</source>
                        <target>${java.target.version}</target>
                        <encoding>${file.encoding}</encoding>
                        <annotationProcessorPaths>
                            <path>
                                <groupId>no.entur.mapstruct.spi</groupId>
                                <artifactId>protobuf-spi-impl</artifactId>
                                <version>1.43</version>
                            </path>
                            <path>
                                <groupId>org.mapstruct</groupId>
                                <artifactId>mapstruct-processor</artifactId>
                                <version>${mapstruct.version}</version>
                            </path>
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                                <version>${lombok.version}</version>
                            </path>
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok-mapstruct-binding</artifactId>
                                <version>0.2.0</version>
                            </path>
                        </annotationProcessorPaths>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>com.bilibili</groupId>
                    <artifactId>buf-maven-plugin</artifactId>
                    <version>1.0.13</version>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <pluginRepositories>
        <pluginRepository>
            <id>bilibili-plugin-snapshots</id>
            <name>Bilibili Nexus Plugin Snapshot</name>
            <url>https://nexus.bilibili.co/content/repositories/snapshots</url>
            <releases>
                <enabled>false</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>daily</updatePolicy>
                <checksumPolicy>warn</checksumPolicy>
            </snapshots>
        </pluginRepository>
        <pluginRepository>
            <id>bilibili-plugin-releases</id>
            <name>Bilibili Nexus Plugin Release</name>
            <url>https://nexus.bilibili.co/content/repositories/releases</url>
            <releases>
                <enabled>true</enabled>
                <checksumPolicy>warn</checksumPolicy>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>
</project>