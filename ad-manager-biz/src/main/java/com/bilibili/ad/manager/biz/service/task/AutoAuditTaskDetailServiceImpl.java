package com.bilibili.ad.manager.biz.service.task;


import com.alibaba.fastjson.JSON;
import com.bilibili.ad.manager.api.task.IAutoAuditTaskDetailService;
import com.bilibili.ad.manager.api.task.bean.AuditTask;
import com.bilibili.ad.manager.api.task.bean.AuditTaskQueryParam;
import com.bilibili.ad.manager.api.task.bean.AutoAuditTaskDetail;
import com.bilibili.ad.manager.api.task.bean.AutoAuditTaskDetailQueryParam;
import com.bilibili.ad.manager.biz.elasticsearch.repository.AutoAuditTaskRepository;
import com.bilibili.ad.manager.biz.service.task.po.AutoAuditTaskSimiPo;
import com.bilibili.ad.manager.biz.service.task.po.EsAuditTaskPo;
import com.bilibili.ad.manager.biz.util.EsUtils;
import com.bilibili.adp.common.enums.SortTypeEnum;
import com.bilibili.adp.common.util.Utils;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.data.elasticsearch.core.query.SearchQuery;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.bilibili.ad.manager.common.enums.task.AutoTaskTag.OTHER;
import static org.elasticsearch.index.query.QueryBuilders.*;
import static org.elasticsearch.index.query.QueryBuilders.termQuery;

/**
 * description: 
 * <AUTHOR>
 * @date 2025/1/22 19:53
 */
@Slf4j
@Service
public class AutoAuditTaskDetailServiceImpl implements IAutoAuditTaskDetailService {

    @Autowired
    private AutoAuditTaskRepository autoAuditTaskRepository;

    /**
     * 创意审核任务
     */
    @Value("${auto.audit.task.index:auto-audit-taskdetail}")
    private String autoAuditTaskIndex;


    @Override
    public void createTask(AutoAuditTaskDetail taskDetail) {
        if(Objects.isNull(taskDetail) || Objects.isNull(taskDetail.getSimiTaskId())){
            return;
        }
        autoAuditTaskRepository.save(task2Po(taskDetail));
    }

    @Override
    public List<AutoAuditTaskDetail> query(AutoAuditTaskDetailQueryParam param) {
        SearchQuery searchQuery = createSearchQuery(autoAuditTaskIndex, getBoolQueryBuilder(param), PageRequest.of(0, 10000), SortTypeEnum.DESC.getCode());
        List<AutoAuditTaskSimiPo> pos = autoAuditTaskRepository.query(searchQuery);
        return pos2task(pos);
    }



    private SearchQuery createSearchQuery(String indices, BoolQueryBuilder queryBuilder, PageRequest pageRequest, Integer sortFlag) {

        return new NativeSearchQueryBuilder()
                .withIndices(indices)
                .withQuery(Objects.isNull(queryBuilder) ? boolQuery() : queryBuilder)
                .withSort(new FieldSortBuilder("createTime").order(Objects.equals(SortTypeEnum.DESC.getCode(), sortFlag) ? SortOrder.DESC : SortOrder.ASC).unmappedType("long"))
                .withPageable(pageRequest)
                .build();
    }


    private AutoAuditTaskSimiPo task2Po(AutoAuditTaskDetail taskDetail) {
        AutoAuditTaskSimiPo po = new AutoAuditTaskSimiPo();
        BeanUtils.copyProperties(taskDetail, po);
        taskDetail.setAutoAuditTaskTag(Optional.ofNullable(taskDetail.getAutoAuditTaskTag()).orElse(OTHER.getTagCode()));
        return po;
    }

    private AutoAuditTaskDetail po2task(AutoAuditTaskSimiPo po) {
        AutoAuditTaskDetail taskDetail = new AutoAuditTaskDetail();
        BeanUtils.copyProperties(po, taskDetail);
        taskDetail.setAutoAuditTaskTag(Optional.ofNullable(taskDetail.getAutoAuditTaskTag()).orElse(OTHER.getTagCode()));
        return taskDetail;
    }

    private List<AutoAuditTaskDetail> pos2task(List<AutoAuditTaskSimiPo> pos) {
        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyList();
        }
        return pos.stream().map(this::po2task).collect(Collectors.toList());
    }

    private BoolQueryBuilder getBoolQueryBuilder(AutoAuditTaskDetailQueryParam param) {

        BoolQueryBuilder queryBuilder = boolQuery();
        EsUtils.setParam(param::getTaskId, (value) -> queryBuilder.filter(termsQuery("taskId", value)));

        EsUtils.setParam(param::getCreativeIds, (value) -> queryBuilder.filter(termsQuery("creativeId", value)));
        EsUtils.setParam(param::getCreateTimeFrom, (value) -> queryBuilder.filter(rangeQuery("createTime").gte(value)));
        EsUtils.setParam(param::getCreateTimeTo, (value) -> queryBuilder.filter(rangeQuery("createTime").lte(value)));
        EsUtils.setParam(param::getExecuteTimeFrom, (value) -> queryBuilder.filter(rangeQuery("executeTime").gte(value)));
        EsUtils.setParam(param::getExecuteTimeTo, (value) -> queryBuilder.filter(rangeQuery("executeTime").lte(value)));
        EsUtils.setParam(param::getCreativeType, (value) -> queryBuilder.filter(termsQuery("creativeType", value)));

        return queryBuilder;
    }

}

