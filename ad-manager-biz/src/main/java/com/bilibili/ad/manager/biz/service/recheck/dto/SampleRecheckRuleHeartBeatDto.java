package com.bilibili.ad.manager.biz.service.recheck.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/6/15 12:55
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SampleRecheckRuleHeartBeatDto {

    private Integer ruleId;

    private Long latestUpdateTime;

    private String status;

    /**
     * 分三个阶段: 扫描, 分组统计, 打标
     */
    private String state;

    /**
     * 查询总数量
     */
    private Integer queryTotalCount;

    /**
     * 查询已经扫描数据量
     */
    private Integer queryHasScanCount;

    /**
     * 需要打标数据量
     */
    private Integer needMarkTotalCount;

    /**
     * 已经打标数据量
     */
    private Integer hasMarkCount;

    private String costTime;

    private String msg;

    private String indexName;
}
