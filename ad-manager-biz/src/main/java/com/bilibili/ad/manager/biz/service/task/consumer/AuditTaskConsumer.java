package com.bilibili.ad.manager.biz.service.task.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bapis.ad.creative.*;
import com.bapis.ad.mgk.LandingPageServiceGrpc;
import com.bapis.ad.mgk.MgkPageStatus;
import com.bapis.ad.mgk.MgkPagesStatusReply;
import com.bapis.ad.mgk.MgkPagesStatusReq;
import com.bilibili.ad.manager.api.audit.landing_page_group.ILandingPageGroupService;
import com.bilibili.ad.manager.api.audit.landing_page_group.dto.LandingPageGroupBaseDto;
import com.bilibili.ad.manager.api.task.IAuditTaskLogService;
import com.bilibili.ad.manager.api.task.IAuditTaskService;
import com.bilibili.ad.manager.api.task.IQueryAuditTaskService;
import com.bilibili.ad.manager.api.task.bean.*;
import com.bilibili.ad.manager.api.task.generator.IAuditTaskGenService;
import com.bilibili.ad.manager.biz.bo.creative.CreativeAuditAction;
import com.bilibili.ad.manager.biz.bo.creative.CreativeAuditPubInfoBo;
import com.bilibili.ad.manager.biz.config.AdMngBizConfig;
import com.bilibili.ad.manager.biz.convertor.ICreativeConvertor;
import com.bilibili.ad.manager.biz.convertor.ICreativeCpsLinkReplaceConvertor;
import com.bilibili.ad.manager.biz.repo.AccountLabelRepo;
import com.bilibili.ad.manager.biz.service.archive.CmArchiveService;
import com.bilibili.ad.manager.biz.service.audit.creative.CreativeCpsReplaceUrlService;
import com.bilibili.ad.manager.api.task.bean.AccountLabelMappingBo;
import com.bilibili.ad.manager.biz.service.audit.programmatic.AdMngProgCreativeService;
import com.bilibili.ad.manager.biz.convertor.ProgramMaterialConvertor;
import com.bilibili.ad.manager.biz.service.audit.creative.CreativeResourceService;
import com.bilibili.ad.manager.biz.service.audit.creative.LauCreativeDynamicService;
import com.bilibili.ad.manager.api.task.bean.CreativeExtraBo;
import com.bilibili.ad.manager.biz.service.audit.recall.CreativeAuditRecallOptService;
import com.bilibili.ad.manager.biz.service.creative.LauCreativeExtraService;
import com.bilibili.ad.manager.biz.service.databus.CreativeAuditTaskPub;
import com.bilibili.ad.manager.biz.service.databus.HardCreativeAuditPub;
import com.bilibili.ad.manager.biz.service.lancer.LancerReportService;
import com.bilibili.ad.manager.biz.service.lancer.param.AuditTaskLancerEnum;
import com.bilibili.ad.manager.biz.service.metrics.CustomMetrics;
import com.bilibili.ad.manager.biz.service.metrics.MetricsKeyConstant;
import com.bilibili.ad.manager.biz.service.task.AuditTaskExecuteService;
import com.bilibili.ad.manager.biz.service.task.CreativeShadowService;
import com.bilibili.ad.manager.biz.service.task.CreativeTypeJudgeService;
import com.bilibili.ad.manager.biz.service.task.config.AuditTaskConfig;
import com.bilibili.ad.manager.biz.service.task.consumer.dto.CreativeAuditTaskConsumeContext;
import com.bilibili.ad.manager.biz.service.task.consumer.dto.LauCreativeFlyDynamicInfoBo;
import com.bilibili.ad.manager.api.task.bean.MaterialInfoPushToAuditMsgBo;
import com.bilibili.ad.manager.biz.service.task.consumer.dto.RiskArchiveDto;
import com.bilibili.ad.manager.biz.service.task.filter.AuditTaskFilter;
import com.bilibili.ad.manager.biz.service.task.generator.AuditTaskGenerateFactory;
import com.bilibili.ad.manager.api.task.bean.CreativeAuditMessage;
import com.bilibili.ad.manager.biz.util.TraceUtil;
import com.bilibili.ad.manager.common.enums.task.AuditTaskLogType;
import com.bilibili.ad.manager.common.enums.task.AuditTaskState;
import com.bilibili.ad.manager.common.enums.task.AuditTaskType;
import com.bilibili.ad.manager.common.utils.MgkLandingPageParserUtils;
import com.bilibili.adp.common.enums.AuditStatus;
import com.bilibili.adp.common.enums.JumpTypeEnum;
import com.bilibili.adp.common.util.SnowflakeIdWorker;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.common.util.biz.bos.MgkLandingPageBo;
import com.bilibili.adp.launch.api.common.CreativeStatus;
import com.bilibili.adp.launch.api.creative.dto.CpcCreativeDto;
import com.bilibili.adp.launch.api.creative.dto.CreativeComponentBo;
import com.bilibili.adp.launch.api.creative.dto.ImageDto;
import com.bilibili.adp.launch.api.creative.dto.QueryCpcCreativeDto;
import com.bilibili.adp.launch.api.service.ICpcCreativeService;
import com.bilibili.crm.platform.common.IsValid;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import io.opentelemetry.api.common.AttributeKey;
import io.opentelemetry.api.common.Attributes;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import pleiades.component.databus.pub.DatabusPub;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 该消费者是老的api，同时是sh001，升级有风险，故用作中转
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Deprecated
public class AuditTaskConsumer extends AbstractAuditTask<CreativeAuditMessage> {

    @Value("${audit_task_consumer.redis.manual.handle:audit_task_manual_handle}")
    private String auditTaskManualHandle;

    @Value("${audit_task_consumer.redis.machine.handle:audit_task_machine_handle}")
    private String auditTaskMachineHandle;

    @Autowired
    private AuditTaskFilter auditTaskFilter;
    @Autowired
    private AuditTaskGenerateFactory auditTaskGenerateFactory;
    @Autowired
    private AuditTaskConfig auditTaskConfig;
    @Autowired
    private IQueryAuditTaskService queryAuditTaskService;
    @Autowired
    private IAuditTaskService taskService;
    @Autowired
    private AuditTaskExecuteService auditTaskExecuteService;
    @Autowired
    private SnowflakeIdWorker snowflakeIdWorker;
    @Autowired
    private ICpcCreativeService cpcCreativeService;
    @Autowired
    private LauCreativeExtraService lauCreativeExtraService;
    @Autowired
    private IAuditTaskLogService auditTaskLogService;
    @Autowired
    private ILandingPageGroupService landingPageGroupService;
    @Autowired
    private CreativeResourceService creativeResourceService;

    @Autowired
    private CreativeServiceGrpc.CreativeServiceBlockingStub creativeServiceBlockingStub;
    @Autowired
    private LandingPageServiceGrpc.LandingPageServiceBlockingStub landingPageServiceBlockingStub;
    @Resource
    private AdMngProgCreativeService adMngProgCreativeService;
    @Resource
    private LauCreativeDynamicService lauCreativeDynamicService;
    @Resource
    private CustomMetrics customMetrics;
    @Autowired
    private CreativeTypeJudgeService creativeTypeJudgeService;
    @Autowired
    private CreativeAuditTaskPub creativeAuditTaskPub;
    @Resource
    private TraceUtil traceUtil;

    private String spanName = this.getClass().getName();

    @Resource(name = "creativeAuditPublisher")
    private DatabusPub creativeAuditPublisher;
    @Resource(name = "creativeAuditTaskExecutor")
    private ThreadPoolTaskExecutor creativeAuditTaskExecutor;

    private final DatabusPub creativeAuditInfoPub;
    @Autowired
    private CreativeShadowService creativeShadowService;
    @Autowired
    private CreativeCpsReplaceUrlService creativeCpsReplaceUrlService;
    @Autowired
    private CmArchiveService cmArchiveService;
    @Autowired
    private CreativeAuditRecallOptService creativeAuditRecallOptService;
    @Autowired
    private LancerReportService lancerReportService;
    @Autowired
    private AccountLabelRepo accountLabelRepo;
    @Autowired
    private AdMngBizConfig adMngBizConfig;
    @Autowired
    private CreativeMaterialPushToAuditService creativeMaterialPushToAuditService;
    @Autowired
    private HardCreativeAuditPub hardCreativeAuditPub;

    public void consume(JSONObject data) {
        log.info("audit task consumer msg 收到创意推审消息：{}", JSON.toJSONString(data));
//        Transaction t = Cat.getProducer().newTransaction("DATABUS", "AuditTaskConsumer");
        CreativeAuditMessage message = data.toJavaObject(CreativeAuditMessage.class);

        if (Objects.isNull(message) || CollectionUtils.isEmpty(message.getCreativeId())) {
            return;
        }

        Transaction t = Cat.getProducer().newTransaction("DATABUS", "consume:硬广");
        try {
            super.process(message);

            t.setStatus(Transaction.SUCCESS);
        } catch (Exception e) {
            t.setStatus(e);
            throw e;
        } finally {
            t.complete();
        }
    }

    /**
     * 1.特殊逻辑处理：
     * 创意删除
     * 建站工具落地页下线，创意自动驳回
     * 稿件不可用，创意自动驳回
     * 2.过滤/check
     * 3.路由
     * 4.处理
     * 5.生成任务
     */
    public void deal(CreativeAuditMessage message, boolean skipAccountFilter, List<MaterialInfoPushToAuditMsgBo> creativeMaterialInfoList) {
        long start = System.currentTimeMillis();
        log.info("AuditTaskConsumer start to deal creativeIds={}, creativeMaterialInfoList={}", JSON.toJSONString(message), creativeMaterialInfoList);
        List<Integer> creativeIds = message.getCreativeId() != null ? message.getCreativeId() : Lists.newArrayList();
        lancerReportService.report(AuditTaskLancerEnum.CREATIVE_AUDIT_CONSUMER_RECEIVE_MSG, creativeIds.size());
        if (CollectionUtils.isEmpty(creativeIds)) {
            lancerReportService.report(AuditTaskLancerEnum.CREATIVE_AUDIT_CONSUMER_RECEIVE_MSG_MISS_PARAM, creativeIds.size());
            return;
        }

        // 闪屏不用处理
        if (CollectionUtils.isNotEmpty(creativeMaterialInfoList)) {
            if (Objects.equals(creativeMaterialInfoList.get(0), 2)) {
                log.info("AuditTaskConsumer filter, 闪屏, creativeIds={}", creativeIds);
                return;
            }
        } else if(null == creativeMaterialInfoList) {
            creativeMaterialInfoList = new ArrayList<>();
        }

        CreativeAuditTaskConsumeContext context = CreativeAuditTaskConsumeContext.init(message);
        context.setSkipAccountFilter(skipAccountFilter);
        Map<Integer, MaterialInfoPushToAuditMsgBo> materialInfoMsgMap = creativeMaterialInfoList.stream().collect(Collectors.toMap(t -> t.getCreative_id(), t -> t, (t1, t2) -> t2));
        context.setMaterialInfoMsgMap(materialInfoMsgMap);

        // 三连新建创意成功后，推送消息进行审核，此时三连审核状态为审核中
        QueryCpcCreativeDto queryCpcCreativeDto = QueryCpcCreativeDto.builder()
                .creativeIds(creativeIds)
                .withUnit(true)
                .withPageGroup(true)
                .withExtraCreativeTab(true)
                .withImages(true)
                .withCampaign(true)
                .build();
        List<CpcCreativeDto> creatives = cpcCreativeService.queryCpcCreativeDto(queryCpcCreativeDto);
        List<Integer> accountIds = creatives.stream().map(CpcCreativeDto::getAccountId).distinct().collect(Collectors.toList());

        if (CollectionUtils.isEmpty(creatives)) {
            lancerReportService.report(AuditTaskLancerEnum.CREATIVE_AUDIT_CONSUMER_RECEIVE_MSG_FILTER_NO_CREATIVE, creativeIds.size());
            log.info("AuditTaskConsumer filter creative is empty");
            return;
        }

        List<AccountLabelMappingBo> accountLabelMappingBos = accountLabelRepo.queryAccountLabelMappingList(accountIds, Arrays.asList(adMngBizConfig.getSplitMaterialCustomizeAuditLabelId(), adMngBizConfig.getSplitMaterialCustomizeAuditLabelId()));
        Map<Integer, List<Integer>> accountLabelIds = accountLabelMappingBos.stream().collect(Collectors.groupingBy(AccountLabelMappingBo::getAccountId, Collectors.mapping(t -> t.getLabelId(), Collectors.toList())));
        context.setAccountLabelMappingMap(accountLabelIds);

        // 查询 creative extra
        Map<Integer, CreativeExtraBo> creativeExtraBoMap = lauCreativeExtraService.queryCreativeExtraMap(creativeIds);
        Map<Integer, LauCreativeFlyDynamicInfoBo> creativeFlyDynamicInfoPoMap = lauCreativeDynamicService.fetchMapByCreativeIds(creativeIds);
        context.setCreativeExtraBoMap(creativeExtraBoMap);
        context.setCreativeFlyDynamicInfoPoMap(creativeFlyDynamicInfoPoMap);

        // 程序化元素详情列表
        List<Integer> progCreativeIds = creatives.stream().filter(t -> Utils.isPositive(t.getIsProgrammatic())).map(t -> t.getCreativeId()).distinct().collect(Collectors.toList());
        List<Long> videoIds = creatives.stream().map(CpcCreativeDto::getVideoId).filter(Utils::isPositive).distinct().collect(Collectors.toList());
        Map<Integer, List<ProgCreativeMaterialAuditInfoBo>> progMaterialAuditInfoBoMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(progCreativeIds)) {
            progMaterialAuditInfoBoMap = adMngProgCreativeService.getMaterialAuditInfoBoMap(progCreativeIds);
        }
        context.setProgMaterialAuditInfoBoMap(progMaterialAuditInfoBoMap);

        // 获取影子创意
        ShadowCreativesReply shadowCreativesReply = creativeShadowService.shadowCreatives(creativeIds);
        Map<Integer, ShadowCreative> shadowCreativesMap = shadowCreativesReply
                .getShadowCreativesMap();
        context.setShadowCreativesMap(shadowCreativesMap);

        Map<Integer, CreativeCpsReplaceLinkBo> replaceLinkBoMap = creativeCpsReplaceUrlService.queryCreativeCpsReplaceUrlMap(creativeIds);
        context.setCreativeCpsReplaceLinkBoMap(replaceLinkBoMap);

        Map<Long, CmArchiveBo> cmArchiveBoMap = cmArchiveService.queryCmArchiveBoMap(videoIds);
        context.setCmArchiveBoMap(cmArchiveBoMap);

        Map<Long, RiskArchiveDto> riskArchiveDtoMap = creativeMaterialPushToAuditService.queryRiskArchiveMap(videoIds);
        context.setRiskArchiveDtoMap(riskArchiveDtoMap);

        // 从创意和影子创意提取 mgkPageId
        List<Long> pageIdsFromCreative = creatives
                .stream()
                .map(CpcCreativeDto::getMgkPageId)
                .filter(Utils::isPositive)
                .collect(Collectors.toList());
        List<Long> pageIdsFromShadowCreative = shadowCreativesMap
                .values()
                .stream()
                .map(ShadowCreative::getMgkPageId)
                .filter(Utils::isPositive)
                .collect(Collectors.toList());
        List<Long> mgkPagesId = Stream.of(pageIdsFromCreative, pageIdsFromShadowCreative)
                .flatMap(List::stream)
                .distinct()
                .filter(Utils::isPositive)
                .collect(Collectors.toList());

        // 获取创意的落地页状态
        //不能因为拿不到
        MgkPagesStatusReply mgkPagesStatusReply = landingPageServiceBlockingStub.mgkPageStatus(MgkPagesStatusReq.newBuilder().addAllMgkPageId(mgkPagesId).build());
        Map<Long, MgkPageStatus> pagesStatusMap = mgkPagesStatusReply.getPagesStatusMap();
        context.setPagesStatusMap(pagesStatusMap);

        // 获取创意和影子创意的落地页组 ids
        List<Long> pageGroupIdsFromCreative = creatives.stream()
                .map(CpcCreativeDto::getPageGroupId)
                .filter(Utils::isPositive)
                .collect(Collectors.toList());
        List<Long> pageGroupIdsFromShadowCreative = shadowCreativesMap.values().stream()
                .map(ShadowCreative::getPageGroupId)
                .filter(Utils::isPositive)
                .collect(Collectors.toList());
        List<Long> pageGroupIds = Stream.of(pageGroupIdsFromCreative, pageGroupIdsFromShadowCreative)
                .flatMap(List::stream)
                .distinct()
                .filter(Utils::isPositive)
                .collect(Collectors.toList());
        // 查询落地页组
        List<LandingPageGroupBaseDto> pageGroupBaseDtoList = landingPageGroupService.queryPageGroupBaseDtoList(pageGroupIds);
        Map<Long, LandingPageGroupBaseDto> pageGroupMap = pageGroupBaseDtoList.stream()
                .collect(Collectors.toMap(LandingPageGroupBaseDto::getGroupId, Function.identity()));
        context.setPageGroupMap(pageGroupMap);

        // aigc 信息
        Map<Integer, List<CreativeTitleBo>> creativeTitleMap = creativeResourceService.listCreativeTitleMap(creativeIds);
        Map<Integer, List<CreativeImageBo>> creativeImageMap = creativeResourceService.listCreativeImageMap(creativeIds);
        context.setCreativeTitleMap(creativeTitleMap);
        context.setCreativeImageMap(creativeImageMap);

        Map<Integer, Boolean> materialCreativeMap = creativeMaterialPushToAuditService.queryIsMaterialCreativeMap(creativeIds);
        context.setIsMaterialCreativeMap(materialCreativeMap);

        // 并发优化
        List<CompletableFuture<Void>> futures = new ArrayList<>();

        creatives.forEach(creative -> {
            CompletableFuture<Void> voidCompletableFuture = CompletableFuture.runAsync(() -> {

                boolean isPageGroupAuditTask = false;
                if (Utils.isPositive(creative.getPageGroupId())) {
                    LandingPageGroupBaseDto landingPageGroupBaseDto = pageGroupMap.get(creative.getPageGroupId());
                    if (Objects.nonNull(landingPageGroupBaseDto)) {
                        isPageGroupAuditTask = landingPageGroupBaseDto.getAuditCreativeId().equals(creative.getCreativeId());
                    }
                }
                try {
                    log.info("AuditTaskConsumer start to deal creativeId={}", creative.getCreativeId());
                    handleSingleCreative(context, creative, isPageGroupAuditTask);
                    log.info("AuditTaskConsumer finish deal creativeId={}", creative.getCreativeId());
                } catch (Exception e) {
                    log.error("AuditTaskConsumer catch exception, creativeId={}", creative.getCreativeId(), e);
                    lancerReportService.report(AuditTaskLancerEnum.CREATIVE_AUDIT_CONSUMER_RECEIVE_MSG_OPT_FAIL, creativeIds.size());
                    throw e;
                }
            }, creativeAuditTaskExecutor);
            futures.add(voidCompletableFuture);
        });

        // 阻塞，等待所有任务执行完成
        CompletableFuture<Void> all = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
        all.join();
        log.info("AuditTaskConsumer all task finish, creativeIds={}", creativeIds);

        all.whenComplete((result, exception) -> {
            if (exception != null) {
                log.error("AuditTaskConsumer deal error", exception);
                throw new RuntimeException(exception);
            }
        });

        long end = System.currentTimeMillis();
        lancerReportService.report(AuditTaskLancerEnum.CREATIVE_AUDIT_CONSUMER_RECEIVE_MSG_GEN_COST, (int) (end - start));
    }

    private void handleSingleCreative(CreativeAuditTaskConsumeContext context, CpcCreativeDto creative,
                                      boolean isPageGroupAuditTask) {

        // adType=广告类型：0-所有广告 1-搜索广告 2-闪屏
        if (creative.getCampaign() != null && Objects.equals(creative.getCampaign().getAdType(), 2)) {
            log.info("handleSingleCreative, adType is splash,creativeId={}", creative.getCreativeId());
            return;
        }

        Map<Integer, ShadowCreative> shadowCreativesMap = context.getShadowCreativesMap();
        Map<Long, MgkPageStatus> pagesStatusMap = context.getPagesStatusMap();

        List<CreativeTitleBo> creativeTitleBos = context.getCreativeTitleMap().getOrDefault(creative.getCreativeId(), Collections.emptyList());
        List<CreativeImageBo> creativeImageBos = context.getCreativeImageMap().getOrDefault(creative.getCreativeId(), Collections.emptyList());
        List<ProgCreativeMaterialAuditInfoBo> progCreativeMaterialAuditInfoBos = context.getProgMaterialAuditInfoBoMap().getOrDefault(creative.getCreativeId(), Collections.emptyList());
        CreativeExtraBo creativeExtraBo = context.getCreativeExtraBoMap().get(creative.getCreativeId());
        LauCreativeFlyDynamicInfoBo creativeFlyDynamicInfoBo = context.getCreativeFlyDynamicInfoPoMap().getOrDefault(creative.getCreativeId(), new LauCreativeFlyDynamicInfoBo());
        CreativeCpsReplaceLinkBo originCpsReplaceLinkBo = context.getCreativeCpsReplaceLinkBoMap().getOrDefault(creative.getCreativeId(), null);
        CreativeCpsReplaceLinkBo creativeCpsReplaceLinkBoAfterProcess = ICreativeConvertor.MAPPER.copy(originCpsReplaceLinkBo);
        CmArchiveBo cmArchiveBo = context.getCmArchiveBoMap().getOrDefault(creative.getVideoId(), null);

        // 影子创意存在，用影子字段覆盖
        ShadowCreative shadowCreative = shadowCreativesMap.get(creative.getCreativeId());
        ShadowCreative curShadowCreative = shadowCreativesMap.get(creative.getCreativeId());
        Attributes att1 = Attributes.of(AttributeKey.stringKey(MetricsKeyConstant.count_type), MetricsKeyConstant.count_type_ad_creative_consume,
                AttributeKey.stringKey(MetricsKeyConstant.a_operator), "接收到消息-创意维度",
                AttributeKey.stringKey("existShadow"), curShadowCreative != null ? "1":"0"
        );
        customMetrics.count(1, att1);

        if (shadowCreative != null) {
            lancerReportService.report(AuditTaskLancerEnum.CREATIVE_AUDIT_CONSUMER_RECEIVE_MSG_EXIST_SHADOW, 1);

            final int jumpType = shadowCreative.getJumpTypeValue();
            final String jumpUrl = shadowCreative.getJumpUrl();
            creative.setJumpType(jumpType);
            // (todo simer:)bug: 三连视频模板组没有该 videoId，先不管 ==
            creative.setVideoId(shadowCreative.getVideoId());

            // 创意推广内容
            if (Objects.equals(jumpType, JumpTypeEnum.VIDEO_MOBILE.getCode())) {
                creative.setPromotionPurposeContent(jumpUrl);
            } else if (Objects.equals(jumpType, JumpTypeEnum.MGK_PAGE_ID.getCode())) {
                final MgkLandingPageBo bo = MgkLandingPageParserUtils.parse(jumpUrl);
                creative.setPromotionPurposeContent(bo.getPageId().toString());
                creative.setMgkLandingPage(bo);
            } else {
                creative.setPromotionPurposeContent(JumpTypeEnum.getByCode(jumpType).parseLaunchUrl(jumpUrl));
            }

            creative.setTitle(shadowCreative.getTitle());
            creative.setDescription(shadowCreative.getDescription());
            creative.setExtDescription(shadowCreative.getExtDescription());
            creative.setMgkPageId(shadowCreative.getMgkPageId());
            creative.setCmMark(shadowCreative.getCmMark());
            creative.setBusMarkId(shadowCreative.getBusMark());
            creative.setButtonCopy(shadowCreative.getButton().getButtonContent());
            creative.setButtonCopyUrl(shadowCreative.getButton().getCustomizedUrl());
            creative.setIsYellowCar(shadowCreative.getFlyExtInfo().getIsYellowCar());
            creative.setYellowCarTitle(shadowCreative.getFlyExtInfo().getYellowCarTitle());
            creative.setPageGroupId(shadowCreative.getPageGroupId());
            creative.setIsPageGroup(shadowCreative.getIsPageGroup());
            List<ImageDto> images = shadowCreative.getImagesList()
                    .stream()
                    .map(image -> ImageDto.builder()
                            .url(image.getImageUrl())
                            .md5(image.getImageMd5())
                            .build())
                    .collect(Collectors.toList());
            String creativeImageUrl = shadowCreative.getImagesList()
                    .stream()
                    .map(CreativeImage::getImageUrl)
                    .findFirst()
                    .orElse(StringUtils.EMPTY);
            String creativeImageMd5 = shadowCreative.getImagesList()
                    .stream()
                    .map(CreativeImage::getImageMd5)
                    .findFirst()
                    .orElse(StringUtils.EMPTY);
            // 兼容新三连 视频模板组
            if (!CollectionUtils.isEmpty(images)) {
                creative.setImageUrl(creativeImageUrl);
                creative.setImageMd5(creativeImageMd5);
                creative.setImageDtos(images);
            }
            List<CreativeComponentBo> components = shadowCreative.getComponentsList()
                    .stream()
                    .map(component -> CreativeComponentBo.builder()
                            .id(component.getId())
                            .type(component.getType())
                            .build())
                    .collect(Collectors.toList());
            creative.setComponents(components);
            creative.setAuditStatus(shadowCreative.getAuditStatusValue());
            creative.setCreativeStatus(shadowCreative.getCreativeStatusValue());
            creative.setProgAuditStatus(shadowCreative.getProgAuditStatus());
            creative.setProgMiscElemAuditStatus(shadowCreative.getProgMiscElemAuditStatus());
            // tabId tabName已经不用了 不需要处理
            if (Utils.isPositive(shadowCreative.getAdvertisingModeValue())) {
                CreativeTab tab = shadowCreative.getTab();
                creative.setTabJumpUrl(tab.getJumpUrl());
            }

            // creative details
            if (!CollectionUtils.isEmpty(shadowCreative.getCreativeDetailsList())) {
                progCreativeMaterialAuditInfoBos = ProgramMaterialConvertor.convertDetailBos2MaterialInfoBos(shadowCreative.getCreativeDetailsList());
            }
            if (shadowCreative.getCpsReplaceLinkInfo() != null) {
                creativeCpsReplaceLinkBoAfterProcess = ICreativeCpsLinkReplaceConvertor.MAPPER.grpc2Bo(shadowCreative.getCpsReplaceLinkInfo());
            }

            // todo simer: creative dynamic == 以后再说
//            Optional.of(shadowCreative.getCreativeDynamic()).ifPresent(creativeDynamic -> {
//                creativeFlyDynamicInfoBo.setDynamicId(creativeDynamic.getDynamicId());
//            });
            // creative archive
        }

        CreativeAuditOtherInfoBo creativeAuditOtherInfoBo = context.getOtherInfoMap() != null ? context.getOtherInfoMap().get(creative.getCreativeId()) : null;
        Integer isRecallCreative = creativeAuditOtherInfoBo != null && creativeAuditOtherInfoBo.isRecall() ? 1 : 0;
        Integer pvLevel = creativeAuditOtherInfoBo != null && creativeAuditOtherInfoBo.getPvLevel() != null ? creativeAuditOtherInfoBo.getPvLevel() : null;

        MaterialInfoPushToAuditMsgBo materialInfoPushToAuditMsgBo = context.getMaterialInfoMsgMap().get(creative.getCreativeId());
        CanSplitMaterialPushToAuditDto canSplitMaterialPushToAuditDto = CanSplitMaterialPushToAuditDto.builder()
                .creativeId(creative.getCreativeId())
                .unitId(creative.getUnitId())
                .isDpa(materialInfoPushToAuditMsgBo != null ? materialInfoPushToAuditMsgBo.getIs_dpa() : 0)
                .isProgrammatic(creative.getIsProgrammatic())
                .labelIds(context.getAccountLabelMappingMap().get(creative.getAccountId()))
                .adpVersion(context.getAdpVersion())
                .parentCreativeId(creativeExtraBo != null ? creativeExtraBo.getParentCreativeId() : 0)
                .bilibiliUserId(creative.getBilibiliUserId())
                .creativeType(creativeTypeJudgeService.judgeProgramCreativeType(creative.getUnit().getBusinessDomain(), creative.getCreativeId()))
                .isMaterialCreative(context.getIsMaterialCreativeMap().getOrDefault(creative.getCreativeId(), false))
                .materialInfoPushToAuditMsgBo(context.getMaterialInfoMsgMap().get(creative.getCreativeId()))
                .creativeCtime(creative.getCtime())
                .isRecall(isRecallCreative)
                .build();
        context.getCanSplitMaterialPushToAuditDtoMap().put(creative.getCreativeId(), canSplitMaterialPushToAuditDto);

        //特殊逻辑处理
        if (Objects.equals(CreativeStatus.DELETED.getCode(), creative.getCreativeStatus())) {
            log.info("handleSingleCreative, creative delete, to stop task,creativeId={}", creative.getCreativeId());

            // 拆素材兜底处理，保证曾经是拆素材的，继续拆素材
            splitMaterialPushTo(context, creative, canSplitMaterialPushToAuditDto);

            taskService.stopTaskWithCreativeId(creative.getCreativeId(), isRecallCreative);

            Attributes att10 = Attributes.of(AttributeKey.stringKey(MetricsKeyConstant.count_type), MetricsKeyConstant.count_type_ad_creative_consume,
                    AttributeKey.stringKey(MetricsKeyConstant.a_operator), "直接stop",
                    AttributeKey.stringKey("type"), "创意删除"
            );
            customMetrics.count(1, att10);
            lancerReportService.report(AuditTaskLancerEnum.CREATIVE_AUDIT_CONSUMER_RECEIVE_MSG_SPECIAL_OPT, 1);
            return;
        }

        // 存在 mgkPageId & 审核驳回 & 审核理由为落地页配置的拒绝理由
        if (Utils.isPositive(creative.getMgkPageId()) && Objects.equals(AuditStatus.REJECT.getCode(), creative.getAuditStatus()) && Objects.equals(auditTaskConfig.getMgkPageIdRejectReason(), creative.getReason())) {
            log.info("handleSingleCreative, 存在 mgkPageId & 审核驳回 & 审核理由为落地页配置的拒绝理由 auto reject,creativeId={}", creative.getCreativeId());

            // 拆素材兜底处理，保证曾经是拆素材的，继续拆素材
            splitMaterialPushTo(context, creative, canSplitMaterialPushToAuditDto);

            autoAuditRejectDeal(creative, isPageGroupAuditTask);

            Attributes att10 = Attributes.of(AttributeKey.stringKey(MetricsKeyConstant.count_type), MetricsKeyConstant.count_type_ad_creative_consume,
                    AttributeKey.stringKey(MetricsKeyConstant.a_operator), "直接stop",
                    AttributeKey.stringKey("type"), "落地页拒审"
            );
            customMetrics.count(1, att10);
            lancerReportService.report(AuditTaskLancerEnum.CREATIVE_AUDIT_CONSUMER_RECEIVE_MSG_SPECIAL_OPT, 1);
            return;
        }

        // 审核驳回 & 审核理由为稿件配置的拒绝理由
        if (Objects.equals(AuditStatus.REJECT.getCode(), creative.getAuditStatus()) && Objects.equals(auditTaskConfig.getArchiveRejectReason(), creative.getReason())) {
            log.info("handleSingleCreative, 审核驳回 & 审核理由为稿件配置的拒绝理由 auto reject,creativeId={}", creative.getCreativeId());

            // 拆素材兜底处理，保证曾经是拆素材的，继续拆素材
            splitMaterialPushTo(context, creative, canSplitMaterialPushToAuditDto);

            // 自动处理
            creativeAuditTaskPub.pub(CreativeAuditTaskMsg.builder().creativeIds(Arrays.asList(creative.getCreativeId())).reason("稿件限制投放").build());

            Attributes att10 = Attributes.of(AttributeKey.stringKey(MetricsKeyConstant.count_type), MetricsKeyConstant.count_type_ad_creative_consume,
                    AttributeKey.stringKey(MetricsKeyConstant.a_operator), "直接stop",
                    AttributeKey.stringKey("type"), "稿件驳回固定理由"
            );
            customMetrics.count(1, att10);
            lancerReportService.report(AuditTaskLancerEnum.CREATIVE_AUDIT_CONSUMER_RECEIVE_MSG_SPECIAL_OPT, 1);
            return;
        }

        // 落地页组 & 创意驳回 & 驳回理由=落地页组已下线
        if (Utils.isPositive(creative.getPageGroupId())
                && Objects.equals(AuditStatus.REJECT.getCode(), creative.getAuditStatus())
                && Objects.equals(auditTaskConfig.getPageGroupIdRejectReason(), creative.getReason())) {
            log.info("handleSingleCreative, 落地页组 & 创意驳回 & 驳回理由=落地页组已下线 auto reject,creativeId={}", creative.getCreativeId());

            // 拆素材兜底处理，保证曾经是拆素材的，继续拆素材
            splitMaterialPushTo(context, creative, canSplitMaterialPushToAuditDto);

            autoAuditRejectDeal(creative, isPageGroupAuditTask);

            Attributes att10 = Attributes.of(AttributeKey.stringKey(MetricsKeyConstant.count_type), MetricsKeyConstant.count_type_ad_creative_consume,
                    AttributeKey.stringKey(MetricsKeyConstant.a_operator), "直接stop",
                    AttributeKey.stringKey("type"), "落地页组拒审"
            );
            customMetrics.count(1, att10);
            lancerReportService.report(AuditTaskLancerEnum.CREATIVE_AUDIT_CONSUMER_RECEIVE_MSG_SPECIAL_OPT, 1);
            return;
        }

        //如果选了建站落地页 并且落地页状态是待审核， 停止所有有效任务
        if (Utils.isPositive(creative.getMgkPageId())
                && Objects.equals(MgkPageStatus.WAIT_AUDIT, pagesStatusMap.get(creative.getMgkPageId()))) {
            log.info("AuditCreativeStopByMgkPageWaitAudit,creativeId={},pageId={},pageStatus={}", creative.getCreativeId(), creative.getMgkPageId(), pagesStatusMap.get(creative.getMgkPageId()).getNumber());

            splitMaterialPushTo(context, creative, canSplitMaterialPushToAuditDto);

            taskService.stopTaskWithCreativeId(creative.getCreativeId(), isRecallCreative);

            Attributes att10 = Attributes.of(AttributeKey.stringKey(MetricsKeyConstant.count_type), MetricsKeyConstant.count_type_ad_creative_consume,
                    AttributeKey.stringKey(MetricsKeyConstant.a_operator), "直接stop",
                    AttributeKey.stringKey("type"), "落地页待审"
            );
            customMetrics.count(1, att10);
            lancerReportService.report(AuditTaskLancerEnum.CREATIVE_AUDIT_CONSUMER_RECEIVE_MSG_SPECIAL_OPT, 1);
            return;
        }

        // 稿件回查标签存在，自动驳回
        if (context.getRiskArchiveDtoMap() != null) {
            RiskArchiveDto riskArchiveDto = context.getRiskArchiveDtoMap().get(creative.getVideoId());
            if (riskArchiveDto != null && Objects.equals(riskArchiveDto.getAuditStatus(), AuditStatus.REJECT.getCode())) {
                log.info("handleSingleCreative, 稿件回查标签存在，自动驳回, creativeId={}, videoId={}", creative.getCreativeId(), creative.getVideoId());

                // 拆素材兜底处理，保证曾经是拆素材的，继续拆素材
                splitMaterialPushTo(context, creative, canSplitMaterialPushToAuditDto);
                autoAuditRejectDeal(creative, isPageGroupAuditTask);
                return;
            }
        }

//        // 方便测试的开关，直接推审到risk
//        if (Utils.isPositive(adMngBizConfig.getDirectMaterialAuditSwitch())) {
//            if (creativeMaterialPushToAuditService.isCanSplitMaterialPushToAudit(context.getCanSplitMaterialPushToAuditDtoMap().get(creative.getCreativeId()))) {
//                creativeMaterialPushToAuditService.splitMaterialPushToAudit(materialInfoPushToAuditMsgBo);
//                context.getCanSplitMaterialPushToAuditDtoMap().get(creative.getCreativeId()).setIsSplitMaterialPushToThisTime(true);
//                return;
//            }
//        }

        //过滤
        if (!auditTaskFilter.filter(creative, isPageGroupAuditTask, creativeExtraBo, progCreativeMaterialAuditInfoBos, context)) {
            log.info("handleSingleCreative, is filtered,creativeId={}", creative.getCreativeId());

            Attributes att10 = Attributes.of(AttributeKey.stringKey(MetricsKeyConstant.count_type), MetricsKeyConstant.count_type_ad_creative_consume,
                    AttributeKey.stringKey(MetricsKeyConstant.a_operator), "filter"
            );
            customMetrics.count(1, att10);
            lancerReportService.report(AuditTaskLancerEnum.CREATIVE_AUDIT_CONSUMER_RECEIVE_MSG_FILTER, 1);

            // 拆素材兜底处理，保证曾经是拆素材的，继续拆素材
            splitMaterialPushTo(context, creative, canSplitMaterialPushToAuditDto);
            return;
        }

        List<Integer> labelIds = context.getAccountLabelMappingMap().get(creative.getAccountId());

        //判断生成任务类型+获取任务生成的service
        IAuditTaskGenService genService = auditTaskGenerateFactory.getHandler(creative, isPageGroupAuditTask, creativeExtraBo, isRecallCreative);

        //生成任务
        CreativeTaskParamBo creativeTaskParamBo = CreativeTaskParamBo.builder()
                .creative(creative)
                .otherInfo(creativeAuditOtherInfoBo)
                .creativeExtraBo(creativeExtraBo)
                .creativeTitleBos(creativeTitleBos)
                .creativeImageBos(creativeImageBos)
                .dynamicId(creativeFlyDynamicInfoBo.getDynamicId())
                .progCreativeMaterialAuditInfoBos(progCreativeMaterialAuditInfoBos)
                .shadowCreative(curShadowCreative)
                .originCreativeCpsReplaceLinkBo(originCpsReplaceLinkBo)
                .creativeCpsReplaceLinkBoAfterProcess(creativeCpsReplaceLinkBoAfterProcess)
                .isRecallCreative(isRecallCreative)
                .pvLevel(pvLevel)
                .cmArchiveBo(cmArchiveBo)
                .labelIds(labelIds)
                .materialInfoPushToAuditMsgBo(materialInfoPushToAuditMsgBo)
                .canSplitMaterialPushToAuditDto(canSplitMaterialPushToAuditDto)
                .build();
        genService.generateTask(creativeTaskParamBo);
        lancerReportService.report(AuditTaskLancerEnum.CREATIVE_AUDIT_CONSUMER_RECEIVE_MSG_OPT_SUCCESS, 1);

        // 拆素材兜底处理，保证曾经是拆素材的，继续拆素材
        splitMaterialPushTo(context, creative, canSplitMaterialPushToAuditDto);

        //发送消息
        CreativeAuditPubInfoBo creativeAuditInfo = CreativeAuditPubInfoBo.fromCpcCreativeDto(creative, CreativeAuditAction.AUDITING, new Date().getTime());
        log.info("start to pub creative auditing info :{}", creativeAuditInfo);
        creativeAuditInfoPub.pub(String.valueOf(creativeAuditInfo.getCreativeId()), JSONObject.toJSONString(creativeAuditInfo));

        Attributes attr0 = Attributes.of(AttributeKey.stringKey(MetricsKeyConstant.count_type), MetricsKeyConstant.count_type_ad_creative_consume,
                AttributeKey.stringKey(MetricsKeyConstant.a_operator), "生成任务",
                AttributeKey.stringKey("isProgram"), creative.getIsProgrammatic() + ""
        );
        customMetrics.count(1, attr0);
    }

    private void splitMaterialPushTo(CreativeAuditTaskConsumeContext context, CpcCreativeDto creative, CanSplitMaterialPushToAuditDto canSplitMaterialPushToAuditDto) {
        // 拆素材兜底处理，保证曾经是拆素材的，继续拆素材
        log.info("splitMaterialPushTo, creativeId={},getIsMaterialCreative={},getIsSplitMaterialPushToThisTime={}", creative.getCreativeId(), canSplitMaterialPushToAuditDto.getIsMaterialCreative(), canSplitMaterialPushToAuditDto.getIsSplitMaterialPushToThisTime());
        if (Utils.is(canSplitMaterialPushToAuditDto.getIsMaterialCreative()) && !Utils.is(canSplitMaterialPushToAuditDto.getIsSplitMaterialPushToThisTime())) {
            MaterialInfoPushToAuditMsgBo creativeInfo = context.getMaterialInfoMsgMap().get(creative.getCreativeId());
            if (creativeInfo != null) {
                creativeMaterialPushToAuditService.splitMaterialPushToAudit(creativeInfo);
            } else {
                log.error("splitMaterialPushTo, creativeInfo is null, creativeId={},getIsMaterialCreative={},getIsSplitMaterialPushToThisTime={}", creative.getCreativeId(), canSplitMaterialPushToAuditDto.getIsMaterialCreative(), canSplitMaterialPushToAuditDto.getIsSplitMaterialPushToThisTime());
            }
        }
    }

    /**
     * case：建站工具落地页下线，创意自动驳回
     * case：稿件不可用，创意自动驳回
     * 1.当前存在游离/执行中的一审任务，任务状态置成完成+执行人为SYSTEM，产生一条质检驳回任务
     * 2.当前存在游离/执行中的质检通过任务，任务状态置成完成+执行人为SYSTEM，操作记质检驳回
     * 3.当前无任务，补一条质检通过任务，完成状态+执行人为SYSTEM
     *
     * @param creative 创意信息
     */
    private void autoAuditRejectDeal(CpcCreativeDto creative, boolean isPageGroupAuditTask) {
        log.info("autoAuditRejectDeal, creativeId={}", creative.getCreativeId());
        //查找任务
        List<AuditTask> tasks = queryAuditTaskService.query(AuditTaskQueryParam.builder()
                .creativeIds(Collections.singletonList(creative.getCreativeId()))
                .state(Lists.newArrayList(AuditTaskState.FREE.getCode(), AuditTaskState.EXECUTING.getCode()))
                .build());

        String logTaskId = null;
        Integer auditTaskLogType = null;
        if (CollectionUtils.isEmpty(tasks)) {
            log.info("autoAuditRejectDeal, no tasks, creativeId={}", creative.getCreativeId());
            //补任务
            tasks = queryAuditTaskService.query(AuditTaskQueryParam.builder()
                    .creativeIds(Collections.singletonList(creative.getCreativeId()))
                    // 完成的
                    .state(Lists.newArrayList(AuditTaskState.FINISH.getCode()))
                    // 机审一审，人工一审任务，人工质检通过任务，人工质检驳回任务
                    .type(Lists.newArrayList(AuditTaskType.AUTO_FIRST_AUDIT.getCode(), AuditTaskType.MANUAL_FIRST_AUDIT.getCode(), AuditTaskType.MANUAL_FIRST_AUDIT.getCode(), AuditTaskType.RECHECK_POSITIVE.getCode(), AuditTaskType.RECHECK_NEGATIVE.getCode()))
                    .build());
            if (CollectionUtils.isEmpty(tasks)) {
                log.info("autoAuditRejectDeal, no tasks2, creativeId={}", creative.getCreativeId());
                return;
            }
            AuditTask task = tasks.get(0);

            // 重新生成 id
            task.setId(String.valueOf(snowflakeIdWorker.nextId()));
            task.setPId(null);
            // 人工质检驳回任务
            task.setType(AuditTaskType.RECHECK_NEGATIVE.getCode());
            // 完成
            task.setState(AuditTaskState.FINISH.getCode());
            task.setCreateTime(Utils.getNow().getTime());
            task.setAcceptorName("");
            task.setAcceptTime(null);
            task.setExecutorName(auditTaskConfig.getSystem());
            task.setExecuteTime(Utils.getNow().getTime());
            task.setRejectRemark(creative.getReason());
            task.setMtime(creative.getMtime());
            // 驳回
            task.setAuditStatus(AuditStatus.REJECT.getCode());
            task.setCreativeStatus(CreativeStatus.AUDIT_REJECT.getCode());
            task.setRejectRemark(creative.getReason());
            taskService.saveTask(task);
            logTaskId = task.getId();
            auditTaskLogType = AuditTaskLogType.RECHECK_REJECT.getCode();
        } else if (Objects.equals(AuditTaskType.MANUAL_FIRST_AUDIT.getCode(), tasks.get(0).getType())) {
            log.info("autoAuditRejectDeal 人审任务, creativeId={}", creative.getCreativeId());
            //更新任务
            taskService.updateTask(Lists.newArrayList(auditTaskExecuteService.buildUpdTask(tasks.get(0), auditTaskConfig.getSystem(), AuditStatus.REJECT.getCode(), creative.getReason())));
            auditTaskLogType = AuditTaskLogType.FIRST_AUDIT_REJECT.getCode();
            //生成任务
            AuditTask task = tasks.get(0);
            logTaskId = task.getId();
            task.setPId(task.getId());
            task.setFirstTaskType(task.getType());
            task.setFirstAuditExecutorName(task.getExecutorName());
            task.setId(String.valueOf(snowflakeIdWorker.nextId()));
            task.setType(AuditTaskType.RECHECK_NEGATIVE.getCode());
            task.setState(AuditTaskState.FREE.getCode());
            task.setCreateTime(Utils.getNow().getTime());
            task.setAcceptorName("");
            task.setAcceptTime(null);
            task.setExecutorName("");
            task.setExecuteTime(null);
            task.setMtime(creative.getMtime());
            taskService.saveTask(task);
        } else if (Objects.equals(AuditTaskType.RECHECK_POSITIVE.getCode(), tasks.get(0).getType())) {
            log.info("autoAuditRejectDeal 质检任务, creativeId={}", creative.getCreativeId());
            logTaskId = tasks.get(0).getId();
            //更新任务
            taskService.updateTask(Lists.newArrayList(auditTaskExecuteService.buildUpdTask(tasks.get(0), auditTaskConfig.getSystem(), AuditStatus.REJECT.getCode(), creative.getReason())));
            auditTaskLogType = AuditTaskLogType.RECHECK_REJECT.getCode();
        }

        if (!Strings.isNullOrEmpty(logTaskId)) {
            auditTaskLogService.add(AuditTaskLogDto.builder()
                    .taskId(logTaskId)
                    .taskState(AuditTaskState.FINISH.getCode())
                    .logType(auditTaskLogType)
                    .rejectRemark(creative.getReason())
                    .build());
        }

        // 如果是落地页组修改原因送审的 自动拒审以后拒审落地页组
        if (isPageGroupAuditTask && !Objects.equals(auditTaskConfig.getPageGroupIdRejectReason(), creative.getReason())) {
            log.info("系统原因拒审落地页组及创意, 创意id:{}, 落地页组id:{}", creative.getCreativeId(), creative.getPageGroupId());
            landingPageGroupService.rejectTotalPageGroup(creative.getPageGroupId(), "系统原因拒审,请重新推审");
        }
    }

    @Override
    protected String getNoticeBizPre(CreativeAuditMessage creativeAuditMessage) {
        return "硬广";
    }

    @Override
    protected void rePublish(CreativeAuditMessage message) {
        creativeAuditPublisher.pub(JSON.toJSONString(message));
    }

    @Override
    protected List<Long> getKeys(CreativeAuditMessage message) {
        return message.getCreativeId().stream().map(t -> t.longValue()).collect(Collectors.toList());
    }

    @Override
    protected String getAuditTaskManualRedisKey() {
        return auditTaskManualHandle;
    }

    @Override
    protected void doDeal(CreativeAuditMessage data) {
//        deal(data, false, null);

        // 发送异步消息，mng 自己消费，因为老的databus是sh001,用的老的api 直接升级改成004有风险
        hardCreativeAuditPub.pub(data);
    }

    @Override
    protected String getAuditTaskMachineRedisKey() {
        return auditTaskMachineHandle;
    }
}
