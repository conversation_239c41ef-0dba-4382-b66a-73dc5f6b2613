package com.bilibili.ad.manager.biz.service.task;

import com.bapis.ad.creative.CreativeServiceGrpc;
import com.bapis.ad.creative.ShadowCreative;
import com.bapis.ad.creative.ShadowCreativesReply;
import com.bapis.ad.creative.ShadowCreativesRequest;
import com.bapis.ad.pandora.service.PandoraAuditServiceGrpc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class CreativeShadowService {

    @Autowired
    private PandoraAuditServiceGrpc.PandoraAuditServiceBlockingStub pandoraAuditServiceBlockingStub;

    public ShadowCreative queryShadowCreative(Integer creativeId) {

        if (creativeId == null) {
            return null;
        }

        return this.queryShadowCreativeMap(Arrays.asList(creativeId)).get(creativeId);
    }

    public Map<Integer, ShadowCreative> queryShadowCreativeMap(List<Integer> creativeIds) {

        if (CollectionUtils.isEmpty(creativeIds)) {
            return Collections.emptyMap();
        }

        ShadowCreativesReply shadowCreativesReply = pandoraAuditServiceBlockingStub.shadowCreatives(ShadowCreativesRequest.newBuilder()
                .addAllCreativeIds(creativeIds)
                .build());
        Map<Integer, ShadowCreative> shadowCreativesMap = shadowCreativesReply.getShadowCreativesMap();
        return shadowCreativesMap;
    }

    public ShadowCreativesReply shadowCreatives(List<Integer> creativeIds) {

        if (CollectionUtils.isEmpty(creativeIds)) {
            return ShadowCreativesReply.getDefaultInstance();
        }

        ShadowCreativesReply shadowCreativesReply = pandoraAuditServiceBlockingStub.shadowCreatives(ShadowCreativesRequest.newBuilder()
                .addAllCreativeIds(creativeIds)
                .build());
        return shadowCreativesReply;
    }
}
