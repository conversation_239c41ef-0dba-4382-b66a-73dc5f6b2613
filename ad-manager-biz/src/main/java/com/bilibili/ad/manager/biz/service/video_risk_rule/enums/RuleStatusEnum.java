package com.bilibili.ad.manager.biz.service.video_risk_rule.enums;

import java.util.Arrays;
import java.util.List;

public enum RuleStatusEnum {

    ONLINE(1, "上线"),
    OFFLINE(2, "离线");

    private Integer code;
    private String name;

    private RuleStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

    public static List<Integer> getAllType(){
        return Arrays.asList(RuleStatusEnum.ONLINE.code,
                RuleStatusEnum.OFFLINE.code);
    }
}
