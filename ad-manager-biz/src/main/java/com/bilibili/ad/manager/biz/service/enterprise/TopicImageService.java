package com.bilibili.ad.manager.biz.service.enterprise;

import com.bilibili.ad.manager.api.complain.ITopicImageService;
import com.bilibili.ad.manager.api.complain.dto.*;
import com.bilibili.ad.manager.biz.mas.dao.BascTopicCoverDao;
import com.bilibili.ad.manager.biz.mas.dao.BascTopicDao;
import com.bilibili.ad.manager.biz.mas.dao.BascTopicOrderDao;
import com.bilibili.ad.manager.biz.mas.dao.EnterpriseAuthInfoDao;
import com.bilibili.ad.manager.biz.mas.po.*;
import com.bilibili.ad.manager.common.enums.ImageAuditStateEnum;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2020.09.29 13:50
 */
@Slf4j
@Service
public class TopicImageService implements ITopicImageService {

    @Autowired
    private EnterpriseAuthInfoDao authInfoDao;

    @Autowired
    private BascTopicCoverDao topicCoverDao;

    @Autowired
    private BascTopicOrderDao topicOrderDao;

    @Autowired
    private BascTopicDao bascTopicDao;


    @Override
    public PageResult<TopicImageAuditInfo> list(QueryTopicCoverDto dto) {
        if(!StringUtils.isEmpty(dto.getMid())){
            return this.getListByMid(dto, null);
        }
        if(!StringUtils.isEmpty(dto.getName())){
            EnterpriseAuthInfoPoExample authInfoPoExample = new EnterpriseAuthInfoPoExample();
            authInfoPoExample.or().andNameLike("%" + dto.getName() + "%");
            List<EnterpriseAuthInfoPo> pos = authInfoDao.selectByExample(authInfoPoExample);
            if(!CollectionUtils.isEmpty(pos)){
                return this.getListByMid(dto,
                        pos.stream().map(EnterpriseAuthInfoPo::getMid).collect(Collectors.toList()));
            }
            return PageResult.emptyPageResult();
        }

        BascTopicCoverPoExample coverPoExample = this.buildCoverParam(dto, null);
        long count = topicCoverDao.countByExample(coverPoExample);
        if(count == 0){
            return PageResult.emptyPageResult();
        }
        Page pageBean = Page.valueOf(dto.getPage(), dto.getPageSize());
        coverPoExample.setLimit(pageBean.getLimit());
        coverPoExample.setOffset(pageBean.getOffset());
        coverPoExample.setOrderByClause("mtime desc");
        List<BascTopicCoverPo> coverPos = topicCoverDao.selectByExample(coverPoExample);
        List<Long> ids = coverPos.stream().map(BascTopicCoverPo::getTopicId).collect(Collectors.toList());

        List<TopicImageAuditInfo> auditInfos = coverPos.stream()
                .map(t->{
                    TopicImageAuditInfo info = TopicImageAuditInfo.builder()
                            .imageAuditStatus(t.getAuditStatus())
                            .topicId(t.getTopicId())
                            .id(t.getId())
                            .coverUrl(t.getCoverUrl())
                            .coverMd5(t.getCoverMd5())
                            .createTime(t.getMtime())
                            .passTime(ImageAuditStateEnum.PASS.getCode().equals(t.getAuditStatus()) ?
                                    t.getPassTime() : null).rejectReason(t.getRejectReason())
                            .rejectType(t.getRejectType())
                            .build();
                    //新话题直接设置mid
                    if (t.getTopicType() == 1) {
                        info.setMid(t.getMid());
                        info.setTopicName(t.getTopicName());
                    }
                    return info;
                })
                .collect(Collectors.toList());

        //查询位次信息
        BascTopicOrderPoExample orderPoExample = new BascTopicOrderPoExample();
        orderPoExample.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andTopicIdIn(ids);
        List<BascTopicOrderPo> topicOrderPos = topicOrderDao.selectByExample(orderPoExample);
        if(!CollectionUtils.isEmpty(topicOrderPos)){
            Map<Long, List<BascTopicOrderPo>> map = topicOrderPos.stream()
                    .collect(Collectors.groupingBy(BascTopicOrderPo::getTopicId));
            auditInfos.forEach(t->{
                        if(map.containsKey(t.getTopicId())){
                            BascTopicOrderPo orderPo = map.get(t.getTopicId()).get(0);
                            t.setOrder(orderPo.getTopicOrder());
                        }
                    }
            );
        }

        //查询旧话题信息
        List<Long> oldTopicIds = coverPos.stream()
                .filter(cover -> cover.getTopicType() == 0)
                .map(BascTopicCoverPo::getTopicId).collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(oldTopicIds)) {
            BascTopicPoExample poExample = new BascTopicPoExample();
            poExample.or().andForeignIdIn(oldTopicIds).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
            List<BascTopicPo> bascTopicPos = bascTopicDao.selectByExample(poExample);
            if(!CollectionUtils.isEmpty(bascTopicPos)){
                Map<Long, List<BascTopicPo>> TopicMap = bascTopicPos.stream()
                        .collect(Collectors.groupingBy(BascTopicPo::getForeignId));
                auditInfos.forEach(t->{
                            if(TopicMap.containsKey(t.getTopicId())){
                                List<BascTopicPo> oldTopicList = TopicMap.get(t.getTopicId());
                                Optional<BascTopicPo> optionalTopicPo =
                                        oldTopicList.stream().filter(po->po.getRelatedUid()!=0).findAny();
                                optionalTopicPo.ifPresent(po->{
                                    t.setTopicName(po.getTitle());
                                    t.setMid(po.getRelatedUid());
                                });
                            }
                        }
                );
            }
        }

        //查询账户信息
        List<Long> mids = auditInfos.stream().map(TopicImageAuditInfo::getMid).collect(Collectors.toList());
        EnterpriseAuthInfoPoExample authInfoPoExample = new EnterpriseAuthInfoPoExample();
        authInfoPoExample.or().andMidIn(mids);
        List<EnterpriseAuthInfoPo> authPos = authInfoDao.selectByExample(authInfoPoExample);
        if (!CollectionUtils.isEmpty(authPos)) {
            Map<Long, String> midToName = new HashMap<>();
            Map<Long, List<EnterpriseAuthInfoPo>> midMaps = authPos.stream()
                    .collect(Collectors.groupingBy(EnterpriseAuthInfoPo::getMid));

            midMaps.forEach((k,v) -> midToName.put(k, v.get(0).getName()));

            auditInfos.forEach(t -> t.setUseName(midToName.getOrDefault(t.getMid(), "")));
        }

        return PageResult.<TopicImageAuditInfo>builder().records(auditInfos).total((int) count).build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, transactionManager = "meetTransactionManager")
    public void auditCover(TopicImageAuditDto auditDto) throws ServiceException {
        BascTopicCoverPoExample coverPoExample = new BascTopicCoverPoExample();
        if (auditDto.getTopic_type() == null || auditDto.getTopic_type() == 0) {
            coverPoExample.or()
                    .andIdEqualTo(auditDto.getId());
        }else{
            coverPoExample.or()
                    .andTopicIdEqualTo(auditDto.getId())
                    .andTopicTypeEqualTo(1);
        }
        List<BascTopicCoverPo> coverPos = topicCoverDao.selectByExample(coverPoExample);
        if(CollectionUtils.isEmpty(coverPos)){
            throw new ServiceException("id" + auditDto.getId() + "对应的数据不存在");
        }

        BascTopicCoverPo coverPo = coverPos.get(0);
        coverPo.setAuditStatus(auditDto.getImageAuditStatus());
        coverPo.setRejectType(auditDto.getRejectType());
        coverPo.setRejectReason(auditDto.getRejectReason() == null ? "" : auditDto.getRejectReason());
        if(ImageAuditStateEnum.PASS.getCode().equals(auditDto.getImageAuditStatus())){
            coverPo.setPassTime(new Timestamp(System.currentTimeMillis()));
        }
        topicCoverDao.insertUpdate(coverPo);
    }


    private PageResult<TopicImageAuditInfo> getListByMid(QueryTopicCoverDto dto, List<Long> mids){
        Map<Long, String> midToName = new HashMap<>();
        Map<Long, BascTopicPo> topicIdToName = new HashMap<>();
        if(CollectionUtils.isEmpty(mids)) {
            long mid = dto.getMid();
            //查询账户信息
            EnterpriseAuthInfoPoExample authPoExample = new EnterpriseAuthInfoPoExample();
            authPoExample.or().andMidEqualTo(mid);
            List<EnterpriseAuthInfoPo> authPos = authInfoDao.selectByExample(authPoExample);
            if (!CollectionUtils.isEmpty(authPos)) {
                midToName.put(mid, authPos.get(0).getName());
            }

            //查询话题信息
            BascTopicPoExample poExample = new BascTopicPoExample();
            poExample.or().andRelatedUidEqualTo(mid).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
            List<BascTopicPo> bascTopicPos = bascTopicDao.selectByExample(poExample);
            if(!CollectionUtils.isEmpty(bascTopicPos)){
                for (BascTopicPo bascTopicPo : bascTopicPos){
                    topicIdToName.put(bascTopicPo.getForeignId(), bascTopicPo);
                }
            }
        }else {
            //查询账户信息
            EnterpriseAuthInfoPoExample authPoExample = new EnterpriseAuthInfoPoExample();
            authPoExample.or().andMidIn(mids);
            List<EnterpriseAuthInfoPo> authPos = authInfoDao.selectByExample(authPoExample);
            if (!CollectionUtils.isEmpty(authPos)) {
                Map<Long, List<EnterpriseAuthInfoPo>> midMaps = authPos.stream()
                        .collect(Collectors.groupingBy(EnterpriseAuthInfoPo::getMid));

                midMaps.forEach((k,v) -> midToName.put(k, v.get(0).getName()));
            }

            //查询话题信息
            BascTopicPoExample poExample = new BascTopicPoExample();
            poExample.or().andRelatedUidIn(mids).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
            List<BascTopicPo> bascTopicPos = bascTopicDao.selectByExample(poExample);
            if(!CollectionUtils.isEmpty(bascTopicPos)){
                for (BascTopicPo bascTopicPo : bascTopicPos){
                    topicIdToName.put(bascTopicPo.getForeignId(), bascTopicPo);
                }
            }
        }

        //查询封面信息
        BascTopicCoverPoExample coverPoExample = this.buildCoverParam(dto, mids);
        long count = topicCoverDao.countByExample(coverPoExample);
        if(count == 0){
            return PageResult.emptyPageResult();
        }
        Page pageBean = Page.valueOf(dto.getPage(), dto.getPageSize());
        coverPoExample.setLimit(pageBean.getLimit());
        coverPoExample.setOffset(pageBean.getOffset());
        List<BascTopicCoverPo> coverPos = topicCoverDao.selectByExample(coverPoExample);
        List<TopicImageAuditInfo> auditInfos = coverPos.stream()
                .map(t -> {
                    TopicImageAuditInfo info = TopicImageAuditInfo.builder()
                            .imageAuditStatus(t.getAuditStatus())
                            .topicId(t.getTopicId())
                            .id(t.getId())
                            .coverUrl(t.getCoverUrl())
                            .coverMd5(t.getCoverMd5())
                            .createTime(t.getMtime())
                            .passTime(ImageAuditStateEnum.PASS.getCode().equals(t.getAuditStatus()) ?
                                    t.getPassTime() : null)
                            .rejectReason(t.getRejectReason())
                            .rejectType(t.getRejectType())
                            .build();
                    if (t.getTopicType() == 1) {
                        info.setMid(t.getMid());
                        info.setTopicName(t.getTopicName());
                    }
                    return info;
                })
                .collect(Collectors.toList());


        List<Long> topicIds = auditInfos.stream().map(TopicImageAuditInfo::getTopicId).collect(Collectors.toList());
        //查询位次信息
        BascTopicOrderPoExample orderPoExample = new BascTopicOrderPoExample();
        orderPoExample.or()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andTopicIdIn(topicIds);
        List<BascTopicOrderPo> topicOrderPos = topicOrderDao.selectByExample(orderPoExample);
        if(!CollectionUtils.isEmpty(topicOrderPos)){
            Map<Long, List<BascTopicOrderPo>> map = topicOrderPos.stream()
                    .collect(Collectors.groupingBy(BascTopicOrderPo::getTopicId));
            auditInfos.forEach(t->{
                        if(map.containsKey(t.getTopicId())){
                            BascTopicOrderPo orderPo = map.get(t.getTopicId()).get(0);
                            t.setOrder(orderPo.getTopicOrder());
                            t.setMid(t.getMid());
                        }
                    }
            );
        }

        if(!CollectionUtils.isEmpty(topicIdToName)){
            auditInfos.forEach(t->{
                if(topicIdToName.containsKey(t.getTopicId())){
                    BascTopicPo bascTopicPo = topicIdToName.get(t.getTopicId());
                    t.setMid(bascTopicPo.getRelatedUid());
                    t.setTopicName(bascTopicPo.getTitle());
                }
            });
        }

        if(!CollectionUtils.isEmpty(midToName)){
            auditInfos.forEach(t -> t.setUseName(midToName.getOrDefault(t.getMid(), "")));
        }

        return PageResult.<TopicImageAuditInfo>builder().records(auditInfos).total((int) count).build();
    }


    private BascTopicCoverPoExample buildCoverParam(QueryTopicCoverDto dto, List<Long> mids) {

        BascTopicCoverPoExample example = new BascTopicCoverPoExample();
        BascTopicCoverPoExample.Criteria criteria = example.or();
        if (dto.getBeginTime() != null) {
            criteria.andMtimeGreaterThanOrEqualTo(dto.getBeginTime());
        }
        if (dto.getEndTime() != null) {
            criteria.andMtimeLessThanOrEqualTo(dto.getEndTime());
        }
        if (dto.getAuditStatus() != null) {
            criteria.andAuditStatusEqualTo(dto.getAuditStatus());
        }
        if (dto.getMid() != null) {
            criteria.andMidEqualTo(dto.getMid());
        }
        if (!CollectionUtils.isEmpty(mids)) {
            criteria.andMidIn(mids);
        }
        example.setOrderByClause("mtime desc");
        return example;
    }


}
