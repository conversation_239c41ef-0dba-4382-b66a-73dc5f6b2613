package com.bilibili.ad.manager.biz.ad.dao;

import com.bilibili.ad.manager.biz.po.EffectSearchBlackwordConfigPo;
import com.bilibili.ad.manager.biz.po.EffectSearchBlackwordConfigPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface EffectSearchBlackwordConfigDao {
    long countByExample(EffectSearchBlackwordConfigPoExample example);

    int deleteByExample(EffectSearchBlackwordConfigPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(EffectSearchBlackwordConfigPo record);

    int insertBatch(List<EffectSearchBlackwordConfigPo> records);

    int insertUpdateBatch(List<EffectSearchBlackwordConfigPo> records);

    int insert(EffectSearchBlackwordConfigPo record);

    int insertUpdateSelective(EffectSearchBlackwordConfigPo record);

    int insertSelective(EffectSearchBlackwordConfigPo record);

    List<EffectSearchBlackwordConfigPo> selectByExample(EffectSearchBlackwordConfigPoExample example);

    EffectSearchBlackwordConfigPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") EffectSearchBlackwordConfigPo record, @Param("example") EffectSearchBlackwordConfigPoExample example);

    int updateByExample(@Param("record") EffectSearchBlackwordConfigPo record, @Param("example") EffectSearchBlackwordConfigPoExample example);

    int updateByPrimaryKeySelective(EffectSearchBlackwordConfigPo record);

    int updateByPrimaryKey(EffectSearchBlackwordConfigPo record);
}