package com.bilibili.ad.manager.biz.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RtaStrategyGroupPo implements Serializable {
    /**
     * 自增主键
     */
    private Long id;

    /**
     * group名称
     */
    private String groupName;

    /**
     * 配置项，json格式
     */
    private String content;

    /**
     * 软删除
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    private static final long serialVersionUID = 1L;
}