package com.bilibili.ad.manager.biz.service.doctree.vo;

import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/12/9
 */
@Data
@Accessors(chain = true)
public class SanlianNodeBatchGetReq implements SnakeCaseBody {


    private List<Long> nodeIds;

    private String accountId;

    private Boolean isShow;

    private Boolean isDeleted;

}
