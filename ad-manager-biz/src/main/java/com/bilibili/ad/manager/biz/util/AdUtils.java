package com.bilibili.ad.manager.biz.util;

import com.bilibili.ad.manager.biz.service.audit.bean.LauUnitCreativeBean;
import com.bilibili.adp.common.enums.AuditStatus;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/3/14 16:50
 */
public class AdUtils {

    public static String generateVideoUrl(Long avid) {
        return "https://www.bilibili.com/video/av" + avid;
    }

    public static boolean judgeIsLaunching(LauUnitCreativeBean creativeBean) {

        if (!Objects.equals(AuditStatus.ACCEPT.getCode(), creativeBean.getAuditStatus()) || Objects.equals(com.bapis.ad.creative.CreativeStatus.CREATIVE_VALID, creativeBean.getCreativeStatus())) {
            return false;
        }
        return true;
    }
}
