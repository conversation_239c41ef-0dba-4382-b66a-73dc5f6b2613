package com.bilibili.ad.manager.biz.service.canal.service;

import com.bilibili.ad.manager.api.cannal.IBinlogHandlerService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collection;

@Component
public class CanalConsumeFactory {

    @Resource
    private Collection<IBinlogHandlerService> dataHandlers;

    public IBinlogHandlerService getHandler(String table) {

        return dataHandlers.stream()
                .filter(handler -> handler.table().equals(table))
                .findFirst().orElse(null);
    }
}
