package com.bilibili.ad.manager.biz.mas.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BascHomepageCarouselPo implements Serializable {
    /**
     * 自增id
     */
    private Long id;

    /**
     * 图片URL
     */
    private String imageUrl;

    /**
     * 图片的MD5值
     */
    private String imageMd5;

    /**
     * 跳转链接
     */
    private String jumpUrl;

    /**
     * 软删除 0 否 1是
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    private static final long serialVersionUID = 1L;
}