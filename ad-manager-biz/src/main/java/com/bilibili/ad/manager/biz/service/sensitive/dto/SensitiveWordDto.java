package com.bilibili.ad.manager.biz.service.sensitive.dto;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/11
 * @description 敏感词dto
 */
@Data
@Builder
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class SensitiveWordDto {


    @Data
    public static class CreateWordDto {
        private String words;
        private Integer wordType;
    }

    @Data
    public static class UpdateWordDto {
        private Long id;
        private String word;
        private Integer wordType;
        private Integer isDeleted;
    }

    @Data
    public static class BatchUpdateWordDto {
        private List<Long> ids;
        private Integer isDeleted;
    }

    @Data
    @Builder
    public static class SelectWordDto{
        private Long id;
        private String word;
        private Integer wordType;
        private Integer isDeleted;
        private Long count;
    }

    @Data
    public static class WordHistoryDto{
        private Long wordId;
        private String operatorType;
        private Long operatorTime;
        private String operator;
        private String operatorDesc;
    }
    @Data
    public static class SensitiveWordCount{
        private String word;
        private Long count;
    }

    @Data
    public static class UnitWord{
        private Integer unitId;
        private String word;
    }

    @Data
    @Builder
    public static class CreativeSensitiveDto{
        private Integer creativeId;
        private List<String> sensitiveWords;
    }
}
