package com.bilibili.ad.manager.biz.service.audit.bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName AlgorithmSimilarResponse
 * <AUTHOR>
 * @Date 2023/10/9 5:05 下午
 * @Version 1.0
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AlgorithmSimilarResp {
    private AlgorithmSimilarInfo result;
    private Integer status;
    private String msg;
}
