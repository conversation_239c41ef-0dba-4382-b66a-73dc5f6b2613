package com.bilibili.ad.manager.biz.service.task.consumer.dto;

import com.bilibili.ad.manager.api.task.bean.CreativeCpsReplaceLinkBo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 该类的设计目的 com.bilibili.adp.launch.api.creative.dto.CpcCreativeDto 在 jar 包中不想修改了
 */
@Deprecated
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CreativeOtherInfoDto {

    private Integer creativeId;

    // 影子信息
    private CreativeCpsReplaceLinkBo cpsReplaceLinkBo;
}
