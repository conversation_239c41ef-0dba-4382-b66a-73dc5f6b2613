package com.bilibili.ad.manager.biz.service.task.native_ad;

import com.alibaba.fastjson.JSON;
import com.bapis.ad.creative.CreativeServiceGrpc;
import com.bapis.ad.creative.ShadowCreative;
import com.bapis.ad.creative.ShadowCreativesReply;
import com.bapis.ad.creative.ShadowCreativesRequest;
import com.bapis.ad.mgk.LandingPageServiceGrpc;
import com.bapis.ad.mgk.MgkPageStatus;
import com.bapis.ad.mgk.MgkPagesStatusReply;
import com.bapis.ad.mgk.MgkPagesStatusReq;
import com.bilibili.ad.manager.api.audit.landing_page_group.ILandingPageGroupService;
import com.bilibili.ad.manager.api.task.INativeAuditTaskLogService;
import com.bilibili.ad.manager.api.task.bean.*;
import com.bilibili.ad.manager.api.task.native_ad.INativeCreativeRelativityAuditTaskService;
import com.bilibili.ad.manager.api.task.native_ad.INativeCreativeRelativityQueryAuditTaskService;
import com.bilibili.ad.manager.biz.convertor.NativeArchiveAuditTaskConvertor;
import com.bilibili.ad.manager.biz.service.audit.bean.LauUnitCreativeBean;
import com.bilibili.ad.manager.biz.service.audit.creative.AdMngCreativeService;
import com.bilibili.ad.manager.biz.service.task.CreativeShadowService;
import com.bilibili.ad.manager.biz.service.task.config.AuditTaskConfig;
import com.bilibili.ad.manager.biz.util.AdUtils;
import com.bilibili.ad.manager.biz.util.LockManager;
import com.bilibili.ad.manager.common.enums.NativeBizTypeEnum;
import com.bilibili.ad.manager.common.enums.NativeBodyTypeEnum;
import com.bilibili.ad.manager.common.enums.creative.CreativeType;
import com.bilibili.ad.manager.common.enums.task.*;
import com.bilibili.adp.common.DbTableInterface;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.enums.AuditStatus;
import com.bilibili.adp.common.util.SnowflakeIdWorker;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.launch.api.common.CreativeStatus;
import com.bilibili.adp.launch.api.creative.dto.CpcCreativeBaseDto;
import com.bilibili.adp.launch.api.creative.dto.CpcCreativeComplexMarkOperationDto;
import com.bilibili.adp.launch.api.service.ILauCreativeService;
import com.bilibili.adp.log.bean.DiffItem;
import com.bilibili.adp.log.service.LogOperateService;
import com.bilibili.crm.platform.common.IsValid;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.bilibili.ad.manager.common.enums.task.AuditTaskOperateType.getByCode;

/**
 * <AUTHOR>
 * @date 2024/3/12 21:30
 */
@Component
@Slf4j
public class NativeCreativeRelativityAuditTaskExecuteService {

    @Value("${native.creative.relativity.jiangji.switch:1}")
    private Integer nativeCreativeRelativityJiangjiSwitch;

    @Autowired
    private INativeCreativeRelativityQueryAuditTaskService creativeRelativityQueryService;
    @Autowired
    private AuditTaskConfig auditTaskConfig;
    @Autowired
    private ILauCreativeService lauCreativeService;
    @Autowired
    private NativeCreativeRelativityRpcProc nativeCreativeRelativityRpcProc;
    @Autowired
    private INativeCreativeRelativityAuditTaskService auditTaskService;
    @Autowired
    private LockManager lockManager;
    @Autowired
    private INativeAuditTaskLogService auditTaskLogService;
    @Autowired
    private SnowflakeIdWorker snowflakeIdWorker;
    @Autowired
    private LogOperateService logOperateService;
    @Autowired
    private CreativeServiceGrpc.CreativeServiceBlockingStub creativeServiceBlockingStub;
    @Autowired
    private LandingPageServiceGrpc.LandingPageServiceBlockingStub landingPageServiceBlockingStub;
    @Autowired
    private ILandingPageGroupService landingPageGroupService;
    @Autowired
    private NativeArchiveRpcProc nativeArchiveRpcProc;
    @Autowired
    private AdMngCreativeService adMngCreativeService;
    @Autowired
    private CreativeShadowService creativeShadowService;

    /**
     * 根据任务id执行(没有任务不改底表)
     * @param dto
     * @return
     */
    public String executeByTaskIds(CreativeAuditTaskExecuteDto dto) {
        log.info("relativity executeByTaskIds, dto={}", JSON.toJSONString(dto));
        if (StringUtils.isNotEmpty(dto.getRejectReason())) {
            Assert.isTrue(dto.getRejectReason().length() <= auditTaskConfig.getCreativeRejectReasonMaxLength(), "驳回理由最长不能超过" + auditTaskConfig.getCreativeRejectReasonMaxLength() + "个字");
        }
        // 获取任务
        List<NativeCreativeRelativityAuditTaskSaveDto> auditTasks = creativeRelativityQueryService.query(NativeCreativeRelativityAuditTaskQueryParam.builder().id(dto.getTaskId()).build());

        if (CollectionUtils.isEmpty(auditTasks)) {
            return auditTaskConfig.getNoTask();
        }
        List<Integer> creativeIdList = auditTasks.stream().map(NativeCreativeRelativityAuditTaskSaveDto::getCreativeId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(creativeIdList)) {
            return auditTaskConfig.getNoCreative();
        }

        // 查询创意列表
        List<NativeCreativeRelativityDto> nativeCreativeRelativityDtos = nativeCreativeRelativityRpcProc.queryNativeCreativeRelativityListByCreativeIds(creativeIdList);
        Map<Integer, NativeCreativeRelativityDto> creativeRelativityDtoMap = nativeCreativeRelativityDtos.stream().collect(Collectors.toMap(NativeCreativeRelativityDto::getCreativeId, Function.identity()));
        Map<Integer, LauUnitCreativeBean> creativeBeanMap = adMngCreativeService.queryCreativeBaseInfoMap(creativeIdList);

        // 影子创意
        ShadowCreativesReply shadowCreativesReply = creativeShadowService.shadowCreatives(creativeIdList);
        Map<Integer, ShadowCreative> shadowCreativesMap = shadowCreativesReply.getShadowCreativesMap();
        creativeRelativityDtoMap.forEach((creativeId, creative) -> {
            ShadowCreative shadowCreative = shadowCreativesMap.get(creativeId);
            Optional.ofNullable(shadowCreative).ifPresent(sc -> {
                //目前仅有状态影响后续链路，仅状态复制
                creative.setAuditStatus(sc.getAuditStatusValue());
                creative.setCreativeStatus(sc.getCreativeStatusValue());
                creative.setMgkPageId(sc.getMgkPageId());
            });
        });

        List<Long> taskMgkPageIds = creativeRelativityDtoMap.values().stream()
                .map(NativeCreativeRelativityDto::getMgkPageId)
                .filter(Utils::isPositive)
                .collect(Collectors.toList());
        List<Long> waitAuditMgkPageIds = new ArrayList<>();
        if (!org.apache.commons.collections.CollectionUtils.isEmpty(taskMgkPageIds)) {
            MgkPagesStatusReq request = MgkPagesStatusReq.newBuilder()
                    .addAllMgkPageId(taskMgkPageIds)
                    .build();
            MgkPagesStatusReply mgkPagesStatusReply = landingPageServiceBlockingStub.mgkPageStatus(request);
            Map<Long, MgkPageStatus> pageStatusMap = mgkPagesStatusReply.getPagesStatusMap();
            waitAuditMgkPageIds = pageStatusMap.keySet().stream()
                    .filter(pageId -> !pageStatusMap.containsKey(pageId)
                            || pageStatusMap.get(pageId).equals(MgkPageStatus.WAIT_AUDIT))
                    .collect(Collectors.toList());
        }

        List<Long> finalWaitAuditMgkPageIds = waitAuditMgkPageIds;

        // 创意的落地页组
        List<Integer> pageGroupAuditCreativeIdList = landingPageGroupService.queryPageGroupAuditCreativeIdList(creativeIdList);

        List<Long> videoIdList = auditTasks.stream().map(NativeCreativeRelativityAuditTaskSaveDto::getVideoId).distinct().collect(Collectors.toList());
        Map<Long, List<NativeArchiveDto>> archiveInfoMap = nativeArchiveRpcProc.queryNativeArchiveMap(videoIdList, null, null);

        for (NativeCreativeRelativityAuditTaskSaveDto auditTaskDto : auditTasks) {
            String taskId = auditTaskDto.getId();
            LauUnitCreativeBean creativeBean = creativeBeanMap.get(auditTaskDto.getCreativeId());
            if (creativeBean == null) {
                log.info("stopTask, taskId={},creativeId={},avid={}", taskId, auditTaskDto.getCreativeId(), auditTaskDto.getVideoId());
                auditTaskService.stopTask(taskId);
                continue;
            }

            // log operate type
            Integer newOperateType = AuditTaskOperateType.getNewAuditTaskType(dto.getOperateType(), auditTaskDto.getType());
            log.info("execute,纠正operate type, taskId={},creativeId={},oldOperateType:{},taskType:{},newOperateType:{}",
                    taskId, auditTaskDto.getCreativeId(), dto.getOperateType(), auditTaskDto.getType(), newOperateType);
            dto.setOperateType(newOperateType);

            List<NativeArchiveDto> nativeArchiveDtos = archiveInfoMap.getOrDefault(auditTaskDto.getVideoId(), Collections.EMPTY_LIST);
            NativeArchiveDto nativeArchiveDto = NativeArchiveDto.filterByCreativeType(nativeArchiveDtos, auditTaskDto.getCreativeType(), auditTaskDto.getNativeBodyType());
            if (nativeArchiveDto == null) {
                log.info("execute creative relativity, no native archive, taskId={},creativeId={}", taskId, auditTaskDto.getCreativeId());
                auditTaskService.stopTask(taskId);
                continue;
            }

            NativeCreativeRelativityDto nativeCreativeRelativityDto = creativeRelativityDtoMap.get(auditTaskDto.getCreativeId());

            // 创意相关性不存在，则停止任务
            if (Objects.isNull(nativeCreativeRelativityDto)) {
                auditTaskService.stopTask(taskId);
                continue;
            }

            Boolean isLaunching = AdUtils.judgeIsLaunching(creativeBean);
            dto.setBizType(NativeBizTypeEnum.getByBiliUserId(creativeBean.getBilibiliUserId()).getCode());
            // 对于必火的任务，如果有审核结果，要求创意在投
            if (Objects.equals(CreativeType.PERSONAL_FLY_CREATIVE.getCode(), auditTaskDto.getCreativeType()) && !Objects.equals(nativeCreativeRelativityDto.getAuditStatus(), AuditStatus.INIT.getCode()) && !isLaunching) {
                log.info("execute person fly not launching stop task, taskId={},creativeId={}", taskId, auditTaskDto.getCreativeId());
                auditTaskService.stopTask(taskId);
                continue;
            }

            boolean needValidateTask = !pageGroupAuditCreativeIdList.contains(auditTaskDto.getCreativeId());
            if (needValidateTask && !validateTask(auditTaskDto, nativeCreativeRelativityDto, AuditTaskOperateType.getByCode(dto.getOperateType()))) {
                auditTaskService.stopTask(auditTaskDto.getId());
                continue;
            }

            Long mgkPageId = nativeCreativeRelativityDto.getMgkPageId();
            if (Utils.isPositive(mgkPageId) && finalWaitAuditMgkPageIds.contains(mgkPageId)) {
                auditTaskService.stopTask(auditTaskDto.getId());
                continue;
            }

            lockManager.doLock(auditTaskConfig.getNativeCreativeRelativityTaskLockName(nativeCreativeRelativityDto.getCreativeId()), () -> {
                //更新创意相关性
                try {
                    auditNativeCreativeRelativity(dto, nativeCreativeRelativityDto, AuditTaskOperateType.getByCode(dto.getOperateType()), dto.getOperator());
                } catch (Exception e) {
                    log.error("auditNativeCreativeRelativity error, creativeId={},taskId={}", nativeCreativeRelativityDto.getCreativeId(), dto.getTaskId(), e);
                }
                //更新任务
                NativeCreativeRelativityAuditTaskSaveDto auditTask = buildUpdTask(auditTaskDto, dto.getOperator().getOperatorName(),
                        AuditTaskOperateType.getByCode(dto.getOperateType()).toAuditStatus(auditTaskDto.getAuditStatus()),
                        dto.getRejectRemark(), nativeArchiveDto.getAuditStatus(), dto.getNativeArchivePassTime());
                log.info("updateTask,taskId:{},creativeId:{},avid:{},operateType={}", dto.getTaskId(),
                        nativeCreativeRelativityDto.getCreativeId(),nativeCreativeRelativityDto.getAvid(), dto.getOperateType());
                auditTaskService.updateTask(Collections.singletonList(auditTask));

                // 降级打开，不生成子任务
                if (!Utils.isPositive(nativeCreativeRelativityJiangjiSwitch)) {
                    //创建子任务
                    createChildTask(auditTask, dto.getOperateType(), nativeArchiveDto, dto.getMachineFlag());
                }
            });

            // 降级打开，不生成子任务
            if (!Utils.isPositive(nativeCreativeRelativityJiangjiSwitch)) {
                addLog(taskId, dto, CpcCreativeBaseDto.builder().creativeId(auditTaskDto.getCreativeId()).build(), auditTaskDto.getVideoId(), auditTaskDto.getType(), auditTaskDto.getNativeBodyType());
            }
        }

        return "ok";
    }

    public String modifyRejectReason(CreativeAuditTaskExecuteDto dto) {
        log.info("NativeCreativeRelativityAuditTask modifyRejectReason, dto={}", JSON.toJSONString(dto));

        // 获取任务
        List<NativeCreativeRelativityAuditTaskSaveDto> auditTasks = creativeRelativityQueryService.query(NativeCreativeRelativityAuditTaskQueryParam.builder().id(dto.getTaskId()).build());
        if (org.apache.commons.collections.CollectionUtils.isEmpty(auditTasks)) {
            log.info("NativeCreativeRelativityAuditTask modifyRejectReason, no tasks, taskIds={}", JSON.toJSONString(dto.getTaskId()));
            return auditTaskConfig.getNoTask();
        }
        List<String> parentTaskIds = auditTasks.stream().map(NativeCreativeRelativityAuditTaskSaveDto::getPId).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        if (org.apache.commons.collections.CollectionUtils.isEmpty(parentTaskIds)) {
            log.info("NativeCreativeRelativityAuditTask modifyRejectReason, no parentTaskIds, taskIds={}", JSON.toJSONString(dto.getTaskId()));
            return auditTaskConfig.getNoTask();
        }
        List<String> noParentTaskIds = auditTasks.stream().filter(x -> StringUtils.isEmpty(x.getPId())).map(NativeCreativeRelativityAuditTaskSaveDto::getId).collect(Collectors.toList());
        if (!org.apache.commons.collections.CollectionUtils.isEmpty(noParentTaskIds)) {
            // task没有父任务的，表示该 task不是质检任务
            log.info("NativeCreativeRelativityAuditTask modifyRejectReason, noParentTaskIds, taskIds={}", JSON.toJSONString(noParentTaskIds));
            return "非质检任务：" + noParentTaskIds;
        }

        List<NativeCreativeRelativityAuditTaskSaveDto> parentTasks = creativeRelativityQueryService.query(NativeCreativeRelativityAuditTaskQueryParam.builder().id(parentTaskIds)
                .build());

        List<Integer> creativeIdList = parentTasks.stream().map(NativeCreativeRelativityAuditTaskSaveDto::getCreativeId).distinct().collect(Collectors.toList());
        if (org.apache.commons.collections.CollectionUtils.isEmpty(creativeIdList)) {
            log.info("NativeCreativeRelativityAuditTask modifyRejectReason, no creativeIdList, taskIds={}", JSON.toJSONString(dto.getTaskId()));
            return auditTaskConfig.getNoCreative();
        }

        List<NativeCreativeRelativityDto> nativeCreativeRelativityDtos = nativeCreativeRelativityRpcProc.queryNativeCreativeRelativityListByCreativeIds(creativeIdList);
        Map<Integer, NativeCreativeRelativityDto> creativeRelativityDtoMap = nativeCreativeRelativityDtos.stream().collect(Collectors.toMap(NativeCreativeRelativityDto::getCreativeId, Function.identity()));

        // 更新底表 lau_creative_native_archive_relativity
        nativeCreativeRelativityDtos.forEach(creative -> {
            lockManager.doLock(auditTaskConfig.getNativeCreativeRelativityTaskLockName(creative.getCreativeId()), () -> {
                //更新创意相关性
                try {
                    auditNativeCreativeRelativity(dto, creative, AuditTaskOperateType.getByCode(dto.getOperateType()), dto.getOperator());
                } catch (Exception e) {
                    log.error("auditNativeCreativeRelativity error, creativeId={},taskId={}", creative.getCreativeId(), dto.getTaskId(), e);
                }
            });
        });

        // 更新任务
        List<Long> videoIdList = auditTasks.stream().map(NativeCreativeRelativityAuditTaskSaveDto::getVideoId).distinct().collect(Collectors.toList());
        Map<Long, List<NativeArchiveDto>> archiveInfoMap = nativeArchiveRpcProc.queryNativeArchiveMap(videoIdList, null, null);
        List<NativeCreativeRelativityAuditTaskSaveDto> updateTasks = auditTasks.stream().map(x -> {
            NativeArchiveDto nativeArchiveDto = NativeArchiveDto.filterByCreativeType(archiveInfoMap.get(x.getVideoId()), x.getCreativeType(), x.getNativeBodyType());
            if (nativeArchiveDto == null) {
                log.info("NativeCreativeRelativityAuditTask modifyRejectReason, no native archive, taskId={},creativeId={}", x.getId(), x.getCreativeId());
                auditTaskService.stopTask(x.getId());
                return null;
            }
            //更新任务
            NativeCreativeRelativityAuditTaskSaveDto auditTask = buildUpdTask(x, dto.getOperator().getOperatorName(),
                    getByCode(dto.getOperateType()).toAuditStatus(AuditStatus.REJECT.getCode()),
                    dto.getRejectRemark(), nativeArchiveDto.getAuditStatus(), dto.getNativeArchivePassTime());
            log.info("NativeCreativeRelativityAuditTask modifyRejectReason,taskId:{},creativeId:{},avid:{},operateType={}", x.getId(),
                    x.getCreativeId(), x.getVideoId(), dto.getOperateType());
            addLog(x.getId(), dto, CpcCreativeBaseDto.builder().creativeId(x.getCreativeId()).build(), x.getVideoId(), x.getType(), x.getNativeBodyType());
            return auditTask;
        }).filter(Objects::nonNull).collect(Collectors.toList());

        auditTaskService.updateTask(updateTasks);

        return null;
    }



    public void doExecuteForNoTasks(CreativeAuditTaskExecuteDto dto, List<Integer> creativeIds) {
        log.info("doExecuteForNoTasks, creativeIds={}", JSON.toJSONString(creativeIds));
        if (CollectionUtils.isEmpty(creativeIds)) {
            return;
        }
        dto.setOperateType(AuditTaskOperateType.FIRST_AUDIT_REJECT.getCode());
        List<NativeCreativeRelativityDto> relativityDtos = nativeCreativeRelativityRpcProc.queryNativeCreativeRelativityListByCreativeIds(creativeIds);
        Map<Integer, LauUnitCreativeBean> creativeBeanMap = adMngCreativeService.queryCreativeBaseInfoMap(creativeIds);

        for (NativeCreativeRelativityDto relativityDto : relativityDtos) {
            LauUnitCreativeBean creativeBean = creativeBeanMap.get(relativityDto.getCreativeId());
            if (creativeBean == null) {
                log.error("doExecuteForNoTasks, no creative, creativeId={}", relativityDto.getCreativeId());
                continue;
            }
            dto.setBizType(NativeBizTypeEnum.getByBiliUserId(creativeBean.getBilibiliUserId()).getCode());

            // 此处没办法。。。 不那么重要，否则整个消费失败
            try {
                //更新创意相关性
                auditNativeCreativeRelativity(dto, relativityDto, AuditTaskOperateType.getByCode(dto.getOperateType()), dto.getOperator());
            } catch (Exception e) {
                log.error("doExecuteForNoTasks error, avid={},creativeId={}", relativityDto.getAvid(), relativityDto.getAvid(), e);
            }
        }

    }


    public List<NativeCreativeRelativityAuditTaskSaveDto> stopExecutingTasksByCreativeId(AuditTaskExecuteDto executeDto) {
        List<NativeCreativeRelativityAuditTaskSaveDto> tasks = creativeRelativityQueryService.query(NativeCreativeRelativityAuditTaskQueryParam.builder()
//                .creativeId(executeDto.getCreativeId())
                .state(Lists.newArrayList(AuditTaskState.FREE.getCode(), AuditTaskState.EXECUTING.getCode()))
                // 所有类型的任务
                .type(Lists.newArrayList(AuditTaskType.AUTO_FIRST_AUDIT.getCode(), AuditTaskType.MANUAL_FIRST_AUDIT.getCode(), AuditTaskType.RECHECK_POSITIVE.getCode(), AuditTaskType.RECHECK_NEGATIVE.getCode(), AuditTaskType.SHELVE.getCode()))
                .build());
        // 修改任务状态
        List<NativeCreativeRelativityAuditTaskSaveDto> updateTaskList = new ArrayList<>();
        for (NativeCreativeRelativityAuditTaskSaveDto task : tasks) {
//            NativeCreativeRelativityAuditTaskSaveDto taskSaveDto = buildUpdTask(task, executeDto.getOperatorName(), executeDto.getToAuditStatus(), executeDto.getRejectRemark(), AuditStatus.REJECT.getCode());
//            updateTaskList.add(taskSaveDto);
        }
        auditTaskService.updateTask(updateTaskList);
        log.info("NativeCreativeRelativityAuditTaskExecuteService.stopExecutingTasksByCreativeId updateTaskListSize : {}", updateTaskList.size());
        return tasks;
    }


    private void auditNativeCreativeRelativity(CreativeAuditTaskExecuteDto dto, NativeCreativeRelativityDto relativityDto,
                                               AuditTaskOperateType operateType, Operator operator) {
        if (Objects.equals(dto.getMachineFlag(), NativeMachineFlagEnum.MACHINE_PASS.getCode())) {
            operateType = AuditTaskOperateType.FIRST_AUDIT_PASS;
        } else if (Objects.equals(dto.getMachineFlag(), NativeMachineFlagEnum.MACHINE_REJECT.getCode())) {
            operateType = AuditTaskOperateType.FIRST_AUDIT_REJECT;
        }
        log.info("auditNativeCreativeRelativity,taskId:{},creativeId:{},avid:{},operateType:{}", dto.getTaskId(),
                relativityDto.getCreativeId(),relativityDto.getAvid(), operateType.getCode());

        switch (operateType) {
            case FIRST_AUDIT_PASS:
                NativeCreativeRelativityAuditPassDto relativityAuditPassDto = NativeCreativeRelativityAuditPassDto.builder()
                        .creativeId(relativityDto.getCreativeId())
                        .avid(relativityDto.getAvid())
                        .version(relativityDto.getVersion())
                        .reCheckFlag(IsValid.FALSE.getCode())
                        .reCheckRemark("")
                        .operator(operator)
                        .build();
                nativeCreativeRelativityRpcProc.auditPass(relativityAuditPassDto);
                break;
            case FIRST_AUDIT_REJECT:
                NativeCreativeRelativityAuditRejectDto relativityAuditRejectDto = NativeCreativeRelativityAuditRejectDto.builder()
                        .avid(relativityDto.getAvid())
                        .creativeId(relativityDto.getCreativeId())
                        .reason(dto.getRejectRemark())
                        .version(relativityDto.getVersion())
                        .reCheckFlag(IsValid.FALSE.getCode())
                        .reCheckRemark("")
                        .operator(operator)
                        .build();
                nativeCreativeRelativityRpcProc.auditReject(relativityAuditRejectDto);
                break;
            case RECHECK_POSITIVE_CONFIRM:
                NativeCreativeRelativityAuditPassDto recheckRelativityAuditPassDto = NativeCreativeRelativityAuditPassDto.builder()
                        .avid(relativityDto.getAvid())
                        .creativeId(relativityDto.getCreativeId())
                        .version(relativityDto.getVersion())
                        .reCheckFlag(IsValid.TRUE.getCode())
                        .reCheckRemark(dto.getRecheckRemark())
                        .operator(operator)
                        .build();
                nativeCreativeRelativityRpcProc.auditPass(recheckRelativityAuditPassDto);
                break;
            case RECHECK_POSITIVE_RECTIFY:
            case REJECT_REASON_MODIFY:
                NativeCreativeRelativityAuditRejectDto recheckRelativityAuditRejectDto = NativeCreativeRelativityAuditRejectDto.builder()
                        .avid(relativityDto.getAvid())
                        .creativeId(relativityDto.getCreativeId())
                        .reason(dto.getRejectRemark())
                        .version(relativityDto.getVersion())
                        .reCheckFlag(IsValid.TRUE.getCode())
                        .reCheckRemark(dto.getRecheckRemark())
                        .operator(operator)
                        .build();
                nativeCreativeRelativityRpcProc.auditReject(recheckRelativityAuditRejectDto);
                break;
            default:
                break;


        }
    }


    private void addLog(String taskId, CreativeAuditTaskExecuteDto dto, CpcCreativeBaseDto creativeBaseDto, Long videoId, Integer oriTaskType, Integer nativeBodyType) {
        auditTaskLogService.add(buildOperateLogDto(taskId, dto, creativeBaseDto.getCreativeId(), videoId, oriTaskType, nativeBodyType));
        if (Objects.equals(AuditTaskOperateType.FIRST_AUDIT_SHELVE.getCode(), dto.getOperateType())) {

            logOperateService.addBatchComplexMarkLog(new DbTableInterface() {
                                                         @Override
                                                         public String getName() {
                                                             return "lau_creative_native_archive_relativity";
                                                         }

                                                         @Override
                                                         public String getIdIndex() {
                                                             return "creative_id";
                                                         }
                                                     }, dto.getOperator(), Lists.newArrayList(CpcCreativeComplexMarkOperationDto.builder().markReason(dto.getComplexReason()).build()),
                    Lists.newArrayList(creativeBaseDto.getCreativeId()), Lists.newArrayList(creativeBaseDto.getAccountId()));
        }
    }


    /**
     * 构建任务
     *
     * @param operatorName
     * @param toAuditStatus
     * @param rejectRemark
     * @return
     */
    public NativeCreativeRelativityAuditTaskSaveDto buildUpdTask(NativeCreativeRelativityAuditTaskSaveDto relativityDto,
                                                                 String operatorName, Integer toAuditStatus, String rejectRemark,
                                                                 Integer nativeArchiveStatus, Long nativeArchivePassTime) {
        log.info("buildUpdTask, ");
        if (Objects.isNull(relativityDto)) {
            return null;
        }
        NativeCreativeRelativityAuditTaskSaveDto task = NativeArchiveAuditTaskConvertor.MAPPER.taskDto2AuditTask(relativityDto);

        if (Objects.equals(operatorName, "SYSTEM")
                && (Objects.equals(task.getType(), AuditTaskType.MANUAL_FIRST_AUDIT.getCode()) || Objects.equals(task.getType(), AuditTaskType.SHELVE.getCode()))) {
            task.setType(AuditTaskType.AUTO_FIRST_AUDIT.getCode());
        } else if (!Objects.equals(operatorName, "SYSTEM") && (Objects.equals(task.getType(), AuditTaskType.AUTO_FIRST_AUDIT.getCode()))) {
            task.setType(AuditTaskType.MANUAL_FIRST_AUDIT.getCode());
        }

        if (Utils.isPositive(nativeArchivePassTime)) {
            if (Objects.equals(relativityDto.getAuditStatus(), AuditStatus.INIT.getCode())
                    && (Objects.equals(task.getType(), AuditTaskType.MANUAL_FIRST_AUDIT.getCode()) || Objects.equals(AuditTaskType.SHELVE.getCode(), task.getType()))
                    && (Objects.equals(task.getState(), AuditTaskState.FREE.getCode()) || Objects.equals(AuditTaskState.EXECUTING.getCode(), task.getState()))) {
                task.setCtime(new Timestamp(nativeArchivePassTime));
            }
        }

        task.setExecutorName(operatorName);
        task.setExecuteTime(Utils.getNow().getTime());
        // 审核状态
        task.setAuditStatus(toAuditStatus);
        task.setRejectRemark(Objects.equals(com.bilibili.adp.common.enums.AuditStatus.ACCEPT.getCode(), toAuditStatus) ? null : rejectRemark);
        // 完成
        task.setState(AuditTaskState.FINISH.getCode());
        if (!Objects.equals(com.bilibili.adp.common.enums.AuditStatus.INIT.getCode(), toAuditStatus)) {
            // 审核通过，创意状态=有效；否则为审核驳回
            task.setCreativeStatus(Objects.equals(com.bilibili.adp.common.enums.AuditStatus.ACCEPT.getCode(), toAuditStatus) ? CreativeStatus.VALID.getCode() : CreativeStatus.AUDIT_REJECT.getCode());
        }
        task.setPId(relativityDto.getPId());
        task.setNativeArchiveAuditStatus(nativeArchiveStatus);

        return task;
    }

    /**
     * 创建子任务
     *
     * @param task
     * @param operateType
     */

    public void createChildTask(NativeCreativeRelativityAuditTaskSaveDto task, Integer operateType,
                                NativeArchiveDto nativeArchiveDto, Integer machineFlag) {
        if (Objects.isNull(task)) {
            return;
        }

        //如果是质检任务 不会再生成子任务
        if (Objects.equals(task.getType(), AuditTaskType.RECHECK_NEGATIVE.getCode()) || Objects.equals(task.getType(), AuditTaskType.RECHECK_POSITIVE.getCode())) {
            return;
        }

        if (task.getNativeBodyType() == null) {
            task.setNativeBodyType(NativeBodyTypeEnum.ARCHIVE.getCode());
        }

        String executorName = task.getExecutorName();
        task.setPId(task.getId());
        task.setId(String.valueOf(snowflakeIdWorker.nextId()));

        task.setAcceptorName(null);
        task.setAcceptTime(null);
        task.setExecutorName(null);
        task.setExecuteTime(null);
        task.setCreateTime(Utils.getNow().getTime());
        if (Objects.nonNull(nativeArchiveDto)) {
            task.setNativeArchiveAuditStatus(nativeArchiveDto.getAuditStatus());
        }

        List<NativeCreativeRelativityDto> relativityDtoList = nativeCreativeRelativityRpcProc.queryNativeCreativeRelativityListByCreativeIds(Collections.singletonList(task.getCreativeId()));

        if (CollectionUtils.isEmpty(relativityDtoList)) {
            throw new IllegalArgumentException("没有查到创意相关性");
        }
        NativeCreativeRelativityDto nativeCreativeRelativityDto = relativityDtoList.get(0);


        // 都生成游离态
        // 一审通过操作
        if (Objects.equals(AuditTaskOperateType.FIRST_AUDIT_PASS.getCode(), operateType) || Objects.equals(NativeMachineFlagEnum.MACHINE_PASS.getCode(),
                machineFlag)) {
            task.setFirstTaskType(task.getType());
            task.setFirstAuditExecutorName(executorName);
            task.setAuditStatus(com.bilibili.adp.common.enums.AuditStatus.ACCEPT.getCode());
            task.setCreativeStatus(CreativeStatus.VALID.getCode());
            // type=人工质检通过任务
            task.setType(AuditTaskType.RECHECK_POSITIVE.getCode());
            task.setState(AuditTaskState.FREE.getCode());
            task.setMtime(nativeCreativeRelativityDto.getMtime());
            auditTaskService.saveTask(task);
            return;
        }

        // 一审驳回操作，生成质检驳回任务
        if (Objects.equals(AuditTaskOperateType.FIRST_AUDIT_REJECT.getCode(), operateType) || Objects.equals(NativeMachineFlagEnum.MACHINE_REJECT.getCode(),
                machineFlag)) {
            task.setFirstTaskType(task.getType());
            task.setFirstAuditExecutorName(executorName);
            task.setAuditStatus(com.bilibili.adp.common.enums.AuditStatus.REJECT.getCode());
            task.setCreativeStatus(CreativeStatus.AUDIT_REJECT.getCode());
            // type = 人工质检驳回任务
            task.setType(AuditTaskType.RECHECK_NEGATIVE.getCode());
            task.setState(AuditTaskState.FREE.getCode());
            task.setMtime(nativeCreativeRelativityDto.getMtime());
            auditTaskService.saveTask(task);
        }

        // 一审搁置操作，生成搁置任务
        if (Objects.equals(AuditTaskOperateType.FIRST_AUDIT_SHELVE.getCode(), operateType)) {
            task.setFirstTaskType(task.getType());
            task.setFirstAuditExecutorName(executorName);
            task.setAuditStatus(com.bilibili.adp.common.enums.AuditStatus.INIT.getCode());
            task.setCreativeStatus(CreativeStatus.AUDITING.getCode());
            task.setType(AuditTaskType.SHELVE.getCode());
            task.setState(AuditTaskState.FREE.getCode());
            task.setMtime(nativeCreativeRelativityDto.getMtime());
            auditTaskService.saveTask(task);
        }
    }

    private NativeAuditTaskLogDto buildOperateLogDto(String taskId, CreativeAuditTaskExecuteDto dto, Integer creativeId, Long videoId, Integer taskType, Integer nativeBodyType) {
        com.bilibili.adp.common.enums.AuditStatus auditStatus = com.bilibili.adp.common.enums.AuditStatus.UNKNOWN;
        if (AuditTaskOperateType.PASSED_TYPES.contains(dto.getOperateType())) {
            auditStatus = com.bilibili.adp.common.enums.AuditStatus.ACCEPT;
        } else if (AuditTaskOperateType.REJECT_TYPES.contains(dto.getOperateType())) {
            auditStatus = com.bilibili.adp.common.enums.AuditStatus.REJECT;
        }

        List<DiffItem> value = new ArrayList<>();
        DiffItem diffItem = DiffItem.builder().key("auditStatus").desc("审核状态").newValue(auditStatus.getName()).oldValue("待审核").build();
        value.add(diffItem);

        Integer logType = AuditTaskOperateType.getByCode(dto.getOperateType()).getAuditTaskLogType();
        if (Objects.equals(dto.getMachineFlag(), NativeMachineFlagEnum.MACHINE_PASS.getCode())) {
            if (Objects.equals(taskType, AuditTaskType.MANUAL_FIRST_AUDIT.getCode())) {
                logType = AuditTaskOperateType.FIRST_AUDIT_PASS.getCode();
            } else if (Objects.equals(taskType, AuditTaskType.RECHECK_POSITIVE.getCode()) || Objects.equals(taskType, AuditTaskType.RECHECK_NEGATIVE.getCode())) {
                logType = AuditTaskOperateType.RECHECK_POSITIVE_CONFIRM.getCode();
            }
        } else if (Objects.equals(dto.getMachineFlag(), NativeMachineFlagEnum.MACHINE_REJECT.getCode())) {
            if (Objects.equals(taskType, AuditTaskType.MANUAL_FIRST_AUDIT.getCode())) {
                logType = AuditTaskOperateType.FIRST_AUDIT_REJECT.getCode();
            } else if (Objects.equals(taskType, AuditTaskType.RECHECK_POSITIVE.getCode())||Objects.equals(taskType, AuditTaskType.RECHECK_NEGATIVE.getCode())) {
                logType = AuditTaskOperateType.RECHECK_POSITIVE_RECTIFY.getCode();
            }
        }

        return NativeAuditTaskLogDto.builder()

                .creativeId(creativeId)
                .taskId(taskId)
                .avid(videoId)
                .type(NativeLogType.CREATIVE_RELATIVITY.getCode())
                .auditStatus(auditStatus.getCode())
                .taskState(AuditTaskState.FINISH.getCode())
                .logType(logType)
                .rejectCategory(dto.getRejectCategory())
                .rejectReason(dto.getRejectReason())
                .rejectRemark(dto.getRejectRemark())
                .recheckRemark(dto.getRecheckRemark())
                .content(JSON.toJSONString(value))
                .operatorUsername(dto.getOperator().getOperatorName())
                .complexReason(dto.getComplexReason())
                .bodyType(nativeBodyType)
                .build();
    }

    private boolean validateTask(NativeCreativeRelativityAuditTaskSaveDto task, NativeCreativeRelativityDto relativityDto, AuditTaskOperateType operateType) {
        if (Objects.equals(AuditTaskState.FINISH.getCode(), task.getState()) || Objects.equals(AuditTaskState.STOP.getCode(), task.getState())) {
            return false;
        }

        if (Objects.equals(CreativeStatus.DELETED.getCode(), relativityDto.getCreativeStatus())) {
            return false;
        }

        switch (operateType) {
            case FIRST_AUDIT_PASS:
            case FIRST_AUDIT_REJECT:
                return (Objects.equals(AuditTaskType.MANUAL_FIRST_AUDIT.getCode(), task.getType()) || Objects.equals(AuditTaskType.SHELVE.getCode(), task.getType())) && Objects.equals(com.bilibili.adp.common.enums.AuditStatus.INIT.getCode(), relativityDto.getAuditStatus());
            case FIRST_AUDIT_SHELVE:
                return Objects.equals(AuditTaskType.MANUAL_FIRST_AUDIT.getCode(), task.getType()) && Objects.equals(com.bilibili.adp.common.enums.AuditStatus.INIT.getCode(), relativityDto.getAuditStatus());
            case RECHECK_POSITIVE_CONFIRM:
                return Objects.equals(AuditTaskType.RECHECK_NEGATIVE.getCode(), task.getType()) && Objects.equals(com.bilibili.adp.common.enums.AuditStatus.REJECT.getCode(), relativityDto.getAuditStatus());
            case RECHECK_POSITIVE_RECTIFY:
                return Objects.equals(AuditTaskType.RECHECK_POSITIVE.getCode(), task.getType()) && Objects.equals(com.bilibili.adp.common.enums.AuditStatus.ACCEPT.getCode(), relativityDto.getAuditStatus());
            default:
                return true;
        }
    }


}

