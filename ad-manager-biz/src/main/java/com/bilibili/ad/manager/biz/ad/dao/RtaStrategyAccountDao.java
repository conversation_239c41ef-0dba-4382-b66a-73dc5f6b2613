package com.bilibili.ad.manager.biz.ad.dao;

import com.bilibili.ad.manager.biz.po.RtaStrategyAccountPo;
import com.bilibili.ad.manager.biz.po.RtaStrategyAccountPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface RtaStrategyAccountDao {
    long countByExample(RtaStrategyAccountPoExample example);

    int deleteByExample(RtaStrategyAccountPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(RtaStrategyAccountPo record);

    int insertBatch(List<RtaStrategyAccountPo> records);

    int insertUpdateBatch(List<RtaStrategyAccountPo> records);

    int insert(RtaStrategyAccountPo record);

    int insertUpdateSelective(RtaStrategyAccountPo record);

    int insertSelective(RtaStrategyAccountPo record);

    List<RtaStrategyAccountPo> selectByExample(RtaStrategyAccountPoExample example);

    RtaStrategyAccountPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") RtaStrategyAccountPo record, @Param("example") RtaStrategyAccountPoExample example);

    int updateByExample(@Param("record") RtaStrategyAccountPo record, @Param("example") RtaStrategyAccountPoExample example);

    int updateByPrimaryKeySelective(RtaStrategyAccountPo record);

    int updateByPrimaryKey(RtaStrategyAccountPo record);
}