package com.bilibili.ad.manager.biz.grpc.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GoodsReplaceResultBo {
    
    private Long id;
    //账户id
    private Integer accountId;

    //内容id
    private Long contentId;

    //内容类型 1-评论
    private Integer contentType;

    //原始商品id
    private Long origItemId;

    //新商品id
    private Long newItemId;

    //跳转URL
    private String newJumpUrl;

    //唤起链接
    private String newSchemaUrl;

    private String lastOperator;
}
