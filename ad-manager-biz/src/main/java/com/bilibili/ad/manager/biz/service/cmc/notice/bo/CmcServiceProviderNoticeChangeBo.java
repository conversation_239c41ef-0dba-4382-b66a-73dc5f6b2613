package com.bilibili.ad.manager.biz.service.cmc.notice.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: muyi
 * @Date: 2024-11-11 21:55
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CmcServiceProviderNoticeChangeBo {

    /**
     * 公告id
     */
    private Long id;
    /**
     * 公告标题
     */
    private String title;
    /**
     * 公告跳转
     */
    private String jumpUrl;
    /**
     * 标签类型, 1-重要 2-新增
     * @see com.bilibili.ad.manager.common.enums.cmc.CmcServiceProviderTagEnum
     */
    private Integer tagType;
    /**
     * 是否公开 0:公开 1:登陆入驻可见
     */
    private Integer isPublic;
    /**
     * 优先级
     */
    private Integer sort;

}
