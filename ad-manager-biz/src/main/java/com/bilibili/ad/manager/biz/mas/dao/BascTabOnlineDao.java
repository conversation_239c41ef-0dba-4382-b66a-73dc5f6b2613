package com.bilibili.ad.manager.biz.mas.dao;

import com.bilibili.ad.manager.biz.mas.po.BascTabOnlinePo;
import com.bilibili.ad.manager.biz.mas.po.BascTabOnlinePoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface BascTabOnlineDao {
    long countByExample(BascTabOnlinePoExample example);

    int deleteByExample(BascTabOnlinePoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(BascTabOnlinePo record);

    int insertBatch(List<BascTabOnlinePo> records);

    int insertUpdateBatch(List<BascTabOnlinePo> records);

    int insert(BascTabOnlinePo record);

    int insertUpdateSelective(BascTabOnlinePo record);

    int insertSelective(BascTabOnlinePo record);

    List<BascTabOnlinePo> selectByExample(BascTabOnlinePoExample example);

    BascTabOnlinePo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") BascTabOnlinePo record, @Param("example") BascTabOnlinePoExample example);

    int updateByExample(@Param("record") BascTabOnlinePo record, @Param("example") BascTabOnlinePoExample example);

    int updateByPrimaryKeySelective(BascTabOnlinePo record);

    int updateByPrimaryKey(BascTabOnlinePo record);
}