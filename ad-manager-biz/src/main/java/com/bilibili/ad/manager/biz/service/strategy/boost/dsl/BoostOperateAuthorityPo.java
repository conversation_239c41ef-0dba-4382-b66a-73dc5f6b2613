package com.bilibili.ad.manager.biz.service.strategy.boost.dsl;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.annotation.Generated;

/**
 * BoostOperateAuthorityPo is a Querydsl bean type
 */
@Generated("com.querydsl.codegen.BeanSerializer")
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class BoostOperateAuthorityPo {

    private Integer bizType;

    private Integer busCate;

    private java.sql.Timestamp ctime;

    private Long id;

    private java.sql.Timestamp mtime;

    private Integer optType;

    private String username;

    private String role;

    public Integer getBizType() {
        return bizType;
    }

    public void setBizType(Integer bizType) {
        this.bizType = bizType;
    }

    public Integer getBusCate() {
        return busCate;
    }

    public void setBusCate(Integer busCate) {
        this.busCate = busCate;
    }

    public java.sql.Timestamp getCtime() {
        return ctime;
    }

    public void setCtime(java.sql.Timestamp ctime) {
        this.ctime = ctime;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public java.sql.Timestamp getMtime() {
        return mtime;
    }

    public void setMtime(java.sql.Timestamp mtime) {
        this.mtime = mtime;
    }

    public Integer getOptType() {
        return optType;
    }

    public void setOptType(Integer optType) {
        this.optType = optType;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    @Override
    public String toString() {
         return "bizType = " + bizType + ", busCate = " + busCate + ", ctime = " + ctime + ", id = " + id + ", mtime = " + mtime + ", optType = " + optType + ", username = " + username;
    }

}

