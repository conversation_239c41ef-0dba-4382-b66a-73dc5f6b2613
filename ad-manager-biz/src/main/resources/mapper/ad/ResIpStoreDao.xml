<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.ad.manager.biz.ad.dao.ResIpStoreDao">
  <resultMap id="BaseResultMap" type="com.bilibili.ad.manager.biz.po.ResIpStorePo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="ipv4_file_name" jdbcType="VARCHAR" property="ipv4FileName" />
    <result column="ipv4_file_url" jdbcType="VARCHAR" property="ipv4FileUrl" />
    <result column="ipv4_file_md5" jdbcType="VARCHAR" property="ipv4FileMd5" />
    <result column="ipv6_file_name" jdbcType="VARCHAR" property="ipv6FileName" />
    <result column="ipv6_file_url" jdbcType="VARCHAR" property="ipv6FileUrl" />
    <result column="ipv6_file_md5" jdbcType="VARCHAR" property="ipv6FileMd5" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="effective_time" jdbcType="TIMESTAMP" property="effectiveTime" />
    <result column="end_time" jdbcType="VARCHAR" property="endTime" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, name, ipv4_file_name, ipv4_file_url, ipv4_file_md5, ipv6_file_name, ipv6_file_url, 
    ipv6_file_md5, status, effective_time, end_time, is_deleted, ctime, mtime
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.ad.manager.biz.po.ResIpStorePoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from res_ip_store
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from res_ip_store
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from res_ip_store
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.ad.manager.biz.po.ResIpStorePoExample">
    delete from res_ip_store
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.ad.manager.biz.po.ResIpStorePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into res_ip_store (name, ipv4_file_name, ipv4_file_url, 
      ipv4_file_md5, ipv6_file_name, ipv6_file_url, 
      ipv6_file_md5, status, effective_time, 
      end_time, is_deleted, ctime, 
      mtime)
    values (#{name,jdbcType=VARCHAR}, #{ipv4FileName,jdbcType=VARCHAR}, #{ipv4FileUrl,jdbcType=VARCHAR}, 
      #{ipv4FileMd5,jdbcType=VARCHAR}, #{ipv6FileName,jdbcType=VARCHAR}, #{ipv6FileUrl,jdbcType=VARCHAR}, 
      #{ipv6FileMd5,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, #{effectiveTime,jdbcType=TIMESTAMP}, 
      #{endTime,jdbcType=VARCHAR}, #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.ad.manager.biz.po.ResIpStorePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into res_ip_store
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null">
        name,
      </if>
      <if test="ipv4FileName != null">
        ipv4_file_name,
      </if>
      <if test="ipv4FileUrl != null">
        ipv4_file_url,
      </if>
      <if test="ipv4FileMd5 != null">
        ipv4_file_md5,
      </if>
      <if test="ipv6FileName != null">
        ipv6_file_name,
      </if>
      <if test="ipv6FileUrl != null">
        ipv6_file_url,
      </if>
      <if test="ipv6FileMd5 != null">
        ipv6_file_md5,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="effectiveTime != null">
        effective_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="ipv4FileName != null">
        #{ipv4FileName,jdbcType=VARCHAR},
      </if>
      <if test="ipv4FileUrl != null">
        #{ipv4FileUrl,jdbcType=VARCHAR},
      </if>
      <if test="ipv4FileMd5 != null">
        #{ipv4FileMd5,jdbcType=VARCHAR},
      </if>
      <if test="ipv6FileName != null">
        #{ipv6FileName,jdbcType=VARCHAR},
      </if>
      <if test="ipv6FileUrl != null">
        #{ipv6FileUrl,jdbcType=VARCHAR},
      </if>
      <if test="ipv6FileMd5 != null">
        #{ipv6FileMd5,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="effectiveTime != null">
        #{effectiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.ad.manager.biz.po.ResIpStorePoExample" resultType="java.lang.Long">
    select count(*) from res_ip_store
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update res_ip_store
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.ipv4FileName != null">
        ipv4_file_name = #{record.ipv4FileName,jdbcType=VARCHAR},
      </if>
      <if test="record.ipv4FileUrl != null">
        ipv4_file_url = #{record.ipv4FileUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.ipv4FileMd5 != null">
        ipv4_file_md5 = #{record.ipv4FileMd5,jdbcType=VARCHAR},
      </if>
      <if test="record.ipv6FileName != null">
        ipv6_file_name = #{record.ipv6FileName,jdbcType=VARCHAR},
      </if>
      <if test="record.ipv6FileUrl != null">
        ipv6_file_url = #{record.ipv6FileUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.ipv6FileMd5 != null">
        ipv6_file_md5 = #{record.ipv6FileMd5,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.effectiveTime != null">
        effective_time = #{record.effectiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.endTime != null">
        end_time = #{record.endTime,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update res_ip_store
    set id = #{record.id,jdbcType=INTEGER},
      name = #{record.name,jdbcType=VARCHAR},
      ipv4_file_name = #{record.ipv4FileName,jdbcType=VARCHAR},
      ipv4_file_url = #{record.ipv4FileUrl,jdbcType=VARCHAR},
      ipv4_file_md5 = #{record.ipv4FileMd5,jdbcType=VARCHAR},
      ipv6_file_name = #{record.ipv6FileName,jdbcType=VARCHAR},
      ipv6_file_url = #{record.ipv6FileUrl,jdbcType=VARCHAR},
      ipv6_file_md5 = #{record.ipv6FileMd5,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=TINYINT},
      effective_time = #{record.effectiveTime,jdbcType=TIMESTAMP},
      end_time = #{record.endTime,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.ad.manager.biz.po.ResIpStorePo">
    update res_ip_store
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="ipv4FileName != null">
        ipv4_file_name = #{ipv4FileName,jdbcType=VARCHAR},
      </if>
      <if test="ipv4FileUrl != null">
        ipv4_file_url = #{ipv4FileUrl,jdbcType=VARCHAR},
      </if>
      <if test="ipv4FileMd5 != null">
        ipv4_file_md5 = #{ipv4FileMd5,jdbcType=VARCHAR},
      </if>
      <if test="ipv6FileName != null">
        ipv6_file_name = #{ipv6FileName,jdbcType=VARCHAR},
      </if>
      <if test="ipv6FileUrl != null">
        ipv6_file_url = #{ipv6FileUrl,jdbcType=VARCHAR},
      </if>
      <if test="ipv6FileMd5 != null">
        ipv6_file_md5 = #{ipv6FileMd5,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="effectiveTime != null">
        effective_time = #{effectiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.ad.manager.biz.po.ResIpStorePo">
    update res_ip_store
    set name = #{name,jdbcType=VARCHAR},
      ipv4_file_name = #{ipv4FileName,jdbcType=VARCHAR},
      ipv4_file_url = #{ipv4FileUrl,jdbcType=VARCHAR},
      ipv4_file_md5 = #{ipv4FileMd5,jdbcType=VARCHAR},
      ipv6_file_name = #{ipv6FileName,jdbcType=VARCHAR},
      ipv6_file_url = #{ipv6FileUrl,jdbcType=VARCHAR},
      ipv6_file_md5 = #{ipv6FileMd5,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      effective_time = #{effectiveTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.ad.manager.biz.po.ResIpStorePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into res_ip_store (name, ipv4_file_name, ipv4_file_url, 
      ipv4_file_md5, ipv6_file_name, ipv6_file_url, 
      ipv6_file_md5, status, effective_time, 
      end_time, is_deleted, ctime, 
      mtime)
    values (#{name,jdbcType=VARCHAR}, #{ipv4FileName,jdbcType=VARCHAR}, #{ipv4FileUrl,jdbcType=VARCHAR}, 
      #{ipv4FileMd5,jdbcType=VARCHAR}, #{ipv6FileName,jdbcType=VARCHAR}, #{ipv6FileUrl,jdbcType=VARCHAR}, 
      #{ipv6FileMd5,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, #{effectiveTime,jdbcType=TIMESTAMP}, 
      #{endTime,jdbcType=VARCHAR}, #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      name = values(name),
      ipv4_file_name = values(ipv4_file_name),
      ipv4_file_url = values(ipv4_file_url),
      ipv4_file_md5 = values(ipv4_file_md5),
      ipv6_file_name = values(ipv6_file_name),
      ipv6_file_url = values(ipv6_file_url),
      ipv6_file_md5 = values(ipv6_file_md5),
      status = values(status),
      effective_time = values(effective_time),
      end_time = values(end_time),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      res_ip_store
      (name,ipv4_file_name,ipv4_file_url,ipv4_file_md5,ipv6_file_name,ipv6_file_url,ipv6_file_md5,status,effective_time,end_time,is_deleted,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.name,jdbcType=VARCHAR},
        #{item.ipv4FileName,jdbcType=VARCHAR},
        #{item.ipv4FileUrl,jdbcType=VARCHAR},
        #{item.ipv4FileMd5,jdbcType=VARCHAR},
        #{item.ipv6FileName,jdbcType=VARCHAR},
        #{item.ipv6FileUrl,jdbcType=VARCHAR},
        #{item.ipv6FileMd5,jdbcType=VARCHAR},
        #{item.status,jdbcType=TINYINT},
        #{item.effectiveTime,jdbcType=TIMESTAMP},
        #{item.endTime,jdbcType=VARCHAR},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      res_ip_store
      (name,ipv4_file_name,ipv4_file_url,ipv4_file_md5,ipv6_file_name,ipv6_file_url,ipv6_file_md5,status,effective_time,end_time,is_deleted,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.name,jdbcType=VARCHAR},
        #{item.ipv4FileName,jdbcType=VARCHAR},
        #{item.ipv4FileUrl,jdbcType=VARCHAR},
        #{item.ipv4FileMd5,jdbcType=VARCHAR},
        #{item.ipv6FileName,jdbcType=VARCHAR},
        #{item.ipv6FileUrl,jdbcType=VARCHAR},
        #{item.ipv6FileMd5,jdbcType=VARCHAR},
        #{item.status,jdbcType=TINYINT},
        #{item.effectiveTime,jdbcType=TIMESTAMP},
        #{item.endTime,jdbcType=VARCHAR},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      name = values(name),
      ipv4_file_name = values(ipv4_file_name),
      ipv4_file_url = values(ipv4_file_url),
      ipv4_file_md5 = values(ipv4_file_md5),
      ipv6_file_name = values(ipv6_file_name),
      ipv6_file_url = values(ipv6_file_url),
      ipv6_file_md5 = values(ipv6_file_md5),
      status = values(status),
      effective_time = values(effective_time),
      end_time = values(end_time),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.ad.manager.biz.po.ResIpStorePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into res_ip_store
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null">
        name,
      </if>
      <if test="ipv4FileName != null">
        ipv4_file_name,
      </if>
      <if test="ipv4FileUrl != null">
        ipv4_file_url,
      </if>
      <if test="ipv4FileMd5 != null">
        ipv4_file_md5,
      </if>
      <if test="ipv6FileName != null">
        ipv6_file_name,
      </if>
      <if test="ipv6FileUrl != null">
        ipv6_file_url,
      </if>
      <if test="ipv6FileMd5 != null">
        ipv6_file_md5,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="effectiveTime != null">
        effective_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="ipv4FileName != null">
        #{ipv4FileName,jdbcType=VARCHAR},
      </if>
      <if test="ipv4FileUrl != null">
        #{ipv4FileUrl,jdbcType=VARCHAR},
      </if>
      <if test="ipv4FileMd5 != null">
        #{ipv4FileMd5,jdbcType=VARCHAR},
      </if>
      <if test="ipv6FileName != null">
        #{ipv6FileName,jdbcType=VARCHAR},
      </if>
      <if test="ipv6FileUrl != null">
        #{ipv6FileUrl,jdbcType=VARCHAR},
      </if>
      <if test="ipv6FileMd5 != null">
        #{ipv6FileMd5,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="effectiveTime != null">
        #{effectiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="name != null">
        name = values(name),
      </if>
      <if test="ipv4FileName != null">
        ipv4_file_name = values(ipv4_file_name),
      </if>
      <if test="ipv4FileUrl != null">
        ipv4_file_url = values(ipv4_file_url),
      </if>
      <if test="ipv4FileMd5 != null">
        ipv4_file_md5 = values(ipv4_file_md5),
      </if>
      <if test="ipv6FileName != null">
        ipv6_file_name = values(ipv6_file_name),
      </if>
      <if test="ipv6FileUrl != null">
        ipv6_file_url = values(ipv6_file_url),
      </if>
      <if test="ipv6FileMd5 != null">
        ipv6_file_md5 = values(ipv6_file_md5),
      </if>
      <if test="status != null">
        status = values(status),
      </if>
      <if test="effectiveTime != null">
        effective_time = values(effective_time),
      </if>
      <if test="endTime != null">
        end_time = values(end_time),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
    </trim>
  </insert>
</mapper>