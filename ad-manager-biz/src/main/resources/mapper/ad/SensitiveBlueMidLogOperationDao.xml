<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.ad.manager.biz.ad.dao.SensitiveBlueMidLogOperationDao">
  <resultMap id="BaseResultMap" type="com.bilibili.ad.manager.biz.po.SensitiveBlueMidLogOperationPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="obj_id" jdbcType="BIGINT" property="objId" />
    <result column="obj_flag" jdbcType="TINYINT" property="objFlag" />
    <result column="operate_type" jdbcType="TINYINT" property="operateType" />
    <result column="operator_username" jdbcType="VARCHAR" property="operatorUsername" />
    <result column="ip" jdbcType="VARCHAR" property="ip" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.bilibili.ad.manager.biz.po.SensitiveBlueMidLogOperationPo">
    <result column="value" jdbcType="LONGVARCHAR" property="value" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, obj_id, obj_flag, operate_type, operator_username, ip, ctime, mtime, is_deleted
  </sql>
  <sql id="Blob_Column_List">
    value
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.bilibili.ad.manager.biz.po.SensitiveBlueMidLogOperationPoExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from sensitive_blue_mid_log_operation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByExample" parameterType="com.bilibili.ad.manager.biz.po.SensitiveBlueMidLogOperationPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from sensitive_blue_mid_log_operation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from sensitive_blue_mid_log_operation
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from sensitive_blue_mid_log_operation
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.ad.manager.biz.po.SensitiveBlueMidLogOperationPoExample">
    delete from sensitive_blue_mid_log_operation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.ad.manager.biz.po.SensitiveBlueMidLogOperationPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into sensitive_blue_mid_log_operation (obj_id, obj_flag, operate_type, 
      operator_username, ip, ctime, 
      mtime, is_deleted, value
      )
    values (#{objId,jdbcType=BIGINT}, #{objFlag,jdbcType=TINYINT}, #{operateType,jdbcType=TINYINT}, 
      #{operatorUsername,jdbcType=VARCHAR}, #{ip,jdbcType=VARCHAR}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=TINYINT}, #{value,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.ad.manager.biz.po.SensitiveBlueMidLogOperationPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into sensitive_blue_mid_log_operation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="objId != null">
        obj_id,
      </if>
      <if test="objFlag != null">
        obj_flag,
      </if>
      <if test="operateType != null">
        operate_type,
      </if>
      <if test="operatorUsername != null">
        operator_username,
      </if>
      <if test="ip != null">
        ip,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="value != null">
        value,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="objId != null">
        #{objId,jdbcType=BIGINT},
      </if>
      <if test="objFlag != null">
        #{objFlag,jdbcType=TINYINT},
      </if>
      <if test="operateType != null">
        #{operateType,jdbcType=TINYINT},
      </if>
      <if test="operatorUsername != null">
        #{operatorUsername,jdbcType=VARCHAR},
      </if>
      <if test="ip != null">
        #{ip,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="value != null">
        #{value,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.ad.manager.biz.po.SensitiveBlueMidLogOperationPoExample" resultType="java.lang.Long">
    select count(*) from sensitive_blue_mid_log_operation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update sensitive_blue_mid_log_operation
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.objId != null">
        obj_id = #{record.objId,jdbcType=BIGINT},
      </if>
      <if test="record.objFlag != null">
        obj_flag = #{record.objFlag,jdbcType=TINYINT},
      </if>
      <if test="record.operateType != null">
        operate_type = #{record.operateType,jdbcType=TINYINT},
      </if>
      <if test="record.operatorUsername != null">
        operator_username = #{record.operatorUsername,jdbcType=VARCHAR},
      </if>
      <if test="record.ip != null">
        ip = #{record.ip,jdbcType=VARCHAR},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.value != null">
        value = #{record.value,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update sensitive_blue_mid_log_operation
    set id = #{record.id,jdbcType=BIGINT},
      obj_id = #{record.objId,jdbcType=BIGINT},
      obj_flag = #{record.objFlag,jdbcType=TINYINT},
      operate_type = #{record.operateType,jdbcType=TINYINT},
      operator_username = #{record.operatorUsername,jdbcType=VARCHAR},
      ip = #{record.ip,jdbcType=VARCHAR},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      value = #{record.value,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update sensitive_blue_mid_log_operation
    set id = #{record.id,jdbcType=BIGINT},
      obj_id = #{record.objId,jdbcType=BIGINT},
      obj_flag = #{record.objFlag,jdbcType=TINYINT},
      operate_type = #{record.operateType,jdbcType=TINYINT},
      operator_username = #{record.operatorUsername,jdbcType=VARCHAR},
      ip = #{record.ip,jdbcType=VARCHAR},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.ad.manager.biz.po.SensitiveBlueMidLogOperationPo">
    update sensitive_blue_mid_log_operation
    <set>
      <if test="objId != null">
        obj_id = #{objId,jdbcType=BIGINT},
      </if>
      <if test="objFlag != null">
        obj_flag = #{objFlag,jdbcType=TINYINT},
      </if>
      <if test="operateType != null">
        operate_type = #{operateType,jdbcType=TINYINT},
      </if>
      <if test="operatorUsername != null">
        operator_username = #{operatorUsername,jdbcType=VARCHAR},
      </if>
      <if test="ip != null">
        ip = #{ip,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="value != null">
        value = #{value,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.bilibili.ad.manager.biz.po.SensitiveBlueMidLogOperationPo">
    update sensitive_blue_mid_log_operation
    set obj_id = #{objId,jdbcType=BIGINT},
      obj_flag = #{objFlag,jdbcType=TINYINT},
      operate_type = #{operateType,jdbcType=TINYINT},
      operator_username = #{operatorUsername,jdbcType=VARCHAR},
      ip = #{ip,jdbcType=VARCHAR},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      value = #{value,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.ad.manager.biz.po.SensitiveBlueMidLogOperationPo">
    update sensitive_blue_mid_log_operation
    set obj_id = #{objId,jdbcType=BIGINT},
      obj_flag = #{objFlag,jdbcType=TINYINT},
      operate_type = #{operateType,jdbcType=TINYINT},
      operator_username = #{operatorUsername,jdbcType=VARCHAR},
      ip = #{ip,jdbcType=VARCHAR},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.ad.manager.biz.po.SensitiveBlueMidLogOperationPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into sensitive_blue_mid_log_operation (obj_id, obj_flag, operate_type, 
      operator_username, ip, ctime, 
      mtime, is_deleted, value
      )
    values (#{objId,jdbcType=BIGINT}, #{objFlag,jdbcType=TINYINT}, #{operateType,jdbcType=TINYINT}, 
      #{operatorUsername,jdbcType=VARCHAR}, #{ip,jdbcType=VARCHAR}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=TINYINT}, #{value,jdbcType=LONGVARCHAR}
      )
    <trim prefix="on duplicate key update" suffixOverrides=",">
      obj_id = values(obj_id),
      obj_flag = values(obj_flag),
      operate_type = values(operate_type),
      operator_username = values(operator_username),
      ip = values(ip),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
      value = values(value),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      sensitive_blue_mid_log_operation
      (obj_id,obj_flag,operate_type,operator_username,ip,ctime,mtime,is_deleted,value)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.objId,jdbcType=BIGINT},
        #{item.objFlag,jdbcType=TINYINT},
        #{item.operateType,jdbcType=TINYINT},
        #{item.operatorUsername,jdbcType=VARCHAR},
        #{item.ip,jdbcType=VARCHAR},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.value,jdbcType=LONGVARCHAR},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      sensitive_blue_mid_log_operation
      (obj_id,obj_flag,operate_type,operator_username,ip,ctime,mtime,is_deleted,value)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.objId,jdbcType=BIGINT},
        #{item.objFlag,jdbcType=TINYINT},
        #{item.operateType,jdbcType=TINYINT},
        #{item.operatorUsername,jdbcType=VARCHAR},
        #{item.ip,jdbcType=VARCHAR},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.value,jdbcType=LONGVARCHAR},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      obj_id = values(obj_id),
      obj_flag = values(obj_flag),
      operate_type = values(operate_type),
      operator_username = values(operator_username),
      ip = values(ip),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
      value = values(value),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.ad.manager.biz.po.SensitiveBlueMidLogOperationPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into sensitive_blue_mid_log_operation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="objId != null">
        obj_id,
      </if>
      <if test="objFlag != null">
        obj_flag,
      </if>
      <if test="operateType != null">
        operate_type,
      </if>
      <if test="operatorUsername != null">
        operator_username,
      </if>
      <if test="ip != null">
        ip,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="value != null">
        value,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="objId != null">
        #{objId,jdbcType=BIGINT},
      </if>
      <if test="objFlag != null">
        #{objFlag,jdbcType=TINYINT},
      </if>
      <if test="operateType != null">
        #{operateType,jdbcType=TINYINT},
      </if>
      <if test="operatorUsername != null">
        #{operatorUsername,jdbcType=VARCHAR},
      </if>
      <if test="ip != null">
        #{ip,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="value != null">
        #{value,jdbcType=LONGVARCHAR},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="objId != null">
        obj_id = values(obj_id),
      </if>
      <if test="objFlag != null">
        obj_flag = values(obj_flag),
      </if>
      <if test="operateType != null">
        operate_type = values(operate_type),
      </if>
      <if test="operatorUsername != null">
        operator_username = values(operator_username),
      </if>
      <if test="ip != null">
        ip = values(ip),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="value != null">
        value = values(value),
      </if>
    </trim>
  </insert>
</mapper>