syntax = "proto3";
package ai.llm.summarize;

import "extension/wdcli/wdcli.proto";

option go_package = "buf.bilibili.co/bapis/bapis-gen/ai/llm.summarize;api";
option java_package = "com.bapis.ai.llm.summarize";
option java_multiple_files = true;
option (wdcli.appid) = "ai.llm.llm-summarize";

message LLMRequest {
    // 用户mid
    int64 mid = 1;
    // 稿件id
    int64 avid = 2;
    // 稿件分p id
    int64 cid = 3;    
    // up主mid
    int64 up_mid = 4;
    // 风控信号
    int32 gaia_data = 5 [deprecated = true];
    // 风控信号
    string gaia_data_mock = 6;
}


message LLMIconResponse {
    // 0 不可生成 1 可生成 
    int32 code = 1;
}

message LLMSummaryResponse {
    // 0 正常下发 1 已提交生产
    int32 code = 1;
    // 总结正文
    ModelReuslt model_result = 2;
    // 内容id
    int64 stid = 3;
    // 当前用户点赞状态, 0 无 1 赞 -1 踩
    int32 status = 4;
    // 内容点赞数
    int64 like_num = 5;
    // 内容点踩数
    int64 dislike_num = 6;
}

service LLMSummarize {
    // 获取入口信息
    rpc GetIcon(LLMRequest) returns(LLMIconResponse);
    // 获取摘要信息
    rpc GetSummary(LLMRequest) returns(LLMSummaryResponse);
}

message PartOutline {
    // 大纲时间戳，单位是秒
    int64 timestamp = 1;
    // 时间戳对应的后面的内容
    string content = 2;
    // 该分段对应的图片boss地址
    string image_url = 3;
}

message Outline {
    // 分段的title
    string title = 1;
    repeated PartOutline part_outline = 2;
    // 分段时间戳
    int64 timestamp = 3;
}

message PartSubtitle {
    // 字幕开始时间戳，单位是秒
    int64 start_timestamp = 1;
    // 字幕截止时间戳，单位是秒
    int64 end_timestamp = 2; 
    // 字幕内容
    string content = 3;
}

message Subtitle {
    // 字幕分段title
    string title = 1;
    // 字幕段落内容
    repeated PartSubtitle part_subtitle = 2;
    // 分段时间戳
    int64 timestamp = 3;
}

message ModelReuslt {
    // 摘要还是大纲, 0是摘要，1是大纲，2是都有
    int32 result_type = 1;
    // 摘要
    string summary = 2;
    // 大纲
    repeated Outline outline = 3;
    // 字幕
    repeated Subtitle subtitle = 4;
}