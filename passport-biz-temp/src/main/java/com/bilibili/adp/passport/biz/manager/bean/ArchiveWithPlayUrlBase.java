package com.bilibili.adp.passport.biz.manager.bean;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by cuihaichuan on 2017/8/1.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ArchiveWithPlayUrlBase {
    private Long aid;//稿件ID
    private Integer videos;
    private Integer tid;//分区ID
    private String tname;
    private Integer copyright;//投稿类型 1-自制、2-转载
    private String pic;
    private String title;//稿件标题
    private Long pubdate;//发布时间
    private Long ctime;//创建时间
    private String desc;//稿件简介
    private Integer state;//稿件状态
    private Integer attribute;//附加权限参数聚合?
    private Integer duration;//稿件时长
    @SerializedName("redirect_url")
    private String redirectUrl;
    private ArchiveDimension dimension;
    private ArchiveOwner owner;
    private ArchiveStat stat;
}
