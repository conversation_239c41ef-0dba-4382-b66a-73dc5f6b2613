package com.bilibili.adp.passport.biz.manager.bean.pay;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RefundWithVirtualCurrencyReq implements Serializable {
    private static final long serialVersionUID = -7484183439945878849L;
    /** 业务方id **/
    private String customerId;

    /** 业务方订单号 **/
    private String orginOrderId;

    /** 请求流水号 **/
    private String customerSeq;

    /** 原交易请求流水号 **/
    private String orginCustomerSeq;

    /** 原账户交易单号 **/
    private String orginTransOrderNo;

    /** 平台类型 0 非IOS 1 IOS **/
    private Integer platform;

    /** 虚拟币类型 **/
    private String coinType;

    /** 交易金额（元） **/
    private String transBalance;

    /** 原交易金额（元） **/
    private String payBalance;

    /** 支付完成后，异步通知商户服务支付信息 **/
    private String notifyUrl;

    /** 订单描述 **/
    private String transDesc;

    /** 用户id **/
    private Long uid;

    /** 签名校验类型，目前仅支持MD5 **/
    private String signType;

    /** 签名检验，生成规则见约定 **/
    private String sign;
}
