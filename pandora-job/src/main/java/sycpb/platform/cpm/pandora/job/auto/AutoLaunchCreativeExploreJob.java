package sycpb.platform.cpm.pandora.job.auto;

import com.alibaba.excel.util.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import sycpb.platform.cpm.pandora.service.api.core.v6.creative.ICreativeExploreService;

import java.util.List;

import static com.xxl.job.core.biz.model.ReturnT.SUCCESS;

/**
 * @ClassName AutoLaunchCreativeExploreJob
 * <AUTHOR>
 * @Date 2025/4/7 2:00 下午
 * @Version 1.0
 **/
@Component
@JobHandler("AutoLaunchCreativeExploreJob")
@Slf4j
public class AutoLaunchCreativeExploreJob extends IJobHandler {

    @Autowired
    private ICreativeExploreService creativeExploreService;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        Long startId = 0L;
        if (StringUtils.hasText(param)) {
            startId = Long.parseLong(param);
        }

        List<Long> refreshIds = creativeExploreService.refreshExploreCreativeByPage(startId, 1000);
        while (!CollectionUtils.isEmpty(refreshIds)) {
            log.info("AutoLaunchCreativeExploreJob refreshIds:{}", JSON.toJSONString(refreshIds));
            startId = refreshIds.get(refreshIds.size() - 1);
            refreshIds = creativeExploreService.refreshExploreCreativeByPage(startId, 1000);
        }

        return SUCCESS;
    }
}
