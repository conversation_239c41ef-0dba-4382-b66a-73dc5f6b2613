package com.bilibili.risk;

import com.bilibili.risk.service.task.PullRedisZsetService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;

@Slf4j
@Component
public class LauCreativeDeleteJob implements BasicProcessor {


    @Override
    public ProcessResult process(TaskContext context) throws Exception {

        return new ProcessResult(true, "刷数据量:" + 1);
    }
}
