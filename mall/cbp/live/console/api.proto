syntax = "proto3";
import "extension/wdcli/wdcli.proto";
import "github.com/gogo/protobuf/gogoproto/gogo.proto";
package mall.cbp.live.console;

option java_package = "com.bapis.mall.cbp.live.console";
option go_package = "buf.bilibili.co/bapis/bapis-gen/mall/cbp.live.console;api";
option java_multiple_files = true;
option (wdcli.appid) = "mall.ecommerce.mall-cbp";

service console{
  // 直播中心添加商品(异步)
  rpc AnchorCenterConsoleAddGoodsV2 (AnchorCenterConsoleAddGoodsReq) returns (AnchorCenterConsoleAddGoodsResp);
  // 获取中控添加商品状态
  rpc AnchorCenterConsoleGetAddStatus (AnchorCenterConsoleGetAddStatusReq) returns (AnchorCenterConsoleGetAddStatusResp);
  // 直播中心删除商品
  rpc AnchorCenterConsoleDelGoods (AnchorCenterConsoleDelGoodsReq) returns (AnchorCenterConsoleDelGoodsResp);
  // 直播中心置顶商品
  rpc AnchorCenterConsoleTopGoods (AnchorCenterConsoleTopGoodsReq) returns (AnchorCenterConsoleTopGoodsResp);
  // 更新讲解状态
  rpc AnchorUpdateCenterConsoleGoodsStatus (AnchorUpdateCenterConsoleGoodsStatusReq) returns (AnchorUpdateCenterConsoleGoodsStatusResp);
  // 直播中心商品排序
  rpc AnchorCenterConsoleSortGoods (AnchorCenterConsoleSortGoodsReq) returns (AnchorCenterConsoleSortGoodsResp);
  // 直播中心商品隐藏/显示
  rpc AnchorCenterConsoleHideGoods (AnchorCenterConsoleHideGoodsReq) returns (AnchorCenterConsoleHideGoodsResp);
  // 给activity用，判断是否所有goodsId都是隐藏的
  rpc AllGoodsInvisible(AllGoodsInvisibleReq) returns (AllGoodsInvisibleResp);
  // 直播中心获取商品列表
  rpc AnchorGetCenterConsoleGoodsList (AnchorGetCenterConsoleGoodsListReq) returns (AnchorGetCenterConsoleGoodsListResp);
  // 获取购物车商品列表页
  rpc GetShoppingCartList (GetShoppingCartListReq) returns (GetShoppingCartListResp);
  // 获取解说卡片信息
  rpc GetShoppingCardDetail (GetShoppingCardDetailReq) returns (ShoppingCartItem);
}

message GetShoppingCardDetailReq {
  // uid
  int64 uid = 1 [(gogoproto.jsontag) = 'uid'];
  // 房间id
  int64 room_id = 2 [(gogoproto.jsontag) = 'room_id'];
  // 主播uid
  int64 ruid = 3 [(gogoproto.jsontag) = 'ruid'];
  // 是否走新通用化版本 1：是 默认否
  int64 new_version = 4 [(gogoproto.jsontag) = 'new_version'];
  // 客户端版本号
  string build = 5 [(gogoproto.jsontag) = 'build'];
  // 客户端类型
  string platform = 6 [(gogoproto.jsontag) = 'platform'];
}

message AllGoodsInvisibleResp {
  bool result = 1 [json_name = "result"];
}

message AllGoodsInvisibleReq {
  uint64 live_id = 1 [json_name = "live_id"];
  repeated string goods_ids = 2 [json_name = "goods_ids"];
}

message AnchorCenterConsoleHideGoodsResp {

}

message AnchorCenterConsoleHideGoodsReq {
  // 主播uid 当mcn时ruid使用这个字段 否则用登录态
  int64 ruid = 1 [(gogoproto.moretags) = 'form:"ruid"'];
  // 商品id
  string goods_id = 2 [(gogoproto.moretags) = 'form:"goods_id" validate:"required"'];
  // 1 表示要变成隐藏 0 表示要变成显示
  bool invisible = 3 [(gogoproto.moretags) = 'form:"invisible"'];
}

message AnchorCenterConsoleSortGoodsResp {
  // 状态 0成功 1失败
  int64 status = 1 [(gogoproto.jsontag) = 'status'];
}

message AnchorCenterConsoleSortGoodsReq {
  // 商品id
  string goods_id = 1 [(gogoproto.jsontag) = 'goods_id'];
  // 修改后页面内商品id前面那个商品的id 不存在（最前面）为0
  string before_goods_id = 2 [(gogoproto.jsontag) = 'before_goods_id'];
  // 用户uid
  int64 uid = 3 [(gogoproto.jsontag) = 'uid'];
}

message AnchorUpdateCenterConsoleGoodsStatusResp {
  // 状态 0成功 1失败
  int64 status = 1 [(gogoproto.jsontag) = 'status'];
  // 房间id
  int64 room_id = 2 [(gogoproto.jsontag) = 'room_id'];
  // 场次ID
  int64 live_id = 3 [(gogoproto.jsontag) = 'live_id'];
  // 商品id
  string goods_id = 4 [(gogoproto.jsontag) = 'goods_id'];
  // 讲解开始时间
  int64 start_time = 5 [(gogoproto.jsontag) = 'start_time'];
  // 讲解结束时间
  int64 end_time = 6 [(gogoproto.jsontag) = 'end_time'];
  // 商品来源 1淘宝 2京东 3会员 xx
  int64 source = 7 [(gogoproto.jsontag) = 'source'];
}

message AnchorUpdateCenterConsoleGoodsStatusReq {
  // 商品id
  string goods_id = 1 [(gogoproto.jsontag) = 'goods_id'];
  // 0取消讲解 1讲解 2重新弹出讲解卡
  int64 status = 2 [(gogoproto.jsontag) = 'status'];
  // 用户uid
  int64 uid = 3 [(gogoproto.jsontag) = 'uid'];
}

message AnchorCenterConsoleTopGoodsResp {
  // 0成功 1失败
  int64 status = 1 [(gogoproto.jsontag) = 'status'];
}

message AnchorCenterConsoleTopGoodsReq {
  // 用户uid
  int64 uid = 1 [(gogoproto.jsontag) = 'uid'];
  // 商品id
  string goods_id = 2 [(gogoproto.jsontag) = 'goods_id'];
  // 操作类型 0：置顶 1：取消
  int64 operation = 3 [(gogoproto.jsontag) = 'operation'];
}

message AnchorCenterConsoleDelGoodsResp {
  // 0成功 1失败
  int64 status = 1 [(gogoproto.jsontag) = 'status'];
  // 轮询删除完成结果checksum
  string checksum = 2 [(gogoproto.jsontag) = 'checksum'];
}

message AnchorCenterConsoleDelGoodsReq {
  // 用户uid
  int64 uid = 1 [(gogoproto.jsontag) = 'uid'];
  // 商品id
  string goods_id = 2 [(gogoproto.jsontag) = 'goods_id'];
}

message AnchorCenterConsoleGetAddStatusResp{
  // 添加状态 -1:未完成 0:成功 1:部分失败
  int64 status = 1 [(gogoproto.jsontag) = 'status'];
  // 添加失败个数
  int64 fail_count = 2 [(gogoproto.jsontag) = 'fail_count'];
  // 失败提示
  string tips = 3 [(gogoproto.jsontag) = 'tips'];
}

message AnchorCenterConsoleGetAddStatusReq{
  // 主播id
  int64 uid = 1 [(gogoproto.jsontag) = 'uid'];
  // 中控添加商品校验码
  string check_sum = 2 [(gogoproto.jsontag) = 'check_sum'];
}

message AnchorCenterConsoleAddGoodsReq {
  // 用户uid
  int64 uid = 1 [(gogoproto.jsontag) = 'uid'];
  // 商品id
  repeated string goods_id = 2 [(gogoproto.jsontag) = 'goods_id'];
  // 添加方式 1：置顶添加，0：默认置底添加
  int64 add_top = 3 [(gogoproto.jsontag) = 'add_top'];
  // 是否隐藏 true：隐藏 false：可见 （默认可见）
  bool invisible = 4 [(gogoproto.jsontag) = 'invisible'];
  // 指定添加序号
  int64 sequence = 5 [(gogoproto.jsontag) = 'sequence'];
}

message AnchorCenterConsoleAddGoodsResp {
  // 状态 0成功 1失败
  int64 status = 1 [(gogoproto.jsontag) = 'status'];
  //失败返回的商品名称
  repeated string invalid_goods = 2 [(gogoproto.jsontag) = 'invalid_goods'];
  // 添加商品校验码
  string check_sum = 3 [(gogoproto.jsontag) = 'check_sum'];
}

message AnchorGetCenterConsoleGoodsListReq {
  // 用户uid
  int64 uid = 1 [(gogoproto.jsontag) = 'uid'];
  // 第几页
  int64 page = 2 [(gogoproto.jsontag) = 'page'];
  // 每页大小
  int64 page_size = 3 [(gogoproto.jsontag) = 'page_size'];
  // 商品类型 0售卖列表 1失效列表
  int64 goods_type = 4 [(gogoproto.jsontag) = 'goods_type'];
}

message AnchorGetCenterConsoleGoodsListResp {
  // 商品总量
  int64 total_count = 1 [(gogoproto.jsontag) = 'total_count'];
  // 售卖中商品总量
  int64 selling_goods_count = 2 [(gogoproto.jsontag) = 'selling_goods_count'];
  // 失效商品总量
  int64 invalid_goods_count = 3 [(gogoproto.jsontag) = 'invalid_goods_count'];
  // 总页数
  int64 total_page_size = 4 [(gogoproto.jsontag) = 'total_page_size'];
  // 商品详情
  repeated AnchorGetCenterConsoleGoodsItem item = 5 [(gogoproto.jsontag) = 'item'];
  // 服务端当前时间，用于时间校准
  int64 timestamp = 6 [(gogoproto.jsontag) = 'timestamp'];
  // 当前开赏中商品的oneItemId
  int64 activeIchibanOneItemId = 7 [(gogoproto.jsontag) = 'active_ichiban_one_item_id'];
}

// 直播中控商品详情
message AnchorGetCenterConsoleGoodsItem {
  // 商品实际id
  string goods_id = 1 [(gogoproto.jsontag) = 'goods_id'];
  // 商品名
  string goods_name = 2 [(gogoproto.jsontag) = 'goods_name'];
  // 商品icon
  string goods_icon = 3 [(gogoproto.jsontag) = 'goods_icon'];
  // 商品价格 （会员购对应SKU中最高价格）
  string goods_price = 4 [(gogoproto.jsontag) = 'goods_price'];
  // 最高价格（会员购对应SKU中最高价格）
  string goods_max_price = 5 [(gogoproto.jsontag) = 'goods_max_price'];
  // 优惠券名
  string coupon_name = 6 [(gogoproto.jsontag) = 'coupon_name'];
  // 佣金率
  string commission_rate = 7 [(gogoproto.jsontag) = 'commission_rate'];
  // 去购买率
  string go_to_bug_rate = 8 [(gogoproto.jsontag) = 'go_to_bug_rate'];
  // 来自哪个平台
  string source = 9 [(gogoproto.jsontag) = 'source'];
  // 来自哪个商铺
  string from_shop = 10 [(gogoproto.jsontag) = 'from_shop'];
  // 0未讲解中 1讲解中
  int64 status = 11 [(gogoproto.jsontag) = 'status'];
  // 失效原因 售卖商品该字段为空
  string invalid_reason = 12 [(gogoproto.jsontag) = 'invalid_reason'];
  // 商品分类
  string category = 13 [(gogoproto.jsontag) = 'category'];
  // 排序 id
  int64 goods_sort_id = 14 [(gogoproto.jsontag) = 'goods_sort_id'];
  // 支付率
  string pay_rate = 15 [(gogoproto.jsontag) = 'pay_rate'];
  // 销售额
  string sale_amount = 16 [(gogoproto.jsontag) = 'sale_amount'];
  // 会员购商品上下架状态 商品售卖状态 0:未上架 1：上架 2：已下架  // http://bapi.bilibili.co/project/7826/interface/api/379302  data.itemsStatus
  int64 member_goods_status = 17 [(gogoproto.jsontag) = 'member_goods_status'];
  // 商品售卖状态 11:售卖中 13：已售罄 14，暂无库存	// http://bapi.bilibili.co/project/7826/interface/api/379302  data.saleStatus
  int64 member_goods_sale_status = 18 [(gogoproto.jsontag) = 'member_goods_sale_status'];
  // 是否预售  0:否 1:是
  int64 is_pre_sale = 19 [(gogoproto.jsontag) = 'is_pre_sale'];
  // 活动信息(目前只有秒杀信息会在里面，早划算单独有一个结构体) http://bapi.bilibili.co/project/7826/interface/api/379302  data.activityInfoVO
  ActivityInfo activity_info = 20 [(gogoproto.jsontag) = 'activity_info'];
  // 预售信息 http://bapi.bilibili.co/project/7826/interface/api/379302  data.advState
  PreSaleInfo pre_sale_info = 21 [(gogoproto.jsontag) = 'pre_sale_info'];
  // 早划算信息  http://bapi.bilibili.co/project/7826/interface/api/379302 data.earlyBuyActivityInfo
  EarlyBirdInfo early_bird_info = 22 [(gogoproto.jsontag) = 'early_bird_info'];
  // 优惠券信息
  repeated CouponInfo coupon_list = 23 [(gogoproto.jsontag) = 'coupon_list'];
  // 商品链接
  string goods_url = 24 [(gogoproto.jsontag) = 'goods_url'];
  // 卖点信息
  SellingPointInfo selling_point_info = 25;
  // 曝光人数
  string exposure = 26 [(gogoproto.jsontag) = 'exposure'];
  // 福利购是否配置活动 2 已配置 1 未配置 0 不展示入口
  int64 gift_buy_show = 27 [(gogoproto.jsontag) = 'gift_buy_show'];
  // 是否为精选商品
  int64 selected_goods_status = 28 [(gogoproto.jsontag) = 'selected_goods_status'];
  // 审核状态 0:待审核 1:审核通过 2:审核驳回
  int64 audit_status = 29 [(gogoproto.jsontag) = 'audit_status'];
  // 预计审核通过时间
  int64 anticipate_pass_time = 30 [(gogoproto.jsontag) = 'anticipate_pass_time'];
  // 商品状态 0:正常 1:失效
  int64 goods_status = 31 [(gogoproto.jsontag) = 'goods_status'];
  // 商品直播状态 0:否, 1:是
  int64 live_status = 32 [(gogoproto.jsontag) = 'live_status'];
  // 是否专享券，true是，false否
  bool is_exclusive = 33 [(gogoproto.jsontag) = 'is_exclusive'];
  // 会员购通用赏信息
  RewardInfo reward_info = 34 [(gogoproto.jsontag) = 'reward_info'];
  // 虚拟商品扩展信息
  VirExtraInfo vir_extra_info = 35 [(gogoproto.jsontag) = 'vir_extra_info'];
  // 商品分组
  repeated GoodsGroupDetail goods_groups = 36 [(gogoproto.jsontag) = 'goods_groups'];
  // 通用价格展示
  PriceInfo price_info = 37 [(gogoproto.jsontag) = 'price_info'];
  // 定时购信息
  GoodsScheduleInfo goods_schedule = 38 [(gogoproto.jsontag) = 'goods_schedule'];
  // 营销数据
  MarketingDetail marketing_detail = 39 [(gogoproto.jsontag) = 'marketing_detail'];
  // 是否置顶 0否 1是
  int64 is_top = 40 [(gogoproto.jsontag) = 'is_top'];
  // 服务商名称
  string provide_name = 41 [(gogoproto.jsontag) = 'provide_name'];
  // 是否隐藏
  bool invisible = 42 [(gogoproto.jsontag) = 'invisible'];
  // 会员购内部类型
  int64 member_source_type = 43 [(gogoproto.jsontag) = 'member_source_type'];
  // (会员购)定向佣金率
  string sp_commission_rate = 44 [(gogoproto.jsontag) = 'sp_commission_rate'];
  // 是否自营
  bool is_self_only = 45 [(gogoproto.jsontag) = 'is_self_only'];
  // 所属计划列表
  repeated PlanBaseInfo plan_list = 46 [(gogoproto.jsontag) = 'plan_list'];
  // oneItemId
  int64 one_item_id = 47 [(gogoproto.jsontag) = 'one_item_id'];
  // 直播惊喜赏商品(非null时是直播惊喜赏商品）
  LiveMerchantIchiban live_merchant_ichiban = 48 [(gogoproto.jsontag) = 'live_merchant_ichiban'];
}

// 会员购活动信息
message ActivityInfo {
  int64 activity_id = 1 [(gogoproto.jsontag) = 'activity_id'];
  // 活动状态：0：未开始，1：进行中，2：已结束
  int64 activity_status = 2 [(gogoproto.jsontag) = 'activity_status'];
  // 活动开始时间
  int64 start_time = 3 [(gogoproto.jsontag) = 'start_time'];
  // 活动结束时间
  int64 end_time = 4 [(gogoproto.jsontag) = 'end_time'];
  // 是否所有sku参加活动 0 否，1 是
  int64 is_all_sku = 5 [(gogoproto.jsontag) = 'is_all_sku'];
  // 类型5 秒杀
  int64 type = 6 [(gogoproto.jsontag) = 'type'];
  // 促销活动最低价
  string lower_discount_price = 7 [(gogoproto.jsontag) = 'lower_discount_price'];
  // 促销活动最高价
  string upper_discount_price = 8 [(gogoproto.jsontag) = 'upper_discount_price'];
  // 直播自定义预热时间 活动开始前4小时
  int64 warm_up_time = 9 [(gogoproto.jsontag) = 'warm_up_time'];
  // 活动售罄状态  无库存:true  有库存:false
  bool activity_sale_out = 10 [(gogoproto.jsontag) = 'activity_sale_out'];
  // 活动标题
  string activity_title = 11 [(gogoproto.jsontag) = 'activity_title'];
  // 活动描述
  string activity_summary = 12 [(gogoproto.jsontag) = 'activity_summary'];
}

message PreSaleInfo {
  // 定金
  string deposit = 1 [(gogoproto.jsontag) = 'deposit'];
  // 定金类型: 0 百分比 1 固定金额 2 全额预定
  int64 deposit_type = 2 [(gogoproto.jsontag) = 'deposit_type'];
  // 最大定金
  string max_deposit = 3 [(gogoproto.jsontag) = 'max_deposit'];
  // 预售开始时间
  int64 presale_start_order_time = 4 [(gogoproto.jsontag) = 'presale_start_order_time'];
  // 预售结束时间
  int64 presale_end_order_time = 5 [(gogoproto.jsontag) = 'presale_end_order_time'];
  // 补款开始时间
  int64 pre_sale_supply_money_start_time = 6 [(gogoproto.jsontag) = 'pre_sale_supply_money_start_time'];
  // 补款结束时间
  int64 pre_sale_supply_money_end_time = 7 [(gogoproto.jsontag) = 'pre_sale_supply_money_end_time'];
}

// 早划算信息
message EarlyBirdInfo {
  int64 activity_id = 1 [(gogoproto.jsontag) = 'activity_id'];
  // 活动状态：0：未开始，1：进行中，2：已结束
  string activity_status = 2 [(gogoproto.jsontag) = 'activity_status'];
  // 活动开始时间
  int64 start_time = 3 [(gogoproto.jsontag) = 'start_time'];
  // 活动结束时间
  int64 end_time = 4 [(gogoproto.jsontag) = 'end_time'];
  // 直播自定义预热时间  活动开始前4小时
  int64 warm_up_time = 5 [(gogoproto.jsontag) = 'warm_up_time'];
  // 活动信息
  string activity_summary = 6 [(gogoproto.jsontag) = 'activity_summary'];
}

// 优惠券信息
message CouponInfo {
  // 券授权ID
  string coupon_authority_id = 1 [(gogoproto.jsontag) = 'coupon_authority_id'];
  // 券来源id
  string coupon_source_id = 2 [(gogoproto.jsontag) = 'coupon_source_id'];
  // 券名称
  string coupon_name = 3 [(gogoproto.jsontag) = 'coupon_name'];
  // 券类型，2满减，3立减
  int64 coupon_type = 4 [(gogoproto.jsontag) = 'coupon_type'];
  // 折扣类型描述
  string coupon_type_desc = 5 [(gogoproto.jsontag) = 'coupon_type_desc'];
  // 0表示已领完，1表示未领完
  int64 user_coupon_receive_status = 6 [(gogoproto.jsontag) = 'user_coupon_receive_status'];
  // 折扣值（元）
  string discount = 7 [(gogoproto.jsontag) = 'discount'];
  // 折扣门槛（元）
  string discount_condition = 8 [(gogoproto.jsontag) = 'discount_condition'];
  // 优惠券前台显示名称
  string show_name = 9 [(gogoproto.jsontag) = 'show_name'];
  // 获取当日起几天有效
  int64 use_effect_days = 10 [(gogoproto.jsontag) = 'use_effect_days'];
  // 获取当日起可用时间单位 0天 1周 2月 3年
  int64 use_effect_unit = 11 [(gogoproto.jsontag) = 'use_effect_unit'];
  // 失效时间时间，精确到秒，当useEffectDays > 0的时候该字段无效
  int64 use_end_time = 12 [(gogoproto.jsontag) = 'use_end_time'];
  // 生效时间，精确到秒，当useEffectDays > 0的时候该字段无效
  int64 use_start_time = 13 [(gogoproto.jsontag) = 'use_start_time'];
  // 优惠券领取状态 COUPON_TO_USE(83110080L, "去使用"), COUPON_IS_RECEIVED(83110038L, "已领取"), COUPON_CAN_RECEIVE(83110039L, "可领取"), COUPON_IS_OUT_STOCK(83110037L, "已领完")
  string coupon_status = 14 [(gogoproto.jsontag) = 'coupon_status'];
  string coupon_status_msg = 15 [(gogoproto.jsontag) = 'coupon_status_msg'];
  bool   is_received = 16 [(gogoproto.jsontag) = 'is_received'];
  // 是否专享券，true是，false否
  bool is_exclusive = 17 [(gogoproto.jsontag) = 'is_exclusive'];
}

message SellingPointInfo {
  // 卖点描述
  string desc = 1 [(gogoproto.jsontag) = 'desc'];
  // 卖点审核状态 0:未审核 1:已通过 2:已驳回
  int64 audit_status = 2 [(gogoproto.jsontag) = 'audit_status'];
  // 驳回原因
  string refuse_reason = 3 [(gogoproto.jsontag) = 'refuse_reason'];
}

message RewardInfo {
  // 会员购通用赏信息 1 一番赏
  int64 type = 1 [(gogoproto.jsontag) = 'type'];
  // 会员购通用赏单位 张
  string unit = 2 [(gogoproto.jsontag) = 'unit'];
  // 会员购通用赏商品icon
  string icon = 3 [(gogoproto.jsontag) = 'icon'];
  // 会员购通用赏b端商品icon
  string b_icon = 4 [(gogoproto.jsontag) = 'b_icon'];
}

message VirExtraInfo {
  // 虚拟商品状态，0 未开售，1售卖中 2已售罄 3已结束
  int64 virtual_state = 1 [(gogoproto.jsontag) = 'virtual_state'];
  // 虚拟商品佣金率类型，佣金比或者固定佣金（固定佣金单位分） 1-固定佣金 2-佣金率
  int64 commission_type = 2 [(gogoproto.jsontag) = 'commission_type'];
  // 开售时间（时间戳，精确到秒）
  int64 sale_start_time = 3 [(gogoproto.jsontag) = 'sale_start_time'];
  // 停售时间（时间戳，精确到秒）
  int64 sale_end_time = 4 [(gogoproto.jsontag) = 'sale_end_time'];
}

message GoodsGroupDetail{
  //分组id
  int64 group_id = 1 [(gogoproto.jsontag) = 'group_id'];
  // 状态：0：通过，1：审核中，2：已删除 请求参数不传 客户端不要用
  int64 group_status = 2 [(gogoproto.jsontag) = 'group_status'];
  // 商品个数 请求参数不传 客户端不要用
  int64 goods_count = 3 [(gogoproto.jsontag) = 'goods_count'];
  // 分组名称
  string group_name = 4 [(gogoproto.jsontag) = 'group_name'];
  // 拒绝原因 请求参数不传 客户端不要用
  string invalid_reason = 5 [(gogoproto.jsontag) = 'invalid_reason'];
  // 奇奇怪怪的名字 请求参数不传 客户端不要用
  string new_group_name = 6 [(gogoproto.jsontag) = 'new_group_name'];
}

message PriceInfo {
  // 正常价格
  NormalPrice normal = 1 [(gogoproto.jsontag) = 'normal'];
  // 活动价格
  repeated ActivityPrice activity = 2 [(gogoproto.jsontag) = 'activity'];
}

message NormalPrice {
  // 价格前缀 如 "劵后"
  string prefix_price = 1 [(gogoproto.jsontag) = 'prefix_price'];
  // 价格
  string sale_price = 2 [(gogoproto.jsontag) = 'sale_price'];
  // 价格后缀 如 "起"
  string suffix_price = 3 [(gogoproto.jsontag) = 'suffix_price'];
  // 划扣价
  string strock_price = 4 [(gogoproto.jsontag) = 'strock_price'];
  // 商品开售时间（时间戳，精确到秒）
  int64 sale_start_time = 5 [(gogoproto.jsontag) = 'sale_start_time'];
  // 商品停售时间（时间戳，精确到秒）
  int64 sale_end_time = 6 [(gogoproto.jsontag) = 'sale_end_time'];
  // 是否显示下划线标识 1：显示 0：不显示
  int64 strock_show = 7 [(gogoproto.jsontag) = 'strock_show'];
}

message ActivityPrice {
  // 营销活动价格开始时间（时间戳，精确到秒）
  int64 market_start_time = 1 [(gogoproto.jsontag) = 'market_start_time'];
  // 营销活动价格结束时间（时间戳，精确到秒）
  int64 market_end_time = 2 [(gogoproto.jsontag) = 'market_end_time'];
  // 价格前缀 如 "劵后"
  string prefix_price = 3 [(gogoproto.jsontag) = 'prefix_price'];
  // 价格
  string sale_price = 4 [(gogoproto.jsontag) = 'sale_price'];
  // 价格后缀 如 "起"
  string suffix_price = 5 [(gogoproto.jsontag) = 'suffix_price'];
  // 划扣价
  string strock_price = 6 [(gogoproto.jsontag) = 'strock_price'];
  // 是否显示下划线标识 1：显示 0：不显示
  int64 strock_show = 7 [(gogoproto.jsontag) = 'strock_show'];
}

message GoodsScheduleInfo {
  message Info {
    // 定时购开始时间戳
    int64 begin_at = 1 [(gogoproto.jsontag) = 'begin_at'];
    // 定时购预告价格信息
    string show_price = 2 [(gogoproto.jsontag) = 'show_price'];
  }

  // 是否有权限操作定时购
  bool enable = 1 [(gogoproto.jsontag) = 'enable'];
  // 已配置定时购信息，无配置时返回null
  Info info = 2 [(gogoproto.jsontag) = 'info'];

}

message MarketingDetail {

  message MarketingDetailInfo {
    // 定时购开始时间戳
    int64 end_at = 1 [(gogoproto.jsontag) = 'end_at'];
    // 到手价格信息
    string show_price = 2 [(gogoproto.jsontag) = 'show_price'];
  }

  // 是否有权限操作定时购
  bool enable = 1 [(gogoproto.jsontag) = 'enable'];
  // 已配置营销信息，无配置时返回null
  MarketingDetailInfo info = 2 [(gogoproto.jsontag) = 'info'];

}

message PlanBaseInfo{
  // 计划id
  int64 id = 1 [(gogoproto.jsontag) = 'id'];
  // 计划名称
  string name = 2 [(gogoproto.jsontag) = 'name'];
  // 备注
  string note = 3 [(gogoproto.jsontag) = 'note'];
  // 失效商品数
  int64 invalid_count = 4 [(gogoproto.jsontag) = 'invalid_count'];
}

message LiveMerchantIchiban {
  // 是否开赏中
  bool is_active = 1 [(gogoproto.jsontag) = 'is_active'];
  // 待开箱数
  int32 wait_reveal_count = 2 [(gogoproto.jsontag) = 'wait_reveal_count'];
}

message GetShoppingCartListReq {
  // 用户uid
  int64 uid = 1 [(gogoproto.jsontag) = 'uid'];
  // 主播uid
  int64 ruid = 2 [(gogoproto.jsontag) = 'ruid'];
  // 房间id
  int64 room_id = 3 [(gogoproto.jsontag) = 'room_id'];
  // page
  int64 page = 4 [(gogoproto.jsontag) = 'page'];
  // page_size
  int64 page_size = 5 [(gogoproto.jsontag) = 'page_size'];
  // 分组id
  int64 group_id = 6  [(gogoproto.jsontag) = 'group_id'];
  // 是否走新通用化版本 1：是 默认否
  int64 new_version = 7 [(gogoproto.jsontag) = 'new_version'];
  // 客户端版本号
  string build = 8 [(gogoproto.jsontag) = 'build'];
  // 客户端类型
  string platform = 9 [(gogoproto.jsontag) = 'platform'];
}

message GetShoppingCartListResp {
  // 商品列表页信息
  repeated ShoppingCartItem item = 1 [(gogoproto.jsontag) = 'goods_list'];
  // 讲解中商品
  ShoppingCartItem introduce_goods = 2 [(gogoproto.jsontag) = 'introduce_goods'];
  // 是否还有下一页 true: 有， false：无
  bool has_next_page = 3 [(gogoproto.jsontag) = 'has_next_page'];
  // 服务端当前时间，用于时间校准
  int64 timestamp = 4 [(gogoproto.jsontag) = 'timestamp'];
}

message ShoppingCartItem {
  // 商品外显排序id
  int64 goods_sort_id = 1 [(gogoproto.jsontag) = 'goods_sort_id'];
  // 商品实际id
  string goods_id = 2 [(gogoproto.jsontag) = 'goods_id'];
  // 商品名 http://bapi.bilibili.co/project/7826/interface/api/379334  data.name
  string goods_name = 3 [(gogoproto.jsontag) = 'goods_name'];
  // 实际售价（会员购对应SKU最低价格） http://bapi.bilibili.co/project/7826/interface/api/379334 data.price
  string goods_price = 4 [(gogoproto.jsontag) = 'goods_price'];
  // 最高价格（会员购对应SKU中最高价格） http://bapi.bilibili.co/project/7826/interface/api/379334 data.maxPrice
  string goods_max_price = 5 [(gogoproto.jsontag) = 'goods_max_price'];
  // 商品售卖状态 11:售卖中 13：已售罄 14，暂无库存 http://bapi.bilibili.co/project/7826/interface/api/379334
  int64 sale_status = 6 [(gogoproto.jsontag) = 'sale_status'];
  // 优惠券名称
  string coupon_name = 7 [(gogoproto.jsontag) = 'coupon_name'];
  // 商品icon  http://bapi.bilibili.co/project/7826/interface/api/379334 data.img
  string goods_icon = 8 [(gogoproto.jsontag) = 'goods_icon'];
  // 0未讲解中 1讲解中
  int64 goods_status = 9 [(gogoproto.jsontag) = 'goods_status'];
  // 商品来源平台
  int64 source = 10 [(gogoproto.jsontag) = 'source'];
  // H5跳转url
  string h5_url = 11 [(gogoproto.jsontag) = 'h5_url'];
  // 是否预售  0:否 1:是 http://bapi.bilibili.co/project/7826/interface/api/379334  data.itemsType
  int64 is_pre_sale = 12 [(gogoproto.jsontag) = 'is_pre_sale'];
  // 活动信息(目前只有秒杀信息会在里面，早划算单独有一个结构体) http://bapi.bilibili.co/project/7826/interface/api/379334  data.activityInfoVO
  ActivityInfo activity_info = 13 [(gogoproto.jsontag) = 'activity_info'];
  // 预售信息 http://bapi.bilibili.co/project/7826/interface/api/379334  data.advState
  PreSaleInfo pre_sale_info = 14 [(gogoproto.jsontag) = 'pre_sale_info'];
  // 早划算信息  http://bapi.bilibili.co/project/7826/interface/api/379334 data.earlyBuyActivityInfo
  EarlyBirdInfo early_bird_info = 15 [(gogoproto.jsontag) = 'early_bird_info'];
  // 券后价
  string coupon_discount_price = 16 [(gogoproto.jsontag) = 'coupon_discount_price'];
  // 0无回放 1有回放
  int64 record_status = 18 [(gogoproto.jsontag) = 'record_status'];
  // 卖点信息
  string selling_point = 19 [(gogoproto.jsontag) = 'selling_point'];
  // 回放页H5 url ？？？什么鬼 两个字段序列化同样的名字？
  string record_h5_url = 20 [(gogoproto.jsontag) = 'record_status'];
  // 当前热卖数
  int64 hot_buy_num = 21 [(gogoproto.jsontag) = 'hot_buy_num'];
  // 0无回放 1有回放 ？？？什么鬼 同样字段两个名字？
  int64 replay_status = 22 [(gogoproto.jsontag) = 'replay_status'];
  // 福利购信息
  repeated GiftBuyInfo gift_buy_info = 23 [(gogoproto.jsontag) = 'gift_buy_info'];
  // 是否专享券，true是，false否
  bool is_exclusive = 24 [(gogoproto.jsontag) = 'is_exclusive'];
  // 券id
  string coupon_id = 25 [(gogoproto.jsontag) = 'coupon_id'];
  // 会员购通用赏信息
  RewardInfo reward_info = 26 [(gogoproto.jsontag) = 'reward_info'];
  // tag list 客户端展示用，没别的用途
  repeated string goods_tag_list = 27 [(gogoproto.jsontag) = 'goods_tag_list'];
  // 虚拟商品扩展信息
  VirExaInfo virtual_extra_info = 28 [(gogoproto.jsontag) = 'virtual_extra_info'];
  // 通用价格展示
  PriceInfo price_info = 29 [(gogoproto.jsontag) = 'price_info'];
  // 通用购物车商品按钮展示
  BtnInfo btn_info = 30 [(gogoproto.jsontag) = 'btn_info'];
  // 优惠劵模块展示
  CouInfo coupon_info = 31 [(gogoproto.jsontag) = 'coupon_info'];
  // 活动模块展示
  ActInfo active_info = 32 [(gogoproto.jsontag) = 'active_info'];
  // 卡片跳转地址
  string jump_url = 33 [(gogoproto.jsontag) = 'jump_url'];
  // json 格式的透出信息
  string feed_tag = 34 [(gogoproto.jsontag) = 'feed_tag'];
  // 带货侧统一的商品id
  int64 item_id = 35 [(gogoproto.jsontag) = 'item_id'];
  int64 one_item_id = 36 [(gogoproto.jsontag) = 'one_item_id'];
}

message GiftBuyInfo {
  // 活动时间
  int64 active_stime = 1 [(gogoproto.jsontag) = 'active_stime', json_name = "active_stime"];
  // 服务器当前时间
  int64 cur_time = 2 [(gogoproto.jsontag) = 'cur_time', json_name = "cur_time"];
  //活动结束时间
  int64 active_etime = 3 [(gogoproto.jsontag) = 'active_etime', json_name = "active_etime"];
  // 库存数量
  int64 stock_num = 4 [(gogoproto.jsontag) = 'stock_num', json_name = "stock_num"];
  // 任务信息
  repeated GTasks task_list = 5 [(gogoproto.jsontag) = 'task_list', json_name = "task_list"];
}

message GTasks {
  // 名称
  string name = 1 [(gogoproto.jsontag) = 'name', json_name = "name"];
  // tag标识
  string tag = 2 [(gogoproto.jsontag) = 'tag', json_name = "tag"];
  // 进度
  string progress = 3 [(gogoproto.jsontag) = 'progress', json_name = "progress"];
  // 是否完成 1完成 0 未完成
  int64  finish = 4 [(gogoproto.jsontag) = 'finish', json_name = "finish"];
}

message VirExaInfo {
  // 是否是商品类，1是 0否
  int64 goods_type = 1 [(gogoproto.jsontag) = 'goods_type'];
  // 商品模型新增（是否使用半包容器 1: 自定义 ）
  int64 web_container_type = 2 [(gogoproto.jsontag) = 'web_container_type'];
}

message BtnInfo {
  // 虚拟按钮商品状态 0未开售 1去抢购 2已售罄 3已结束
  int64 card_btn_status = 1 [(gogoproto.jsontag) = 'card_btn_status'];
  // 商品卡片按钮文案title  商品类（未开售、去抢购、已售罄、已结 束） 非商品类：兜底文案：立即查看、立即下载、立即抽奖、立即查看、立即预约
  string card_btn_title = 2 [(gogoproto.jsontag) = 'card_btn_title'];
  // 商品卡片按钮文案样式 0:grey 1:highlight 2:lightgrey
  int64 card_btn_style = 3 [(gogoproto.jsontag) = 'card_btn_style'];
  // 商品按钮跳转事件地址(保持现状，不再变更)
  string card_btn_jumpurl = 4 [(gogoproto.jsontag) = 'card_btn_jumpurl'];
  // 商品按钮跳转事件地址(保持现状，不再变更)
  string card_btn_route_jump_url = 5 [(gogoproto.jsontag) = 'card_btn_route_jump_url'];
  // 商品按钮跳转事件地址
  string card_btn_click_url = 6 [(gogoproto.jsontag) = 'card_btn_click_url'];
  // 新版实验商品按钮文案
  string card_btn_new_title = 7 [(gogoproto.jsontag) = 'card_btn_new_title'];
}

message CouInfo {
  // 优惠劵名称
  string coupon_name = 1 [(gogoproto.jsontag) = 'coupon_name'];
  // 劵信息描述 如：满x减y
  string coupon_desc = 2 [(gogoproto.jsontag) = 'coupon_desc'];
  // 优惠劵id
  string coupon_id = 3 [(gogoproto.jsontag) = 'coupon_id'];
  // 优惠劵数量
  int64 coupon_count = 4 [(gogoproto.jsontag) = 'coupon_count'];
  // 是否专享券，true是，false否
  bool is_exclusive = 5 [(gogoproto.jsontag) = 'is_exclusive'];
}

message ActInfo {
  // 活动开始前文案 如：秒杀|稍后开始
  string activity_before_text = 1 [(gogoproto.jsontag) = 'activity_before_text'];
  // 活动进行中文案
  string activity_effect_text = 2 [(gogoproto.jsontag) = 'activity_effect_text'];
  // 活动icon 秒杀icon
  string activity_icon = 3 [(gogoproto.jsontag) = 'activity_icon'];
  // 活动类型，0：无活动 1：倒计时 2：固定文案
  int64 activity_type = 4 [(gogoproto.jsontag) = 'activity_type'];
  // 活动商品库存  0:无，1:有
  bool activity_sold_out = 5 [(gogoproto.jsontag) = 'activity_sold_out'];
  // 活动开始时间 时间戳 秒
  int64 activity_start_time = 6 [(gogoproto.jsontag) = 'activity_start_time'];
  // 活动结束时间 时间戳 秒
  int64 activity_end_time = 7 [(gogoproto.jsontag) = 'activity_end_time'];
  // 活动类型埋点需要 0 无活动、1：描述 2：早划算
  int64 activity_mode = 8 [(gogoproto.jsontag) = 'activity_mode'];
}