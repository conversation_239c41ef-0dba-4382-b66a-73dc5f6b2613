syntax = "proto3";

import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "google/protobuf/empty.proto";
import "extension/wdcli/wdcli.proto";
package main.community.reply.v1;

option (wdcli.appid)       = "main.community.reply";
option go_package          = "buf.bilibili.co/bapis/bapis-gen/community/interface.reply;api";
option java_package        = "com.bapis.community.interfaces.reply";
option java_multiple_files = true;

service ReplyInterface {
  // 网关调用，评论区交互管理相关功能
  rpc SubjectInteractionStatus(SubjectInteractionStatusReq) returns (SubjectInteractionStatusReply);
  // 网关调用，评论区是否受管控（管理员/up主精选、只读、管理员/up主关闭、关闭时间序等等）
  rpc SubjectStatus(SubjectInteractionStatusReq) returns (SubjectInteractionStatusReply);
  // 网关调用，评论区是否受管控（管理员/up主精选、只读、管理员/up主关闭、关闭时间序等等）
  rpc SubjectsStatus(SubjectsInteractionStatusReq) returns (SubjectsInteractionStatusReply);
  // 网关调用，用于在展示评论列表前获取一些必要信息
  rpc ReplyListPreface(ReplyListPrefaceReq) returns (ReplyListPrefaceReply);
  // 网关调用，用于在展示评论列表前获取一些必要信息（批量接口）
  rpc ReplyListsPreface(ReplyListsPrefaceReq) returns (ReplyListsPrefaceReply);

  // 评论状态（可见、删除、自见等）
  rpc RepliesStatus(RepliesReq) returns (RepliesStatusReply);

  // up 主查看评论列表
  rpc RepliesForUpSelection(RepliesForUpSelectionReq) returns (RepliesForUpSelectionResp);
  // up 主操作精选评论
  rpc UpSelectionDo(UpSelectionDoReq) returns (UpSelectionDoResp);

  // up主置顶某一条根评论
  rpc UpTopReply(UpTopReplyReq) returns (.google.protobuf.Empty);
  // up主取消置顶某一条根评论 (管理员置顶的评论无法通过此接口取消置顶)
  rpc UpUnTopReply(UpUnTopReplyReq) returns (.google.protobuf.Empty);
  // 查询评论区的置顶评论信息
  rpc TopReplyInfo(TopReplyInfoReq) returns (TopReplyInfoReply);

  // 评论区发起活动，活动专用接口
  rpc UpdateActivityState(UpdateActivityStateReq) returns (.google.protobuf.Empty);
  // 动态feed流下展示评论使用
  rpc DynamicFeed(DynamicFeedReq) returns (DynamicFeedReply);

  // 评论详情 (不会递归获取子评论)
  rpc RepliesInfo(RepliesInfoReq) returns (RepliesInfoReply);
  // 评论管控操作
  rpc RepliesDo(RepliesDoReq) returns (.google.protobuf.Empty);
  // 评论搜索
  rpc ReplyAdminSearch(ReplyAdminSearchReq) returns (ReplyAdminSearchResp);

  // 评论区信息查询
  rpc SubjectInfo(SubjectInfoReq) returns (SubjectInfoReply);
  // 评论区信息查询（批量）
  rpc SubjectsInfo(SubjectsInfoReq) returns (SubjectsInfoReply);
  // 评论区管控信息查询
  rpc SubjectsManagementInfo(SubjectsManagementInfoReq) returns (SubjectsManagementInfoResp);

  // deprecated 新业务接入不可再使用，接入InsertSubject
  rpc RegistSubject(RegistSubjectReq) returns (.google.protobuf.Empty) {
    option deprecated = true;
  };
  // 注册评论区, INSERT行为
  rpc InsertSubject(InsertSubjectReq) returns (.google.protobuf.Empty);
  // 打开或者关闭评论区, UPDATE行为
  rpc UpdateSubjectState(UpdateSubjectStateReq) returns (.google.protobuf.Empty);
  // 更新评论区属性（UP精选、UP关闭等）
  rpc UpdateSubjectAttr(UpdateSubjectAttrReq) returns (.google.protobuf.Empty);
  // 更新评论区 meta
  rpc UpdateSubjectMeta(UpdateSubjectMetaReq) returns (.google.protobuf.Empty) {
    option deprecated = true;
  };

  // 用户发过的评论的互动计数统计
  rpc UserReplyStats(UserReplyStatsReq) returns (UserReplyStatsReply) {
    option deprecated = true;
  };

  // 根据oid，type查询业务meta信息
  rpc BusinessMeta(BusinessMetaReq) returns (BusinessMetaReply);
  // 根据oid，type查询业务meta信息（批量）
  rpc BusinessMetas(BusinessMetasReq) returns (BusinessMetasReply);

  // 添加运营位配置
  rpc AddOperation(AddOperationReq) returns (AddOperationResp);
  // 下线运营位配置
  rpc OfflineOperation(OfflineOperationReq) returns (OfflineOperationResp);

  // 优质评论推荐
  rpc ReplyRecommend(ReplyRecommendReq) returns (ReplyRecommendResp) {
    option deprecated = true;
  };
  // 评论推荐元信息
  rpc ReplyRecommendMeta(ReplyRecommendMetaReq) returns (ReplyRecommendMetaResp) {
    option deprecated = true;
  };
  // 优质评论搜索
  rpc ReplySearch(ReplySearchReq) returns (ReplySearchResp) {
    option deprecated = true;
  };
  // 评论搜索热词列表
  rpc ReplySearchHotWords(ReplySearchHotWordsReq) returns (ReplySearchHotWordsResp) {
    option deprecated = true;
  };

  // 稿件小黄条
  rpc ArchiveHonor(ArchiveHonorReq) returns (ArchiveHonorResp);

  // 根据评论 id 获取绑定的第三方业务 id
  rpc GetBizIdByRpid(GetBizIdByRpidReq) returns (GetBizIdByRpidResp);
  // 根据第三方业务 id 获取关联的评论 id
  rpc GetRpidByBizId(GetRpidByBizIdReq) returns (GetRpidByBizIdResp);

  // 通用版AddReply
  rpc GeneralUserAddReply(GeneralUserAddReplyReq) returns (GeneralUserAddReplyResp) {
    option deprecated = true;
  };

  // 通用版用户删评接口（含发布者、UP主、其他有权限的用户等）
  rpc GeneralUserDeleteReply(GeneralUserDeleteReplyReq) returns (GeneralUserDeleteReplyResp) {
    option deprecated = true;
  };

  // 更新评论区mid
  rpc UpdateSubjectMid(UpdateSubjectMidReq) returns (.google.protobuf.Empty);

  // 根评论id列表查询
  rpc RootReplyList(RootReplyListReq) returns (RootReplyListReply) {
    option deprecated = true;
  };

  // 根评论点赞序 ID 列表 (仅包含公开可见的评论, 默认降序)
  rpc RootReplyListOrderByLike(RootReplyListOrderByLikeReq) returns (RootReplyListOrderByLikeResp);
  // 批量获取外露的子评论
  rpc BatchGetExposedChildReplyList(BatchGetExposedChildReplyListReq) returns (BatchGetExposedChildReplyListResp);

  // 添加评论区投票组件配置
  rpc AddVoteCard(AddVoteCardReq) returns (AddVoteCardResp);
  // 下线评论区投票组件配置
  rpc OfflineVoteCard(OfflineVoteCardReq) returns (OfflineVoteCardResp);

  // 添加互动组件
  rpc AddInteractComponent(AddInteractComponentReq) returns (AddInteractComponentResp);
  // 删除互动组件
  rpc DelInteractComponent(DelInteractComponentReq) returns (DelInteractComponentResp);

  // Story 热门评论
  rpc StoryHotReply(StoryHotReplyReq) returns (StoryHotReplyResp);

  // 查询用户在某个评论区下的举报
  rpc GetUserReportedRpidsBySubject(GetUserReportedRpidsBySubjectReq) returns (GetUserReportedRpidsBySubjectResp);

  // 获取有标记评论的点赞序列表的top k
  rpc GetTopKFlagReplyOrderByLike(GetTopKFlagReplyOrderByLikeReq) returns (GetTopKFlagReplyOrderByLikeResp);

  // 其他屏蔽词同步到评论
  rpc SyncUpFilterToReply(SyncUpFilterToReplyReq) returns (SyncUpFilterToReplyResp);
}

enum SubjectState {
  // 打开 (e.g., 稿件过审后打开评论区)
  NORMAL = 0;
  // 关闭 (e.g., 稿件未过审前先关闭评论区)
  FORBID = 1;
  // 管理员操作的关闭 (仅在 manager 后台可以重新打开)
  ADMIN_FORBID = 2;
}

message DynamicFeedReq {
  // oid,type  oid和type用逗号分隔拼成一个字符串, oid在前，type在后
  repeated string ids = 1;
  // 访问用户的mid，用以后续关系链使用，非必传
  int64 mid = 2;
  // 访问用户的buvid，用于ABTest，非必传
  string buvid = 3;
  enum From {
    UNKNOWN = 0;
    ADMIN   = 1;
    TOPIC   = 2;  // 来源：话题
    TAB3    = 3;  // 来源：tab3
  }
  From from = 4;
  // 需要展示对应话题发起人的评论的评论区 id（格式和 ids 中一致，且为 ids 的子集）
  repeated string ids_need_topic_creator_reply = 5;
  // 请求方可指定外露条数，但不得大于From限定的条数；该值将覆盖评论服务端根据From分配的默认条数
  int64 count = 6;
  // 相簿/专栏的评论区 id -> opus id
  map<string, int64> opus_id_mapping = 7;
}

message Emote {
  // 表情的大小 1-小 2-大
  int64 size = 1;
  // 表情资源的链接
  string url        = 2;
  string jump_url   = 3;
  string jump_title = 4;
  int64 id          = 5;
  int64 package_id  = 6;
  string gif_url    = 7;
  // 表情在评论中的文本
  string text = 8;
}

message DynamicFeedReply {
  message meta {
    message reply {
      int64 mid      = 1;
      string content = 2;
      // 评论中的[xxx] key是对应需要被渲染的表情文本，value是对应的表情的信息
      map<string, Emote> emotes = 3;
      int64 rpid                = 4;
      enum Label {
        Normal  = 0;
        Godlike = 1;  // 神评
      }
      Label label = 5;
      // 发布时间
      int64 ctime = 17 [(gogoproto.casttype) = "go-common/library/time.Time"];
    }
    repeated reply replies = 1;
    // 评论区的评论计数
    int64 count = 2;
    // 是否禁止评论
    bool no_comment = 3;
  }
  // 这里的key是oid,type拼成的字符串
  map<string, meta> feed = 1;
}

message RpIDs {
  repeated int64 ids = 1 [(gogoproto.moretags) = 'validate:"required"'];
}

message RepliesInfoReq {
  // deprecated
  map<int64, RpIDs> Rps = 1 [deprecated = true];
  // 最多 50 个
  repeated int64 rpids  = 2;
}

message RepliesInfoReply {
  map<int64, ReplyInfo> replies = 1;
}

message ReplyInfo {
  // reply id
  int64 ID = 1;
  // 评论区id
  int64 oid = 2;
  // 评论区类型，https://info.bilibili.co/pages/viewpage.action?pageId=77660914
  int64 type = 3;
  // 评论发布者mid
  int64 mid = 4;
  // 根评论评论id（没有根评论就是0）
  int64 root = 5;
  // 父评论评论id（没有父评论就是0）
  int64 parent = 6;
  // 所属对话id（对话是回复的回复及以下的层级，不属于对话就是0）
  int64 dialog = 7;
  // 子评论总数（包含不可见的）
  int64 count = 8;
  // 子评论可见总数（不包含不可见的）
  int64 real_count = 9;
  // 楼层号
  int64 floor = 10;
  // deprecated: 状态（DB），请使用 visibility
  ReplyState state = 11 [deprecated = true];
  // deprecated: 属性位（DB），请使用 attributes
  int64 attr = 12 [deprecated = true];
  // 点赞数
  int64 like = 13;
  // 点踩数
  int64 hate = 14;
  // 评论内容
  Content content = 15;
  // 子评论列表
  repeated ReplyInfo replies = 16;
  // 发布时间
  int64 ctime = 17 [(gogoproto.casttype) = "go-common/library/time.Time"];
  // 最近修改时间（DB）
  int64 mtime = 18 [(gogoproto.casttype) = "go-common/library/time.Time"];
  // 举报数
  int64 report_count = 19;
  // 评论状态（外部使用）
  RepliesStatusReply.ReplyStatus status = 20;
  // 评论属性（外部使用）
  message ReplyAttributes {
    // 是否置顶
    bool is_top = 1;
    // 是否为商业评论
    bool is_commercial = 2;
    // 是否为充电评论
    bool is_charged = 3;
    // 是否为彩蛋评论
    bool is_easter_egg = 4;
  }
  ReplyAttributes attributes = 21;
  // C端可见性
  ReplyVisibility visibility = 22;
}

// C端可见性
enum ReplyVisibility {
  // 默认值
  ReplyVisibility_UNSPECIFIED = 0;
  // 公开可见
  ReplyVisibility_PUBLIC_VISIBLE = 1;
  // 仅自己可见
  ReplyVisibility_SELF_VISIBLE = 2;
  // 所有人都不可见
  ReplyVisibility_INVISIBLE = 3;
}

message Content {
  // 评论id
  int64 rp_id = 1;
  // 评论文本
  string message = 2;
  // 评论@的mid列表
  repeated int64 ats = 3;
  // ip地址的int32表示（不支持ipv6）
  int64 ip = 4;
  // deprecated: 评论发布的plat
  int32 plat = 5;
  // deprecated: 评论发布的device
  string device = 6;
  // deprecated: 评论发布的version
  string version = 7;
  // 评论关联的话题
  repeated string topics = 8;
  // 评论发布时间
  int64 ctime = 9 [(gogoproto.casttype) = "go-common/library/time.Time"];
  // 评论内容更新时间（DB）
  int64 mtime = 10 [(gogoproto.casttype) = "go-common/library/time.Time"];
  // 评论文本中的跳转链接（蓝链）
  map<string, JumpMeta> jump_url = 11;
  // 评论ip字符串（支持ipv4+ipv6）
  string ip_str = 12;
  // 被 at 的昵称到 mid 的映射
  map<string, int64> at_name_to_mid = 13;
  // 评论带的图
  repeated Picture pictures = 14;
}

enum ReplyState {
  option deprecated = true;

  ReplyStateNormal        = 0;   // 正常状态
  ReplyStateHidden        = 1;   // 被up主隐藏
  ReplyStateFiltered      = 2;   // 敏感词过滤 (废弃 移动到attr3)
  ReplyStateAdminDel      = 3;   // 管理员删除
  ReplyStateUserDel       = 4;   // 用户删除
  ReplyStateMonitor       = 5;   // 先发后审
  ReplyStateGarbage       = 6;   // 大数据过滤 (废弃 移动到attr2)
  ReplyStateTop           = 7;   // 管理员置顶 (废弃 移动到attr1)
  ReplyStateUpDel         = 8;   // up主删除
  ReplyStateBlacklist     = 9;   // up黑名单屏蔽 (显示状态不进db)
  ReplyStateAssistDel     = 10;  // 协管删除
  ReplyStateAudit         = 11;  // 先审后发
  ReplyStateFolded        = 12;  // 被折叠
  ReplyStateFilterDel     = 13;  // 被filter-service命中条件删除
  ReplyStateInit          = 14;  // 初始状态(初始状态 不进db)
  ReplyUpSelectionPending = 15;  // up精选评论区 待精选的评论
  ReplyStateDevDel        = 16;  // 研发删除，压测用
  ReplyStateSelfvisible   = 17;  // 自见
  ReplyStateUpFiltered    = 18;  // up主屏蔽词过滤
}

message JumpMeta {
  enum IconPosition {
    Prefix = 0;
    Suffix = 1;
  }
  message Extra {
    enum GoodsShowType {
      Popup      = 0;  // 弹窗
      FullScreen = 1;  // 全屏
      HalfScreen = 2;  // 半屏
    }
    int64 goods_item_id = 1;
    // 预加载商品信息
    string goods_prefetched_cache = 2;
    // 商品展示样式
    GoodsShowType goods_show_type = 3;
    // 是否为划词搜索
    bool is_word_search = 4;
    // 是否由商业客户端控制跳转和上报逻辑
    int64 goods_cm_control = 5;
  }
  string title = 1;
  int64 state  = 2;
  // icon 的 url ，实际位置看 icon_position
  string prefix_icon      = 3;
  string app_url_schema   = 4;
  string app_name         = 5;
  string app_package_name = 6;
  string click_report     = 7;
  bool is_half_screen     = 8;
  string exposure_report  = 9;
  // 存各种非通用的字段，例如商品 id
  Extra extra = 10;
  // 下划线样式
  bool underline = 11;
  // 是否只匹配一次
  bool match_once = 12;
  // pc 端跳转链接（如果不为空，pc 端用这个链接跳转）
  string pc_url = 13;
  // icon 的位置
  IconPosition icon_position = 14;
}

message PageReq {
  enum Direction {
    // 下一页
    NEXT = 0;
    // 前一页
    PREV = 1;
  }
  int64 page_size = 1;
  // 翻页方向
  Direction direction = 2;
  // cursor （首次请求传空字符串）
  string cursor = 3;
}

message PageResp {
  // 下一次翻页需要提供的游标
  string cursor = 1;
  // 是否为第一页
  bool is_begin = 2;
  // 是否为最后一页
  bool is_end = 3;
}

message RepliesForUpSelectionReq {
  // 分页参数
  PageReq page_req = 1;
  // 业务
  int64 type = 2;
  // 主题id
  int64 oid = 3;
  // up mid
  int64 up_mid = 4;
}

message RepliesForUpSelectionResp {
  // 分页信息
  PageResp page_resp = 1;
  // 评论列表
  repeated ReplyForUpSelection replies = 2;
}

message ReplyForUpSelection {
  enum UpSelectionState {
    NORMAL  = 0;  // 已展示的评论
    PENDING = 1;  // 待精选的评论
  }
  // 评论 ID
  int64 id = 1;
  // 评论区 ID
  int64 oid = 2;
  // 评论区业务类型
  int64 type = 3;
  // 发评论人的 mid
  int64 mid = 4;
  // 根评论 id
  int64 root = 5;
  // 父评论 id
  int64 parent = 6;
  // 对话的 id
  int64 dialog = 7;
  // 楼层
  int64 floor = 8;
  // 评论内容
  Content content = 9;
  // up 主精选状态
  UpSelectionState up_selection_state = 10;
  // 发送时间
  int64 ctime = 11 [(gogoproto.casttype) = "go-common/library/time.Time"];
  // 更新时间
  int64 mtime = 12 [(gogoproto.casttype) = "go-common/library/time.Time"];
}

message UpSelectionDoReq {
  enum Action {
    // 展示
    PASS = 0;
    // 删除
    DELETE = 1;
  }
  int64 up_mid             = 1;
  Action action            = 2;
  repeated int64 reply_ids = 3;
}

message UpSelectionDoResp {
}

message SubjectsInfoReq {
  // 评论区类型，https://info.bilibili.co/pages/viewpage.action?pageId=77660914
  int64 type = 1 [(gogoproto.moretags) = 'validate:"required"'];
  // 评论区ID
  repeated int64 oids = 2 [(gogoproto.moretags) = 'validate:"required"'];
}

message SubjectsInfoReply {
  map<int64, SubjectInfoReply> subjects = 1;
}

message SubjectInfoReq {
  // 评论区类型，https://info.bilibili.co/pages/viewpage.action?pageId=77660914
  int64 type = 1 [(gogoproto.moretags) = 'validate:"required"'];
  // 评论区ID
  int64 oid = 2 [(gogoproto.moretags) = 'validate:"required"'];
}

message SubjectInfoReply {
  // 评论区类型，https://info.bilibili.co/pages/viewpage.action?pageId=77660914
  int64 type = 1;
  // 评论区id
  int64 oid = 2;
  // up主mid（评论区owner）
  int64 mid = 3;
  // 根评论总数, 包括不可见的
  int64 count = 4;
  // deprecated: 根评论总数, 包括不可见的
  int64 rcount = 5;
  // 评论加回复总数, 仅包括可见态
  int64 acount = 6;
  // deprecated: 评论区状态. 如果是判断开关, 建议使用 closed
  SubjectState state = 7 [deprecated = true];
  // deprecated: 请使用 attributes
  int64 attr = 8 [deprecated = true];
  // deprecated
  string meta = 9 [deprecated = true];
  // 注册时间
  int64 ctime = 10 [(gogoproto.casttype) = "go-common/library/time.Time"];
  // 最近更新时间
  int64 mtime = 11 [(gogoproto.casttype) = "go-common/library/time.Time"];
  // deprecated
  int64 mcount = 12 [deprecated = true];
  // 是否已关闭, 包括内部管控或者UP主操作
  bool closed = 13;
  // 外部使用的属性
  SubjectAttributes attributes = 14;
}

// 评论区属性 (外部使用)
message SubjectAttributes {
  // 是否被up主关闭
  bool up_closed = 1;
  // 是否开启了up主精选
  bool up_selection = 2;
  // 是否开启了审核精选
  bool admin_selection = 3;
  // 是否开启了只读 (允许查看，但是不允许发评论)
  bool read_only = 4;
}

message SubjectsManagementInfoReq {
  // 业务 type
  int64 type = 1 [(gogoproto.moretags) = 'validate:"required"'];
  // 评论区 id 列表
  repeated int64 oids = 2 [(gogoproto.moretags) = 'validate:"required"'];
}

message SubjectsManagementInfoResp {
  map<int64, SubjectManagementInfo> subjects = 1;
}

message SubjectManagementInfo {
  message UpSelection {
    // 是否开启精选
    bool is_on = 1;
    // 待精选评论数
    int64 pending_count = 2;
  }
  // 业务 type
  int64 type = 1;
  // 评论区 id
  int64 oid = 2;
  // 所有评论和回复总数，删除的不计在内
  int64 show_count = 3;
  // up 主精选相关
  UpSelection up_selection = 4;
}

message RegistSubjectReq {
  // 业务type
  int64 type = 1 [(gogoproto.moretags) = 'validate:"required"'];
  // 评论区ID
  int64 oid = 2 [(gogoproto.moretags) = 'validate:"required"'];
  // 评论区的up主的mid， 用于对评论区的评论管理，非必须
  int64 mid = 3;
  // 评论区的打开与关闭状态0-正常 1-关闭
  int64 state = 4;
  // appkey
  string appkey = 5 [deprecated = true];
  // 评论区的一些额外属性
  SubjectExtra extra = 6;
}

message SubjectExtra {
  // true=开启up主精选评论区
  bool up_selection = 1;
  // up发起活动的ID
  int64 activity_id = 2 [deprecated = true];
  // true=up关闭评论区
  bool up_close = 3;
  // 备注
  string remark = 4;
}

// deprecated: 请使用 SubjectAttributes
enum SubjectAttrBit {
  option deprecated = true;

  TopAdmin           = 0;   // 评论区有管理员置顶的评论
  TopUpper           = 1;   // 评论区有UP置顶的评论
  Monitor            = 2;   // 评论区处于先发后审状态
  Config             = 3;   // 评论区有针对是否展示管理员删除日志的配置
  Audit              = 4;   // 评论区处于先审后发状态
  Frozen             = 5;   // 评论区处于冻结状态(此状态不允许调用接口修改评论区状态)
  TopVote            = 6;   // 评论区有置顶的投票评论
  Folded             = 7;   // 评论区有折叠根评论
  MoniExAudit        = 10;  // 审核加的监控, 用于监控列表搜索
  MoniExOperation    = 11;  // 运营加的监控, 用于监控列表搜索
  MoniExWhiteList    = 12;  // 评论区有管理员添加的黑名单或者白名单, 用于监控列表搜索
  MoniExBlackList    = 13;  // 评论区有管理员添加的黑名单或者白名单, 用于监控列表搜索
  MoniExSensitive    = 14;  // 评论区有管理员添加的敏感词, 用于监控列表搜索
  ReadOnly           = 15;  // 评论区只读
  ShowDelLog         = 16;  // 展示删除日志(客户端已删除删除日志入口)
  Lottery            = 17;  // 有抽奖评论
  MoniExOGVAudit     = 18;  // OGV审核加的监控
  MoniExOGVOperation = 19;  // OGV运营加的监控
  AuditPlus          = 20;  // 升级版先审后发, 用户自己也不可见，但是要提示用户发的评论要被审核后才能可见
  UpSelection        = 21;  // up主精选评论区
}

message InsertSubjectReq {
  // 业务type
  int64 type = 1 [(gogoproto.moretags) = 'validate:"required"'];
  // 评论区ID
  int64 oid = 2 [(gogoproto.moretags) = 'validate:"required"'];
  // 评论区的up主的mid， 用于对评论区的评论管理，非必须,
  // 如果不传，则评论区没有up主可以删除评论,置顶评论等操作
  int64 mid = 3;
  // 评论区的打开与关闭状态0-正常 1-关闭
  int64 state = 4;
  // 评论区的一些额外属性
  SubjectExtra extra = 5;
}

message UpdateSubjectStateReq {
  // 业务type
  int64 type = 1 [(gogoproto.moretags) = 'validate:"required"'];
  // 评论区ID
  int64 oid = 2 [(gogoproto.moretags) = 'validate:"required"'];
  // 评论区的状态0-打开 1-关闭
  SubjectState state = 3;
  // deprecated (之前用appkey来做业务级别的鉴权)
  string appkey = 4;
  // 备注
  string remark = 5;
}

message UpdateActivityStateReq {
  int64 activity_id = 1 [(gogoproto.moretags) = 'validate:"required"'];
  int64 state       = 2;
}

message SubjectInteractionStatusReq {
  // 业务
  int64 type = 1;
  // 主题id
  int64 oid = 2;
  // 用户mid（用于查看该用户是否有权限修改，默认0表示"超级管理员"权限）
  int64 mid = 3;
}

message SubjectInteractionStatusReply {
  message detail {
    // 当前登录用户是否可以修改该状态
    bool can_modify = 1;
    // up关闭评论区功能 1未关闭/允许关闭 0已关闭/允许开放
    // 精选评论区功能 1已开启/允许停止评论精选 0未开启/允许评论精选
    // 只读评论区功能 1开启只读/允许关闭只读 0未开启只读/允许开启只读
    // 充电评论功能 1开启充电/允许关闭充电 0未开启充电/允许开启充电
    int64 status = 2;
  }
  // 精选评论区功能
  detail up_selection = 1;
  // up关闭评论区功能
  detail up_close = 2;
  // 是否受控
  bool is_controlled = 3;
  // 是否已关闭
  bool is_forbidden = 4;
  // 只读评论区功能
  detail read_only = 5;
  // up开关充电评论功能
  detail up_charged = 6;
}

message SubjectsInteractionStatusReq {
  repeated SubjectInteractionStatusReq subjects = 1;
}
message SubjectsInteractionStatusReply {
  repeated SubjectInteractionStatusReply subjects = 1;
}

message ReplyListPrefaceReq {
  // 访问人mid
  int64 mid    = 1;
  int64 oid    = 2;
  int64 type   = 3;
  string buvid = 4;
}

message ReplyListPrefaceReply {
  // 评论入口的图片 (badge_type = 1|2)
  string badge_url  = 1;
  // 评论入口的文案 (badge_type = 3|4)
  string badge_text = 2;
  // 评论入口的动效类型 1:翻转动效 2:滚动动效 3:轮播文字 4:气泡文字
  int64 badge_type = 3;
  // 评论区的各种状态
  SubjectInteractionStatusReply subject_status = 4;
}

message ReplyListsPrefaceReq {
  repeated ReplyListPrefaceReq subjects = 1;
}

message ReplyListsPrefaceReply {
  // key是oid,type拼成的字符串
  map<string, ReplyListPrefaceReply> prefaces = 1;
}

message UpdateSubjectAttrReq {
  // 业务type
  int64 type = 1 [(gogoproto.moretags) = 'validate:"required"'];
  // 评论区ID
  int64 oid = 2 [(gogoproto.moretags) = 'validate:"required"'];
  // 评论区的属性操作枚举值
  // 3: 开启评论区只读 (允许查看，但是不允许发评论)
  // 4: UP主关闭评论区
  // 5: UP主开启精选
  map<int64, bool> attrs = 3 [(gogoproto.moretags) = 'validate:"required"'];
  // 备注
  string remark = 4;
}

message UpdateSubjectMetaReq {
  message TopicMeta {
    // 话题发起人 mid
    int64 creator_mid = 1;
  }
  // 业务type
  int64 type = 1 [(gogoproto.moretags) = 'validate:"required"'];
  // 评论区ID
  int64 oid = 2 [(gogoproto.moretags) = 'validate:"required"'];
  // 话题相关
  TopicMeta topic_meta = 3;
}

message UserReplyStatsReq {
  int64 mid           = 1;
  repeated int64 type = 2;
}

message UserReplyStatsReply {
  int64 like = 1;
}

message BusinessMetaReq {
  // 业务
  int64 type = 1;
  // 主题id
  int64 oid = 2;
}

message BusinessMetaReply {
  string title = 1;
  string link  = 2;
  // 稿件的分区ID
  int64 type_id = 3;
}

message BusinessMetasReq {
  message oids {
    repeated int64 oid = 1;
  }
  // type, oid
  map<int64, oids> subjects = 1;
}

message BusinessMetasReply {
  map<int64, BusinessMetaReply> metas = 1;
}

message RepliesDoReq {
  enum Action {
    Recover     = 0; // 恢复正常
    Delete      = 1; // 删除
    Selfvisible = 2; // 自见
    CancelSelfvisible = 3; // 取消自见
  }
  enum Biz {
    Unknown = 0;
    Ice     = 1;  // 薄冰
    Cm      = 2;  // 商业
    Workflow = 3;  // 申诉
    Govern   = 4;  // 治理平台
    Aegis   = 5;  // 审核平台
  }
  // 待操作的评论rpid列表
  repeated int64 rpids = 1;
  // 操作类型
  Action action = 2;
  // 操作人 id
  int64 operator_mid = 3;
  // 备注
  string remark = 4;
  // 操作人 name
  string operator_name = 5;
  // 业务
  Biz biz = 6;
}

message ReplyAdminSearchReq {
  // 业务 type (0 代表不限制)
  int64 type = 1;
  // 业务 id (0 代表不限制)
  int64 oid  = 2;
  // 发评人 mid (0 代表不限制)
  int64 mid  = 3;
  // 评论 C 端可见性 (空代表不限制)
  repeated ReplyVisibility visibility_filters = 4;
  // 评论发布时间范围 - 开始时间 (默认为 ctime_end - 31d)
  int64 ctime_begin = 5 [(gogoproto.casttype) = "go-common/library/time.Time"];
  // 评论发布时间范围 - 结束时间 (默认当前时间) (如果 ctime_end - ctime_begin 超过 31d, 则仅返回最近 31d 的数据)
  int64 ctime_end = 6 [(gogoproto.casttype) = "go-common/library/time.Time"];
  // 分页 (0 <= offset <= 5000, 0 < limit <= 50)
  OffsetPagination pagination = 7;
}

message ReplyAdminSearchResp {
  // 评论 ID 列表
  repeated int64 ids = 1;
  // 总数
  int64 total = 2;
}

// 运营位类型
enum OperationType {
  UNKNOWN = 0;
  // 笔记
  NOTE = 1;
  // 话题
  TOPIC = 2;
  // deprecated: 公告
  NOTICE = 3 [deprecated = true];
}

message AddOperationReq {
  // 业务 type (目前只支持 1 (稿件)）
  int64 type = 1;
  // 评论区 ID list
  repeated int64 oids = 2;
  // 运营位类型
  OperationType operation_type = 3;
  // 跳转链接
  string link = 4;
  // 主标题
  string title = 5;
  // 副标题
  string subtitle = 6;
  // deprecated
  string icon = 7 [deprecated = true];
  // 生效时间
  int64 start_time = 8 [(gogoproto.casttype) = "go-common/library/time.Time"];
  // 结束时间
  int64 end_time = 9 [(gogoproto.casttype) = "go-common/library/time.Time"];
}

message AddOperationResp {
  int64 id = 1;
}

message OfflineOperationReq {
  int64 id = 1;
}

message OfflineOperationResp {
}

message ReplyRecommendReq {
  // 必剪："bcut"
  string business = 1;
  // 发起搜索的用户mid
  int64 mid = 2;
  // 页码，从1开始
  int64 pn = 3;
  // 页面大小，不能大于50
  int64 ps = 4;
  // 分类，为空时则返回结果包含任意分类
  string category = 5;
  // 稿件标签
  string archive_tag = 6;
}

message ReplyRecommendResp {
  repeated ReplyInfo replies = 1;
  Page page                  = 2;
}

message ReplyRecommendMetaReq {
  // 必剪："bcut"
  string business = 1;
  // 发起搜索的用户mid
  int64 mid = 2;
}

message ReplyRecommendMetaResp {
  repeated string categories   = 1;
  repeated string archive_tags = 2;
}

message ReplySearchReq {
  // 必剪："bcut"
  string business = 1;
  // 发起搜索的用户mid
  int64 mid = 2;
  // 页码，从1开始
  int64 pn = 3;
  // 页面大小，不能大于50
  int64 ps = 4;
  // 搜索关键词
  string keyword = 5;
}

message ReplySearchResp {
  repeated ReplyInfo replies = 1;
  Page page                  = 2;
}

message ReplySearchHotWordsReq {
  int64 mid = 1;
}

message ReplySearchHotWordsResp {
  message HotWord {
    string word = 1;
  }
  repeated HotWord words = 1;
}

message Page {
  int64 num   = 1;
  int64 size  = 2;
  int64 total = 3;
}

message RepliesReq {
  repeated int64 rpids = 1;
}

message RepliesStatusReply {
  enum ReplyStatus {
    // 公开可见
    PublicVisible = 0;
    // 仅自己可见（不包括进审态）
    SelfVisible = 1;
    // 已删除
    Deleted = 2;
    // 待处理（包括待审核、待UP精选等）
    Pending = 3;
  }
  map<int64, ReplyStatus> status = 1;
}

message UpUnTopReplyReq {
  // 业务
  int64 type = 1 [(gogoproto.moretags) = 'validate:"required"'];
  // 主题id
  int64 oid = 2 [(gogoproto.moretags) = 'validate:"required"'];
  // 评论ID
  int64 rp_id = 3 [(gogoproto.moretags) = 'validate:"required"'];
}

message UpTopReplyReq {
  // 业务
  int64 type = 1 [(gogoproto.moretags) = 'validate:"required"'];
  // 主题id
  int64 oid = 2 [(gogoproto.moretags) = 'validate:"required"'];
  // 评论ID
  int64 rp_id = 3 [(gogoproto.moretags) = 'validate:"required"'];
}

message TopReplyInfoReq {
  // 业务
  int64 type = 1 [(gogoproto.moretags) = 'validate:"required"'];
  // 主题id
  int64 oid = 2 [(gogoproto.moretags) = 'validate:"required"'];
}

message TopReplyInfoReply {
  ReplyInfo admin = 1 [deprecated = true];
  ReplyInfo up    = 2 [deprecated = true];
  ReplyInfo vote  = 3 [deprecated = true];
  // 置顶评论列表
  repeated ReplyInfo top_replies = 4;
}

message ArchiveHonorReq {
  enum Kind {
    UNKNOWN = 0;
    // 妙评
    SUPERB_REPLY = 1;
  }
  Kind kind = 1;
  int64 aid = 2;
}

message ArchiveHonor {
  // 图标
  string icon = 1;
  // 夜间图标
  string icon_night = 2;
  // 文案
  string text = 3;
  // 附属文案（如收录）
  string text_extra = 4;
  // 文案颜色
  string text_color = 5;
  // 文案夜间颜色
  string text_color_night = 6;
  // 背景色
  string bg_color = 7;
  // 夜间背景色
  string bg_color_night = 8;
  // 跳转链接
  string url = 9;
  // 跳转链接文案
  string url_text = 10;
}

message ArchiveHonorResp {
  ArchiveHonor archive_honor = 1;
}

enum BizIdKind {
  BizIdKind_UNKNOWN          = 0;
  BizIdKind_DYNAMIC_ID       = 1;
  BizIdKind_CHARGED_ORDER_ID = 2;
  BizIdKind_GRADE_ID         = 3;
}

message GetBizIdByRpidReq {
  int64 rpid = 1;
  // 第三方 id 的类型
  BizIdKind biz_id_kind = 2;
}

message GetBizIdByRpidResp {
  string biz_id = 1;
}

message GetRpidByBizIdReq {
  // 第三方 id 的类型
  BizIdKind biz_id_kind = 1;
  string biz_id         = 2;
}

message GetRpidByBizIdResp {
  int64 rpid = 1;
}

message SubjectID {
  // 评论区业务id
  int64 oid = 1 [(gogoproto.moretags) = 'validate:"required,min=1"'];
  // 业务
  int64 type = 2 [(gogoproto.moretags) = 'validate:"required,min=1"'];
}

enum Mode {
  DEFAULT     = 0;
  UNSPECIFIED = 1;
  // 在主评论列表里 2-时间
  MAIN_LIST_TIME = 2;
  // 在主评论列表里 3-热度
  MAIN_LIST_HOT = 3;
}

message Picture {
  string img_src    = 1;
  double img_width  = 2;
  double img_height = 3;
  // 大小KB
  double img_size = 4;
}
message GeneralUserID {
  int64 mid    = 1;
  string buvid = 2;
}

message Device {
  string device = 1; // app: 运行设备
  string mobi_app = 2; // app: 包类型
  string platform = 3; // app: 设备类型
  string buvid = 4; // app: buvid
  string buvid3 = 5; // cookie: buvid
  int64  build = 6; // app: 构建号
  string user_agent = 7; // user-agent
  int64  app_id = 8; // 产品编号，跟数据平台一致
}

message GeneralClientCommon {
  string ip = 1;
  Device device = 2;
}

message GeneralInteractCommon {
  enum Scene {
    Unknown = 0;
    Main    = 1;
    Detail  = 2;
    Dialog  = 3;
    Msg     = 4;
    General = 5;
  }
  // 发生互动行为的所在页面场景
  Scene scene = 1;
  // 发生互动行为的所在页面的排序模式
  Mode ordering_mode = 2;
  // 业务场景 story\ugc\pgc\im\biligram
  string biz_scene = 3;
}
message Member {
  // 基础信息展示(头像，昵称，等级，认证标记, 性别)
  message Basic {
    // mid
    int64 mid = 1;
    // 昵称
    string name = 2;
    // 性别
    string sex = 3;
    // 用户头像
    string face = 4;
    // 用户的等级
    int64 level = 5;
  }
  Basic basic = 1;
}
// 控制评论展示的字段
message ReplyControl {
  // 访问人对该条评论的行为 0-未点赞也未点踩 1-点过赞 2-点过踩
  int64 action = 1;
  // 发布时间文案，如"30秒之前"
  string time_desc = 2;
  // 该评论所属的特殊业务场景，如"note,insertion"，多场景使用英文逗号分割，主要用于客户端数据上报
  string biz_scene = 3;
  // 发评ip属地
  string location = 4;
  // 评论状态
  RepliesStatusReply.ReplyStatus reply_status = 5;
}
message GeneralReplyInfoCard {
  // 主评论列表下根评论自带的N条子评论
  repeated GeneralReplyInfoCard replies = 1;
  // 评论ID
  int64 id = 2;
  // 评论区ID, 如视频就是AV号
  int64 oid = 3;
  // 评论区业务类型
  int64 type = 4;
  // 发评论人的mid
  int64 mid = 5;
  // 根评论ID
  int64 root = 6;
  // 父评论ID
  int64 parent = 7;
  // 评论对话ID
  int64 dialog = 8;
  // 评论点赞数
  int64 like = 9;
  // 评论发送时间
  int64 ctime = 10;
  // 子评论数
  int64 count = 11;
  // 评论文本以及评论文本中一些特殊符号需要客户端特殊展示的一些metadata
  GeneralReplyContent content = 12 [(gogoproto.nullable) = false];
  // 评论人相关的账号信息
  Member member = 13;
  // 评论的一些控制字段
  ReplyControl reply_control = 14 [(gogoproto.nullable) = false];
  // 序列号
  int64 seq_num = 15;
}

message GeneralReplyContent {
  // 客户端通过在线配置拉到的正则表达式，所能匹配到的超链接对应的metadata
  message Url {
    enum IconPosition {
      Prefix = 0;
      Suffix = 1;
    }
    message ReportExtra {
      string click_report    = 1;
      string exposure_report = 2;
    }
    message JumpMeta {
      string app_url_schema   = 1;
      string app_name         = 2;
      string app_package_name = 3;
      // pc 端跳转链接（如果不为空，pc 端用这个链接跳转）
      string pc_url = 4;
    }
    string title = 1;
    // icon 的 url ，实际位置看 icon_position
    string icon = 2;
    // icon 的位置
    IconPosition icon_position = 3;
    JumpMeta jump              = 4;
    ReportExtra report         = 5;
  }
  // 评论中的表情字符 形如:[xxx] 对应的metadata
  message Emote {
    // 表情的大小 1-小 2-大
    int64 size = 1;
    // 表情资源的链接
    string url        = 2;
    string jump_url   = 3;
    string jump_title = 4;
    int64 id          = 5;
    int64 package_id  = 6;
    string gif_url    = 7;
    // 表情在评论中的文本
    string text = 8;
  }
  // 评论中的 @
  message At {
    int64 mid   = 1;
    string name = 2;
  }
  // ContentType 枚举-文本类型
  enum ContentType {
    NONE  = 0;  // 占位
    TEXT  = 1;  // 文本，简单内容，直接取raw_text
    URL   = 2;  // 蓝链，对应Url
    EMOTE = 3;  // 表情，对应Emote
    AT    = 4;  // @，对应 At
  }

  message Item {
    // 原始文案
    string raw_text = 1;
    // 类型
    ContentType type = 2;
    message Content {
      oneof item {
        Url url     = 1;
        Emote emote = 2;
        At at       = 3;
      }
    }
    Content content = 3;
  }
  // 评论文本
  string raw_message        = 1;
  repeated Item contents    = 2;
  repeated Picture pictures = 3;
}
message GeneralUserAddReplyReq {
  GeneralClientCommon client_common     = 1;
  GeneralInteractCommon interact_common = 2;
  GeneralUserID user_id                 = 3 [(gogoproto.nullable) = false];
  SubjectID subject_id                  = 4 [(gogoproto.nullable) = false];
  string message                        = 5 [(gogoproto.moretags) = 'validate:"required"'];
  int64 root                            = 6;
  int64 parent                          = 7;
  map<string, int64> at_name_to_mid     = 8;
  repeated Picture pictures             = 9;
  bool is_easter_egg                    = 10;
}

message GeneralUserAddReplyResp {
  enum SuccessAction {
    // 假卡
    Display = 0;
    // 什么也不展示
    None = 1;
  }
  SuccessAction success_action = 1;
  string success_toast         = 2;
  string success_animation     = 3;
  bool change_nickname_prompt  = 4;
  GeneralReplyInfoCard reply   = 5;
}

message GeneralUserDeleteReplyReq {
  GeneralClientCommon client_common     = 1;
  GeneralInteractCommon interact_common = 2;
  // mid 必传
  GeneralUserID user_id = 3 [(gogoproto.nullable) = false];
  // 必传
  SubjectID subject_id = 4 [(gogoproto.nullable) = false];
  int64 rpid           = 5 [(gogoproto.moretags) = 'validate:"required,min=1"'];
}

message GeneralUserDeleteReplyResp {
}

message UpdateSubjectMidReq {
  // 主题id
  int64 oid = 1 [(gogoproto.moretags) = 'validate:"min=1"'];
  // 业务
  int64 type = 2 [(gogoproto.moretags) = 'validate:"min=1"'];
  // up mid
  int64 mid = 3 [(gogoproto.moretags) = 'validate:"min=1"'];
}

message RootReplyListReq {
  SubjectID subject_id = 1;
  Mode ordering_mode   = 2;
  PageReq page         = 3;
}

message RootReplyListReply {
  repeated int64 rpids = 1;
  PageResp page        = 2;
}

// offset-based pagination
message OffsetPagination {
  // 从 0 开始的偏移量
  int64 offset = 1;
  // 一页的数量
  int64 limit  = 2;
}

message RootReplyListOrderByLikeReq {
  SubjectID subject_id = 1;
  // 限制: 0 < limit <= 500, 0<=offset <= 2000
  OffsetPagination pagination = 2;
}

message RootReplyListOrderByLikeResp {
  repeated int64 ids = 1;
  bool has_more = 2;
}

message BatchGetExposedChildReplyListReq {
  // 评论区 id
  SubjectID subject_id = 1;
  // 根评论 id 列表, 最多 30 个
  repeated int64 root_ids = 2;
}

message BatchGetExposedChildReplyListResp {
  // key 为 root_id
  map<int64, ExposedChildReplyList> lists = 1;
}

message ExposedChildReplyList {
  int64 root_id = 1;
  // 外露的子评论 id 列表
  repeated int64 child_ids = 2;
}

message AddVoteCardReq {
  // 评论区 id
  SubjectID subject_id = 1;
}

message AddVoteCardResp {
  // 给互动中台用的 id
  int32 interact_biz_code = 1;
  int64 interact_biz_id   = 2;
}

message OfflineVoteCardReq {
  // 评论区 id
  SubjectID subject_id = 1;
}

message OfflineVoteCardResp {
}

enum InteractComponentKind {
  InteractComponentKind_UNKNOWN = 0;
  // 投票
  InteractComponentKind_VOTE = 1;
  // 互动中台S13打分
  InteractComponentKind_GRADE_S13 = 2;
  // 赛事通用打分
  InteractComponentKind_GRADE_ESPORT = 3;
}

message AddInteractComponentReq {
  // 评论区 id
  SubjectID subject_id = 1;
  // 类型
  InteractComponentKind kind = 2;
}

message AddInteractComponentResp {
  // biz_code=2 稿件
  int32 interact_biz_code = 1;
  // biz_id = 稿件aid
  int64 interact_biz_id   = 2;
}

message DelInteractComponentReq {
  // 评论区 id
  SubjectID subject_id = 1;
  // 类型
  InteractComponentKind kind = 2;
}

message DelInteractComponentResp {
}


message StoryHotReplyReq {
  // 评论区 id
  SubjectID subject_id = 1;
  // 用户mid
  int64 mid = 2;
  // 设备客户端信息
  GeneralClientCommon client_common = 3;
  // spmid
  string spmid = 4;
}

message StoryHotReplyResp {
  message Reply {
    int64 mid      = 1;
    string message = 2;
    int64 rpid     = 3;
    int64 like     = 4;
    int64 count    = 5;
    map<string, Emote> emotes = 6;
  }
  // 算法直接推荐的story外露热评
  Reply reply = 1;
  // 评论区是否关闭
  bool closed = 2;
  // 所有评论和回复总数，删除的不计在内
  int64 show_count = 3;
  // 根据算法的首页排序结果，选出点赞数>100的第一个评论作为story外露热评
  Reply reply_peak_like = 4;
  // 根据算法的首页排序结果，选出回复数>10的第一个评论作为story外露热评
  Reply reply_peak_count = 5;
}

message GetUserReportedRpidsBySubjectReq {
  // 评论区 id
  SubjectID subject_id = 1;
  // 举报人 mid
  int64 reporter_mid = 2;
}

message GetUserReportedRpidsBySubjectResp {
  // 被 reporter_mid 举报过的 rpid
  repeated int64 rpids = 1;
}

message GetTopKFlagReplyOrderByLikeReq {
  enum FlagType {
    Unknown = 0;
    ESport = 1;
  }
  // 评论区 id
  SubjectID subject_id = 1;
  // flag 类型
  FlagType flag_type = 2;
  // 标记id
  int64 flag_id = 3;
  // 取多少条
  int64 top_k = 4;
}

message GetTopKFlagReplyOrderByLikeResp {
  repeated int64 ids = 1;
}



message SyncUpFilterToReplyReq {
  // 业务类型 1-稿件
  int64 scope_type =1;
  // 业务id
  // 对应 scope_type 下的业务对象的 id，例如：scope_type = 1 时，scope_id = avid
  // 特殊值 0 代表对该 scope_type 下所有对象生效。例如：scope_type = 1, scope_id = 0 时， 代表对所有稿件生效
  int64 scope_id = 2;
  // 屏蔽词内容
  string content = 3;
  int64 mid=4;
}

message SyncUpFilterToReplyResp {
}

enum ReportReason {
  REPORT_REASON_OTHER = 0 [deprecated = true]; // 其他
  REPORT_REASON_AD = 1;  // 垃圾广告
  REPORT_REASON_PORN = 2;  // 色情信息
  REPORT_REASON_MEANINGLESS = 3;  // 刷屏
  REPORT_REASON_PROVOKE = 4;  // 引战
  REPORT_REASON_SPOILER = 5;  // 剧透
  REPORT_REASON_POLITIC = 6 [deprecated = true]; // 政治
  REPORT_REASON_ATTACK = 7;  // 人身攻击
  REPORT_REASON_UNRELATED = 8;  // 视频不相关
  REPORT_REASON_PROHIBITED = 9;  // 违法违禁
  REPORT_REASON_VULGAR = 10 [deprecated = true]; // 低俗
  REPORT_REASON_ILLEGAL_WEBSITE = 11 [deprecated = true]; // 非法网站
  REPORT_REASON_GAMBLING_FRAUD = 12; // 赌博诈骗
  REPORT_REASON_RUMOR = 13 [deprecated = true]; // 传播不实信息
  REPORT_REASON_ABETTING = 14 [deprecated = true]; // 怂恿教唆信息
  REPORT_REASON_PRIVACY_INVASION = 15; // 侵犯隐私
  REPORT_REASON_UNLIMITED_SIGN = 16 [deprecated = true]; // 抢楼
  REPORT_REASON_YOUTH_INAPPROPRIATE = 17; // 青少年不良信息
  REPORT_REASON_ILLEGAL_LOTTERY = 18 [deprecated = true]; // 违规抽奖
  REPORT_REASON_POLITICS_RUMOR = 19; // 涉政敏感
  REPORT_REASON_SOCIAL_EVENTS_RUMOR = 20 [deprecated = true]; // 涉社会事件谣言
  REPORT_REASON_EPIDEMIC_RUMOR = 21 [deprecated = true]; // 疫情谣言
  REPORT_REASON_LIE = 22; // 虚假不实信息（原来二级，新的一级）
  REPORT_REASON_ILLEGAL_LINK = 23; //违法信息外链
  REPORT_REASON_DISCRIMINATION = 24;  // 歧视对立
  REPORT_REASON_CYBERBULLYING = 25; // 网暴
  REPORT_REASON_UNFRIENDLY = 26; // 不友善言论
  REPORT_REASON_PROSTITUTION_INFORMATION = 27; // 招嫖信息
  REPORT_REASON_SEXUAL_HARASSMENT = 28; // 性骚扰
  REPORT_REASON_SPREAD_PORN_RESOURCES = 29; // 传播色情资源
  REPORT_REASON_ILLEGAL_OR_POLITICAL = 30; // 违法违规涉政行为（一级）
  REPORT_REASON_UNFRIENDLY_OR_DESTRUCTIVE = 31; // 不友善、破坏社区氛围（一级）
  REPORT_REASON_PORN_OR_VULGAR = 32; // 色情低俗（一级）
  REPORT_REASON_DISLIKE = 33; // 我不喜欢（一级）
}
