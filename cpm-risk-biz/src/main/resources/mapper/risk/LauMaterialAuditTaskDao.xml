<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.risk.dao.risk.LauMaterialAuditTaskDao">
  <resultMap id="BaseResultMap" type="com.bilibili.risk.po.risk.LauMaterialAuditTaskPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="task_id" jdbcType="CHAR" property="taskId" />
    <result column="material_id" jdbcType="CHAR" property="materialId" />
    <result column="material_md5" jdbcType="VARCHAR" property="materialMd5" />
    <result column="material_type" jdbcType="TINYINT" property="materialType" />
    <result column="material_content" jdbcType="VARCHAR" property="materialContent" />
    <result column="receive_batch_no" jdbcType="VARCHAR" property="receiveBatchNo" />
    <result column="creative_id" jdbcType="INTEGER" property="creativeId" />
    <result column="biz_type" jdbcType="INTEGER" property="bizType" />
    <result column="queue_id" jdbcType="BIGINT" property="queueId" />
    <result column="creative_audit_status" jdbcType="INTEGER" property="creativeAuditStatus" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="account_id" jdbcType="INTEGER" property="accountId" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="accept_time" jdbcType="TIMESTAMP" property="acceptTime" />
    <result column="accept_name" jdbcType="VARCHAR" property="acceptName" />
    <result column="execute_time" jdbcType="TIMESTAMP" property="executeTime" />
    <result column="execute_name" jdbcType="VARCHAR" property="executeName" />
    <result column="audit_label_third_id" jdbcType="VARCHAR" property="auditLabelThirdId" />
    <result column="united_first_industry_id" jdbcType="INTEGER" property="unitedFirstIndustryId" />
    <result column="united_second_industry_id" jdbcType="INTEGER" property="unitedSecondIndustryId" />
    <result column="united_third_industry_id" jdbcType="INTEGER" property="unitedThirdIndustryId" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="material_source" jdbcType="VARCHAR" property="materialSource" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="enter_audit_time" jdbcType="TIMESTAMP" property="enterAuditTime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.bilibili.risk.po.risk.LauMaterialAuditTaskPo">
    <result column="extra" jdbcType="LONGVARCHAR" property="extra" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, task_id, material_id, material_md5, material_type, material_content, receive_batch_no, 
    creative_id, biz_type, queue_id, creative_audit_status, status, account_id, version, 
    accept_time, accept_name, execute_time, execute_name, audit_label_third_id, united_first_industry_id, 
    united_second_industry_id, united_third_industry_id, ctime, mtime, is_deleted, type, 
    material_source, reason, enter_audit_time
  </sql>
  <sql id="Blob_Column_List">
    extra
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.bilibili.risk.po.risk.LauMaterialAuditTaskPoExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from lau_material_audit_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByExample" parameterType="com.bilibili.risk.po.risk.LauMaterialAuditTaskPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from lau_material_audit_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from lau_material_audit_task
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from lau_material_audit_task
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.risk.po.risk.LauMaterialAuditTaskPoExample">
    delete from lau_material_audit_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.risk.po.risk.LauMaterialAuditTaskPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_material_audit_task (task_id, material_id, material_md5,
      material_type, material_content, receive_batch_no, 
      creative_id, biz_type, queue_id, 
      creative_audit_status, status, account_id, 
      version, accept_time, accept_name, 
      execute_time, execute_name, audit_label_third_id, 
      united_first_industry_id, united_second_industry_id, 
      united_third_industry_id, ctime, mtime, 
      is_deleted, type, material_source, 
      reason, enter_audit_time, extra
      )
    values (#{taskId,jdbcType=CHAR}, #{materialId,jdbcType=CHAR}, #{materialMd5,jdbcType=VARCHAR}, 
      #{materialType,jdbcType=TINYINT}, #{materialContent,jdbcType=VARCHAR}, #{receiveBatchNo,jdbcType=VARCHAR}, 
      #{creativeId,jdbcType=INTEGER}, #{bizType,jdbcType=INTEGER}, #{queueId,jdbcType=BIGINT}, 
      #{creativeAuditStatus,jdbcType=INTEGER}, #{status,jdbcType=INTEGER}, #{accountId,jdbcType=INTEGER}, 
      #{version,jdbcType=INTEGER}, #{acceptTime,jdbcType=TIMESTAMP}, #{acceptName,jdbcType=VARCHAR}, 
      #{executeTime,jdbcType=TIMESTAMP}, #{executeName,jdbcType=VARCHAR}, #{auditLabelThirdId,jdbcType=VARCHAR}, 
      #{unitedFirstIndustryId,jdbcType=INTEGER}, #{unitedSecondIndustryId,jdbcType=INTEGER}, 
      #{unitedThirdIndustryId,jdbcType=INTEGER}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, 
      #{isDeleted,jdbcType=TINYINT}, #{type,jdbcType=TINYINT}, #{materialSource,jdbcType=VARCHAR}, 
      #{reason,jdbcType=VARCHAR}, #{enterAuditTime,jdbcType=TIMESTAMP}, #{extra,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.risk.po.risk.LauMaterialAuditTaskPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_material_audit_task
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="taskId != null">
        task_id,
      </if>
      <if test="materialId != null">
        material_id,
      </if>
      <if test="materialMd5 != null">
        material_md5,
      </if>
      <if test="materialType != null">
        material_type,
      </if>
      <if test="materialContent != null">
        material_content,
      </if>
      <if test="receiveBatchNo != null">
        receive_batch_no,
      </if>
      <if test="creativeId != null">
        creative_id,
      </if>
      <if test="bizType != null">
        biz_type,
      </if>
      <if test="queueId != null">
        queue_id,
      </if>
      <if test="creativeAuditStatus != null">
        creative_audit_status,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="acceptTime != null">
        accept_time,
      </if>
      <if test="acceptName != null">
        accept_name,
      </if>
      <if test="executeTime != null">
        execute_time,
      </if>
      <if test="executeName != null">
        execute_name,
      </if>
      <if test="auditLabelThirdId != null">
        audit_label_third_id,
      </if>
      <if test="unitedFirstIndustryId != null">
        united_first_industry_id,
      </if>
      <if test="unitedSecondIndustryId != null">
        united_second_industry_id,
      </if>
      <if test="unitedThirdIndustryId != null">
        united_third_industry_id,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="materialSource != null">
        material_source,
      </if>
      <if test="reason != null">
        reason,
      </if>
      <if test="enterAuditTime != null">
        enter_audit_time,
      </if>
      <if test="extra != null">
        extra,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="taskId != null">
        #{taskId,jdbcType=CHAR},
      </if>
      <if test="materialId != null">
        #{materialId,jdbcType=CHAR},
      </if>
      <if test="materialMd5 != null">
        #{materialMd5,jdbcType=VARCHAR},
      </if>
      <if test="materialType != null">
        #{materialType,jdbcType=TINYINT},
      </if>
      <if test="materialContent != null">
        #{materialContent,jdbcType=VARCHAR},
      </if>
      <if test="receiveBatchNo != null">
        #{receiveBatchNo,jdbcType=VARCHAR},
      </if>
      <if test="creativeId != null">
        #{creativeId,jdbcType=INTEGER},
      </if>
      <if test="bizType != null">
        #{bizType,jdbcType=INTEGER},
      </if>
      <if test="queueId != null">
        #{queueId,jdbcType=BIGINT},
      </if>
      <if test="creativeAuditStatus != null">
        #{creativeAuditStatus,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="acceptTime != null">
        #{acceptTime,jdbcType=TIMESTAMP},
      </if>
      <if test="acceptName != null">
        #{acceptName,jdbcType=VARCHAR},
      </if>
      <if test="executeTime != null">
        #{executeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="executeName != null">
        #{executeName,jdbcType=VARCHAR},
      </if>
      <if test="auditLabelThirdId != null">
        #{auditLabelThirdId,jdbcType=VARCHAR},
      </if>
      <if test="unitedFirstIndustryId != null">
        #{unitedFirstIndustryId,jdbcType=INTEGER},
      </if>
      <if test="unitedSecondIndustryId != null">
        #{unitedSecondIndustryId,jdbcType=INTEGER},
      </if>
      <if test="unitedThirdIndustryId != null">
        #{unitedThirdIndustryId,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="materialSource != null">
        #{materialSource,jdbcType=VARCHAR},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="enterAuditTime != null">
        #{enterAuditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="extra != null">
        #{extra,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.risk.po.risk.LauMaterialAuditTaskPoExample" resultType="java.lang.Long">
    select count(*) from lau_material_audit_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update lau_material_audit_task
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.taskId != null">
        task_id = #{record.taskId,jdbcType=CHAR},
      </if>
      <if test="record.materialId != null">
        material_id = #{record.materialId,jdbcType=CHAR},
      </if>
      <if test="record.materialMd5 != null">
        material_md5 = #{record.materialMd5,jdbcType=VARCHAR},
      </if>
      <if test="record.materialType != null">
        material_type = #{record.materialType,jdbcType=TINYINT},
      </if>
      <if test="record.materialContent != null">
        material_content = #{record.materialContent,jdbcType=VARCHAR},
      </if>
      <if test="record.receiveBatchNo != null">
        receive_batch_no = #{record.receiveBatchNo,jdbcType=VARCHAR},
      </if>
      <if test="record.creativeId != null">
        creative_id = #{record.creativeId,jdbcType=INTEGER},
      </if>
      <if test="record.bizType != null">
        biz_type = #{record.bizType,jdbcType=INTEGER},
      </if>
      <if test="record.queueId != null">
        queue_id = #{record.queueId,jdbcType=BIGINT},
      </if>
      <if test="record.creativeAuditStatus != null">
        creative_audit_status = #{record.creativeAuditStatus,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.accountId != null">
        account_id = #{record.accountId,jdbcType=INTEGER},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.acceptTime != null">
        accept_time = #{record.acceptTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.acceptName != null">
        accept_name = #{record.acceptName,jdbcType=VARCHAR},
      </if>
      <if test="record.executeTime != null">
        execute_time = #{record.executeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.executeName != null">
        execute_name = #{record.executeName,jdbcType=VARCHAR},
      </if>
      <if test="record.auditLabelThirdId != null">
        audit_label_third_id = #{record.auditLabelThirdId,jdbcType=VARCHAR},
      </if>
      <if test="record.unitedFirstIndustryId != null">
        united_first_industry_id = #{record.unitedFirstIndustryId,jdbcType=INTEGER},
      </if>
      <if test="record.unitedSecondIndustryId != null">
        united_second_industry_id = #{record.unitedSecondIndustryId,jdbcType=INTEGER},
      </if>
      <if test="record.unitedThirdIndustryId != null">
        united_third_industry_id = #{record.unitedThirdIndustryId,jdbcType=INTEGER},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=TINYINT},
      </if>
      <if test="record.materialSource != null">
        material_source = #{record.materialSource,jdbcType=VARCHAR},
      </if>
      <if test="record.reason != null">
        reason = #{record.reason,jdbcType=VARCHAR},
      </if>
      <if test="record.enterAuditTime != null">
        enter_audit_time = #{record.enterAuditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extra != null">
        extra = #{record.extra,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update lau_material_audit_task
    set id = #{record.id,jdbcType=BIGINT},
      task_id = #{record.taskId,jdbcType=CHAR},
      material_id = #{record.materialId,jdbcType=CHAR},
      material_md5 = #{record.materialMd5,jdbcType=VARCHAR},
      material_type = #{record.materialType,jdbcType=TINYINT},
      material_content = #{record.materialContent,jdbcType=VARCHAR},
      receive_batch_no = #{record.receiveBatchNo,jdbcType=VARCHAR},
      creative_id = #{record.creativeId,jdbcType=INTEGER},
      biz_type = #{record.bizType,jdbcType=INTEGER},
      queue_id = #{record.queueId,jdbcType=BIGINT},
      creative_audit_status = #{record.creativeAuditStatus,jdbcType=INTEGER},
      status = #{record.status,jdbcType=INTEGER},
      account_id = #{record.accountId,jdbcType=INTEGER},
      version = #{record.version,jdbcType=INTEGER},
      accept_time = #{record.acceptTime,jdbcType=TIMESTAMP},
      accept_name = #{record.acceptName,jdbcType=VARCHAR},
      execute_time = #{record.executeTime,jdbcType=TIMESTAMP},
      execute_name = #{record.executeName,jdbcType=VARCHAR},
      audit_label_third_id = #{record.auditLabelThirdId,jdbcType=VARCHAR},
      united_first_industry_id = #{record.unitedFirstIndustryId,jdbcType=INTEGER},
      united_second_industry_id = #{record.unitedSecondIndustryId,jdbcType=INTEGER},
      united_third_industry_id = #{record.unitedThirdIndustryId,jdbcType=INTEGER},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      type = #{record.type,jdbcType=TINYINT},
      material_source = #{record.materialSource,jdbcType=VARCHAR},
      reason = #{record.reason,jdbcType=VARCHAR},
      enter_audit_time = #{record.enterAuditTime,jdbcType=TIMESTAMP},
      extra = #{record.extra,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update lau_material_audit_task
    set id = #{record.id,jdbcType=BIGINT},
      task_id = #{record.taskId,jdbcType=CHAR},
      material_id = #{record.materialId,jdbcType=CHAR},
      material_md5 = #{record.materialMd5,jdbcType=VARCHAR},
      material_type = #{record.materialType,jdbcType=TINYINT},
      material_content = #{record.materialContent,jdbcType=VARCHAR},
      receive_batch_no = #{record.receiveBatchNo,jdbcType=VARCHAR},
      creative_id = #{record.creativeId,jdbcType=INTEGER},
      biz_type = #{record.bizType,jdbcType=INTEGER},
      queue_id = #{record.queueId,jdbcType=BIGINT},
      creative_audit_status = #{record.creativeAuditStatus,jdbcType=INTEGER},
      status = #{record.status,jdbcType=INTEGER},
      account_id = #{record.accountId,jdbcType=INTEGER},
      version = #{record.version,jdbcType=INTEGER},
      accept_time = #{record.acceptTime,jdbcType=TIMESTAMP},
      accept_name = #{record.acceptName,jdbcType=VARCHAR},
      execute_time = #{record.executeTime,jdbcType=TIMESTAMP},
      execute_name = #{record.executeName,jdbcType=VARCHAR},
      audit_label_third_id = #{record.auditLabelThirdId,jdbcType=VARCHAR},
      united_first_industry_id = #{record.unitedFirstIndustryId,jdbcType=INTEGER},
      united_second_industry_id = #{record.unitedSecondIndustryId,jdbcType=INTEGER},
      united_third_industry_id = #{record.unitedThirdIndustryId,jdbcType=INTEGER},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      type = #{record.type,jdbcType=TINYINT},
      material_source = #{record.materialSource,jdbcType=VARCHAR},
      reason = #{record.reason,jdbcType=VARCHAR},
      enter_audit_time = #{record.enterAuditTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.risk.po.risk.LauMaterialAuditTaskPo">
    update lau_material_audit_task
    <set>
      <if test="taskId != null">
        task_id = #{taskId,jdbcType=CHAR},
      </if>
      <if test="materialId != null">
        material_id = #{materialId,jdbcType=CHAR},
      </if>
      <if test="materialMd5 != null">
        material_md5 = #{materialMd5,jdbcType=VARCHAR},
      </if>
      <if test="materialType != null">
        material_type = #{materialType,jdbcType=TINYINT},
      </if>
      <if test="materialContent != null">
        material_content = #{materialContent,jdbcType=VARCHAR},
      </if>
      <if test="receiveBatchNo != null">
        receive_batch_no = #{receiveBatchNo,jdbcType=VARCHAR},
      </if>
      <if test="creativeId != null">
        creative_id = #{creativeId,jdbcType=INTEGER},
      </if>
      <if test="bizType != null">
        biz_type = #{bizType,jdbcType=INTEGER},
      </if>
      <if test="queueId != null">
        queue_id = #{queueId,jdbcType=BIGINT},
      </if>
      <if test="creativeAuditStatus != null">
        creative_audit_status = #{creativeAuditStatus,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=INTEGER},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="acceptTime != null">
        accept_time = #{acceptTime,jdbcType=TIMESTAMP},
      </if>
      <if test="acceptName != null">
        accept_name = #{acceptName,jdbcType=VARCHAR},
      </if>
      <if test="executeTime != null">
        execute_time = #{executeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="executeName != null">
        execute_name = #{executeName,jdbcType=VARCHAR},
      </if>
      <if test="auditLabelThirdId != null">
        audit_label_third_id = #{auditLabelThirdId,jdbcType=VARCHAR},
      </if>
      <if test="unitedFirstIndustryId != null">
        united_first_industry_id = #{unitedFirstIndustryId,jdbcType=INTEGER},
      </if>
      <if test="unitedSecondIndustryId != null">
        united_second_industry_id = #{unitedSecondIndustryId,jdbcType=INTEGER},
      </if>
      <if test="unitedThirdIndustryId != null">
        united_third_industry_id = #{unitedThirdIndustryId,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=TINYINT},
      </if>
      <if test="materialSource != null">
        material_source = #{materialSource,jdbcType=VARCHAR},
      </if>
      <if test="reason != null">
        reason = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="enterAuditTime != null">
        enter_audit_time = #{enterAuditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="extra != null">
        extra = #{extra,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.bilibili.risk.po.risk.LauMaterialAuditTaskPo">
    update lau_material_audit_task
    set task_id = #{taskId,jdbcType=CHAR},
      material_id = #{materialId,jdbcType=CHAR},
      material_md5 = #{materialMd5,jdbcType=VARCHAR},
      material_type = #{materialType,jdbcType=TINYINT},
      material_content = #{materialContent,jdbcType=VARCHAR},
      receive_batch_no = #{receiveBatchNo,jdbcType=VARCHAR},
      creative_id = #{creativeId,jdbcType=INTEGER},
      biz_type = #{bizType,jdbcType=INTEGER},
      queue_id = #{queueId,jdbcType=BIGINT},
      creative_audit_status = #{creativeAuditStatus,jdbcType=INTEGER},
      status = #{status,jdbcType=INTEGER},
      account_id = #{accountId,jdbcType=INTEGER},
      version = #{version,jdbcType=INTEGER},
      accept_time = #{acceptTime,jdbcType=TIMESTAMP},
      accept_name = #{acceptName,jdbcType=VARCHAR},
      execute_time = #{executeTime,jdbcType=TIMESTAMP},
      execute_name = #{executeName,jdbcType=VARCHAR},
      audit_label_third_id = #{auditLabelThirdId,jdbcType=VARCHAR},
      united_first_industry_id = #{unitedFirstIndustryId,jdbcType=INTEGER},
      united_second_industry_id = #{unitedSecondIndustryId,jdbcType=INTEGER},
      united_third_industry_id = #{unitedThirdIndustryId,jdbcType=INTEGER},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      type = #{type,jdbcType=TINYINT},
      material_source = #{materialSource,jdbcType=VARCHAR},
      reason = #{reason,jdbcType=VARCHAR},
      enter_audit_time = #{enterAuditTime,jdbcType=TIMESTAMP},
      extra = #{extra,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.risk.po.risk.LauMaterialAuditTaskPo">
    update lau_material_audit_task
    set task_id = #{taskId,jdbcType=CHAR},
      material_id = #{materialId,jdbcType=CHAR},
      material_md5 = #{materialMd5,jdbcType=VARCHAR},
      material_type = #{materialType,jdbcType=TINYINT},
      material_content = #{materialContent,jdbcType=VARCHAR},
      receive_batch_no = #{receiveBatchNo,jdbcType=VARCHAR},
      creative_id = #{creativeId,jdbcType=INTEGER},
      biz_type = #{bizType,jdbcType=INTEGER},
      queue_id = #{queueId,jdbcType=BIGINT},
      creative_audit_status = #{creativeAuditStatus,jdbcType=INTEGER},
      status = #{status,jdbcType=INTEGER},
      account_id = #{accountId,jdbcType=INTEGER},
      version = #{version,jdbcType=INTEGER},
      accept_time = #{acceptTime,jdbcType=TIMESTAMP},
      accept_name = #{acceptName,jdbcType=VARCHAR},
      execute_time = #{executeTime,jdbcType=TIMESTAMP},
      execute_name = #{executeName,jdbcType=VARCHAR},
      audit_label_third_id = #{auditLabelThirdId,jdbcType=VARCHAR},
      united_first_industry_id = #{unitedFirstIndustryId,jdbcType=INTEGER},
      united_second_industry_id = #{unitedSecondIndustryId,jdbcType=INTEGER},
      united_third_industry_id = #{unitedThirdIndustryId,jdbcType=INTEGER},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      type = #{type,jdbcType=TINYINT},
      material_source = #{materialSource,jdbcType=VARCHAR},
      reason = #{reason,jdbcType=VARCHAR},
      enter_audit_time = #{enterAuditTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.risk.po.risk.LauMaterialAuditTaskPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_material_audit_task (task_id, material_id, material_md5,
      material_type, material_content, receive_batch_no, 
      creative_id, biz_type, queue_id, 
      creative_audit_status, status, account_id, 
      version, accept_time, accept_name, 
      execute_time, execute_name, audit_label_third_id, 
      united_first_industry_id, united_second_industry_id, 
      united_third_industry_id, ctime, mtime, 
      is_deleted, type, material_source, 
      reason, enter_audit_time, extra
      )
    values (#{taskId,jdbcType=CHAR}, #{materialId,jdbcType=CHAR}, #{materialMd5,jdbcType=VARCHAR}, 
      #{materialType,jdbcType=TINYINT}, #{materialContent,jdbcType=VARCHAR}, #{receiveBatchNo,jdbcType=VARCHAR}, 
      #{creativeId,jdbcType=INTEGER}, #{bizType,jdbcType=INTEGER}, #{queueId,jdbcType=BIGINT}, 
      #{creativeAuditStatus,jdbcType=INTEGER}, #{status,jdbcType=INTEGER}, #{accountId,jdbcType=INTEGER}, 
      #{version,jdbcType=INTEGER}, #{acceptTime,jdbcType=TIMESTAMP}, #{acceptName,jdbcType=VARCHAR}, 
      #{executeTime,jdbcType=TIMESTAMP}, #{executeName,jdbcType=VARCHAR}, #{auditLabelThirdId,jdbcType=VARCHAR}, 
      #{unitedFirstIndustryId,jdbcType=INTEGER}, #{unitedSecondIndustryId,jdbcType=INTEGER}, 
      #{unitedThirdIndustryId,jdbcType=INTEGER}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, 
      #{isDeleted,jdbcType=TINYINT}, #{type,jdbcType=TINYINT}, #{materialSource,jdbcType=VARCHAR}, 
      #{reason,jdbcType=VARCHAR}, #{enterAuditTime,jdbcType=TIMESTAMP}, #{extra,jdbcType=LONGVARCHAR}
      )
    <trim prefix="on duplicate key update" suffixOverrides=",">
      task_id = values(task_id),
      material_id = values(material_id),
      material_md5 = values(material_md5),
      material_type = values(material_type),
      material_content = values(material_content),
      receive_batch_no = values(receive_batch_no),
      creative_id = values(creative_id),
      biz_type = values(biz_type),
      queue_id = values(queue_id),
      creative_audit_status = values(creative_audit_status),
      status = values(status),
      account_id = values(account_id),
      version = values(version),
      accept_time = values(accept_time),
      accept_name = values(accept_name),
      execute_time = values(execute_time),
      execute_name = values(execute_name),
      audit_label_third_id = values(audit_label_third_id),
      united_first_industry_id = values(united_first_industry_id),
      united_second_industry_id = values(united_second_industry_id),
      united_third_industry_id = values(united_third_industry_id),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
      type = values(type),
      material_source = values(material_source),
      reason = values(reason),
      enter_audit_time = values(enter_audit_time),
      extra = values(extra),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      lau_material_audit_task
      (task_id,material_id,material_md5,material_type,material_content,receive_batch_no,creative_id,biz_type,queue_id,creative_audit_status,status,account_id,version,accept_time,accept_name,execute_time,execute_name,audit_label_third_id,united_first_industry_id,united_second_industry_id,united_third_industry_id,ctime,mtime,is_deleted,type,material_source,reason,enter_audit_time,extra)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.taskId,jdbcType=CHAR},
        #{item.materialId,jdbcType=CHAR},
        #{item.materialMd5,jdbcType=VARCHAR},
        #{item.materialType,jdbcType=TINYINT},
        #{item.materialContent,jdbcType=VARCHAR},
        #{item.receiveBatchNo,jdbcType=VARCHAR},
        #{item.creativeId,jdbcType=INTEGER},
        #{item.bizType,jdbcType=INTEGER},
        #{item.queueId,jdbcType=BIGINT},
        #{item.creativeAuditStatus,jdbcType=INTEGER},
        #{item.status,jdbcType=INTEGER},
        #{item.accountId,jdbcType=INTEGER},
        #{item.version,jdbcType=INTEGER},
        #{item.acceptTime,jdbcType=TIMESTAMP},
        #{item.acceptName,jdbcType=VARCHAR},
        #{item.executeTime,jdbcType=TIMESTAMP},
        #{item.executeName,jdbcType=VARCHAR},
        #{item.auditLabelThirdId,jdbcType=VARCHAR},
        #{item.unitedFirstIndustryId,jdbcType=INTEGER},
        #{item.unitedSecondIndustryId,jdbcType=INTEGER},
        #{item.unitedThirdIndustryId,jdbcType=INTEGER},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.type,jdbcType=TINYINT},
        #{item.materialSource,jdbcType=VARCHAR},
        #{item.reason,jdbcType=VARCHAR},
        #{item.enterAuditTime,jdbcType=TIMESTAMP},
        #{item.extra,jdbcType=LONGVARCHAR},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      lau_material_audit_task
      (task_id,material_id,material_md5,material_type,material_content,receive_batch_no,creative_id,biz_type,queue_id,creative_audit_status,status,account_id,version,accept_time,accept_name,execute_time,execute_name,audit_label_third_id,united_first_industry_id,united_second_industry_id,united_third_industry_id,ctime,mtime,is_deleted,type,material_source,reason,enter_audit_time,extra)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.taskId,jdbcType=CHAR},
        #{item.materialId,jdbcType=CHAR},
        #{item.materialMd5,jdbcType=VARCHAR},
        #{item.materialType,jdbcType=TINYINT},
        #{item.materialContent,jdbcType=VARCHAR},
        #{item.receiveBatchNo,jdbcType=VARCHAR},
        #{item.creativeId,jdbcType=INTEGER},
        #{item.bizType,jdbcType=INTEGER},
        #{item.queueId,jdbcType=BIGINT},
        #{item.creativeAuditStatus,jdbcType=INTEGER},
        #{item.status,jdbcType=INTEGER},
        #{item.accountId,jdbcType=INTEGER},
        #{item.version,jdbcType=INTEGER},
        #{item.acceptTime,jdbcType=TIMESTAMP},
        #{item.acceptName,jdbcType=VARCHAR},
        #{item.executeTime,jdbcType=TIMESTAMP},
        #{item.executeName,jdbcType=VARCHAR},
        #{item.auditLabelThirdId,jdbcType=VARCHAR},
        #{item.unitedFirstIndustryId,jdbcType=INTEGER},
        #{item.unitedSecondIndustryId,jdbcType=INTEGER},
        #{item.unitedThirdIndustryId,jdbcType=INTEGER},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.type,jdbcType=TINYINT},
        #{item.materialSource,jdbcType=VARCHAR},
        #{item.reason,jdbcType=VARCHAR},
        #{item.enterAuditTime,jdbcType=TIMESTAMP},
        #{item.extra,jdbcType=LONGVARCHAR},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      task_id = values(task_id),
      material_id = values(material_id),
      material_md5 = values(material_md5),
      material_type = values(material_type),
      material_content = values(material_content),
      receive_batch_no = values(receive_batch_no),
      creative_id = values(creative_id),
      biz_type = values(biz_type),
      queue_id = values(queue_id),
      creative_audit_status = values(creative_audit_status),
      status = values(status),
      account_id = values(account_id),
      version = values(version),
      accept_time = values(accept_time),
      accept_name = values(accept_name),
      execute_time = values(execute_time),
      execute_name = values(execute_name),
      audit_label_third_id = values(audit_label_third_id),
      united_first_industry_id = values(united_first_industry_id),
      united_second_industry_id = values(united_second_industry_id),
      united_third_industry_id = values(united_third_industry_id),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
      type = values(type),
      material_source = values(material_source),
      reason = values(reason),
      enter_audit_time = values(enter_audit_time),
      extra = values(extra),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.risk.po.risk.LauMaterialAuditTaskPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_material_audit_task
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="taskId != null">
        task_id,
      </if>
      <if test="materialId != null">
        material_id,
      </if>
      <if test="materialMd5 != null">
        material_md5,
      </if>
      <if test="materialType != null">
        material_type,
      </if>
      <if test="materialContent != null">
        material_content,
      </if>
      <if test="receiveBatchNo != null">
        receive_batch_no,
      </if>
      <if test="creativeId != null">
        creative_id,
      </if>
      <if test="bizType != null">
        biz_type,
      </if>
      <if test="queueId != null">
        queue_id,
      </if>
      <if test="creativeAuditStatus != null">
        creative_audit_status,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="acceptTime != null">
        accept_time,
      </if>
      <if test="acceptName != null">
        accept_name,
      </if>
      <if test="executeTime != null">
        execute_time,
      </if>
      <if test="executeName != null">
        execute_name,
      </if>
      <if test="auditLabelThirdId != null">
        audit_label_third_id,
      </if>
      <if test="unitedFirstIndustryId != null">
        united_first_industry_id,
      </if>
      <if test="unitedSecondIndustryId != null">
        united_second_industry_id,
      </if>
      <if test="unitedThirdIndustryId != null">
        united_third_industry_id,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="materialSource != null">
        material_source,
      </if>
      <if test="reason != null">
        reason,
      </if>
      <if test="enterAuditTime != null">
        enter_audit_time,
      </if>
      <if test="extra != null">
        extra,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="taskId != null">
        #{taskId,jdbcType=CHAR},
      </if>
      <if test="materialId != null">
        #{materialId,jdbcType=CHAR},
      </if>
      <if test="materialMd5 != null">
        #{materialMd5,jdbcType=VARCHAR},
      </if>
      <if test="materialType != null">
        #{materialType,jdbcType=TINYINT},
      </if>
      <if test="materialContent != null">
        #{materialContent,jdbcType=VARCHAR},
      </if>
      <if test="receiveBatchNo != null">
        #{receiveBatchNo,jdbcType=VARCHAR},
      </if>
      <if test="creativeId != null">
        #{creativeId,jdbcType=INTEGER},
      </if>
      <if test="bizType != null">
        #{bizType,jdbcType=INTEGER},
      </if>
      <if test="queueId != null">
        #{queueId,jdbcType=BIGINT},
      </if>
      <if test="creativeAuditStatus != null">
        #{creativeAuditStatus,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="acceptTime != null">
        #{acceptTime,jdbcType=TIMESTAMP},
      </if>
      <if test="acceptName != null">
        #{acceptName,jdbcType=VARCHAR},
      </if>
      <if test="executeTime != null">
        #{executeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="executeName != null">
        #{executeName,jdbcType=VARCHAR},
      </if>
      <if test="auditLabelThirdId != null">
        #{auditLabelThirdId,jdbcType=VARCHAR},
      </if>
      <if test="unitedFirstIndustryId != null">
        #{unitedFirstIndustryId,jdbcType=INTEGER},
      </if>
      <if test="unitedSecondIndustryId != null">
        #{unitedSecondIndustryId,jdbcType=INTEGER},
      </if>
      <if test="unitedThirdIndustryId != null">
        #{unitedThirdIndustryId,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="materialSource != null">
        #{materialSource,jdbcType=VARCHAR},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="enterAuditTime != null">
        #{enterAuditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="extra != null">
        #{extra,jdbcType=LONGVARCHAR},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="taskId != null">
        task_id = values(task_id),
      </if>
      <if test="materialId != null">
        material_id = values(material_id),
      </if>
      <if test="materialMd5 != null">
        material_md5 = values(material_md5),
      </if>
      <if test="materialType != null">
        material_type = values(material_type),
      </if>
      <if test="materialContent != null">
        material_content = values(material_content),
      </if>
      <if test="receiveBatchNo != null">
        receive_batch_no = values(receive_batch_no),
      </if>
      <if test="creativeId != null">
        creative_id = values(creative_id),
      </if>
      <if test="bizType != null">
        biz_type = values(biz_type),
      </if>
      <if test="queueId != null">
        queue_id = values(queue_id),
      </if>
      <if test="creativeAuditStatus != null">
        creative_audit_status = values(creative_audit_status),
      </if>
      <if test="status != null">
        status = values(status),
      </if>
      <if test="accountId != null">
        account_id = values(account_id),
      </if>
      <if test="version != null">
        version = values(version),
      </if>
      <if test="acceptTime != null">
        accept_time = values(accept_time),
      </if>
      <if test="acceptName != null">
        accept_name = values(accept_name),
      </if>
      <if test="executeTime != null">
        execute_time = values(execute_time),
      </if>
      <if test="executeName != null">
        execute_name = values(execute_name),
      </if>
      <if test="auditLabelThirdId != null">
        audit_label_third_id = values(audit_label_third_id),
      </if>
      <if test="unitedFirstIndustryId != null">
        united_first_industry_id = values(united_first_industry_id),
      </if>
      <if test="unitedSecondIndustryId != null">
        united_second_industry_id = values(united_second_industry_id),
      </if>
      <if test="unitedThirdIndustryId != null">
        united_third_industry_id = values(united_third_industry_id),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="type != null">
        type = values(type),
      </if>
      <if test="materialSource != null">
        material_source = values(material_source),
      </if>
      <if test="reason != null">
        reason = values(reason),
      </if>
      <if test="enterAuditTime != null">
        enter_audit_time = values(enter_audit_time),
      </if>
      <if test="extra != null">
        extra = values(extra),
      </if>
    </trim>
  </insert>
</mapper>