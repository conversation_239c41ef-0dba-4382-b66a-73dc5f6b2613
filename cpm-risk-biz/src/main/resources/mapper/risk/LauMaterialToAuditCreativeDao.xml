<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.risk.dao.risk.LauMaterialToAuditCreativeDao">
  <resultMap id="BaseResultMap" type="com.bilibili.risk.po.risk.LauMaterialToAuditCreativePo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="creative_id" jdbcType="INTEGER" property="creativeId" />
    <result column="audit_status" jdbcType="TINYINT" property="auditStatus" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="event_time" jdbcType="BIGINT" property="eventTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, creative_id, audit_status, is_deleted, version, ctime, mtime, event_time
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.risk.po.risk.LauMaterialToAuditCreativePoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from lau_material_to_audit_creative
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from lau_material_to_audit_creative
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from lau_material_to_audit_creative
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.risk.po.risk.LauMaterialToAuditCreativePoExample">
    delete from lau_material_to_audit_creative
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.risk.po.risk.LauMaterialToAuditCreativePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_material_to_audit_creative (creative_id, audit_status, is_deleted, 
      version, ctime, mtime, 
      event_time)
    values (#{creativeId,jdbcType=INTEGER}, #{auditStatus,jdbcType=TINYINT}, #{isDeleted,jdbcType=TINYINT}, 
      #{version,jdbcType=INTEGER}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, 
      #{eventTime,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.risk.po.risk.LauMaterialToAuditCreativePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_material_to_audit_creative
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="creativeId != null">
        creative_id,
      </if>
      <if test="auditStatus != null">
        audit_status,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="eventTime != null">
        event_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="creativeId != null">
        #{creativeId,jdbcType=INTEGER},
      </if>
      <if test="auditStatus != null">
        #{auditStatus,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="eventTime != null">
        #{eventTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.risk.po.risk.LauMaterialToAuditCreativePoExample" resultType="java.lang.Long">
    select count(*) from lau_material_to_audit_creative
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update lau_material_to_audit_creative
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.creativeId != null">
        creative_id = #{record.creativeId,jdbcType=INTEGER},
      </if>
      <if test="record.auditStatus != null">
        audit_status = #{record.auditStatus,jdbcType=TINYINT},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.eventTime != null">
        event_time = #{record.eventTime,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update lau_material_to_audit_creative
    set id = #{record.id,jdbcType=BIGINT},
      creative_id = #{record.creativeId,jdbcType=INTEGER},
      audit_status = #{record.auditStatus,jdbcType=TINYINT},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      version = #{record.version,jdbcType=INTEGER},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      event_time = #{record.eventTime,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.risk.po.risk.LauMaterialToAuditCreativePo">
    update lau_material_to_audit_creative
    <set>
      <if test="creativeId != null">
        creative_id = #{creativeId,jdbcType=INTEGER},
      </if>
      <if test="auditStatus != null">
        audit_status = #{auditStatus,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="eventTime != null">
        event_time = #{eventTime,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.risk.po.risk.LauMaterialToAuditCreativePo">
    update lau_material_to_audit_creative
    set creative_id = #{creativeId,jdbcType=INTEGER},
      audit_status = #{auditStatus,jdbcType=TINYINT},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      version = #{version,jdbcType=INTEGER},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      event_time = #{eventTime,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.risk.po.risk.LauMaterialToAuditCreativePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_material_to_audit_creative (creative_id, audit_status, is_deleted, 
      version, ctime, mtime, 
      event_time)
    values (#{creativeId,jdbcType=INTEGER}, #{auditStatus,jdbcType=TINYINT}, #{isDeleted,jdbcType=TINYINT}, 
      #{version,jdbcType=INTEGER}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, 
      #{eventTime,jdbcType=BIGINT})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      creative_id = values(creative_id),
      audit_status = values(audit_status),
      is_deleted = values(is_deleted),
      version = values(version),
      ctime = values(ctime),
      mtime = values(mtime),
      event_time = values(event_time),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      lau_material_to_audit_creative
      (creative_id,audit_status,is_deleted,version,ctime,mtime,event_time)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.creativeId,jdbcType=INTEGER},
        #{item.auditStatus,jdbcType=TINYINT},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.version,jdbcType=INTEGER},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.eventTime,jdbcType=BIGINT},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      lau_material_to_audit_creative
      (creative_id,audit_status,is_deleted,version,ctime,mtime,event_time)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.creativeId,jdbcType=INTEGER},
        #{item.auditStatus,jdbcType=TINYINT},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.version,jdbcType=INTEGER},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.eventTime,jdbcType=BIGINT},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      audit_status = values(audit_status),
      is_deleted = values(is_deleted),
      version = values(version),
      ctime = values(ctime),
      mtime = values(mtime),
      event_time = values(event_time),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.risk.po.risk.LauMaterialToAuditCreativePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_material_to_audit_creative
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="creativeId != null">
        creative_id,
      </if>
      <if test="auditStatus != null">
        audit_status,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="eventTime != null">
        event_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="creativeId != null">
        #{creativeId,jdbcType=INTEGER},
      </if>
      <if test="auditStatus != null">
        #{auditStatus,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="eventTime != null">
        #{eventTime,jdbcType=BIGINT},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="auditStatus != null">
        audit_status = values(audit_status),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="version != null">
        version = values(version),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="eventTime != null">
        event_time = values(event_time),
      </if>
    </trim>
  </insert>
</mapper>