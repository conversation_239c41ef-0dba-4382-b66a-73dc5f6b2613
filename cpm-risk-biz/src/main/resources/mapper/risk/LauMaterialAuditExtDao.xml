<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.risk.dao.risk.LauMaterialAuditExtDao">
    <resultMap id="BaseResultMap" type="com.bilibili.risk.po.risk.LauMaterialAuditPo">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="material_id" jdbcType="CHAR" property="materialId" />
        <result column="material_md5" jdbcType="VARCHAR" property="materialMd5" />
        <result column="material_type" jdbcType="TINYINT" property="materialType" />
        <result column="material_content" jdbcType="VARCHAR" property="materialContent" />
        <result column="extra" jdbcType="VARCHAR" property="extra" />
        <result column="audit_label_third_id" jdbcType="VARCHAR" property="auditLabelThirdId" />
        <result column="version" jdbcType="INTEGER" property="version" />
        <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
        <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
        <result column="raw_content" jdbcType="VARCHAR" property="rawContent" />
        <result column="audit_status" jdbcType="INTEGER" property="auditStatus" />
        <result column="reason" jdbcType="VARCHAR" property="reason" />
        <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        id, material_id, material_md5, material_type, material_content, extra, audit_label_third_id,
    version, ctime, mtime, raw_content, audit_status, reason, is_deleted
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from lau_material_audit
        where material_id = #{materialId,jdbcType=CHAR}
    </select>

    <select id="selectByExampleWithShardingNo" parameterType="map" resultMap="BaseResultMap">
        select
        <if test="example.distinct">
            distinct
        </if>
        <include refid="Base_Column_List" />
        from lau_material_audit_${shardingNo}
        <if test="example != null">
            <include refid="Update_By_Example_Where_Clause" />
        </if>
        <if test="example.orderByClause != null">
            order by ${example.orderByClause}
        </if>
        <if test="example.limit != null">
            <if test="example.offset != null">
                limit ${example.offset}, ${example.limit}
            </if>
            <if test="example.offset == null">
                limit ${example.limit}
            </if>
        </if>
    </select>

    <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.risk.po.risk.LauMaterialAuditPo">
        update lau_material_audit
        <set>
            <if test="materialId != null">
                material_id = #{materialId,jdbcType=CHAR},
            </if>
            <if test="materialMd5 != null">
                material_md5 = #{materialMd5,jdbcType=VARCHAR},
            </if>
            <if test="materialType != null">
                material_type = #{materialType,jdbcType=TINYINT},
            </if>
            <if test="materialContent != null">
                material_content = #{materialContent,jdbcType=VARCHAR},
            </if>
            <if test="extra != null">
                extra = #{extra,jdbcType=VARCHAR},
            </if>
            <if test="auditLabelThirdId != null">
                audit_label_third_id = #{auditLabelThirdId,jdbcType=VARCHAR},
            </if>
            <if test="version != null">
                version = #{version,jdbcType=INTEGER},
            </if>
            <if test="ctime != null">
                ctime = #{ctime,jdbcType=TIMESTAMP},
            </if>
            <if test="mtime != null">
                mtime = #{mtime,jdbcType=TIMESTAMP},
            </if>
            <if test="rawContent != null">
                raw_content = #{rawContent,jdbcType=VARCHAR},
            </if>
            <if test="auditStatus != null">
                audit_status = #{auditStatus,jdbcType=INTEGER},
            </if>
            <if test="reason != null">
                reason = #{reason,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=TINYINT},
            </if>
        </set>
        where material_id = #{materialId,jdbcType=CHAR}
    </update>
</mapper>