<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.risk.dao.risk.LauMaterialAuditDao">
  <resultMap id="BaseResultMap" type="com.bilibili.risk.po.risk.LauMaterialAuditPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="material_id" jdbcType="CHAR" property="materialId" />
    <result column="material_md5" jdbcType="VARCHAR" property="materialMd5" />
    <result column="material_type" jdbcType="TINYINT" property="materialType" />
    <result column="material_content" jdbcType="VARCHAR" property="materialContent" />
    <result column="extra" jdbcType="VARCHAR" property="extra" />
    <result column="audit_label_third_id" jdbcType="VARCHAR" property="auditLabelThirdId" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="raw_content" jdbcType="VARCHAR" property="rawContent" />
    <result column="audit_status" jdbcType="INTEGER" property="auditStatus" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, material_id, material_md5, material_type, material_content, extra, audit_label_third_id, 
    version, ctime, mtime, raw_content, audit_status, reason, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.risk.po.risk.LauMaterialAuditPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from lau_material_audit
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from lau_material_audit
    where material_id = #{materialId,jdbcType=CHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from lau_material_audit
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.risk.po.risk.LauMaterialAuditPoExample">
    delete from lau_material_audit
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.risk.po.risk.LauMaterialAuditPo">
    insert into lau_material_audit (material_id, material_md5, material_type,
      material_content, extra, audit_label_third_id, 
      version, ctime, mtime, raw_content, audit_status, reason, is_deleted)
    values (#{materialId,jdbcType=CHAR}, #{materialMd5,jdbcType=VARCHAR}, #{materialType,jdbcType=TINYINT}, 
      #{materialContent,jdbcType=VARCHAR}, #{extra,jdbcType=VARCHAR}, #{auditLabelThirdId,jdbcType=VARCHAR}, 
      #{version,jdbcType=INTEGER}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, 
      #{rawContent,jdbcType=VARCHAR}, #{auditStatus,jdbcType=INTEGER}, #{reason,jdbcType=VARCHAR}, 
      #{isDeleted,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.risk.po.risk.LauMaterialAuditPo">
    insert into lau_material_audit
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="materialId != null">
        material_id,
      </if>
      <if test="materialMd5 != null">
        material_md5,
      </if>
      <if test="materialType != null">
        material_type,
      </if>
      <if test="materialContent != null">
        material_content,
      </if>
      <if test="extra != null">
        extra,
      </if>
      <if test="auditLabelThirdId != null">
        audit_label_third_id,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="rawContent != null">
        raw_content,
      </if>
      <if test="auditStatus != null">
        audit_status,
      </if>
      <if test="reason != null">
        reason,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="materialId != null">
        #{materialId,jdbcType=CHAR},
      </if>
      <if test="materialMd5 != null">
        #{materialMd5,jdbcType=VARCHAR},
      </if>
      <if test="materialType != null">
        #{materialType,jdbcType=TINYINT},
      </if>
      <if test="materialContent != null">
        #{materialContent,jdbcType=VARCHAR},
      </if>
      <if test="extra != null">
        #{extra,jdbcType=VARCHAR},
      </if>
      <if test="auditLabelThirdId != null">
        #{auditLabelThirdId,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="rawContent != null">
        #{rawContent,jdbcType=VARCHAR},
      </if>
      <if test="auditStatus != null">
        #{auditStatus,jdbcType=INTEGER},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.risk.po.risk.LauMaterialAuditPoExample" resultType="java.lang.Long">
    select count(*) from lau_material_audit
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update lau_material_audit
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.materialId != null">
        material_id = #{record.materialId,jdbcType=CHAR},
      </if>
      <if test="record.materialMd5 != null">
        material_md5 = #{record.materialMd5,jdbcType=VARCHAR},
      </if>
      <if test="record.materialType != null">
        material_type = #{record.materialType,jdbcType=TINYINT},
      </if>
      <if test="record.materialContent != null">
        material_content = #{record.materialContent,jdbcType=VARCHAR},
      </if>
      <if test="record.extra != null">
        extra = #{record.extra,jdbcType=VARCHAR},
      </if>
      <if test="record.auditLabelThirdId != null">
        audit_label_third_id = #{record.auditLabelThirdId,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.rawContent != null">
        raw_content = #{record.rawContent,jdbcType=VARCHAR},
      </if>
      <if test="record.auditStatus != null">
        audit_status = #{record.auditStatus,jdbcType=INTEGER},
      </if>
      <if test="record.reason != null">
        reason = #{record.reason,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update lau_material_audit
    set id = #{record.id,jdbcType=BIGINT},
      material_id = #{record.materialId,jdbcType=CHAR},
      material_md5 = #{record.materialMd5,jdbcType=VARCHAR},
      material_type = #{record.materialType,jdbcType=TINYINT},
      material_content = #{record.materialContent,jdbcType=VARCHAR},
      extra = #{record.extra,jdbcType=VARCHAR},
      audit_label_third_id = #{record.auditLabelThirdId,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      raw_content = #{record.rawContent,jdbcType=VARCHAR},
      audit_status = #{record.auditStatus,jdbcType=INTEGER},
      reason = #{record.reason,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.risk.po.risk.LauMaterialAuditPo">
    update lau_material_audit
    <set>
      <if test="materialId != null">
        material_id = #{materialId,jdbcType=CHAR},
      </if>
      <if test="materialMd5 != null">
        material_md5 = #{materialMd5,jdbcType=VARCHAR},
      </if>
      <if test="materialType != null">
        material_type = #{materialType,jdbcType=TINYINT},
      </if>
      <if test="materialContent != null">
        material_content = #{materialContent,jdbcType=VARCHAR},
      </if>
      <if test="extra != null">
        extra = #{extra,jdbcType=VARCHAR},
      </if>
      <if test="auditLabelThirdId != null">
        audit_label_third_id = #{auditLabelThirdId,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="rawContent != null">
        raw_content = #{rawContent,jdbcType=VARCHAR},
      </if>
      <if test="auditStatus != null">
        audit_status = #{auditStatus,jdbcType=INTEGER},
      </if>
      <if test="reason != null">
        reason = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
    </set>
    where material_id = #{materialId,jdbcType=CHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.risk.po.risk.LauMaterialAuditPo">
    update lau_material_audit
    set material_id = #{materialId,jdbcType=CHAR},
      material_md5 = #{materialMd5,jdbcType=VARCHAR},
      material_type = #{materialType,jdbcType=TINYINT},
      material_content = #{materialContent,jdbcType=VARCHAR},
      extra = #{extra,jdbcType=VARCHAR},
      audit_label_third_id = #{auditLabelThirdId,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      raw_content = #{rawContent,jdbcType=VARCHAR},
      audit_status = #{auditStatus,jdbcType=INTEGER},
      reason = #{reason,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.risk.po.risk.LauMaterialAuditPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_material_audit (material_id, material_md5, material_type,
      material_content, extra, audit_label_third_id, 
      version, ctime, mtime, 
      raw_content, audit_status, reason, is_deleted
      )
    values (#{materialId,jdbcType=CHAR}, #{materialMd5,jdbcType=VARCHAR}, #{materialType,jdbcType=TINYINT}, 
      #{materialContent,jdbcType=VARCHAR}, #{extra,jdbcType=VARCHAR}, #{auditLabelThirdId,jdbcType=VARCHAR}, 
      #{version,jdbcType=INTEGER}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, 
      #{rawContent,jdbcType=VARCHAR}, #{auditStatus,jdbcType=INTEGER}, #{reason,jdbcType=VARCHAR}, #{isDeleted,jdbcType=TINYINT}
      )
    <trim prefix="on duplicate key update" suffixOverrides=",">
      material_content = values(material_content),
      extra = values(extra),
      audit_label_third_id = values(audit_label_third_id),
      version = values(version),
      ctime = values(ctime),
      mtime = values(mtime),
      raw_content = values(raw_content),
      audit_status = values(audit_status),
      reason = values(reason),
      is_deleted = values(is_deleted),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      lau_material_audit
      (material_id,material_md5,material_type,material_content,extra,audit_label_third_id,version,ctime,mtime,raw_content,audit_status,reason,is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.materialId,jdbcType=CHAR},
        #{item.materialMd5,jdbcType=VARCHAR},
        #{item.materialType,jdbcType=TINYINT},
        #{item.materialContent,jdbcType=VARCHAR},
        #{item.extra,jdbcType=VARCHAR},
        #{item.auditLabelThirdId,jdbcType=VARCHAR},
        #{item.version,jdbcType=INTEGER},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.rawContent,jdbcType=VARCHAR},
        #{item.auditStatus,jdbcType=INTEGER},
        #{item.reason,jdbcType=VARCHAR},
        #{item.isDeleted,jdbcType=TINYINT},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      lau_material_audit
      (material_id,material_md5,material_type,material_content,extra,audit_label_third_id,version,ctime,mtime,raw_content,audit_status,reason,is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.materialId,jdbcType=CHAR},
        #{item.materialMd5,jdbcType=VARCHAR},
        #{item.materialType,jdbcType=TINYINT},
        #{item.materialContent,jdbcType=VARCHAR},
        #{item.extra,jdbcType=VARCHAR},
        #{item.auditLabelThirdId,jdbcType=VARCHAR},
        #{item.version,jdbcType=INTEGER},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.rawContent,jdbcType=VARCHAR},
        #{item.auditStatus,jdbcType=INTEGER},
        #{item.reason,jdbcType=VARCHAR},
        #{item.isDeleted,jdbcType=TINYINT},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      material_content = values(material_content),
      extra = values(extra),
      audit_label_third_id = values(audit_label_third_id),
      version = values(version),
      ctime = values(ctime),
      mtime = values(mtime),
      raw_content = values(raw_content),
      audit_status = values(audit_status),
      reason = values(reason),
      is_deleted = values(is_deleted),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.risk.po.risk.LauMaterialAuditPo">
    insert into lau_material_audit
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="materialId != null">
        material_id,
      </if>
      <if test="materialMd5 != null">
        material_md5,
      </if>
      <if test="materialType != null">
        material_type,
      </if>
      <if test="materialContent != null">
        material_content,
      </if>
      <if test="extra != null">
        extra,
      </if>
      <if test="auditLabelThirdId != null">
        audit_label_third_id,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="rawContent != null">
        raw_content,
      </if>
      <if test="auditStatus != null">
        audit_status,
      </if>
      <if test="reason != null">
        reason,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="materialId != null">
        #{materialId,jdbcType=CHAR},
      </if>
      <if test="materialMd5 != null">
        #{materialMd5,jdbcType=VARCHAR},
      </if>
      <if test="materialType != null">
        #{materialType,jdbcType=TINYINT},
      </if>
      <if test="materialContent != null">
        #{materialContent,jdbcType=VARCHAR},
      </if>
      <if test="extra != null">
        #{extra,jdbcType=VARCHAR},
      </if>
      <if test="auditLabelThirdId != null">
        #{auditLabelThirdId,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="rawContent != null">
        #{rawContent,jdbcType=VARCHAR},
      </if>
      <if test="auditStatus != null">
        #{auditStatus,jdbcType=INTEGER},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="materialContent != null">
        material_content = values(material_content),
      </if>
      <if test="extra != null">
        extra = values(extra),
      </if>
      <if test="auditLabelThirdId != null">
        audit_label_third_id = values(audit_label_third_id),
      </if>
      <if test="version != null">
        version = values(version),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="rawContent != null">
        raw_content = values(raw_content),
      </if>
      <if test="auditStatus != null">
        audit_status = values(audit_status),
      </if>
      <if test="reason != null">
        reason = values(reason),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
    </trim>
  </insert>
</mapper>