<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.risk.dao.risk.LauMaterialAuditTaskExtDao">
    <resultMap id="BaseResultMap" type="com.bilibili.risk.po.risk.LauMaterialAuditTaskPo">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="task_id" jdbcType="CHAR" property="taskId" />
        <result column="material_id" jdbcType="CHAR" property="materialId" />
        <result column="material_md5" jdbcType="VARCHAR" property="materialMd5" />
        <result column="material_type" jdbcType="TINYINT" property="materialType" />
        <result column="material_content" jdbcType="VARCHAR" property="materialContent" />
        <result column="receive_batch_no" jdbcType="VARCHAR" property="receiveBatchNo" />
        <result column="creative_id" jdbcType="INTEGER" property="creativeId" />
        <result column="biz_type" jdbcType="INTEGER" property="bizType" />
        <result column="queue_id" jdbcType="BIGINT" property="queueId" />
        <result column="creative_audit_status" jdbcType="INTEGER" property="creativeAuditStatus" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="account_id" jdbcType="INTEGER" property="accountId" />
        <result column="version" jdbcType="INTEGER" property="version" />
        <result column="accept_time" jdbcType="TIMESTAMP" property="acceptTime" />
        <result column="accept_name" jdbcType="VARCHAR" property="acceptName" />
        <result column="execute_time" jdbcType="TIMESTAMP" property="executeTime" />
        <result column="execute_name" jdbcType="VARCHAR" property="executeName" />
        <result column="audit_label_third_id" jdbcType="VARCHAR" property="auditLabelThirdId" />
        <result column="united_first_industry_id" jdbcType="INTEGER" property="unitedFirstIndustryId" />
        <result column="united_second_industry_id" jdbcType="INTEGER" property="unitedSecondIndustryId" />
        <result column="united_third_industry_id" jdbcType="INTEGER" property="unitedThirdIndustryId" />
        <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
        <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
        <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
        <result column="type" jdbcType="TINYINT" property="type" />
        <result column="material_source" jdbcType="VARCHAR" property="materialSource" />
        <result column="reason" jdbcType="VARCHAR" property="reason" />
        <result column="enter_audit_time" jdbcType="TIMESTAMP" property="enterAuditTime" />
    </resultMap>
    <sql id="Base_Column_List">
        id, task_id, material_id, material_md5, material_type, material_content, receive_batch_no,
    creative_id, biz_type, queue_id, creative_audit_status, status, account_id, version,
    accept_time, accept_name, execute_time, execute_name, audit_label_third_id, united_first_industry_id,
    united_second_industry_id, united_third_industry_id, ctime, mtime, is_deleted, type,
    material_source, reason, enter_audit_time
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <!-- 批量乐观锁更新审核任务 -->
    <update id="updateMaterialAuditTaskBosOptimistic" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update lau_material_audit_task
            <set>
                <if test="item.status != null">
                    `status` = #{item.status,jdbcType=INTEGER},
                </if>
                <if test="item.acceptTime != null">
                    accept_time = #{item.acceptTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.acceptName != null">
                    accept_name = #{item.acceptName,jdbcType=VARCHAR},
                </if>
                <if test="item.executeTime != null">
                    execute_time = #{item.executeTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.executeName != null">
                    execute_name = #{item.executeName,jdbcType=VARCHAR},
                </if>
                <if test="item.queueId != null">
                    queue_id = #{item.queueId,jdbcType=BIGINT},
                </if>
                <if test="item.receiveBatchNo != null">
                    receive_batch_no = #{item.receiveBatchNo,jdbcType=VARCHAR},
                </if>
                <if test="item.auditLabelThirdId != null">
                    audit_label_third_id = #{item.auditLabelThirdId,jdbcType=VARCHAR},
                </if>
                version = version + 1,
                mtime = now()
            </set>
            where task_id = #{item.taskId,jdbcType=VARCHAR}
            and version = #{item.version,jdbcType=INTEGER}
        </foreach>
    </update>

    <select id="selectByExample" parameterType="map" resultMap="BaseResultMap">
        select
        <if test="example.distinct">
            distinct
        </if>
        <include refid="Base_Column_List" />
        from lau_material_audit_task_${shardingNo}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause" />
        </if>
        <if test="example.orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="example.limit != null">
            <if test="example.offset != null">
                limit ${example.offset}, ${example.limit}
            </if>
            <if test="example.offset == null">
                limit ${example.limit}
            </if>
        </if>
    </select>

    <update id="updateByPrimaryKeySelective" parameterType="map">
        update lau_material_audit_task_${shardingNo}
        <set>
            <if test="record.taskId != null">
                task_id = #{record.taskId,jdbcType=CHAR},
            </if>
            <if test="record.materialId != null">
                material_id = #{record.materialId,jdbcType=CHAR},
            </if>
            <if test="record.materialMd5 != null">
                material_md5 = #{record.materialMd5,jdbcType=VARCHAR},
            </if>
            <if test="record.materialType != null">
                material_type = #{record.materialType,jdbcType=TINYINT},
            </if>
            <if test="record.materialContent != null">
                material_content = #{record.materialContent,jdbcType=VARCHAR},
            </if>
            <if test="record.receiveBatchNo != null">
                receive_batch_no = #{record.receiveBatchNo,jdbcType=VARCHAR},
            </if>
            <if test="record.creativeId != null">
                creative_id = #{record.creativeId,jdbcType=INTEGER},
            </if>
            <if test="record.bizType != null">
                biz_type = #{record.bizType,jdbcType=INTEGER},
            </if>
            <if test="record.queueId != null">
                queue_id = #{record.queueId,jdbcType=BIGINT},
            </if>
            <if test="record.creativeAuditStatus != null">
                creative_audit_status = #{record.creativeAuditStatus,jdbcType=INTEGER},
            </if>
            <if test="record.status != null">
                status = #{record.status,jdbcType=INTEGER},
            </if>
            <if test="record.accountId != null">
                account_id = #{record.accountId,jdbcType=INTEGER},
            </if>
            <if test="record.version != null">
                version = #{record.version,jdbcType=INTEGER},
            </if>
            <if test="record.acceptTime != null">
                accept_time = #{record.acceptTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.acceptName != null">
                accept_name = #{record.acceptName,jdbcType=VARCHAR},
            </if>
            <if test="record.executeTime != null">
                execute_time = #{record.executeTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.executeName != null">
                execute_name = #{record.executeName,jdbcType=VARCHAR},
            </if>
            <if test="record.auditLabelThirdId != null">
                audit_label_third_id = #{record.auditLabelThirdId,jdbcType=VARCHAR},
            </if>
            <if test="record.unitedFirstIndustryId != null">
                united_first_industry_id = #{record.unitedFirstIndustryId,jdbcType=INTEGER},
            </if>
            <if test="record.unitedSecondIndustryId != null">
                united_second_industry_id = #{record.unitedSecondIndustryId,jdbcType=INTEGER},
            </if>
            <if test="record.unitedThirdIndustryId != null">
                united_third_industry_id = #{record.unitedThirdIndustryId,jdbcType=INTEGER},
            </if>
            <if test="record.ctime != null">
                ctime = #{record.ctime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.mtime != null">
                mtime = #{record.mtime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.isDeleted != null">
                is_deleted = #{record.isDeleted,jdbcType=TINYINT},
            </if>
            <if test="record.type != null">
                type = #{record.type,jdbcType=TINYINT},
            </if>
            <if test="record.materialSource != null">
                material_source = #{record.materialSource,jdbcType=VARCHAR},
            </if>
            <if test="record.reason != null">
                reason = #{record.reason,jdbcType=VARCHAR},
            </if>
            <if test="record.enterAuditTime != null">
                enter_audit_time = #{record.enterAuditTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.extra != null">
                extra = #{record.extra,jdbcType=LONGVARCHAR},
            </if>
        </set>
        where id = #{record.id,jdbcType=BIGINT}
    </update>
</mapper>