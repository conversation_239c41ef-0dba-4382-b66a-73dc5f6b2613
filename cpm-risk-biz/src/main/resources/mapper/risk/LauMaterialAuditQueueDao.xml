<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.risk.dao.risk.LauMaterialAuditQueueDao">
  <resultMap id="BaseResultMap" type="com.bilibili.risk.po.risk.LauMaterialAuditQueuePo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="biz_type" jdbcType="INTEGER" property="bizType" />
    <result column="material_type" jdbcType="INTEGER" property="materialType" />
    <result column="pull_num" jdbcType="BIGINT" property="pullNum" />
    <result column="task_timeout" jdbcType="BIGINT" property="taskTimeout" />
    <result column="role_ids" jdbcType="VARCHAR" property="roleIds" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="seq" jdbcType="INTEGER" property="seq" />
    <result column="queue_type" jdbcType="INTEGER" property="queueType" />
    <result column="tag_id" jdbcType="INTEGER" property="tagId" />
  </resultMap>
  <resultMap id="AliasBaseResultMap" type="com.bilibili.risk.po.risk.LauMaterialAuditQueuePo">
    <id column="lau_material_audit_queue_id" jdbcType="BIGINT" property="id" />
    <result column="lau_material_audit_queue_name" jdbcType="VARCHAR" property="name" />
    <result column="lau_material_audit_queue_biz_type" jdbcType="INTEGER" property="bizType" />
    <result column="lau_material_audit_queue_material_type" jdbcType="INTEGER" property="materialType" />
    <result column="lau_material_audit_queue_pull_num" jdbcType="BIGINT" property="pullNum" />
    <result column="lau_material_audit_queue_task_timeout" jdbcType="BIGINT" property="taskTimeout" />
    <result column="lau_material_audit_queue_role_ids" jdbcType="VARCHAR" property="roleIds" />
    <result column="lau_material_audit_queue_is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="lau_material_audit_queue_ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="lau_material_audit_queue_mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="lau_material_audit_queue_seq" jdbcType="INTEGER" property="seq" />
    <result column="lau_material_audit_queue_queue_type" jdbcType="INTEGER" property="queueType" />
    <result column="lau_material_audit_queue_tag_id" jdbcType="INTEGER" property="tagId" />
  </resultMap>
  <sql id="Alias_Base_Column_List">
    ${alias}.id as lau_material_audit_queue_id, ${alias}.name as lau_material_audit_queue_name, 
    ${alias}.biz_type as lau_material_audit_queue_biz_type, ${alias}.material_type as lau_material_audit_queue_material_type, 
    ${alias}.pull_num as lau_material_audit_queue_pull_num, ${alias}.task_timeout as lau_material_audit_queue_task_timeout, 
    ${alias}.role_ids as lau_material_audit_queue_role_ids, ${alias}.is_deleted as lau_material_audit_queue_is_deleted, 
    ${alias}.ctime as lau_material_audit_queue_ctime, ${alias}.mtime as lau_material_audit_queue_mtime, 
    ${alias}.seq as lau_material_audit_queue_seq, ${alias}.queue_type as lau_material_audit_queue_queue_type, 
    ${alias}.tag_id as lau_material_audit_queue_tag_id
  </sql>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, name, biz_type, material_type, pull_num, task_timeout, role_ids, is_deleted, 
    ctime, mtime, seq, queue_type, tag_id
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.risk.po.risk.LauMaterialAuditQueuePoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from lau_material_audit_queue
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from lau_material_audit_queue
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from lau_material_audit_queue
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.risk.po.risk.LauMaterialAuditQueuePoExample">
    delete from lau_material_audit_queue
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.risk.po.risk.LauMaterialAuditQueuePo">
    insert into lau_material_audit_queue (id, name, biz_type, 
      material_type, pull_num, task_timeout, 
      role_ids, is_deleted, ctime, 
      mtime, seq, queue_type, 
      tag_id)
    values (#{id,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, #{bizType,jdbcType=INTEGER}, 
      #{materialType,jdbcType=INTEGER}, #{pullNum,jdbcType=BIGINT}, #{taskTimeout,jdbcType=BIGINT}, 
      #{roleIds,jdbcType=VARCHAR}, #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP}, #{seq,jdbcType=INTEGER}, #{queueType,jdbcType=INTEGER}, 
      #{tagId,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.risk.po.risk.LauMaterialAuditQueuePo">
    insert into lau_material_audit_queue
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="bizType != null">
        biz_type,
      </if>
      <if test="materialType != null">
        material_type,
      </if>
      <if test="pullNum != null">
        pull_num,
      </if>
      <if test="taskTimeout != null">
        task_timeout,
      </if>
      <if test="roleIds != null">
        role_ids,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="seq != null">
        seq,
      </if>
      <if test="queueType != null">
        queue_type,
      </if>
      <if test="tagId != null">
        tag_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="bizType != null">
        #{bizType,jdbcType=INTEGER},
      </if>
      <if test="materialType != null">
        #{materialType,jdbcType=INTEGER},
      </if>
      <if test="pullNum != null">
        #{pullNum,jdbcType=BIGINT},
      </if>
      <if test="taskTimeout != null">
        #{taskTimeout,jdbcType=BIGINT},
      </if>
      <if test="roleIds != null">
        #{roleIds,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="seq != null">
        #{seq,jdbcType=INTEGER},
      </if>
      <if test="queueType != null">
        #{queueType,jdbcType=INTEGER},
      </if>
      <if test="tagId != null">
        #{tagId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.risk.po.risk.LauMaterialAuditQueuePoExample" resultType="java.lang.Long">
    select count(*) from lau_material_audit_queue
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update lau_material_audit_queue
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.bizType != null">
        biz_type = #{record.bizType,jdbcType=INTEGER},
      </if>
      <if test="record.materialType != null">
        material_type = #{record.materialType,jdbcType=INTEGER},
      </if>
      <if test="record.pullNum != null">
        pull_num = #{record.pullNum,jdbcType=BIGINT},
      </if>
      <if test="record.taskTimeout != null">
        task_timeout = #{record.taskTimeout,jdbcType=BIGINT},
      </if>
      <if test="record.roleIds != null">
        role_ids = #{record.roleIds,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.seq != null">
        seq = #{record.seq,jdbcType=INTEGER},
      </if>
      <if test="record.queueType != null">
        queue_type = #{record.queueType,jdbcType=INTEGER},
      </if>
      <if test="record.tagId != null">
        tag_id = #{record.tagId,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update lau_material_audit_queue
    set id = #{record.id,jdbcType=BIGINT},
      name = #{record.name,jdbcType=VARCHAR},
      biz_type = #{record.bizType,jdbcType=INTEGER},
      material_type = #{record.materialType,jdbcType=INTEGER},
      pull_num = #{record.pullNum,jdbcType=BIGINT},
      task_timeout = #{record.taskTimeout,jdbcType=BIGINT},
      role_ids = #{record.roleIds,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      seq = #{record.seq,jdbcType=INTEGER},
      queue_type = #{record.queueType,jdbcType=INTEGER},
      tag_id = #{record.tagId,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.risk.po.risk.LauMaterialAuditQueuePo">
    update lau_material_audit_queue
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="bizType != null">
        biz_type = #{bizType,jdbcType=INTEGER},
      </if>
      <if test="materialType != null">
        material_type = #{materialType,jdbcType=INTEGER},
      </if>
      <if test="pullNum != null">
        pull_num = #{pullNum,jdbcType=BIGINT},
      </if>
      <if test="taskTimeout != null">
        task_timeout = #{taskTimeout,jdbcType=BIGINT},
      </if>
      <if test="roleIds != null">
        role_ids = #{roleIds,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="seq != null">
        seq = #{seq,jdbcType=INTEGER},
      </if>
      <if test="queueType != null">
        queue_type = #{queueType,jdbcType=INTEGER},
      </if>
      <if test="tagId != null">
        tag_id = #{tagId,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.risk.po.risk.LauMaterialAuditQueuePo">
    update lau_material_audit_queue
    set name = #{name,jdbcType=VARCHAR},
      biz_type = #{bizType,jdbcType=INTEGER},
      material_type = #{materialType,jdbcType=INTEGER},
      pull_num = #{pullNum,jdbcType=BIGINT},
      task_timeout = #{taskTimeout,jdbcType=BIGINT},
      role_ids = #{roleIds,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      seq = #{seq,jdbcType=INTEGER},
      queue_type = #{queueType,jdbcType=INTEGER},
      tag_id = #{tagId,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.risk.po.risk.LauMaterialAuditQueuePo">
    insert into lau_material_audit_queue (id, name, biz_type, 
      material_type, pull_num, task_timeout, 
      role_ids, is_deleted, ctime, 
      mtime, seq, queue_type, 
      tag_id)
    values (#{id,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, #{bizType,jdbcType=INTEGER}, 
      #{materialType,jdbcType=INTEGER}, #{pullNum,jdbcType=BIGINT}, #{taskTimeout,jdbcType=BIGINT}, 
      #{roleIds,jdbcType=VARCHAR}, #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP}, #{seq,jdbcType=INTEGER}, #{queueType,jdbcType=INTEGER}, 
      #{tagId,jdbcType=INTEGER})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      name = values(name),
      biz_type = values(biz_type),
      material_type = values(material_type),
      pull_num = values(pull_num),
      task_timeout = values(task_timeout),
      role_ids = values(role_ids),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      seq = values(seq),
      queue_type = values(queue_type),
      tag_id = values(tag_id),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      lau_material_audit_queue
      (name,biz_type,material_type,pull_num,task_timeout,role_ids,is_deleted,ctime,mtime,seq,queue_type,tag_id)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.name,jdbcType=VARCHAR},
        #{item.bizType,jdbcType=INTEGER},
        #{item.materialType,jdbcType=INTEGER},
        #{item.pullNum,jdbcType=BIGINT},
        #{item.taskTimeout,jdbcType=BIGINT},
        #{item.roleIds,jdbcType=VARCHAR},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.seq,jdbcType=INTEGER},
        #{item.queueType,jdbcType=INTEGER},
        #{item.tagId,jdbcType=INTEGER},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      lau_material_audit_queue
      (name,biz_type,material_type,pull_num,task_timeout,role_ids,is_deleted,ctime,mtime,seq,queue_type,tag_id)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.name,jdbcType=VARCHAR},
        #{item.bizType,jdbcType=INTEGER},
        #{item.materialType,jdbcType=INTEGER},
        #{item.pullNum,jdbcType=BIGINT},
        #{item.taskTimeout,jdbcType=BIGINT},
        #{item.roleIds,jdbcType=VARCHAR},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.seq,jdbcType=INTEGER},
        #{item.queueType,jdbcType=INTEGER},
        #{item.tagId,jdbcType=INTEGER},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      name = values(name),
      biz_type = values(biz_type),
      material_type = values(material_type),
      pull_num = values(pull_num),
      task_timeout = values(task_timeout),
      role_ids = values(role_ids),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      seq = values(seq),
      queue_type = values(queue_type),
      tag_id = values(tag_id),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.risk.po.risk.LauMaterialAuditQueuePo">
    insert into lau_material_audit_queue
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="bizType != null">
        biz_type,
      </if>
      <if test="materialType != null">
        material_type,
      </if>
      <if test="pullNum != null">
        pull_num,
      </if>
      <if test="taskTimeout != null">
        task_timeout,
      </if>
      <if test="roleIds != null">
        role_ids,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="seq != null">
        seq,
      </if>
      <if test="queueType != null">
        queue_type,
      </if>
      <if test="tagId != null">
        tag_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="bizType != null">
        #{bizType,jdbcType=INTEGER},
      </if>
      <if test="materialType != null">
        #{materialType,jdbcType=INTEGER},
      </if>
      <if test="pullNum != null">
        #{pullNum,jdbcType=BIGINT},
      </if>
      <if test="taskTimeout != null">
        #{taskTimeout,jdbcType=BIGINT},
      </if>
      <if test="roleIds != null">
        #{roleIds,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="seq != null">
        #{seq,jdbcType=INTEGER},
      </if>
      <if test="queueType != null">
        #{queueType,jdbcType=INTEGER},
      </if>
      <if test="tagId != null">
        #{tagId,jdbcType=INTEGER},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="name != null">
        name = values(name),
      </if>
      <if test="bizType != null">
        biz_type = values(biz_type),
      </if>
      <if test="materialType != null">
        material_type = values(material_type),
      </if>
      <if test="pullNum != null">
        pull_num = values(pull_num),
      </if>
      <if test="taskTimeout != null">
        task_timeout = values(task_timeout),
      </if>
      <if test="roleIds != null">
        role_ids = values(role_ids),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="seq != null">
        seq = values(seq),
      </if>
      <if test="queueType != null">
        queue_type = values(queue_type),
      </if>
      <if test="tagId != null">
        tag_id = values(tag_id),
      </if>
    </trim>
  </insert>
</mapper>