<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.risk.dao.risk.LauMaterialCreativeRelDao">
  <resultMap id="BaseResultMap" type="com.bilibili.risk.po.risk.LauMaterialCreativeRelPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="material_id" jdbcType="CHAR" property="materialId" />
    <result column="material_md5" jdbcType="VARCHAR" property="materialMd5" />
    <result column="material_type" jdbcType="TINYINT" property="materialType" />
    <result column="creative_id" jdbcType="INTEGER" property="creativeId" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, material_id, material_md5, material_type, creative_id, is_deleted, ctime, mtime
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.risk.po.risk.LauMaterialCreativeRelPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from lau_material_creative_rel
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from lau_material_creative_rel
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from lau_material_creative_rel
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.risk.po.risk.LauMaterialCreativeRelPoExample">
    delete from lau_material_creative_rel
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.risk.po.risk.LauMaterialCreativeRelPo">
    insert into lau_material_creative_rel (material_id, material_md5, material_type,
      creative_id, is_deleted, ctime, 
      mtime)
    values (#{materialId,jdbcType=CHAR}, #{materialMd5,jdbcType=VARCHAR}, #{materialType,jdbcType=TINYINT}, 
      #{creativeId,jdbcType=INTEGER}, #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.risk.po.risk.LauMaterialCreativeRelPo">
    insert into lau_material_creative_rel
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="materialId != null">
        material_id,
      </if>
      <if test="materialMd5 != null">
        material_md5,
      </if>
      <if test="materialType != null">
        material_type,
      </if>
      <if test="creativeId != null">
        creative_id,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="materialId != null">
        #{materialId,jdbcType=CHAR},
      </if>
      <if test="materialMd5 != null">
        #{materialMd5,jdbcType=VARCHAR},
      </if>
      <if test="materialType != null">
        #{materialType,jdbcType=TINYINT},
      </if>
      <if test="creativeId != null">
        #{creativeId,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.risk.po.risk.LauMaterialCreativeRelPoExample" resultType="java.lang.Long">
    select count(*) from lau_material_creative_rel
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update lau_material_creative_rel
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.materialId != null">
        material_id = #{record.materialId,jdbcType=CHAR},
      </if>
      <if test="record.materialMd5 != null">
        material_md5 = #{record.materialMd5,jdbcType=VARCHAR},
      </if>
      <if test="record.materialType != null">
        material_type = #{record.materialType,jdbcType=TINYINT},
      </if>
      <if test="record.creativeId != null">
        creative_id = #{record.creativeId,jdbcType=INTEGER},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update lau_material_creative_rel
    set id = #{record.id,jdbcType=BIGINT},
      material_id = #{record.materialId,jdbcType=CHAR},
      material_md5 = #{record.materialMd5,jdbcType=VARCHAR},
      material_type = #{record.materialType,jdbcType=TINYINT},
      creative_id = #{record.creativeId,jdbcType=INTEGER},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.risk.po.risk.LauMaterialCreativeRelPo">
    update lau_material_creative_rel
    <set>
      <if test="materialId != null">
        material_id = #{materialId,jdbcType=CHAR},
      </if>
      <if test="materialMd5 != null">
        material_md5 = #{materialMd5,jdbcType=VARCHAR},
      </if>
      <if test="materialType != null">
        material_type = #{materialType,jdbcType=TINYINT},
      </if>
      <if test="creativeId != null">
        creative_id = #{creativeId,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.risk.po.risk.LauMaterialCreativeRelPo">
    update lau_material_creative_rel
    set material_id = #{materialId,jdbcType=CHAR},
      material_md5 = #{materialMd5,jdbcType=VARCHAR},
      material_type = #{materialType,jdbcType=TINYINT},
      creative_id = #{creativeId,jdbcType=INTEGER},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.risk.po.risk.LauMaterialCreativeRelPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_material_creative_rel (material_id, material_md5, material_type, 
      creative_id, is_deleted, ctime, 
      mtime)
    values (#{materialId,jdbcType=CHAR}, #{materialMd5,jdbcType=VARCHAR}, #{materialType,jdbcType=TINYINT},
      #{creativeId,jdbcType=BIGINT}, #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      material_md5 = values(material_md5),
      material_type = values(material_type),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      lau_material_creative_rel
      (material_id,material_md5,material_type,creative_id,is_deleted,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.materialId,jdbcType=CHAR},
        #{item.materialMd5,jdbcType=VARCHAR},
        #{item.materialType,jdbcType=TINYINT},
        #{item.creativeId,jdbcType=BIGINT},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      lau_material_creative_rel
      (material_id,material_md5,material_type,creative_id,is_deleted,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.materialId,jdbcType=CHAR},
        #{item.materialMd5,jdbcType=VARCHAR},
        #{item.materialType,jdbcType=TINYINT},
        #{item.creativeId,jdbcType=BIGINT},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      is_deleted = values(is_deleted),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.risk.po.risk.LauMaterialCreativeRelPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_material_creative_rel
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="materialId != null">
        material_id,
      </if>
      <if test="materialMd5 != null">
        material_md5,
      </if>
      <if test="materialType != null">
        material_type,
      </if>
      <if test="creativeId != null">
        creative_id,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="materialId != null">
        #{materialId,jdbcType=CHAR},
      </if>
      <if test="materialMd5 != null">
        #{materialMd5,jdbcType=VARCHAR},
      </if>
      <if test="materialType != null">
        #{materialType,jdbcType=TINYINT},
      </if>
      <if test="creativeId != null">
        #{creativeId,jdbcType=BIGINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="materialMd5 != null">
        material_md5 = values(material_md5),
      </if>
      <if test="materialType != null">
        material_type = values(material_type),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
    </trim>
  </insert>
</mapper>