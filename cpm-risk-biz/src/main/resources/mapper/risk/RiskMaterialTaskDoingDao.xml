<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.risk.dao.risk.RiskMaterialTaskDoingDao">
  <resultMap id="BaseResultMap" type="com.bilibili.risk.po.risk.RiskMaterialTaskDoingPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="username" jdbcType="VARCHAR" property="username" />
    <result column="queue_id" jdbcType="BIGINT" property="queueId" />
    <result column="receive_batch_no" jdbcType="VARCHAR" property="receiveBatchNo" />
    <result column="task_id" jdbcType="CHAR" property="taskId" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, username, queue_id, receive_batch_no, task_id, ctime, mtime
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.risk.po.risk.RiskMaterialTaskDoingPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from risk_material_task_doing
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from risk_material_task_doing
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from risk_material_task_doing
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.risk.po.risk.RiskMaterialTaskDoingPoExample">
    delete from risk_material_task_doing
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.risk.po.risk.RiskMaterialTaskDoingPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into risk_material_task_doing (username, queue_id, receive_batch_no, 
      task_id, ctime, mtime
      )
    values (#{username,jdbcType=VARCHAR}, #{queueId,jdbcType=BIGINT}, #{receiveBatchNo,jdbcType=VARCHAR}, 
      #{taskId,jdbcType=CHAR}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.risk.po.risk.RiskMaterialTaskDoingPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into risk_material_task_doing
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="username != null">
        username,
      </if>
      <if test="queueId != null">
        queue_id,
      </if>
      <if test="receiveBatchNo != null">
        receive_batch_no,
      </if>
      <if test="taskId != null">
        task_id,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="username != null">
        #{username,jdbcType=VARCHAR},
      </if>
      <if test="queueId != null">
        #{queueId,jdbcType=BIGINT},
      </if>
      <if test="receiveBatchNo != null">
        #{receiveBatchNo,jdbcType=VARCHAR},
      </if>
      <if test="taskId != null">
        #{taskId,jdbcType=CHAR},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.risk.po.risk.RiskMaterialTaskDoingPoExample" resultType="java.lang.Long">
    select count(*) from risk_material_task_doing
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update risk_material_task_doing
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.username != null">
        username = #{record.username,jdbcType=VARCHAR},
      </if>
      <if test="record.queueId != null">
        queue_id = #{record.queueId,jdbcType=BIGINT},
      </if>
      <if test="record.receiveBatchNo != null">
        receive_batch_no = #{record.receiveBatchNo,jdbcType=VARCHAR},
      </if>
      <if test="record.taskId != null">
        task_id = #{record.taskId,jdbcType=CHAR},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update risk_material_task_doing
    set id = #{record.id,jdbcType=BIGINT},
      username = #{record.username,jdbcType=VARCHAR},
      queue_id = #{record.queueId,jdbcType=BIGINT},
      receive_batch_no = #{record.receiveBatchNo,jdbcType=VARCHAR},
      task_id = #{record.taskId,jdbcType=CHAR},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.risk.po.risk.RiskMaterialTaskDoingPo">
    update risk_material_task_doing
    <set>
      <if test="username != null">
        username = #{username,jdbcType=VARCHAR},
      </if>
      <if test="queueId != null">
        queue_id = #{queueId,jdbcType=BIGINT},
      </if>
      <if test="receiveBatchNo != null">
        receive_batch_no = #{receiveBatchNo,jdbcType=VARCHAR},
      </if>
      <if test="taskId != null">
        task_id = #{taskId,jdbcType=CHAR},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.risk.po.risk.RiskMaterialTaskDoingPo">
    update risk_material_task_doing
    set username = #{username,jdbcType=VARCHAR},
      queue_id = #{queueId,jdbcType=BIGINT},
      receive_batch_no = #{receiveBatchNo,jdbcType=VARCHAR},
      task_id = #{taskId,jdbcType=CHAR},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.risk.po.risk.RiskMaterialTaskDoingPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into risk_material_task_doing (username, queue_id, receive_batch_no, 
      task_id, ctime, mtime
      )
    values (#{username,jdbcType=VARCHAR}, #{queueId,jdbcType=BIGINT}, #{receiveBatchNo,jdbcType=VARCHAR}, 
      #{taskId,jdbcType=CHAR}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}
      )
    <trim prefix="on duplicate key update" suffixOverrides=",">
      username = values(username),
      queue_id = values(queue_id),
      receive_batch_no = values(receive_batch_no),
      task_id = values(task_id),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      risk_material_task_doing
      (username,queue_id,receive_batch_no,task_id,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.username,jdbcType=VARCHAR},
        #{item.queueId,jdbcType=BIGINT},
        #{item.receiveBatchNo,jdbcType=VARCHAR},
        #{item.taskId,jdbcType=CHAR},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      risk_material_task_doing
      (username,queue_id,receive_batch_no,task_id,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.username,jdbcType=VARCHAR},
        #{item.queueId,jdbcType=BIGINT},
        #{item.receiveBatchNo,jdbcType=VARCHAR},
        #{item.taskId,jdbcType=CHAR},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      username = values(username),
      queue_id = values(queue_id),
      receive_batch_no = values(receive_batch_no),
      task_id = values(task_id),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.risk.po.risk.RiskMaterialTaskDoingPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into risk_material_task_doing
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="username != null">
        username,
      </if>
      <if test="queueId != null">
        queue_id,
      </if>
      <if test="receiveBatchNo != null">
        receive_batch_no,
      </if>
      <if test="taskId != null">
        task_id,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="username != null">
        #{username,jdbcType=VARCHAR},
      </if>
      <if test="queueId != null">
        #{queueId,jdbcType=BIGINT},
      </if>
      <if test="receiveBatchNo != null">
        #{receiveBatchNo,jdbcType=VARCHAR},
      </if>
      <if test="taskId != null">
        #{taskId,jdbcType=CHAR},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="username != null">
        username = values(username),
      </if>
      <if test="queueId != null">
        queue_id = values(queue_id),
      </if>
      <if test="receiveBatchNo != null">
        receive_batch_no = values(receive_batch_no),
      </if>
      <if test="taskId != null">
        task_id = values(task_id),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
    </trim>
  </insert>
</mapper>