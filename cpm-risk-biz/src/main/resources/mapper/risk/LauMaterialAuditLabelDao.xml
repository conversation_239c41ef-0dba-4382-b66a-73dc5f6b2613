<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.risk.dao.risk.LauMaterialAuditLabelDao">
  <resultMap id="BaseResultMap" type="com.bilibili.risk.po.risk.LauMaterialAuditLabelPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="level" jdbcType="BIGINT" property="level" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="pid" jdbcType="BIGINT" property="pid" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="handle_type" jdbcType="INTEGER" property="handleType" />
    <result column="sub_handle_type" jdbcType="INTEGER" property="subHandleType" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, name, level, reason, pid, is_deleted, ctime, mtime, handle_type, sub_handle_type
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.risk.po.risk.LauMaterialAuditLabelPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from lau_material_audit_label
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from lau_material_audit_label
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from lau_material_audit_label
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.risk.po.risk.LauMaterialAuditLabelPoExample">
    delete from lau_material_audit_label
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.risk.po.risk.LauMaterialAuditLabelPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_material_audit_label (name, level, reason, 
      pid, is_deleted, ctime, 
      mtime, handle_type, sub_handle_type
      )
    values (#{name,jdbcType=VARCHAR}, #{level,jdbcType=BIGINT}, #{reason,jdbcType=VARCHAR}, 
      #{pid,jdbcType=BIGINT}, #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP}, #{handleType,jdbcType=INTEGER}, #{subHandleType,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.risk.po.risk.LauMaterialAuditLabelPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_material_audit_label
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null">
        name,
      </if>
      <if test="level != null">
        level,
      </if>
      <if test="reason != null">
        reason,
      </if>
      <if test="pid != null">
        pid,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="handleType != null">
        handle_type,
      </if>
      <if test="subHandleType != null">
        sub_handle_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="level != null">
        #{level,jdbcType=BIGINT},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="pid != null">
        #{pid,jdbcType=BIGINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="handleType != null">
        #{handleType,jdbcType=INTEGER},
      </if>
      <if test="subHandleType != null">
        #{subHandleType,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.risk.po.risk.LauMaterialAuditLabelPoExample" resultType="java.lang.Long">
    select count(*) from lau_material_audit_label
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update lau_material_audit_label
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.level != null">
        level = #{record.level,jdbcType=BIGINT},
      </if>
      <if test="record.reason != null">
        reason = #{record.reason,jdbcType=VARCHAR},
      </if>
      <if test="record.pid != null">
        pid = #{record.pid,jdbcType=BIGINT},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.handleType != null">
        handle_type = #{record.handleType,jdbcType=INTEGER},
      </if>
      <if test="record.subHandleType != null">
        sub_handle_type = #{record.subHandleType,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update lau_material_audit_label
    set id = #{record.id,jdbcType=BIGINT},
      name = #{record.name,jdbcType=VARCHAR},
      level = #{record.level,jdbcType=BIGINT},
      reason = #{record.reason,jdbcType=VARCHAR},
      pid = #{record.pid,jdbcType=BIGINT},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      handle_type = #{record.handleType,jdbcType=INTEGER},
      sub_handle_type = #{record.subHandleType,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.risk.po.risk.LauMaterialAuditLabelPo">
    update lau_material_audit_label
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="level != null">
        level = #{level,jdbcType=BIGINT},
      </if>
      <if test="reason != null">
        reason = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="pid != null">
        pid = #{pid,jdbcType=BIGINT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="handleType != null">
        handle_type = #{handleType,jdbcType=INTEGER},
      </if>
      <if test="subHandleType != null">
        sub_handle_type = #{subHandleType,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.risk.po.risk.LauMaterialAuditLabelPo">
    update lau_material_audit_label
    set name = #{name,jdbcType=VARCHAR},
      level = #{level,jdbcType=BIGINT},
      reason = #{reason,jdbcType=VARCHAR},
      pid = #{pid,jdbcType=BIGINT},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      handle_type = #{handleType,jdbcType=INTEGER},
      sub_handle_type = #{subHandleType,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.risk.po.risk.LauMaterialAuditLabelPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_material_audit_label (name, level, reason, 
      pid, is_deleted, ctime, 
      mtime, handle_type, sub_handle_type
      )
    values (#{name,jdbcType=VARCHAR}, #{level,jdbcType=BIGINT}, #{reason,jdbcType=VARCHAR}, 
      #{pid,jdbcType=BIGINT}, #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP}, #{handleType,jdbcType=INTEGER}, #{subHandleType,jdbcType=INTEGER}
      )
    <trim prefix="on duplicate key update" suffixOverrides=",">
      name = values(name),
      level = values(level),
      reason = values(reason),
      pid = values(pid),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      handle_type = values(handle_type),
      sub_handle_type = values(sub_handle_type),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      lau_material_audit_label
      (name,level,reason,pid,is_deleted,ctime,mtime,handle_type,sub_handle_type)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.name,jdbcType=VARCHAR},
        #{item.level,jdbcType=BIGINT},
        #{item.reason,jdbcType=VARCHAR},
        #{item.pid,jdbcType=BIGINT},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.handleType,jdbcType=INTEGER},
        #{item.subHandleType,jdbcType=INTEGER},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      lau_material_audit_label
      (name,level,reason,pid,is_deleted,ctime,mtime,handle_type,sub_handle_type)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.name,jdbcType=VARCHAR},
        #{item.level,jdbcType=BIGINT},
        #{item.reason,jdbcType=VARCHAR},
        #{item.pid,jdbcType=BIGINT},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.handleType,jdbcType=INTEGER},
        #{item.subHandleType,jdbcType=INTEGER},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      name = values(name),
      level = values(level),
      reason = values(reason),
      pid = values(pid),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      handle_type = values(handle_type),
      sub_handle_type = values(sub_handle_type),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.risk.po.risk.LauMaterialAuditLabelPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_material_audit_label
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null">
        name,
      </if>
      <if test="level != null">
        level,
      </if>
      <if test="reason != null">
        reason,
      </if>
      <if test="pid != null">
        pid,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="handleType != null">
        handle_type,
      </if>
      <if test="subHandleType != null">
        sub_handle_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="level != null">
        #{level,jdbcType=BIGINT},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="pid != null">
        #{pid,jdbcType=BIGINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="handleType != null">
        #{handleType,jdbcType=INTEGER},
      </if>
      <if test="subHandleType != null">
        #{subHandleType,jdbcType=INTEGER},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="name != null">
        name = values(name),
      </if>
      <if test="level != null">
        level = values(level),
      </if>
      <if test="reason != null">
        reason = values(reason),
      </if>
      <if test="pid != null">
        pid = values(pid),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="handleType != null">
        handle_type = values(handle_type),
      </if>
      <if test="subHandleType != null">
        sub_handle_type = values(sub_handle_type),
      </if>
    </trim>
  </insert>
</mapper>