<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.risk.dao.ad.LauStoryCouponComponentDao">
  <resultMap id="BaseResultMap" type="com.bilibili.risk.po.ad.LauStoryCouponComponentPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="account_id" jdbcType="INTEGER" property="accountId" />
    <result column="component_id" jdbcType="BIGINT" property="componentId" />
    <result column="component_name" jdbcType="VARCHAR" property="componentName" />
    <result column="cost_fen" jdbcType="INTEGER" property="costFen" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="comment" jdbcType="VARCHAR" property="comment" />
    <result column="use_period_start" jdbcType="TIMESTAMP" property="usePeriodStart" />
    <result column="use_period_end" jdbcType="TIMESTAMP" property="usePeriodEnd" />
    <result column="obtain_period_start" jdbcType="TIMESTAMP" property="obtainPeriodStart" />
    <result column="obtain_period_end" jdbcType="TIMESTAMP" property="obtainPeriodEnd" />
    <result column="button_id" jdbcType="INTEGER" property="buttonId" />
    <result column="button_type" jdbcType="INTEGER" property="buttonType" />
    <result column="button_text" jdbcType="VARCHAR" property="buttonText" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, ctime, mtime, is_deleted, account_id, component_id, component_name, cost_fen, 
    description, comment, use_period_start, use_period_end, obtain_period_start, obtain_period_end, 
    button_id, button_type, button_text
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.risk.po.ad.LauStoryCouponComponentPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from lau_story_coupon_component
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from lau_story_coupon_component
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from lau_story_coupon_component
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.risk.po.ad.LauStoryCouponComponentPoExample">
    delete from lau_story_coupon_component
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.risk.po.ad.LauStoryCouponComponentPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_story_coupon_component (ctime, mtime, is_deleted, 
      account_id, component_id, component_name, 
      cost_fen, description, comment, 
      use_period_start, use_period_end, obtain_period_start, 
      obtain_period_end, button_id, button_type, 
      button_text)
    values (#{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=TINYINT}, 
      #{accountId,jdbcType=INTEGER}, #{componentId,jdbcType=BIGINT}, #{componentName,jdbcType=VARCHAR}, 
      #{costFen,jdbcType=INTEGER}, #{description,jdbcType=VARCHAR}, #{comment,jdbcType=VARCHAR}, 
      #{usePeriodStart,jdbcType=TIMESTAMP}, #{usePeriodEnd,jdbcType=TIMESTAMP}, #{obtainPeriodStart,jdbcType=TIMESTAMP}, 
      #{obtainPeriodEnd,jdbcType=TIMESTAMP}, #{buttonId,jdbcType=INTEGER}, #{buttonType,jdbcType=INTEGER}, 
      #{buttonText,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.risk.po.ad.LauStoryCouponComponentPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_story_coupon_component
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="componentId != null">
        component_id,
      </if>
      <if test="componentName != null">
        component_name,
      </if>
      <if test="costFen != null">
        cost_fen,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="comment != null">
        comment,
      </if>
      <if test="usePeriodStart != null">
        use_period_start,
      </if>
      <if test="usePeriodEnd != null">
        use_period_end,
      </if>
      <if test="obtainPeriodStart != null">
        obtain_period_start,
      </if>
      <if test="obtainPeriodEnd != null">
        obtain_period_end,
      </if>
      <if test="buttonId != null">
        button_id,
      </if>
      <if test="buttonType != null">
        button_type,
      </if>
      <if test="buttonText != null">
        button_text,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="componentId != null">
        #{componentId,jdbcType=BIGINT},
      </if>
      <if test="componentName != null">
        #{componentName,jdbcType=VARCHAR},
      </if>
      <if test="costFen != null">
        #{costFen,jdbcType=INTEGER},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="comment != null">
        #{comment,jdbcType=VARCHAR},
      </if>
      <if test="usePeriodStart != null">
        #{usePeriodStart,jdbcType=TIMESTAMP},
      </if>
      <if test="usePeriodEnd != null">
        #{usePeriodEnd,jdbcType=TIMESTAMP},
      </if>
      <if test="obtainPeriodStart != null">
        #{obtainPeriodStart,jdbcType=TIMESTAMP},
      </if>
      <if test="obtainPeriodEnd != null">
        #{obtainPeriodEnd,jdbcType=TIMESTAMP},
      </if>
      <if test="buttonId != null">
        #{buttonId,jdbcType=INTEGER},
      </if>
      <if test="buttonType != null">
        #{buttonType,jdbcType=INTEGER},
      </if>
      <if test="buttonText != null">
        #{buttonText,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.risk.po.ad.LauStoryCouponComponentPoExample" resultType="java.lang.Long">
    select count(*) from lau_story_coupon_component
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update lau_story_coupon_component
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.accountId != null">
        account_id = #{record.accountId,jdbcType=INTEGER},
      </if>
      <if test="record.componentId != null">
        component_id = #{record.componentId,jdbcType=BIGINT},
      </if>
      <if test="record.componentName != null">
        component_name = #{record.componentName,jdbcType=VARCHAR},
      </if>
      <if test="record.costFen != null">
        cost_fen = #{record.costFen,jdbcType=INTEGER},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.comment != null">
        comment = #{record.comment,jdbcType=VARCHAR},
      </if>
      <if test="record.usePeriodStart != null">
        use_period_start = #{record.usePeriodStart,jdbcType=TIMESTAMP},
      </if>
      <if test="record.usePeriodEnd != null">
        use_period_end = #{record.usePeriodEnd,jdbcType=TIMESTAMP},
      </if>
      <if test="record.obtainPeriodStart != null">
        obtain_period_start = #{record.obtainPeriodStart,jdbcType=TIMESTAMP},
      </if>
      <if test="record.obtainPeriodEnd != null">
        obtain_period_end = #{record.obtainPeriodEnd,jdbcType=TIMESTAMP},
      </if>
      <if test="record.buttonId != null">
        button_id = #{record.buttonId,jdbcType=INTEGER},
      </if>
      <if test="record.buttonType != null">
        button_type = #{record.buttonType,jdbcType=INTEGER},
      </if>
      <if test="record.buttonText != null">
        button_text = #{record.buttonText,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update lau_story_coupon_component
    set id = #{record.id,jdbcType=BIGINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      account_id = #{record.accountId,jdbcType=INTEGER},
      component_id = #{record.componentId,jdbcType=BIGINT},
      component_name = #{record.componentName,jdbcType=VARCHAR},
      cost_fen = #{record.costFen,jdbcType=INTEGER},
      description = #{record.description,jdbcType=VARCHAR},
      comment = #{record.comment,jdbcType=VARCHAR},
      use_period_start = #{record.usePeriodStart,jdbcType=TIMESTAMP},
      use_period_end = #{record.usePeriodEnd,jdbcType=TIMESTAMP},
      obtain_period_start = #{record.obtainPeriodStart,jdbcType=TIMESTAMP},
      obtain_period_end = #{record.obtainPeriodEnd,jdbcType=TIMESTAMP},
      button_id = #{record.buttonId,jdbcType=INTEGER},
      button_type = #{record.buttonType,jdbcType=INTEGER},
      button_text = #{record.buttonText,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.risk.po.ad.LauStoryCouponComponentPo">
    update lau_story_coupon_component
    <set>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=INTEGER},
      </if>
      <if test="componentId != null">
        component_id = #{componentId,jdbcType=BIGINT},
      </if>
      <if test="componentName != null">
        component_name = #{componentName,jdbcType=VARCHAR},
      </if>
      <if test="costFen != null">
        cost_fen = #{costFen,jdbcType=INTEGER},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="comment != null">
        comment = #{comment,jdbcType=VARCHAR},
      </if>
      <if test="usePeriodStart != null">
        use_period_start = #{usePeriodStart,jdbcType=TIMESTAMP},
      </if>
      <if test="usePeriodEnd != null">
        use_period_end = #{usePeriodEnd,jdbcType=TIMESTAMP},
      </if>
      <if test="obtainPeriodStart != null">
        obtain_period_start = #{obtainPeriodStart,jdbcType=TIMESTAMP},
      </if>
      <if test="obtainPeriodEnd != null">
        obtain_period_end = #{obtainPeriodEnd,jdbcType=TIMESTAMP},
      </if>
      <if test="buttonId != null">
        button_id = #{buttonId,jdbcType=INTEGER},
      </if>
      <if test="buttonType != null">
        button_type = #{buttonType,jdbcType=INTEGER},
      </if>
      <if test="buttonText != null">
        button_text = #{buttonText,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.risk.po.ad.LauStoryCouponComponentPo">
    update lau_story_coupon_component
    set ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      account_id = #{accountId,jdbcType=INTEGER},
      component_id = #{componentId,jdbcType=BIGINT},
      component_name = #{componentName,jdbcType=VARCHAR},
      cost_fen = #{costFen,jdbcType=INTEGER},
      description = #{description,jdbcType=VARCHAR},
      comment = #{comment,jdbcType=VARCHAR},
      use_period_start = #{usePeriodStart,jdbcType=TIMESTAMP},
      use_period_end = #{usePeriodEnd,jdbcType=TIMESTAMP},
      obtain_period_start = #{obtainPeriodStart,jdbcType=TIMESTAMP},
      obtain_period_end = #{obtainPeriodEnd,jdbcType=TIMESTAMP},
      button_id = #{buttonId,jdbcType=INTEGER},
      button_type = #{buttonType,jdbcType=INTEGER},
      button_text = #{buttonText,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.risk.po.ad.LauStoryCouponComponentPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_story_coupon_component (ctime, mtime, is_deleted, 
      account_id, component_id, component_name, 
      cost_fen, description, comment, 
      use_period_start, use_period_end, obtain_period_start, 
      obtain_period_end, button_id, button_type, 
      button_text)
    values (#{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=TINYINT}, 
      #{accountId,jdbcType=INTEGER}, #{componentId,jdbcType=BIGINT}, #{componentName,jdbcType=VARCHAR}, 
      #{costFen,jdbcType=INTEGER}, #{description,jdbcType=VARCHAR}, #{comment,jdbcType=VARCHAR}, 
      #{usePeriodStart,jdbcType=TIMESTAMP}, #{usePeriodEnd,jdbcType=TIMESTAMP}, #{obtainPeriodStart,jdbcType=TIMESTAMP}, 
      #{obtainPeriodEnd,jdbcType=TIMESTAMP}, #{buttonId,jdbcType=INTEGER}, #{buttonType,jdbcType=INTEGER}, 
      #{buttonText,jdbcType=VARCHAR})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
      account_id = values(account_id),
      component_id = values(component_id),
      component_name = values(component_name),
      cost_fen = values(cost_fen),
      description = values(description),
      comment = values(comment),
      use_period_start = values(use_period_start),
      use_period_end = values(use_period_end),
      obtain_period_start = values(obtain_period_start),
      obtain_period_end = values(obtain_period_end),
      button_id = values(button_id),
      button_type = values(button_type),
      button_text = values(button_text),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      lau_story_coupon_component
      (ctime,mtime,is_deleted,account_id,component_id,component_name,cost_fen,description,comment,use_period_start,use_period_end,obtain_period_start,obtain_period_end,button_id,button_type,button_text)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.accountId,jdbcType=INTEGER},
        #{item.componentId,jdbcType=BIGINT},
        #{item.componentName,jdbcType=VARCHAR},
        #{item.costFen,jdbcType=INTEGER},
        #{item.description,jdbcType=VARCHAR},
        #{item.comment,jdbcType=VARCHAR},
        #{item.usePeriodStart,jdbcType=TIMESTAMP},
        #{item.usePeriodEnd,jdbcType=TIMESTAMP},
        #{item.obtainPeriodStart,jdbcType=TIMESTAMP},
        #{item.obtainPeriodEnd,jdbcType=TIMESTAMP},
        #{item.buttonId,jdbcType=INTEGER},
        #{item.buttonType,jdbcType=INTEGER},
        #{item.buttonText,jdbcType=VARCHAR},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      lau_story_coupon_component
      (ctime,mtime,is_deleted,account_id,component_id,component_name,cost_fen,description,comment,use_period_start,use_period_end,obtain_period_start,obtain_period_end,button_id,button_type,button_text)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.accountId,jdbcType=INTEGER},
        #{item.componentId,jdbcType=BIGINT},
        #{item.componentName,jdbcType=VARCHAR},
        #{item.costFen,jdbcType=INTEGER},
        #{item.description,jdbcType=VARCHAR},
        #{item.comment,jdbcType=VARCHAR},
        #{item.usePeriodStart,jdbcType=TIMESTAMP},
        #{item.usePeriodEnd,jdbcType=TIMESTAMP},
        #{item.obtainPeriodStart,jdbcType=TIMESTAMP},
        #{item.obtainPeriodEnd,jdbcType=TIMESTAMP},
        #{item.buttonId,jdbcType=INTEGER},
        #{item.buttonType,jdbcType=INTEGER},
        #{item.buttonText,jdbcType=VARCHAR},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
      account_id = values(account_id),
      component_id = values(component_id),
      component_name = values(component_name),
      cost_fen = values(cost_fen),
      description = values(description),
      comment = values(comment),
      use_period_start = values(use_period_start),
      use_period_end = values(use_period_end),
      obtain_period_start = values(obtain_period_start),
      obtain_period_end = values(obtain_period_end),
      button_id = values(button_id),
      button_type = values(button_type),
      button_text = values(button_text),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.risk.po.ad.LauStoryCouponComponentPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_story_coupon_component
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="componentId != null">
        component_id,
      </if>
      <if test="componentName != null">
        component_name,
      </if>
      <if test="costFen != null">
        cost_fen,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="comment != null">
        comment,
      </if>
      <if test="usePeriodStart != null">
        use_period_start,
      </if>
      <if test="usePeriodEnd != null">
        use_period_end,
      </if>
      <if test="obtainPeriodStart != null">
        obtain_period_start,
      </if>
      <if test="obtainPeriodEnd != null">
        obtain_period_end,
      </if>
      <if test="buttonId != null">
        button_id,
      </if>
      <if test="buttonType != null">
        button_type,
      </if>
      <if test="buttonText != null">
        button_text,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="componentId != null">
        #{componentId,jdbcType=BIGINT},
      </if>
      <if test="componentName != null">
        #{componentName,jdbcType=VARCHAR},
      </if>
      <if test="costFen != null">
        #{costFen,jdbcType=INTEGER},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="comment != null">
        #{comment,jdbcType=VARCHAR},
      </if>
      <if test="usePeriodStart != null">
        #{usePeriodStart,jdbcType=TIMESTAMP},
      </if>
      <if test="usePeriodEnd != null">
        #{usePeriodEnd,jdbcType=TIMESTAMP},
      </if>
      <if test="obtainPeriodStart != null">
        #{obtainPeriodStart,jdbcType=TIMESTAMP},
      </if>
      <if test="obtainPeriodEnd != null">
        #{obtainPeriodEnd,jdbcType=TIMESTAMP},
      </if>
      <if test="buttonId != null">
        #{buttonId,jdbcType=INTEGER},
      </if>
      <if test="buttonType != null">
        #{buttonType,jdbcType=INTEGER},
      </if>
      <if test="buttonText != null">
        #{buttonText,jdbcType=VARCHAR},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="accountId != null">
        account_id = values(account_id),
      </if>
      <if test="componentId != null">
        component_id = values(component_id),
      </if>
      <if test="componentName != null">
        component_name = values(component_name),
      </if>
      <if test="costFen != null">
        cost_fen = values(cost_fen),
      </if>
      <if test="description != null">
        description = values(description),
      </if>
      <if test="comment != null">
        comment = values(comment),
      </if>
      <if test="usePeriodStart != null">
        use_period_start = values(use_period_start),
      </if>
      <if test="usePeriodEnd != null">
        use_period_end = values(use_period_end),
      </if>
      <if test="obtainPeriodStart != null">
        obtain_period_start = values(obtain_period_start),
      </if>
      <if test="obtainPeriodEnd != null">
        obtain_period_end = values(obtain_period_end),
      </if>
      <if test="buttonId != null">
        button_id = values(button_id),
      </if>
      <if test="buttonType != null">
        button_type = values(button_type),
      </if>
      <if test="buttonText != null">
        button_text = values(button_text),
      </if>
    </trim>
  </insert>
</mapper>