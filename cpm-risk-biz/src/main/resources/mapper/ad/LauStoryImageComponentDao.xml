<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.risk.dao.ad.LauStoryImageComponentDao">
  <resultMap id="BaseResultMap" type="com.bilibili.risk.po.ad.LauStoryImageComponentPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="account_id" jdbcType="INTEGER" property="accountId" />
    <result column="component_id" jdbcType="BIGINT" property="componentId" />
    <result column="component_name" jdbcType="VARCHAR" property="componentName" />
    <result column="image_url" jdbcType="VARCHAR" property="imageUrl" />
    <result column="image_md5" jdbcType="VARCHAR" property="imageMd5" />
    <result column="is_contains_goods" jdbcType="TINYINT" property="isContainsGoods" />
    <result column="item_id" jdbcType="BIGINT" property="itemId" />
    <result column="item_source" jdbcType="INTEGER" property="itemSource" />
    <result column="jump_type" jdbcType="INTEGER" property="jumpType" />
    <result column="jump_url" jdbcType="VARCHAR" property="jumpUrl" />
    <result column="schema_url" jdbcType="VARCHAR" property="schemaUrl" />
    <result column="mini_program_id" jdbcType="VARCHAR" property="miniProgramId" />
    <result column="mini_program_name" jdbcType="VARCHAR" property="miniProgramName" />
    <result column="mini_program_path" jdbcType="VARCHAR" property="miniProgramPath" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, ctime, mtime, is_deleted, account_id, component_id, component_name, image_url, 
    image_md5, is_contains_goods, item_id, item_source, jump_type, jump_url, schema_url, 
    mini_program_id, mini_program_name, mini_program_path
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.risk.po.ad.LauStoryImageComponentPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from lau_story_image_component
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from lau_story_image_component
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from lau_story_image_component
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.risk.po.ad.LauStoryImageComponentPoExample">
    delete from lau_story_image_component
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.risk.po.ad.LauStoryImageComponentPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_story_image_component (ctime, mtime, is_deleted, 
      account_id, component_id, component_name, 
      image_url, image_md5, is_contains_goods, 
      item_id, item_source, jump_type, 
      jump_url, schema_url, mini_program_id, 
      mini_program_name, mini_program_path)
    values (#{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=TINYINT}, 
      #{accountId,jdbcType=INTEGER}, #{componentId,jdbcType=BIGINT}, #{componentName,jdbcType=VARCHAR}, 
      #{imageUrl,jdbcType=VARCHAR}, #{imageMd5,jdbcType=VARCHAR}, #{isContainsGoods,jdbcType=TINYINT}, 
      #{itemId,jdbcType=BIGINT}, #{itemSource,jdbcType=INTEGER}, #{jumpType,jdbcType=INTEGER}, 
      #{jumpUrl,jdbcType=VARCHAR}, #{schemaUrl,jdbcType=VARCHAR}, #{miniProgramId,jdbcType=VARCHAR}, 
      #{miniProgramName,jdbcType=VARCHAR}, #{miniProgramPath,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.risk.po.ad.LauStoryImageComponentPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_story_image_component
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="componentId != null">
        component_id,
      </if>
      <if test="componentName != null">
        component_name,
      </if>
      <if test="imageUrl != null">
        image_url,
      </if>
      <if test="imageMd5 != null">
        image_md5,
      </if>
      <if test="isContainsGoods != null">
        is_contains_goods,
      </if>
      <if test="itemId != null">
        item_id,
      </if>
      <if test="itemSource != null">
        item_source,
      </if>
      <if test="jumpType != null">
        jump_type,
      </if>
      <if test="jumpUrl != null">
        jump_url,
      </if>
      <if test="schemaUrl != null">
        schema_url,
      </if>
      <if test="miniProgramId != null">
        mini_program_id,
      </if>
      <if test="miniProgramName != null">
        mini_program_name,
      </if>
      <if test="miniProgramPath != null">
        mini_program_path,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="componentId != null">
        #{componentId,jdbcType=BIGINT},
      </if>
      <if test="componentName != null">
        #{componentName,jdbcType=VARCHAR},
      </if>
      <if test="imageUrl != null">
        #{imageUrl,jdbcType=VARCHAR},
      </if>
      <if test="imageMd5 != null">
        #{imageMd5,jdbcType=VARCHAR},
      </if>
      <if test="isContainsGoods != null">
        #{isContainsGoods,jdbcType=TINYINT},
      </if>
      <if test="itemId != null">
        #{itemId,jdbcType=BIGINT},
      </if>
      <if test="itemSource != null">
        #{itemSource,jdbcType=INTEGER},
      </if>
      <if test="jumpType != null">
        #{jumpType,jdbcType=INTEGER},
      </if>
      <if test="jumpUrl != null">
        #{jumpUrl,jdbcType=VARCHAR},
      </if>
      <if test="schemaUrl != null">
        #{schemaUrl,jdbcType=VARCHAR},
      </if>
      <if test="miniProgramId != null">
        #{miniProgramId,jdbcType=VARCHAR},
      </if>
      <if test="miniProgramName != null">
        #{miniProgramName,jdbcType=VARCHAR},
      </if>
      <if test="miniProgramPath != null">
        #{miniProgramPath,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.risk.po.ad.LauStoryImageComponentPoExample" resultType="java.lang.Long">
    select count(*) from lau_story_image_component
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update lau_story_image_component
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.accountId != null">
        account_id = #{record.accountId,jdbcType=INTEGER},
      </if>
      <if test="record.componentId != null">
        component_id = #{record.componentId,jdbcType=BIGINT},
      </if>
      <if test="record.componentName != null">
        component_name = #{record.componentName,jdbcType=VARCHAR},
      </if>
      <if test="record.imageUrl != null">
        image_url = #{record.imageUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.imageMd5 != null">
        image_md5 = #{record.imageMd5,jdbcType=VARCHAR},
      </if>
      <if test="record.isContainsGoods != null">
        is_contains_goods = #{record.isContainsGoods,jdbcType=TINYINT},
      </if>
      <if test="record.itemId != null">
        item_id = #{record.itemId,jdbcType=BIGINT},
      </if>
      <if test="record.itemSource != null">
        item_source = #{record.itemSource,jdbcType=INTEGER},
      </if>
      <if test="record.jumpType != null">
        jump_type = #{record.jumpType,jdbcType=INTEGER},
      </if>
      <if test="record.jumpUrl != null">
        jump_url = #{record.jumpUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.schemaUrl != null">
        schema_url = #{record.schemaUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.miniProgramId != null">
        mini_program_id = #{record.miniProgramId,jdbcType=VARCHAR},
      </if>
      <if test="record.miniProgramName != null">
        mini_program_name = #{record.miniProgramName,jdbcType=VARCHAR},
      </if>
      <if test="record.miniProgramPath != null">
        mini_program_path = #{record.miniProgramPath,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update lau_story_image_component
    set id = #{record.id,jdbcType=BIGINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      account_id = #{record.accountId,jdbcType=INTEGER},
      component_id = #{record.componentId,jdbcType=BIGINT},
      component_name = #{record.componentName,jdbcType=VARCHAR},
      image_url = #{record.imageUrl,jdbcType=VARCHAR},
      image_md5 = #{record.imageMd5,jdbcType=VARCHAR},
      is_contains_goods = #{record.isContainsGoods,jdbcType=TINYINT},
      item_id = #{record.itemId,jdbcType=BIGINT},
      item_source = #{record.itemSource,jdbcType=INTEGER},
      jump_type = #{record.jumpType,jdbcType=INTEGER},
      jump_url = #{record.jumpUrl,jdbcType=VARCHAR},
      schema_url = #{record.schemaUrl,jdbcType=VARCHAR},
      mini_program_id = #{record.miniProgramId,jdbcType=VARCHAR},
      mini_program_name = #{record.miniProgramName,jdbcType=VARCHAR},
      mini_program_path = #{record.miniProgramPath,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.risk.po.ad.LauStoryImageComponentPo">
    update lau_story_image_component
    <set>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=INTEGER},
      </if>
      <if test="componentId != null">
        component_id = #{componentId,jdbcType=BIGINT},
      </if>
      <if test="componentName != null">
        component_name = #{componentName,jdbcType=VARCHAR},
      </if>
      <if test="imageUrl != null">
        image_url = #{imageUrl,jdbcType=VARCHAR},
      </if>
      <if test="imageMd5 != null">
        image_md5 = #{imageMd5,jdbcType=VARCHAR},
      </if>
      <if test="isContainsGoods != null">
        is_contains_goods = #{isContainsGoods,jdbcType=TINYINT},
      </if>
      <if test="itemId != null">
        item_id = #{itemId,jdbcType=BIGINT},
      </if>
      <if test="itemSource != null">
        item_source = #{itemSource,jdbcType=INTEGER},
      </if>
      <if test="jumpType != null">
        jump_type = #{jumpType,jdbcType=INTEGER},
      </if>
      <if test="jumpUrl != null">
        jump_url = #{jumpUrl,jdbcType=VARCHAR},
      </if>
      <if test="schemaUrl != null">
        schema_url = #{schemaUrl,jdbcType=VARCHAR},
      </if>
      <if test="miniProgramId != null">
        mini_program_id = #{miniProgramId,jdbcType=VARCHAR},
      </if>
      <if test="miniProgramName != null">
        mini_program_name = #{miniProgramName,jdbcType=VARCHAR},
      </if>
      <if test="miniProgramPath != null">
        mini_program_path = #{miniProgramPath,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.risk.po.ad.LauStoryImageComponentPo">
    update lau_story_image_component
    set ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      account_id = #{accountId,jdbcType=INTEGER},
      component_id = #{componentId,jdbcType=BIGINT},
      component_name = #{componentName,jdbcType=VARCHAR},
      image_url = #{imageUrl,jdbcType=VARCHAR},
      image_md5 = #{imageMd5,jdbcType=VARCHAR},
      is_contains_goods = #{isContainsGoods,jdbcType=TINYINT},
      item_id = #{itemId,jdbcType=BIGINT},
      item_source = #{itemSource,jdbcType=INTEGER},
      jump_type = #{jumpType,jdbcType=INTEGER},
      jump_url = #{jumpUrl,jdbcType=VARCHAR},
      schema_url = #{schemaUrl,jdbcType=VARCHAR},
      mini_program_id = #{miniProgramId,jdbcType=VARCHAR},
      mini_program_name = #{miniProgramName,jdbcType=VARCHAR},
      mini_program_path = #{miniProgramPath,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.risk.po.ad.LauStoryImageComponentPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_story_image_component (ctime, mtime, is_deleted, 
      account_id, component_id, component_name, 
      image_url, image_md5, is_contains_goods, 
      item_id, item_source, jump_type, 
      jump_url, schema_url, mini_program_id, 
      mini_program_name, mini_program_path)
    values (#{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=TINYINT}, 
      #{accountId,jdbcType=INTEGER}, #{componentId,jdbcType=BIGINT}, #{componentName,jdbcType=VARCHAR}, 
      #{imageUrl,jdbcType=VARCHAR}, #{imageMd5,jdbcType=VARCHAR}, #{isContainsGoods,jdbcType=TINYINT}, 
      #{itemId,jdbcType=BIGINT}, #{itemSource,jdbcType=INTEGER}, #{jumpType,jdbcType=INTEGER}, 
      #{jumpUrl,jdbcType=VARCHAR}, #{schemaUrl,jdbcType=VARCHAR}, #{miniProgramId,jdbcType=VARCHAR}, 
      #{miniProgramName,jdbcType=VARCHAR}, #{miniProgramPath,jdbcType=VARCHAR})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
      account_id = values(account_id),
      component_id = values(component_id),
      component_name = values(component_name),
      image_url = values(image_url),
      image_md5 = values(image_md5),
      is_contains_goods = values(is_contains_goods),
      item_id = values(item_id),
      item_source = values(item_source),
      jump_type = values(jump_type),
      jump_url = values(jump_url),
      schema_url = values(schema_url),
      mini_program_id = values(mini_program_id),
      mini_program_name = values(mini_program_name),
      mini_program_path = values(mini_program_path),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      lau_story_image_component
      (ctime,mtime,is_deleted,account_id,component_id,component_name,image_url,image_md5,is_contains_goods,item_id,item_source,jump_type,jump_url,schema_url,mini_program_id,mini_program_name,mini_program_path)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.accountId,jdbcType=INTEGER},
        #{item.componentId,jdbcType=BIGINT},
        #{item.componentName,jdbcType=VARCHAR},
        #{item.imageUrl,jdbcType=VARCHAR},
        #{item.imageMd5,jdbcType=VARCHAR},
        #{item.isContainsGoods,jdbcType=TINYINT},
        #{item.itemId,jdbcType=BIGINT},
        #{item.itemSource,jdbcType=INTEGER},
        #{item.jumpType,jdbcType=INTEGER},
        #{item.jumpUrl,jdbcType=VARCHAR},
        #{item.schemaUrl,jdbcType=VARCHAR},
        #{item.miniProgramId,jdbcType=VARCHAR},
        #{item.miniProgramName,jdbcType=VARCHAR},
        #{item.miniProgramPath,jdbcType=VARCHAR},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      lau_story_image_component
      (ctime,mtime,is_deleted,account_id,component_id,component_name,image_url,image_md5,is_contains_goods,item_id,item_source,jump_type,jump_url,schema_url,mini_program_id,mini_program_name,mini_program_path)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.accountId,jdbcType=INTEGER},
        #{item.componentId,jdbcType=BIGINT},
        #{item.componentName,jdbcType=VARCHAR},
        #{item.imageUrl,jdbcType=VARCHAR},
        #{item.imageMd5,jdbcType=VARCHAR},
        #{item.isContainsGoods,jdbcType=TINYINT},
        #{item.itemId,jdbcType=BIGINT},
        #{item.itemSource,jdbcType=INTEGER},
        #{item.jumpType,jdbcType=INTEGER},
        #{item.jumpUrl,jdbcType=VARCHAR},
        #{item.schemaUrl,jdbcType=VARCHAR},
        #{item.miniProgramId,jdbcType=VARCHAR},
        #{item.miniProgramName,jdbcType=VARCHAR},
        #{item.miniProgramPath,jdbcType=VARCHAR},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
      account_id = values(account_id),
      component_id = values(component_id),
      component_name = values(component_name),
      image_url = values(image_url),
      image_md5 = values(image_md5),
      is_contains_goods = values(is_contains_goods),
      item_id = values(item_id),
      item_source = values(item_source),
      jump_type = values(jump_type),
      jump_url = values(jump_url),
      schema_url = values(schema_url),
      mini_program_id = values(mini_program_id),
      mini_program_name = values(mini_program_name),
      mini_program_path = values(mini_program_path),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.risk.po.ad.LauStoryImageComponentPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_story_image_component
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="componentId != null">
        component_id,
      </if>
      <if test="componentName != null">
        component_name,
      </if>
      <if test="imageUrl != null">
        image_url,
      </if>
      <if test="imageMd5 != null">
        image_md5,
      </if>
      <if test="isContainsGoods != null">
        is_contains_goods,
      </if>
      <if test="itemId != null">
        item_id,
      </if>
      <if test="itemSource != null">
        item_source,
      </if>
      <if test="jumpType != null">
        jump_type,
      </if>
      <if test="jumpUrl != null">
        jump_url,
      </if>
      <if test="schemaUrl != null">
        schema_url,
      </if>
      <if test="miniProgramId != null">
        mini_program_id,
      </if>
      <if test="miniProgramName != null">
        mini_program_name,
      </if>
      <if test="miniProgramPath != null">
        mini_program_path,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="componentId != null">
        #{componentId,jdbcType=BIGINT},
      </if>
      <if test="componentName != null">
        #{componentName,jdbcType=VARCHAR},
      </if>
      <if test="imageUrl != null">
        #{imageUrl,jdbcType=VARCHAR},
      </if>
      <if test="imageMd5 != null">
        #{imageMd5,jdbcType=VARCHAR},
      </if>
      <if test="isContainsGoods != null">
        #{isContainsGoods,jdbcType=TINYINT},
      </if>
      <if test="itemId != null">
        #{itemId,jdbcType=BIGINT},
      </if>
      <if test="itemSource != null">
        #{itemSource,jdbcType=INTEGER},
      </if>
      <if test="jumpType != null">
        #{jumpType,jdbcType=INTEGER},
      </if>
      <if test="jumpUrl != null">
        #{jumpUrl,jdbcType=VARCHAR},
      </if>
      <if test="schemaUrl != null">
        #{schemaUrl,jdbcType=VARCHAR},
      </if>
      <if test="miniProgramId != null">
        #{miniProgramId,jdbcType=VARCHAR},
      </if>
      <if test="miniProgramName != null">
        #{miniProgramName,jdbcType=VARCHAR},
      </if>
      <if test="miniProgramPath != null">
        #{miniProgramPath,jdbcType=VARCHAR},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="accountId != null">
        account_id = values(account_id),
      </if>
      <if test="componentId != null">
        component_id = values(component_id),
      </if>
      <if test="componentName != null">
        component_name = values(component_name),
      </if>
      <if test="imageUrl != null">
        image_url = values(image_url),
      </if>
      <if test="imageMd5 != null">
        image_md5 = values(image_md5),
      </if>
      <if test="isContainsGoods != null">
        is_contains_goods = values(is_contains_goods),
      </if>
      <if test="itemId != null">
        item_id = values(item_id),
      </if>
      <if test="itemSource != null">
        item_source = values(item_source),
      </if>
      <if test="jumpType != null">
        jump_type = values(jump_type),
      </if>
      <if test="jumpUrl != null">
        jump_url = values(jump_url),
      </if>
      <if test="schemaUrl != null">
        schema_url = values(schema_url),
      </if>
      <if test="miniProgramId != null">
        mini_program_id = values(mini_program_id),
      </if>
      <if test="miniProgramName != null">
        mini_program_name = values(mini_program_name),
      </if>
      <if test="miniProgramPath != null">
        mini_program_path = values(mini_program_path),
      </if>
    </trim>
  </insert>
</mapper>