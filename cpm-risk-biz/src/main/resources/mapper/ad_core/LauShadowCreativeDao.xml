<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.risk.dao.ad_core.LauShadowCreativeDao">
  <resultMap id="BaseResultMap" type="com.bilibili.risk.po.ad.LauShadowCreativePo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="account_id" jdbcType="INTEGER" property="accountId" />
    <result column="campaign_id" jdbcType="INTEGER" property="campaignId" />
    <result column="unit_id" jdbcType="INTEGER" property="unitId" />
    <result column="creative_id" jdbcType="INTEGER" property="creativeId" />
    <result column="mgk_page_id" jdbcType="BIGINT" property="mgkPageId" />
    <result column="audit_status" jdbcType="TINYINT" property="auditStatus" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="creative_status" jdbcType="TINYINT" property="creativeStatus" />
    <result column="page_group_id" jdbcType="BIGINT" property="pageGroupId" />
    <result column="video_id" jdbcType="BIGINT" property="videoId" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.bilibili.risk.po.ad.LauShadowCreativePo">
    <result column="shadow_creative" jdbcType="LONGVARCHAR" property="shadowCreative" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, account_id, campaign_id, unit_id, creative_id, mgk_page_id, audit_status, reason, 
    ctime, mtime, creative_status, page_group_id, video_id
  </sql>
  <sql id="Blob_Column_List">
    shadow_creative
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.bilibili.risk.po.ad.LauShadowCreativePoExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from lau_shadow_creative
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByExample" parameterType="com.bilibili.risk.po.ad.LauShadowCreativePoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from lau_shadow_creative
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from lau_shadow_creative
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from lau_shadow_creative
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.risk.po.ad.LauShadowCreativePoExample">
    delete from lau_shadow_creative
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.risk.po.ad.LauShadowCreativePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_shadow_creative (account_id, campaign_id, unit_id, 
      creative_id, mgk_page_id, audit_status, 
      reason, ctime, mtime, 
      creative_status, page_group_id, video_id, 
      shadow_creative)
    values (#{accountId,jdbcType=INTEGER}, #{campaignId,jdbcType=INTEGER}, #{unitId,jdbcType=INTEGER}, 
      #{creativeId,jdbcType=INTEGER}, #{mgkPageId,jdbcType=BIGINT}, #{auditStatus,jdbcType=TINYINT}, 
      #{reason,jdbcType=VARCHAR}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, 
      #{creativeStatus,jdbcType=TINYINT}, #{pageGroupId,jdbcType=BIGINT}, #{videoId,jdbcType=BIGINT}, 
      #{shadowCreative,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.risk.po.ad.LauShadowCreativePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_shadow_creative
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        account_id,
      </if>
      <if test="campaignId != null">
        campaign_id,
      </if>
      <if test="unitId != null">
        unit_id,
      </if>
      <if test="creativeId != null">
        creative_id,
      </if>
      <if test="mgkPageId != null">
        mgk_page_id,
      </if>
      <if test="auditStatus != null">
        audit_status,
      </if>
      <if test="reason != null">
        reason,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="creativeStatus != null">
        creative_status,
      </if>
      <if test="pageGroupId != null">
        page_group_id,
      </if>
      <if test="videoId != null">
        video_id,
      </if>
      <if test="shadowCreative != null">
        shadow_creative,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="campaignId != null">
        #{campaignId,jdbcType=INTEGER},
      </if>
      <if test="unitId != null">
        #{unitId,jdbcType=INTEGER},
      </if>
      <if test="creativeId != null">
        #{creativeId,jdbcType=INTEGER},
      </if>
      <if test="mgkPageId != null">
        #{mgkPageId,jdbcType=BIGINT},
      </if>
      <if test="auditStatus != null">
        #{auditStatus,jdbcType=TINYINT},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="creativeStatus != null">
        #{creativeStatus,jdbcType=TINYINT},
      </if>
      <if test="pageGroupId != null">
        #{pageGroupId,jdbcType=BIGINT},
      </if>
      <if test="videoId != null">
        #{videoId,jdbcType=BIGINT},
      </if>
      <if test="shadowCreative != null">
        #{shadowCreative,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.risk.po.ad.LauShadowCreativePoExample" resultType="java.lang.Long">
    select count(*) from lau_shadow_creative
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update lau_shadow_creative
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.accountId != null">
        account_id = #{record.accountId,jdbcType=INTEGER},
      </if>
      <if test="record.campaignId != null">
        campaign_id = #{record.campaignId,jdbcType=INTEGER},
      </if>
      <if test="record.unitId != null">
        unit_id = #{record.unitId,jdbcType=INTEGER},
      </if>
      <if test="record.creativeId != null">
        creative_id = #{record.creativeId,jdbcType=INTEGER},
      </if>
      <if test="record.mgkPageId != null">
        mgk_page_id = #{record.mgkPageId,jdbcType=BIGINT},
      </if>
      <if test="record.auditStatus != null">
        audit_status = #{record.auditStatus,jdbcType=TINYINT},
      </if>
      <if test="record.reason != null">
        reason = #{record.reason,jdbcType=VARCHAR},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.creativeStatus != null">
        creative_status = #{record.creativeStatus,jdbcType=TINYINT},
      </if>
      <if test="record.pageGroupId != null">
        page_group_id = #{record.pageGroupId,jdbcType=BIGINT},
      </if>
      <if test="record.videoId != null">
        video_id = #{record.videoId,jdbcType=BIGINT},
      </if>
      <if test="record.shadowCreative != null">
        shadow_creative = #{record.shadowCreative,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update lau_shadow_creative
    set id = #{record.id,jdbcType=BIGINT},
      account_id = #{record.accountId,jdbcType=INTEGER},
      campaign_id = #{record.campaignId,jdbcType=INTEGER},
      unit_id = #{record.unitId,jdbcType=INTEGER},
      creative_id = #{record.creativeId,jdbcType=INTEGER},
      mgk_page_id = #{record.mgkPageId,jdbcType=BIGINT},
      audit_status = #{record.auditStatus,jdbcType=TINYINT},
      reason = #{record.reason,jdbcType=VARCHAR},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      creative_status = #{record.creativeStatus,jdbcType=TINYINT},
      page_group_id = #{record.pageGroupId,jdbcType=BIGINT},
      video_id = #{record.videoId,jdbcType=BIGINT},
      shadow_creative = #{record.shadowCreative,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update lau_shadow_creative
    set id = #{record.id,jdbcType=BIGINT},
      account_id = #{record.accountId,jdbcType=INTEGER},
      campaign_id = #{record.campaignId,jdbcType=INTEGER},
      unit_id = #{record.unitId,jdbcType=INTEGER},
      creative_id = #{record.creativeId,jdbcType=INTEGER},
      mgk_page_id = #{record.mgkPageId,jdbcType=BIGINT},
      audit_status = #{record.auditStatus,jdbcType=TINYINT},
      reason = #{record.reason,jdbcType=VARCHAR},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      creative_status = #{record.creativeStatus,jdbcType=TINYINT},
      page_group_id = #{record.pageGroupId,jdbcType=BIGINT},
      video_id = #{record.videoId,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.risk.po.ad.LauShadowCreativePo">
    update lau_shadow_creative
    <set>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=INTEGER},
      </if>
      <if test="campaignId != null">
        campaign_id = #{campaignId,jdbcType=INTEGER},
      </if>
      <if test="unitId != null">
        unit_id = #{unitId,jdbcType=INTEGER},
      </if>
      <if test="creativeId != null">
        creative_id = #{creativeId,jdbcType=INTEGER},
      </if>
      <if test="mgkPageId != null">
        mgk_page_id = #{mgkPageId,jdbcType=BIGINT},
      </if>
      <if test="auditStatus != null">
        audit_status = #{auditStatus,jdbcType=TINYINT},
      </if>
      <if test="reason != null">
        reason = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="creativeStatus != null">
        creative_status = #{creativeStatus,jdbcType=TINYINT},
      </if>
      <if test="pageGroupId != null">
        page_group_id = #{pageGroupId,jdbcType=BIGINT},
      </if>
      <if test="videoId != null">
        video_id = #{videoId,jdbcType=BIGINT},
      </if>
      <if test="shadowCreative != null">
        shadow_creative = #{shadowCreative,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.bilibili.risk.po.ad.LauShadowCreativePo">
    update lau_shadow_creative
    set account_id = #{accountId,jdbcType=INTEGER},
      campaign_id = #{campaignId,jdbcType=INTEGER},
      unit_id = #{unitId,jdbcType=INTEGER},
      creative_id = #{creativeId,jdbcType=INTEGER},
      mgk_page_id = #{mgkPageId,jdbcType=BIGINT},
      audit_status = #{auditStatus,jdbcType=TINYINT},
      reason = #{reason,jdbcType=VARCHAR},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      creative_status = #{creativeStatus,jdbcType=TINYINT},
      page_group_id = #{pageGroupId,jdbcType=BIGINT},
      video_id = #{videoId,jdbcType=BIGINT},
      shadow_creative = #{shadowCreative,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.risk.po.ad.LauShadowCreativePo">
    update lau_shadow_creative
    set account_id = #{accountId,jdbcType=INTEGER},
      campaign_id = #{campaignId,jdbcType=INTEGER},
      unit_id = #{unitId,jdbcType=INTEGER},
      creative_id = #{creativeId,jdbcType=INTEGER},
      mgk_page_id = #{mgkPageId,jdbcType=BIGINT},
      audit_status = #{auditStatus,jdbcType=TINYINT},
      reason = #{reason,jdbcType=VARCHAR},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      creative_status = #{creativeStatus,jdbcType=TINYINT},
      page_group_id = #{pageGroupId,jdbcType=BIGINT},
      video_id = #{videoId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.risk.po.ad.LauShadowCreativePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_shadow_creative (account_id, campaign_id, unit_id, 
      creative_id, mgk_page_id, audit_status, 
      reason, ctime, mtime, 
      creative_status, page_group_id, video_id, 
      shadow_creative)
    values (#{accountId,jdbcType=INTEGER}, #{campaignId,jdbcType=INTEGER}, #{unitId,jdbcType=INTEGER}, 
      #{creativeId,jdbcType=INTEGER}, #{mgkPageId,jdbcType=BIGINT}, #{auditStatus,jdbcType=TINYINT}, 
      #{reason,jdbcType=VARCHAR}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, 
      #{creativeStatus,jdbcType=TINYINT}, #{pageGroupId,jdbcType=BIGINT}, #{videoId,jdbcType=BIGINT}, 
      #{shadowCreative,jdbcType=LONGVARCHAR})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      account_id = values(account_id),
      campaign_id = values(campaign_id),
      unit_id = values(unit_id),
      creative_id = values(creative_id),
      mgk_page_id = values(mgk_page_id),
      audit_status = values(audit_status),
      reason = values(reason),
      ctime = values(ctime),
      mtime = values(mtime),
      creative_status = values(creative_status),
      page_group_id = values(page_group_id),
      video_id = values(video_id),
      shadow_creative = values(shadow_creative),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      lau_shadow_creative
      (account_id,campaign_id,unit_id,creative_id,mgk_page_id,audit_status,reason,ctime,mtime,creative_status,page_group_id,video_id,shadow_creative)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.accountId,jdbcType=INTEGER},
        #{item.campaignId,jdbcType=INTEGER},
        #{item.unitId,jdbcType=INTEGER},
        #{item.creativeId,jdbcType=INTEGER},
        #{item.mgkPageId,jdbcType=BIGINT},
        #{item.auditStatus,jdbcType=TINYINT},
        #{item.reason,jdbcType=VARCHAR},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.creativeStatus,jdbcType=TINYINT},
        #{item.pageGroupId,jdbcType=BIGINT},
        #{item.videoId,jdbcType=BIGINT},
        #{item.shadowCreative,jdbcType=LONGVARCHAR},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      lau_shadow_creative
      (account_id,campaign_id,unit_id,creative_id,mgk_page_id,audit_status,reason,ctime,mtime,creative_status,page_group_id,video_id,shadow_creative)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.accountId,jdbcType=INTEGER},
        #{item.campaignId,jdbcType=INTEGER},
        #{item.unitId,jdbcType=INTEGER},
        #{item.creativeId,jdbcType=INTEGER},
        #{item.mgkPageId,jdbcType=BIGINT},
        #{item.auditStatus,jdbcType=TINYINT},
        #{item.reason,jdbcType=VARCHAR},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.creativeStatus,jdbcType=TINYINT},
        #{item.pageGroupId,jdbcType=BIGINT},
        #{item.videoId,jdbcType=BIGINT},
        #{item.shadowCreative,jdbcType=LONGVARCHAR},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      account_id = values(account_id),
      campaign_id = values(campaign_id),
      unit_id = values(unit_id),
      creative_id = values(creative_id),
      mgk_page_id = values(mgk_page_id),
      audit_status = values(audit_status),
      reason = values(reason),
      ctime = values(ctime),
      mtime = values(mtime),
      creative_status = values(creative_status),
      page_group_id = values(page_group_id),
      video_id = values(video_id),
      shadow_creative = values(shadow_creative),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.risk.po.ad.LauShadowCreativePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_shadow_creative
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        account_id,
      </if>
      <if test="campaignId != null">
        campaign_id,
      </if>
      <if test="unitId != null">
        unit_id,
      </if>
      <if test="creativeId != null">
        creative_id,
      </if>
      <if test="mgkPageId != null">
        mgk_page_id,
      </if>
      <if test="auditStatus != null">
        audit_status,
      </if>
      <if test="reason != null">
        reason,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="creativeStatus != null">
        creative_status,
      </if>
      <if test="pageGroupId != null">
        page_group_id,
      </if>
      <if test="videoId != null">
        video_id,
      </if>
      <if test="shadowCreative != null">
        shadow_creative,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="campaignId != null">
        #{campaignId,jdbcType=INTEGER},
      </if>
      <if test="unitId != null">
        #{unitId,jdbcType=INTEGER},
      </if>
      <if test="creativeId != null">
        #{creativeId,jdbcType=INTEGER},
      </if>
      <if test="mgkPageId != null">
        #{mgkPageId,jdbcType=BIGINT},
      </if>
      <if test="auditStatus != null">
        #{auditStatus,jdbcType=TINYINT},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="creativeStatus != null">
        #{creativeStatus,jdbcType=TINYINT},
      </if>
      <if test="pageGroupId != null">
        #{pageGroupId,jdbcType=BIGINT},
      </if>
      <if test="videoId != null">
        #{videoId,jdbcType=BIGINT},
      </if>
      <if test="shadowCreative != null">
        #{shadowCreative,jdbcType=LONGVARCHAR},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="accountId != null">
        account_id = values(account_id),
      </if>
      <if test="campaignId != null">
        campaign_id = values(campaign_id),
      </if>
      <if test="unitId != null">
        unit_id = values(unit_id),
      </if>
      <if test="creativeId != null">
        creative_id = values(creative_id),
      </if>
      <if test="mgkPageId != null">
        mgk_page_id = values(mgk_page_id),
      </if>
      <if test="auditStatus != null">
        audit_status = values(audit_status),
      </if>
      <if test="reason != null">
        reason = values(reason),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="creativeStatus != null">
        creative_status = values(creative_status),
      </if>
      <if test="pageGroupId != null">
        page_group_id = values(page_group_id),
      </if>
      <if test="videoId != null">
        video_id = values(video_id),
      </if>
      <if test="shadowCreative != null">
        shadow_creative = values(shadow_creative),
      </if>
    </trim>
  </insert>
</mapper>