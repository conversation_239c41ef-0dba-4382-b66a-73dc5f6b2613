<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.risk.dao.ad_core.LauCreativeComponentDao">
  <resultMap id="BaseResultMap" type="com.bilibili.risk.po.ad.LauCreativeComponentPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="account_id" jdbcType="INTEGER" property="accountId" />
    <result column="campaign_id" jdbcType="INTEGER" property="campaignId" />
    <result column="unit_id" jdbcType="INTEGER" property="unitId" />
    <result column="creative_id" jdbcType="INTEGER" property="creativeId" />
    <result column="component_type" jdbcType="TINYINT" property="componentType" />
    <result column="component_id" jdbcType="BIGINT" property="componentId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, ctime, mtime, is_deleted, account_id, campaign_id, unit_id, creative_id, component_type, 
    component_id
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.risk.po.ad.LauCreativeComponentPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from lau_creative_component
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from lau_creative_component
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from lau_creative_component
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.risk.po.ad.LauCreativeComponentPoExample">
    delete from lau_creative_component
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.risk.po.ad.LauCreativeComponentPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_creative_component (ctime, mtime, is_deleted, 
      account_id, campaign_id, unit_id, 
      creative_id, component_type, component_id
      )
    values (#{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=TINYINT}, 
      #{accountId,jdbcType=INTEGER}, #{campaignId,jdbcType=INTEGER}, #{unitId,jdbcType=INTEGER}, 
      #{creativeId,jdbcType=INTEGER}, #{componentType,jdbcType=TINYINT}, #{componentId,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.risk.po.ad.LauCreativeComponentPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_creative_component
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="campaignId != null">
        campaign_id,
      </if>
      <if test="unitId != null">
        unit_id,
      </if>
      <if test="creativeId != null">
        creative_id,
      </if>
      <if test="componentType != null">
        component_type,
      </if>
      <if test="componentId != null">
        component_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="campaignId != null">
        #{campaignId,jdbcType=INTEGER},
      </if>
      <if test="unitId != null">
        #{unitId,jdbcType=INTEGER},
      </if>
      <if test="creativeId != null">
        #{creativeId,jdbcType=INTEGER},
      </if>
      <if test="componentType != null">
        #{componentType,jdbcType=TINYINT},
      </if>
      <if test="componentId != null">
        #{componentId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.risk.po.ad.LauCreativeComponentPoExample" resultType="java.lang.Long">
    select count(*) from lau_creative_component
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update lau_creative_component
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.accountId != null">
        account_id = #{record.accountId,jdbcType=INTEGER},
      </if>
      <if test="record.campaignId != null">
        campaign_id = #{record.campaignId,jdbcType=INTEGER},
      </if>
      <if test="record.unitId != null">
        unit_id = #{record.unitId,jdbcType=INTEGER},
      </if>
      <if test="record.creativeId != null">
        creative_id = #{record.creativeId,jdbcType=INTEGER},
      </if>
      <if test="record.componentType != null">
        component_type = #{record.componentType,jdbcType=TINYINT},
      </if>
      <if test="record.componentId != null">
        component_id = #{record.componentId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update lau_creative_component
    set id = #{record.id,jdbcType=BIGINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      account_id = #{record.accountId,jdbcType=INTEGER},
      campaign_id = #{record.campaignId,jdbcType=INTEGER},
      unit_id = #{record.unitId,jdbcType=INTEGER},
      creative_id = #{record.creativeId,jdbcType=INTEGER},
      component_type = #{record.componentType,jdbcType=TINYINT},
      component_id = #{record.componentId,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.risk.po.ad.LauCreativeComponentPo">
    update lau_creative_component
    <set>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=INTEGER},
      </if>
      <if test="campaignId != null">
        campaign_id = #{campaignId,jdbcType=INTEGER},
      </if>
      <if test="unitId != null">
        unit_id = #{unitId,jdbcType=INTEGER},
      </if>
      <if test="creativeId != null">
        creative_id = #{creativeId,jdbcType=INTEGER},
      </if>
      <if test="componentType != null">
        component_type = #{componentType,jdbcType=TINYINT},
      </if>
      <if test="componentId != null">
        component_id = #{componentId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.risk.po.ad.LauCreativeComponentPo">
    update lau_creative_component
    set ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      account_id = #{accountId,jdbcType=INTEGER},
      campaign_id = #{campaignId,jdbcType=INTEGER},
      unit_id = #{unitId,jdbcType=INTEGER},
      creative_id = #{creativeId,jdbcType=INTEGER},
      component_type = #{componentType,jdbcType=TINYINT},
      component_id = #{componentId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.risk.po.ad.LauCreativeComponentPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_creative_component (ctime, mtime, is_deleted, 
      account_id, campaign_id, unit_id, 
      creative_id, component_type, component_id
      )
    values (#{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=TINYINT}, 
      #{accountId,jdbcType=INTEGER}, #{campaignId,jdbcType=INTEGER}, #{unitId,jdbcType=INTEGER}, 
      #{creativeId,jdbcType=INTEGER}, #{componentType,jdbcType=TINYINT}, #{componentId,jdbcType=BIGINT}
      )
    <trim prefix="on duplicate key update" suffixOverrides=",">
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
      account_id = values(account_id),
      campaign_id = values(campaign_id),
      unit_id = values(unit_id),
      creative_id = values(creative_id),
      component_type = values(component_type),
      component_id = values(component_id),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      lau_creative_component
      (ctime,mtime,is_deleted,account_id,campaign_id,unit_id,creative_id,component_type,component_id)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.accountId,jdbcType=INTEGER},
        #{item.campaignId,jdbcType=INTEGER},
        #{item.unitId,jdbcType=INTEGER},
        #{item.creativeId,jdbcType=INTEGER},
        #{item.componentType,jdbcType=TINYINT},
        #{item.componentId,jdbcType=BIGINT},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      lau_creative_component
      (ctime,mtime,is_deleted,account_id,campaign_id,unit_id,creative_id,component_type,component_id)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.accountId,jdbcType=INTEGER},
        #{item.campaignId,jdbcType=INTEGER},
        #{item.unitId,jdbcType=INTEGER},
        #{item.creativeId,jdbcType=INTEGER},
        #{item.componentType,jdbcType=TINYINT},
        #{item.componentId,jdbcType=BIGINT},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
      account_id = values(account_id),
      campaign_id = values(campaign_id),
      unit_id = values(unit_id),
      creative_id = values(creative_id),
      component_type = values(component_type),
      component_id = values(component_id),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.risk.po.ad.LauCreativeComponentPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_creative_component
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="campaignId != null">
        campaign_id,
      </if>
      <if test="unitId != null">
        unit_id,
      </if>
      <if test="creativeId != null">
        creative_id,
      </if>
      <if test="componentType != null">
        component_type,
      </if>
      <if test="componentId != null">
        component_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="campaignId != null">
        #{campaignId,jdbcType=INTEGER},
      </if>
      <if test="unitId != null">
        #{unitId,jdbcType=INTEGER},
      </if>
      <if test="creativeId != null">
        #{creativeId,jdbcType=INTEGER},
      </if>
      <if test="componentType != null">
        #{componentType,jdbcType=TINYINT},
      </if>
      <if test="componentId != null">
        #{componentId,jdbcType=BIGINT},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="accountId != null">
        account_id = values(account_id),
      </if>
      <if test="campaignId != null">
        campaign_id = values(campaign_id),
      </if>
      <if test="unitId != null">
        unit_id = values(unit_id),
      </if>
      <if test="creativeId != null">
        creative_id = values(creative_id),
      </if>
      <if test="componentType != null">
        component_type = values(component_type),
      </if>
      <if test="componentId != null">
        component_id = values(component_id),
      </if>
    </trim>
  </insert>
</mapper>