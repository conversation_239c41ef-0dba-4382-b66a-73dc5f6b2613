<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.risk.dao.ad_core.LauUnitExtraDao">
  <resultMap id="BaseResultMap" type="com.bilibili.risk.po.ad.LauUnitExtraPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="account_id" jdbcType="INTEGER" property="accountId" />
    <result column="unit_id" jdbcType="INTEGER" property="unitId" />
    <result column="is_bili_native" jdbcType="TINYINT" property="isBiliNative" />
    <result column="tags" jdbcType="VARCHAR" property="tags" />
    <result column="device_app_store" jdbcType="VARCHAR" property="deviceAppStore" />
    <result column="smart_key_word" jdbcType="TINYINT" property="smartKeyWord" />
    <result column="android_app_package_id" jdbcType="INTEGER" property="androidAppPackageId" />
    <result column="ios_app_package_id" jdbcType="INTEGER" property="iosAppPackageId" />
    <result column="parent_unit_id" jdbcType="INTEGER" property="parentUnitId" />
    <result column="allow_aigc_replace_material" jdbcType="TINYINT" property="allowAigcReplaceMaterial" />
    <result column="search_first_price_coefficient" jdbcType="INTEGER" property="searchFirstPriceCoefficient" />
    <result column="general_version" jdbcType="TINYINT" property="generalVersion" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, ctime, mtime, account_id, unit_id, is_bili_native, tags, device_app_store, smart_key_word, 
    android_app_package_id, ios_app_package_id, parent_unit_id, allow_aigc_replace_material, 
    search_first_price_coefficient, general_version
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.risk.po.ad.LauUnitExtraPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from lau_unit_extra
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from lau_unit_extra
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from lau_unit_extra
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.risk.po.ad.LauUnitExtraPoExample">
    delete from lau_unit_extra
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.risk.po.ad.LauUnitExtraPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_unit_extra (ctime, mtime, account_id, 
      unit_id, is_bili_native, tags, 
      device_app_store, smart_key_word, android_app_package_id, 
      ios_app_package_id, parent_unit_id, allow_aigc_replace_material, 
      search_first_price_coefficient, general_version
      )
    values (#{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{accountId,jdbcType=INTEGER}, 
      #{unitId,jdbcType=INTEGER}, #{isBiliNative,jdbcType=TINYINT}, #{tags,jdbcType=VARCHAR}, 
      #{deviceAppStore,jdbcType=VARCHAR}, #{smartKeyWord,jdbcType=TINYINT}, #{androidAppPackageId,jdbcType=INTEGER}, 
      #{iosAppPackageId,jdbcType=INTEGER}, #{parentUnitId,jdbcType=INTEGER}, #{allowAigcReplaceMaterial,jdbcType=TINYINT}, 
      #{searchFirstPriceCoefficient,jdbcType=INTEGER}, #{generalVersion,jdbcType=TINYINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.risk.po.ad.LauUnitExtraPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_unit_extra
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="unitId != null">
        unit_id,
      </if>
      <if test="isBiliNative != null">
        is_bili_native,
      </if>
      <if test="tags != null">
        tags,
      </if>
      <if test="deviceAppStore != null">
        device_app_store,
      </if>
      <if test="smartKeyWord != null">
        smart_key_word,
      </if>
      <if test="androidAppPackageId != null">
        android_app_package_id,
      </if>
      <if test="iosAppPackageId != null">
        ios_app_package_id,
      </if>
      <if test="parentUnitId != null">
        parent_unit_id,
      </if>
      <if test="allowAigcReplaceMaterial != null">
        allow_aigc_replace_material,
      </if>
      <if test="searchFirstPriceCoefficient != null">
        search_first_price_coefficient,
      </if>
      <if test="generalVersion != null">
        general_version,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="unitId != null">
        #{unitId,jdbcType=INTEGER},
      </if>
      <if test="isBiliNative != null">
        #{isBiliNative,jdbcType=TINYINT},
      </if>
      <if test="tags != null">
        #{tags,jdbcType=VARCHAR},
      </if>
      <if test="deviceAppStore != null">
        #{deviceAppStore,jdbcType=VARCHAR},
      </if>
      <if test="smartKeyWord != null">
        #{smartKeyWord,jdbcType=TINYINT},
      </if>
      <if test="androidAppPackageId != null">
        #{androidAppPackageId,jdbcType=INTEGER},
      </if>
      <if test="iosAppPackageId != null">
        #{iosAppPackageId,jdbcType=INTEGER},
      </if>
      <if test="parentUnitId != null">
        #{parentUnitId,jdbcType=INTEGER},
      </if>
      <if test="allowAigcReplaceMaterial != null">
        #{allowAigcReplaceMaterial,jdbcType=TINYINT},
      </if>
      <if test="searchFirstPriceCoefficient != null">
        #{searchFirstPriceCoefficient,jdbcType=INTEGER},
      </if>
      <if test="generalVersion != null">
        #{generalVersion,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.risk.po.ad.LauUnitExtraPoExample" resultType="java.lang.Long">
    select count(*) from lau_unit_extra
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update lau_unit_extra
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.accountId != null">
        account_id = #{record.accountId,jdbcType=INTEGER},
      </if>
      <if test="record.unitId != null">
        unit_id = #{record.unitId,jdbcType=INTEGER},
      </if>
      <if test="record.isBiliNative != null">
        is_bili_native = #{record.isBiliNative,jdbcType=TINYINT},
      </if>
      <if test="record.tags != null">
        tags = #{record.tags,jdbcType=VARCHAR},
      </if>
      <if test="record.deviceAppStore != null">
        device_app_store = #{record.deviceAppStore,jdbcType=VARCHAR},
      </if>
      <if test="record.smartKeyWord != null">
        smart_key_word = #{record.smartKeyWord,jdbcType=TINYINT},
      </if>
      <if test="record.androidAppPackageId != null">
        android_app_package_id = #{record.androidAppPackageId,jdbcType=INTEGER},
      </if>
      <if test="record.iosAppPackageId != null">
        ios_app_package_id = #{record.iosAppPackageId,jdbcType=INTEGER},
      </if>
      <if test="record.parentUnitId != null">
        parent_unit_id = #{record.parentUnitId,jdbcType=INTEGER},
      </if>
      <if test="record.allowAigcReplaceMaterial != null">
        allow_aigc_replace_material = #{record.allowAigcReplaceMaterial,jdbcType=TINYINT},
      </if>
      <if test="record.searchFirstPriceCoefficient != null">
        search_first_price_coefficient = #{record.searchFirstPriceCoefficient,jdbcType=INTEGER},
      </if>
      <if test="record.generalVersion != null">
        general_version = #{record.generalVersion,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update lau_unit_extra
    set id = #{record.id,jdbcType=BIGINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      account_id = #{record.accountId,jdbcType=INTEGER},
      unit_id = #{record.unitId,jdbcType=INTEGER},
      is_bili_native = #{record.isBiliNative,jdbcType=TINYINT},
      tags = #{record.tags,jdbcType=VARCHAR},
      device_app_store = #{record.deviceAppStore,jdbcType=VARCHAR},
      smart_key_word = #{record.smartKeyWord,jdbcType=TINYINT},
      android_app_package_id = #{record.androidAppPackageId,jdbcType=INTEGER},
      ios_app_package_id = #{record.iosAppPackageId,jdbcType=INTEGER},
      parent_unit_id = #{record.parentUnitId,jdbcType=INTEGER},
      allow_aigc_replace_material = #{record.allowAigcReplaceMaterial,jdbcType=TINYINT},
      search_first_price_coefficient = #{record.searchFirstPriceCoefficient,jdbcType=INTEGER},
      general_version = #{record.generalVersion,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.risk.po.ad.LauUnitExtraPo">
    update lau_unit_extra
    <set>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=INTEGER},
      </if>
      <if test="unitId != null">
        unit_id = #{unitId,jdbcType=INTEGER},
      </if>
      <if test="isBiliNative != null">
        is_bili_native = #{isBiliNative,jdbcType=TINYINT},
      </if>
      <if test="tags != null">
        tags = #{tags,jdbcType=VARCHAR},
      </if>
      <if test="deviceAppStore != null">
        device_app_store = #{deviceAppStore,jdbcType=VARCHAR},
      </if>
      <if test="smartKeyWord != null">
        smart_key_word = #{smartKeyWord,jdbcType=TINYINT},
      </if>
      <if test="androidAppPackageId != null">
        android_app_package_id = #{androidAppPackageId,jdbcType=INTEGER},
      </if>
      <if test="iosAppPackageId != null">
        ios_app_package_id = #{iosAppPackageId,jdbcType=INTEGER},
      </if>
      <if test="parentUnitId != null">
        parent_unit_id = #{parentUnitId,jdbcType=INTEGER},
      </if>
      <if test="allowAigcReplaceMaterial != null">
        allow_aigc_replace_material = #{allowAigcReplaceMaterial,jdbcType=TINYINT},
      </if>
      <if test="searchFirstPriceCoefficient != null">
        search_first_price_coefficient = #{searchFirstPriceCoefficient,jdbcType=INTEGER},
      </if>
      <if test="generalVersion != null">
        general_version = #{generalVersion,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.risk.po.ad.LauUnitExtraPo">
    update lau_unit_extra
    set ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      account_id = #{accountId,jdbcType=INTEGER},
      unit_id = #{unitId,jdbcType=INTEGER},
      is_bili_native = #{isBiliNative,jdbcType=TINYINT},
      tags = #{tags,jdbcType=VARCHAR},
      device_app_store = #{deviceAppStore,jdbcType=VARCHAR},
      smart_key_word = #{smartKeyWord,jdbcType=TINYINT},
      android_app_package_id = #{androidAppPackageId,jdbcType=INTEGER},
      ios_app_package_id = #{iosAppPackageId,jdbcType=INTEGER},
      parent_unit_id = #{parentUnitId,jdbcType=INTEGER},
      allow_aigc_replace_material = #{allowAigcReplaceMaterial,jdbcType=TINYINT},
      search_first_price_coefficient = #{searchFirstPriceCoefficient,jdbcType=INTEGER},
      general_version = #{generalVersion,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.risk.po.ad.LauUnitExtraPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_unit_extra (ctime, mtime, account_id, 
      unit_id, is_bili_native, tags, 
      device_app_store, smart_key_word, android_app_package_id, 
      ios_app_package_id, parent_unit_id, allow_aigc_replace_material, 
      search_first_price_coefficient, general_version
      )
    values (#{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{accountId,jdbcType=INTEGER}, 
      #{unitId,jdbcType=INTEGER}, #{isBiliNative,jdbcType=TINYINT}, #{tags,jdbcType=VARCHAR}, 
      #{deviceAppStore,jdbcType=VARCHAR}, #{smartKeyWord,jdbcType=TINYINT}, #{androidAppPackageId,jdbcType=INTEGER}, 
      #{iosAppPackageId,jdbcType=INTEGER}, #{parentUnitId,jdbcType=INTEGER}, #{allowAigcReplaceMaterial,jdbcType=TINYINT}, 
      #{searchFirstPriceCoefficient,jdbcType=INTEGER}, #{generalVersion,jdbcType=TINYINT}
      )
    <trim prefix="on duplicate key update" suffixOverrides=",">
      ctime = values(ctime),
      mtime = values(mtime),
      account_id = values(account_id),
      unit_id = values(unit_id),
      is_bili_native = values(is_bili_native),
      tags = values(tags),
      device_app_store = values(device_app_store),
      smart_key_word = values(smart_key_word),
      android_app_package_id = values(android_app_package_id),
      ios_app_package_id = values(ios_app_package_id),
      parent_unit_id = values(parent_unit_id),
      allow_aigc_replace_material = values(allow_aigc_replace_material),
      search_first_price_coefficient = values(search_first_price_coefficient),
      general_version = values(general_version),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      lau_unit_extra
      (ctime,mtime,account_id,unit_id,is_bili_native,tags,device_app_store,smart_key_word,android_app_package_id,ios_app_package_id,parent_unit_id,allow_aigc_replace_material,search_first_price_coefficient,general_version)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.accountId,jdbcType=INTEGER},
        #{item.unitId,jdbcType=INTEGER},
        #{item.isBiliNative,jdbcType=TINYINT},
        #{item.tags,jdbcType=VARCHAR},
        #{item.deviceAppStore,jdbcType=VARCHAR},
        #{item.smartKeyWord,jdbcType=TINYINT},
        #{item.androidAppPackageId,jdbcType=INTEGER},
        #{item.iosAppPackageId,jdbcType=INTEGER},
        #{item.parentUnitId,jdbcType=INTEGER},
        #{item.allowAigcReplaceMaterial,jdbcType=TINYINT},
        #{item.searchFirstPriceCoefficient,jdbcType=INTEGER},
        #{item.generalVersion,jdbcType=TINYINT},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      lau_unit_extra
      (ctime,mtime,account_id,unit_id,is_bili_native,tags,device_app_store,smart_key_word,android_app_package_id,ios_app_package_id,parent_unit_id,allow_aigc_replace_material,search_first_price_coefficient,general_version)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.accountId,jdbcType=INTEGER},
        #{item.unitId,jdbcType=INTEGER},
        #{item.isBiliNative,jdbcType=TINYINT},
        #{item.tags,jdbcType=VARCHAR},
        #{item.deviceAppStore,jdbcType=VARCHAR},
        #{item.smartKeyWord,jdbcType=TINYINT},
        #{item.androidAppPackageId,jdbcType=INTEGER},
        #{item.iosAppPackageId,jdbcType=INTEGER},
        #{item.parentUnitId,jdbcType=INTEGER},
        #{item.allowAigcReplaceMaterial,jdbcType=TINYINT},
        #{item.searchFirstPriceCoefficient,jdbcType=INTEGER},
        #{item.generalVersion,jdbcType=TINYINT},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      ctime = values(ctime),
      mtime = values(mtime),
      account_id = values(account_id),
      unit_id = values(unit_id),
      is_bili_native = values(is_bili_native),
      tags = values(tags),
      device_app_store = values(device_app_store),
      smart_key_word = values(smart_key_word),
      android_app_package_id = values(android_app_package_id),
      ios_app_package_id = values(ios_app_package_id),
      parent_unit_id = values(parent_unit_id),
      allow_aigc_replace_material = values(allow_aigc_replace_material),
      search_first_price_coefficient = values(search_first_price_coefficient),
      general_version = values(general_version),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.risk.po.ad.LauUnitExtraPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_unit_extra
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="unitId != null">
        unit_id,
      </if>
      <if test="isBiliNative != null">
        is_bili_native,
      </if>
      <if test="tags != null">
        tags,
      </if>
      <if test="deviceAppStore != null">
        device_app_store,
      </if>
      <if test="smartKeyWord != null">
        smart_key_word,
      </if>
      <if test="androidAppPackageId != null">
        android_app_package_id,
      </if>
      <if test="iosAppPackageId != null">
        ios_app_package_id,
      </if>
      <if test="parentUnitId != null">
        parent_unit_id,
      </if>
      <if test="allowAigcReplaceMaterial != null">
        allow_aigc_replace_material,
      </if>
      <if test="searchFirstPriceCoefficient != null">
        search_first_price_coefficient,
      </if>
      <if test="generalVersion != null">
        general_version,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="unitId != null">
        #{unitId,jdbcType=INTEGER},
      </if>
      <if test="isBiliNative != null">
        #{isBiliNative,jdbcType=TINYINT},
      </if>
      <if test="tags != null">
        #{tags,jdbcType=VARCHAR},
      </if>
      <if test="deviceAppStore != null">
        #{deviceAppStore,jdbcType=VARCHAR},
      </if>
      <if test="smartKeyWord != null">
        #{smartKeyWord,jdbcType=TINYINT},
      </if>
      <if test="androidAppPackageId != null">
        #{androidAppPackageId,jdbcType=INTEGER},
      </if>
      <if test="iosAppPackageId != null">
        #{iosAppPackageId,jdbcType=INTEGER},
      </if>
      <if test="parentUnitId != null">
        #{parentUnitId,jdbcType=INTEGER},
      </if>
      <if test="allowAigcReplaceMaterial != null">
        #{allowAigcReplaceMaterial,jdbcType=TINYINT},
      </if>
      <if test="searchFirstPriceCoefficient != null">
        #{searchFirstPriceCoefficient,jdbcType=INTEGER},
      </if>
      <if test="generalVersion != null">
        #{generalVersion,jdbcType=TINYINT},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="accountId != null">
        account_id = values(account_id),
      </if>
      <if test="unitId != null">
        unit_id = values(unit_id),
      </if>
      <if test="isBiliNative != null">
        is_bili_native = values(is_bili_native),
      </if>
      <if test="tags != null">
        tags = values(tags),
      </if>
      <if test="deviceAppStore != null">
        device_app_store = values(device_app_store),
      </if>
      <if test="smartKeyWord != null">
        smart_key_word = values(smart_key_word),
      </if>
      <if test="androidAppPackageId != null">
        android_app_package_id = values(android_app_package_id),
      </if>
      <if test="iosAppPackageId != null">
        ios_app_package_id = values(ios_app_package_id),
      </if>
      <if test="parentUnitId != null">
        parent_unit_id = values(parent_unit_id),
      </if>
      <if test="allowAigcReplaceMaterial != null">
        allow_aigc_replace_material = values(allow_aigc_replace_material),
      </if>
      <if test="searchFirstPriceCoefficient != null">
        search_first_price_coefficient = values(search_first_price_coefficient),
      </if>
      <if test="generalVersion != null">
        general_version = values(general_version),
      </if>
    </trim>
  </insert>
</mapper>