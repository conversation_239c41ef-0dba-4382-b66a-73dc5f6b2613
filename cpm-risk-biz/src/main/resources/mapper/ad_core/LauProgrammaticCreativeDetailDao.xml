<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.risk.dao.ad_core.LauProgrammaticCreativeDetailDao">
  <resultMap id="BaseResultMap" type="com.bilibili.risk.po.ad.LauProgrammaticCreativeDetailPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="account_id" jdbcType="INTEGER" property="accountId" />
    <result column="campaign_id" jdbcType="INTEGER" property="campaignId" />
    <result column="unit_id" jdbcType="INTEGER" property="unitId" />
    <result column="creative_id" jdbcType="INTEGER" property="creativeId" />
    <result column="template_group_id" jdbcType="INTEGER" property="templateGroupId" />
    <result column="material_id" jdbcType="BIGINT" property="materialId" />
    <result column="biz_status" jdbcType="TINYINT" property="bizStatus" />
    <result column="material_type" jdbcType="TINYINT" property="materialType" />
    <result column="material_md5" jdbcType="VARCHAR" property="materialMd5" />
    <result column="rejected_reason" jdbcType="VARCHAR" property="rejectedReason" />
    <result column="mgk_template_id" jdbcType="INTEGER" property="mgkTemplateId" />
    <result column="mgk_media_id" jdbcType="BIGINT" property="mgkMediaId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, ctime, mtime, account_id, campaign_id, unit_id, creative_id, template_group_id, 
    material_id, biz_status, material_type, material_md5, rejected_reason, mgk_template_id, 
    mgk_media_id
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.risk.po.ad.LauProgrammaticCreativeDetailPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from lau_programmatic_creative_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from lau_programmatic_creative_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from lau_programmatic_creative_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.risk.po.ad.LauProgrammaticCreativeDetailPoExample">
    delete from lau_programmatic_creative_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.risk.po.ad.LauProgrammaticCreativeDetailPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_programmatic_creative_detail (ctime, mtime, account_id, 
      campaign_id, unit_id, creative_id, 
      template_group_id, material_id, biz_status, 
      material_type, material_md5, rejected_reason, 
      mgk_template_id, mgk_media_id)
    values (#{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{accountId,jdbcType=INTEGER}, 
      #{campaignId,jdbcType=INTEGER}, #{unitId,jdbcType=INTEGER}, #{creativeId,jdbcType=INTEGER}, 
      #{templateGroupId,jdbcType=INTEGER}, #{materialId,jdbcType=BIGINT}, #{bizStatus,jdbcType=TINYINT}, 
      #{materialType,jdbcType=TINYINT}, #{materialMd5,jdbcType=VARCHAR}, #{rejectedReason,jdbcType=VARCHAR}, 
      #{mgkTemplateId,jdbcType=INTEGER}, #{mgkMediaId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.risk.po.ad.LauProgrammaticCreativeDetailPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_programmatic_creative_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="campaignId != null">
        campaign_id,
      </if>
      <if test="unitId != null">
        unit_id,
      </if>
      <if test="creativeId != null">
        creative_id,
      </if>
      <if test="templateGroupId != null">
        template_group_id,
      </if>
      <if test="materialId != null">
        material_id,
      </if>
      <if test="bizStatus != null">
        biz_status,
      </if>
      <if test="materialType != null">
        material_type,
      </if>
      <if test="materialMd5 != null">
        material_md5,
      </if>
      <if test="rejectedReason != null">
        rejected_reason,
      </if>
      <if test="mgkTemplateId != null">
        mgk_template_id,
      </if>
      <if test="mgkMediaId != null">
        mgk_media_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="campaignId != null">
        #{campaignId,jdbcType=INTEGER},
      </if>
      <if test="unitId != null">
        #{unitId,jdbcType=INTEGER},
      </if>
      <if test="creativeId != null">
        #{creativeId,jdbcType=INTEGER},
      </if>
      <if test="templateGroupId != null">
        #{templateGroupId,jdbcType=INTEGER},
      </if>
      <if test="materialId != null">
        #{materialId,jdbcType=BIGINT},
      </if>
      <if test="bizStatus != null">
        #{bizStatus,jdbcType=TINYINT},
      </if>
      <if test="materialType != null">
        #{materialType,jdbcType=TINYINT},
      </if>
      <if test="materialMd5 != null">
        #{materialMd5,jdbcType=VARCHAR},
      </if>
      <if test="rejectedReason != null">
        #{rejectedReason,jdbcType=VARCHAR},
      </if>
      <if test="mgkTemplateId != null">
        #{mgkTemplateId,jdbcType=INTEGER},
      </if>
      <if test="mgkMediaId != null">
        #{mgkMediaId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.risk.po.ad.LauProgrammaticCreativeDetailPoExample" resultType="java.lang.Long">
    select count(*) from lau_programmatic_creative_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update lau_programmatic_creative_detail
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.accountId != null">
        account_id = #{record.accountId,jdbcType=INTEGER},
      </if>
      <if test="record.campaignId != null">
        campaign_id = #{record.campaignId,jdbcType=INTEGER},
      </if>
      <if test="record.unitId != null">
        unit_id = #{record.unitId,jdbcType=INTEGER},
      </if>
      <if test="record.creativeId != null">
        creative_id = #{record.creativeId,jdbcType=INTEGER},
      </if>
      <if test="record.templateGroupId != null">
        template_group_id = #{record.templateGroupId,jdbcType=INTEGER},
      </if>
      <if test="record.materialId != null">
        material_id = #{record.materialId,jdbcType=BIGINT},
      </if>
      <if test="record.bizStatus != null">
        biz_status = #{record.bizStatus,jdbcType=TINYINT},
      </if>
      <if test="record.materialType != null">
        material_type = #{record.materialType,jdbcType=TINYINT},
      </if>
      <if test="record.materialMd5 != null">
        material_md5 = #{record.materialMd5,jdbcType=VARCHAR},
      </if>
      <if test="record.rejectedReason != null">
        rejected_reason = #{record.rejectedReason,jdbcType=VARCHAR},
      </if>
      <if test="record.mgkTemplateId != null">
        mgk_template_id = #{record.mgkTemplateId,jdbcType=INTEGER},
      </if>
      <if test="record.mgkMediaId != null">
        mgk_media_id = #{record.mgkMediaId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update lau_programmatic_creative_detail
    set id = #{record.id,jdbcType=BIGINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      account_id = #{record.accountId,jdbcType=INTEGER},
      campaign_id = #{record.campaignId,jdbcType=INTEGER},
      unit_id = #{record.unitId,jdbcType=INTEGER},
      creative_id = #{record.creativeId,jdbcType=INTEGER},
      template_group_id = #{record.templateGroupId,jdbcType=INTEGER},
      material_id = #{record.materialId,jdbcType=BIGINT},
      biz_status = #{record.bizStatus,jdbcType=TINYINT},
      material_type = #{record.materialType,jdbcType=TINYINT},
      material_md5 = #{record.materialMd5,jdbcType=VARCHAR},
      rejected_reason = #{record.rejectedReason,jdbcType=VARCHAR},
      mgk_template_id = #{record.mgkTemplateId,jdbcType=INTEGER},
      mgk_media_id = #{record.mgkMediaId,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.risk.po.ad.LauProgrammaticCreativeDetailPo">
    update lau_programmatic_creative_detail
    <set>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=INTEGER},
      </if>
      <if test="campaignId != null">
        campaign_id = #{campaignId,jdbcType=INTEGER},
      </if>
      <if test="unitId != null">
        unit_id = #{unitId,jdbcType=INTEGER},
      </if>
      <if test="creativeId != null">
        creative_id = #{creativeId,jdbcType=INTEGER},
      </if>
      <if test="templateGroupId != null">
        template_group_id = #{templateGroupId,jdbcType=INTEGER},
      </if>
      <if test="materialId != null">
        material_id = #{materialId,jdbcType=BIGINT},
      </if>
      <if test="bizStatus != null">
        biz_status = #{bizStatus,jdbcType=TINYINT},
      </if>
      <if test="materialType != null">
        material_type = #{materialType,jdbcType=TINYINT},
      </if>
      <if test="materialMd5 != null">
        material_md5 = #{materialMd5,jdbcType=VARCHAR},
      </if>
      <if test="rejectedReason != null">
        rejected_reason = #{rejectedReason,jdbcType=VARCHAR},
      </if>
      <if test="mgkTemplateId != null">
        mgk_template_id = #{mgkTemplateId,jdbcType=INTEGER},
      </if>
      <if test="mgkMediaId != null">
        mgk_media_id = #{mgkMediaId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.risk.po.ad.LauProgrammaticCreativeDetailPo">
    update lau_programmatic_creative_detail
    set ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      account_id = #{accountId,jdbcType=INTEGER},
      campaign_id = #{campaignId,jdbcType=INTEGER},
      unit_id = #{unitId,jdbcType=INTEGER},
      creative_id = #{creativeId,jdbcType=INTEGER},
      template_group_id = #{templateGroupId,jdbcType=INTEGER},
      material_id = #{materialId,jdbcType=BIGINT},
      biz_status = #{bizStatus,jdbcType=TINYINT},
      material_type = #{materialType,jdbcType=TINYINT},
      material_md5 = #{materialMd5,jdbcType=VARCHAR},
      rejected_reason = #{rejectedReason,jdbcType=VARCHAR},
      mgk_template_id = #{mgkTemplateId,jdbcType=INTEGER},
      mgk_media_id = #{mgkMediaId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.risk.po.ad.LauProgrammaticCreativeDetailPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_programmatic_creative_detail (ctime, mtime, account_id, 
      campaign_id, unit_id, creative_id, 
      template_group_id, material_id, biz_status, 
      material_type, material_md5, rejected_reason, 
      mgk_template_id, mgk_media_id)
    values (#{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{accountId,jdbcType=INTEGER}, 
      #{campaignId,jdbcType=INTEGER}, #{unitId,jdbcType=INTEGER}, #{creativeId,jdbcType=INTEGER}, 
      #{templateGroupId,jdbcType=INTEGER}, #{materialId,jdbcType=BIGINT}, #{bizStatus,jdbcType=TINYINT}, 
      #{materialType,jdbcType=TINYINT}, #{materialMd5,jdbcType=VARCHAR}, #{rejectedReason,jdbcType=VARCHAR}, 
      #{mgkTemplateId,jdbcType=INTEGER}, #{mgkMediaId,jdbcType=BIGINT})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      ctime = values(ctime),
      mtime = values(mtime),
      account_id = values(account_id),
      campaign_id = values(campaign_id),
      unit_id = values(unit_id),
      creative_id = values(creative_id),
      template_group_id = values(template_group_id),
      material_id = values(material_id),
      biz_status = values(biz_status),
      material_type = values(material_type),
      material_md5 = values(material_md5),
      rejected_reason = values(rejected_reason),
      mgk_template_id = values(mgk_template_id),
      mgk_media_id = values(mgk_media_id),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      lau_programmatic_creative_detail
      (ctime,mtime,account_id,campaign_id,unit_id,creative_id,template_group_id,material_id,biz_status,material_type,material_md5,rejected_reason,mgk_template_id,mgk_media_id)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.accountId,jdbcType=INTEGER},
        #{item.campaignId,jdbcType=INTEGER},
        #{item.unitId,jdbcType=INTEGER},
        #{item.creativeId,jdbcType=INTEGER},
        #{item.templateGroupId,jdbcType=INTEGER},
        #{item.materialId,jdbcType=BIGINT},
        #{item.bizStatus,jdbcType=TINYINT},
        #{item.materialType,jdbcType=TINYINT},
        #{item.materialMd5,jdbcType=VARCHAR},
        #{item.rejectedReason,jdbcType=VARCHAR},
        #{item.mgkTemplateId,jdbcType=INTEGER},
        #{item.mgkMediaId,jdbcType=BIGINT},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      lau_programmatic_creative_detail
      (ctime,mtime,account_id,campaign_id,unit_id,creative_id,template_group_id,material_id,biz_status,material_type,material_md5,rejected_reason,mgk_template_id,mgk_media_id)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.accountId,jdbcType=INTEGER},
        #{item.campaignId,jdbcType=INTEGER},
        #{item.unitId,jdbcType=INTEGER},
        #{item.creativeId,jdbcType=INTEGER},
        #{item.templateGroupId,jdbcType=INTEGER},
        #{item.materialId,jdbcType=BIGINT},
        #{item.bizStatus,jdbcType=TINYINT},
        #{item.materialType,jdbcType=TINYINT},
        #{item.materialMd5,jdbcType=VARCHAR},
        #{item.rejectedReason,jdbcType=VARCHAR},
        #{item.mgkTemplateId,jdbcType=INTEGER},
        #{item.mgkMediaId,jdbcType=BIGINT},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      ctime = values(ctime),
      mtime = values(mtime),
      account_id = values(account_id),
      campaign_id = values(campaign_id),
      unit_id = values(unit_id),
      creative_id = values(creative_id),
      template_group_id = values(template_group_id),
      material_id = values(material_id),
      biz_status = values(biz_status),
      material_type = values(material_type),
      material_md5 = values(material_md5),
      rejected_reason = values(rejected_reason),
      mgk_template_id = values(mgk_template_id),
      mgk_media_id = values(mgk_media_id),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.risk.po.ad.LauProgrammaticCreativeDetailPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_programmatic_creative_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="campaignId != null">
        campaign_id,
      </if>
      <if test="unitId != null">
        unit_id,
      </if>
      <if test="creativeId != null">
        creative_id,
      </if>
      <if test="templateGroupId != null">
        template_group_id,
      </if>
      <if test="materialId != null">
        material_id,
      </if>
      <if test="bizStatus != null">
        biz_status,
      </if>
      <if test="materialType != null">
        material_type,
      </if>
      <if test="materialMd5 != null">
        material_md5,
      </if>
      <if test="rejectedReason != null">
        rejected_reason,
      </if>
      <if test="mgkTemplateId != null">
        mgk_template_id,
      </if>
      <if test="mgkMediaId != null">
        mgk_media_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="campaignId != null">
        #{campaignId,jdbcType=INTEGER},
      </if>
      <if test="unitId != null">
        #{unitId,jdbcType=INTEGER},
      </if>
      <if test="creativeId != null">
        #{creativeId,jdbcType=INTEGER},
      </if>
      <if test="templateGroupId != null">
        #{templateGroupId,jdbcType=INTEGER},
      </if>
      <if test="materialId != null">
        #{materialId,jdbcType=BIGINT},
      </if>
      <if test="bizStatus != null">
        #{bizStatus,jdbcType=TINYINT},
      </if>
      <if test="materialType != null">
        #{materialType,jdbcType=TINYINT},
      </if>
      <if test="materialMd5 != null">
        #{materialMd5,jdbcType=VARCHAR},
      </if>
      <if test="rejectedReason != null">
        #{rejectedReason,jdbcType=VARCHAR},
      </if>
      <if test="mgkTemplateId != null">
        #{mgkTemplateId,jdbcType=INTEGER},
      </if>
      <if test="mgkMediaId != null">
        #{mgkMediaId,jdbcType=BIGINT},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="accountId != null">
        account_id = values(account_id),
      </if>
      <if test="campaignId != null">
        campaign_id = values(campaign_id),
      </if>
      <if test="unitId != null">
        unit_id = values(unit_id),
      </if>
      <if test="creativeId != null">
        creative_id = values(creative_id),
      </if>
      <if test="templateGroupId != null">
        template_group_id = values(template_group_id),
      </if>
      <if test="materialId != null">
        material_id = values(material_id),
      </if>
      <if test="bizStatus != null">
        biz_status = values(biz_status),
      </if>
      <if test="materialType != null">
        material_type = values(material_type),
      </if>
      <if test="materialMd5 != null">
        material_md5 = values(material_md5),
      </if>
      <if test="rejectedReason != null">
        rejected_reason = values(rejected_reason),
      </if>
      <if test="mgkTemplateId != null">
        mgk_template_id = values(mgk_template_id),
      </if>
      <if test="mgkMediaId != null">
        mgk_media_id = values(mgk_media_id),
      </if>
    </trim>
  </insert>
</mapper>