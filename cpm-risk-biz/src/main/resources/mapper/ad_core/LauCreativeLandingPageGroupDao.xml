<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.risk.dao.ad_core.LauCreativeLandingPageGroupDao">
  <resultMap id="BaseResultMap" type="com.bilibili.risk.po.ad.LauCreativeLandingPageGroupPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="account_id" jdbcType="INTEGER" property="accountId" />
    <result column="creative_id" jdbcType="INTEGER" property="creativeId" />
    <result column="group_id" jdbcType="BIGINT" property="groupId" />
    <result column="can_joint_launch" jdbcType="TINYINT" property="canJointLaunch" />
    <result column="group_source" jdbcType="INTEGER" property="groupSource" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, ctime, mtime, is_deleted, account_id, creative_id, group_id, can_joint_launch, 
    group_source
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.risk.po.ad.LauCreativeLandingPageGroupPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from lau_creative_landing_page_group
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from lau_creative_landing_page_group
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from lau_creative_landing_page_group
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.risk.po.ad.LauCreativeLandingPageGroupPoExample">
    delete from lau_creative_landing_page_group
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.risk.po.ad.LauCreativeLandingPageGroupPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_creative_landing_page_group (ctime, mtime, is_deleted, 
      account_id, creative_id, group_id, 
      can_joint_launch, group_source)
    values (#{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=TINYINT}, 
      #{accountId,jdbcType=INTEGER}, #{creativeId,jdbcType=INTEGER}, #{groupId,jdbcType=BIGINT}, 
      #{canJointLaunch,jdbcType=TINYINT}, #{groupSource,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.risk.po.ad.LauCreativeLandingPageGroupPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_creative_landing_page_group
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="creativeId != null">
        creative_id,
      </if>
      <if test="groupId != null">
        group_id,
      </if>
      <if test="canJointLaunch != null">
        can_joint_launch,
      </if>
      <if test="groupSource != null">
        group_source,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="creativeId != null">
        #{creativeId,jdbcType=INTEGER},
      </if>
      <if test="groupId != null">
        #{groupId,jdbcType=BIGINT},
      </if>
      <if test="canJointLaunch != null">
        #{canJointLaunch,jdbcType=TINYINT},
      </if>
      <if test="groupSource != null">
        #{groupSource,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.risk.po.ad.LauCreativeLandingPageGroupPoExample" resultType="java.lang.Long">
    select count(*) from lau_creative_landing_page_group
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update lau_creative_landing_page_group
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.accountId != null">
        account_id = #{record.accountId,jdbcType=INTEGER},
      </if>
      <if test="record.creativeId != null">
        creative_id = #{record.creativeId,jdbcType=INTEGER},
      </if>
      <if test="record.groupId != null">
        group_id = #{record.groupId,jdbcType=BIGINT},
      </if>
      <if test="record.canJointLaunch != null">
        can_joint_launch = #{record.canJointLaunch,jdbcType=TINYINT},
      </if>
      <if test="record.groupSource != null">
        group_source = #{record.groupSource,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update lau_creative_landing_page_group
    set id = #{record.id,jdbcType=BIGINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      account_id = #{record.accountId,jdbcType=INTEGER},
      creative_id = #{record.creativeId,jdbcType=INTEGER},
      group_id = #{record.groupId,jdbcType=BIGINT},
      can_joint_launch = #{record.canJointLaunch,jdbcType=TINYINT},
      group_source = #{record.groupSource,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.risk.po.ad.LauCreativeLandingPageGroupPo">
    update lau_creative_landing_page_group
    <set>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=INTEGER},
      </if>
      <if test="creativeId != null">
        creative_id = #{creativeId,jdbcType=INTEGER},
      </if>
      <if test="groupId != null">
        group_id = #{groupId,jdbcType=BIGINT},
      </if>
      <if test="canJointLaunch != null">
        can_joint_launch = #{canJointLaunch,jdbcType=TINYINT},
      </if>
      <if test="groupSource != null">
        group_source = #{groupSource,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.risk.po.ad.LauCreativeLandingPageGroupPo">
    update lau_creative_landing_page_group
    set ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      account_id = #{accountId,jdbcType=INTEGER},
      creative_id = #{creativeId,jdbcType=INTEGER},
      group_id = #{groupId,jdbcType=BIGINT},
      can_joint_launch = #{canJointLaunch,jdbcType=TINYINT},
      group_source = #{groupSource,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.risk.po.ad.LauCreativeLandingPageGroupPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_creative_landing_page_group (ctime, mtime, is_deleted, 
      account_id, creative_id, group_id, 
      can_joint_launch, group_source)
    values (#{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=TINYINT}, 
      #{accountId,jdbcType=INTEGER}, #{creativeId,jdbcType=INTEGER}, #{groupId,jdbcType=BIGINT}, 
      #{canJointLaunch,jdbcType=TINYINT}, #{groupSource,jdbcType=INTEGER})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
      account_id = values(account_id),
      creative_id = values(creative_id),
      group_id = values(group_id),
      can_joint_launch = values(can_joint_launch),
      group_source = values(group_source),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      lau_creative_landing_page_group
      (ctime,mtime,is_deleted,account_id,creative_id,group_id,can_joint_launch,group_source)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.accountId,jdbcType=INTEGER},
        #{item.creativeId,jdbcType=INTEGER},
        #{item.groupId,jdbcType=BIGINT},
        #{item.canJointLaunch,jdbcType=TINYINT},
        #{item.groupSource,jdbcType=INTEGER},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      lau_creative_landing_page_group
      (ctime,mtime,is_deleted,account_id,creative_id,group_id,can_joint_launch,group_source)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.accountId,jdbcType=INTEGER},
        #{item.creativeId,jdbcType=INTEGER},
        #{item.groupId,jdbcType=BIGINT},
        #{item.canJointLaunch,jdbcType=TINYINT},
        #{item.groupSource,jdbcType=INTEGER},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
      account_id = values(account_id),
      creative_id = values(creative_id),
      group_id = values(group_id),
      can_joint_launch = values(can_joint_launch),
      group_source = values(group_source),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.risk.po.ad.LauCreativeLandingPageGroupPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_creative_landing_page_group
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="creativeId != null">
        creative_id,
      </if>
      <if test="groupId != null">
        group_id,
      </if>
      <if test="canJointLaunch != null">
        can_joint_launch,
      </if>
      <if test="groupSource != null">
        group_source,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="creativeId != null">
        #{creativeId,jdbcType=INTEGER},
      </if>
      <if test="groupId != null">
        #{groupId,jdbcType=BIGINT},
      </if>
      <if test="canJointLaunch != null">
        #{canJointLaunch,jdbcType=TINYINT},
      </if>
      <if test="groupSource != null">
        #{groupSource,jdbcType=INTEGER},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="accountId != null">
        account_id = values(account_id),
      </if>
      <if test="creativeId != null">
        creative_id = values(creative_id),
      </if>
      <if test="groupId != null">
        group_id = values(group_id),
      </if>
      <if test="canJointLaunch != null">
        can_joint_launch = values(can_joint_launch),
      </if>
      <if test="groupSource != null">
        group_source = values(group_source),
      </if>
    </trim>
  </insert>
</mapper>