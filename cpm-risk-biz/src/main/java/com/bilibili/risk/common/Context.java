package com.bilibili.risk.common;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025-04-16 16:13
 * web层的请求上下文
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Context implements Serializable {

    private static final long serialVersionUID = -5434194122932365941L;

    @Schema(description = "用户id")
    private Integer userId;

    @Schema(description = "用户名")
    private String username;

    private String ip;

}
