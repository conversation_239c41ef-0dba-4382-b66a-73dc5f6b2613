package com.bilibili.risk.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * mid	Y	int	用户mid
 * token	Y	String	sessiondata
 * expires	Y	int	过期时间
 * csrfToken	Y	String	csrfToken
 * <p>
 * Created by <PERSON><PERSON><PERSON> on 2017/2/28.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AuthInfo {
    private Long mid;
    private String token;
    private Integer expires;
    private String csrfToken;
}
