package com.bilibili.risk.exception;

import com.bilibili.risk.utils.NumberUtils;
import org.springframework.util.Assert;

import java.text.MessageFormat;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Supplier;

public class RiskAssert {
    public static void isPositive(Number x, String desc) {
        if (!NumberUtils.isPositive(x)) throw new RiskDataErr(MessageFormat.format("{0}的值必须大于0", desc));
    }

    public static void isBetween(Number x, Number floor, Number ceiling, String desc) {
        final StringBuilder sb = new StringBuilder().append(desc)
                .append("不符合区间约束, 当前值为")
                .append(Optional.ofNullable(x).map(Object::toString).orElse("空"));
        if (Objects.nonNull(floor)) {
            sb.append(", 最小值要求: ")
                    .append(floor);
        }
        if (Objects.nonNull(ceiling)) {
            sb.append(", 最大值要求: ")
                    .append(ceiling);
        }
        if (!NumberUtils.between(x, floor, ceiling)) throw new RiskDataErr(sb.toString());
    }

    public static void equals(Number x, Number y, String desc) {
        if (!Objects.equals(x, y)) {
            throw new RiskDataErr(MessageFormat.format("{0}数据不匹配, 预期值为{1}, 实际值为{2}", desc, x, y));
        }
    }

    public static void exists(Object x, Object key, String desc) {
        if (Objects.isNull(x)) {
            final String msg;
            if (Objects.isNull(key)) {
                msg = MessageFormat.format("不存在对应的{0}", desc);
            } else {
                msg = MessageFormat.format("不存在id为{0}的{1}", key.toString(), desc);
            }
            throw new RiskDataErr(msg);
        }
    }

    public static void exists(Object x, Supplier<Object> keySupplier, String desc) {
        if (Objects.isNull(x)) {
            final String msg;
            if (Objects.isNull(keySupplier)) {
                msg = MessageFormat.format("不存在对应的{0}", desc);
            } else {
                msg = MessageFormat.format("不存在id为{0}的{1}", keySupplier.get().toString(), desc);
            }
            throw new RiskDataErr(msg);
        }
    }


    public static void isBoolNum(Integer num, String desc) {
        Assert.isTrue(Objects.equals(num, 1) || Objects.equals(num, 0), desc + "的取值只能是0或1");
    }
}
