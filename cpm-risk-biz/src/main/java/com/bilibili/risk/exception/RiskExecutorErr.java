package com.bilibili.risk.exception;

import java.text.MessageFormat;

public class RiskExecutorErr extends RiskServiceErr {
    private RiskExecutorErr(String msg) {
        super(-503, msg);
    }

    public static RiskExecutorErr notRegistered(String key) {
        return new RiskExecutorErr(MessageFormat.format("执行器{0}未注册, 无可用资源", key));
    }

    public static RiskExecutorErr resourceNotAvailable() {
        return new RiskExecutorErr("当前无可用资源");
    }
}
