package com.bilibili.risk.exception;

import java.text.MessageFormat;

public class RiskRemoteAccessErr extends RiskServiceErr {
    private static final String KEY = "远程获取";

    private RiskRemoteAccessErr(String msg) {
        super(-501, msg);
    }

    public static RiskRemoteAccessErr noBody() {
        return new RiskRemoteAccessErr(MessageFormat.format("{0}-返回结果为空", KEY));
    }

    public static RiskRemoteAccessErr wrongStatus(Integer code, String msg) {
        return new RiskRemoteAccessErr(MessageFormat.format("{0}-状态错误, code={1}, message={2}", KEY, code, msg));
    }

    public static RiskRemoteAccessErr networkFailure(String url) {
        return new RiskRemoteAccessErr(MessageFormat.format("{0}-网络错误, url={1}", KEY, url));
    }

    public static RiskRemoteAccessErr networkFailureWithMsg(String msg) {
        return new RiskRemoteAccessErr(MessageFormat.format("{0}-网络错误, message={1}", KEY, msg));
    }
}
