package com.bilibili.risk.exception;

import java.text.MessageFormat;

public class RiskImageProcessErr extends RiskServiceErr {
    private RiskImageProcessErr(String msg) {
        super(-502, msg);
    }

    public static RiskImageProcessErr metaErr() {
        return new RiskImageProcessErr("图片元数据错误");
    }

    public static RiskImageProcessErr mimeTypeErr(String type) {
        return new RiskImageProcessErr(MessageFormat.format("暂不支持解析type={0}的图片", type));
    }

    public static RiskImageProcessErr parseImageErr() {
        return new RiskImageProcessErr("图片数据错误");
    }

    public static RiskImageProcessErr gif2webpOvertime() {
        return new RiskImageProcessErr("gif转webp超时");
    }
}
