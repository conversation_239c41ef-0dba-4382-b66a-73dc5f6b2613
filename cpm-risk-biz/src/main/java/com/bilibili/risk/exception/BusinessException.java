package com.bilibili.risk.exception;

import com.bilibili.risk.enums.MetricsCodeEnum;
import lombok.Getter;

/**
 * 业务异常类
 * 用于包装业务逻辑异常，包含错误码和错误信息
 */
@Getter
public class BusinessException extends RuntimeException {
    
    private final MetricsCodeEnum.DomainType domainType;
    private final MetricsCodeEnum.Type errorType;
    private final MetricsCodeEnum.SubCode subCode;
    private final String errMsg;

     /**
     * 获取完整错误码
     * 
     * @return 完整错误码，格式：域-类型-子码
     */
    public String getErrCode() {
        return MetricsCodeEnum.generateFullCode(domainType, errorType, subCode);
    }
    
    /**
     * 使用错误码组件构造异常
     * 错误信息使用子码中的默认描述
     * 
     * @param domainType 业务域
     * @param errorType 错误类型
     * @param subCode 错误子码
     */
    public BusinessException(MetricsCodeEnum.DomainType domainType, MetricsCodeEnum.Type errorType, MetricsCodeEnum.SubCode subCode) {
        super(subCode.getDesc());
        this.domainType = domainType;
        this.errorType = errorType;
        this.subCode = subCode;
        this.errMsg = subCode.getDesc();
    }
    
    /**
     * 使用错误码组件和自定义错误信息构造异常
     * 
     * @param domainType 业务域
     * @param errorType 错误类型
     * @param subCode 错误子码
     * @param errMsg 自定义错误信息
     */
    public BusinessException(MetricsCodeEnum.DomainType domainType, MetricsCodeEnum.Type errorType, MetricsCodeEnum.SubCode subCode, String errMsg) {
        super(errMsg);
        this.domainType = domainType;
        this.errorType = errorType;
        this.subCode = subCode;
        this.errMsg = errMsg;
    }
    
    /**
     * 使用错误码组件、自定义错误信息和原始异常构造异常（支持异常链，传递引起异常的根本原因cause）
     * 
     * @param domainType 业务域
     * @param errorType 错误类型
     * @param subCode 错误子码
     * @param errMsg 自定义错误信息
     * @param cause 原始异常
     */
    public BusinessException(MetricsCodeEnum.DomainType domainType, MetricsCodeEnum.Type errorType, MetricsCodeEnum.SubCode subCode, String errMsg, Throwable cause) {
        super(errMsg, cause);
        this.domainType = domainType;
        this.errorType = errorType;
        this.subCode = subCode;
        this.errMsg = errMsg;
    }
    
    /**
     * 使用错误码组件和原始异常构造异常
     * 错误信息使用子码中的默认描述
     * 
     * @param domainType 业务域
     * @param errorType 错误类型
     * @param subCode 错误子码
     * @param cause 原始异常
     */
    public BusinessException(MetricsCodeEnum.DomainType domainType, MetricsCodeEnum.Type errorType, MetricsCodeEnum.SubCode subCode, Throwable cause) {
        super(subCode.getDesc(), cause);
        this.domainType = domainType;
        this.errorType = errorType;
        this.subCode = subCode;
        this.errMsg = subCode.getDesc();
    }
    
    
    @Override
    public String toString() {
        return "BusinessException{" +
                "code='" + getErrCode() + '\'' +
                ", message='" + errMsg + '\'' +
                '}';
    }
}
