package com.bilibili.risk.exception;

import com.bilibili.adp.common.exception.IExceptionCode;

public enum ExceptionCode implements IExceptionCode {
    SUCCESS(0, "成功"),
    SYSTEM_ERROR(90000, "系统异常");

    private String message;
    private Integer code;

    ExceptionCode(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public Integer getCode() {
        return this.code;
    }

    @Override
    public String getMessage() {
        return this.message;
    }
}
