package com.bilibili.risk.metrics_report.metrics;


import io.opentelemetry.api.OpenTelemetry;
import io.opentelemetry.api.common.Attributes;
import io.opentelemetry.api.metrics.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Component
public class RiskMetricsImpl implements CustomMetrics {

    public static final String METRIC_DATA_UNKNOWN = "UNKNOWN";
    public static final String DEFAULT_REQUEST_SOURCE = "0";
    public static final String DEFAULT_ACCOUNT_UNKNOWN = "0";

    public static final String METRIC_KEY_RPC_REQUEST_COUNT = "risk_rpc_request_count";
    public static final String METRIC_KEY_RPC_LATENCY = "risk_rpc_latency";

    public static final String METRIC_KEY_CONTROLLER_REQUEST_COUNT = "risk_controller_request_count";
    public static final String METRIC_KEY_CONTROLLER_LATENCY = "risk_controller_latency";

    public static final String METRIC_KEY_BIZ_REQUEST_COUNT = "risk_biz_request_count";
    public static final String METRIC_KEY_BIZ_LATENCY = "risk_biz_cost";

    private static final String METER_SERVICE = "sycpb_cpm_risk_service";
    private static final String DEFAULT_METRICS = "sycpb_cpm_risk_metrics";
    private static final String METRICS_PREFIX = "risk_";
    private final OpenTelemetry openTelemetry;
    private final Map<String, LongCounter> counterMap;
    private final HashMap<String, LongHistogram> histogramMap;

    public RiskMetricsImpl(OpenTelemetry openTelemetry) {
        Meter meter = openTelemetry.getMeter(METER_SERVICE);
        LongCounter defaultCounter = meter.counterBuilder(DEFAULT_METRICS)
                .setDescription("custom counter").build();
        LongHistogram defaultHistogram = meter.histogramBuilder(DEFAULT_METRICS)
                .setDescription("custom histogram").ofLongs().build();
        this.counterMap = new HashMap<>();
        this.counterMap.put(DEFAULT_METRICS, defaultCounter);
        this.histogramMap = new HashMap<>();
        histogramMap.put(DEFAULT_METRICS, defaultHistogram);
        this.openTelemetry = openTelemetry;
    }

    /**
     * 计数器：默认加1
     *
     * @param counterKey
     * @param attributes
     */
    @Override
    public void count(String counterKey, Attributes attributes) {
        if (Objects.isNull(attributes) || attributes.isEmpty()) {
            return;
        }
        try {
            LongCounter counter = this.getCounter(counterKey);
            counter.add(1, attributes);
        } catch (Exception e) {
            log.error("RiskMetricsImpl count error.", e);
        }
    }

    // 默认创建counter时，desc与counter key一致
    @Override
    public void count(String counterKey, long value, Attributes attributes) {
        if (Objects.isNull(attributes) || attributes.isEmpty()) {
            return;
        }
        if (Objects.isNull(counterMap.get(counterKey))) {
            Meter meter = openTelemetry.getMeter(METER_SERVICE);
            LongCounter c = meter.counterBuilder(counterKey).setDescription(counterKey).build();
            counterMap.put(counterKey, c);
        }
        try {
            LongCounter counter = this.counterMap.get(counterKey);
            counter.add(value, attributes);
        } catch (Exception e) {
            log.error("RiskMetricsImpl count error.", e);
        }
    }

    /**
     * 直方图：记录观测值的分布直方图。使用场景eg: 请求耗时。
     *
     * @param histogramKey
     * @param value
     * @param attributes
     */
    @Override
    public void histogram(String histogramKey, long value, Attributes attributes) {
        if (Objects.isNull(attributes) || attributes.isEmpty()) {
            return;
        }
        try {
            LongHistogram histogram = this.getHistogram(histogramKey);
            histogram.record(value, attributes);
        } catch (Exception e) {
            log.error("RiskMetricsImpl histogram error.", e);
        }
    }

    private LongCounter getCounter(String key) {
        String metricsKey = this.getMetricsKey(key);
        if (Objects.isNull(this.counterMap.get(metricsKey))) {
            Meter meter = openTelemetry.getMeter(METER_SERVICE);
            LongCounter c = meter.counterBuilder(metricsKey).setDescription(metricsKey).build();
            this.counterMap.put(metricsKey, c);
        }
        return this.counterMap.get(metricsKey);
    }

    private LongHistogram getHistogram(String key) {
        String metricsKey = this.getMetricsKey(key);
        if (Objects.isNull(this.histogramMap.get(metricsKey))) {
            Meter meter = openTelemetry.getMeter(METER_SERVICE);
            LongHistogram c = meter.histogramBuilder(metricsKey).setDescription(metricsKey).ofLongs()
                    .build();
            this.histogramMap.put(metricsKey, c);
        }
        return this.histogramMap.get(metricsKey);
    }

    private String getMetricsKey(String key) {
        return METRICS_PREFIX + key;
    }

    /**
     * 上报接口调用 请求计数
     * @param data 从 AspectService 收集到的指标数据
     * @param requestSource 请求来源
     */
    public void reportMetricsCount(String key, MetricDataHolder data, String requestSource) {
        try {
            String instanceName = getInstanceName();
            String env = getEnv();
            log.info("reportMetricsCount, key={}, data={}, requestSource={}", key, data, requestSource);

            // 上报请求计数 (Counter)
            Attributes countAttributes = Attributes.builder()
                    .put("method", data.methodName)
                    .put("domain_type", data.domainType)
                    .put("type", data.type)
                    .put("code", data.code)
                    .put("msg", data.msg)
                    .put("mapi", data.isMapi)
                    .put("request_source", requestSource)
                    .put("instance_name", instanceName)
                    .put("env", env)
                    .build();
            this.count(key, countAttributes);
        } catch (Exception e) {
            log.error("RiskMetricsImpl reportMetricsCount error.", e);
        }
    }

    /**
     * 上报接口调用 延迟指标
     * @param data 从 AspectService 收集到的指标数据
     * @param requestSource 请求来源
     * @param durationMs 请求耗时 (毫秒)
     */
    public void reportMetricsHistogram(String key, MetricDataHolder data, String requestSource, long durationMs) {
        try {
            log.info("reportMetricsHistogram, key={}, data={}, requestSource={}, durationMs={}", key, data, requestSource, durationMs);
            String instanceName = getInstanceName();
            String env = getEnv();

            // 上报请求延迟 (Histogram)
            Attributes latencyAttributes = Attributes.builder()
                    .put("method", data.methodName)
                    .put("mapi", data.isMapi)
                    .put("request_source", requestSource)
                    .put("instance_name", instanceName)
                    .put("env", env)
                    .build();
            this.histogram(key, durationMs, latencyAttributes);
        } catch (Exception e) {
            log.error("RiskMetricsImpl reportMetricsHistogram error.", e);
        }
    }

    /**
     * 获取当前服务实例的主机名
     * @return 主机名，获取失败时返回 "UNKNOWN"
     */
    private String getInstanceName() {
        try {
            return InetAddress.getLocalHost().getHostName();
        } catch (UnknownHostException e) {
            log.error("获取主机名失败", e);
            return METRIC_DATA_UNKNOWN;
        }
    }

    /**
     * 获取当前部署环境标识 (从环境变量 DEPLOY_ENV 读取)
     * @return 环境标识，未设置时返回 "UNKNOWN"
     */
    public String getEnv() {
        String env = System.getenv("DEPLOY_ENV");
        return env == null ? METRIC_DATA_UNKNOWN : env;
    }

}
