package com.bilibili.risk.metrics_report;


import com.bilibili.risk.enums.MetricsCodeEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * RPC服务切面注解
 * 用于标记RPC服务类，并指定其对应的业务域类型
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface RpcService {
    /**
     * 业务域类型
     * 默认为OTHER
     */
    MetricsCodeEnum.DomainType domainType() default MetricsCodeEnum.DomainType.OTHER;
}