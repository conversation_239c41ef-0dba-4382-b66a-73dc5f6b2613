package com.bilibili.risk.metrics_report.metrics;

import com.bilibili.risk.enums.MetricsCodeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class TaskMetricsReportService {

    private final MetricsReportService metricsReportService;

    public void addBizMetricCountAndCost(Long costTime, MetricsCodeEnum.SubCode subCode, String username, Integer materialType, Integer size) {
        try {
            MetricDataHolder data = new MetricDataHolder();
            data.setDomainType(MetricsCodeEnum.DomainType.CREATIVE_MATERIAL_TASK.name());
            data.setType(MetricsCodeEnum.Type.BIZ_INDEX.name());
            data.setCode(subCode.getCode());
            data.setMsg(subCode.getDesc());
            data.setUsername(username);
            data.setMaterialTypeName(materialType + "");
            data.setSize(String.valueOf(size));
            data.setSuccess(MetricsCodeEnum.SubCode.SUCCESS.getDesc());
            metricsReportService.reportMetricsCount(data);

            metricsReportService.reportMetricCostTime(data, costTime);
        } catch (Exception e) {
            log.error("addBizMetrics fail", e);
        }
    }

    public void addBizMetricCountAndCost(Long costTime, MetricDataHolder data) {
        try {
            data.setDomainType(MetricsCodeEnum.DomainType.CREATIVE_MATERIAL_TASK.name());
            data.setType(MetricsCodeEnum.Type.BIZ_INDEX.name());
            metricsReportService.reportMetricsCount(data);

            metricsReportService.reportMetricCostTime(data, costTime);
        } catch (Exception e) {
            log.error("addBizMetrics fail", e);
        }
    }

    public void addBizMetricCount(MetricsCodeEnum.SubCode subCode, String username, Integer materialType, Integer size) {
        try {
            MetricDataHolder data = new MetricDataHolder();
            data.setDomainType(MetricsCodeEnum.DomainType.CREATIVE_MATERIAL_TASK.name());
            data.setType(MetricsCodeEnum.Type.BIZ_INDEX.name());
            data.setCode(subCode.getCode());
            data.setMsg(subCode.getDesc());
            data.setUsername(username);
            data.setMaterialTypeName(materialType + "");
            data.setSize(String.valueOf(size));
            data.setSuccess(MetricsCodeEnum.SubCode.SUCCESS.getDesc());
            metricsReportService.reportMetricsCount(data);
        } catch (Exception e) {
            log.error("addBizMetrics fail", e);
        }
    }
}
