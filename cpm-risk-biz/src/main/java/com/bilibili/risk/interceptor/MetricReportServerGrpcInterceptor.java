package com.bilibili.risk.interceptor;

import com.bilibili.risk.aspect.RpcServiceAspect;
import com.bilibili.risk.enums.MetricsCodeEnum;
import com.bilibili.risk.service.metrics.MetricDataHolder;
import com.bilibili.risk.service.metrics.RiskMetricsImpl;
import com.bilibili.warp.grpc.global.GlobalServerInterceptor;
import io.grpc.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.atomic.AtomicReference;


/**
 * <AUTHOR>
 * @Description gRPC 服务端拦截器，用于处理指标上报，包括从 Metadata 中获取请求来源。
 * @date 2025/4/23
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MetricReportServerGrpcInterceptor implements GlobalServerInterceptor {

    private final RiskMetricsImpl riskMetrics;


    // 定义Metadata Key
    private static final Metadata.Key<String> REQUEST_SOURCE_METADATA_KEY =
            Metadata.Key.of("x-adp-request-source", Metadata.ASCII_STRING_MARSHALLER);
    private static final Metadata.Key<String> ACCOUNT_ID_METADATA_KEY =
            Metadata.Key.of("x-adp-account-id", Metadata.ASCII_STRING_MARSHALLER);

    @Override
    public <ReqT, RespT> ServerCall.Listener<ReqT> interceptCall(
            ServerCall<ReqT, RespT> call,
            Metadata headers,
            ServerCallHandler<ReqT, RespT> next) {

        String methodName = call.getMethodDescriptor().getFullMethodName();

        long startTime = System.currentTimeMillis(); // 记录开始时间
        // 从 Metadata 中获取上游传递的请求来源、账户 ID
        String requestSource = headers.get(REQUEST_SOURCE_METADATA_KEY);
        String accountId = headers.get(ACCOUNT_ID_METADATA_KEY);

        //log.info("MetricReportServerInterceptor requestSource={}, accountId={}, methodName={}", requestSource, accountId, methodName);

        String finalRequestSource = requestSource == null ? RiskMetricsImpl.DEFAULT_REQUEST_SOURCE : requestSource;
        String finalAccountId = accountId == null ? RiskMetricsImpl.DEFAULT_ACCOUNT_UNKNOWN : accountId;

        // 创建 MetricDataHolder 的原子引用，用于在 AspectService 中修改指标数据
        AtomicReference<MetricDataHolder> metricDataHolderRef = new AtomicReference<>(new MetricDataHolder());

        // 创建新的 Context
        Context context = Context.current()
                .withValue(RpcServiceAspect.REQUEST_SOURCE_CTX_KEY, finalRequestSource)
                .withValue(RpcServiceAspect.ACCOUNT_ID_CTX_KEY, finalAccountId)
                .withValue(RpcServiceAspect.METRIC_DATA_HOLDER_CTX_KEY, metricDataHolderRef);

        // 包装 ServerCall 以便拦截 close() 方法，在调用结束时执行指标上报
        ServerCall<ReqT, RespT> wrappedCall = new ForwardingServerCall.SimpleForwardingServerCall<ReqT, RespT>(call) {
            @Override
            public void close(Status status, Metadata trailers) {
                long durationMs = System.currentTimeMillis() - startTime;
                // 获取 AspectService 可能修改过的 MetricDataHolder
                MetricDataHolder finalMetricData = metricDataHolderRef.get();

                // 处理没有被 AspectService 捕获到的 gRPC 错误， 根据 gRPC Status 推断基本的错误信息用于上报
                // 没有被 AspectService 捕获到的条件：gRPC 状态不是 OK，并且 AspectService 没有更新 Holder中的errCode
                // 例如在 gRPC 框架处理请求的早期阶段（虽然 Contexts.interceptCall 的 catch 处理了一部分）或晚期阶段）
                if (!status.isOk() && finalMetricData.code.equals(MetricsCodeEnum.SubCode.SUCCESS.getCode())) {
                    finalMetricData.type = MetricsCodeEnum.Type.SYS_ERR.name(); // 默认为系统错误
                    switch (status.getCode()) {
                        case DEADLINE_EXCEEDED: // 超时
                            finalMetricData.code = MetricsCodeEnum.SubCode.TIMEOUT.getCode();
                            finalMetricData.msg = MetricsCodeEnum.SubCode.TIMEOUT.getDesc();
                            break;
                        case CANCELLED:
                            finalMetricData.code = MetricsCodeEnum.SubCode.CLIENT_CANCELLED.getCode();
                            finalMetricData.msg = MetricsCodeEnum.SubCode.CLIENT_CANCELLED.getDesc();
                            finalMetricData.type = MetricsCodeEnum.Type.BIZ_ERR.name(); // 业务错误
                            break;
                        case INVALID_ARGUMENT: // 参数无效
                            finalMetricData.code = MetricsCodeEnum.SubCode.PARAM_INVALID.getCode();
                            finalMetricData.msg = MetricsCodeEnum.SubCode.PARAM_INVALID.getDesc();
                            finalMetricData.type = MetricsCodeEnum.Type.BIZ_ERR.name(); // 业务错误
                            break;
                        case UNAUTHENTICATED: // 未认证
                        case PERMISSION_DENIED: // 无权限
                            finalMetricData.code = MetricsCodeEnum.SubCode.DOWNSTREAM_CALL_ERROR.getCode();
                            finalMetricData.msg = MetricsCodeEnum.SubCode.DOWNSTREAM_CALL_ERROR.getDesc();
                            break;
                        case NOT_FOUND: // 未找到资源
                            finalMetricData.code = MetricsCodeEnum.SubCode.DOWNSTREAM_CALL_ERROR.getCode();
                            finalMetricData.msg = MetricsCodeEnum.SubCode.DOWNSTREAM_CALL_ERROR.getDesc();
                            break;
                        case UNAVAILABLE: // 下游服务不可用
                            finalMetricData.code = MetricsCodeEnum.SubCode.DOWNSTREAM_CALL_ERROR.getCode();
                            finalMetricData.msg = MetricsCodeEnum.SubCode.DOWNSTREAM_CALL_ERROR.getDesc();
                            break;
                        default: // 其他下游 gRPC 错误
                            finalMetricData.code = MetricsCodeEnum.SubCode.DOWNSTREAM_CALL_ERROR.getCode();
                            finalMetricData.msg = MetricsCodeEnum.SubCode.DOWNSTREAM_CALL_ERROR.getDesc();
                            break;
                    }
                    // 如果方法名未被 AspectService 设置 (例如 AspectService 执行前出错)，则从 call 中获取
                    if (RiskMetricsImpl.METRIC_DATA_UNKNOWN.equals(finalMetricData.methodName)) {
                        finalMetricData.methodName = call.getMethodDescriptor().getFullMethodName();
                    }
                    log.info("MetricReportServerInterceptor gRPC 调用在 AspectService 处理之前/期间失败。Status: {}, finalMetricData: {}", status, finalMetricData);
                }

                // 指标上报
                riskMetrics.reportMetricsCount(RiskMetricsImpl.METRIC_KEY_RPC_REQUEST_COUNT, finalMetricData, requestSource);
                riskMetrics.reportMetricsHistogram(RiskMetricsImpl.METRIC_KEY_RPC_LATENCY, finalMetricData, requestSource, durationMs);

                super.close(status, trailers);
            }
        };

        ServerCall.Listener<ReqT> listener;
        try {
            listener = Contexts.interceptCall(context, wrappedCall, headers, next);
        } catch (Exception e) {
            log.error("MetricReportServerInterceptor Contexts.interceptCall err", e);
            // 如果 interceptCall 失败，确保调用被正确关闭
            wrappedCall.close(Status.INTERNAL.withDescription("interceptCall err").withCause(e), new Metadata());
            // 返回一个无操作的 Listener，避免后续处理出错
            return new ServerCall.Listener<ReqT>() {
            };
        }
        return listener;
    }

}