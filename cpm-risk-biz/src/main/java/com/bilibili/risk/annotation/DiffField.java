package com.bilibili.risk.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 标记需要比较差异的字段
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface DiffField {

    /**
     * 字段显示名称（默认使用字段名）
     */
    String name() default "";

    /**
     * 是否忽略该字段（默认不忽略）
     */
    boolean ignore() default false;

    boolean sensitive() default false;
}
