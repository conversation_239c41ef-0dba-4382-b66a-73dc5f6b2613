package com.bilibili.risk.biz.quality.task.service;

import com.bilibili.risk.biz.quality.task.bo.RiskMaterialAuditQualityTaskBo;
import com.bilibili.risk.biz.quality.task.convertor.IRiskMaterialAuditQualityTaskConvertor;
import com.bilibili.risk.dao.risk.RiskMaterialAuditQualityTaskDao;
import com.bilibili.risk.enums.ShardingTableEnum;
import com.bilibili.risk.po.risk.RiskMaterialAuditQualityTaskPo;
import com.bilibili.risk.po.risk.RiskMaterialAuditQualityTaskPoExample;
import com.bilibili.risk.utils.MaterialTaskUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
@AllArgsConstructor
public class RiskMaterialAuditQualityTaskService {

    private final RiskMaterialAuditQualityTaskDao riskMaterialAuditQualityTaskDao;

    public Map<String, RiskMaterialAuditQualityTaskBo> queryMapByTaskIds(List<String> taskIds) {
        List<RiskMaterialAuditQualityTaskBo> auditQualityTaskBos = this.queryListByTaskIds(taskIds);
        return auditQualityTaskBos.stream().collect(Collectors.toMap(t -> t.getTaskId(), t -> t));
    }

    public List<RiskMaterialAuditQualityTaskBo> queryListByTaskIds(List<String> taskIds) {

        if (CollectionUtils.isEmpty(taskIds)) {
            return Collections.emptyList();
        }

        Map<Integer, List<String>> taskIdMap = taskIds.stream()
                .collect(Collectors.groupingBy(t -> MaterialTaskUtils.calculateShardingKeyByTaskId(t, ShardingTableEnum.TABLE_LAU_MATERIAL_AUDIT_QUALITY_TASK.getShardingAlgorithm())));

        List<RiskMaterialAuditQualityTaskPo> qualityTaskPos = new ArrayList<>();
        for (Map.Entry<Integer, List<String>> entry : taskIdMap.entrySet()) {
            RiskMaterialAuditQualityTaskPoExample example = new RiskMaterialAuditQualityTaskPoExample();
            example.createCriteria().andTaskIdIn(entry.getValue());
            List<RiskMaterialAuditQualityTaskPo> tmpList = riskMaterialAuditQualityTaskDao.selectByExample(example);
            if (!org.springframework.util.CollectionUtils.isEmpty(tmpList)) {
                qualityTaskPos.addAll(tmpList);
            }
        }
        return IRiskMaterialAuditQualityTaskConvertor.INSTANCE.pos2bos(qualityTaskPos);
    }

}
