package com.bilibili.risk.biz.quality.task.service;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.risk.biz.quality.task.bo.QualityTaskExtraBo;
import com.bilibili.risk.biz.quality.task.bo.RiskMaterialAuditQualityTaskBo;
import com.bilibili.risk.biz.quality.task.convertor.IRiskMaterialAuditQualityTaskConvertor;
import com.bilibili.risk.bo.LauMaterialAuditTaskBo;
import com.bilibili.risk.constant.RiskConstants;
import com.bilibili.risk.dao.risk.RiskMaterialAuditQualityTaskDao;
import com.bilibili.risk.enums.MaterialTaskStatusEnum;
import com.bilibili.risk.enums.ShardingTableEnum;
import com.bilibili.risk.po.risk.RiskMaterialAuditQualityTaskPo;
import com.bilibili.risk.service.task.MaterialAuditTaskDbService;
import com.bilibili.risk.service.task.TaskMatchQueueService;
import com.bilibili.risk.service.wechat.WechatService;
import com.bilibili.risk.utils.MaterialTaskUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
@AllArgsConstructor
public class QualityTaskGenService {

    private final RiskMaterialAuditQualityTaskService riskMaterialAuditQualityTaskService;
    private final RiskMaterialAuditQualityTaskDao riskMaterialAuditQualityTaskDao;
    private final TaskMatchQueueService taskMatchQueueService;
    private final WechatService wechatService;
    private final MaterialAuditTaskDbService materialAuditTaskDbService;

    /**
     * 生成质检任务
     *
     * @param auditTaskBos
     */
    public void genTask(List<LauMaterialAuditTaskBo> auditTaskBos, Integer forceUpdate) {

        if (CollectionUtils.isEmpty(auditTaskBos)) {
            return;
        }

        // 按任务id分表键分组
        List<String> taskIds = auditTaskBos.stream().map(t -> t.getTaskId()).distinct().collect(Collectors.toList());
        Map<Integer, List<String>> taskIdMap = taskIds.stream()
                .collect(Collectors.groupingBy(t -> MaterialTaskUtils.calculateShardingKeyByTaskId(t, ShardingTableEnum.TABLE_LAU_MATERIAL_AUDIT_QUALITY_TASK.getShardingAlgorithm())));

        // 处理每个分组
        for (Map.Entry<Integer, List<String>> taskIdsOnePar : taskIdMap.entrySet()) {

            Map<String, RiskMaterialAuditQualityTaskBo> auditQualityTaskBoMap = riskMaterialAuditQualityTaskService.queryMapByTaskIds(taskIdsOnePar.getValue());
            // 幂等处理
            if (!Utils.isPositive(forceUpdate)) {
                auditTaskBos = auditTaskBos.stream().filter(t -> auditQualityTaskBoMap.containsKey(t.getTaskId())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(auditTaskBos)) {
                    continue;
                }
            }

            List<RiskMaterialAuditQualityTaskPo> qualityTaskPos = new ArrayList<>();
            for (LauMaterialAuditTaskBo auditTaskBo : auditTaskBos) {
                // todo extra 字段
                QualityTaskExtraBo extraBo = new QualityTaskExtraBo();
                // 原任务信息
                extraBo.setAuditTaskBo(auditTaskBo);

                // 创意信息
                // 单元信息
                // 账户信息
                // 稿件/动态/直播间/等信息

                RiskMaterialAuditQualityTaskBo qualityTaskBo = RiskMaterialAuditQualityTaskBo.builder()
                        .taskId(auditTaskBo.getTaskId())
                        .originTaskId(auditTaskBo.getTaskId())
                        .materialType(auditTaskBo.getMaterialType())
                        .materialId(auditTaskBo.getMaterialId())
                        .creativeId(auditTaskBo.getCreativeId())
                        // 匹配质检队列 id
                        .queueId(taskMatchQueueService.matchQualityQueueId(auditTaskBo))
                        .status(MaterialTaskStatusEnum.TO_MAKE_QUALITY_LABEL.getCode())
                        .version(0)
                        // 默认值表示空
                        .enterAuditTime(RiskConstants.BUSINESS_EARLIEST_TIMESTAMP)
                        .acceptTime(RiskConstants.BUSINESS_EARLIEST_TIMESTAMP)
                        .executeTime(RiskConstants.BUSINESS_EARLIEST_TIMESTAMP)
                        .extra(JSON.toJSONString(extraBo))
                        .qualityRuleId(0)
                        .build();

                // 保存
                RiskMaterialAuditQualityTaskPo qualityTaskPo = IRiskMaterialAuditQualityTaskConvertor.INSTANCE.bo2po(qualityTaskBo);
                qualityTaskPos.add(qualityTaskPo);

                if (!Utils.isPositive(qualityTaskBo.getQueueId())) {
                    wechatService.asyncSendMsg("质检任务没有匹配到质检队列，taskId=" + qualityTaskBo.getTaskId());
                }
            }

            riskMaterialAuditQualityTaskDao.insertUpdateBatch(qualityTaskPos);
        }



    }
}
