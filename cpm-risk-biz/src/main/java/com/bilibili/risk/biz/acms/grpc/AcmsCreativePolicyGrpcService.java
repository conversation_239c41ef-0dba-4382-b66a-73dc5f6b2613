package com.bilibili.risk.biz.acms.grpc;

import com.bapis.ad.risk.acms.AcmsCreativePolicyServiceGrpc;
import com.bapis.ad.risk.acms.CheckPolicyResp;
import com.bapis.ad.risk.acms.PolicyReq;
import com.bapis.ad.risk.acms.PolicyResp;
import com.bilibili.risk.metrics_report.RpcService;
import com.bilibili.risk.biz.acms.creativePolicy.bean.PolicyResponse;
import com.bilibili.risk.biz.acms.creativePolicy.enums.LowRejectLevel;
import com.bilibili.risk.biz.acms.creativePolicy.enums.PolicyType;
import com.bilibili.risk.biz.acms.creativePolicy.service.AcmsCreativePolicyService;
import com.bilibili.risk.enums.MetricsCodeEnum;
import io.grpc.Status;
import io.grpc.stub.StreamObserver;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import pleiades.venus.starter.rpc.server.RPCService;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025-05-14 19:10
 * 机审-创意grpc接口
 */
@RPCService
@RequiredArgsConstructor
@Slf4j
@RpcService(domainType = MetricsCodeEnum.DomainType.OTHER)
public class AcmsCreativePolicyGrpcService extends AcmsCreativePolicyServiceGrpc.AcmsCreativePolicyServiceImplBase {

    private final AcmsCreativePolicyService acmsCreativePolicyService;

    @Override
    public void checkPolicyOnDuty(PolicyReq request, StreamObserver<CheckPolicyResp> responseObserver) {

        try {

            PolicyType policyType = acmsCreativePolicyService.checkPolicyOnDuty(request.getCreativeId());

            responseObserver.onNext(CheckPolicyResp.newBuilder()
                    .setPolicyType(policyType.getCode())
                    .build());
            responseObserver.onCompleted();

        } catch (Throwable e) {
            responseObserver.onError(Status.INTERNAL
                    .withCause(e.getCause())
                    .withDescription(e.getMessage())
                    .asRuntimeException());
            log.error("AcmsCreativePolicyGrpcService checkPolicyOnDuty error", e);
        }
    }

    @Override
    public void executePolicy(PolicyReq request, StreamObserver<PolicyResp> responseObserver) {

        try {

            PolicyResponse response = acmsCreativePolicyService.executePolicy(request.getCreativeId());

            Integer lowRejectLevel = Objects.isNull(response.getLowRejectLevel()) ? LowRejectLevel.UNKNOWN.getCode() : response.getLowRejectLevel().getCode();
            responseObserver.onNext(PolicyResp.newBuilder()
                    .setPolicyType(response.getPolicyType().getCode())
                    .setLowRejectLevel(lowRejectLevel)
                    .build());
            responseObserver.onCompleted();

        } catch (Throwable e) {
            responseObserver.onError(Status.INTERNAL
                    .withCause(e.getCause())
                    .withDescription(e.getMessage())
                    .asRuntimeException());
            log.error("AcmsCreativePolicyGrpcService executePolicy error", e);
        }
    }
}
