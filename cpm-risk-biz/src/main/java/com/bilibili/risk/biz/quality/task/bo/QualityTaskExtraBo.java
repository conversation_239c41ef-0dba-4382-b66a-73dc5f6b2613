package com.bilibili.risk.biz.quality.task.bo;

import com.bilibili.risk.bo.LauMaterialAuditTaskBo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QualityTaskExtraBo implements Serializable {

    // 一审任务信息
    private LauMaterialAuditTaskBo auditTaskBo;

    private static final long serialVersionUID = 1L;
}