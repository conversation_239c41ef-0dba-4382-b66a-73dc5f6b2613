package com.bilibili.risk.biz.quality.rule.service;

import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.util.Page;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.risk.biz.quality.rule.bo.QualityRuleQueryBo;
import com.bilibili.risk.biz.quality.rule.bo.QualityRuleSaveContext;
import com.bilibili.risk.biz.quality.rule.bo.RiskMaterialAuditQualityRuleBo;
import com.bilibili.risk.biz.quality.rule.convertor.IRiskMaterialAuditQualityRuleConvertor;
import com.bilibili.risk.biz.quality.rule.enums.QualityRuleStatusEnum;
import com.bilibili.risk.biz.quality.rule.enums.QualityRuleTypeEnum;
import com.bilibili.risk.biz.queue.enums.QueueLevelEnum;
import com.bilibili.risk.bo.OperatorBo;
import com.bilibili.risk.constant.DatabaseConstant;
import com.bilibili.risk.constant.RiskConstants;
import com.bilibili.risk.dao.risk.LauMaterialAuditQueueDao;
import com.bilibili.risk.dao.risk.RiskMaterialAuditQualityRuleDao;
import com.bilibili.risk.enums.MaterialTaskTypeEnum;
import com.bilibili.risk.po.risk.LauMaterialAuditQueuePo;
import com.bilibili.risk.po.risk.LauMaterialAuditQueuePoExample;
import com.bilibili.risk.po.risk.RiskMaterialAuditQualityRulePo;
import com.bilibili.risk.po.risk.RiskMaterialAuditQualityRulePoExample;
import com.vividsolutions.jts.util.Assert;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class RiskMaterialAuditQualityRuleService implements IRiskMaterialAuditQualityRuleService {

    private final RiskMaterialAuditQualityRuleDao riskMaterialAuditQualityRuleDao;
    private final LauMaterialAuditQueueDao lauMaterialAuditQueueDao;
    private final RedissonClient adpRedissonClient;

    /**
     * 保存质检规则，包含新建和修改
     *
     * @param newQualityRuleBo
     * @param operatorBo
     */
    @Override
    @SneakyThrows
    @Transactional(rollbackFor = Exception.class, transactionManager = DatabaseConstant.RISK_TRANSACTION_MANAGER)
    public Long save(RiskMaterialAuditQualityRuleBo newQualityRuleBo, OperatorBo operatorBo) {

        String lockBody = operatorBo.getBilibiliUserName();
        if (Utils.isPositive(newQualityRuleBo.getId())) {
            lockBody = newQualityRuleBo.getId() + "";
        }

        RLock lock = adpRedissonClient.getLock(RiskConstants.LOCK_QUALITY_RULE_SAVE + lockBody);
        try {
            if (lock.tryLock(3, 10, TimeUnit.SECONDS)) {


                QualityRuleSaveContext ctx = new QualityRuleSaveContext(newQualityRuleBo, operatorBo);
                // 校验
                validate(ctx);

                // 预处理
                preHandle(ctx);

                // 保存
                Long id = save2Db(newQualityRuleBo);

                // todo simer: 日志
                return id;
            } else {
                throw new RuntimeException("当前规则正在被修改，请稍后重试");
            }
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    private Long save2Db(RiskMaterialAuditQualityRuleBo newQualityRuleBo) {
        RiskMaterialAuditQualityRulePo qualityRulePo = IRiskMaterialAuditQualityRuleConvertor.INSTANCE.bo2Po(newQualityRuleBo);
        riskMaterialAuditQualityRuleDao.insertUpdateSelective(qualityRulePo);
        return qualityRulePo.getId();
    }

    /**
     * 删除规则
     *
     * @param id
     * @param operatorBo
     */
    @Override
    public Integer delete(Long id, OperatorBo operatorBo) {
        RiskMaterialAuditQualityRulePo qualityRulePo = riskMaterialAuditQualityRuleDao.selectByPrimaryKey(id);
        Assert.isTrue(qualityRulePo != null, "规则不存在,id=" + id);
        qualityRulePo.setIsDeleted(IsDeleted.DELETED.getCode());
        return riskMaterialAuditQualityRuleDao.updateByPrimaryKeySelective(qualityRulePo);
    }

    @Override
    public PageResult<RiskMaterialAuditQualityRuleBo> queryList(Integer page, Integer pageSize, QualityRuleQueryBo queryBo) {

        RiskMaterialAuditQualityRulePoExample example = new RiskMaterialAuditQualityRulePoExample();
        RiskMaterialAuditQualityRulePoExample.Criteria criteria = example.createCriteria();
        example.setOrderByClause("mtime desc");

        if (!CollectionUtils.isEmpty(queryBo.getIds())) {
            criteria.andIdIn(queryBo.getIds());
        }
        if (!CollectionUtils.isEmpty(queryBo.getBizTypes())) {
            criteria.andBizTypeIn(queryBo.getBizTypes());
        }
        if (!CollectionUtils.isEmpty(queryBo.getMaterialTypes())) {
            criteria.andMaterialTypeIn(queryBo.getMaterialTypes());
        }
        if (!CollectionUtils.isEmpty(queryBo.getTypes())) {
            criteria.andTaskTypeIn(queryBo.getTypes());
        }
        if (!CollectionUtils.isEmpty(queryBo.getQualityRuleTypes())) {
            criteria.andQualityRuleTypeIn(queryBo.getQualityRuleTypes());
        }

        Page pg = Page.valueOf(page, pageSize);
        example.setLimit(pg.getLimit());
        example.setOffset(pg.getOffset());

        long total = riskMaterialAuditQualityRuleDao.countByExample(example);
        if (total == 0) {
            return PageResult.emptyPageResult();
        }
        List<RiskMaterialAuditQualityRulePo> qualityRulePos = riskMaterialAuditQualityRuleDao.selectByExample(example);
        List<RiskMaterialAuditQualityRuleBo> qualityRuleBos = IRiskMaterialAuditQualityRuleConvertor.INSTANCE.pos2Bos(qualityRulePos);

        return PageResult.<RiskMaterialAuditQualityRuleBo>builder()
                .total((int) total)
                .records(qualityRuleBos)
                .build();
    }

    @Override
    public Integer executeMakeLabels(Long id, OperatorBo operatorBo) {

        // 限流

        // 修改执行中
        
        // 异步执行任务


        return 0;
    }

    @Override
    public Integer clearLabels(Long id, OperatorBo operatorBo) {


        return 0;
    }

    private void preHandle(QualityRuleSaveContext ctx) {

        RiskMaterialAuditQualityRuleBo newQualityRuleBo = ctx.getNewQualityRuleBo();
        if (!CollectionUtils.isEmpty(newQualityRuleBo.getRelatedIdList())) {
            newQualityRuleBo.setRelatedIds(newQualityRuleBo.getRelatedIdList().stream().collect(Collectors.joining(",")));
        }
        // 检查队列，行业等，暂时不加

        // 修改的相关校验处理
        if (Utils.isPositive(newQualityRuleBo.getId())) {
            RiskMaterialAuditQualityRulePo curQualityRulePo = riskMaterialAuditQualityRuleDao.selectByPrimaryKey(newQualityRuleBo.getId());
            Assert.isTrue(curQualityRulePo != null, "规则不存在,id=" + newQualityRuleBo.getId());
            RiskMaterialAuditQualityRuleBo curQualityRuleBo = IRiskMaterialAuditQualityRuleConvertor.INSTANCE.po2Bo(curQualityRulePo);
            ctx.setCurQualityRuleBo(curQualityRuleBo);

            // 结束时间未改动
            if (curQualityRulePo.getScheduleDateEnd().equals(newQualityRuleBo.getScheduleDateEnd())) {
                // 待执行才能编辑
                Assert.isTrue(Objects.equals(curQualityRulePo.getStatus(), QualityRuleStatusEnum.TODO.getCode()), "待执行状态才能编辑");
            }

            // 未到结束时间的周期规则，允许更改结束时间
            if (Utils.isPositive(curQualityRulePo.getIsSchedule()) && !newQualityRuleBo.getScheduleDateEnd().after(Utils.getNow()) && !curQualityRulePo.getScheduleDateEnd().equals(newQualityRuleBo.getScheduleDateEnd())) {
                throw new RuntimeException("定时执行结束时间已过，不允许修改");
            }

        }

        // 只能选择一审审核队列
        if (Objects.equals(newQualityRuleBo.getQualityRuleType(), QualityRuleTypeEnum.QUEUE.getKey())) {
            LauMaterialAuditQueuePoExample example = new LauMaterialAuditQueuePoExample();
            example.createCriteria()
                    .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                    .andQueueLevelEqualTo(QueueLevelEnum.AUDIT_QUEUE.getCode())
                    .andIdIn(newQualityRuleBo.getRelatedIdList().stream().map(Long::valueOf).collect(Collectors.toList()));
            List<LauMaterialAuditQueuePo> auditQueuePos = lauMaterialAuditQueueDao.selectByExample(example);
            Assert.isTrue(auditQueuePos.size() == newQualityRuleBo.getRelatedIdList().size(), "存在队列不存在，请检查");
//            List<LauMaterialAuditQueueBo> auditQueueBos = IMaterialAuditQueueConvertor.INSTANCE.pos2bos(auditQueuePos);
        }

    }

    private void validate(QualityRuleSaveContext ctx) {
        RiskMaterialAuditQualityRuleBo qualityRuleBo = ctx.getNewQualityRuleBo();

        Assert.isTrue(Utils.isPositive(qualityRuleBo.getBizType()), "业务类型不能为空");
        Assert.isTrue(MaterialTaskTypeEnum.FIRST_AUDIT_TASK_TYPES.contains(qualityRuleBo.getBizType()), "任务类型不合法");
        Assert.isTrue(Utils.isPositive(qualityRuleBo.getMaterialType()), "素材类型不能为空");
        Optional<QualityRuleTypeEnum> qualityRuleTypeEnumOptional = QualityRuleTypeEnum.getByCode(qualityRuleBo.getQualityRuleType());
        Assert.isTrue(qualityRuleTypeEnumOptional.isPresent(), "质检规则类型不能为空");

        switch (qualityRuleTypeEnumOptional.get()) {
            case PERSON:
            case INDUSTRY:
            case QUEUE:
            case AMCS_TAG:
                Assert.isTrue(!CollectionUtils.isEmpty(qualityRuleBo.getRelatedIdList()), "未选择规则类型下关联的人员/行业/队列/机审标签");
            case ALL:
                break;
        }

        Assert.isTrue(qualityRuleBo.getExecuteDateBegin() != null, "开始审出日期不能为空");
        Assert.isTrue(qualityRuleBo.getExecuteDateEnd() != null, "结束审出日期不能为空");
        Assert.isTrue(qualityRuleBo.getRate() > 0 && qualityRuleBo.getRate() <= 100, "随机比例不合法");

        if (Utils.isPositive(qualityRuleBo.getIsSchedule())) {
            Assert.isTrue(qualityRuleBo.getScheduleDateBegin() != null, "周期规则开始时间不能为空");
            Assert.isTrue(qualityRuleBo.getScheduleDateEnd() != null, "周期规则结束时间不能为空");
        }

    }

}
