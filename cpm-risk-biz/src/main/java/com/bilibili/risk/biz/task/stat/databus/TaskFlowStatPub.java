package com.bilibili.risk.biz.task.stat.databus;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.risk.biz.task.stat.databus.bean.TaskFlowStatMsg;
import com.bilibili.risk.databus.DatabusKeyConstants;
import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.databus.DatabusTemplate;
import com.bilibili.warp.databus.Message;
import com.bilibili.warp.databus.PubResult;
import com.bilibili.warp.spring.boot.autoconfigure.databus.DatabusProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025-05-20 16:04
 * 风控-任务流量-上报-统计-pub
 */
@Slf4j
@Service
public class TaskFlowStatPub {

    private final String topic;
    private final String group;

    @Resource
    private DatabusTemplate template;

    public void pub (TaskFlowStatMsg msg) {

        String msgKey = String.valueOf(Utils.getNow().getTime());
        Message message = Message.Builder
                .of(msgKey, msg)
                .build();

        PubResult result = template.pub(topic, group, message);

        if (!result.isSuccess()) {
            log.error("TaskFlowStatPub pub error, msg: {}", msg, result.getThrowable());
        }
    }

    public TaskFlowStatPub(DatabusProperties databusProperties) {

        DatabusProperty databusProperty = databusProperties.getProperties().get(DatabusKeyConstants.TASK_FLOW_STAT);
        this.topic = databusProperty.getTopic();
        this.group = databusProperty.getPub().getGroup();
    }
}
