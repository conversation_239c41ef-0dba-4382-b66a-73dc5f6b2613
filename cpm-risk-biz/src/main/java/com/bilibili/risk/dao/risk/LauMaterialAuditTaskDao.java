package com.bilibili.risk.dao.risk;

import com.bilibili.risk.po.risk.LauMaterialAuditTaskPo;
import java.util.List;

import com.bilibili.risk.po.risk.LauMaterialAuditTaskPoExample;
import org.apache.ibatis.annotations.Param;

public interface LauMaterialAuditTaskDao {
    long countByExample(LauMaterialAuditTaskPoExample example);

    int deleteByExample(LauMaterialAuditTaskPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(LauMaterialAuditTaskPo record);

    int insertBatch(List<LauMaterialAuditTaskPo> records);

    int insertUpdateBatch(List<LauMaterialAuditTaskPo> records);

    int insert(LauMaterialAuditTaskPo record);

    int insertUpdateSelective(LauMaterialAuditTaskPo record);

    int insertSelective(LauMaterialAuditTaskPo record);

    List<LauMaterialAuditTaskPo> selectByExampleWithBLOBs(LauMaterialAuditTaskPoExample example);

    List<LauMaterialAuditTaskPo> selectByExample(LauMaterialAuditTaskPoExample example);

    LauMaterialAuditTaskPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") LauMaterialAuditTaskPo record, @Param("example") LauMaterialAuditTaskPoExample example);

    int updateByExampleWithBLOBs(@Param("record") LauMaterialAuditTaskPo record, @Param("example") LauMaterialAuditTaskPoExample example);

    int updateByExample(@Param("record") LauMaterialAuditTaskPo record, @Param("example") LauMaterialAuditTaskPoExample example);

    int updateByPrimaryKeySelective(LauMaterialAuditTaskPo record);

    int updateByPrimaryKeyWithBLOBs(LauMaterialAuditTaskPo record);

    int updateByPrimaryKey(LauMaterialAuditTaskPo record);
}