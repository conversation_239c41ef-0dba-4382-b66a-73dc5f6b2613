package com.bilibili.risk.dao.ad_core;

import com.bilibili.risk.po.ad.LauCreativeLandingPageGroupPo;
import com.bilibili.risk.po.ad.LauCreativeLandingPageGroupPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface LauCreativeLandingPageGroupDao {
    long countByExample(LauCreativeLandingPageGroupPoExample example);

    int deleteByExample(LauCreativeLandingPageGroupPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(LauCreativeLandingPageGroupPo record);

    int insertBatch(List<LauCreativeLandingPageGroupPo> records);

    int insertUpdateBatch(List<LauCreativeLandingPageGroupPo> records);

    int insert(LauCreativeLandingPageGroupPo record);

    int insertUpdateSelective(LauCreativeLandingPageGroupPo record);

    int insertSelective(LauCreativeLandingPageGroupPo record);

    List<LauCreativeLandingPageGroupPo> selectByExample(LauCreativeLandingPageGroupPoExample example);

    LauCreativeLandingPageGroupPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") LauCreativeLandingPageGroupPo record, @Param("example") LauCreativeLandingPageGroupPoExample example);

    int updateByExample(@Param("record") LauCreativeLandingPageGroupPo record, @Param("example") LauCreativeLandingPageGroupPoExample example);

    int updateByPrimaryKeySelective(LauCreativeLandingPageGroupPo record);

    int updateByPrimaryKey(LauCreativeLandingPageGroupPo record);
}