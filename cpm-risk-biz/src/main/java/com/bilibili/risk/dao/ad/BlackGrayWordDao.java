package com.bilibili.risk.dao.ad;

import com.bilibili.risk.po.ad.BlackGrayWordPo;
import com.bilibili.risk.po.ad.BlackGrayWordPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface BlackGrayWordDao {
    long countByExample(BlackGrayWordPoExample example);

    int deleteByExample(BlackGrayWordPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(BlackGrayWordPo record);

    int insertBatch(List<BlackGrayWordPo> records);

    int insertUpdateBatch(List<BlackGrayWordPo> records);

    int insert(BlackGrayWordPo record);

    int insertUpdateSelective(BlackGrayWordPo record);

    int insertSelective(BlackGrayWordPo record);

    List<BlackGrayWordPo> selectByExample(BlackGrayWordPoExample example);

    BlackGrayWordPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") BlackGrayWordPo record, @Param("example") BlackGrayWordPoExample example);

    int updateByExample(@Param("record") BlackGrayWordPo record, @Param("example") BlackGrayWordPoExample example);

    int updateByPrimaryKeySelective(BlackGrayWordPo record);

    int updateByPrimaryKey(BlackGrayWordPo record);
}