package com.bilibili.risk.dao.risk;

import com.bilibili.risk.po.risk.LauMaterialAuditRulePo;
import com.bilibili.risk.po.risk.LauMaterialAuditRulePoExample;
import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface LauMaterialAuditRuleDao {
    long countByExample(LauMaterialAuditRulePoExample example);

    int deleteByExample(LauMaterialAuditRulePoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(LauMaterialAuditRulePo record);

    int insertBatch(List<LauMaterialAuditRulePo> records);

    int insertUpdateBatch(List<LauMaterialAuditRulePo> records);

    int insert(LauMaterialAuditRulePo record);

    int insertUpdateSelective(LauMaterialAuditRulePo record);

    int insertSelective(LauMaterialAuditRulePo record);

    List<LauMaterialAuditRulePo> selectByExample(LauMaterialAuditRulePoExample example);

    LauMaterialAuditRulePo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") LauMaterialAuditRulePo record, @Param("example") LauMaterialAuditRulePoExample example);

    int updateByExample(@Param("record") LauMaterialAuditRulePo record, @Param("example") LauMaterialAuditRulePoExample example);

    int updateByPrimaryKeySelective(LauMaterialAuditRulePo record);

    int updateByPrimaryKey(LauMaterialAuditRulePo record);
}