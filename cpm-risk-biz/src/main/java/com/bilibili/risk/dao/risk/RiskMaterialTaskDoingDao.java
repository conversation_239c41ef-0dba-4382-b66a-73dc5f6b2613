package com.bilibili.risk.dao.risk;

import com.bilibili.risk.po.risk.RiskMaterialTaskDoingPo;
import com.bilibili.risk.po.risk.RiskMaterialTaskDoingPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface RiskMaterialTaskDoingDao {
    long countByExample(RiskMaterialTaskDoingPoExample example);

    int deleteByExample(RiskMaterialTaskDoingPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(RiskMaterialTaskDoingPo record);

    int insertBatch(List<RiskMaterialTaskDoingPo> records);

    int insertUpdateBatch(List<RiskMaterialTaskDoingPo> records);

    int insert(RiskMaterialTaskDoingPo record);

    int insertUpdateSelective(RiskMaterialTaskDoingPo record);

    int insertSelective(RiskMaterialTaskDoingPo record);

    List<RiskMaterialTaskDoingPo> selectByExample(RiskMaterialTaskDoingPoExample example);

    RiskMaterialTaskDoingPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") RiskMaterialTaskDoingPo record, @Param("example") RiskMaterialTaskDoingPoExample example);

    int updateByExample(@Param("record") RiskMaterialTaskDoingPo record, @Param("example") RiskMaterialTaskDoingPoExample example);

    int updateByPrimaryKeySelective(RiskMaterialTaskDoingPo record);

    int updateByPrimaryKey(RiskMaterialTaskDoingPo record);
}