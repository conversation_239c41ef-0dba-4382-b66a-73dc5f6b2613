package com.bilibili.risk.dao.risk;

import com.bilibili.risk.po.risk.LauMaterialAuditRuleRoleRelPo;
import com.bilibili.risk.po.risk.LauMaterialAuditRuleRoleRelPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface LauMaterialAuditRuleRoleRelDao {
    long countByExample(LauMaterialAuditRuleRoleRelPoExample example);

    int deleteByExample(LauMaterialAuditRuleRoleRelPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(LauMaterialAuditRuleRoleRelPo record);

    int insertBatch(List<LauMaterialAuditRuleRoleRelPo> records);

    int insertUpdateBatch(List<LauMaterialAuditRuleRoleRelPo> records);

    int insert(LauMaterialAuditRuleRoleRelPo record);

    int insertUpdateSelective(LauMaterialAuditRuleRoleRelPo record);

    int insertSelective(LauMaterialAuditRuleRoleRelPo record);

    List<LauMaterialAuditRuleRoleRelPo> selectByExample(LauMaterialAuditRuleRoleRelPoExample example);

    LauMaterialAuditRuleRoleRelPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") LauMaterialAuditRuleRoleRelPo record, @Param("example") LauMaterialAuditRuleRoleRelPoExample example);

    int updateByExample(@Param("record") LauMaterialAuditRuleRoleRelPo record, @Param("example") LauMaterialAuditRuleRoleRelPoExample example);

    int updateByPrimaryKeySelective(LauMaterialAuditRuleRoleRelPo record);

    int updateByPrimaryKey(LauMaterialAuditRuleRoleRelPo record);
}