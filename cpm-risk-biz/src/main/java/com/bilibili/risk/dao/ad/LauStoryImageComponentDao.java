package com.bilibili.risk.dao.ad;

import com.bilibili.risk.po.ad.LauStoryImageComponentPo;
import com.bilibili.risk.po.ad.LauStoryImageComponentPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface LauStoryImageComponentDao {
    long countByExample(LauStoryImageComponentPoExample example);

    int deleteByExample(LauStoryImageComponentPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(LauStoryImageComponentPo record);

    int insertBatch(List<LauStoryImageComponentPo> records);

    int insertUpdateBatch(List<LauStoryImageComponentPo> records);

    int insert(LauStoryImageComponentPo record);

    int insertUpdateSelective(LauStoryImageComponentPo record);

    int insertSelective(LauStoryImageComponentPo record);

    List<LauStoryImageComponentPo> selectByExample(LauStoryImageComponentPoExample example);

    LauStoryImageComponentPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") LauStoryImageComponentPo record, @Param("example") LauStoryImageComponentPoExample example);

    int updateByExample(@Param("record") LauStoryImageComponentPo record, @Param("example") LauStoryImageComponentPoExample example);

    int updateByPrimaryKeySelective(LauStoryImageComponentPo record);

    int updateByPrimaryKey(LauStoryImageComponentPo record);
}