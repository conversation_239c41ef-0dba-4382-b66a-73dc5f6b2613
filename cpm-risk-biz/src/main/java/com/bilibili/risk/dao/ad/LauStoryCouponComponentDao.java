package com.bilibili.risk.dao.ad;

import com.bilibili.risk.po.ad.LauStoryCouponComponentPo;
import com.bilibili.risk.po.ad.LauStoryCouponComponentPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface LauStoryCouponComponentDao {
    long countByExample(LauStoryCouponComponentPoExample example);

    int deleteByExample(LauStoryCouponComponentPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(LauStoryCouponComponentPo record);

    int insertBatch(List<LauStoryCouponComponentPo> records);

    int insertUpdateBatch(List<LauStoryCouponComponentPo> records);

    int insert(LauStoryCouponComponentPo record);

    int insertUpdateSelective(LauStoryCouponComponentPo record);

    int insertSelective(LauStoryCouponComponentPo record);

    List<LauStoryCouponComponentPo> selectByExample(LauStoryCouponComponentPoExample example);

    LauStoryCouponComponentPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") LauStoryCouponComponentPo record, @Param("example") LauStoryCouponComponentPoExample example);

    int updateByExample(@Param("record") LauStoryCouponComponentPo record, @Param("example") LauStoryCouponComponentPoExample example);

    int updateByPrimaryKeySelective(LauStoryCouponComponentPo record);

    int updateByPrimaryKey(LauStoryCouponComponentPo record);
}