package com.bilibili.risk.dao.risk;

import com.bilibili.risk.po.risk.LauMaterialAuditPo;
import com.bilibili.risk.po.risk.LauMaterialAuditPoExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface LauMaterialAuditExtDao {

    LauMaterialAuditPo selectByPrimaryKey(String materialId);

    List<LauMaterialAuditPo> selectByExampleWithShardingNo(@Param("shardingNo") int shardingNo, @Param("example") LauMaterialAuditPoExample example);

    int updateByPrimaryKeySelective(LauMaterialAuditPo record);
}
