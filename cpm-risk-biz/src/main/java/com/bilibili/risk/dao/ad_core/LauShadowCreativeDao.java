package com.bilibili.risk.dao.ad_core;

import com.bilibili.risk.po.ad.LauShadowCreativePo;
import com.bilibili.risk.po.ad.LauShadowCreativePoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface LauShadowCreativeDao {
    long countByExample(LauShadowCreativePoExample example);

    int deleteByExample(LauShadowCreativePoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(LauShadowCreativePo record);

    int insertBatch(List<LauShadowCreativePo> records);

    int insertUpdateBatch(List<LauShadowCreativePo> records);

    int insert(LauShadowCreativePo record);

    int insertUpdateSelective(LauShadowCreativePo record);

    int insertSelective(LauShadowCreativePo record);

    List<LauShadowCreativePo> selectByExampleWithBLOBs(LauShadowCreativePoExample example);

    List<LauShadowCreativePo> selectByExample(LauShadowCreativePoExample example);

    LauShadowCreativePo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") LauShadowCreativePo record, @Param("example") LauShadowCreativePoExample example);

    int updateByExampleWithBLOBs(@Param("record") LauShadowCreativePo record, @Param("example") LauShadowCreativePoExample example);

    int updateByExample(@Param("record") LauShadowCreativePo record, @Param("example") LauShadowCreativePoExample example);

    int updateByPrimaryKeySelective(LauShadowCreativePo record);

    int updateByPrimaryKeyWithBLOBs(LauShadowCreativePo record);

    int updateByPrimaryKey(LauShadowCreativePo record);
}