package com.bilibili.risk.dao.ad_core;

import com.bilibili.risk.po.ad.LauCreativeComponentPo;
import com.bilibili.risk.po.ad.LauCreativeComponentPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface LauCreativeComponentDao {
    long countByExample(LauCreativeComponentPoExample example);

    int deleteByExample(LauCreativeComponentPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(LauCreativeComponentPo record);

    int insertBatch(List<LauCreativeComponentPo> records);

    int insertUpdateBatch(List<LauCreativeComponentPo> records);

    int insert(LauCreativeComponentPo record);

    int insertUpdateSelective(LauCreativeComponentPo record);

    int insertSelective(LauCreativeComponentPo record);

    List<LauCreativeComponentPo> selectByExample(LauCreativeComponentPoExample example);

    LauCreativeComponentPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") LauCreativeComponentPo record, @Param("example") LauCreativeComponentPoExample example);

    int updateByExample(@Param("record") LauCreativeComponentPo record, @Param("example") LauCreativeComponentPoExample example);

    int updateByPrimaryKeySelective(LauCreativeComponentPo record);

    int updateByPrimaryKey(LauCreativeComponentPo record);
}