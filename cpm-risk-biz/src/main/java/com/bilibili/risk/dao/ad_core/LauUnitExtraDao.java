package com.bilibili.risk.dao.ad_core;

import com.bilibili.risk.po.ad.LauUnitExtraPo;
import com.bilibili.risk.po.ad.LauUnitExtraPoExample;
import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface LauUnitExtraDao {
    long countByExample(LauUnitExtraPoExample example);

    int deleteByExample(LauUnitExtraPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(LauUnitExtraPo record);

    int insertBatch(List<LauUnitExtraPo> records);

    int insertUpdateBatch(List<LauUnitExtraPo> records);

    int insert(LauUnitExtraPo record);

    int insertUpdateSelective(LauUnitExtraPo record);

    int insertSelective(LauUnitExtraPo record);

    List<LauUnitExtraPo> selectByExample(LauUnitExtraPoExample example);

    LauUnitExtraPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") LauUnitExtraPo record, @Param("example") LauUnitExtraPoExample example);

    int updateByExample(@Param("record") LauUnitExtraPo record, @Param("example") LauUnitExtraPoExample example);

    int updateByPrimaryKeySelective(LauUnitExtraPo record);

    int updateByPrimaryKey(LauUnitExtraPo record);
}