package com.bilibili.risk.dao.risk;

import com.bilibili.risk.po.risk.LauMaterialAuditLabelPo;
import com.bilibili.risk.po.risk.LauMaterialAuditLabelPoExample;
import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface LauMaterialAuditLabelDao {
    long countByExample(LauMaterialAuditLabelPoExample example);

    int deleteByExample(LauMaterialAuditLabelPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(LauMaterialAuditLabelPo record);

    int insertBatch(List<LauMaterialAuditLabelPo> records);

    int insertUpdateBatch(List<LauMaterialAuditLabelPo> records);

    int insert(LauMaterialAuditLabelPo record);

    int insertUpdateSelective(LauMaterialAuditLabelPo record);

    int insertSelective(LauMaterialAuditLabelPo record);

    List<LauMaterialAuditLabelPo> selectByExample(LauMaterialAuditLabelPoExample example);

    LauMaterialAuditLabelPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") LauMaterialAuditLabelPo record, @Param("example") LauMaterialAuditLabelPoExample example);

    int updateByExample(@Param("record") LauMaterialAuditLabelPo record, @Param("example") LauMaterialAuditLabelPoExample example);

    int updateByPrimaryKeySelective(LauMaterialAuditLabelPo record);

    int updateByPrimaryKey(LauMaterialAuditLabelPo record);
}