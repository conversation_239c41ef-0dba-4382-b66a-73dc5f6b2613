package com.bilibili.risk.dao.ad_core;

import com.bilibili.risk.po.ad.LauProgrammaticCreativeDetailPo;
import com.bilibili.risk.po.ad.LauProgrammaticCreativeDetailPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface LauProgrammaticCreativeDetailDao {
    long countByExample(LauProgrammaticCreativeDetailPoExample example);

    int deleteByExample(LauProgrammaticCreativeDetailPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(LauProgrammaticCreativeDetailPo record);

    int insertBatch(List<LauProgrammaticCreativeDetailPo> records);

    int insertUpdateBatch(List<LauProgrammaticCreativeDetailPo> records);

    int insert(LauProgrammaticCreativeDetailPo record);

    int insertUpdateSelective(LauProgrammaticCreativeDetailPo record);

    int insertSelective(LauProgrammaticCreativeDetailPo record);

    List<LauProgrammaticCreativeDetailPo> selectByExample(LauProgrammaticCreativeDetailPoExample example);

    LauProgrammaticCreativeDetailPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") LauProgrammaticCreativeDetailPo record, @Param("example") LauProgrammaticCreativeDetailPoExample example);

    int updateByExample(@Param("record") LauProgrammaticCreativeDetailPo record, @Param("example") LauProgrammaticCreativeDetailPoExample example);

    int updateByPrimaryKeySelective(LauProgrammaticCreativeDetailPo record);

    int updateByPrimaryKey(LauProgrammaticCreativeDetailPo record);
}