package com.bilibili.risk.dao.risk;

import com.bilibili.risk.po.risk.LauMaterialAuditQueuePo;
import com.bilibili.risk.po.risk.LauMaterialAuditQueuePoExample;
import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface LauMaterialAuditQueueDao {
    long countByExample(LauMaterialAuditQueuePoExample example);

    int deleteByExample(LauMaterialAuditQueuePoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(LauMaterialAuditQueuePo record);

    int insertBatch(List<LauMaterialAuditQueuePo> records);

    int insertUpdateBatch(List<LauMaterialAuditQueuePo> records);

    int insert(LauMaterialAuditQueuePo record);

    int insertUpdateSelective(LauMaterialAuditQueuePo record);

    int insertSelective(LauMaterialAuditQueuePo record);

    List<LauMaterialAuditQueuePo> selectByExample(LauMaterialAuditQueuePoExample example);

    LauMaterialAuditQueuePo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") LauMaterialAuditQueuePo record, @Param("example") LauMaterialAuditQueuePoExample example);

    int updateByExample(@Param("record") LauMaterialAuditQueuePo record, @Param("example") LauMaterialAuditQueuePoExample example);

    int updateByPrimaryKeySelective(LauMaterialAuditQueuePo record);

    int updateByPrimaryKey(LauMaterialAuditQueuePo record);
}