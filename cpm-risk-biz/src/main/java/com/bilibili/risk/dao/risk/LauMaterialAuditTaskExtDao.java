package com.bilibili.risk.dao.risk;

import com.bilibili.risk.bo.LauMaterialAuditTaskUpdateBo;
import com.bilibili.risk.po.risk.LauMaterialAuditTaskPo;
import com.bilibili.risk.po.risk.LauMaterialAuditTaskPoExample;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface LauMaterialAuditTaskExtDao {

    Integer updateMaterialAuditTaskBosOptimistic(List<LauMaterialAuditTaskUpdateBo> updateBos);

    List<LauMaterialAuditTaskPo> selectByExample(@Param("shardingNo") int shardingNo, @Param("example") LauMaterialAuditTaskPoExample example);

    int updateByPrimaryKeySelective(@Param("shardingNo") int shardingNo, @Param("record") LauMaterialAuditTaskPo record);

}