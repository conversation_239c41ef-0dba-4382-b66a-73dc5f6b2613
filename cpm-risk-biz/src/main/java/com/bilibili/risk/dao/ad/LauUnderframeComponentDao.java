package com.bilibili.risk.dao.ad;

import com.bilibili.risk.po.ad.LauUnderframeComponentPo;
import com.bilibili.risk.po.ad.LauUnderframeComponentPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface LauUnderframeComponentDao {
    long countByExample(LauUnderframeComponentPoExample example);

    int deleteByExample(LauUnderframeComponentPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(LauUnderframeComponentPo record);

    int insertBatch(List<LauUnderframeComponentPo> records);

    int insertUpdateBatch(List<LauUnderframeComponentPo> records);

    int insert(LauUnderframeComponentPo record);

    int insertUpdateSelective(LauUnderframeComponentPo record);

    int insertSelective(LauUnderframeComponentPo record);

    List<LauUnderframeComponentPo> selectByExample(LauUnderframeComponentPoExample example);

    LauUnderframeComponentPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") LauUnderframeComponentPo record, @Param("example") LauUnderframeComponentPoExample example);

    int updateByExample(@Param("record") LauUnderframeComponentPo record, @Param("example") LauUnderframeComponentPoExample example);

    int updateByPrimaryKeySelective(LauUnderframeComponentPo record);

    int updateByPrimaryKey(LauUnderframeComponentPo record);
}