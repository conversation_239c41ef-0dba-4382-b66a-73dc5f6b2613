package com.bilibili.risk.dao.risk;

import com.bilibili.risk.po.risk.LauMaterialToAuditCreativePo;
import com.bilibili.risk.po.risk.LauMaterialToAuditCreativePoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface LauMaterialToAuditCreativeDao {
    long countByExample(LauMaterialToAuditCreativePoExample example);

    int deleteByExample(LauMaterialToAuditCreativePoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(LauMaterialToAuditCreativePo record);

    int insertBatch(List<LauMaterialToAuditCreativePo> records);

    int insertUpdateBatch(List<LauMaterialToAuditCreativePo> records);

    int insert(LauMaterialToAuditCreativePo record);

    int insertUpdateSelective(LauMaterialToAuditCreativePo record);

    int insertSelective(LauMaterialToAuditCreativePo record);

    List<LauMaterialToAuditCreativePo> selectByExample(LauMaterialToAuditCreativePoExample example);

    LauMaterialToAuditCreativePo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") LauMaterialToAuditCreativePo record, @Param("example") LauMaterialToAuditCreativePoExample example);

    int updateByExample(@Param("record") LauMaterialToAuditCreativePo record, @Param("example") LauMaterialToAuditCreativePoExample example);

    int updateByPrimaryKeySelective(LauMaterialToAuditCreativePo record);

    int updateByPrimaryKey(LauMaterialToAuditCreativePo record);
}