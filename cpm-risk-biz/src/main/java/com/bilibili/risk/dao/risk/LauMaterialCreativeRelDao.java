package com.bilibili.risk.dao.risk;

import com.bilibili.risk.po.risk.LauMaterialCreativeRelPo;
import com.bilibili.risk.po.risk.LauMaterialCreativeRelPoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface LauMaterialCreativeRelDao {
    long countByExample(LauMaterialCreativeRelPoExample example);

    int deleteByExample(LauMaterialCreativeRelPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(LauMaterialCreativeRelPo record);

    int insertBatch(List<LauMaterialCreativeRelPo> records);

    int insertUpdateBatch(List<LauMaterialCreativeRelPo> records);

    int insert(LauMaterialCreativeRelPo record);

    int insertUpdateSelective(LauMaterialCreativeRelPo record);

    int insertSelective(LauMaterialCreativeRelPo record);

    List<LauMaterialCreativeRelPo> selectByExample(LauMaterialCreativeRelPoExample example);

    LauMaterialCreativeRelPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") LauMaterialCreativeRelPo record, @Param("example") LauMaterialCreativeRelPoExample example);

    int updateByExample(@Param("record") LauMaterialCreativeRelPo record, @Param("example") LauMaterialCreativeRelPoExample example);

    int updateByPrimaryKeySelective(LauMaterialCreativeRelPo record);

    int updateByPrimaryKey(LauMaterialCreativeRelPo record);
}