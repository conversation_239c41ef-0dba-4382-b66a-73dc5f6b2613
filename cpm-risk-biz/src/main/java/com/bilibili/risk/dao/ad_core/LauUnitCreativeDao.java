package com.bilibili.risk.dao.ad_core;

import com.bilibili.risk.po.ad.LauUnitCreativePo;
import com.bilibili.risk.po.ad.LauUnitCreativePoExample;
import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface LauUnitCreativeDao {
    long countByExample(LauUnitCreativePoExample example);

    int deleteByExample(LauUnitCreativePoExample example);

    int deleteByPrimaryKey(Integer creativeId);

    int insertUpdate(LauUnitCreativePo record);

    int insertBatch(List<LauUnitCreativePo> records);

    int insertUpdateBatch(List<LauUnitCreativePo> records);

    int insert(LauUnitCreativePo record);

    int insertUpdateSelective(LauUnitCreativePo record);

    int insertSelective(LauUnitCreativePo record);

    List<LauUnitCreativePo> selectByExample(LauUnitCreativePoExample example);

    LauUnitCreativePo selectByPrimaryKey(Integer creativeId);

    int updateByExampleSelective(@Param("record") LauUnitCreativePo record, @Param("example") LauUnitCreativePoExample example);

    int updateByExample(@Param("record") LauUnitCreativePo record, @Param("example") LauUnitCreativePoExample example);

    int updateByPrimaryKeySelective(LauUnitCreativePo record);

    int updateByPrimaryKey(LauUnitCreativePo record);
}