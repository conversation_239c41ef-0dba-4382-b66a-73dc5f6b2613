package com.bilibili.risk.dao.ad;

import com.bilibili.risk.po.ad.LauStoryCommonComponentPo;
import com.bilibili.risk.po.ad.LauStoryCommonComponentPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface LauStoryCommonComponentDao {
    long countByExample(LauStoryCommonComponentPoExample example);

    int deleteByExample(LauStoryCommonComponentPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(LauStoryCommonComponentPo record);

    int insertBatch(List<LauStoryCommonComponentPo> records);

    int insertUpdateBatch(List<LauStoryCommonComponentPo> records);

    int insert(LauStoryCommonComponentPo record);

    int insertUpdateSelective(LauStoryCommonComponentPo record);

    int insertSelective(LauStoryCommonComponentPo record);

    List<LauStoryCommonComponentPo> selectByExample(LauStoryCommonComponentPoExample example);

    LauStoryCommonComponentPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") LauStoryCommonComponentPo record, @Param("example") LauStoryCommonComponentPoExample example);

    int updateByExample(@Param("record") LauStoryCommonComponentPo record, @Param("example") LauStoryCommonComponentPoExample example);

    int updateByPrimaryKeySelective(LauStoryCommonComponentPo record);

    int updateByPrimaryKey(LauStoryCommonComponentPo record);
}