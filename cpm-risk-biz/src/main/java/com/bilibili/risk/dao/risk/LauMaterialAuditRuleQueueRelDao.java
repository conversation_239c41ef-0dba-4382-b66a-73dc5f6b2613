package com.bilibili.risk.dao.risk;

import com.bilibili.risk.po.risk.LauMaterialAuditRuleQueueRelPo;
import com.bilibili.risk.po.risk.LauMaterialAuditRuleQueueRelPoExample;
import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface LauMaterialAuditRuleQueueRelDao {
    long countByExample(LauMaterialAuditRuleQueueRelPoExample example);

    int deleteByExample(LauMaterialAuditRuleQueueRelPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(LauMaterialAuditRuleQueueRelPo record);

    int insertBatch(List<LauMaterialAuditRuleQueueRelPo> records);

    int insertUpdateBatch(List<LauMaterialAuditRuleQueueRelPo> records);

    int insert(LauMaterialAuditRuleQueueRelPo record);

    int insertUpdateSelective(LauMaterialAuditRuleQueueRelPo record);

    int insertSelective(LauMaterialAuditRuleQueueRelPo record);

    List<LauMaterialAuditRuleQueueRelPo> selectByExample(LauMaterialAuditRuleQueueRelPoExample example);

    LauMaterialAuditRuleQueueRelPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") LauMaterialAuditRuleQueueRelPo record, @Param("example") LauMaterialAuditRuleQueueRelPoExample example);

    int updateByExample(@Param("record") LauMaterialAuditRuleQueueRelPo record, @Param("example") LauMaterialAuditRuleQueueRelPoExample example);

    int updateByPrimaryKeySelective(LauMaterialAuditRuleQueueRelPo record);

    int updateByPrimaryKey(LauMaterialAuditRuleQueueRelPo record);
}