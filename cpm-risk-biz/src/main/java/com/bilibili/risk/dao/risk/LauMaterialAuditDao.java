package com.bilibili.risk.dao.risk;

import java.util.List;

import com.bilibili.risk.po.risk.LauMaterialAuditPo;
import com.bilibili.risk.po.risk.LauMaterialAuditPoExample;
import org.apache.ibatis.annotations.Param;

public interface LauMaterialAuditDao {
    long countByExample(LauMaterialAuditPoExample example);

    int deleteByExample(LauMaterialAuditPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(LauMaterialAuditPo record);

    int insertBatch(List<LauMaterialAuditPo> records);

    int insertUpdateBatch(List<LauMaterialAuditPo> records);

    int insert(LauMaterialAuditPo record);

    int insertUpdateSelective(LauMaterialAuditPo record);

    int insertSelective(LauMaterialAuditPo record);

    List<LauMaterialAuditPo> selectByExampleWithBLOBs(LauMaterialAuditPoExample example);

    List<LauMaterialAuditPo> selectByExample(LauMaterialAuditPoExample example);

    LauMaterialAuditPo selectByPrimaryKey(String materialId);

    int updateByExampleSelective(@Param("record") LauMaterialAuditPo record, @Param("example") LauMaterialAuditPoExample example);

    int updateByExampleWithBLOBs(@Param("record") LauMaterialAuditPo record, @Param("example") LauMaterialAuditPoExample example);

    int updateByExample(@Param("record") LauMaterialAuditPo record, @Param("example") LauMaterialAuditPoExample example);

    int updateByPrimaryKeySelective(LauMaterialAuditPo record);

    int updateByPrimaryKey(LauMaterialAuditPo record);
}