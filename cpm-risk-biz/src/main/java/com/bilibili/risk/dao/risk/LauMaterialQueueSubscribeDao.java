package com.bilibili.risk.dao.risk;

import com.bilibili.risk.po.risk.LauMaterialQueueSubscribePo;
import com.bilibili.risk.po.risk.LauMaterialQueueSubscribePoExample;
import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface LauMaterialQueueSubscribeDao {
    long countByExample(LauMaterialQueueSubscribePoExample example);

    int deleteByExample(LauMaterialQueueSubscribePoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(LauMaterialQueueSubscribePo record);

    int insertBatch(List<LauMaterialQueueSubscribePo> records);

    int insertUpdateBatch(List<LauMaterialQueueSubscribePo> records);

    int insert(LauMaterialQueueSubscribePo record);

    int insertUpdateSelective(LauMaterialQueueSubscribePo record);

    int insertSelective(LauMaterialQueueSubscribePo record);

    List<LauMaterialQueueSubscribePo> selectByExample(LauMaterialQueueSubscribePoExample example);

    LauMaterialQueueSubscribePo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") LauMaterialQueueSubscribePo record, @Param("example") LauMaterialQueueSubscribePoExample example);

    int updateByExample(@Param("record") LauMaterialQueueSubscribePo record, @Param("example") LauMaterialQueueSubscribePoExample example);

    int updateByPrimaryKeySelective(LauMaterialQueueSubscribePo record);

    int updateByPrimaryKey(LauMaterialQueueSubscribePo record);
}