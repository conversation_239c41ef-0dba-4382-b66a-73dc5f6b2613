package com.bilibili.risk.aspect;

import com.dianping.cat.Cat;
import com.dianping.cat.CatConstants;
import com.dianping.cat.message.Message;
import com.dianping.cat.message.Transaction;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.StopWatch;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

@Aspect
@Component
@Slf4j
public class DatabusCatMonitoringAspect {

    // 修改切点定义，只匹配onMessages和onMessage方法
    @Pointcut("execution(* com.bilibili.risk.databus..*.onMessage*(..)) || execution(* com.bilibili.risk.databus..*.onMessages*(..))")
    public void databusMessageMethods() {
    }

    @Around("databusMessageMethods()")
    public Object monitorService(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        String className = signature.getDeclaringType().getSimpleName();
        String methodName = signature.getName();
        String transactionName = className + "." + methodName;
        StopWatch sw = StopWatch.createStarted();
        try {
            Object result = joinPoint.proceed();
            return result;
        } catch (Exception e) {
            throw e;
        } finally {
            log.info("Databus method {} executed in {} ms", transactionName, sw.getTime());
        }
    }
}
