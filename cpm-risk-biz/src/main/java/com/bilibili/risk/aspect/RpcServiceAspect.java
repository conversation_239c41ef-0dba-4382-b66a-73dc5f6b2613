package com.bilibili.risk.aspect;

import com.bilibili.databus.exception.DataBusException;
import com.bilibili.risk.annotation.RpcService;
import com.bilibili.risk.enums.MetricsCodeEnum;
import com.bilibili.risk.exception.BusinessException;
import com.bilibili.risk.exception.RiskServiceErr;
import com.bilibili.risk.service.metrics.MetricDataHolder;
import io.grpc.Context;
import io.grpc.Status;
import io.grpc.StatusRuntimeException;
import io.grpc.stub.StreamObserver;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Component;
import redis.shaded.jedis.exceptions.JedisDataException;

import javax.annotation.Nullable;
import java.util.concurrent.atomic.AtomicReference;

/**
 * <AUTHOR>
 * @Description RPC 服务切面，用于简化 gRPC 调用、捕获异常并准备指标数据。
 *              指标上报的具体逻辑已移至 MetricReportServerInterceptor。
 * @date 2025/4/23
 */
@Slf4j
@Aspect
@Component
@Order(1)
@RequiredArgsConstructor
public class RpcServiceAspect {

    /** Operator 类的完全限定名，用于 MAPI 请求判断 */
    private static final String OPERATOR_CLASS_NAME = "com.bapis.ad.Risk.core.Operator";

    // 用于在 Context 中传递信息的 Key
    public static final Context.Key<String> REQUEST_SOURCE_CTX_KEY = Context.key("requestSource");
    public static final Context.Key<String> ACCOUNT_ID_CTX_KEY = Context.key("accountId");
    public static final Context.Key<AtomicReference<MetricDataHolder>> METRIC_DATA_HOLDER_CTX_KEY =
            Context.key("metricDataHolder");

    /**
     * 环绕通知，应用于所有标记了 @RpcService 注解的类的方法。
     * @param joinPoint 切入点，代表被拦截的方法
     * @return 被拦截方法的返回值 (如果正常执行)
     */
    @Around("@within(com.bilibili.risk.annotation.RpcService)")
    public Object rpcServiceAspect(ProceedingJoinPoint joinPoint) {
        Object[] args = joinPoint.getArgs();
        StreamObserver<?> streamObserver = (StreamObserver<?>) args[1];

        // 从 gRPC Context 中获取传递的信息
        AtomicReference<MetricDataHolder> metricDataHolderRef = METRIC_DATA_HOLDER_CTX_KEY.get(Context.current());
        String accountId = ACCOUNT_ID_CTX_KEY.get(Context.current());
        String requestSource = REQUEST_SOURCE_CTX_KEY.get(Context.current());

        // 防御性检查：如果 Holder 未在 Context 中找到 (理论上不应发生)
        if (metricDataHolderRef == null) {
             log.info("AspectService: 在 Context 中未找到 MetricDataHolder，拦截器可能未运行或失败，将在无详细指标的情况下继续执行。method:{}", getMethodName(joinPoint));
             try {
                 return joinPoint.proceed();
             } catch (Throwable t) {
                 // 在没有指标上下文的情况下执行失败
                 log.error("AspectService: 在没有指标上下文的情况下执行失败。method:{}", getMethodName(joinPoint), t);
                 streamObserver.onError(Status.UNKNOWN
                         .withDescription(t.getMessage())
                         .withCause(t)
                         .asRuntimeException());
                 return null;
             }
        }

        // 获取可变的 MetricDataHolder 实例
        MetricDataHolder metricData = metricDataHolderRef.get();
        String methodName = getMethodName(joinPoint);
        metricData.methodName = methodName;
        MetricsCodeEnum.DomainType domainType = getDomainTypeFromAnnotation(joinPoint);
        metricData.domainType = domainType.name();
        boolean isMapi = false;
        metricData.isMapi = isMapi;

        try {
            Object obj = joinPoint.proceed();// gRPC 成功执行时，MetricDataHolder 中使用默认的成功状态 (SUCCESS)
            streamObserver.onCompleted();
            return obj;
        } catch (BusinessException businessException) {
            metricData.domainType = businessException.getDomainType().name();
            metricData.type = businessException.getErrorType().name();
            metricData.code = businessException.getErrCode();
            metricData.msg = businessException.getSubCode().getDesc();
            log.error("businessException, method:{}, accountId:{}, requestSource:{}, errorCode:{}, errorMsg:{}, isMapi:{}", methodName, accountId, requestSource, metricData.getCode(),metricData.getMsg(), isMapi, businessException);
            streamObserver.onError(Status.INVALID_ARGUMENT
                    .withDescription(businessException.getErrMsg()) // 使用原始异常信息
                    .asRuntimeException());
            return null;
        } catch (IllegalArgumentException illegalArgumentException) {
            BusinessException businessException = convertToBizException(joinPoint, illegalArgumentException);
            metricData.domainType = businessException.getDomainType().name();
            metricData.type = businessException.getErrorType().name();
            metricData.code = businessException.getErrCode();
            metricData.msg = businessException.getSubCode().getDesc();
            log.error("illegalArgumentException, method:{}, accountId:{}, requestSource:{}, errorCode:{}, errorMsg:{}, isMapi:{}", methodName, accountId, requestSource, metricData.getCode(), metricData.getMsg(), isMapi, businessException);
            streamObserver.onError(Status.INVALID_ARGUMENT
                    .withDescription(illegalArgumentException.getMessage()) // 使用原始异常信息
                    .asRuntimeException());
            return null;
        } catch (StatusRuntimeException err) {
            // 捕获下游 gRPC 调用返回的异常，默认为系统错误
            metricData.type = MetricsCodeEnum.Type.SYS_ERR.name();
            switch(err.getStatus().getCode()) {
                case DEADLINE_EXCEEDED:
                    metricData.code = MetricsCodeEnum.SubCode.TIMEOUT.getCode();
                    metricData.msg = MetricsCodeEnum.SubCode.TIMEOUT.getDesc();
                    break;
                case CANCELLED:
                    metricData.code = MetricsCodeEnum.SubCode.CLIENT_CANCELLED.getCode();
                    metricData.msg = MetricsCodeEnum.SubCode.CLIENT_CANCELLED.getDesc();
                    metricData.type = MetricsCodeEnum.Type.BIZ_ERR.name(); // 业务错误
                    break;
                case INVALID_ARGUMENT: // 参数无效
                    metricData.code = MetricsCodeEnum.SubCode.PARAM_INVALID.getCode();
                    metricData.msg = MetricsCodeEnum.SubCode.PARAM_INVALID.getDesc();
                    metricData.type = MetricsCodeEnum.Type.BIZ_ERR.name(); // 业务错误
                    break;
                case UNAUTHENTICATED: // 未认证
                case PERMISSION_DENIED: // 无权限
                     metricData.code = MetricsCodeEnum.SubCode.DOWNSTREAM_CALL_ERROR.getCode();
                     metricData.msg = MetricsCodeEnum.SubCode.DOWNSTREAM_CALL_ERROR.getDesc();
                     break;
                case NOT_FOUND: // 未找到资源
                     metricData.code = MetricsCodeEnum.SubCode.DOWNSTREAM_CALL_ERROR.getCode();
                     metricData.msg = MetricsCodeEnum.SubCode.DOWNSTREAM_CALL_ERROR.getDesc();
                     break;
                case UNAVAILABLE: // 下游服务不可用
                     metricData.code = MetricsCodeEnum.SubCode.DOWNSTREAM_CALL_ERROR.getCode();
                     metricData.msg = MetricsCodeEnum.SubCode.DOWNSTREAM_CALL_ERROR.getDesc();
                     break;
                default: // 其他下游 gRPC 错误
                    metricData.code = MetricsCodeEnum.SubCode.DOWNSTREAM_CALL_ERROR.getCode();
                    metricData.msg = MetricsCodeEnum.SubCode.DOWNSTREAM_CALL_ERROR.getDesc();
                    break;
            }
            log.error("statusRuntimeException, method:{}, accountId:{}, requestSource:{}, errorCode:{}, errorMsg:{}, isMapi:{}, status:{}", methodName, accountId, requestSource, metricData.getCode(), metricData.getMsg(), isMapi, err.getStatus(), err);
            streamObserver.onError(err);  // 将原始的 StatusRuntimeException 传递给客户端
            return null;
        } catch (NullPointerException err) {
            metricData.type = MetricsCodeEnum.Type.SYS_ERR.name();
            metricData.code = MetricsCodeEnum.SubCode.NULL_POINTER_ERR.getCode();
            metricData.msg = MetricsCodeEnum.SubCode.NULL_POINTER_ERR.getDesc();
            log.error("NullPointerException, method:{}, accountId:{}, requestSource:{}, errorCode:{}, errorMsg:{}, isMapi:{}", methodName, accountId, requestSource, metricData.getCode(), metricData.getMsg(), isMapi, err);
            streamObserver.onError(Status.INTERNAL
                    .withDescription(err.getMessage())
                    .withCause(err)
                    .asRuntimeException());
            return null;
        } catch (DataAccessException err) {
            metricData.type = MetricsCodeEnum.Type.SYS_ERR.name();
            metricData.code = MetricsCodeEnum.SubCode.DB_ERROR.getCode();
            metricData.msg = MetricsCodeEnum.SubCode.DB_ERROR.getDesc();
            log.error("DataAccessException, method:{}, accountId:{}, requestSource:{}, errorCode:{}, errorMsg:{}, isMapi:{}", methodName, accountId, requestSource, metricData.getCode(), metricData.getMsg(), isMapi, err);
            streamObserver.onError(Status.INTERNAL
                    .withDescription(err.getMessage())
                    .withCause(err)
                    .asRuntimeException());
            return null;
        } catch (DataBusException err) {
            metricData.type = MetricsCodeEnum.Type.SYS_ERR.name();
            metricData.code = MetricsCodeEnum.SubCode.DATABUS_ERROR.getCode();
            metricData.msg = MetricsCodeEnum.SubCode.DATABUS_ERROR.getDesc();
            log.error("DataBusException, method:{}, accountId:{}, requestSource:{}, errorCode:{}, errorMsg:{}, isMapi:{}", methodName, accountId, requestSource, metricData.getCode(), metricData.getMsg(), isMapi, err);
            streamObserver.onError(Status.INTERNAL
                    .withDescription(err.getMessage())
                    .withCause(err)
                    .asRuntimeException());
            return null;
        } catch (RiskServiceErr err) {
            metricData.type = MetricsCodeEnum.Type.BIZ_ERR.name();
            metricData.code = MetricsCodeEnum.SubCode.PARAM_INVALID.getCode();
            metricData.msg = MetricsCodeEnum.SubCode.PARAM_INVALID.getDesc();
            log.error("RiskServiceErr, method:{}, accountId:{}, requestSource:{}, errorCode:{}, errorMsg:{}, isMapi:{}", methodName, accountId, requestSource, metricData.getCode(), metricData.getMsg(), isMapi, err);
            streamObserver.onError(Status.INVALID_ARGUMENT
                    .withDescription(err.getMsg())
                    .asRuntimeException());
            return null;
        } catch (JedisDataException err) {
            metricData.type = MetricsCodeEnum.Type.SYS_ERR.name();
            metricData.code = MetricsCodeEnum.SubCode.REDIS_ERROR.getCode();
            metricData.msg = MetricsCodeEnum.SubCode.REDIS_ERROR.getDesc();
            log.error("JedisDataException, method:{}, accountId:{}, requestSource:{}, errorCode:{}, errorMsg:{}, isMapi:{}", methodName, accountId, requestSource, metricData.getCode(), metricData.getMsg(), isMapi, err);
            streamObserver.onError(Status.INTERNAL
                    .withDescription(err.getMessage())
                    .withCause(err)
                    .asRuntimeException());
            return null;
        } catch (Throwable t) {
            // 捕获所有其他未发现到的异常
            metricData.type = MetricsCodeEnum.Type.SYS_ERR.name();
            metricData.code = MetricsCodeEnum.SubCode.SYSTEM_ERROR.getCode();
            metricData.msg = MetricsCodeEnum.SubCode.SYSTEM_ERROR.getDesc();
            log.error("throwable, method:{}, accountId:{}, requestSource:{}, errorCode:{}, errorMsg:{}, isMapi:{}", methodName, accountId, requestSource, metricData.getCode(), metricData.getMsg(), isMapi, t);
            streamObserver.onError(Status.UNKNOWN
                    .withDescription(t.getMessage())
                    .withCause(t)
                    .asRuntimeException());
            return null;
        }
    }

    /**
     * 将 IllegalArgumentException 转换为 BusinessException。
     * 从注解中获取业务域类型。
     *
     * @param joinPoint 切入点
     * @param exception IllegalArgumentException 异常
     * @return 转换后的 BusinessException
     */
    private BusinessException convertToBizException(ProceedingJoinPoint joinPoint, IllegalArgumentException exception) {
        MetricsCodeEnum.DomainType domainType = getDomainTypeFromAnnotation(joinPoint);
        // 使用 PARAM_INVALID 作为错误子码，使用 IllegalArgumentException 的错误信息作为错误描述
        String errorMsg = exception.getMessage();
        return new BusinessException(domainType, MetricsCodeEnum.Type.BIZ_ERR, MetricsCodeEnum.SubCode.PARAM_INVALID, errorMsg);
    }

    /**
     * 从目标类上的 @RpcService 注解中获取业务域类型。
     *
     * @param joinPoint 切入点
     * @return 业务域类型，如果注解不存在或未指定，则返回 OTHER
     */
    private MetricsCodeEnum.DomainType getDomainTypeFromAnnotation(ProceedingJoinPoint joinPoint) {
        Class<?> targetClass = joinPoint.getTarget().getClass();
        RpcService annotation = targetClass.getAnnotation(RpcService.class);
        if (annotation != null) {
            return annotation.domainType();
        }
        // 如果注解不存在或未指定 domainType，返回默认值
        return MetricsCodeEnum.DomainType.OTHER;
    }

    /**
     * 获取被拦截的方法名。
     *
     * @param joinPoint 切入点
     * @return 方法名
     */
    private String getMethodName(ProceedingJoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        // 仅使用方法名
        return signature.getName();
        // 如果需要更详细的名称（类名.方法名），可以使用下面的代码:
        // return joinPoint.getTarget().getClass().getSimpleName() + "." + signature.getName();
    }

    /**
     * 判断 gRPC 请求是否为 MAPI 请求。
     * MAPI 请求的判断依据是请求参数中包含 Operator 对象，且其 flag 字段值为 1。
     *
     * @param joinPoint 切入点
     * @return 如果是 MAPI 请求则返回 true，否则返回 false
     */
    private boolean isMapiRequest(ProceedingJoinPoint joinPoint) {
        try {
            Object[] args = joinPoint.getArgs();
            // gRPC 方法至少需要请求对象和 StreamObserver 两个参数
            if (args == null || args.length < 2) {
                return false;
            }
            Object requestParam = args[0];
            if (requestParam == null) {
                return false;
            }
            // 获取Operator对象
            Object operator = getOperatorFromRequest(requestParam);
            if (operator == null) {
                return false;
            }
            // 检查flag字段值
            Integer flag = getOperatorFlag(operator);
            return flag != null && flag == 1;
        } catch (Exception e) {
            log.info("isMapiRequest, 判断MAPI请求时发生异常");
            return false;
        }
    }

    /**
     * 通过反射从请求参数对象中获取 Operator 对象。
     * 假设 Operator 对象通过名为 getOperator() 的方法获取。
     *
     * @param requestParam 请求参数对象
     * @return Operator 对象，如果不存在或获取失败则返回 null
     */
    @Nullable
    private Object getOperatorFromRequest(Object requestParam) {
        try {
            // 尝试获取 getOperator 方法
            java.lang.reflect.Method getOperatorMethod = requestParam.getClass().getMethod("getOperator");
            Object operator = getOperatorMethod.invoke(requestParam);
            if (operator != null && operator.getClass().getName().equals(OPERATOR_CLASS_NAME)) {
                 return operator;
            }
        } catch (Exception e) {
            log.info("isMapiRequest, 获取Operator对象失败");
        }
        return null;
    }

    /**
     * 通过反射获取 Operator 对象中的 flag 字段值。
     * 假设 flag 值通过名为 getFlag() 的方法获取，且返回类型为 Integer。
     *
     * @param operator Operator 对象
     * @return flag 字段值 (Integer)，如果获取失败或类型不匹配则返回 null
     */
    @Nullable
    private Integer getOperatorFlag(Object operator) {
        try {
            // 尝试获取 getFlag 方法
            java.lang.reflect.Method getFlagMethod = operator.getClass().getMethod("getFlag");
            Object flagObj = getFlagMethod.invoke(operator);
            if (flagObj instanceof Integer) {
                return (Integer) flagObj;
            } else if (flagObj != null) {
                log.info("isMapiRequest, 获取Operator对象的flag字段失败, 返回了预期之外的类型");
            }
        } catch (Exception e) {
            log.info("isMapiRequest, 获取Operator对象的flag字段失败");
        }
        return null;
    }
}