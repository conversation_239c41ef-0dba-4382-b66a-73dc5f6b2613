package com.bilibili.risk.aspect;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Message;
import com.dianping.cat.message.Transaction;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

@Aspect
@Component
public class ControllerCatAspect {

    @Pointcut("@within(org.springframework.stereotype.Controller) || " +
            "@within(org.springframework.web.bind.annotation.RestController)")
    public void controllerLayer() {}

    @Around("controllerLayer()")
    public Object monitorController(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        String className = signature.getDeclaringType().getSimpleName();
        String methodName = signature.getName();
        String transactionName = "Controller." + className + "." + methodName;

        Transaction t = Cat.newTransaction("Controller", transactionName);

        try {
            // 记录方法参数
            logMethodParameters(joinPoint);

            long start = System.currentTimeMillis();
            Object result = joinPoint.proceed();
            long cost = System.currentTimeMillis() - start;

            // 记录耗时
            Cat.logMetricForCount("method.cost", (int) cost);
            Cat.logMetricForDuration("method.duration", cost);

            t.setStatus(Message.SUCCESS);
            return result;
        } catch (Exception e) {
            t.setStatus(e);
            Cat.logError("Controller Error", e);
            throw e;
        } finally {
            t.complete();
        }
    }

    private void logMethodParameters(ProceedingJoinPoint joinPoint) {
        Object[] args = joinPoint.getArgs();
        if (args != null && args.length > 0) {
            for (int i = 0; i < args.length; i++) {
                Cat.logEvent("Method.Parameter", "Arg-" + i, Message.SUCCESS,
                        args[i] != null ? args[i].toString() : "null");
            }
        }
    }
}