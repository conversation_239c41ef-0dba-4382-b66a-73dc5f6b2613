package com.bilibili.risk.convertor;

import com.bilibili.risk.bo.LauMaterialAuditQueueBo;
import com.bilibili.risk.po.risk.LauMaterialAuditQueuePo;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED)
public interface IMaterialAuditQueueConvertor {
    IMaterialAuditQueueConvertor INSTANCE = Mappers.getMapper(IMaterialAuditQueueConvertor.class);

    LauMaterialAuditQueuePo bo2po(LauMaterialAuditQueueBo po);

    LauMaterialAuditQueueBo po2bo(LauMaterialAuditQueuePo po);

    List<LauMaterialAuditQueueBo> pos2bos(List<LauMaterialAuditQueuePo> pos);
}
