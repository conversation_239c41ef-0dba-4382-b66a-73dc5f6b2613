package com.bilibili.risk.convertor;

import com.bilibili.risk.bo.MaterialAuditTaskOperationLogBo;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED)
public interface IOperationLogConvertor {
    IOperationLogConvertor INSTANCE = Mappers.getMapper(IOperationLogConvertor.class);
}
