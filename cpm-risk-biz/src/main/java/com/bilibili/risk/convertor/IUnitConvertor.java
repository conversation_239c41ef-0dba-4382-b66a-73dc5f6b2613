package com.bilibili.risk.convertor;

import com.bapis.ad.crm.account.AccountIndustryItem;
import com.bilibili.risk.bo.AccountIndustryItemBo;
import com.bilibili.risk.bo.LauUnitExtraBo;
import com.bilibili.risk.po.ad.LauUnitExtraPo;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED)
public interface IUnitConvertor {
    IUnitConvertor INSTANCE = Mappers.getMapper(IUnitConvertor.class);

    LauUnitExtraBo po2bo(LauUnitExtraPo x);
    List<LauUnitExtraBo> pos2bos(List<LauUnitExtraPo> x);

}
