package com.bilibili.risk.convertor;

import com.bapis.archive.service.Arc;
import com.bilibili.risk.bo.BiliArchiveBo;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.factory.Mappers;

import java.util.function.BiFunction;

@Mapper(collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface BiliArchiveMapper {
    BiliArchiveMapper MAPPER = Mappers.getMapper(BiliArchiveMapper.class);

    default Boolean isVerticalScreen(Arc arc) {
        if (arc != null && arc.getDimension() != null) {
            long h = arc.getDimension().getRotate() == 0L ? arc.getDimension().getHeight() : arc.getDimension().getWidth();
            long w = arc.getDimension().getRotate() == 0L ? arc.getDimension().getWidth() : arc.getDimension().getHeight();
            return h > w;
        }
        return false;
    }

    BiFunction<Integer, Integer, Boolean> ATTRIBUTE_BI_FUNCTION = (source, index) -> (source >> index & 1) == 1;

    @Mapping(target = "tid", source = "typeID")
    @Mapping(target = "nickName", source = "author.name")
    @Mapping(target = "mid", source = "author.mid")
    @Mapping(target = "width", source = "dimension.width")
    @Mapping(target = "height", source = "dimension.height")
    @Mapping(target = "coverUrl", source = "pic")
    @Mapping(target = "cid", source = "firstCid")
    @Mapping(target = "avid", source = "aid")
    @Mapping(target = "title", source = "title")
    @Mapping(target = "tagIds",source = "tag")
    @Mapping(target = "autoplay",source = "rights.autoplay")
    @Mapping(target = "isVerticalScreen", expression = "java(isVerticalScreen(x))")
    @Mapping(target = "play", expression = "java(x.getStat().getView())")
    @Mapping(target = "isPgc", expression = "java(ATTRIBUTE_BI_FUNCTION.apply(x.getAttribute(), com.bilibili.risk.enums.ArchiveAttributeEnum.IS_PGC.getIndex()))")
    @Mapping(target = "isPugv", expression = "java(ATTRIBUTE_BI_FUNCTION.apply(x.getAttribute(), com.bilibili.risk.enums.ArchiveAttributeEnum.IS_PUGV.getIndex()))")
    @Mapping(target = "stateDesc", expression = "java(com.bilibili.risk.enums.ArchiveState.getByCode(x.getState()).getDesc())")
    @Mapping(target = "tidV2", source = "typeIDV2")
    BiliArchiveBo fromRo(Arc x);


}
