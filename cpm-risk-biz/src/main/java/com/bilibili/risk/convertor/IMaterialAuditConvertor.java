package com.bilibili.risk.convertor;

import com.bilibili.risk.bo.CreativeDetailMaterialBo;
import com.bilibili.risk.bo.LauMaterialAuditBo;
import com.bilibili.risk.bo.msg.*;
import com.bilibili.risk.po.risk.LauMaterialAuditPo;
import com.bilibili.risk.po.risk.LauMaterialAuditTaskPo;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED)
public interface IMaterialAuditConvertor {
    IMaterialAuditConvertor INSTANCE = Mappers.getMapper(IMaterialAuditConvertor.class);

    LauMaterialAuditBo po2bo(LauMaterialAuditPo x);

    List<LauMaterialAuditBo> pos2bos(List<LauMaterialAuditPo> x);

    // 注意: 该方法下面这些字段如果不传，会直接生成默认值，从而导致表里数据被覆盖
    @Deprecated
    @Named("bo2po")
    @Mapping(target = "rawContent", expression = "java(java.util.Optional.ofNullable(x.getRawContent()).orElse(\"\"))")
    @Mapping(target = "materialContent", expression = "java(java.util.Optional.ofNullable(x.getMaterialContent()).orElse(\"\"))")
    @Mapping(target = "extra", expression = "java(java.util.Optional.ofNullable(x.getExtra()).orElse(\"\"))")
    @Mapping(target = "auditLabelThirdId", expression = "java(java.util.Optional.ofNullable(x.getAuditLabelThirdId()).orElse(\"\"))")
    @Mapping(target = "reason", expression = "java(java.util.Optional.ofNullable(x.getReason()).orElse(\"\"))")
    LauMaterialAuditPo bo2po(LauMaterialAuditBo x);

    @IterableMapping(qualifiedByName = "bo2po") // 明确指定使用哪个方法
    List<LauMaterialAuditPo> bos2pos(List<LauMaterialAuditBo> x);

    @Named("bo2po2")
    LauMaterialAuditPo bo2po2(LauMaterialAuditBo x);
    // 不糊更新默认的那些字段
    @IterableMapping(qualifiedByName = "bo2po2") // 明确指定使用哪个方法
    List<LauMaterialAuditPo> bos2pos2(List<LauMaterialAuditBo> x);

    @IterableMapping(qualifiedByName = "creativeDetailInfoOfMaterialBo2MaterialAuditBo")
    List<LauMaterialAuditBo> creativeDetailInfoOfMaterialBo2Bos(List<CreativeDetailMaterialBo> pos);

    @Mapping(target = "rawContent", source = "rawContent")
    @Named("creativeDetailInfoOfMaterialBo2MaterialAuditBo")
    @Mapping(target = "isDeleted", expression = "java(com.bilibili.adp.common.enums.IsDeleted.DELETED.getCode())")
    LauMaterialAuditBo creativeDetailInfoOfMaterialBo2MaterialAuditBo(CreativeDetailMaterialBo x);
    List<LauMaterialAuditBo> creativeDetailInfoOfMaterialBo2MaterialAuditBos(List<CreativeDetailMaterialBo> x);

    @Mapping(target = "rawContent", source = "title")
    @Mapping(target = "materialMd5", source = "md5")
    @Mapping(target = "materialType", expression = "java(com.bilibili.risk.enums.RiskMaterialTypeEnum.TEXT.getCode())")
    @Mapping(target = "materialSourceSet", expression = "java(org.elasticsearch.common.util.set.Sets.newHashSet(com.bilibili.risk.enums.MaterialSourceEnum.CREATIVE.getCode()))")
    CreativeDetailMaterialBo toTitleMaterialBo(TitleMsgBo x);
    List<CreativeDetailMaterialBo> toTitleMaterialBos(List<TitleMsgBo> x);

    @Mapping(target = "rawContent", source = "description")
    @Mapping(target = "materialMd5", source = "md5")
    @Mapping(target = "materialType", expression = "java(com.bilibili.risk.enums.RiskMaterialTypeEnum.TEXT.getCode())")
    @Mapping(target = "materialSourceSet", expression = "java(org.elasticsearch.common.util.set.Sets.newHashSet(com.bilibili.risk.enums.MaterialSourceEnum.CREATIVE.getCode()))")
    CreativeDetailMaterialBo toDescMaterialBo(DescriptionMsgBo x);
    List<CreativeDetailMaterialBo> toDescMaterialBos(List<DescriptionMsgBo> x);

    @Mapping(target = "rawContent", source = "image_url")
    @Mapping(target = "materialMd5", source = "image_md5")
    @Mapping(target = "materialType", expression = "java(com.bilibili.risk.enums.RiskMaterialTypeEnum.IMAGE.getCode())")
    @Mapping(target = "materialSourceSet", expression = "java(org.elasticsearch.common.util.set.Sets.newHashSet(com.bilibili.risk.enums.MaterialSourceEnum.CREATIVE.getCode()))")
    CreativeDetailMaterialBo toImageMaterialBo(ImageMsgBo x);
    List<CreativeDetailMaterialBo> toImageMaterialBos(List<ImageMsgBo> x);

    @Mapping(target = "rawContent", source = "mgk_video_id")
    @Mapping(target = "materialType", expression = "java(com.bilibili.risk.enums.RiskMaterialTypeEnum.VIDEO.getCode())")
    @Mapping(target = "materialSourceSet", expression = "java(org.elasticsearch.common.util.set.Sets.newHashSet(com.bilibili.risk.enums.MaterialSourceEnum.CREATIVE.getCode()))")
    CreativeDetailMaterialBo toVideoMaterialBo(MgkVideoMsgBo x);
    List<CreativeDetailMaterialBo> toVideoMaterialBos(List<MgkVideoMsgBo> x);

    @Mapping(target = "rawContent", source = "avid")
    @Mapping(target = "materialType", expression = "java(com.bilibili.risk.enums.RiskMaterialTypeEnum.VIDEO.getCode())")
    @Mapping(target = "materialSourceSet", expression = "java(org.elasticsearch.common.util.set.Sets.newHashSet(com.bilibili.risk.enums.MaterialSourceEnum.CREATIVE.getCode()))")
    CreativeDetailMaterialBo toArchiveMaterialBo(ArchiveMsgBo x);
    List<CreativeDetailMaterialBo> toArchiveMaterialBos(List<ArchiveMsgBo> x);

    @Mapping(target = "rawContent", source = "dynamic_id")
    @Mapping(target = "materialType", expression = "java(com.bilibili.risk.enums.RiskMaterialTypeEnum.DYNAMIC.getCode())")
    @Mapping(target = "materialSourceSet", expression = "java(org.elasticsearch.common.util.set.Sets.newHashSet(com.bilibili.risk.enums.MaterialSourceEnum.CREATIVE.getCode()))")
    CreativeDetailMaterialBo toDynamicMaterialBo(DynamicMsgBo x);
    List<CreativeDetailMaterialBo> toDynamicMaterialBos(List<DynamicMsgBo> x);

    @Mapping(target = "rawContent", source = "material_id")
    @Mapping(target = "materialType", expression = "java(com.bilibili.risk.enums.RiskMaterialTypeEnum.LIVE.getCode())")
    @Mapping(target = "materialSourceSet", expression = "java(org.elasticsearch.common.util.set.Sets.newHashSet(com.bilibili.risk.enums.MaterialSourceEnum.CREATIVE.getCode()))")
    CreativeDetailMaterialBo toLiveRoomMaterialBo(LiveRoomMsgBo x);
    List<CreativeDetailMaterialBo> toLiveRoomMaterialBos(List<LiveRoomMsgBo> x);

    @Mapping(target = "rawContent", source = "url")
    @Mapping(target = "materialType", expression = "java(com.bilibili.risk.enums.RiskMaterialTypeEnum.LINK.getCode())")
    @Mapping(target = "materialSourceSet", expression = "java(org.elasticsearch.common.util.set.Sets.newHashSet(com.bilibili.risk.enums.MaterialSourceEnum.CREATIVE.getCode()))")
    CreativeDetailMaterialBo toTabUrlMaterialBo(RawJumpUrlMsgBo x);
    List<CreativeDetailMaterialBo> toTabUrlMaterialBos(List<RawJumpUrlMsgBo> x);

    @Mapping(target = "rawContent", source = "url")
    @Mapping(target = "materialType", expression = "java(com.bilibili.risk.enums.RiskMaterialTypeEnum.LINK.getCode())")
    @Mapping(target = "materialSourceSet", expression = "java(org.elasticsearch.common.util.set.Sets.newHashSet(com.bilibili.risk.enums.MaterialSourceEnum.CREATIVE.getCode()))")
    CreativeDetailMaterialBo toReplaceUrlMaterialBo(LinkReplaceUrlMsgBo x);
    List<CreativeDetailMaterialBo> toReplaceUrlMaterialBos(List<LinkReplaceUrlMsgBo> x);

    @Mapping(target = "rawContent", source = "access_url")
    @Mapping(target = "materialType", expression = "java(com.bilibili.risk.enums.RiskMaterialTypeEnum.LINK.getCode())")
    @Mapping(target = "materialSourceSet", expression = "java(org.elasticsearch.common.util.set.Sets.newHashSet(com.bilibili.risk.enums.MaterialSourceEnum.CREATIVE.getCode()))")
    CreativeDetailMaterialBo gameCardBos2MaterialBo(GameCardMsgBo x);
    List<CreativeDetailMaterialBo> gameCardBos2MaterialBos(List<GameCardMsgBo> x);

    @Mapping(target = "materialSourceSet", expression = "java(com.bilibili.risk.utils.JacksonUtils.toIntSet(x.getMaterialSource()))")
    CreativeDetailMaterialBo toCreativeMaterialBo(LauMaterialAuditTaskPo x) throws Exception;

    List<CreativeDetailMaterialBo> toCreativeMaterialBos(List<LauMaterialAuditTaskPo> x) throws Exception;
}
