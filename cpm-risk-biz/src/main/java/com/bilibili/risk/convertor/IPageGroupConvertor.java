package com.bilibili.risk.convertor;

import com.bapis.ad.mgk.page.group.AuditPageGroupMappingEntity;
import com.bapis.ad.mgk.page.group.PageGroupBaseEntity;
import com.bapis.ad.mgk.page.group.PageGroupEntity;
import com.bapis.ad.mgk.page.group.PageGroupMappingEntity;
import com.bilibili.risk.bo.AuditPageGroupMappingEntityBo;
import com.bilibili.risk.bo.LandingPageGroupBaseBo;
import com.bilibili.risk.bo.LandingPageGroupBo;
import com.bilibili.risk.bo.LandingPageGroupMappingBo;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED)
public interface IPageGroupConvertor {
    IPageGroupConvertor INSTANCE = Mappers.getMapper(IPageGroupConvertor.class);

    @Mapping(target = "ctime", expression = "java(new java.sql.Timestamp(baseEntity.getCtime()))")
    @Mapping(target = "mtime", expression = "java(new java.sql.Timestamp(baseEntity.getMtime()))")
    LandingPageGroupBaseBo grpc2Bo(PageGroupBaseEntity baseEntity);

    @Mapping(target = "ctime", expression = "java(new java.sql.Timestamp(baseEntity.getCtime()))")
    @Mapping(target = "mtime", expression = "java(new java.sql.Timestamp(baseEntity.getMtime()))")
    @Mapping(target = "mappingBos", expression = "java(grpc2Bo(baseEntity.getMappingList()))")
    LandingPageGroupBo grpc2Bo(PageGroupEntity baseEntity);
    List<LandingPageGroupMappingBo> grpc2Bo(List<PageGroupMappingEntity> x);

    AuditPageGroupMappingEntity bo2grpc(AuditPageGroupMappingEntityBo x);
    List<AuditPageGroupMappingEntity> bo2grpcs(List<AuditPageGroupMappingEntityBo> x);
}
