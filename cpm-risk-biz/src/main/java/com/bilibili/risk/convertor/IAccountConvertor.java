package com.bilibili.risk.convertor;

import com.bapis.ad.crm.account.AccountIndustryItem;
import com.bilibili.risk.bo.AccountIndustryItemBo;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED)
public interface IAccountConvertor {
    IAccountConvertor INSTANCE = Mappers.getMapper(IAccountConvertor.class);

    AccountIndustryItemBo grpc2Bo(AccountIndustryItem x);
    List<AccountIndustryItemBo> grpc2Bo(List<AccountIndustryItem> x);

}
