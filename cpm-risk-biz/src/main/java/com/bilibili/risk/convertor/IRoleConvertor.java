package com.bilibili.risk.convertor;

import com.bilibili.rbac.api.dto.RoleBaseDto;
import com.bilibili.rbac.api.dto.RoleDto;
import com.bilibili.rbac.biz.po.RolePo;
import com.bilibili.risk.bo.RoleBo;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED)
public interface IRoleConvertor {
    IRoleConvertor INSTANCE = Mappers.getMapper(IRoleConvertor.class);

    RoleBo dto2Bo(RoleBaseDto po);
    List<RoleBo> pos2Bos(List<RolePo> pos);
    List<RoleBo> dtos2Bos(List<RoleBaseDto> pos);
    List<RoleBo> roleDtos2Bos(List<RoleDto> pos);
}
