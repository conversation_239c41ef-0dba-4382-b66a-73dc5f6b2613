package com.bilibili.risk.convertor;

import com.bapis.ad.audit.MaterialElemAuditInfo;
import com.bapis.ad.audit.MiscElemAuditInfo;
import com.bapis.ad.creative.SingleQueryCreativeListRep;
import com.bapis.ad.creative.SingleQueryProgramCreativeDetailListRep;
import com.bapis.ad.mgk.page.group.PageGroupBaseEntity;
import com.bapis.ad.mng.creative.CreativeQualificationResp;
import com.bapis.ad.mng.creative.SingleQueryCreativeOtherInfosResp;
import com.bapis.ad.risk.material.SingleQueryMaterialCreativesResp;
import com.bilibili.risk.bo.*;
import com.bilibili.risk.po.ad.LauProgrammaticCreativeDetailPo;
import com.bilibili.risk.po.ad.LauUnitCreativePo;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED)
public interface ICreativeConvertor {
    ICreativeConvertor INSTANCE = Mappers.getMapper(ICreativeConvertor.class);

    CreativeBo grpc2Bo(SingleQueryCreativeListRep x);
    List<CreativeBo> grpcs2Bos(List<SingleQueryCreativeListRep> x);

    LauProgrammaticCreativeDetailBo grpc2Bo(SingleQueryProgramCreativeDetailListRep x);
    List<LauProgrammaticCreativeDetailBo> grpc2Bo(List<SingleQueryProgramCreativeDetailListRep> x);
    LauProgrammaticCreativeDetailBo po2Bo(LauProgrammaticCreativeDetailPo x);
    List<LauProgrammaticCreativeDetailBo> pos2Bo(List<LauProgrammaticCreativeDetailPo> x);

    CreativeBo unitCreativePo2Bo(LauUnitCreativePo unitCreativePos);
    List<CreativeBo> unitCreativePos2Bos(List<LauUnitCreativePo> unitCreativePos);

    MiscElemAuditInfo fromBo(MiscElemBo x);

    List<MaterialElemAuditInfo> fromBos(List<MaterialBo> x);
    MaterialElemAuditInfo fromBo(MaterialBo x);

    CreativeOtherBo copy(CreativeOtherBo x);
    CreativeQualificationBo grpc2bo(CreativeQualificationResp x);
    List<CreativeQualificationBo> grpcs2bo(List<CreativeQualificationResp> x);

    CreativeOtherBo grpc2bo(SingleQueryCreativeOtherInfosResp x);
    List<CreativeOtherBo> grpcs2bos(List<SingleQueryCreativeOtherInfosResp> x);

    SingleQueryMaterialCreativesResp bo2grpc(QueryMaterialCreativeBo x);
    List<SingleQueryMaterialCreativesResp> bos2grpcs(List<QueryMaterialCreativeBo> x);
}
