package com.bilibili.risk.convertor;

import com.bilibili.risk.bo.*;
import com.bilibili.risk.po.es.LauMaterialAuditTaskEsPo;
import com.bilibili.risk.po.risk.LauMaterialAuditTaskPo;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED)
public interface IMaterialAuditTaskConvertor {
    
    IMaterialAuditTaskConvertor INSTANCE = Mappers.getMapper(IMaterialAuditTaskConvertor.class);

    LauMaterialAuditTaskEsPo copy(LauMaterialAuditTaskEsPo po);
    LauMaterialAuditTaskPo copy(LauMaterialAuditTaskPo po);
    @Mapping(target = "id", source = "id")
    MaterialAuditTaskEsBo po2bo(LauMaterialAuditTaskEsPo po);

    @Mapping(target = "id", source = "taskId")
    @Mapping(target = "auditLabelThirdIds", expression = "java(com.bilibili.risk.utils.MaterialTaskUtils.string2LongList(x.getAuditLabelThirdId()))")
    LauMaterialAuditTaskEsPo updateBo2EsPo(LauMaterialAuditTaskUpdateBo x);

    MaterialAuditTaskCommonQueryBo copy(MaterialAuditTaskCommonQueryBo bo);
    List<MaterialAuditTaskEsBo> pos2bos(List<LauMaterialAuditTaskEsPo> pos);

    @Mapping(target = "taskId", source = "id")
    LauMaterialAuditTaskUpdateBo esPo2UpdateBo(MaterialAuditTaskEsBo pos);
    List<LauMaterialAuditTaskUpdateBo> esPos2UpdateBos(List<MaterialAuditTaskEsBo> pos);

    MaterialAuditTaskEsBo creativeDetailInfoOfMaterialBo2bo(CreativeDetailMaterialBo x);

    List<MaterialAuditTaskEsBo> creativeDetailInfoOfMaterialBos2Bos(List<CreativeDetailMaterialBo> x);

    @Mapping(target = "materialContent", source = "materialContent")
    @Mapping(target = "materialSource", expression = "java(com.alibaba.fastjson.JSON.toJSONString(x.getMaterialSourceSet()))")
    LauMaterialAuditTaskPo creativeDetailInfoOfMaterialBo2Po(CreativeDetailMaterialBo x);

    List<LauMaterialAuditTaskPo> creativeDetailInfoOfMaterialBos2Pos(List<CreativeDetailMaterialBo> x);

    @Mapping(target = "auditLabelThirdIds", expression = "java(com.bilibili.risk.utils.MaterialTaskUtils.string2LongList(x.getAuditLabelThirdId()))")
    @Mapping(target = "id", source = "taskId")
    LauMaterialAuditTaskEsPo bosEsPo(LauMaterialAuditTaskBo x);


    @Mapping(target = "auditLabelThirdIds", expression = "java(com.bilibili.risk.utils.MaterialTaskUtils.string2LongList(x.getAuditLabelThirdId()))")
    @Mapping(target = "id", source = "taskId")
    LauMaterialAuditTaskEsPo po2EsPo(LauMaterialAuditTaskPo x);

    List<LauMaterialAuditTaskEsPo> pos2EsPos(List<LauMaterialAuditTaskPo> x);

    List<LauMaterialAuditTaskEsPo> bos2EsPos(List<LauMaterialAuditTaskBo> x);

    MaterialAuditTaskPullQueryBo copy(MaterialAuditTaskPullQueryBo x);


    @Mapping(target = "reason", source = "reason")
    LauMaterialAuditTaskPo updateBo2Po(LauMaterialAuditTaskUpdateBo x);
    LauMaterialAuditTaskUpdateBo po2UpdateBo(LauMaterialAuditTaskPo x);
}
