package com.bilibili.risk.convertor;


import com.bapis.dynamic.service.feed.DynSimpleInfo;
import com.bilibili.risk.bo.BiliDynamicBo;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface MiscMapper {
    MiscMapper MAPPER = Mappers.getMapper(MiscMapper.class);

    @Mapping(target = "mid", source = "uid")
    @Mapping(target = "dynamicId", source = "dynId")
    @Mapping(target = "like", source = "stats.likeNum")
    BiliDynamicBo fromRo(DynSimpleInfo x);



}
