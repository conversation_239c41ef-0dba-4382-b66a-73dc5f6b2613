package com.bilibili.risk.convertor;

import com.bilibili.risk.bo.CreativeDetailMaterialBo;
import com.bilibili.risk.bo.LauMaterialAuditBo;
import com.bilibili.risk.bo.LauMaterialCreativeRelBo;
import com.bilibili.risk.po.risk.LauMaterialCreativeRelPo;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED)
public interface IMaterialCreativeRelConvertor {
    IMaterialCreativeRelConvertor INSTANCE = Mappers.getMapper(IMaterialCreativeRelConvertor.class);

    LauMaterialAuditBo po2bo(LauMaterialCreativeRelPo po);

    LauMaterialCreativeRelPo bo2po(LauMaterialCreativeRelBo bo);

    List<LauMaterialCreativeRelBo> pos2bos(List<LauMaterialCreativeRelPo> pos);

    List<LauMaterialCreativeRelPo> bos2pos(List<LauMaterialCreativeRelBo> bos);

    LauMaterialCreativeRelPo creativeDetailInfoOfMaterialBo2Po(CreativeDetailMaterialBo pos);

    List<LauMaterialCreativeRelPo> creativeDetailInfoOfMaterialBos2Pos(List<CreativeDetailMaterialBo> pos);
}
