package com.bilibili.risk.convertor;

import com.bilibili.rbac.api.dto.RoleBaseDto;
import com.bilibili.risk.bo.OperatorBo;
import com.bilibili.risk.bo.RoleBo;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED)
public interface IOperatorConvertor {
    IOperatorConvertor INSTANCE = Mappers.getMapper(IOperatorConvertor.class);
}
