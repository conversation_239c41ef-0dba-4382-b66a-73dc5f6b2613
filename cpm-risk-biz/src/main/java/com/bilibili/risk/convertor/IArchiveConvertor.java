package com.bilibili.risk.convertor;

import com.bapis.ad.archive.CmArchiveFullInfo;
import com.bapis.ad.mng.risk_video.SingleQueryRiskVideoResp;
import com.bilibili.risk.bo.ArchiveRiskVideoBo;
import com.bilibili.risk.bo.CmArchiveBo;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.Collection;
import java.util.List;

@Mapper(collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED)
public interface IArchiveConvertor {

    IArchiveConvertor MAPPER = Mappers.getMapper(IArchiveConvertor.class);

    List<CmArchiveBo> grpc2Bos(Collection<CmArchiveFullInfo> x);
    CmArchiveBo grpc2Bo(CmArchiveFullInfo x);

    ArchiveRiskVideoBo riskGrpc2Bo(SingleQueryRiskVideoResp x);
    List<ArchiveRiskVideoBo> riskGrpcs2Bos(List<SingleQueryRiskVideoResp> x);

}
