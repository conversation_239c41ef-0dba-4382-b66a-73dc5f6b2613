package com.bilibili.risk.convertor;

import com.bilibili.risk.bo.LauMaterialAuditLabelBo;
import com.bilibili.risk.bo.MaterialAuditLabelTreeNode;
import com.bilibili.risk.bo.ThirdLevelAuditLabelBo;
import com.bilibili.risk.po.risk.LauMaterialAuditLabelPo;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED)
public interface IMaterialAuditLabelConvertor {
    IMaterialAuditLabelConvertor INSTANCE = Mappers.getMapper(IMaterialAuditLabelConvertor.class);

    @Mapping(target = "pid", source = "pid")
    MaterialAuditLabelTreeNode po2NodeBo(LauMaterialAuditLabelPo po);
    List<MaterialAuditLabelTreeNode> pos2NodeBos(List<LauMaterialAuditLabelPo> pos);

    LauMaterialAuditLabelBo po2bo(LauMaterialAuditLabelPo po);
    List<LauMaterialAuditLabelBo> pos2bos(List<LauMaterialAuditLabelPo> pos);

    List<ThirdLevelAuditLabelBo> bos2bos(List<LauMaterialAuditLabelBo> auditLabelBos);
}
