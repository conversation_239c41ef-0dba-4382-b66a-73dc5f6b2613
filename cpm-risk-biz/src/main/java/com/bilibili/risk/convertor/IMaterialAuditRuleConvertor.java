package com.bilibili.risk.convertor;

import com.bilibili.risk.bo.LauMaterialAuditQueueBo;
import com.bilibili.risk.bo.LauMaterialAuditRuleBo;
import com.bilibili.risk.bo.MaterialAuditRuleInfoBo;
import com.bilibili.risk.bo.MaterialAuditRuleSaveBo;
import com.bilibili.risk.po.risk.LauMaterialAuditRulePo;
import edu.emory.mathcs.backport.java.util.Collections;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Mapper(nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED)
public interface IMaterialAuditRuleConvertor {
    IMaterialAuditRuleConvertor INSTANCE = Mappers.getMapper(IMaterialAuditRuleConvertor.class);

    LauMaterialAuditRuleBo po2bo(LauMaterialAuditRulePo x);

    @Mapping(target = "rolesIds", ignore = true)
    MaterialAuditRuleInfoBo po2InfoBo(LauMaterialAuditRulePo x);

    LauMaterialAuditRulePo bo2po(LauMaterialAuditRuleBo x);

    @Mapping(defaultValue = "", target = "remark")
    @Mapping(defaultValue = "{}", target = "extra")
    @Mapping(defaultValue = "0", target = "isDeleted")
    @Mapping(target = "rolesIds", ignore = true)
    LauMaterialAuditRulePo saveBo2po(MaterialAuditRuleSaveBo x);

    @Mapping(target = "rolesIds", ignore = true)
    MaterialAuditRuleSaveBo po2SaveBo(LauMaterialAuditRulePo x);
}
