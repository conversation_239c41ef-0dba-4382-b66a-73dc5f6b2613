package com.bilibili.risk.databus.push_to_audit;

import com.alibaba.fastjson.JSON;
import com.bilibili.risk.bo.msg.MaterialInfoPushToAuditMsgBo;
import com.bilibili.risk.bo.msg.TaskMaterialSyncMsgBo;
import com.bilibili.risk.databus.DatabusKeyConstants;
import com.bilibili.risk.service.material.CreativeMaterialSyncService;
import com.bilibili.risk.utils.CatUtils;
import com.bilibili.risk.utils.TraceUtil;
import com.bilibili.warp.databus.AckableMessage;
import com.bilibili.warp.databus.AckableMessages;
import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.databus.MessageListener;
import com.bilibili.warp.spring.boot.autoconfigure.databus.DatabusProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class TaskMaterialSyncSub implements MessageListener {

    @Autowired
    private TraceUtil traceUtil;

    @Autowired
    private CreativeMaterialSyncService creativeMaterialSyncService;

    private final String topic;
    private final String group;

    public TaskMaterialSyncSub(DatabusProperties databusProperties) {
        DatabusProperty databusProperty = databusProperties.getProperties().get(DatabusKeyConstants.TASK_MATERIAL_SYNC);
        this.topic = databusProperty.getTopic();
        this.group = databusProperty.getSub().getGroup();
        log.info("TaskMaterialSyncSub init, topic={}, group={}", this.topic, this.group);
    }

    @Override
    public String topic() {
        return this.topic;
    }

    @Override
    public String group() {
        return this.group;
    }

    @Override
    public boolean autoCommit() {
        return false;
    }

    @Override
    public void onMessages(AckableMessages messages) {
        List<AckableMessage> msgList = messages.getMessages();

        traceUtil.spanDataBus(this.getClass().getName(), null, transaction -> {
            msgList.stream().forEach(ackableMessage -> {
                try {
                    String content = new String(ackableMessage.payload());
                    log.info("TaskMaterialSyncSub onMessages, msg={}", content);
                    TaskMaterialSyncMsgBo syncMsgBo = JSON.parseObject(content, TaskMaterialSyncMsgBo.class);
                    if(null == syncMsgBo) {
                        log.error("TaskMaterialSyncSub onMessages error, msg={}", content);
                        return;
                    }

                    creativeMaterialSyncService.syncTaskMaterial(syncMsgBo.getCreativeId());
                    ackableMessage.ack();
                } catch (Exception e) {
                    log.error("msg consume error, msg={}, class={}", ackableMessage.originMessage(), this.getClass().getSimpleName(), e);
                    ackableMessage.retry();
                    transaction.setStatus(e);
                }
            });
            messages.ack();
        });
    }

    @Override
    public void onMessage(AckableMessage ackableMessage) {
        traceUtil.spanDataBus(this.getClass().getName(), null, transaction -> {
            try {
                String content = new String(ackableMessage.payload());
                log.info("TaskMaterialSyncSub onMessage, msg={}", content);
                TaskMaterialSyncMsgBo syncMsgBo = JSON.parseObject(content, TaskMaterialSyncMsgBo.class);
                if(null == syncMsgBo) {
                    log.error("TaskMaterialSyncSub onMessage error, msg={}", content);
                    return;
                }

                creativeMaterialSyncService.syncTaskMaterial(syncMsgBo.getCreativeId());
                ackableMessage.ack();
            } catch (Exception e) {
                log.error("msg consume error, msg={}, class={}", ackableMessage.originMessage(), this.getClass().getSimpleName(), e);
                ackableMessage.retry();
                transaction.setStatus(e);
            }
        });
    }
}
