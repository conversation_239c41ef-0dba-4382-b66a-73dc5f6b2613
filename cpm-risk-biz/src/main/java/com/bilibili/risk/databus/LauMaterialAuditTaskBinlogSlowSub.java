package com.bilibili.risk.databus;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bilibili.risk.bo.LauMaterialAuditTaskBo;
import com.bilibili.risk.constant.BinlogConstant;
import com.bilibili.risk.service.task.MaterialAuditTaskEsService;
import com.bilibili.risk.utils.TraceUtil;
import com.bilibili.warp.databus.AckableMessage;
import com.bilibili.warp.databus.AckableMessages;
import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.databus.MessageListener;
import com.bilibili.warp.spring.boot.autoconfigure.databus.DatabusProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class LauMaterialAuditTaskBinlogSlowSub implements MessageListener {

    @Resource
    private MaterialAuditTaskEsService materialAuditTaskEsService;
    @Autowired
    private TraceUtil traceUtil;

    private final String topic;
    private final String group;


    public LauMaterialAuditTaskBinlogSlowSub(DatabusProperties databusProperties) {
        DatabusProperty databusProperty = databusProperties.getProperties().get(DatabusKeyConstants.LAU_MATERIAL_AUDIT_TASK_BINLOG_SLOW_KEY);
        this.topic = databusProperty.getTopic();
        this.group = databusProperty.getSub().getGroup();
    }


    private LauMaterialAuditTaskBo deserializeMaterialTaskBinlogBo(JSONObject insertObject) {
        if (Objects.isNull(insertObject)) {
            return null;
        }
        Timestamp mtime = null;

        try {
            mtime = new Timestamp(DateUtils.parseDate(insertObject.getString("mtime"), "yyyy-dd-MM hh:mm:ss").getTime());
        } catch (Exception e) {
        }

        return LauMaterialAuditTaskBo.builder()
                .creativeId(insertObject.getInteger("creative_id"))
                .taskId(insertObject.getString("task_id"))
                .mtime(mtime)
                .build();
    }


    @Override
    public String topic() {
        return this.topic;
    }

    @Override
    public String group() {
        return this.group;
    }

    @Override
    public boolean autoCommit() {
        return false;
    }


    @Override
    public void onMessages(AckableMessages messages) {
        List<AckableMessage> msgList = messages.getMessages();

        try {
            traceUtil.spanDataBus(this.getClass().getName(), null, transaction -> {
                List<LauMaterialAuditTaskBo> list = msgList.stream().map(m -> {
                            String value = new String(m.payload());
                            JSONObject msgJsonObj = JSONObject.parseObject(value);
                            String action = msgJsonObj.getString(BinlogConstant.ACTION);
                            if (StringUtils.isEmpty(action)) {
                                m.ack();
                                return null;
                            }
                            JSONObject newObject = msgJsonObj.getJSONObject(BinlogConstant.NEW);
                            LauMaterialAuditTaskBo newBo = deserializeMaterialTaskBinlogBo(newObject);
                            if (null != newBo) {
                                log.info("audit task binlog onMessages, taskId={}, delay={}", newBo.getTaskId(), (null == newBo.getMtime() ? 0 : System.currentTimeMillis() - newBo.getMtime().getTime()));
                            }
                            return newBo;
                        }).filter(e -> null != e)
                        .collect(Collectors.toList());

                materialAuditTaskEsService.syncTask2Es(list, true);
                messages.ack();
            });
        } catch (Exception e) {
            log.error("audit task binlog onMessages error, msgList={}", JSON.toJSONString(msgList), e);
            msgList.forEach(AckableMessage::retry);
            messages.ack();
        }
    }

    @Override
    public void onMessage(AckableMessage message) {
        String value = new String(message.payload());

        try {
            traceUtil.spanDataBus(this.getClass().getName(), null, transaction -> {
                JSONObject msgJsonObj = JSONObject.parseObject(value);
                String action = msgJsonObj.getString(BinlogConstant.ACTION);
                if (StringUtils.isEmpty(action)) {
                    return;
                }
                JSONObject newObject = msgJsonObj.getJSONObject(BinlogConstant.NEW);
                LauMaterialAuditTaskBo newBo = deserializeMaterialTaskBinlogBo(newObject);
                if(null != newBo){
                    log.info("audit task binlog onMessage, taskId={}, delay={}", newBo.getTaskId(), (null == newBo.getMtime() ? 0 : System.currentTimeMillis() - newBo.getMtime().getTime()));
                }

                try {
                    materialAuditTaskEsService.syncTask2Es(Arrays.asList(newBo), true);
                    message.ack();
                } catch (Exception e) {
                    log.error("msg consume error, msgList={}, class={}", value, this.getClass().getSimpleName(), e);
                    message.retry();
                    transaction.setStatus(e);
                }
            });
        } catch (Exception e) {
            log.error("audit task binlog onMessage error, msgList={}", value, e);
            message.retry();
        }
    }
}
