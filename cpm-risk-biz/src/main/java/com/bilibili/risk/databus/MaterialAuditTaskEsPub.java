package com.bilibili.risk.databus;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.risk.bo.msg.MaterialTaskTimeoutMsgBo;
import com.bilibili.risk.po.es.LauMaterialAuditTaskEsPo;
import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.databus.DatabusTemplate;
import com.bilibili.warp.databus.Message;
import com.bilibili.warp.databus.PubResult;
import com.bilibili.warp.spring.boot.autoconfigure.databus.DatabusProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;

@Slf4j
@Service
public class MaterialAuditTaskEsPub {

    @Resource
    private DatabusTemplate databusTemplate;

    private final String topic;
    private final String group;

    public MaterialAuditTaskEsPub(DatabusProperties databusProperties) {
        DatabusProperty databusProperty = databusProperties.getProperties().get(DatabusKeyConstants.LAU_MATERIAL_AUDIT_TASK_ES_KEY);
        this.topic = databusProperty.getTopic();
        this.group = databusProperty.getPub().getGroup();
        log.info("MaterialAuditTaskEsPub, topic={}, group={}", topic, group);
    }

    public void pub(LauMaterialAuditTaskEsPo msg) {

        if (msg == null) {
            return;
        }

        JSONObject newJson = new JSONObject();
        newJson.put("new", msg);
        newJson.put("action", "update");
        newJson.put("binlog_time", System.currentTimeMillis());

        String msgStr = JSON.toJSONString(msg);
        log.info("material task es save pub msg, msg={}", msgStr);
        Assert.notNull(msg, "auditCreativeMessage参数不能为空");

        // messageKey和value自定义，value会被配置的serializer序列化
        Message message = Message.Builder
                .of(msg.getId(), newJson)
                .build();
        // topic 和 group 按照申请的填写即可. 可以自行注入相关配置，用配置的方式来发送消息
        PubResult result = databusTemplate.pub(topic, group, message);
        if (result.isSuccess()) {
            log.info("material task es save pub msg success, msg={}", JSON.toJSONString(msg));
        } else {
            Throwable throwable = result.getThrowable();
            log.error("material task es save pub msg error ", throwable);
            throw new RuntimeException(throwable.getMessage());
        }

    }

}
