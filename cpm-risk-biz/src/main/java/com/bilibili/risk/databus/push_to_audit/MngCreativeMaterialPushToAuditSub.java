package com.bilibili.risk.databus.push_to_audit;

import com.alibaba.fastjson.JSON;
import com.bilibili.risk.bo.msg.MaterialInfoPushToAuditMsgBo;
import com.bilibili.risk.databus.DatabusKeyConstants;
import com.bilibili.risk.service.material.CreativeMaterialPushToAuditService;
import com.bilibili.risk.utils.TraceUtil;
import com.bilibili.warp.databus.AckableMessage;
import com.bilibili.warp.databus.AckableMessages;
import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.databus.MessageListener;
import com.bilibili.warp.spring.boot.autoconfigure.databus.DatabusProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class MngCreativeMaterialPushToAuditSub implements MessageListener {

    private final String topic;
    private final String group;

    @Autowired
    private CreativeMaterialPushToAuditService creativeMaterialPushToAuditService;
    @Autowired
    private TraceUtil traceUtil;

    public MngCreativeMaterialPushToAuditSub(DatabusProperties databusProperties) {
        DatabusProperty databusProperty = databusProperties.getProperties().get(DatabusKeyConstants.CREATIVE_MATERIAL_PUSH_TO_AUDIT_KEY);
        this.topic = databusProperty.getTopic();
        this.group = databusProperty.getSub().getGroup();
        log.info("CreativeMaterialPushToAuditSub init, topic={}, group={}", this.topic, this.group);
    }


    @Override
    public String topic() {
        return this.topic;
    }

    @Override
    public String group() {
        return this.group;
    }

    @Override
    public boolean autoCommit() {
        return false;
    }

    @Override
    public void onMessages(AckableMessages messages) {
        for (AckableMessage message : messages.getMessages()) {
            String content = new String(message.payload());
            MaterialInfoPushToAuditMsgBo msgBo = JSON.parseObject(content, MaterialInfoPushToAuditMsgBo.class);

            try {
                traceUtil.spanDataBus(this.getClass().getName(), null, transaction -> {
                    log.info("creative material push to audit,creative change onMessages[推审,运营后台], ackableMessage={}, msgBo={}", JSON.toJSONString(content), JSON.toJSONString(msgBo));
                    creativeMaterialPushToAuditService.processCreativeMaterialPushToAudit(msgBo);
                    message.ack();
                });
            } catch (Exception e) {
                log.error("msg consume error, msgList={}, class={}", JSON.toJSONString(msgBo), this.getClass().getSimpleName(), e);
                message.retry();
            }
        }
        messages.ack();
    }

    @Override
    public void onMessage(AckableMessage message) {
        String content = new String(message.payload());
        MaterialInfoPushToAuditMsgBo msgBo = JSON.parseObject(content, MaterialInfoPushToAuditMsgBo.class);

        try {
            traceUtil.spanDataBus(this.getClass().getName(), null, x -> {
                log.info("creative material push to audit,creative change[推审,运营后台], ackableMessage={}, msgBo={}", JSON.toJSONString(content), JSON.toJSONString(msgBo));
                creativeMaterialPushToAuditService.processCreativeMaterialPushToAudit(msgBo);
                message.ack();
            });
        } catch (Exception e) {
            log.error("msg consume error, msgList={}, class={}", JSON.toJSONString(msgBo), this.getClass().getSimpleName(), e);
            message.retry();
        }
    }
}
