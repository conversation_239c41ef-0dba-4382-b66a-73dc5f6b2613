package com.bilibili.risk.databus;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.risk.bo.LauMaterialAuditTaskBo;
import com.bilibili.risk.config.BizConfig;
import com.bilibili.risk.constant.BinlogConstant;
import com.bilibili.risk.enums.ShardingTableEnum;
import com.bilibili.risk.po.risk.LauMaterialAuditTaskPo;
import com.bilibili.risk.service.material.CreativeMaterialSyncService;
import com.bilibili.risk.utils.TraceUtil;
import com.bilibili.warp.databus.AckableMessage;
import com.bilibili.warp.databus.AckableMessages;
import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.databus.MessageListener;
import com.bilibili.warp.spring.boot.autoconfigure.databus.DatabusProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TaskBinlogBizSub implements MessageListener {

    @Autowired
    private TraceUtil traceUtil;

    private final String topic;
    private final String group;

    @Autowired
    private BizConfig bizConfig;

    @Autowired
    private CreativeMaterialSyncService creativeMaterialSyncService;


    public TaskBinlogBizSub(DatabusProperties databusProperties) {
        DatabusProperty databusProperty = databusProperties.getProperties().get(DatabusKeyConstants.LAU_MATERIAL_AUDIT_TASK_BINLOG_BIZ_KEY);
        this.topic = databusProperty.getTopic();
        this.group = databusProperty.getSub().getGroup();
    }

    @Override
    public String topic() {
        return this.topic;
    }

    @Override
    public String group() {
        return this.group;
    }

    @Override
    public boolean autoCommit() {
        return false;
    }

    @Override
    public void onMessages(AckableMessages messages) {
        List<AckableMessage> msgList = messages.getMessages();

        try {
            traceUtil.spanDataBus(this.getClass().getName(), null, transaction -> {
                List<Pair<AckableMessage, LauMaterialAuditTaskPo>> pairs = msgList.stream().map(m -> {
                    String value = new String(m.payload());
                    JSONObject msgJsonObj = JSONObject.parseObject(value);
                    String action = msgJsonObj.getString(BinlogConstant.ACTION);

                    log.info("task biz binlog onMessages, msg={}", value);
                    // 只处理更新操作
                    if (StringUtils.isBlank(action) || !BinlogConstant.UPDATE.equals(action)) {
                        m.ack();
                        return null;
                    }

                    JSONObject newObject = msgJsonObj.getJSONObject(BinlogConstant.NEW);
                    LauMaterialAuditTaskPo newPo = deserializeMaterialTaskBinlogBo(newObject);
                    JSONObject oldObject = msgJsonObj.getJSONObject(BinlogConstant.OLD);
                    LauMaterialAuditTaskPo oldPo = deserializeMaterialTaskBinlogBo(oldObject);
                    if(null == newPo || null == oldPo) {
                        m.ack();
                        return null;
                    }

                    // 修改为已删除的才处理
                    if(!Objects.equals(IsDeleted.VALID.getCode(), oldPo.getIsDeleted()) ||
                        !Objects.equals(IsDeleted.DELETED.getCode(), newPo.getIsDeleted())) {
                        m.ack();
                        return null;
                    }

                    // 如果无审核结果 && 不在等待队列
                    if(StringUtils.isBlank(newPo.getAuditLabelThirdId())
                            && !Objects.equals(newPo.getQueueId(), bizConfig.getWaitQueueId())){
                        return Pair.of(m, newPo);
                    }

                    m.ack();
                    return null;
                }).filter(e -> null != e && null != e.getValue()).collect(Collectors.toList());

                for (Pair<AckableMessage, LauMaterialAuditTaskPo> pair : pairs) {
                    creativeMaterialSyncService.unAuditTaskDelete(pair.getValue().getTaskId());
                }
            });
            messages.ack();
        } catch (Exception e) {
            log.error("audit task binlog onMessages error, msgList={}", JSON.toJSONString(msgList), e);
            msgList.forEach(AckableMessage::retry);
            messages.ack();
        }
    }

    private LauMaterialAuditTaskPo deserializeMaterialTaskBinlogBo(JSONObject insertObject) {
        if (Objects.isNull(insertObject)) {
            return null;
        }
        return LauMaterialAuditTaskPo.builder()
                .materialId(insertObject.getString("material_id"))
                .auditLabelThirdId(insertObject.getString("audit_label_third_id"))
                .queueId(insertObject.getLong("queue_id"))
                .isDeleted(insertObject.getInteger("is_deleted"))
                .taskId(insertObject.getString("task_id"))
                .build();
    }
}
