package com.bilibili.risk.databus.component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.risk.bo.LauMaterialAuditBo;
import com.bilibili.risk.bo.msg.*;
import com.bilibili.risk.constant.BinlogConstant;
import com.bilibili.risk.constant.RiskConstants;
import com.bilibili.risk.databus.DatabusKeyConstants;
import com.bilibili.risk.enums.FirePushAuditSourceEnum;
import com.bilibili.risk.enums.RiskMaterialTypeEnum;
import com.bilibili.risk.service.component.ComponentMaterialSyncService;
import com.bilibili.risk.service.material.CreativeMaterialPushToAuditService;
import com.bilibili.risk.utils.CatUtils;
import com.bilibili.risk.utils.TraceUtil;
import com.bilibili.warp.databus.AckableMessage;
import com.bilibili.warp.databus.AckableMessages;
import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.databus.MessageListener;
import com.bilibili.warp.spring.boot.autoconfigure.databus.DatabusProperties;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.common.util.set.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class LauStoryCommonComponentBinlogSub implements MessageListener {

    @Autowired
    private ComponentMaterialSyncService componentMaterialSyncService;

    @Autowired
    private CreativeMaterialPushToAuditService creativeMaterialPushToAuditService;
    @Autowired
    private TraceUtil traceUtil;

    private final String topic;
    private final String group;

    private static final String COMMON = "lau_story_common_component";
    private static final String COUPON = "lau_story_coupon_component";
    private static final String IMAGE = "lau_story_image_component";
    private static final String UNDERFRAME = "lau_underframe_component";

    private static final String CREATIVE_STORY = "lau_creative_component";


    public LauStoryCommonComponentBinlogSub(DatabusProperties databusProperties) {
        DatabusProperty databusProperty = databusProperties.getProperties().get(DatabusKeyConstants.STORY_BINLOG);
        this.topic = databusProperty.getTopic();
        this.group = databusProperty.getSub().getGroup();
        log.info("LauStoryCommonComponentBinlogSub topic:{}, group:{}", this.topic, this.group);
    }


    private LauStoryCommonComponentBinlogBo deserializeCommonMBinlogBo(JSONObject insertObject) {
        if (Objects.isNull(insertObject)) {
            return null;
        }
        LauStoryCommonComponentBinlogBo binlogBo = null;
        try {
            binlogBo = insertObject.toJavaObject(insertObject, LauStoryCommonComponentBinlogBo.class);
        } catch (Exception e) {
            log.error("deserializeMBinlogBo error, insertObject:{}", insertObject, e);
        }
        return binlogBo;
    }


    @Override
    public String topic() {
        return this.topic;
    }

    @Override
    public String group() {
        return this.group;
    }

    @Override
    public boolean autoCommit() {
        return false;
    }


    @Override
    public void onMessages(AckableMessages messages) {

        List<AckableMessage> msgList = messages.getMessages();

        try {
            traceUtil.spanDataBus(this.getClass().getName(), null, transaction -> {

                for (AckableMessage ackableMessage : msgList) {
                    try {
                        String value = new String(ackableMessage.payload());
                        JSONObject msgJsonObj = JSONObject.parseObject(value);
                        String table = msgJsonObj.getString(BinlogConstant.TABLE);

                        if(StringUtils.isBlank(table)){
                            log.error("table is null, msgJsonObj:{}", msgJsonObj);
                            ackableMessage.ack();
                            continue;
                        }

                        log.info("story binlog onMessage, table:{}, msg:{}", table, value);
                        switch (table){
                            case COMMON:
                                syncCommonMaterials2Risk(Lists.newArrayList(ackableMessage));
                                break;
                            case COUPON:
                                syncCouponMaterials2Risk(Lists.newArrayList(ackableMessage));
                                break;
                            case IMAGE:
                                syncImageMaterials2Risk(Lists.newArrayList(ackableMessage));
                                break;
                            case UNDERFRAME:
                                syncUnderframe2LauMaterial(Lists.newArrayList(ackableMessage));
                                break;
                            case CREATIVE_STORY:
                                syncCreativeStory(ackableMessage);
                                break;
                            default:
                                log.error("table is not support, table:{}, msgJsonObj:{}", table, msgJsonObj);
                                break;
                        }
                        ackableMessage.ack();
                    } catch (Exception e) {
                        log.error("msg consume error, msgList={}, class={}", JSON.toJSONString(ackableMessage), this.getClass().getSimpleName(), e);
                        ackableMessage.retry();
                        transaction.setStatus(e);
                    }
                }
                messages.ack();
            });
        } catch (Exception e) {
            log.error("onMessage error, msgList={}", JSON.toJSONString(msgList), e);
            msgList.forEach(AckableMessage::retry);
            messages.ack();
        }
    }

    /**
     * 变动的素材同步到risk
     * @param msgList
     */
    private void syncCommonMaterials2Risk(List<AckableMessage> msgList) {
        List<Long> componentIds = msgList.stream().map(m -> {
            String value = new String(m.payload());
            JSONObject msgJsonObj = JSONObject.parseObject(value);
            String action = msgJsonObj.getString(BinlogConstant.ACTION);
            // 只处理更新的
            if (!Objects.equals(action, BinlogConstant.UPDATE)) {
                return null;
            }
            JSONObject oldObject = msgJsonObj.getJSONObject(BinlogConstant.OLD);
            LauStoryCommonComponentBinlogBo oldBo = deserializeCommonMBinlogBo(oldObject);
            JSONObject newObject = msgJsonObj.getJSONObject(BinlogConstant.NEW);
            LauStoryCommonComponentBinlogBo newBo = deserializeCommonMBinlogBo(newObject);
            if (Objects.isNull(oldBo) || Objects.isNull(newBo)) {
                return null;
            }
            // 标题，描述，图片至少一个发生改变
            if (!newBo.getTitle().equals(oldBo.getTitle()) && !newBo.getDescription().equals(oldBo.getDescription()) && !newBo.getImageUrl().equals(oldBo.getImageUrl())) {
                return null;
            }
            return newBo;
        }).filter(Objects::nonNull).map(t -> t.getComponentId()).collect(Collectors.toList());

        // 关联的创意重新推审
        componentMaterialSyncService.relatedCreativeSplitMaterialsPushToAudit(componentIds, RiskConstants.COMPONENT_TYPE_STORY_COMMON);
    }


    private void syncCouponMaterials2Risk(List<AckableMessage> msgList) {
        List<Long> componentIds = msgList.stream().map(m -> {
            String value = new String(m.payload());
            JSONObject msgJsonObj = JSONObject.parseObject(value);
            String action = msgJsonObj.getString(BinlogConstant.ACTION);
            // 只处理更新的
            if (!Objects.equals(action, BinlogConstant.UPDATE)) {
                return null;
            }
            JSONObject oldObject = msgJsonObj.getJSONObject(BinlogConstant.OLD);
            LauStoryCouponComponentBinlogBo oldBo = deserializeCouponMBinlogBo(oldObject);
            JSONObject newObject = msgJsonObj.getJSONObject(BinlogConstant.NEW);
            LauStoryCouponComponentBinlogBo newBo = deserializeCouponMBinlogBo(newObject);
            if (Objects.isNull(oldBo) || Objects.isNull(newBo)) {
                return null;
            }
            // 标题，描述，图片至少一个发生改变
            if (!newBo.getDescription().equals(oldBo.getDescription())) {
                return null;
            }
            return newBo;
        }).filter(Objects::nonNull).map(t -> t.getComponentId()).collect(Collectors.toList());

        componentMaterialSyncService.relatedCreativeSplitMaterialsPushToAudit(componentIds, RiskConstants.COMPONENT_TYPE_STORY_COUPON);
    }

    private LauStoryCouponComponentBinlogBo deserializeCouponMBinlogBo(JSONObject insertObject) {
        if (Objects.isNull(insertObject)) {
            return null;
        }
        LauStoryCouponComponentBinlogBo binlogBo = null;
        try {
            binlogBo = insertObject.toJavaObject(insertObject, LauStoryCouponComponentBinlogBo.class);
        } catch (Exception e) {
            log.error("deserializeMBinlogBo error, insertObject:{}", insertObject, e);
        }
        return binlogBo;
    }

    private void syncImageMaterials2Risk(List<AckableMessage> msgList) {
        List<Long> componentIds = msgList.stream().map(m -> {
            String value = new String(m.payload());
            JSONObject msgJsonObj = JSONObject.parseObject(value);
            String action = msgJsonObj.getString(BinlogConstant.ACTION);
            // 只处理更新的
            if (!Objects.equals(action, BinlogConstant.UPDATE)) {
                return null;
            }
            JSONObject oldObject = msgJsonObj.getJSONObject(BinlogConstant.OLD);
            LauStoryImageComponentBinlogBo oldBo = deserializeImageMBinlogBo(oldObject);
            JSONObject newObject = msgJsonObj.getJSONObject(BinlogConstant.NEW);
            log.info("story image component, newObject:{}", newObject);
            LauStoryImageComponentBinlogBo newBo = deserializeImageMBinlogBo(newObject);
            if (Objects.isNull(oldBo) || Objects.isNull(newBo)) {
                return null;
            }
            // 图片发生改变
            if (!newBo.getImageUrl().equals(oldBo.getImageUrl())) {
                return null;
            }
            return newBo;
        }).filter(Objects::nonNull).map(t -> t.getComponentId()).collect(Collectors.toList());

        componentMaterialSyncService.relatedCreativeSplitMaterialsPushToAudit(componentIds, RiskConstants.COMPONENT_TYPE_STORY_IMAGE);
    }

    private LauStoryImageComponentBinlogBo deserializeImageMBinlogBo(JSONObject insertObject) {
        if (Objects.isNull(insertObject)) {
            return null;
        }
        LauStoryImageComponentBinlogBo binlogBo = null;
        try {
            binlogBo = insertObject.toJavaObject(insertObject, LauStoryImageComponentBinlogBo.class);
        } catch (Exception e) {
            log.error("deserializeMBinlogBo error, insertObject:{}", insertObject, e);
        }
        return binlogBo;
    }

    private void syncUnderframe2LauMaterial(List<AckableMessage> msgList) {
        List<Long> componentIds = msgList.stream().map(m -> {
            String value = new String(m.payload());
            JSONObject msgJsonObj = JSONObject.parseObject(value);
            String action = msgJsonObj.getString(BinlogConstant.ACTION);
            // 只处理更新的
            if (!Objects.equals(action, BinlogConstant.UPDATE)) {
                return null;
            }
            JSONObject oldObject = msgJsonObj.getJSONObject(BinlogConstant.OLD);
            LauUnderframeComponentBinlogBo oldBo = deserializeUnderframeMBinlogBo(oldObject);
            JSONObject newObject = msgJsonObj.getJSONObject(BinlogConstant.NEW);
            LauUnderframeComponentBinlogBo newBo = deserializeUnderframeMBinlogBo(newObject);
            if (Objects.isNull(oldBo) || Objects.isNull(newBo)) {
                return null;
            }
            // 标题，描述，图片,url至少一个发生改变
            if (!newBo.getTitle().equals(oldBo.getTitle()) && !newBo.getDescription().equals(oldBo.getDescription()) && !newBo.getImageUrl().equals(oldBo.getImageUrl()) && !newBo.getRawJumpUrl().equals(oldBo.getRawJumpUrl())) {
                return null;
            }
            return newBo;
        }).filter(Objects::nonNull).map(t -> t.getId()).collect(Collectors.toList());

        componentMaterialSyncService.relatedCreativeSplitMaterialsPushToAudit(componentIds, RiskConstants.COMPONENT_TYPE_UNDERFRAME);
    }

    private LauUnderframeComponentBinlogBo deserializeUnderframeMBinlogBo(JSONObject insertObject) {
        if (Objects.isNull(insertObject)) {
            return null;
        }
        LauUnderframeComponentBinlogBo binlogBo = null;
        try {
            binlogBo = insertObject.toJavaObject(insertObject, LauUnderframeComponentBinlogBo.class);
        } catch (Exception e) {
            log.error("deserializeMBinlogBo error, insertObject:{}", insertObject, e);
        }
        return binlogBo;
    }

    private void syncCreativeStory(AckableMessage msg){
        String value = new String(msg.payload());
        JSONObject msgJsonObj = JSONObject.parseObject(value);
        String action = msgJsonObj.getString(BinlogConstant.ACTION);

        // 只处理删除的
        if (Objects.equals(action, BinlogConstant.INSERT)) {
            return;
        }

        LauCreativeComponentBinlogBo newBo = deserializeCreativeMBinlogBo(msgJsonObj.getJSONObject(BinlogConstant.NEW));
        LauCreativeComponentBinlogBo oldBo = deserializeCreativeMBinlogBo(msgJsonObj.getJSONObject(BinlogConstant.OLD));

        // 物理删的
        if(Objects.equals(action, BinlogConstant.DELETE)) {
            OtherSourceFirePushAuditMsgBo msgBo = OtherSourceFirePushAuditMsgBo.builder().source(FirePushAuditSourceEnum.STORY.getCode())
                    .componentId(newBo.getComponentId()).build();
            creativeMaterialPushToAuditService.firePushAuditIfNeed(Sets.newHashSet(newBo.getCreativeId()), msgBo);
        }

        // 逻辑删的
        if(Objects.equals(action, BinlogConstant.UPDATE) && Objects.equals(newBo.getIsDeleted(), IsDeleted.DELETED.getCode())
                && Objects.equals(oldBo.getIsDeleted(), IsDeleted.VALID.getCode())) {
            OtherSourceFirePushAuditMsgBo msgBo = OtherSourceFirePushAuditMsgBo.builder().source(FirePushAuditSourceEnum.STORY.getCode())
                    .componentId(newBo.getComponentId()).build();
            creativeMaterialPushToAuditService.firePushAuditIfNeed(Sets.newHashSet(newBo.getCreativeId()), msgBo);
        }
    }

    private LauCreativeComponentBinlogBo deserializeCreativeMBinlogBo(JSONObject insertObject) {
        if (Objects.isNull(insertObject)) {
            return null;
        }
        LauCreativeComponentBinlogBo binlogBo = null;
        try {
            binlogBo = insertObject.toJavaObject(insertObject, LauCreativeComponentBinlogBo.class);
        } catch (Exception e) {
            log.error("deserializeMBinlogBo error, insertObject:{}", insertObject, e);
        }
        return binlogBo;
    }
}
