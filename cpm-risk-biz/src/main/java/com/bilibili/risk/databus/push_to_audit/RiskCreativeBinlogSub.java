package com.bilibili.risk.databus.push_to_audit;

import com.alibaba.fastjson.JSONObject;
import com.bilibili.risk.constant.BinlogConstant;
import com.bilibili.risk.databus.DatabusKeyConstants;
import com.bilibili.risk.service.material.CreativeMaterialSyncService;
import com.bilibili.risk.utils.CatUtils;
import com.bilibili.risk.utils.JacksonUtils;
import com.bilibili.risk.utils.TraceUtil;
import com.bilibili.warp.databus.AckableMessage;
import com.bilibili.warp.databus.AckableMessages;
import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.databus.MessageListener;
import com.bilibili.warp.spring.boot.autoconfigure.databus.DatabusProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

@Service
@Slf4j
public class RiskCreativeBinlogSub implements MessageListener {

    @Autowired
    private TraceUtil traceUtil;

    @Autowired
    private CreativeMaterialSyncService creativeMaterialSyncService;

    private final String topic;
    private final String group;

    public RiskCreativeBinlogSub(DatabusProperties databusProperties) {
        DatabusProperty databusProperty = databusProperties.getProperties().get(DatabusKeyConstants.RISK_CREATIVE_BINLOG);
        this.topic = databusProperty.getTopic();
        this.group = databusProperty.getSub().getGroup();
        log.info("TaskMaterialSyncSub init, topic={}, group={}", this.topic, this.group);
    }

    @Override
    public String topic() {
        return this.topic;
    }

    @Override
    public String group() {
        return this.group;
    }

    @Override
    public boolean autoCommit() {
        return false;
    }


    @Override
    public void onMessage(AckableMessage message) {
        traceUtil.spanDataBus(this.getClass().getName(), null, transaction -> {
            try {
                String value = new String(message.payload());
                log.info("RiskCreativeBinlogSub, msg={}", value);
                JSONObject msgJsonObj = JSONObject.parseObject(value);
                String action = msgJsonObj.getString(BinlogConstant.ACTION);
                if (StringUtils.isEmpty(action) || BinlogConstant.DELETE.equals(action)) {
                    return;
                }
                JSONObject newObject = msgJsonObj.getJSONObject(BinlogConstant.NEW);
                Integer creativeId = newObject.getInteger("creative_id");

                creativeMaterialSyncService.syncMaterialCreative(creativeId);
                message.ack();
            } catch (Exception e) {
                log.error("msg consume error, msgList={}, class={}", message.originMessage(), this.getClass().getSimpleName(), e);
                message.retry();
                transaction.setStatus(e);
            }
        });
    }

    @Override
    public void onMessages(AckableMessages messages) {
        traceUtil.spanDataBus(this.getClass().getName(), null, transaction -> {

            for (AckableMessage message : messages.getMessages()) {
                try {
                    String value = new String(message.payload());
                    log.info("RiskCreativeBinlogSub, msg={}", value);
                    JSONObject msgJsonObj = JSONObject.parseObject(value);
                    String action = msgJsonObj.getString(BinlogConstant.ACTION);
                    if (StringUtils.isEmpty(action) || BinlogConstant.DELETE.equals(action)) {
                        return;
                    }
                    JSONObject newObject = msgJsonObj.getJSONObject(BinlogConstant.NEW);
                    Integer creativeId = newObject.getInteger("creative_id");

                    creativeMaterialSyncService.syncMaterialCreative(creativeId);
                    message.ack();
                } catch (Exception e) {
                    log.error("msg consume error, msgList={}, class={}", message.originMessage(), this.getClass().getSimpleName(), e);
                    message.retry();
                    transaction.setStatus(e);
                }
            }
        });
    }
}
