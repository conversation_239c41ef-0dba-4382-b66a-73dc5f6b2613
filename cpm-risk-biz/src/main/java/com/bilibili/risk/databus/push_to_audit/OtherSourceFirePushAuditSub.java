package com.bilibili.risk.databus.push_to_audit;

import com.alibaba.fastjson.JSON;
import com.bilibili.risk.bo.msg.OtherSourceFirePushAuditMsgBo;
import com.bilibili.risk.databus.DatabusKeyConstants;
import com.bilibili.risk.service.material.CreativeMaterialPushToAuditService;
import com.bilibili.risk.utils.CatUtils;
import com.bilibili.risk.utils.TraceUtil;
import com.bilibili.warp.databus.AckableMessage;
import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.databus.MessageListener;
import com.bilibili.warp.spring.boot.autoconfigure.databus.DatabusProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class OtherSourceFirePushAuditSub implements MessageListener {

    private final String topic;
    private final String group;

    @Autowired
    private CreativeMaterialPushToAuditService creativeMaterialPushToAuditService;

    @Autowired
    private TraceUtil traceUtil;

    public OtherSourceFirePushAuditSub(DatabusProperties databusProperties) {
        DatabusProperty databusProperty = databusProperties.getProperties().get(DatabusKeyConstants.OTHER_SOURCE_FIRE_PUSH_AUDIT);
        this.topic = databusProperty.getTopic();
        this.group = databusProperty.getSub().getGroup();
        log.info("OtherSourceFirePushAuditSub, topic={}, group={}", topic, group);
    }

    @Override
    public void onMessage(AckableMessage message) {
        traceUtil.spanDataBus(this.getClass().getName(), null, transaction -> {
            String content = new String(message.payload());

            OtherSourceFirePushAuditMsgBo msg = JSON.parseObject(content, OtherSourceFirePushAuditMsgBo.class);
            try {
                creativeMaterialPushToAuditService.pushAuditByTask(msg);
                message.ack();
            } catch (Exception e) {
                log.error("msg consume error, msgList={}, class={}", JSON.toJSONString(msg), this.getClass().getSimpleName(), e);
                message.retry();
                transaction.setStatus(e);
            }
        });
    }

    @Override
    public String topic() {
        return this.topic;
    }

    @Override
    public String group() {
        return this.group;
    }

    @Override
    public boolean autoCommit() {
        return false;
    }
}
