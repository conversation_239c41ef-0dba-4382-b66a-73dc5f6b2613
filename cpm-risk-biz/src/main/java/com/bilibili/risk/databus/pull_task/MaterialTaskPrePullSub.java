package com.bilibili.risk.databus.pull_task;

import com.alibaba.fastjson.JSON;
import com.bilibili.risk.bo.MaterialAuditTaskPullQueryBo;
import com.bilibili.risk.databus.DatabusKeyConstants;
import com.bilibili.risk.service.task.MaterialAuditTaskPullService;
import com.bilibili.risk.utils.TraceUtil;
import com.bilibili.warp.databus.AckableMessage;
import com.bilibili.warp.databus.AckableMessages;
import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.databus.MessageListener;
import com.bilibili.warp.spring.boot.autoconfigure.databus.DatabusProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class MaterialTaskPrePullSub implements MessageListener {

    private final String topic;
    private final String group;

    @Autowired
    private TraceUtil traceUtil;
    @Autowired
    private MaterialAuditTaskPullService materialAuditTaskPullService;

    public MaterialTaskPrePullSub(DatabusProperties databusProperties) {
        DatabusProperty databusProperty = databusProperties.getProperties().get(DatabusKeyConstants.CREATIVE_MATERIAL_PRE_PULL);
        this.topic = databusProperty.getTopic();
        this.group = databusProperty.getSub().getGroup();
        log.info("MaterialTaskPrePullSub init, topic={}, group={}", this.topic, this.group);
    }

    @Override
    public String topic() {
        return this.topic;
    }

    @Override
    public String group() {
        return this.group;
    }

    @Override
    public boolean autoCommit() {
        return false;
    }

    @Override
    public void onMessage(AckableMessage message) {
        try {
            traceUtil.spanDataBus(this.getClass().getName(), null, transaction -> {
                String content = new String(message.payload());
                log.info("receive retry message onMessage[预拉取消息], msg={}, retryCount={}", content, message.originMessage().getRetryCount());

                MaterialAuditTaskPullQueryBo msgBo = JSON.parseObject(content, MaterialAuditTaskPullQueryBo.class);
                materialAuditTaskPullService.pullOneBatchMaterialAuditTasks(msgBo);
                message.ack();
            });
        } catch (Exception e) {
            message.ack();
        }
    }

    @Override
    public void onMessages(AckableMessages messages) {
        try {
            traceUtil.spanDataBus(this.getClass().getName(), null, x -> {
                for (AckableMessage message : messages.getMessages()) {
                    String content = new String(message.payload());
                    log.info("receive retry message onMessages[预拉取消息], msg={}, retryCount={}", content, message.originMessage().getRetryCount());

                    MaterialAuditTaskPullQueryBo msgBo = JSON.parseObject(content, MaterialAuditTaskPullQueryBo.class);
                    materialAuditTaskPullService.pullOneBatchMaterialAuditTasks(msgBo);
                    message.ack();
                }
            });
        } catch (Exception e) {
            for (AckableMessage message : messages.getMessages()) {
                message.retry();
            }
            messages.ack();
        }
    }
}
