package com.bilibili.risk.databus.material_audit;

import com.alibaba.fastjson.JSON;
import com.bilibili.risk.bo.msg.MaterialAuditMsgBo;
import com.bilibili.risk.databus.DatabusKeyConstants;
import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.databus.DatabusTemplate;
import com.bilibili.warp.databus.Message;
import com.bilibili.warp.databus.PubResult;
import com.bilibili.warp.spring.boot.autoconfigure.databus.DatabusProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;

/**
 **/
@Slf4j
@Service
public class MaterialAuditToJudgeAllCreativePub {

    @Resource
    private DatabusTemplate databusTemplate;

    private final String topic;
    private final String group;

    public MaterialAuditToJudgeAllCreativePub(DatabusProperties databusProperties) {
        DatabusProperty databusProperty = databusProperties.getProperties().get(DatabusKeyConstants.MATERIAL_AUDIT_KEY);
        this.topic = databusProperty.getTopic();
        this.group = databusProperty.getPub().getGroup();
        log.info("MaterialAuditPub, topic={}, group={}", topic, group);
    }

    public void pub(MaterialAuditMsgBo auditCreativeMessage) {

        if (auditCreativeMessage == null || StringUtils.isBlank(auditCreativeMessage.getMaterialId())) {
            return;
        }

        String msgStr = JSON.toJSONString(auditCreativeMessage);
        log.info("material audit pub msg, msg={}", msgStr);
        Assert.notNull(auditCreativeMessage, "auditCreativeMessage参数不能为空");

        // messageKey和value自定义，value会被配置的serializer序列化
        Message message = Message.Builder.of(auditCreativeMessage.getMaterialId() + "", auditCreativeMessage)
                .build();
        // topic 和 group 按照申请的填写即可. 可以自行注入相关配置，用配置的方式来发送消息
        PubResult result = databusTemplate.pub(topic, group, message);
        if (result.isSuccess()) {
            log.info("material audit pub msg success, msg={}", JSON.toJSONString(auditCreativeMessage));
        } else {
            Throwable throwable = result.getThrowable();
            log.error("material audit pub msg error ", throwable);
            throw new RuntimeException(throwable.getMessage());
        }

    }

}
