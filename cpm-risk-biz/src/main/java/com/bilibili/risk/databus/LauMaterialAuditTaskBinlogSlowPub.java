package com.bilibili.risk.databus;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bilibili.risk.bo.LauMaterialAuditTaskBo;
import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.databus.DatabusTemplate;
import com.bilibili.warp.databus.Message;
import com.bilibili.warp.databus.PubResult;
import com.bilibili.warp.spring.boot.autoconfigure.databus.DatabusProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class LauMaterialAuditTaskBinlogSlowPub {

    @Resource
    private DatabusTemplate databusTemplate;

    private final String topic;
    private final String group;

    public LauMaterialAuditTaskBinlogSlowPub(DatabusProperties databusProperties) {
        DatabusProperty databusProperty = databusProperties.getProperties().get(DatabusKeyConstants.LAU_MATERIAL_AUDIT_TASK_BINLOG_SLOW_KEY);
        this.topic = databusProperty.getTopic();
        this.group = databusProperty.getPub().getGroup();
        log.info("LauMaterialAuditTaskBinlogSlowPub, topic={}, group={}", topic, group);
    }

    public void pub(String taskId, JSONObject msg) {

        if (msg == null) {
            return;
        }


        String msgStr = JSON.toJSONString(msg);
        log.info("lau material task binlog slow save pub msg, taskId={},msg={}", taskId, msgStr);
        Assert.notNull(msg, "auditCreativeMessage参数不能为空");

        // messageKey和value自定义，value会被配置的serializer序列化
        Message message = Message.Builder
                .of(taskId, msg)
                .build();
        // topic 和 group 按照申请的填写即可. 可以自行注入相关配置，用配置的方式来发送消息
        PubResult result = databusTemplate.pub(topic, group, message);
        if (result.isSuccess()) {
            log.info("lau material task binlog slow save pub msg success, msg={}", JSON.toJSONString(msg));
        } else {
            Throwable throwable = result.getThrowable();
            log.error("lau material task binlog slow save pub msg error ", throwable);
            throw new RuntimeException(throwable.getMessage());
        }

    }

}
