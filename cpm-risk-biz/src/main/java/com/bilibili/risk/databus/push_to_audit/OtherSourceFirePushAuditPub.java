package com.bilibili.risk.databus.push_to_audit;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.risk.bo.msg.CreativeMaterialPushToAuditMsgBo;
import com.bilibili.risk.bo.msg.OtherSourceFirePushAuditMsgBo;
import com.bilibili.risk.databus.DatabusKeyConstants;
import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.databus.DatabusTemplate;
import com.bilibili.warp.databus.Message;
import com.bilibili.warp.databus.PubResult;
import com.bilibili.warp.spring.boot.autoconfigure.databus.DatabusProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class OtherSourceFirePushAuditPub {

    @Autowired
    private DatabusTemplate databusTemplate;

    private final String topic;
    private final String group;

    public OtherSourceFirePushAuditPub(DatabusProperties databusProperties) {
        DatabusProperty databusProperty = databusProperties.getProperties().get(DatabusKeyConstants.OTHER_SOURCE_FIRE_PUSH_AUDIT);
        this.topic = databusProperty.getTopic();
        this.group = databusProperty.getPub().getGroup();
        log.info("CreativeMaterialPushToAuditPub, topic={}, group={}", topic, group);
    }

    public void pub(OtherSourceFirePushAuditMsgBo msg) {

        if (msg == null || !Utils.isPositive(msg.getCreativeId())) {
            return;
        }

        String msgStr = JSON.toJSONString(msg);
        log.info("OtherSourceFirePushAuditPub pub msg, msg={}", msgStr);

        // messageKey和value自定义，value会被配置的serializer序列化
        Message message = Message.Builder.of(msg.getCreativeId() + "", msg)
                .build();
        // topic 和 group 按照申请的填写即可. 可以自行注入相关配置，用配置的方式来发送消息
        PubResult result = databusTemplate.pub(topic, group, message);
        if (result.isSuccess()) {
            log.info("creative material push to audit pub msg success, msg={}", JSON.toJSONString(msg));
        } else {
            Throwable throwable = result.getThrowable();
            log.error("creative material push to audit pub msg error ", throwable);
            throw new RuntimeException(throwable.getMessage());
        }

    }
}
