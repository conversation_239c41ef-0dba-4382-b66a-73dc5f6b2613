package com.bilibili.risk.databus.pull_task;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.risk.bo.MaterialAuditTaskPullQueryBo;
import com.bilibili.risk.config.BizConfig;
import com.bilibili.risk.databus.DatabusKeyConstants;
import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.databus.DatabusTemplate;
import com.bilibili.warp.databus.Message;
import com.bilibili.warp.databus.PubResult;
import com.bilibili.warp.spring.boot.autoconfigure.databus.DatabusProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.UUID;

/**
 **/
@Slf4j
@Service
public class MaterialTaskPrePullMsgPub {

    @Resource
    private DatabusTemplate databusTemplate;
    @Autowired
    private BizConfig bizConfig;

    private final String topic;
    private final String group;

    public MaterialTaskPrePullMsgPub(DatabusProperties databusProperties) {
        DatabusProperty databusProperty = databusProperties.getProperties().get(DatabusKeyConstants.CREATIVE_MATERIAL_PRE_PULL);
        this.topic = databusProperty.getTopic();
        this.group = databusProperty.getPub().getGroup();
        log.info("MaterialTaskPrePullMsgPub, topic={}, group={}", topic, group);
    }

    public void pub(MaterialAuditTaskPullQueryBo msg) {

        if (msg == null) {
            return;
        }
        if (!Utils.isPositive(bizConfig.getPrePullSwitch())) {
            return;
        }

        UUID uuid = UUID.randomUUID();
        msg.setUuid(uuid.toString());
        String msgStr = JSON.toJSONString(msg);
        log.info("creative material pre pull pub msg[预拉取下一批任务], msg={}", msgStr);
        Assert.notNull(msg, "auditCreativeMessage参数不能为空");

        // messageKey和value自定义，value会被配置的serializer序列化
        Message message = Message.Builder.of(msg.getQueueId() + "-" + msg.getRuleId(), msg)
//                .withDelayAfter(Duration.ofSeconds(bizConfig.getPrePullDelay()))
                .build();
        // topic 和 group 按照申请的填写即可. 可以自行注入相关配置，用配置的方式来发送消息
        PubResult result = databusTemplate.pub(topic, group, message);
        if (result.isSuccess()) {
            log.info("creative material pre pull pub msg success, msg={}", JSON.toJSONString(msg));
        } else {
            Throwable throwable = result.getThrowable();
            log.error("creative material pre pull pub msg error ", throwable);
            throw new RuntimeException(throwable.getMessage());
        }

    }

}
