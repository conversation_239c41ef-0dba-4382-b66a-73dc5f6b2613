package com.bilibili.risk.databus.timeout_task;

import com.alibaba.fastjson.JSON;
import com.bilibili.risk.bo.msg.MaterialTaskTimeoutMsgBo;
import com.bilibili.risk.databus.DatabusKeyConstants;
import com.bilibili.risk.service.task.MaterialAuditTaskTimeoutService;
import com.bilibili.risk.utils.TraceUtil;
import com.bilibili.warp.databus.AckableMessage;
import com.bilibili.warp.databus.AckableMessages;
import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.databus.MessageListener;
import com.bilibili.warp.spring.boot.autoconfigure.databus.DatabusProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 超时任务处理，接收到的是延时消息
 */
@Slf4j
@Service
public class MaterialTaskTimeoutSub implements MessageListener {

    private final String topic;
    private final String group;

    @Autowired
    private MaterialAuditTaskTimeoutService materialAuditTaskTimeoutService;
    @Autowired
    private TraceUtil traceUtil;

    public MaterialTaskTimeoutSub(DatabusProperties databusProperties) {
        DatabusProperty databusProperty = databusProperties.getProperties().get(DatabusKeyConstants.MATERIAL_TASK_TIMEOUT);
        this.topic = databusProperty.getTopic();
        this.group = databusProperty.getSub().getGroup();
        log.info("MaterialTaskTimeoutSub init, topic={}, group={}", this.topic, this.group);
    }


    @Override
    public String topic() {
        return this.topic;
    }

    @Override
    public String group() {
        return this.group;
    }

    @Override
    public boolean autoCommit() {
        return false;
    }

    @Override
    public void onMessage(AckableMessage ackableMessage) {
        String msg = new String(ackableMessage.payload());
        try {
            traceUtil.spanDataBus(this.getClass().getName(), null, transaction -> {
                log.info("material task timeout wrap onMessage, ackableMessage={}", msg);
                MaterialTaskTimeoutMsgBo msgBo = JSON.parseObject(msg, MaterialTaskTimeoutMsgBo.class);
                materialAuditTaskTimeoutService.processTimeoutTask(msgBo);
                ackableMessage.ack();
            });
        } catch (Exception e) {
            log.error("material task timeout wrap onMessage error, ackableMessage={}", msg, e);
            ackableMessage.retry();
        }
    }

    @Override
    public void onMessages(AckableMessages messages) {
        traceUtil.spanDataBus(this.getClass().getName(), null, transaction -> {
            for (AckableMessage message : messages.getMessages()) {

                try {
                    String msg = new String(message.payload());
                    log.info("material task timeout wrap onMessages, ackableMessage={}", msg);
                    MaterialTaskTimeoutMsgBo msgBo = JSON.parseObject(msg, MaterialTaskTimeoutMsgBo.class);
                    materialAuditTaskTimeoutService.processTimeoutTask(msgBo);
                    message.ack();
                } catch (Exception e) {
                    log.error("msg consume error, msg={}, class={}", message.originMessage(), this.getClass().getSimpleName(), e);
                    message.retry();
                    transaction.setStatus(e);
                }
            }
        });

    }
}
