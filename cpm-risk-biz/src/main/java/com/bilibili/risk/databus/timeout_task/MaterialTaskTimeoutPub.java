package com.bilibili.risk.databus.timeout_task;

import com.alibaba.fastjson.JSON;
import com.bilibili.risk.bo.msg.MaterialTaskTimeoutMsgBo;
import com.bilibili.risk.databus.DatabusKeyConstants;
import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.databus.DatabusTemplate;
import com.bilibili.warp.databus.Message;
import com.bilibili.warp.databus.PubResult;
import com.bilibili.warp.spring.boot.autoconfigure.databus.DatabusProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.time.Duration;

/**
 **/
@Slf4j
@Service
public class MaterialTaskTimeoutPub {

    @Resource
    private DatabusTemplate databusTemplate;

    private final String topic;
    private final String group;

    public MaterialTaskTimeoutPub(DatabusProperties databusProperties) {
        DatabusProperty databusProperty = databusProperties.getProperties().get(DatabusKeyConstants.MATERIAL_TASK_TIMEOUT);
        this.topic = databusProperty.getTopic();
        this.group = databusProperty.getPub().getGroup();
        log.info("MaterialTaskTimeoutPub, topic={}, group={}", topic, group);
    }

    public void pub(MaterialTaskTimeoutMsgBo msg) {

        if (msg == null || StringUtils.isEmpty(msg.getBatchNo())) {
            return;
        }
        String msgStr = JSON.toJSONString(msg);
        log.info("material task timeout pub msg[这批任务超时处理通知], msg={}", msgStr);
        Assert.notNull(msg, "auditCreativeMessage参数不能为空");

        // messageKey和value自定义，value会被配置的serializer序列化
        Message message = Message.Builder.of(msg.getBatchNo(), msg)
                // 延时消息
                .withDelayAfter(Duration.ofMinutes(msg.getTimeoutMins()))
                .build();
        // topic 和 group 按照申请的填写即可. 可以自行注入相关配置，用配置的方式来发送消息
        PubResult result = databusTemplate.pub(topic, group, message);
        if (result.isSuccess()) {
            log.info("material task timeout pub msg success, msg={}", JSON.toJSONString(msg));
        } else {
            Throwable throwable = result.getThrowable();
            log.error("material task timeout pub msg error ", throwable);
            throw new RuntimeException(throwable.getMessage());
        }

    }

}
