package com.bilibili.risk.databus.push_to_audit;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.risk.bo.msg.TaskMaterialSyncMsgBo;
import com.bilibili.risk.databus.DatabusKeyConstants;
import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.databus.DatabusTemplate;
import com.bilibili.warp.databus.Message;
import com.bilibili.warp.databus.PubResult;
import com.bilibili.warp.spring.boot.autoconfigure.databus.DatabusProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;

@Service
@Slf4j
public class TaskMaterialSyncPub {

    @Resource
    private DatabusTemplate databusTemplate;

    private final String topic;
    private final String group;

    public TaskMaterialSyncPub(DatabusProperties databusProperties) {
        DatabusProperty databusProperty = databusProperties.getProperties().get(DatabusKeyConstants.TASK_MATERIAL_SYNC);
        this.topic = databusProperty.getTopic();
        this.group = databusProperty.getPub().getGroup();
        log.info("TaskMatchQueuePub, topic={}, group={}", topic, group);
    }

    public void pub(TaskMaterialSyncMsgBo msg) {

        if (msg == null || !Utils.isPositive(msg.getCreativeId())) {
            return;
        }

        String msgStr = JSON.toJSONString(msg);
        log.info("TaskMaterialSyncPub msg, msg={}", msgStr);
        Assert.notNull(msg, "auditCreativeMessage参数不能为空");

        // messageKey和value自定义，value会被配置的serializer序列化
        Message message = Message.Builder.of(msg.getCreativeId().toString(), msg)
                .build();
        // topic 和 group 按照申请的填写即可. 可以自行注入相关配置，用配置的方式来发送消息
        PubResult result = databusTemplate.pub(topic, group, message);
        if (result.isSuccess()) {
            log.info("TaskMaterialSyncPub pub msg success, msg={}", JSON.toJSONString(msg));
        } else {
            Throwable throwable = result.getThrowable();
            log.error("TaskMaterialSyncPub pub msg error ", throwable);
            throw new RuntimeException(throwable.getMessage());
        }

    }
}
