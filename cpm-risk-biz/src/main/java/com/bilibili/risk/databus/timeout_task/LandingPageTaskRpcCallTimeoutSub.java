package com.bilibili.risk.databus.timeout_task;

import com.alibaba.fastjson.JSON;
import com.bilibili.risk.bo.msg.ExternalMaterialAuditMsgBo;
import com.bilibili.risk.bo.msg.LandingPageTaskRpcCallTimeoutMsgBo;
import com.bilibili.risk.databus.DatabusKeyConstants;
import com.bilibili.risk.service.task.LandingPageTaskRpcCallTimeoutService;
import com.bilibili.risk.service.task.MaterialAuditTaskAuditService;
import com.bilibili.risk.service.task.MaterialAuditTaskTimeoutService;
import com.bilibili.risk.utils.TraceUtil;
import com.bilibili.warp.databus.AckableMessage;
import com.bilibili.warp.databus.AckableMessages;
import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.databus.MessageListener;
import com.bilibili.warp.spring.boot.autoconfigure.databus.DatabusProperties;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 超时任务处理，接收到的是延时消息
 */
@Slf4j
@Service
public class LandingPageTaskRpcCallTimeoutSub implements MessageListener {

    private final String topic;
    private final String group;

    @Autowired
    private MaterialAuditTaskAuditService materialAuditTaskAuditService;
    @Autowired
    private LandingPageTaskRpcCallTimeoutService landingPageTaskRpcCallTimeoutService;
    @Autowired
    private TraceUtil traceUtil;

    public LandingPageTaskRpcCallTimeoutSub(DatabusProperties databusProperties) {
        DatabusProperty databusProperty = databusProperties.getProperties().get(DatabusKeyConstants.LANDING_PAGE_TASK_RPC_CALL_TIMEOUT_KEY);
        this.topic = databusProperty.getTopic();
        this.group = databusProperty.getSub().getGroup();
        log.info("LandingPageTaskRpcCallTimeoutSub init, topic={}, group={}", this.topic, this.group);
    }


    @Override
    public String topic() {
        return this.topic;
    }

    @Override
    public String group() {
        return this.group;
    }

    @Override
    public boolean autoCommit() {
        return false;
    }
/*
    @Override
    public void onMessage(AckableMessage ackableMessage) {

        try {
            traceUtil.spanDataBus(this.getClass().getName(), null, transaction -> {
                String msg = new String(ackableMessage.payload());
                log.info("landing page task rpc call timeout warp onMessage[三方落地页组任务rpc调用超时处理], ackableMessage={}", msg);
                LandingPageTaskRpcCallTimeoutMsgBo msgBo = JSON.parseObject(msg, LandingPageTaskRpcCallTimeoutMsgBo.class);
                try {
                    landingPageTaskRpcCallTimeoutService.processTimeoutCallTasks(msgBo.getTaskIds());
                    ackableMessage.ack();
                } catch (Exception e) {
                    log.error("msg consume error, msg={}, class={}", msg, this.getClass().getSimpleName(), e);
                    ackableMessage.retry();
                    transaction.setStatus(e);
                }
            });
        } catch (Exception e) {
            log.error("landing page task rpc call timeout warp onMessage error, ackableMessage={}", JSON.toJSONString(ackableMessage), e);
            ackableMessage.retry();
        }
    }*/

    @Override
    public void onMessages(AckableMessages messages) {
        try {
            for (AckableMessage message : messages.getMessages()) {
                traceUtil.spanDataBus(this.getClass().getName(), null, transaction -> {
                    String msg = new String(message.payload());
                    log.info("landing page task rpc call timeout warp onMessages[三方落地页组任务rpc调用超时处理], ackableMessage={}", msg);
                    LandingPageTaskRpcCallTimeoutMsgBo msgBo = JSON.parseObject(msg, LandingPageTaskRpcCallTimeoutMsgBo.class);
                    try {
                        // TODO @chenquanqing 上线后taskIds的可以删掉
                        landingPageTaskRpcCallTimeoutService.processTimeoutCallTasks(StringUtils.isNotBlank(msgBo.getTaskId()) ?
                                Lists.newArrayList(msgBo.getTaskId()) : msgBo.getTaskIds());

                        if(StringUtils.isNotBlank(msgBo.getTaskId())) {
                            materialAuditTaskAuditService.sendExternalMaterialAuditMsg(ExternalMaterialAuditMsgBo.builder()
                                    .materialId(msgBo.getMaterialId()).executeName(msgBo.getExecuteName()).triggerTaskId(msgBo.getTaskId()).build());
                        }
                        message.ack();
                    } catch (Exception e) {
                        log.error("msg consume error, msg={}, class={}", msg, this.getClass().getSimpleName(), e);
                        message.retry();
                        transaction.setStatus(e);
                    }
                });
            }
            messages.ack();
        } catch (Exception e) {
            log.error("landing page task rpc call timeout warp onMessages error, ackableMessage={}", JSON.toJSONString(messages), e);
            messages.getMessages().forEach(AckableMessage::retry);
            messages.ack();
        }
    }
}
