package com.bilibili.risk.databus.material_audit;

import com.alibaba.fastjson.JSON;
import com.bilibili.risk.bo.msg.ExternalMaterialAuditMsgBo;
import com.bilibili.risk.databus.DatabusKeyConstants;
import com.bilibili.risk.utils.JacksonUtils;
import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.databus.DatabusTemplate;
import com.bilibili.warp.databus.Message;
import com.bilibili.warp.databus.PubResult;
import com.bilibili.warp.spring.boot.autoconfigure.databus.DatabusProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;

@Slf4j
@Service
public class ExternalMaterialAuditPub {

    @Resource
    private DatabusTemplate databusTemplate;

    private final String topic;
    private final String group;

    public ExternalMaterialAuditPub(DatabusProperties databusProperties) {
        DatabusProperty databusProperty = databusProperties.getProperties().get(DatabusKeyConstants.EXTERNAL_MATERIAL_AUDIT_KEY);
        this.topic = databusProperty.getTopic();
        this.group = databusProperty.getPub().getGroup();
        log.info("ExternalMaterialAuditPub, topic={}, group={}", topic, group);
    }

    public void pub(ExternalMaterialAuditMsgBo msgBo){

        log.info("external material audit pub msg[外部素材审核], msg={}", JacksonUtils.toJson(msgBo));
        Assert.notNull(msgBo, "external material audit msgBo参数不能为空");

        // messageKey和value自定义，value会被配置的serializer序列化
        Message message = Message.Builder.of(msgBo.getMaterialId(), msgBo).build();
        PubResult result = databusTemplate.pub(topic, group, message);
        if (!result.isSuccess()) {
            Throwable throwable = result.getThrowable();
            log.error("external material audit pub msg error[外部素材审核], msg={}", JacksonUtils.toJson(msgBo), throwable);
            throw new RuntimeException(throwable.getMessage());
        }
    }
}
