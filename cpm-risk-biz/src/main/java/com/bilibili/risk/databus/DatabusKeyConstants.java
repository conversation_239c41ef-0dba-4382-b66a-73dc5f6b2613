package com.bilibili.risk.databus;

public interface DatabusKeyConstants {

    // 创意素材推审
    public static final String CREATIVE_MATERIAL_PUSH_TO_AUDIT_KEY = "creative-material-push-to-audit";
    // 非创意渠道，触发创意推审
    String OTHER_SOURCE_FIRE_PUSH_AUDIT = "other-source-fire-push-audit";
    // 任务素材同步
    String TASK_MATERIAL_SYNC = "task-material-sync";

    String RISK_CREATIVE_BINLOG = "risk-creative-binlog";

    // 素材预拉取
    public static final String CREATIVE_MATERIAL_PRE_PULL = "creative-material-pre-push";
    // 素材任务超时
    public static final String MATERIAL_TASK_TIMEOUT = "material-task-timeout";

    // 素材审核
    public static final String MATERIAL_AUDIT_KEY = "material-audit";

    String EXTERNAL_MATERIAL_AUDIT_KEY = "external-material-audit";
    // 创意素材判定处理
    String CREATIVE_MATERIALS_JUDGE_AUDIT_KEY = "creative-materials-judge-audit";
    String MATERIAL_CREATIVE_SCROLL_KEY = "material-creative-scroll";

    // 素材等待队列任务审核
    public static final String MATERIAL_WAIT_QUEUE_TASK_AUDIT_KEY = "material-wait-queue-task-audit";

    // 素材任务表 binlog
    public static final String LAU_MATERIAL_AUDIT_TASK_BINLOG_KEY = "lau-material-audit-task-binlog";
    public static final String LAU_MATERIAL_AUDIT_TASK_BINLOG_BIZ_KEY = "lau-material-audit-task-biz-binlog";
    public static final String LAU_MATERIAL_AUDIT_TASK_BINLOG_QUICK_KEY = "lau-material-audit-task-binlog-quick";
    public static final String LAU_MATERIAL_AUDIT_TASK_BINLOG_SLOW_KEY = "lau-material-audit-task-binlog-slow";


    // 创意 binlog
    public static final String LAU_UNIT_CREATIVE_BINLOG_KEY = "lau-unit-creative-binlog";
    // 组件binlog
    public static final String STORY_BINLOG = "story-binlog";

    // 落地页组binlog
    public static final String MGK_LANDING_PAGE_GROUP_MAPPING_BINLOG_KEY = "mgk-landing-page-group-mapping-binlog";

    // 三方落地页组任务rpc调用超时处理
    public static final String LANDING_PAGE_TASK_RPC_CALL_TIMEOUT_KEY = "landing-page-task-rpc-call-timeout";

    // 素材任务表 es(可以局部更新)
    public static final String LAU_MATERIAL_AUDIT_TASK_ES_KEY = "material-audit-task-es-a";
    // 素材任务表 es
    @Deprecated
    public static final String LAU_MATERIAL_AUDIT_TASK_ES_FOR_DATA_KEY = "material-audit-task-es-b";

    // 素材任务表操作日志
    public static final String OPERATION_LOG_KEY = "sycpb-operation-log-es-a";

}
