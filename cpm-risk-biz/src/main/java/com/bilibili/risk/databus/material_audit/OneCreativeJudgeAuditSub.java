package com.bilibili.risk.databus.material_audit;

import com.alibaba.fastjson.JSON;
import com.bilibili.risk.bo.msg.CreativeJudgeAuditMsgBo;
import com.bilibili.risk.bo.msg.CreativeMaterialJudgeAuditMsgBo;
import com.bilibili.risk.databus.DatabusKeyConstants;
import com.bilibili.risk.service.creative.OneCreativeJudgeAuditService;
import com.bilibili.risk.utils.TraceUtil;
import com.bilibili.warp.databus.AckableMessage;
import com.bilibili.warp.databus.AckableMessages;
import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.databus.MessageListener;
import com.bilibili.warp.spring.boot.autoconfigure.databus.DatabusProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;

@Slf4j
@Service
public class OneCreativeJudgeAuditSub implements MessageListener {

    private final String topic;
    private final String group;

    @Autowired
    private OneCreativeJudgeAuditService oneCreativeJudgeAuditService;
    @Autowired
    private TraceUtil traceUtil;

    public OneCreativeJudgeAuditSub(DatabusProperties databusProperties) {
        DatabusProperty databusProperty = databusProperties.getProperties().get(DatabusKeyConstants.CREATIVE_MATERIALS_JUDGE_AUDIT_KEY);
        this.topic = databusProperty.getTopic();
        this.group = databusProperty.getSub().getGroup();
        log.info("CreativeMaterialJudgeAuditSub init, topic={}, group={}", this.topic, this.group);
    }


    @Override
    public String topic() {
        return this.topic;
    }

    @Override
    public String group() {
        return this.group;
    }

    @Override
    public boolean autoCommit() {
        return false;
    }


    @Override
    public void onMessages(AckableMessages messages) {

        try {
            traceUtil.spanDataBus(this.getClass().getName(), null, transaction -> {
                for (AckableMessage message : messages.getMessages()) {
                    String content = new String(message.payload());
                    log.info("receive retry message onMessages:{}, retryCount={}", content, message.originMessage().getRetryCount());
                    CreativeMaterialJudgeAuditMsgBo judgeAuditMsgBo = JSON.parseObject(content, CreativeMaterialJudgeAuditMsgBo.class);

                    oneCreativeJudgeAuditService.processCreativeMaterials(CreativeJudgeAuditMsgBo.builder().data(Arrays.asList(judgeAuditMsgBo)).build());
                    message.ack();
                }
                messages.ack();
            });
        } catch (Exception e) {
            log.error("CreativeMaterialJudgeAuditSub onMessages error", e);
            messages.getMessages().forEach(AckableMessage::retry);
            messages.ack();
        }
    }

    @Override
    public void onMessage(AckableMessage message) {
        try {
            traceUtil.spanDataBus(this.getClass().getName(), null, transaction -> {
                String content = new String(message.payload());
                log.info("receive retry message onMessage:{}, retryCount={}", content, message.originMessage().getRetryCount());
                CreativeMaterialJudgeAuditMsgBo judgeAuditMsgBo = JSON.parseObject(content, CreativeMaterialJudgeAuditMsgBo.class);
                oneCreativeJudgeAuditService.processCreativeMaterials(CreativeJudgeAuditMsgBo.builder().data(Arrays.asList(judgeAuditMsgBo)).build());
                message.ack();
            });
        } catch (Exception e) {
            log.error("CreativeMaterialJudgeAuditSub onMessage error", e);
            message.retry();
        }
    }
}
