package com.bilibili.risk.databus.timeout_task;

import com.alibaba.fastjson.JSON;
import com.bilibili.risk.bo.msg.LandingPageTaskRpcCallTimeoutMsgBo;
import com.bilibili.risk.databus.DatabusKeyConstants;
import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.databus.DatabusTemplate;
import com.bilibili.warp.databus.Message;
import com.bilibili.warp.databus.PubResult;
import com.bilibili.warp.spring.boot.autoconfigure.databus.DatabusProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.Duration;

/**
 *
 **/
@Slf4j
@Service
public class LandingPageTaskRpcCallTimeoutPub {

    @Resource
    private DatabusTemplate databusTemplate;

    private final String topic;
    private final String group;

    public LandingPageTaskRpcCallTimeoutPub(DatabusProperties databusProperties) {
        DatabusProperty databusProperty = databusProperties.getProperties().get(DatabusKeyConstants.LANDING_PAGE_TASK_RPC_CALL_TIMEOUT_KEY);
        this.topic = databusProperty.getTopic();
        this.group = databusProperty.getPub().getGroup();
        log.info("LandingPageTaskRpcCallTimeoutPub, topic={}, group={}", topic, group);
    }

    public void pub(LandingPageTaskRpcCallTimeoutMsgBo msg) {

        if (msg == null) {
            log.error("landing page task rpc call timeout pub msg is null[三方落地页组任务rpc调用超时处理], msg={}", msg);
            return;
        }

        String msgStr = JSON.toJSONString(msg);
        log.info("landing page task rpc call timeout pub msg[三方落地页组任务rpc调用超时处理], msg={}", msgStr);
        Assert.notNull(msg, "auditCreativeMessage参数不能为空");

        // messageKey和value自定义，value会被配置的serializer序列化
        Message message = Message.Builder.of(msg.getTaskId(), msg)
                // 延时消息5分钟
                .withDelayAfter(Duration.ofMinutes(5))
                .build();
        // topic 和 group 按照申请的填写即可. 可以自行注入相关配置，用配置的方式来发送消息
        PubResult result = databusTemplate.pub(topic, group, message);
        if (result.isSuccess()) {
            log.info("landing page task rpc call timeout pub msg success[三方落地页组任务rpc调用超时处理], msg={}", JSON.toJSONString(msg));
        } else {
            Throwable throwable = result.getThrowable();
            log.error("landing page task rpc call timeout pub msg error ", throwable);
            throw new RuntimeException(throwable.getMessage());
        }
    }

}
