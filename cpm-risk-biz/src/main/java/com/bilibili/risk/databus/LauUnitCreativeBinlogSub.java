package com.bilibili.risk.databus;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bilibili.risk.bo.msg.CreativeBinlogBo;
import com.bilibili.risk.constant.BinlogConstant;
import com.bilibili.risk.service.task.MaterialAuditTaskEsService;
import com.bilibili.risk.utils.TraceUtil;
import com.bilibili.warp.databus.AckableMessage;
import com.bilibili.warp.databus.AckableMessages;
import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.databus.MessageListener;
import com.bilibili.warp.spring.boot.autoconfigure.databus.DatabusProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 创意审核状态同步到 es
 */
@Slf4j
@Service
public class LauUnitCreativeBinlogSub implements MessageListener {

    @Resource
    private MaterialAuditTaskEsService materialAuditTaskEsService;
    @Autowired
    private TraceUtil traceUtil;

    private final String topic;
    private final String group;


    public LauUnitCreativeBinlogSub(DatabusProperties databusProperties) {
        DatabusProperty databusProperty = databusProperties.getProperties().get(DatabusKeyConstants.LAU_UNIT_CREATIVE_BINLOG_KEY);
        this.topic = databusProperty.getTopic();
        this.group = databusProperty.getSub().getGroup();
    }


    private CreativeBinlogBo deserializeMaterialTaskBinlogBo(JSONObject insertObject) {
        if (Objects.isNull(insertObject)) {
            return null;
        }
        CreativeBinlogBo CreativeBinlogBo = null;
        try {
            CreativeBinlogBo = insertObject.toJavaObject(insertObject, CreativeBinlogBo.class);
        } catch (Exception e) {
            log.error("deserializeCreativeBinlogDto error, insertObject:{}", insertObject, e);
        }
        return CreativeBinlogBo;
    }


    @Override
    public String topic() {
        return this.topic;
    }

    @Override
    public String group() {
        return this.group;
    }

    @Override
    public boolean autoCommit() {
        return false;
    }


    @Override
    public void onMessages(AckableMessages messages) {

        List<AckableMessage> msgList = messages.getMessages();

        try {

            traceUtil.spanDataBus(this.getClass().getName(), null, transaction -> {
                List<CreativeBinlogBo> auditTask00Bos = msgList.stream().map(m -> {
                    String value = new String(m.payload());
                    JSONObject msgJsonObj = JSONObject.parseObject(value);
                    String action = msgJsonObj.getString(BinlogConstant.ACTION);
                    // 只处理更新的
                    if (!Objects.equals(action, BinlogConstant.UPDATE)) {
                        return null;
                    }
                    JSONObject oldObject = msgJsonObj.getJSONObject(BinlogConstant.OLD);
                    CreativeBinlogBo oldBo = deserializeMaterialTaskBinlogBo(oldObject);
                    JSONObject newObject = msgJsonObj.getJSONObject(BinlogConstant.NEW);
                    CreativeBinlogBo newBo = deserializeMaterialTaskBinlogBo(newObject);
                    if (Objects.isNull(oldBo) || Objects.isNull(newBo)) {
                        return null;
                    }
                    if (Objects.equals(oldBo.getAuditStatus(), newBo.getAuditStatus())) {
                        return null;
                    }
                    // 要求审核状态变化，才处理
                    return newBo;
                }).filter(Objects::nonNull).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(auditTask00Bos)) {
                    return;
                }

                materialAuditTaskEsService.updateTaskCreativeAuditStatus2Es(auditTask00Bos);
                messages.ack();
            });
        } catch (Exception e) {
            log.error("onMessages error, msgList={}", JSON.toJSONString(msgList), e);
            msgList.forEach(AckableMessage::retry);
            messages.ack();
        }
    }

    @Override
    public void onMessage(AckableMessage message) {
        String value = new String(message.payload());
        try {
            traceUtil.spanDataBus(this.getClass().getName(), null, transaction -> {
                JSONObject msgJsonObj = JSONObject.parseObject(value);
                String action = msgJsonObj.getString(BinlogConstant.ACTION);
                // 只处理更新的
                if (!Objects.equals(action, BinlogConstant.UPDATE)) {
                    return;
                }
                JSONObject oldObject = msgJsonObj.getJSONObject(BinlogConstant.OLD);
                CreativeBinlogBo oldBo = deserializeMaterialTaskBinlogBo(oldObject);
                JSONObject newObject = msgJsonObj.getJSONObject(BinlogConstant.NEW);
                CreativeBinlogBo newBo = deserializeMaterialTaskBinlogBo(newObject);
                if (Objects.isNull(oldBo) || Objects.isNull(newBo)) {
                    return;
                }
                if (Objects.equals(oldBo.getAuditStatus(), newBo.getAuditStatus())) {
                    return;
                }
                // 要求审核状态变化，才处理

                materialAuditTaskEsService.updateTaskCreativeAuditStatus2Es(Arrays.asList(newBo));
                message.ack();
            });
        } catch (Exception e) {
            log.error("onMessage error, msgList={}", value, e);
            message.retry();
        }
    }
}
