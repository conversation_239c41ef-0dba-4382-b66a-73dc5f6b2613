package com.bilibili.risk.databus;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bilibili.risk.biz.quality.task.service.QualityTaskGenService;
import com.bilibili.risk.biz.quality.task.service.RiskMaterialAuditQualityTaskService;
import com.bilibili.risk.bo.LauMaterialAuditTaskBo;
import com.bilibili.risk.constant.BinlogConstant;
import com.bilibili.risk.enums.MaterialTaskStatusEnum;
import com.bilibili.risk.utils.TraceUtil;
import com.bilibili.warp.databus.AckableMessage;
import com.bilibili.warp.databus.AckableMessages;
import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.databus.MessageListener;
import com.bilibili.warp.spring.boot.autoconfigure.databus.DatabusProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * task binlog 其他情况处理的消费者
 */
@Slf4j
@Service
public class LauUnitCreativeBinlogOtherSub implements MessageListener {

    @Autowired
    private TraceUtil traceUtil;
    @Autowired
    private QualityTaskGenService qualityTaskGenService;

    private final String topic;
    private final String group;
    private RiskMaterialAuditQualityTaskService riskMaterialAuditQualityTaskService;


    public LauUnitCreativeBinlogOtherSub(DatabusProperties databusProperties) {
        DatabusProperty databusProperty = databusProperties.getProperties().get(DatabusKeyConstants.LAU_MATERIAL_AUDIT_TASK_BINLOG_OTHER_KEY);
        this.topic = databusProperty.getTopic();
        this.group = databusProperty.getSub().getGroup();
    }


    private LauMaterialAuditTaskBo deserializeMaterialTaskBinlogBo(JSONObject insertObject) {
        if (Objects.isNull(insertObject)) {
            return null;
        }
        LauMaterialAuditTaskBo LauMaterialAuditTaskBo = null;
        try {
            LauMaterialAuditTaskBo = insertObject.toJavaObject(insertObject, LauMaterialAuditTaskBo.class);
        } catch (Exception e) {
            log.error("deserializeCreativeBinlogDto error, insertObject:{}", insertObject, e);
        }
        return LauMaterialAuditTaskBo;
    }


    @Override
    public String topic() {
        return this.topic;
    }

    @Override
    public String group() {
        return this.group;
    }

    @Override
    public boolean autoCommit() {
        return false;
    }


    @Override
    public void onMessages(AckableMessages messages) {

        List<AckableMessage> msgList = messages.getMessages();

        try {

            traceUtil.spanDataBus(this.getClass().getName(), null, transaction -> {

                List<LauMaterialAuditTaskBo> auditTaskBos = new ArrayList<>();
                for (AckableMessage ackableMessage : msgList) {
                    String value = new String(ackableMessage.payload());
                    JSONObject msgJsonObj = JSONObject.parseObject(value);
                    String action = msgJsonObj.getString(BinlogConstant.ACTION);
                    JSONObject oldObject = msgJsonObj.getJSONObject(BinlogConstant.OLD);
                    LauMaterialAuditTaskBo oldBo = deserializeMaterialTaskBinlogBo(oldObject);
                    JSONObject newObject = msgJsonObj.getJSONObject(BinlogConstant.NEW);
                    LauMaterialAuditTaskBo newBo = deserializeMaterialTaskBinlogBo(newObject);

                    // 变成完成
                    if (Objects.equals(action, BinlogConstant.UPDATE)
                            && oldBo != null && newBo != null
                            && !Objects.equals(oldBo.getStatus(), MaterialTaskStatusEnum.COMPLETE.getCode())
                            && Objects.equals(newBo.getStatus(), MaterialTaskStatusEnum.COMPLETE.getCode())) {
                        auditTaskBos.add(newBo);
                    }
                }

                if (CollectionUtils.isEmpty(auditTaskBos)) {
                    messages.ack();
                    return;
                }

                qualityTaskGenService.genTask(auditTaskBos, 0);
                messages.ack();
            });
        } catch (Exception e) {
            log.error("onMessages error, msgList={}", JSON.toJSONString(msgList), e);
            msgList.forEach(AckableMessage::retry);
            messages.ack();
        }
    }

    @Override
    public void onMessage(AckableMessage message) {
        String value = new String(message.payload());
        try {
            traceUtil.spanDataBus(this.getClass().getName(), null, transaction -> {
                JSONObject msgJsonObj = JSONObject.parseObject(value);
                String action = msgJsonObj.getString(BinlogConstant.ACTION);
                JSONObject oldObject = msgJsonObj.getJSONObject(BinlogConstant.OLD);
                LauMaterialAuditTaskBo oldBo = deserializeMaterialTaskBinlogBo(oldObject);
                JSONObject newObject = msgJsonObj.getJSONObject(BinlogConstant.NEW);
                LauMaterialAuditTaskBo newBo = deserializeMaterialTaskBinlogBo(newObject);

                // 变成完成
                if (Objects.equals(action, BinlogConstant.UPDATE)
                        && oldBo != null && newBo != null
                        && !Objects.equals(oldBo.getStatus(), MaterialTaskStatusEnum.COMPLETE.getCode())
                        && Objects.equals(newBo.getStatus(), MaterialTaskStatusEnum.COMPLETE.getCode())) {

                    qualityTaskGenService.genTask(Arrays.asList(newBo), 0);
                }

                message.ack();
            });
        } catch (Exception e) {
            log.error("onMessage error, msgList={}", value, e);
            message.retry();
        }
    }
}
