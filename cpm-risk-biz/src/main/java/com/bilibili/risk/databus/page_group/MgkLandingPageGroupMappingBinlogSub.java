package com.bilibili.risk.databus.page_group;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.risk.bo.LandingPageGroupBo;
import com.bilibili.risk.bo.LandingPageGroupMappingBo;
import com.bilibili.risk.bo.msg.LauCreativeComponentBinlogBo;
import com.bilibili.risk.bo.msg.LauCreativeGroupBinlogBo;
import com.bilibili.risk.bo.msg.MgkLandingPageGroupMappingBinlogBo;
import com.bilibili.risk.bo.msg.OtherSourceFirePushAuditMsgBo;
import com.bilibili.risk.constant.BinlogConstant;
import com.bilibili.risk.databus.DatabusKeyConstants;
import com.bilibili.risk.enums.FirePushAuditSourceEnum;
import com.bilibili.risk.service.material.CreativeMaterialPushToAuditService;
import com.bilibili.risk.service.page_group.PageGroupAuditService;
import com.bilibili.risk.service.page_group.PageGroupMaterialSyncService;
import com.bilibili.risk.service.page_group.PageGroupService;
import com.bilibili.risk.utils.CatUtils;
import com.bilibili.risk.utils.TraceUtil;
import com.bilibili.warp.databus.AckableMessage;
import com.bilibili.warp.databus.AckableMessages;
import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.databus.MessageListener;
import com.bilibili.warp.spring.boot.autoconfigure.databus.DatabusProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.common.util.set.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
public class MgkLandingPageGroupMappingBinlogSub implements MessageListener {

    @Autowired
    private TraceUtil traceUtil;
    @Autowired
    private PageGroupMaterialSyncService pageGroupMaterialSyncService;
    @Autowired
    private PageGroupAuditService pageGroupAuditService;

    @Autowired
    private CreativeMaterialPushToAuditService creativeMaterialPushToAuditService;

    private static final String MAPPING = "mgk_landing_page_group_mapping";
    private static final String CREATIVE_GROUP = "lau_creative_landing_page_group";

    private final String topic;
    private final String group;


    public MgkLandingPageGroupMappingBinlogSub(DatabusProperties databusProperties) {
        DatabusProperty databusProperty = databusProperties.getProperties().get(DatabusKeyConstants.MGK_LANDING_PAGE_GROUP_MAPPING_BINLOG_KEY);
        this.topic = databusProperty.getTopic();
        this.group = databusProperty.getSub().getGroup();
        log.info("MgkLandingPageGroupMappingBinlogSub topic:{}, group:{}", this.topic, this.group);
    }


    private MgkLandingPageGroupMappingBinlogBo deserializeMBinlogBo(JSONObject insertObject) {
        if (Objects.isNull(insertObject)) {
            return null;
        }
        MgkLandingPageGroupMappingBinlogBo binlogBo = null;
        try {
            binlogBo = insertObject.toJavaObject(insertObject, MgkLandingPageGroupMappingBinlogBo.class);
        } catch (Exception e) {
            log.error("deserializeMBinlogBo error, insertObject:{}", insertObject, e);
        }
        return binlogBo;
    }


    @Override
    public String topic() {
        return this.topic;
    }

    @Override
    public String group() {
        return this.group;
    }

    @Override
    public boolean autoCommit() {
        return false;
    }


    @Override
    public void onMessage(AckableMessage message) {

        try {
            traceUtil.spanDataBus(this.getClass().getName(), null, transaction -> {
                try {
                    log.info("onMessage[落地页组mapping], msg={}", message.originMessage());
                    String value = new String(message.payload());
                    JSONObject msgJsonObj = JSONObject.parseObject(value);
                    String table = msgJsonObj.getString(BinlogConstant.TABLE);

                    if(StringUtils.isBlank(table)){
                        log.error("table is null, msgJsonObj:{}", msgJsonObj);
                        message.ack();
                        return;
                    }

                    switch (table) {
                        case MAPPING:
                            // 落地页组page素材的变动，需要重新送审
                            processPageGroupMappingMaterialsChange(message);

                            // 落地页组page审核状态变更
                            processPageGroupMappingAuditStatusChange(message);
                            break;
                        case CREATIVE_GROUP:
                            // 目前应该不需要加，删除时，应该创意会推审
                            syncCreativeGroup(message);
                            break;
                        default:
                            log.error("unknown table, msgJsonObj:{}", msgJsonObj);
                            break;
                    }

                    message.ack();
                } catch (Exception e) {
                    log.error("msg consume error, msg={}, class={}", message.originMessage(), this.getClass().getSimpleName(), e);
                    message.retry();
                    transaction.setStatus(e);
                }
            });
        } catch (Exception e) {
            log.error("onMessage error, msgList={}", JSON.toJSONString(message), e);
            message.retry();
        }
    }

    private void processPageGroupMappingAuditStatusChange(AckableMessage message) throws Exception {
        String value = new String(message.payload());
        JSONObject msgJsonObj = JSONObject.parseObject(value);
        String action = msgJsonObj.getString(BinlogConstant.ACTION);

        // 修改的
        if (!Objects.equals(action, BinlogConstant.UPDATE)) {
            return;
        }
        JSONObject oldObject = msgJsonObj.getJSONObject(BinlogConstant.OLD);
        MgkLandingPageGroupMappingBinlogBo oldBo = deserializeMBinlogBo(oldObject);
        JSONObject newObject = msgJsonObj.getJSONObject(BinlogConstant.NEW);
        MgkLandingPageGroupMappingBinlogBo newBo = deserializeMBinlogBo(newObject);
        if (Objects.isNull(newBo) || Objects.isNull(oldBo)) {
            return;
        }

        // 要求审核状态变更
        if (Objects.equals(newBo.getMappingStatus(), oldBo.getMappingStatus())) {
            return;
        }

        pageGroupAuditService.auditLandingPageGroupMaterial(newBo.getPageId(), newBo.getGroupId());
    }

    private void processPageGroupMappingMaterialsChange(AckableMessage msg) throws Exception{
        String value = new String(msg.payload());
        JSONObject msgJsonObj = JSONObject.parseObject(value);
        String action = msgJsonObj.getString(BinlogConstant.ACTION);

        // 只处理新增需要重新送审，删除的也需要对应删除素材关系
        if (!Objects.equals(action, BinlogConstant.INSERT) && !Objects.equals(action, BinlogConstant.DELETE)) {
            return;
        }
        JSONObject oldObject = msgJsonObj.getJSONObject(BinlogConstant.OLD);
        MgkLandingPageGroupMappingBinlogBo oldBo = deserializeMBinlogBo(oldObject);
        JSONObject newObject = msgJsonObj.getJSONObject(BinlogConstant.NEW);
        MgkLandingPageGroupMappingBinlogBo newBo = deserializeMBinlogBo(newObject);

        Long pageGroupId = null;
        if (!Objects.isNull(newBo)) {
            pageGroupId = newBo.getGroupId();
        }
        if (!Objects.isNull(oldBo)) {
            pageGroupId = Optional.ofNullable(oldBo.getGroupId()).orElse(pageGroupId);
        }

        pageGroupMaterialSyncService.relatedCreativeSplitMaterialsPushToAudit(pageGroupId);
    }

    private void syncCreativeGroup(AckableMessage msg) {
        String value = new String(msg.payload());
        JSONObject msgJsonObj = JSONObject.parseObject(value);
        String action = msgJsonObj.getString(BinlogConstant.ACTION);

        // 只处理删除的
        if (Objects.equals(action, BinlogConstant.INSERT)) {
            return;
        }

        LauCreativeGroupBinlogBo newBo = deserializeCreativeMBinlogBo(msgJsonObj.getJSONObject(BinlogConstant.NEW));
        LauCreativeGroupBinlogBo oldBo = deserializeCreativeMBinlogBo(msgJsonObj.getJSONObject(BinlogConstant.OLD));

        // 物理删的
        if(Objects.equals(action, BinlogConstant.DELETE)) {
            OtherSourceFirePushAuditMsgBo msgBo = OtherSourceFirePushAuditMsgBo.builder().source(FirePushAuditSourceEnum.PAGE_GROUP.getCode())
                    .pageGroupId(newBo.getGroupId()).build();
            creativeMaterialPushToAuditService.firePushAuditIfNeed(Sets.newHashSet(newBo.getCreativeId()), msgBo);
        }

        // 逻辑删的
        if(Objects.equals(action, BinlogConstant.UPDATE) && Objects.equals(newBo.getIsDeleted(), IsDeleted.DELETED.getCode())
                && Objects.equals(oldBo.getIsDeleted(), IsDeleted.VALID.getCode())) {
            OtherSourceFirePushAuditMsgBo msgBo = OtherSourceFirePushAuditMsgBo.builder().source(FirePushAuditSourceEnum.PAGE_GROUP.getCode())
                    .pageGroupId(newBo.getGroupId()).build();
            creativeMaterialPushToAuditService.firePushAuditIfNeed(Sets.newHashSet(newBo.getCreativeId()), msgBo);
        }
    }

    private LauCreativeGroupBinlogBo deserializeCreativeMBinlogBo(JSONObject insertObject) {
        if (Objects.isNull(insertObject)) {
            return null;
        }
        LauCreativeGroupBinlogBo binlogBo = null;
        try {
            binlogBo = insertObject.toJavaObject(insertObject, LauCreativeGroupBinlogBo.class);
        } catch (Exception e) {
            log.error("deserializeCreativeMBinlogBo error, insertObject:{}", insertObject, e);
        }
        return binlogBo;
    }
}
