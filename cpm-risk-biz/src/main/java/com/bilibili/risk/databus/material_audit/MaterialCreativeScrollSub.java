package com.bilibili.risk.databus.material_audit;

import com.bilibili.risk.service.material.MaterialAuditToJudgeAllCreativesService;
import com.fasterxml.jackson.core.type.TypeReference;
import com.bilibili.risk.bo.msg.MaterialCreativeScrollMsgBo;
import com.bilibili.risk.databus.DatabusKeyConstants;
import com.bilibili.risk.utils.JacksonUtils;
import com.bilibili.risk.utils.TraceUtil;
import com.bilibili.warp.databus.AckableMessage;
import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.databus.MessageListener;
import com.bilibili.warp.spring.boot.autoconfigure.databus.DatabusProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class MaterialCreativeScrollSub implements MessageListener {

    private final String topic;
    private final String group;

    @Autowired
    private TraceUtil traceUtil;

    @Autowired
    private MaterialAuditToJudgeAllCreativesService materialAuditToJudgeAllCreativesService;

    public MaterialCreativeScrollSub(DatabusProperties databusProperties) {
        DatabusProperty databusProperty = databusProperties.getProperties().get(DatabusKeyConstants.MATERIAL_CREATIVE_SCROLL_KEY);
        this.topic = databusProperty.getTopic();
        this.group = databusProperty.getSub().getGroup();
        log.info("MaterialCreativeScrollSub init, topic={}, group={}", this.topic, this.group);
    }

    @Override
    public String topic() {
        return this.topic;
    }

    @Override
    public String group() {
        return this.group;
    }

    @Override
    public boolean autoCommit() {
        return false;
    }

    @Override
    public void onMessage(AckableMessage message) {

        traceUtil.spanDataBus(this.getClass().getName(), null, transaction -> {
            String content = new String(message.payload());

            try {
                MaterialCreativeScrollMsgBo msg = JacksonUtils.parse(content,  new TypeReference<MaterialCreativeScrollMsgBo>(){});

                materialAuditToJudgeAllCreativesService.scrollCreative(msg);
            } catch (Exception e) {
                log.error("msg consume error, msg={}, class={}", message.originMessage(), this.getClass().getSimpleName(), e);
                message.retry();
            }

            message.ack();
        });

    }

}
