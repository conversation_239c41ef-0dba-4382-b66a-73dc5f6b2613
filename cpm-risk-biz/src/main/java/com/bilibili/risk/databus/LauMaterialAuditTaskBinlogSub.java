package com.bilibili.risk.databus;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bilibili.risk.bo.LauMaterialAuditTaskBo;
import com.bilibili.risk.constant.BinlogConstant;
import com.bilibili.risk.enums.MaterialTaskStatusEnum;
import com.bilibili.risk.enums.ShardingTableEnum;
import com.bilibili.risk.service.task.MaterialAuditTaskEsService;
import com.bilibili.risk.utils.CatUtils;
import com.bilibili.risk.utils.JacksonUtils;
import com.bilibili.risk.utils.TraceUtil;
import com.bilibili.warp.databus.*;
import com.bilibili.warp.spring.boot.autoconfigure.databus.DatabusProperties;
import com.dianping.cat.CatConstants;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class LauMaterialAuditTaskBinlogSub implements MessageListener {

    @Autowired
    private TraceUtil traceUtil;
    @Resource
    private LauMaterialAuditTaskBinlogQuickPub lauMaterialAuditTaskBinlogQuickPub;
    @Resource
    private LauMaterialAuditTaskBinlogSlowPub lauMaterialAuditTaskBinlogSlowPub;

    private final String topic;
    private final String group;


    public LauMaterialAuditTaskBinlogSub(DatabusProperties databusProperties) {
        DatabusProperty databusProperty = databusProperties.getProperties().get(DatabusKeyConstants.LAU_MATERIAL_AUDIT_TASK_BINLOG_KEY);
        this.topic = databusProperty.getTopic();
        this.group = databusProperty.getSub().getGroup();
    }


    private LauMaterialAuditTaskBo deserializeMaterialTaskBinlogBo(JSONObject insertObject) {
        if (insertObject == null) {
            return null;
        }

        LauMaterialAuditTaskBo lauMaterialAuditTaskBo = JSONObject.parseObject(insertObject.toJSONString(), LauMaterialAuditTaskBo.class);
        return lauMaterialAuditTaskBo;
    }


    @Override
    public String topic() {
        return this.topic;
    }

    @Override
    public String group() {
        return this.group;
    }

    @Override
    public boolean autoCommit() {
        return false;
    }


    @Override
    public void onMessages(AckableMessages messages) {
        List<AckableMessage> msgList = messages.getMessages();

        try {
            traceUtil.spanDataBus(this.getClass().getName(), null, transaction -> {
                List<LauMaterialAuditTaskBo> list = msgList.stream().map(m -> {
                            String value = new String(m.payload());
                            JSONObject msgJsonObj = JSONObject.parseObject(value);
                            String action = msgJsonObj.getString(BinlogConstant.ACTION);
                            if (StringUtils.isEmpty(action)) {
                                return null;
                            }
                            JSONObject oldObject = msgJsonObj.getJSONObject(BinlogConstant.OLD);
                            JSONObject newObject = msgJsonObj.getJSONObject(BinlogConstant.NEW);
                            LauMaterialAuditTaskBo oldBo = deserializeMaterialTaskBinlogBo(oldObject);
                            LauMaterialAuditTaskBo newBo = deserializeMaterialTaskBinlogBo(newObject);
                            if (newBo == null) {
                                return null;
                            }
                            if (null != newBo) {
                                log.info("audit task binlog onMessages, taskId={}, delay={}", newBo.getTaskId(), (null == newBo.getMtime() ? 0 : System.currentTimeMillis() - newBo.getMtime().getTime()));
                            }

                            if (isQuickGroup(action, oldBo, newBo)) {
                                lauMaterialAuditTaskBinlogQuickPub.pub(newBo.getTaskId(), msgJsonObj);
                            } else {
                                lauMaterialAuditTaskBinlogSlowPub.pub(newBo.getTaskId(), msgJsonObj);
                            }
                            return newBo;
                        }).filter(e -> null != e)
                        .collect(Collectors.toList());
                messages.ack();
            });
        } catch (Exception e) {
            log.error("audit task binlog onMessages error, msgList={}", JSON.toJSONString(msgList), e);
            msgList.forEach(AckableMessage::retry);
            messages.ack();
        }
    }

    @Override
    public void onMessage(AckableMessage message) {
        String value = new String(message.payload());

        try {
            traceUtil.spanDataBus(this.getClass().getName(), null, transaction -> {
                JSONObject msgJsonObj = JSONObject.parseObject(value);
                String action = msgJsonObj.getString(BinlogConstant.ACTION);
                if (StringUtils.isEmpty(action)) {
                    return;
                }
                JSONObject oldObject = msgJsonObj.getJSONObject(BinlogConstant.OLD);
                JSONObject newObject = msgJsonObj.getJSONObject(BinlogConstant.NEW);
                LauMaterialAuditTaskBo oldBo = deserializeMaterialTaskBinlogBo(oldObject);
                LauMaterialAuditTaskBo newBo = deserializeMaterialTaskBinlogBo(newObject);
                if(null != newBo){
                    log.info("audit task binlog onMessage, taskId={}, delay={}", newBo.getTaskId(), (null == newBo.getMtime() ? 0 : System.currentTimeMillis() - newBo.getMtime().getTime()));
                }

                if (newBo == null) {
                    message.ack();
                    return;
                }

                try {
                    if (isQuickGroup(action, oldBo, newBo)) {
                        lauMaterialAuditTaskBinlogQuickPub.pub(newBo.getTaskId(), msgJsonObj);
                    } else {
                        lauMaterialAuditTaskBinlogSlowPub.pub(newBo.getTaskId(), msgJsonObj);
                    }
                    message.ack();
                } catch (Exception e) {
                    log.error("msg consume error, msgList={}, class={}", value, this.getClass().getSimpleName(), e);
                    message.retry();
                    transaction.setStatus(e);
                }
            });
        } catch (Exception e) {
            log.error("audit task binlog onMessage error, msgList={}", value, e);
            message.retry();
        }
    }

    /**
     * 是否快的方式：拉取和人工审核，都是修改的类型
     * 1.拉取会改领取人，从无到有，状态变成doing
     * 2. 人工审核关心改状态的，从doing变成done
     * @param action
     * @param oldBo
     * @param newBo
     * @return
     */
    private static boolean isQuickGroup(String action, LauMaterialAuditTaskBo oldBo, LauMaterialAuditTaskBo newBo) {

        if (!Objects.equals(action, BinlogConstant.UPDATE)) {
            return false;
        }
        if (oldBo == null || newBo == null) {
            return false;
        }

        // 是否拉取
        if (Objects.equals(oldBo.getStatus(), MaterialTaskStatusEnum.FREE) &&
                Objects.equals(newBo.getStatus(), MaterialTaskStatusEnum.DOING)) {
            return true;
        }
        // 是否人工审核
        if (Objects.equals(oldBo.getStatus(), MaterialTaskStatusEnum.DOING) &&
                Objects.equals(newBo.getStatus(), MaterialTaskStatusEnum.COMPLETE)) {
            return true;
        }
        return false;
    }
}
