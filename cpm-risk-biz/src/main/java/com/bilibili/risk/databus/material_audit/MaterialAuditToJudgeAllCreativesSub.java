package com.bilibili.risk.databus.material_audit;

import com.alibaba.fastjson.JSON;
import com.bilibili.risk.bo.msg.ExternalMaterialAuditMsgBo;
import com.bilibili.risk.bo.msg.MaterialAuditMsgBo;
import com.bilibili.risk.databus.DatabusKeyConstants;
import com.bilibili.risk.service.material.MaterialAuditToJudgeAllCreativesService;
import com.bilibili.risk.service.task.MaterialAuditTaskAuditService;
import com.bilibili.risk.utils.TraceUtil;
import com.bilibili.warp.databus.AckableMessage;
import com.bilibili.warp.databus.AckableMessages;
import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.databus.MessageListener;
import com.bilibili.warp.spring.boot.autoconfigure.databus.DatabusProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;

@Slf4j
@Service
public class MaterialAuditToJudgeAllCreativesSub implements MessageListener {

    private final String topic;
    private final String group;

    @Autowired
    private MaterialAuditToJudgeAllCreativesService materialAuditToJudgeAllCreativesService;
    @Autowired
    private MaterialAuditTaskAuditService materialAuditTaskAuditService;
    @Autowired
    private TraceUtil traceUtil;

    public MaterialAuditToJudgeAllCreativesSub(DatabusProperties databusProperties) {
        DatabusProperty databusProperty = databusProperties.getProperties().get(DatabusKeyConstants.MATERIAL_AUDIT_KEY);
        this.topic = databusProperty.getTopic();
        this.group = databusProperty.getSub().getGroup();
        log.info("MaterialAuditSub init, topic={}, group={}", this.topic, this.group);
    }

    @Override
    public String topic() {
        return this.topic;
    }

    @Override
    public String group() {
        return this.group;
    }

    @Override
    public boolean autoCommit() {
        return false;
    }


    @Override
    public void onMessages(AckableMessages messages) {

        try {
            traceUtil.spanDataBus(this.getClass().getName(), null, transaction -> {
                for (AckableMessage message : messages.getMessages()) {
                    String content = new String(message.payload());
                    log.info("receive retry message onMessages:{}, retryCount={}", content, message.originMessage().getRetryCount());

                    MaterialAuditMsgBo msgBo = JSON.parseObject(content, MaterialAuditMsgBo.class);
                    materialAuditToJudgeAllCreativesService.processMaterialCreative(msgBo);

                    if(StringUtils.isNotBlank(msgBo.getAuditLabelThirdId())){
                        materialAuditTaskAuditService.sendExternalMaterialAuditMsg(ExternalMaterialAuditMsgBo.builder()
                                .auditLabelThirdId(Arrays.asList(msgBo.getAuditLabelThirdId().split(","))).reason(msgBo.getReason())
                                .materialId(msgBo.getMaterialId()).executeName(msgBo.getExecuteName()).triggerTaskId(msgBo.getTriggerTaskId()).build());
                    }

                    message.ack();
                }
                messages.ack();
            });
        } catch (Exception e) {
            messages.getMessages().forEach(AckableMessage::retry);
            messages.ack();
        }
    }

    @Override
    public void onMessage(AckableMessage message) {
        try {
            traceUtil.spanDataBus(this.getClass().getName(), null, transaction -> {
                String content = new String(message.payload());
                log.info("receive retry message onMessage:{}, retryCount={}", content, message.originMessage().getRetryCount());

                MaterialAuditMsgBo msgBo = JSON.parseObject(content, MaterialAuditMsgBo.class);
                materialAuditToJudgeAllCreativesService.processMaterialCreative(msgBo);

                if(StringUtils.isNotBlank(msgBo.getAuditLabelThirdId())){
                    materialAuditTaskAuditService.sendExternalMaterialAuditMsg(ExternalMaterialAuditMsgBo.builder()
                            .auditLabelThirdId(Arrays.asList(msgBo.getAuditLabelThirdId().split(","))).reason(msgBo.getReason())
                            .materialId(msgBo.getMaterialId()).executeName(msgBo.getExecuteName()).triggerTaskId(msgBo.getTriggerTaskId()).build());
                }

                message.ack();
            });
        } catch (Exception e) {
            message.retry();
        }
    }
}
