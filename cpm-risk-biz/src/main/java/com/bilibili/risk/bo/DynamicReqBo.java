package com.bilibili.risk.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName DynamicReqBo
 * <AUTHOR>
 * @Date 2024/4/18 7:46 下午
 * @Version 1.0
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DynamicReqBo {

    @Data
    public static class RevBo {
        // 动态类型
        private Integer type;
        // 业务方资源id
        private Long rid;
    }

    private List<Long> dyn_ids;
    private List<RevBo> revs;

    private Boolean need_shadow_info;
}
