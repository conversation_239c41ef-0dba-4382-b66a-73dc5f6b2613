package com.bilibili.risk.bo;

import com.bilibili.risk.po.risk.LauMaterialAuditTaskPo;
import com.bilibili.risk.metrics_report.metrics.MetricDataHolder;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreativeJudgeContext {

    private Integer creativeId;

    // 触发创意判定的素材id
    private String triggerMaterialId;
    private String executeName;

    // =================================================
    private CreativeBo creativeBo;
    // 程序化 details(影子替换之后的)
    private List<LauProgrammaticCreativeDetailBo> creativeDetailBos = new ArrayList<>();
    private ShadowCreativeBo shadowCreativeBo;

    // 该创意的素材任务列表(排除了 is_deleted=1 的素材任务)
    private List<LauMaterialAuditTaskPo> auditTaskPos = new ArrayList<>();
//    // 触发创意判定的素材的任务，可能没有
//    private LauMaterialAuditTaskPo triggerMaterialAuditTaskPo;
//    // 触发创意判定的素材，可能没有
//    private LauMaterialAuditBo triggerMaterialAuditBo;

    // 该创意的素材任务map, key: md5-type
    private Map<String, LauMaterialAuditTaskPo> materialAuditTaskPoMapByMd5 = new HashMap<>();

    // 标签map
    private Map<Long, LauMaterialAuditLabelBo> auditLabelBoMap = new HashMap<>();
    // 素材的打上的标签
    private Map<String, List<LauMaterialAuditLabelBo>> materialThreeAuditLabelMapByTaskId = new HashMap<>();

    // 主要是针对等待队列的任务审核的时候记录操作日志
    private MaterialAuditTaskOperationLogBo operationLogBo;

//    private Set<Integer> materialHandleTypes = new HashSet<>();

    // 创意层级处理类型
    private Integer creativeMaterialHandleType;

    // 是否存在未审核的素材，标记创意是否需要审核
    private boolean existMaterialNotAuditCreative = false;
    // 创意驳回理由拼接
    private String creativeReason;

    private MetricDataHolder metricDataHolder;

    // 存在程序化创意素材未审核
    private boolean existMaterialNotAuditProgram = false;
    private List<MaterialBo> materialBosProgram;
    private MiscElemBo miscElemBoProgram;

}
