package com.bilibili.risk.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.elasticsearch.search.sort.SortOrder;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MaterialAuditTaskCommonQueryBo {

    private Integer page = 1;
    private Integer pageSize = 20;

    private String sortField = "mtime";
    private SortOrder sortOrder = SortOrder.DESC;

    private List<String> ids;
    private List<String> materialIds;
    private List<String> materialMd5s;
    private List<Integer> materialTypes;

    private List<String> avids;
    private List<String> bvids;
    private List<String> dynamicIds;
    private List<String> roomIds;
    private String pageGroupId;

    private String materialContent;
    private String textLike;

    // material content字段前缀匹配
    private String materialContentPrefixMatch;

    /**
     * 领取批次号
     */
    private String receiveBatchNo;

    private List<Integer> creativeIds;

    /**
     * 业务类型1三连2必火
     */
    private List<Integer> bizTypes;

    /**
     * 队列id
     */
    private List<Long> queueIds;
    private List<Long> excludeQueueIds;
    private List<String> excludeTaskIds;
    private List<String> excludeExecuteNames;

    /**
     * 处置类型
     */
    private List<Integer> handleTypes;

    /**
     * 创意审核状态:1待审2通过3驳回
     */
    private List<Integer> creativeAuditStatusList;

    /**
     * 任务状态:0游离1执行中2完成3stop
     */
    private List<Integer> statusList;

    /**
     * 账户id
     */
    private List<Integer> accountIds;

    /**
     * 领取人
     */
    private List<String> acceptNames;
    /**
     * 执行人
     */
    private List<String> executeNames;

    private List<Boolean> isUserSpaceArchiveList;

    /**
     * 审核三级标签ids
     */
    private List<Long> auditLabelThirdIds;

    /**
     * 统一一级行业标签
     */
    private List<Integer> unitedFirstIndustryIds;

    /**
     * 统一二级行业标签
     */
    private List<Integer> unitedSecondIndustryIds;

    /**
     * 统一三级行业标签
     */
    private List<Integer> unitedThirdIndustryIds;
    private List<Integer> isDeleted;

    private Long executeTimeStart;
    private Long executeTimeEnd;
    private Long acceptTimeStart;
    private Long acceptTimeEnd;
    private Long ctimeStart;
    private Long ctimeEnd;

    private Long mtimeStart;
    private Long mtimeEnd;

    // ==============================
    // 是否需要最早的待审时间
    private boolean withOldestAuditTime;

    /**
     * 审核队列，按规则多队列拉取，需要数量
     */
    private Map<Long, LauMaterialAuditQueueBo> auditQueueBoMap;

    // 字段存在
    private Set<String> fieldExists;
    // 字段不存在
    private Set<String> fieldNotExists;
}