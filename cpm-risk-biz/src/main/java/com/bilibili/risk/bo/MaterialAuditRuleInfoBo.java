package com.bilibili.risk.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MaterialAuditRuleInfoBo implements Serializable {
    /**
     * 自增主键ID
     */
    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 拉取方式1逐个队列领取2时间正序领取
     */
    private Integer pullType;

    /**
     * 顺序
     */
    private Integer seq;

    /**
     * 角色列表
     */
    private List<Integer> rolesIds;
    private List<RoleBo> roleBos;
    private String roleNames;
    private List<LauMaterialAuditQueueBo> bindQueues;

    /**
     * 备注
     */
    private String remark;

    /**
     * 扩展字段
     */
    private String extra;

    /**
     * 是否删除
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    private static final long serialVersionUID = 1L;
}