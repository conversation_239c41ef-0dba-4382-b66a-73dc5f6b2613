package com.bilibili.risk.bo;

import com.bapis.ad.archive.CmArchiveMode;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CmArchiveBo implements Serializable {
    private static final long serialVersionUID = 1L;

    private long avid;

    /**
     *     public static final int CM_SPACE = 0;
     *     public static final int USER_SPACE = 1;
     *     public static final int BRAND_OGV = 2;
     */
    private Integer archiveMode;

    public static Integer isUserSpaceArchive(CmArchiveBo cmArchiveBo) {
        if (cmArchiveBo == null) {
            return 0;
        }
        return cmArchiveBo.getArchiveMode() == CmArchiveMode.USER_SPACE_VALUE ? 1 : 0;
    }
}
