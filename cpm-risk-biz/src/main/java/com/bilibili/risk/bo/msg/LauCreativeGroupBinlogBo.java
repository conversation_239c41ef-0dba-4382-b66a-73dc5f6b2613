package com.bilibili.risk.bo.msg;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class LauCreativeGroupBinlogBo {

    @JSONField(name = "creative_id")
    private Integer creativeId;

    @JSONField(name = "group_id")
    private Long groupId;

    @JSONField(name = "is_deleted")
    private Integer isDeleted;
}
