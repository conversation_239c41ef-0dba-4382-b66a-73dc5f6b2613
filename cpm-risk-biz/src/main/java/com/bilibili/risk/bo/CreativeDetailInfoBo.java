package com.bilibili.risk.bo;

import com.bilibili.risk.bo.msg.ComponentMsgBo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreativeDetailInfoBo {

    private Integer creativeId;
    private Integer adpVersion;
    private Integer version;
    private Long mtime;
    private Long eventTime;

    // 投放端推送的素材列表
    private List<CreativeDetailMaterialBo> titleBos;
    private List<CreativeDetailMaterialBo> imageBos;

    private List<CreativeDetailMaterialBo> descBos;
    private List<CreativeDetailMaterialBo> videoBos;
    private List<CreativeDetailMaterialBo> liveRoomBos;
    private List<CreativeDetailMaterialBo> dynamicBos;
    private List<CreativeDetailMaterialBo> buttonBos; // 按钮文案，投放端就固定的，按钮文案id，没必要审核
    // 三方落地页地址
    private List<CreativeDetailMaterialBo> thirdPartyLandingPageUrlBos;
    // 三方落地页组关系
    private List<CreativeDetailMaterialBo> thirdPartyLandingPageGroupBos;

    private List<CreativeDetailMaterialBo> tabUrlBos;
    // 替链
    private List<CreativeDetailMaterialBo> replaceUrlBo;
    // 游戏卡地址
    private List<CreativeDetailMaterialBo> gameCardBos;

    // 创意组件集合
    private List<ComponentMsgBo> componentList;
    // 三方落地页组id
    private Long pageGroupId;

    // =============== 根据组件素材投放端只提供id, 根据组件id查询后赋值 ==================
    private List<CreativeDetailMaterialBo> componentTitleBos;
    private List<CreativeDetailMaterialBo> componentDescBos;
    private List<CreativeDetailMaterialBo> componentImageBos;
    // 框下有三方落地页链接
    private List<CreativeDetailMaterialBo> underThirdPartyLandingPageUrlBos;

    // 程序化第三方落地页组 url
    private List<CreativeDetailMaterialBo> thirdPartyPageGroupUrlBos;

    // ====== 其他中间的变量
    private Integer accountId;

    private Integer isProgrammatic;
    private Integer auditStatus;
    private Integer bilibiliUserId;
    private Integer parentCreativeId;
    // 落地页组的审核状态
//    private Integer pageGroupAuditStatus;

    // 投放端推审的时候，投放端推送的时候就是使用影子覆盖之后
//    @Deprecated
//    private List<CreativeDetailInfoOfMaterialBo> shadowTitleBos;
//    @Deprecated
//    private List<CreativeDetailInfoOfMaterialBo> shadowImageBos;
//    @Deprecated
//    private CreativeDetailInfoOfMaterialBo shadowThirdPartyLandingPageUrlBo;
//    @Deprecated
//    private List<CreativeDetailInfoOfMaterialBo> shadowThirdPartyPageGroupUrlBos;

}
