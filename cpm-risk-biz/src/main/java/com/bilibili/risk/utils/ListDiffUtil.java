package com.bilibili.risk.utils;

import com.bilibili.risk.enums.DiffTypeEnum;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

public class ListDiffUtil {

    /**
     * 基于自定义 key 比较两个列表的差异
     *
     * @param newList      新列表
     * @param existList    旧列表
     * @param keyExtractor 用于提取比较的 key（如 User::getId）
     * @return Map<String, List < T>>，包含 "added", "updated", "removed"
     */
    public static <T, K> Map<Integer, List<T>> calculateDiff(
            List<T> newList,
            List<T> existList,
            Function<T, K> keyExtractor) {

        Map<Integer, List<T>> diffResult = new HashMap<>();

        // 转换为 Map<Key, T> 方便查找
        Map<K, T> existMap = existList.stream()
                .collect(Collectors.toMap(keyExtractor, item -> item));

        Map<K, T> newMap = newList.stream()
                .collect(Collectors.toMap(keyExtractor, item -> item));

        // 新增的元素：newMap 的 key 不在 existMap 中
        List<T> added = newList.stream()
                .filter(item -> !existMap.containsKey(keyExtractor.apply(item)))
                .collect(Collectors.toList());
        diffResult.put(DiffTypeEnum.ADDED.getKey(), added);

        // 删除的元素：existMap 的 key 不在 newMap 中
        List<T> removed = existList.stream()
                .filter(item -> !newMap.containsKey(keyExtractor.apply(item)))
                .collect(Collectors.toList());
        diffResult.put(DiffTypeEnum.DELETED.getKey(), removed);

        // 修改的元素：key 都存在，但对象不相等
        List<T> updated = newList.stream()
                .filter(item -> existMap.containsKey(keyExtractor.apply(item)))
                .filter(item -> !existMap.get(keyExtractor.apply(item)).equals(item))
                .collect(Collectors.toList());
        diffResult.put(DiffTypeEnum.UPDATED.getKey(), updated);
        return diffResult;
    }
}
