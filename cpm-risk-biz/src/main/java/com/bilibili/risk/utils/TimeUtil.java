package com.bilibili.risk.utils;

import com.bilibili.adp.common.util.Utils;
import org.springframework.util.StringUtils;

import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;

/**
 * @Description
 * <AUTHOR>
 * @Date 2020.09.29 18:55
 */
public class TimeUtil {

    public static final String DATE_FORMAT = "yyyy-MM-dd";
    public static final String DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
    public static final String TIMESTAMP_FORMAT = "yyyy-MM-dd HH:mm:ss.S";

    public final static long ONE_HOUR_MILLIS = 1000 * 60 * 60L;

    //thread safe
    public static final DateTimeFormatter FORMATTER_YYYY_MM_DD_HH_MM_SS = DateTimeFormatter.ofPattern(DATE_TIME_FORMAT);

    private TimeUtil(){}

    public static String toStr(Timestamp timestamp){
        if(timestamp == null){
            return null;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(timestamp);
    }


    public static String toStrIgnoreZero(Timestamp timestamp){
        if(timestamp == null){
            return null;
        }
        if (timestamp.getTime() == 0) {
            return "";
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(timestamp);
    }

    public static String date2String(Date date) {
        if (date == null) {
            return null;
        }
        return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(date);
    }

    public static String long2String(Long time) {
        if(!Utils.isPositive(time)){
            return "";
        }
        SimpleDateFormat sf = new SimpleDateFormat(DATE_TIME_FORMAT);
        Date date = new Date(time);
        return sf.format(date);
    }

    public static String longSecond2String(Long time) {
        if(!Utils.isPositive(time)){
            return "";
        }
        time = time * 1000;
        SimpleDateFormat sf = new SimpleDateFormat(DATE_TIME_FORMAT);
        Date date = new Date(time);
        return sf.format(date);
    }

    public static String long2String(Long time, String format) {
        if(!Utils.isPositive(time)){
            return "";
        }
        SimpleDateFormat sf = new SimpleDateFormat(format);
        Date date = new Date(time);
        return sf.format(date);
    }

    /**
     * 把日期类型格式化成字符串。
     */
    public static String format(Date date, String format) {
        if (date == null) {
            return null;
        }
        return new SimpleDateFormat(format).format(date);
    }

    /**
     * 把日期类型格式化成字符串。
     */
    public static String formatDate(Date date) {
        if (date == null) {
            return null;
        }
        return new SimpleDateFormat(DATE_FORMAT).format(date);
    }

    /**
     * 把日期类型格式化成字符串。
     */
    public static String formatDateTime(Date date) {
        if (date == null) {
            return null;
        }
        return new SimpleDateFormat(DATE_TIME_FORMAT).format(date);
    }

    /**
     * 把日期类型格式化成字符串。
     */
    public static String formatTimestamp(Date date) {
        if (date == null) {
            return null;
        }
        return new SimpleDateFormat(TIMESTAMP_FORMAT).format(date);
    }

    /**
     * 字符串转时间
     * @param dateStr
     * @param format
     * @return
     */
    public static Date convertDateByStr(String dateStr, String format) {
        try {
            Date date = new SimpleDateFormat(format).parse(dateStr);
            return date;
        } catch (ParseException e) {
            throw new IllegalArgumentException("date parse error");
        }
    }

    /**
     * 根据字符串获取时间戳，单位：秒
     * @param dateStr
     * @param format
     * @return
     */
    public static Long getTimestampByStr(String dateStr, String format) {
        if (StringUtils.isEmpty(dateStr)) {
            return 0l;
        }
        try {
            Date date = new SimpleDateFormat(format).parse(dateStr);
            return date.getTime();
        } catch (ParseException e) {
            throw new IllegalArgumentException("date parse error");
        }
    }

    public static String getTimestamp2HourString(Timestamp timestamp) {
        SimpleDateFormat sim = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        return sim.format(timestamp);
    }

    public static Timestamp getLastMinuteOfHourTimestamp(Timestamp timestamp) {
        Timestamp beforeTime = new Timestamp(timestamp.getTime() + ONE_HOUR_MILLIS - 1);
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:00");
        return Timestamp.valueOf(df.format(beforeTime));
    }

    public static Timestamp getMaxTime(){
        return Timestamp.valueOf("2099-12-31 23:59:59.999");
    }

    /**
     * 返回和当前时间对比的结果（-1：在当前时间之前，0与当前时间相等,1：在当前时间之后）
     * @param timestamp
     * @return
     */
    public static Integer comparisonCurrentTime(Timestamp timestamp){
        // 获取当前时间的 Timestamp 类型的对象
        Timestamp now = new Timestamp(Instant.now().toEpochMilli());

        // 对比 Timestamp 类型的对象和当前时间
        if (timestamp.before(now)) {
            // timestamp 在当前时间之前
            return -1;
        } else if (timestamp.after(now)) {
            // timestamp 在当前时间之后
            return 1;
        } else {
            // timestamp 与当前时间相等
            return 0;
        }
    }

    //yyyy-MM-dd HH:mm:ss
    public static LocalDateTime parseLocalDateTime(String value) {
        return LocalDateTime.parse(value, FORMATTER_YYYY_MM_DD_HH_MM_SS);
    }

    //yyyy-MM-dd HH:mm:ss
    public static long parseMills(String value) {
        return parseLocalDateTime(value)
                .toInstant(OffsetDateTime.now().getOffset())
                .toEpochMilli();
    }

    public static String formatOnlyDate(Long timestamp) {
        return new Timestamp(timestamp).toLocalDateTime().format(DateTimeFormatter.ofPattern(DATE_FORMAT));
    }

    public static Timestamp dateToTimestamp(Date date) {
        if (date == null) {
            return null;
        }
        try {
            return new Timestamp(date.getTime());
        } catch (Exception e) {
            return null;
        }
    }

    public static Timestamp todayEndTime(Long time) {
        if (time == null) {
            return null;
        }

        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(time);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);

        return new Timestamp(calendar.getTimeInMillis());
    }

    public static Timestamp todayEndTime(Date date) {
        if (date == null) {
            return null;
        }
        return todayEndTime(date.getTime());
    }

    public static int getYear() {
        Calendar calendar = Calendar.getInstance();
        return calendar.get(Calendar.YEAR);
    }
}
