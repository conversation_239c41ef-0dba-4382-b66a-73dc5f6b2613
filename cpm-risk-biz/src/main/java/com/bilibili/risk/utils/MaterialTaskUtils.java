package com.bilibili.risk.utils;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.risk.bo.LauMaterialAuditLabelBo;
import com.bilibili.risk.config.BizConfig;
import com.bilibili.risk.enums.ShardingTableEnum;
import edu.emory.mathcs.backport.java.util.Collections;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class MaterialTaskUtils {

    @Autowired
    private BizConfig bizConfig;

    public long calculateTimeoutMin(Long timeoutMin) {

        if (timeoutMin == null || Objects.equals(timeoutMin, 0L)) {
            timeoutMin = bizConfig.getTaskTimeoutFallbackMin();
        }
        return timeoutMin;
    }

    public static String parsePageGroupPrefix(String materialContent) {
        String materialContentPrefix = "";
        if (!StringUtils.isEmpty(materialContent)) {
            String[] parts = materialContent.split("-");  // 用 "-" 分割字符串
            if (parts.length == 3) {
                materialContentPrefix = "pageGroupPage-" + parts[1] + "-";
            }
        }
        return materialContentPrefix;
    }

    public static Long parsePageGroupId(String materialContent) {
        // 格式: pageGroupPage-{groupId}-{pageId}
        Long pageGroupId = 0L;
        if (!StringUtils.isEmpty(materialContent)) {
            String[] parts = materialContent.split("-");  // 用 "-" 分割字符串
            if (parts.length == 3) {
                try {
                    pageGroupId = Long.parseLong(parts[1]);
                } catch (Exception e) {
                    log.error("parsePageGroupId error, materialContent: {}, error: {}", materialContent, e.getMessage());
                }
            }
        }
        return pageGroupId;
    }

    public static Long parseGroupPageId(String materialContent) {
        Long pageGroupId = 0L;
        if (!StringUtils.isEmpty(materialContent)) {
            String[] parts = materialContent.split("-");  // 用 "-" 分割字符串
            if (parts.length == 3) {
                try {
                    pageGroupId = Long.parseLong(parts[2]);
                } catch (Exception e) {
                    log.error("parseGroupPageId error, materialContent: {}, error: {}", materialContent, e.getMessage());
                }
            }
        }
        return pageGroupId;
    }

    // 切分三级标签
    public static List<Long> string2LongList(String ids) {
        if (org.apache.commons.lang3.StringUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return Arrays.asList(ids.split(",")).stream().map(Long::parseLong).collect(Collectors.toList());
    }

    public static List<Integer> string2List(String ids) {
        if (ids == null) {
            return Collections.emptyList();
        }
        return Arrays.asList(ids.split(",")).stream().map(Integer::parseInt).collect(Collectors.toList());
    }

    public static String list2String(List<Integer> ids) {
        if (ids == null) {
            return "";
        }
        return ids.stream().map(String::valueOf).collect(Collectors.joining(","));
    }

    public static String longList2String(List<Long> ids) {
        if (ids == null) {
            return "";
        }
        return ids.stream().map(String::valueOf).collect(Collectors.joining(","));
    }

    public static Long convertTime(Timestamp timestamp) {
        if (timestamp == null) {
            return 0L;
        }
        return timestamp.getTime();
    }

    /**
     * 根据materialId 计算发表位
     *
     * @param materialId
     * @param type
     * @return
     */
    public static Integer calculateShardingKeyByMaterialId(String materialId, String type) {
        if (StringUtils.isBlank(materialId)) {
            throw new RuntimeException("materialId is empty");
        }
        if (ShardingTableEnum.TABLE_LAU_MATERIAL_AUDIT.getShardingAlgorithm().equals(type)) {
            return Integer.valueOf(materialId.substring(0, 3));
        } else if (ShardingTableEnum.TABLE_LAU_MATERIAL_CREATIVE_REL.getShardingAlgorithm().equals(type)) {
            return Math.abs(materialId.hashCode()) % ShardingTableEnum.TABLE_LAU_MATERIAL_CREATIVE_REL.getShardingNum();
        } else {
            throw new RuntimeException("sharding type is not supported");
        }
    }

    public static Integer calculateShardingKeyByCreativeId(Integer creativeId) {
        if (creativeId == null) {
            throw new RuntimeException("creativeId is empty");
        }
        return creativeId % ShardingTableEnum.TABLE_LAU_MATERIAL_AUDIT_TASK.getShardingNum();
    }

    public static Integer calculateShardingKeyByMd5(String md5, String type) {
        if (StringUtils.isBlank(md5)) {
            throw new RuntimeException("md5 is empty");
        }

        if (ShardingTableEnum.TABLE_LAU_MATERIAL_AUDIT.getShardingAlgorithm().equals(type)) {
            return Math.abs(md5.hashCode()) % ShardingTableEnum.TABLE_LAU_MATERIAL_AUDIT.getShardingNum();
        } else {
            throw new RuntimeException("sharding type is not supported");
        }
    }

    public static Integer calculateShardingKeyByTaskId(String taskId, String type) {
        if (StringUtils.isBlank(taskId)) {
            throw new RuntimeException("taskId is empty");
        }

        if (ShardingTableEnum.TABLE_LAU_MATERIAL_AUDIT_TASK.getShardingAlgorithm().equals(type)) {
            return Integer.valueOf(taskId.substring(0, 4));
        } else {
            throw new RuntimeException("sharding type is not supported");
        }
    }

    public static String genKey(String materialMd5, Integer materialType) {
        return materialMd5 + "-" + materialType;
    }

    /**
     * 根据三级标签 id 拼接一级二级三级标签名称
     * 格式: 一级名称-二级名称-三级名称;一级名称-二级名称-三级名称;
     *
     * @param auditLabelThirdId
     * @param auditLabelBoMap
     * @return
     */
    public static String genAuditLogValue(String auditLabelThirdId, Map<Long, LauMaterialAuditLabelBo> auditLabelBoMap) {
        StringBuilder levelsLabelSb = new StringBuilder();

        List<Long> thirdLabelIds = MaterialTaskUtils.string2LongList(auditLabelThirdId);
        for (Long thirdLabelId : thirdLabelIds) {
            List<String> oneLevelLabelList = new ArrayList<>();
            // 三级
            LauMaterialAuditLabelBo thirdAuditLabelBo = auditLabelBoMap.get(thirdLabelId);
            if (thirdAuditLabelBo != null) {
                oneLevelLabelList.add(thirdAuditLabelBo.getName());
                // 二级
                if (Utils.isPositive(thirdAuditLabelBo.getPid())) {
                    LauMaterialAuditLabelBo secondAuditLabelBo = auditLabelBoMap.get(thirdAuditLabelBo.getPid());
                    if (secondAuditLabelBo != null) {
                        oneLevelLabelList.add(secondAuditLabelBo.getName());
                        // 一级
                        if (Utils.isPositive(secondAuditLabelBo.getPid())) {
                            LauMaterialAuditLabelBo firstAuditLabelBo = auditLabelBoMap.get(secondAuditLabelBo.getPid());
                            if (firstAuditLabelBo != null) {
                                oneLevelLabelList.add(firstAuditLabelBo.getName());
                            }
                        }
                    }
                }
            }
            Collections.reverse(oneLevelLabelList);
            levelsLabelSb.append(oneLevelLabelList.stream().collect(Collectors.joining("-"))).append(";");
        }
        if (levelsLabelSb.length() == 0) {
            levelsLabelSb.append("无");
        }
        return "标记的标签:" + levelsLabelSb;
    }
}
