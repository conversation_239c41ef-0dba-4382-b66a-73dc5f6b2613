/*
 * Copyright (c) 2015-2021 BiliBili Inc.
 */

package com.bilibili.risk.utils;


import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

public class NumberUtils {
    public static final int TRUE = 1;
    public static final int FALSE = 0;

    public static String boolIntDesc(Integer x) {
        return NumberUtils.isPositive(x) ? "是" : "否";
    }

    public static boolean isPositive(Number number) {
        return goe(number, 1);
    }
    public static boolean goe(Number number, Number anchor) {
        if (Objects.isNull(number) || Objects.isNull(anchor)) return false;

        return number.longValue() >= anchor.longValue();
    }

    public static boolean between(Number number, Number floor, Number ceiling) {
        if (Objects.isNull(floor) && Objects.isNull(ceiling)) return true;

        if (Objects.isNull(number)) return false;

        if (Objects.nonNull(floor) && number.longValue() < floor.longValue()) return false;

        if (Objects.nonNull(ceiling) && number.longValue() > ceiling.longValue()) return false;

        return true;
    }

    public static boolean integer2boolean(int x) {
        if (x == 0) return false;

        if (x > 0) return true;

        throw new IllegalArgumentException("代表布尔值的数字不能为负数");
    }

    public static int boolean2Integer(boolean x) {
        return x ? TRUE : FALSE;
    }

    public static long parseDecimal(String decimal, int scale) {
        if (!StringUtils.hasText(decimal)) return 0L;

        return new BigDecimal(decimal).multiply(BigDecimal.valueOf(10).pow(scale)).longValue();
    }

    public static String formatDecimal(long value, int scale) {
        return BigDecimal.valueOf(value).divide(BigDecimal.valueOf(10).pow(scale), scale, RoundingMode.HALF_EVEN).toString();
    }

    public static boolean in(Number src, Number... targets) {
        if (Objects.isNull(src) || Objects.isNull(targets)) return false;

        for (Number target : targets) {
            if (Objects.isNull(target)) continue;

            if (src.longValue() == target.longValue()) return true;
        }
        return false;
    }
}
