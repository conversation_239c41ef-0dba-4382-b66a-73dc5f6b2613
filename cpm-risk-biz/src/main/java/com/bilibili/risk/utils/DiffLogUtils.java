package com.bilibili.risk.utils;
import com.bilibili.risk.annotation.DiffField;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class DiffLogUtils {

    /**
     * 比较两个对象的差异，允许其中一个对象为null
     */
    public static <T> List<String> diff(T oldObj, T newObj) {
        List<String> diffs = new ArrayList<>();

        // 处理一个或两个对象为null的情况
        if (oldObj == null && newObj == null) {
            return diffs; // 两个都为null，没有差异
        }

        // 确定要使用的Class类型和要比较的字段
        Class<?> clazz;
        if (oldObj != null) {
            clazz = oldObj.getClass();
        } else {
            clazz = newObj.getClass();
        }

        Field[] fields = FieldUtils.getAllFields(clazz);

        for (Field field : fields) {
            try {
                if (!field.isAnnotationPresent(DiffField.class)) {
                    continue; // 没有注解的字段跳过
                }

                DiffField annotation = field.getAnnotation(DiffField.class);
                if (annotation.ignore()) {
                    continue; // 标记忽略的字段跳过
                }

                field.setAccessible(true);

                // 获取新旧值，处理对象可能为null的情况
                Object oldValue = oldObj != null ? field.get(oldObj) : null;
                Object newValue = newObj != null ? field.get(newObj) : null;

                if (!Objects.equals(oldValue, newValue)) {
                    String fieldName = StringUtils.hasText(annotation.name()) ?
                            annotation.name() : field.getName();

                    String oldStr = formatValue(oldValue, annotation.sensitive());
                    String newStr = formatValue(newValue, annotation.sensitive());

                    diffs.add(String.format("%s: %s → %s", fieldName, oldStr, newStr));
                }
            } catch (IllegalAccessException e) {
                // 记录错误但继续处理其他字段
                diffs.add("无法访问字段: " + field.getName());
            }
        }

        return diffs;
    }

    private static String formatValue(Object value, boolean sensitive) {
        if (value == null) {
            return "";
        }

        if (sensitive) {
            return "***"; // 敏感数据脱敏
        }

        // 特殊处理常见类型
        if (value instanceof CharSequence) {
            return "" + value;
        }

        return value.toString();
    }

    /**
     * 生成格式化的差异日志
     */
    public static <T> String generateDiffField(T oldObj, T newObj, String objectName) {
        List<String> diffs = diff(oldObj, newObj);
        if (diffs.isEmpty()) {
            return objectName + ": 未检测到变更";
        }

        return objectName + " 变更明细:\n" + String.join("\n", diffs);
    }
}
