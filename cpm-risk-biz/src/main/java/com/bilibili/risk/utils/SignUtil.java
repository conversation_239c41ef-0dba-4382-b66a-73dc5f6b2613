package com.bilibili.risk.utils;

import com.alibaba.fastjson.JSONObject;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.Md5Util;
import com.bilibili.risk.exception.ExceptionCode;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * Created by fanwenbin on 2017/2/27.
 */
@Component
public class SignUtil {
    @Value("${passport.key}")
    private String key;

    public String getSign(Object param) throws ServiceException {
        JSONObject paramJson = (JSONObject) JSONObject.toJSON(param);
        List<String> paramList = new ArrayList<>();
        for (Map.Entry<String, Object> entry : paramJson.entrySet()) {
            if ("sign".equals(entry.getKey())) continue;
            try {
                paramList.add(entry.getKey() + "=" + URLEncoder.encode(null == entry.getValue() ? "null" : entry.getValue().toString(), "UTF-8"));
            } catch (UnsupportedEncodingException e) {
                throw new ServiceException(ExceptionCode.SYSTEM_ERROR);
            }
        }
        Collections.sort(paramList);
        String sign = String.join("&", paramList);
        return Md5Util.md5Hash(sign + key);
    }

    public String getKey() {
        return key;
    }
}
