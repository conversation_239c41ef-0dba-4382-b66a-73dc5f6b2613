package com.bilibili.risk.utils;

import com.bilibili.risk.constant.MDCConstants;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import org.slf4j.Logger;
import org.slf4j.MDC;

import java.util.function.Consumer;
import java.util.function.Function;

public class CatUtils {

    public static final String TRANSACTION_UNSET = "unset";

    public static void run(String type, String name, Logger logger, Consumer<Transaction> consumer) {
        Transaction transaction = Cat.newTransaction(type, name);
        MDC.put(MDCConstants.catId, Cat.getCurrentMessageId());
        try {
            consumer.accept(transaction);
            transaction.setStatus(Transaction.SUCCESS);
        } catch (Exception e) {
            logger.error("{} call error", name, e);
            transaction.setStatus(e);
            throw e;
        } finally {
            MDC.remove(MDCConstants.catId);
            transaction.complete();
        }
    }

    public static <R> R call(String type, String name, Logger logger, Function<Transaction, R> function) {
        Transaction transaction = Cat.newTransaction(type, name);
        MDC.put(MDCConstants.catId, Cat.getCurrentMessageId());
        try {
            R r = function.apply(transaction);
            transaction.setStatus(Transaction.SUCCESS);
            return r;
        } catch (Exception e) {
            logger.error("{} call error", name, e);
            transaction.setStatus(e);
            throw e;
        } finally {
            MDC.remove(MDCConstants.catId);
            transaction.complete();
        }
    }


}
