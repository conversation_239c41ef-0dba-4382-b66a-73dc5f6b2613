package com.bilibili.risk.utils;

import com.bilibili.risk.constant.MDCConstants;
import com.dianping.cat.Cat;
import com.dianping.cat.CatConstants;
import com.dianping.cat.message.Transaction;
import io.opentelemetry.api.OpenTelemetry;
import io.opentelemetry.api.trace.Span;
import io.opentelemetry.api.trace.SpanBuilder;
import io.opentelemetry.api.trace.StatusCode;
import io.opentelemetry.api.trace.Tracer;
import io.opentelemetry.context.Context;
import io.opentelemetry.context.Scope;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.function.Consumer;
import java.util.function.Function;

@Service
@Slf4j
public class TraceUtil {

    @Autowired
    private OpenTelemetry openTelemetry;

    public Tracer getTracer(String instrumentationScopeName) {
        // 相同的instrumentationScopeName将拿到相同的Tracer
        return openTelemetry.getTracer(instrumentationScopeName);
    }

    public <T> void span(String spanName, Context parentContext, Function<T, T> function) {
        SpanBuilder spanBuilder = getTracer(spanName).spanBuilder(spanName);
        if (parentContext != null) {
            spanBuilder.setParent(parentContext);
        }
        Span span = spanBuilder.startSpan();
        log.info("spanName: {}", spanName);
        try (Scope ignored = span.makeCurrent()) {
            MDC.put(MDCConstants.catId, Cat.getCurrentMessageId());
            log.info("log.trace_id={}", span.getSpanContext().getTraceId());
            function.apply(null);
        } catch (Exception e) {
            // 异常处理，标记错误状态后会被强制采样
            span.setStatus(StatusCode.ERROR)
                    .recordException(e);
            log.error("com.bilibili.ad.manager.biz.util.TraceUtil.span error trace_id={}", span.getSpanContext().getTraceId(), e);
            throw e;
        } finally {
            // 需要包裹的代码块结束后必须调用 end
            MDC.remove(MDCConstants.catId);
            span.end();
        }
    }

    public void spanDataBus(String spanName, Context parentContext, Consumer<Transaction> consumer) {

        Transaction t = Cat.newTransaction(CatConstants.TYPE_SERVICE, "Databus." + spanName);
        SpanBuilder spanBuilder = getTracer(spanName).spanBuilder(spanName);
        if (parentContext != null) {
            spanBuilder.setParent(parentContext);
        }
        Span span = spanBuilder.startSpan();
        log.info("spanName: {}", spanName);
        try (Scope ignored = span.makeCurrent()) {
            MDC.put(MDCConstants.catId, Cat.getCurrentMessageId());
            log.info("log.trace_id={}", span.getSpanContext().getTraceId());
            consumer.accept(t);
            // transaction 未设置再去设，防止冲掉业务内的 error 设值
            try {
                if(CatUtils.TRANSACTION_UNSET.equals(t.getStatus())){
                    t.setStatus(Transaction.SUCCESS);
                }
            } catch (Exception e) {
                log.error("cat transaction set status error, trace_id={}, spanName={}",
                          span.getSpanContext().getTraceId(), spanName, e);
                t.setStatus(Transaction.SUCCESS);
            }
        } catch (Exception e) {
            // 异常处理，标记错误状态后会被强制采样
            span.setStatus(StatusCode.ERROR)
                    .recordException(e);
            t.setStatus(e);
            log.error("spanDataBus error trace_id={}, spanName={}", span.getSpanContext().getTraceId(), spanName, e);
            throw e;
        } finally {
            // 需要包裹的代码块结束后必须调用 end
            span.end();
            MDC.remove(MDCConstants.catId);
            t.complete();
        }
    }

    public Context getCurrentContext() {
        return Context.current();
    }

}
