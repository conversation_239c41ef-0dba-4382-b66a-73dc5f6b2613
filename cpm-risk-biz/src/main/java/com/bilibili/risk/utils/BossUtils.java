package com.bilibili.risk.utils;

import com.google.common.base.Joiner;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.S3Configuration;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;

import javax.annotation.PostConstruct;
import java.net.URI;

/**
 * description: 
 * <AUTHOR>
 * @date 2025/1/20 17:59
 */
@Component
@Slf4j
public class BossUtils {
//
//    @Value("${boss.UrlDomain:http://uat-boss.bilibili.co}")
//    private String url;
//    @Value("${boss.AccessKey:2e849d9e038c4c05}")
//    private String accessKey;
//    @Value("${boss.SecretKey:a0e1581f05c4d596c144356b179c7bf9}")
//    private String secretKey;
//    @Value("${boss.Bucket:cm}")
//    private String bucket;
//
//    private S3Client client;
//
//    @PostConstruct
//    public void init() {
//        client = buildClient(URI.create(url),
//                accessKey,
//                secretKey);
//        log.info("BossUtils init success");
//    }
//
//    public S3Client buildClient(URI endpoint, String accessKey, String secretKey) {
//        Region region = Region.of("boss"); // 目前暂未开启 Region 校验
//        AwsBasicCredentials credentials = AwsBasicCredentials.create(accessKey, secretKey);
//        StaticCredentialsProvider provider = StaticCredentialsProvider.create(credentials);
//        S3Configuration config = S3Configuration.builder()
//                .pathStyleAccessEnabled(true)
//                .checksumValidationEnabled(false) // !!!!重要 这里一定是false 不然校验过不去
//                .build();
//
//        return S3Client.builder()
//                .endpointOverride(endpoint)
//                .region(region)
//                .credentialsProvider(provider)
//                .serviceConfiguration(config)
//                .build();
//    }
//
//    public S3Presigner buildS3Presigner(URI endpoint, String accessKey, String secretKey) {
//        Region region = Region.of("boss"); // 目前暂未开启 Region 校验
//        AwsBasicCredentials credentials = AwsBasicCredentials.create(accessKey, secretKey);
//        StaticCredentialsProvider provider = StaticCredentialsProvider.create(credentials);
//        S3Configuration config = S3Configuration.builder()
//                .pathStyleAccessEnabled(true)
//                .checksumValidationEnabled(false)   // !!!!重要 这里一定是false 不然校验过不去
//                .build();
//
//        return S3Presigner.builder()
//                .endpointOverride(endpoint)
//                .region(region)
//                .credentialsProvider(provider)
//                .serviceConfiguration(config)
//                .build();
//    }
//
//    public String uploadByte(String path , String fileName, byte[] content) {
//        String key = Joiner.on("/").join(path, fileName);
//        PutObjectRequest req = PutObjectRequest
//                .builder()
//                .bucket(this.bucket)
//                .contentType("text/plain;charset=utf-8")
//                .key(key)
//                .build();
//        client.putObject(req, RequestBody.fromBytes(content)); // 上传 Byte 数组
//        String fileAddress = url + "/" + bucket + "/" + key;
//        log.info("upload success, fileAddress: {}}" , fileAddress);
//        return fileAddress;
//    }

}
