package com.bilibili.risk.utils;

import com.google.common.base.Charsets;
import com.google.common.hash.Hashing;

public final class Md5Util {
    public final static String md5Hash(String value) {
        return Hashing.md5().newHasher().putString(value, Charsets.UTF_8).hash().toString();
    }

    public static void main(String[] args) {
        String value = "1qaz@WSX";
        System.out.println(md5Hash(value));
    }
}
