package com.bilibili.risk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.elasticsearch.common.util.set.Sets;

import java.util.Set;

@AllArgsConstructor
@Getter
public enum AuditLabelIdSpecialEnum {

    // 0被一级的占用了
    PAGE_GROUP_REJECT("-1", "落地页组组拒绝"),
    PAGE_GROUP_PASS("-2", "落地页组组通过"),
    DEFAULT_PASS("-3", "通过"),
    ;

    private final String code;

    private final String desc;

    public static Set<String> pageGroupResult(){
        return Sets.newHashSet(PAGE_GROUP_PASS.getCode(), PAGE_GROUP_REJECT.getCode());
    }

    public static AuditLabelIdSpecialEnum getByCode(String code) {
        for (AuditLabelIdSpecialEnum auditLabelIdSpecialEnum : AuditLabelIdSpecialEnum.values()) {
            if (auditLabelIdSpecialEnum.code.equals(code)) {
                return auditLabelIdSpecialEnum;
            }
        }
        return null;
    }

    /**
     * 根据落地页组页面审核状态获取 labelId
     * @param statusEnum
     * @return
     */
    public static AuditLabelIdSpecialEnum getByGroupPageStatus(CreativeAuditStatusEnum statusEnum) {
        if(statusEnum == CreativeAuditStatusEnum.AUDIT_REJECTED) {
            // 审核拒绝
            return AuditLabelIdSpecialEnum.PAGE_GROUP_REJECT;
        } else if(statusEnum == CreativeAuditStatusEnum.AUDIT_PADDED) {
            // 审核通过
            return AuditLabelIdSpecialEnum.PAGE_GROUP_PASS;
        }

        return null;
    }
}
