package com.bilibili.risk.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * description: 
 * <AUTHOR>
 * @date 2025/1/20 14:29
 */
@Slf4j
@AllArgsConstructor
public enum PromotionPurposeContentProcessorEnum {


    GAME_CENTER("game_center/detail", "游戏中心") {
        @Override
        public boolean isProcessByThisPattern(String content) {
            return content.contains("game_center/detail");
        }
        @Override
        public String getContextToMatch(String content) {
            return getIdFromUrlInQueryParam("id" , content);
        }

        @Override
        public String splitUrl(String content) {
            String gameId = getContextToMatch(content);
            return BILI_GAME_URL + gameId;
        }
    },


    BILI_GAME("www.biligame.com/detail", "bilibili游戏") {
        @Override
        public boolean isProcessByThisPattern(String content) {
            return content.contains("www.biligame.com/detail");
        }
        @Override
        public String getContextToMatch(String content) {
            return getIdFromUrlInQueryParam("id" , content);
        }
        @Override
        public String splitUrl(String content) {
            return content;
        }
    },

    APP_GAME("app.biligame.com/detail", "appBili游戏游戏") {
        @Override
        public boolean isProcessByThisPattern(String content) {
            return content.contains("app.biligame.com/detail");
        }
        @Override
        public String getContextToMatch(String content) {
            return getIdFromUrlInQueryParam("id", content);
        }
        @Override
        public String splitUrl(String content) {
            String gameId = getContextToMatch(content);
            return BILI_GAME_URL + gameId;
        }
    },


    MINIAPP_GAME("miniapp.bilibili.com/game", "miniapp游戏") {
        @Override
        public boolean isProcessByThisPattern(String content) {
            return content.contains("miniapp.bilibili.com/game");
        }
        @Override
        public String getContextToMatch(String content) {
            return getIdFromUrlWithoutQueryParam(content);
        }
        @Override
        public String splitUrl(String content) {
            return content;
        }
    },



    VIDEO("www.bilibili.com/video", "video") {
        @Override
        public boolean isProcessByThisPattern(String content) {
            return content.contains("www.bilibili.com/video");
        }
        @Override
        public String getContextToMatch(String content) {
            return getIdFromUrlWithoutQueryParam(content);
        }
        @Override
        public String splitUrl(String content) {
            return content;
        }
    },

    APPLET("miniapp.bilibili.com/applet", "高能建站-applet") {
        @Override
        public boolean isProcessByThisPattern(String content) {
            return content.contains("miniapp.bilibili.com/applet");
        }
        @Override
        public String getContextToMatch(String content) {
            return getIdFromUrlInQueryParam("pageId" , content);
        }
        @Override
        public String splitUrl(String content) {
            return content;
        }
    },

    GAONENG("gaoneng.bilibili.com", "高能建站") {
        @Override
        public boolean isProcessByThisPattern(String content) {
            return content.contains("gaoneng.bilibili.com");
        }
        @Override
        public String getContextToMatch(String content) {
            return getIdFromUrlInQueryParam("pageId" , content);
        }
        @Override
        public String splitUrl(String content) {
            return content;
        }
    },

    OPUS_DETAIL("opusdetail", "动态") {
        @Override
        public boolean isProcessByThisPattern(String content) {
            return content.contains("opusdetail") || content.contains("opus/detail");
        }
        @Override
        public String getContextToMatch(String content) {
            return getIdFromUrlWithoutQueryParam(content);
        }
        @Override
        public String splitUrl(String content) {
            return content;
        }
    },

    LIVE("live.bilibili.com", "直播") {
        @Override
        public boolean isProcessByThisPattern(String content) {
            return content.contains("live.bilibili.com");
        }
        @Override
        public String getContextToMatch(String content) {
            return getIdFromUrlWithoutQueryParam(content);
        }
        @Override
        public String splitUrl(String content) {
            return content;
        }
    },

    MALL("mall.bilibili.com", "会员购") {
        @Override
        public boolean isProcessByThisPattern(String content) {
            return content.contains("mall.bilibili.com");
        }
        @Override
        public String getContextToMatch(String content) {
            return getIdFromUrlInQueryParam("itemsId" , content);
        }
        @Override
        public String splitUrl(String content) {
            return content;
        }
    },

    APPLE("apps.apple.com", "苹果商店") {
        @Override
        public boolean isProcessByThisPattern(String content) {
            return content.contains("apps.apple.com");
        }
        @Override
        public String getContextToMatch(String content) {
            return getIdFromUrlWithoutQueryParam(content);
        }
        @Override
        public String splitUrl(String content) {
            return content;
        }
    },

    OTHER("other", "其他") {
        @Override
        public boolean isProcessByThisPattern(String content) {
            if(content.contains("bili")){
                log.warn("可能存在未识别的case,  content:{}", content);
            }
            return true;
        }
        @Override
        public String getContextToMatch(String content) {
            if(StringUtils.isBlank(content)){
                return content;
            } else {
                int firstIndex = content.indexOf("?");
                if (firstIndex != -1) {
                    return content.substring(0, firstIndex);
                }
                return content;
            }
        }
        @Override
        public String splitUrl(String content) {
            return content;
        }
    },

    ;

    public static final String BILI_GAME_URL = "https://www.biligame.com/detail/?id=";
    @Getter
    private final String code;
    @Getter
    private final String desc;

    public static PromotionPurposeContentProcessorEnum getByCode(String code) {
        for (PromotionPurposeContentProcessorEnum bean : values()) {
            if (bean.getCode().equals(code)) {
                return bean;
            }
        }
        return OTHER;
    }

    public static String searchContextToMatch(String content) {
        for (PromotionPurposeContentProcessorEnum processor : values()) {
            if (processor.isProcessByThisPattern(content)) {
                return processor.getContextToMatch(content);
            }
        }
        return content;
    }

    public static PromotionPurposeContentProcessorEnum searchProcessorEnum(String content) {
        for (PromotionPurposeContentProcessorEnum processor : values()) {
            if (processor.isProcessByThisPattern(content)) {
                return processor;
            }
        }
        return OTHER;
    }

    /**
     * 获取content中需要进行匹配的部分
     * @return 获取content中需要进行匹配的部分
     */
    public abstract String getContextToMatch (String content);

    /**
     * 是否由该处理器进行处理
     * @return 是否由该处理器进行处理
     */
    public abstract boolean isProcessByThisPattern (String content);

    public abstract String splitUrl(String content);

    /**
     * 获取content中的id部分
     * @return 匹配的id
     */
    public String getIdFromUrlInQueryParam(String id , String content){
        String target = id +"=";
        int startIndex = -1;
        do {
            startIndex = content.indexOf(target, startIndex + 1);
            if (startIndex == -1 || (startIndex > 0 && content.charAt(startIndex - 1) != '?' && content.charAt(startIndex - 1) != '&')) {
                continue;
            }
            startIndex += target.length();
            int endIndex = content.indexOf('&', startIndex);
            if (endIndex == -1) {
                endIndex = content.length();
            }
            return content.substring(startIndex, endIndex);
        } while (startIndex != -1);
        log.warn("cannot find param, id :{}, content:{}", id, content);
        return "";
    }


    /**
     * 获取content中的id部分
     * @return 匹配的id
     */
    public String getIdFromUrlWithoutQueryParam(String content){
        int lastSlashIndex = content.lastIndexOf('/');
        if (lastSlashIndex == -1) {
            log.warn("cannot find param, content:{}", content);
            return "";
        }
        int questionMarkIndex = content.indexOf('?', lastSlashIndex);
        int endIndex = (questionMarkIndex != -1) ? questionMarkIndex : content.length();
        return content.substring(lastSlashIndex + 1, endIndex);
    }
}
