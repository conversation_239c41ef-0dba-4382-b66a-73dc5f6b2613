package com.bilibili.risk.enums;

import java.util.Optional;

public enum MaterialTaskTypeEnum {

    UNKNOWN(0, "未知"),
    MANUAL_AUDIT(1, "人审"),
    REUSE(2, "复用"),
    MACHINE_AUDIT(3, "机审"),
    QUALITY_AUDIT(4, "质检");

    private final Integer code;
    private final String name;


    private MaterialTaskTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static Optional<MaterialTaskTypeEnum> getByCode(Integer code) {
        MaterialTaskTypeEnum[] var1 = values();
        int var2 = var1.length;

        for (int var3 = 0; var3 < var2; ++var3) {
            MaterialTaskTypeEnum creativeStyleEnum = var1[var3];
            if (creativeStyleEnum.code.equals(code)) {
                return Optional.of(creativeStyleEnum);
            }
        }

        return Optional.empty();
    }

    public String getValue() {
        return this.name;
    }

    public Integer getKey() {
        return this.code;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }
}
