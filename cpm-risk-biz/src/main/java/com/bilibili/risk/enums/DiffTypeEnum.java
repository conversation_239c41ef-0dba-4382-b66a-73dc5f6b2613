package com.bilibili.risk.enums;

import java.util.Optional;

/**
 * 审核状态
 **/
public enum DiffTypeEnum {

    ADDED(1, "新增的"),
    UPDATED(2, "修改的"),
    DELETED(3, "删除的"),
    ;

    private final Integer code;
    private final String name;


    private DiffTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static Optional<DiffTypeEnum> getByCode(Integer code) {
        DiffTypeEnum[] var1 = values();
        int var2 = var1.length;

        for (int var3 = 0; var3 < var2; ++var3) {
            DiffTypeEnum creativeStyleEnum = var1[var3];
            if (creativeStyleEnum.code.equals(code)) {
                return Optional.of(creativeStyleEnum);
            }
        }

        return Optional.empty();
    }

    public String getValue() {
        return this.name;
    }

    public Integer getKey() {
        return this.code;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }
}
