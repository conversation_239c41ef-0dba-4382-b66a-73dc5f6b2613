package com.bilibili.risk.enums;

import java.util.Optional;

public enum EsShardingTypeEnum {

    SINGLE(1, "单分片"),
    BY_YEAR(2, "按年"),
    BY_MONTH(3, "按月份"),
    ;

    private final Integer code;
    private final String name;


    private EsShardingTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static Optional<EsShardingTypeEnum> getByCode(Integer code) {
        EsShardingTypeEnum[] var1 = values();
        int var2 = var1.length;

        for (int var3 = 0; var3 < var2; ++var3) {
            EsShardingTypeEnum creativeStyleEnum = var1[var3];
            if (creativeStyleEnum.code.equals(code)) {
                return Optional.of(creativeStyleEnum);
            }
        }

        return Optional.empty();
    }

    public String getValue() {
        return this.name;
    }

    public Integer getKey() {
        return this.code;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }
}
