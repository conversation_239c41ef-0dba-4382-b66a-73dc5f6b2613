package com.bilibili.risk.enums;

import lombok.Getter;

@Getter
public enum AuditTaskLancerEnum {

    // 创意推审
    CREATIVE_PUSH_TO_AUDIT("创意推审", "创意推审", "创意推审"),
    ;

    // 二级操作类型：对业务操作
    private String optType;
    // 一级类型：业务类型
    private String bizType;
    // 描述
    private String desc;
    
    AuditTaskLancerEnum(String optType, String bizType, String desc) {
        this.optType = optType;
        this.bizType = bizType;
        this.desc = desc;
    }
    
    
}
