package com.bilibili.risk.enums;

import java.util.Optional;

/**
 * 素材来源: 创意,story
 **/
public enum MaterialSourceEnum {

    CREATIVE(1, "创意"),
    STORY(2, "story"),
    PAGE_GROUP_PAGE(3, "落地页组页面"),
    ;

    private final Integer code;
    private final String name;

    private MaterialSourceEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static Optional<MaterialSourceEnum> getByCode(Integer code) {
        MaterialSourceEnum[] var1 = values();
        int var2 = var1.length;

        for (int var3 = 0; var3 < var2; ++var3) {
            MaterialSourceEnum creativeStyleEnum = var1[var3];
            if (creativeStyleEnum.code.equals(code)) {
                return Optional.of(creativeStyleEnum);
            }
        }

        return Optional.empty();
    }

    public String getValue() {
        return this.name;
    }

    public Integer getKey() {
        return this.code;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }
}
