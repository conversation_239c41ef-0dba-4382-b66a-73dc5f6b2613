package com.bilibili.risk.enums;

import lombok.Getter;

/**
 * metrics 打点枚举类
 * 提供错误码的组成部分：业务域、错误类型、错误子码
 */
public class MetricsCodeEnum {

	/**
	 * 业务域枚举
	 */
	@Getter
	public enum DomainType {
		MATERIAL("素材"),
		CREATIVE_MATERIAL_TASK("创意素材任务"),
		CREATIVE("创意"),
		OTHER("其它"),
		NONE("NONE");

		private final String desc;

		DomainType(String desc) {
			this.desc = desc;
		}

		@Override
		public String toString() {
			return name();
		}
	}

	/**
	 * 错误类型枚举
	 */
	@Getter
	public enum Type {
		BIZ_ERR("业务逻辑错误"),
		SYS_ERR("系统错误"),
		BIZ_INDEX("业务指标"),
		;

		private final String desc;

		Type(String desc) {
			this.desc = desc;
		}

		@Override
		public String toString() {
			return name();
		}
	}

	/**
	 * 错误子码枚举
	 */
	@Getter
	public enum SubCode {
		// 通用错误子码
		FAIL("-1", "FAIL"),
		SUCCESS("0", "SUCCESS"),
		PARAM_INVALID("10001", "参数无效"),
		DATA_NOT_FOUND("10002", "数据不存在"),
		DATA_STATUS_INVALID("10003", "数据状态无效"),
		PERMISSION_DENIED("10004", "无操作权限"),
		OPERATE_ILLEGAL("10005", "操作非法"),
		BUSINESS_ERROR("10006", "系统错误，请稍后重试"),
		SYSTEM_ERROR("10007", "系统错误"),
		TIMEOUT("10008", "请求超时"),
		NETWORK_ERROR("10009", "网络错误"),
		DB_ERROR("10010", "数据库错误"),
		DOWNSTREAM_CALL_ERROR("10011", "下游调用错误"),
		DATA_INVALID("10012", "数据异常"),
		NULL_POINTER_ERR("10013", "空指针异常"),
		REDIS_ERROR("10014", "Redis错误"),
		DATABUS_ERROR("10015", "databus错误"),
		GET_LOCK_FAILED("10016", "抢占锁失败"),
		IMAGE_ERROR("10017", "图片处理失败"),
		CLIENT_CANCELLED("10018", "上游请求取消"),

		// 计划相关错误子码
		CAMPAIGN_NOT_FOUND("20001", "计划不存在"),

		// 单元相关错误子码
		UNIT_NOT_FOUND("30001", "单元不存在"),

		// 创意相关错误子码
        CREATIVE_NOT_FOUND("40001", "创意不存在"),
		CREATIVE_AUDIT_FAILED("40002", "创意审核失败"),
		CREATIVE_MATERIAL_MISSING("40003", "创意素材缺失"),

		// 账户相关错误子码
		ACCOUNT_NOT_FOUND("50001", "账户不存在"),
        ACCOUNT_STATUS_ABNORMAL("50002", "账户状态异常"),

		// 业务指标
		// 创意维度
		BIZ_CREATIVE_PUSH_TO_AUDIT("100001", "创意推审"),
		BIZ_CREATIVE_JUDGE("100002", "创意判定"),
		BIZ_CREATIVE_AUDIT("100003", "创意审核"),

		// 任务维度
		BIZ_MATERIAL_TASK_ENTER_AUDIT_TO_MATCH("200001", "任务进审待匹配"),
		BIZ_MATERIAL_TASK_ENTER_AUDIT("200002", "任务进审"),
		BIZ_MATERIAL_TASK_REUSE_RESULT("200006", "任务进审-复用结果"),
		BIZ_MATERIAL_TASK_DELETE("200009", "任务进审-任务删除"),
		BIZ_MATERIAL_TASK_RECOVER("200013", "任务机审-删除又恢复"),

		BIZ_MATERIAL_TASK_PULL("200010", "任务拉取"),
		BIZ_MATERIAL_TASK_PRE_PULL("200011", "任务预拉取"),

		BIZ_MATERIAL_TASK_AUDIT("200004", "任务提交"),
		BIZ_MATERIAL_TASK_AUDIT_REUSE("200014", "任务提交-复用结果"),
		BIZ_MATERIAL_TASK_AUDIT_TO_CALLBACK("200007", "任务提交-待修改落地页"),
		BIZ_MATERIAL_TASK_AUDIT_AND_RELEASE("200008", "任务提交-并释放"),

		BIZ_MATERIAL_TASK_AUTO_RELEASE("200005", "任务自动释放"),

		BIZ_MATERIAL_TASK_PAGE_LIST("200012", "任务列表查询"),

		// 素材维度
		BIZ_MATERIAL_ALL_CREATIVE_JUDGE("300001", "素材下所有创意判定"),
		;


		private final String code;
		private final String desc;

		SubCode(String code, String desc) {
			this.code = code;
			this.desc = desc;
		}

		@Override
		public String toString() {
			return code;
		}
	}

	/**
	 * 生成完整错误码
	 *
	 * @param domainType 业务域
	 * @param type  错误类型
	 * @param subCode    错误子码
	 * @return 完整错误码，格式：域-类型-子码
	 */
	public static String generateFullCode(DomainType domainType, Type type, SubCode subCode) {
		return domainType.name() + "-" + type.name() + "-" + subCode.getCode();
	}
}
