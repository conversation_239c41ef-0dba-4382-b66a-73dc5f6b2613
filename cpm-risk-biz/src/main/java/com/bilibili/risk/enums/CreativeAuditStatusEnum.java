package com.bilibili.risk.enums;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * 审核状态
 **/
public enum CreativeAuditStatusEnum {

    AUDITING(1, "审核中"),
    AUDIT_PADDED(2, "审核通过"),
    AUDIT_REJECTED(3, "审核驳回"),
    ;

    private final Integer code;
    private final String name;

    public static List<Integer> getCompletedStatus() {
        return Arrays.asList(AUDIT_PADDED.getCode(), AUDIT_REJECTED.getCode());
    }

    private CreativeAuditStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static Optional<CreativeAuditStatusEnum> getByCode(Integer code) {
        CreativeAuditStatusEnum[] var1 = values();
        int var2 = var1.length;

        for (int var3 = 0; var3 < var2; ++var3) {
            CreativeAuditStatusEnum creativeStyleEnum = var1[var3];
            if (creativeStyleEnum.code.equals(code)) {
                return Optional.of(creativeStyleEnum);
            }
        }

        return Optional.empty();
    }

    public String getValue() {
        return this.name;
    }

    public Integer getKey() {
        return this.code;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }
}
