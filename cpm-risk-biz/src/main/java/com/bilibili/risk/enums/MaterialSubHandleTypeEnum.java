package com.bilibili.risk.enums;

import java.util.Optional;

public enum MaterialSubHandleTypeEnum {

    NO_HANDLE(0, "不处理"),
    PASS(1, "更新评级标签"),
    ;

    private final Integer code;
    private final String name;


    private MaterialSubHandleTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static Optional<MaterialSubHandleTypeEnum> getByCode(Integer code) {
        MaterialSubHandleTypeEnum[] var1 = values();
        int var2 = var1.length;

        for (int var3 = 0; var3 < var2; ++var3) {
            MaterialSubHandleTypeEnum creativeStyleEnum = var1[var3];
            if (creativeStyleEnum.code.equals(code)) {
                return Optional.of(creativeStyleEnum);
            }
        }

        return Optional.empty();
    }

    public static Optional<MaterialSubHandleTypeEnum> getByName(String name) {
        MaterialSubHandleTypeEnum[] var1 = values();
        int var2 = var1.length;

        for (int var3 = 0; var3 < var2; ++var3) {
            MaterialSubHandleTypeEnum creativeStyleEnum = var1[var3];
            if (creativeStyleEnum.name.equals(name)) {
                return Optional.of(creativeStyleEnum);
            }
        }

        return Optional.of(NO_HANDLE);
    }

    public String getValue() {
        return this.name;
    }

    public Integer getKey() {
        return this.code;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

}
