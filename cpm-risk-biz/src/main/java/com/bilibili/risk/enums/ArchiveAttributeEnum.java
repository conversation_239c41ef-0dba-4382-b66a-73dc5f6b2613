package com.bilibili.risk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <a href="https://info.bilibili.co/pages/viewpage.action?pageId=167232908#id-%E5%A4%8D%E5%88%B6%E4%BB%8E%E7%A8%BF%E4%BB%B6%E5%AD%97%E6%AE%B5%E6%9E%9A%E4%B8%BE%E5%80%BC%E5%B1%9E%E6%80%A7%E8%AF%B4%E6%98%8E-%E7%A8%BF%E4%BB%B6%E5%B1%9E%E6%80%A7attribute">https://info.bilibili.co/pages/viewpage.action?pageId=167232908#id-%E5%A4%8D%E5%88%B6%E4%BB%8E%E7%A8%BF%E4%BB%B6%E5%AD%97%E6%AE%B5%E6%9E%9A%E4%B8%BE%E5%80%BC%E5%B1%9E%E6%80%A7%E8%AF%B4%E6%98%8E-%E7%A8%BF%E4%BB%B6%E5%B1%9E%E6%80%A7attribute</a>
 */
@Getter
@AllArgsConstructor
public enum ArchiveAttributeEnum {

    /**
     * 是否是PGC稿件
     */
    IS_PGC(9, "是否是PGC稿件"),
    /**
     * 是否是私单
     */
    IS_PORDER(12, "是否是私单"),
    /**
     * 是否课堂稿件
     */
    IS_PUGV(30,"是否课堂稿件"),
    ;

    private final int index;

    private final String desc;
}
