package com.bilibili.risk.enums;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

public enum QueueTypeEnum {

    NORMAL(1, "普通队列"),
    FALLBACK(2, "兜底队列"),
    WAIT(3, "等待队列"),
    ;

    private final Integer code;
    private final String name;


    private QueueTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static Optional<QueueTypeEnum> getByCode(Integer code) {
        QueueTypeEnum[] var1 = values();
        int var2 = var1.length;

        for (int var3 = 0; var3 < var2; ++var3) {
            QueueTypeEnum creativeStyleEnum = var1[var3];
            if (creativeStyleEnum.code.equals(code)) {
                return Optional.of(creativeStyleEnum);
            }
        }

        return Optional.empty();
    }

    public String getValue() {
        return this.name;
    }

    public Integer getKey() {
        return this.code;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }
}
