package com.bilibili.risk.enums;

import java.util.Optional;

/**
 * 审核状态
 **/
public enum ProgMaterialAuditStatusEnum {

    /**
     *
     private static final int AUDIT_OK = 0;
     private static final int AUDIT_WAITING = 1;
     private static final int AUDIT_REJECTED = 2;
     */

    AUDIT_OK(0, "通过"),
    AUDIT_WAITING(1, "待审"),
    AUDIT_REJECTED(2, "驳回"),
    ;

    private final Integer code;
    private final String name;


    private ProgMaterialAuditStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static Optional<ProgMaterialAuditStatusEnum> getByCode(Integer code) {
        ProgMaterialAuditStatusEnum[] var1 = values();
        int var2 = var1.length;

        for (int var3 = 0; var3 < var2; ++var3) {
            ProgMaterialAuditStatusEnum creativeStyleEnum = var1[var3];
            if (creativeStyleEnum.code.equals(code)) {
                return Optional.of(creativeStyleEnum);
            }
        }

        return Optional.empty();
    }

    public String getValue() {
        return this.name;
    }

    public Integer getKey() {
        return this.code;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }
}
