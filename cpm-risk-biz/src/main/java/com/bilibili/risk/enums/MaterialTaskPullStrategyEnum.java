package com.bilibili.risk.enums;

import java.util.Optional;

/**
 * 素材拉取策略，内部用的
 **/
public enum MaterialTaskPullStrategyEnum {

    BY_SINGLE_QUEUE_ID(0, "按队列id拉取"),
    BY_RULE_PER_QUEUE(1, "按规则逐个队列领取"),
    BY_RULE_CTIME_ASC(2, "按规则时间正序领取");

    private final Integer code;
    private final String name;

    private MaterialTaskPullStrategyEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static Optional<MaterialTaskPullStrategyEnum> getByCode(Integer code) {
        MaterialTaskPullStrategyEnum[] var1 = values();
        int var2 = var1.length;

        for (int var3 = 0; var3 < var2; ++var3) {
            MaterialTaskPullStrategyEnum creativeStyleEnum = var1[var3];
            if (creativeStyleEnum.code.equals(code)) {
                return Optional.of(creativeStyleEnum);
            }
        }

        return Optional.empty();
    }

    public String getValue() {
        return this.name;
    }

    public Integer getKey() {
        return this.code;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }
}
