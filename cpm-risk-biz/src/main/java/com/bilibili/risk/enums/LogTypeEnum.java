package com.bilibili.risk.enums;

import java.util.Optional;

public enum LogTypeEnum {

    TASK(1, "任务"),
    RULE(2, "规则"),
    ;

    private final Integer code;
    private final String name;


    private LogTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static Optional<LogTypeEnum> getByCode(Integer code) {
        LogTypeEnum[] var1 = values();
        int var2 = var1.length;

        for (int var3 = 0; var3 < var2; ++var3) {
            LogTypeEnum creativeStyleEnum = var1[var3];
            if (creativeStyleEnum.code.equals(code)) {
                return Optional.of(creativeStyleEnum);
            }
        }

        return Optional.empty();
    }

    public String getValue() {
        return this.name;
    }

    public Integer getKey() {
        return this.code;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }
}
