package com.bilibili.risk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum FirePushAuditSourceEnum {

    STORY(1, "组建"),
    PAGE_GROUP(2, "落地页组"),
    ;

    private Integer code;

    private String desc;

    public static FirePushAuditSourceEnum getByCode(Integer code) {
        for (FirePushAuditSourceEnum firePushAuditSourceEnum : values()) {
            if (firePushAuditSourceEnum.getCode().equals(code)) {
                return firePushAuditSourceEnum;
            }
        }
        return null;
    }
}
