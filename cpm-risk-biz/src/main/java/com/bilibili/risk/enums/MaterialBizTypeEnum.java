package com.bilibili.risk.enums;

import com.bilibili.adp.common.util.Utils;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * 业务类型
 **/
public enum MaterialBizTypeEnum {

    UNKNOWN(0, "未知"),
    SANLIAN(1, "三连"),
    BIHUO(2, "必火"),
    ;

    private final Integer code;
    private final String name;


    private MaterialBizTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static Optional<MaterialBizTypeEnum> getByCode(Integer code) {
        MaterialBizTypeEnum[] var1 = values();
        int var2 = var1.length;

        for (int var3 = 0; var3 < var2; ++var3) {
            MaterialBizTypeEnum creativeStyleEnum = var1[var3];
            if (creativeStyleEnum.code.equals(code)) {
                return Optional.of(creativeStyleEnum);
            }
        }

        return Optional.empty();
    }

    public String getValue() {
        return this.name;
    }

    public Integer getKey() {
        return this.code;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

    public static Integer getBizTypeByBiliUserId(Integer biliUserId) {
        if (Utils.isPositive(biliUserId)) {
            return BIHUO.getCode();
        }
        return SANLIAN.getCode();
    }
}
