package com.bilibili.risk.enums;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * 任务状态
 **/
public enum MaterialTaskStatusEnum {

    /**
     * 负数的都是中间临时状态
     */
    // 用来触发审核按钮的中间状态，处理落地页组的情况
    TO_CALLBACK_AUDIT(-2, "待回调执行审核操作"),
    // 用来触发匹配队列的中间状态
    TO_MATCH_QUEUE(-1, "待匹配"),

    FREE(0, "游离"),
    DOING(1, "进行中"),
    COMPLETE(2, "完成"),
    STOP(3, "废弃"),
    ;

    private final Integer code;
    private final String name;

    /**
     * 未完成的正常状态
     *
     * @return
     */
    public static List<Integer> getNotCompleteStatusList() {
        return Arrays.asList(FREE.code, DOING.code);
    }

    /**
     * 中间临时状态
     *
     * @return
     */
    public static List<Integer> getTmpStatusList() {
        return Arrays.asList(TO_CALLBACK_AUDIT.code, TO_MATCH_QUEUE.code);
    }

    private MaterialTaskStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static Optional<MaterialTaskStatusEnum> getByCode(Integer code) {
        MaterialTaskStatusEnum[] var1 = values();
        int var2 = var1.length;

        for (int var3 = 0; var3 < var2; ++var3) {
            MaterialTaskStatusEnum creativeStyleEnum = var1[var3];
            if (creativeStyleEnum.code.equals(code)) {
                return Optional.of(creativeStyleEnum);
            }
        }

        return Optional.empty();
    }

    public String getValue() {
        return this.name;
    }

    public Integer getKey() {
        return this.code;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }
}
