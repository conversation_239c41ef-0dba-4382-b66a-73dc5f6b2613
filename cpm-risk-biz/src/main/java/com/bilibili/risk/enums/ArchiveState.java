package com.bilibili.risk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum ArchiveState {
    /**
     * 未知
     */
    UNKNOW(-404, "未知"),
    /**
     * 开放浏览
     */
    OPEN_BROWSE(0, "开放浏览"),
    /**
     * 橙色通过
     */
    ORANGE_THROUGH(1, "橙色通过"),
    /**
     *
     */
    VIP_ACCESS(10000, "会员可见"),
    /**
     * 待审
     */
    TO_BE_AUDIT(-1, "待审"),
    /**
     * 打回
     */
    REJECT(-2, "打回"),
    /**
     * 网警锁定
     */
    NETWORK_LOCK(-3, "网警锁定"),
    /**
     * 锁定
     */
    LOCK(-4, "锁定"),
    /**
     * 修复待审
     */
    REPAIR_PENDING(-6, "修复待审"),
    /**
     * 暂缓审核
     */
    SUSPENSION_AUDIT(-7, "暂缓审核"),
    /**
     * 转码中
     */
    TRANSCODING(-9, "转码中"),
    /**
     * 延迟发布
     */
    DELAY_RELEASE(-10, "延迟发布"),
    /**
     * 视频源待修
     */
    VIDEO_SOURCE_TO_BE_REPAIR(-11, "视频源待修"),
    /**
     * 允许评论待审
     */
    ALLOW_COMMENT_TO_BE_PENDING(-13, "允许评论待审"),
    /**
     * 分发中
     */
    DISTRIBUTION(-15, "分发中"),
    /**
     * 转码失败
     */
    TRANSCODING_FAILED(-16, "转码失败"),
    /**
     *
     */
    AUDIT_PASSED_WAITING_FOR_PUB(-20, "稿件已过审等待第三方通知发布"),
    /**
     * 创建提交
     */
    CREATE_SUBMIT(-30, "创建提交"),
    /**
     * UP主定时发布
     */
    UP_TIMED_RELEASE(-40, "UP主定时发布"),
    /**
     * UP主删除
     */
    UP_DELETED(-100, "UP主删除")
    ;
    private final int code;

    private final String desc;

    public static ArchiveState getByCode(Integer code) {
        return Arrays.stream(values()).filter(e -> Objects.equals(code, e.getCode())).findFirst()
                .orElse(UNKNOW);
    }

    public static final Set<Integer> VALID_STATE_CODE_SET = new HashSet<>(
            Arrays.asList(
                    OPEN_BROWSE.getCode(),
                    ORANGE_THROUGH.getCode(),
                    VIP_ACCESS.getCode(),
                    REPAIR_PENDING.getCode()
            )
    );

}
