package com.bilibili.risk.enums;

import com.bilibili.risk.constants.AuditTaskTreeMarkPatternConstants;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 素材类型
 * material type 老三连还有一套: com.bilibili.adp.launch.api.prog.enums.MaterialType
 **/
@Getter
public enum RiskMaterialTypeEnum {


    // lau_material 里的图片和gif 都归一成变成审核端的图片1
    IMAGE(1, "图片") {
        @Override
        public String generateMaterialContent(String bodyId) {
            // 为图片上传后的地址, 如http://i1.hdslb.com/bfs/archive/29e03d644d344cc6a0696a099d54fe67d3ee2b18.jpg
            return bodyId;
        }

        @Override
        public String parse(String content) {
            return content;
        }

        @Override
        public String splitJumpLink(String content) {
            return "";
        }
    },
    // 这个是投放端素材类型的title，变成了文本含义
    TEXT(4, "文本") {
        @Override
        public String generateMaterialContent(String bodyId) {
            // 文本内容
            return bodyId;
        }

        @Override
        public String parse(String content) {
            return content;
        }

        @Override
        public String splitJumpLink(String content) {
            return content;
        }
    },
    DYNAMIC(9, "动态") {
        @Override
        public String generateMaterialContent(String bodyId) {
            // dynamic-{dynamicId}
            return splitDynamicMaterialContent(bodyId);
        }

        @Override
        public String parse(String content) {
            // dynamic-{dynamicId}
            if (content == null || !content.startsWith(DYNAMIC_MATERIAL_PREFIX)) {
                return content;
            }
            String[] parts = content.split("-");
            if (parts.length != 2) {
                return content;
            }
            return parts[1];
        }

        @Override
        public String splitJumpLink(String content) {
            // return "https://t.bilibili.com/" + dynamicId + "?track_id=__TRACKID__";
            return AuditTaskTreeMarkPatternConstants.DYNAMIC_ORIGIN_URL_PREFIX + content;
        }
    },
    LIVE(10, "直播") {
        @Override
        public String generateMaterialContent(String bodyId) {
            // live-{roomId}
            return splitLiveMaterialContent(bodyId);
        }

        @Override
        public String parse(String content) {
            // (live-{roomId})
            if (content == null || !content.startsWith(LIVE_MATERIAL_PREFIX)) {
                return content;
            }
            String[] parts = content.split("-");
            if (parts.length != 2) {
                return content;
            }
            return parts[1];
        }

        @Override
        public String splitJumpLink(String content) {
            return AuditTaskTreeMarkPatternConstants.LIVE_ROOM_URL_PREFIX + content;
        }
    },
    // 投放端原来的 ARCHIVE_AND_COVER(5, "ARCHIVE_AND_COVER"),ARCHIVE(8, "ARCHIVE"), 不适用于审核端，新加了一个12稿件/视频
    VIDEO(12, "稿件") {
        @Override
        public String generateMaterialContent(String bodyId) {
            // avid-{avid}
            return splitVideoMaterialContent(bodyId);
        }

        @Override
        public String parse(String content) {
            // (avid-{avid})
            if (content == null || !content.startsWith(AVID_MATERIAL_PREFIX)) {
                return content;
            }
            String[] parts = content.split("-");
            if (parts.length != 2) {
                return content;
            }
            return parts[1];
        }

        @Override
        public String splitJumpLink(String content) {
            // https://www.bilibili.com/video/av
            return AuditTaskTreeMarkPatternConstants.BV_PLAY_URL_PREFIX + content;
        }
    },
    // 用户自己填写的 url，根据规定的几种 url 类型解析id,除此以外的url都不解析
    // 未实现，实现在: com.bilibili.risk.enums.PromotionPurposeContentProcessorEnum
    LINK(11, "链接") {
        @Override
        public String generateMaterialContent(String bodyId) {
            // 格式: link-{subType}-{id}, link-{subType}-{url}
            return bodyId;
        }

        @Override
        public String parse(String content) {
            // 解析不出来的返回的是url自己
            return content;
        }

        @Override
        public String splitJumpLink(String content) {
            return content;
        }
    },
    // 三方落地页组下page
    PAGE_GROUP_PAGE(14, "三方落地页组") {
        // material表的 content 前面就拼接成 {groupId}-{pageId} 了
        @Override
        public String generateMaterialContent(String bodyId) {
            // pageGroupPage-{groupId}-{pageId}
            return splitPageGroupPageMaterialContent(bodyId);
        }

        // 解析 pageId
        @Override
        public String parse(String content) {
            // ( pageGroupPage-{groupId}-{pageId})
            if (content == null || !content.startsWith(PAGE_GROUP_PAGE_MATERIAL_PREFIX)) {
                return "";
            }
            // 正则表达式匹配模式
            Pattern pattern = Pattern.compile("pageGroupPage-(\\d+)-(\\d+)");
            Matcher matcher = pattern.matcher(content);

            if (matcher.find()) {
                return matcher.group(2); // 第2个捕获组是 pageId
            } else {
                // 如果没有匹配到，返回原始内容
                return "";
            }
        }

        /**
         * 三方落地页的需要去建站获取
         * @param content
         * @return
         */
        @Deprecated
        @Override
        public String splitJumpLink(String content) {
            return AuditTaskTreeMarkPatternConstants.MGK_GAONENG_URL_PREFIX + content;
        }
    },
    UNKNOWN(-1, "未知") {
        @Override
        public String generateMaterialContent(String bodyId) {
            return bodyId;
        }

        @Override
        public String parse(String content) {
            return content;
        }

        @Override
        public String splitJumpLink(String content) {
            return content;
        }
    };

    public static String splitPageGroupPageMaterialContent(String bodyId) {
        return PAGE_GROUP_PAGE_MATERIAL_PREFIX + bodyId;
    }

    public static Long getPageGroupId(String materialContent){
        if(null == materialContent){
            return null;
        }

        if(!materialContent.startsWith(PAGE_GROUP_PAGE_MATERIAL_PREFIX)){
            return null;
        }

        String groupIdStr = materialContent.substring(materialContent.indexOf("-") + 1, materialContent.lastIndexOf("-"));
        return Long.valueOf(groupIdStr);
    }


    public static String splitVideoMaterialContent(String bodyId) {
        return AVID_MATERIAL_PREFIX + bodyId;
    }


    public static String splitLiveMaterialContent(String bodyId) {
        return LIVE_MATERIAL_PREFIX + bodyId;
    }

    public static final String AVID_MATERIAL_PREFIX = "avid-";
    public static final String LIVE_MATERIAL_PREFIX = "live-";
    public static final String LINK_MATERIAL_PREFIX = "link";
    public static final String LINK_OTHER_MATERIAL_PREFIX = "link-other-";
    public static final String DYNAMIC_MATERIAL_PREFIX = "dynamic-";
    public static final String PAGE_GROUP_PAGE_MATERIAL_PREFIX = "pageGroupPage-";

    public static String splitDynamicMaterialContent(String bodyId) {
        return DYNAMIC_MATERIAL_PREFIX + bodyId;
    }

    public static String splitPageGroupBodyId(Long groupId, Long pageId) {
        return groupId + "-" + pageId;
    }

    // 可以解析出主体id的类型
    public static List<Integer> getCanParseBodyIdTypes() {
        return Arrays.asList(DYNAMIC.getCode(), LIVE.getCode(), VIDEO.getCode(), LINK.getCode(), PAGE_GROUP_PAGE.getCode());
    }

    private final Integer code;
    private final String desc;

    // 生成 material content,入参有id的一般是前缀-id，如avid-{avid}，dynamic-{dynamicId}
    public abstract String generateMaterialContent(String bodyId);

    // 解析出 lau_material.content 里的 id，
    public abstract String parse(String content);

    // 根据解析出来的 id 拼接出对应的 url，注意url解析不出来的
    public abstract String splitJumpLink(String content);

    RiskMaterialTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static RiskMaterialTypeEnum getByCode(Integer code) {
        for (RiskMaterialTypeEnum bean : values()) {
            if (code.equals(bean.getCode())) {
                return bean;
            }
        }
        return UNKNOWN;
    }

    public static String extractUrlFromRawContent(String rawUrl) {
        if (rawUrl == null) {
            return null;
        }

        // 检查是否以 http:// 或 https:// 开头
        if (!rawUrl.startsWith("http://") && !rawUrl.startsWith("https://")) {
            return rawUrl;
        }

        // 查找第一个 ? 的位置
        int queryParamIndex = rawUrl.indexOf('?');

        // 如果没有 ?，返回整个 URL；否则截取到 ? 之前
        return (queryParamIndex == -1) ? rawUrl : rawUrl.substring(0, queryParamIndex);
    }

    public static String extractHttpUrlFromMaterialContent(String input) {
        // 查找 "http" 或 "https" 的位置
        int httpIndex = input.indexOf("http://");
        int httpsIndex = input.indexOf("https://");

        // 确定有效的起始位置
        int startIndex = -1;
        if (httpIndex != -1 && httpsIndex != -1) {
            startIndex = Math.min(httpIndex, httpsIndex); // 取更早出现的
        } else if (httpIndex != -1) {
            startIndex = httpIndex;
        } else if (httpsIndex != -1) {
            startIndex = httpsIndex;
        } else {
            return input; // 没有 http/https，直接返回
        }

        // 从 startIndex 开始查找第一个 ?
        int queryParamIndex = input.indexOf('?', startIndex);

        // 截取到 ? 或字符串末尾
        return (queryParamIndex == -1)
                ? input.substring(startIndex)
                : input.substring(startIndex, queryParamIndex);
    }

    public static void main(String[] args) {
        String a = "pageGroupPage-1026138905828007936-1026138906004168704";
        System.out.println(a.substring(a.indexOf("-") + 1));
    }
}
