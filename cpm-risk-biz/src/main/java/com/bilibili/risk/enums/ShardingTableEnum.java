package com.bilibili.risk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;


@Getter
@AllArgsConstructor
public enum ShardingTableEnum {

    TABLE_LAU_MATERIAL_AUDIT_TASK("lau_material_audit_task", 128, "taskShardingAlgorithm", "创意素材的任务表"),
    TABLE_LAU_MATERIAL_AUDIT("lau_material_audit", 32, "materialShardingAlgorithm","素材表"),
    TABLE_LAU_MATERIAL_CREATIVE_REL("lau_material_creative_rel",64, "materialCreativeRelShardingAlgorithm", "素材创意关系表"),
    ;

    private final String tableName;

    // 分片数
    private final Integer shardingNum;

    // 分片算法
    private final String shardingAlgorithm;

    private final String desc;

}
