package com.bilibili.risk.enums;

import java.util.Optional;

/**
 * 标签等级
 **/
public enum AuditLabelLevelEnum {

    ONE(1L, "一级"),
    TWO(2L, "二级"),
    THREE(3L, "三级"),
    ;

    private final Long code;
    private final String name;


    private AuditLabelLevelEnum(Long code, String name) {
        this.code = code;
        this.name = name;
    }

    public static Optional<AuditLabelLevelEnum> getByCode(Long code) {
        AuditLabelLevelEnum[] var1 = values();
        int var2 = var1.length;

        for (int var3 = 0; var3 < var2; ++var3) {
            AuditLabelLevelEnum creativeStyleEnum = var1[var3];
            if (creativeStyleEnum.code.equals(code)) {
                return Optional.of(creativeStyleEnum);
            }
        }

        return Optional.empty();
    }

    public String getValue() {
        return this.name;
    }

    public Long getKey() {
        return this.code;
    }

    public Long getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }
}
