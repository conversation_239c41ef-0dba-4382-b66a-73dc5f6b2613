package com.bilibili.risk.enums;

import java.util.Optional;

public enum OperationTypeEnum {

    ENTER_AUDIT_TO_MATCH(0, "进审待匹配"),
    ENTER_AUDIT(1, "进审"),
    ACCEPT(2, "领取"),
    AUDIT(3, "提交"),
    TIMEOUT(4, "自动释放"),
    REUSE_RESULT(5, "复用结果"),
    AUDIT_TO_EXECUTE(6, "提交待修改落地页"),
    AUDIT_TIMEOUT(7, "提交并释放"),

    ADD(100, "新增"),
    DELETE(101, "删除"),
    UPDATE(102, "修改"),


    ;

    private final Integer code;
    private final String name;


    private OperationTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static Optional<OperationTypeEnum> getByCode(Integer code) {
        OperationTypeEnum[] var1 = values();
        int var2 = var1.length;

        for (int var3 = 0; var3 < var2; ++var3) {
            OperationTypeEnum creativeStyleEnum = var1[var3];
            if (creativeStyleEnum.code.equals(code)) {
                return Optional.of(creativeStyleEnum);
            }
        }

        return Optional.empty();
    }

    public String getValue() {
        return this.name;
    }

    public Integer getKey() {
        return this.code;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }
}
