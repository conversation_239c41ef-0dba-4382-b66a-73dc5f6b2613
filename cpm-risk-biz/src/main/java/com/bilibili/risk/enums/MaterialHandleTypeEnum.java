package com.bilibili.risk.enums;

import com.bilibili.risk.bo.LauMaterialAuditLabelBo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 素材处置类型:1通过2通过且带标3驳回
 **/
public enum MaterialHandleTypeEnum {

    NO_HANDLE(0, "不处理", ProgMaterialAuditStatusEnum.AUDIT_WAITING.getCode()), // 目前不会出现
    PASS(2, "通过", ProgMaterialAuditStatusEnum.AUDIT_OK.getCode()),
    REJECT(3, "驳回", ProgMaterialAuditStatusEnum.AUDIT_REJECTED.getCode()),
    ;

    private final Integer code;
    private final String name;
    private final Integer progMaterialAuditStatus;


    private MaterialHandleTypeEnum(Integer code, String name, Integer progMaterialAuditStatus) {
        this.code = code;
        this.name = name;
        this.progMaterialAuditStatus = progMaterialAuditStatus;
    }

    public static Optional<MaterialHandleTypeEnum> getByCode(Integer code) {
        MaterialHandleTypeEnum[] var1 = values();
        int var2 = var1.length;

        for (int var3 = 0; var3 < var2; ++var3) {
            MaterialHandleTypeEnum creativeStyleEnum = var1[var3];
            if (creativeStyleEnum.code.equals(code)) {
                return Optional.of(creativeStyleEnum);
            }
        }

        return Optional.empty();
    }

    public static Optional<MaterialHandleTypeEnum> getByName(String name) {
        MaterialHandleTypeEnum[] var1 = values();
        int var2 = var1.length;

        for (int var3 = 0; var3 < var2; ++var3) {
            MaterialHandleTypeEnum creativeStyleEnum = var1[var3];
            if (creativeStyleEnum.name.equals(name)) {
                return Optional.of(creativeStyleEnum);
            }
        }

        return Optional.of(NO_HANDLE);
    }

    public static String pandoraReason(List<LauMaterialAuditLabelBo> auditLabelBoList){
        if(CollectionUtils.isEmpty(auditLabelBoList)){
            return StringUtils.EMPTY;
        }

        // 排序，保证不同顺序的标签输出结果一样
        auditLabelBoList = auditLabelBoList.stream().sorted(Comparator.comparing(LauMaterialAuditLabelBo::getId)).collect(Collectors.toList());

        StringBuilder reason = new StringBuilder();
        for (LauMaterialAuditLabelBo labelBo : auditLabelBoList) {
            if (REJECT == getByCode(labelBo.getHandleType()).orElse(NO_HANDLE)) {
                reason.append(labelBo.getReason()).append(",");
            }
        }

        if (reason.length() > 0) {
            reason.deleteCharAt(reason.length() - 1); // 删除最后一个逗号
        }

        return reason.toString();
    }

    public String getValue() {
        return this.name;
    }

    public Integer getKey() {
        return this.code;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

    public Integer getProgMaterialAuditStatus() {
        return progMaterialAuditStatus;
    }
}
