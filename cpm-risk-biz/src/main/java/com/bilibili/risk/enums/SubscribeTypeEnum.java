package com.bilibili.risk.enums;

import java.util.Optional;

public enum SubscribeTypeEnum {

    SUBSCRIBE(1, "订阅"),
    CANCEL_SUBSCRIBE(0, "取消订阅"),
    ;

    private final Integer code;
    private final String name;


    private SubscribeTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static Optional<SubscribeTypeEnum> getByCode(Integer code) {
        SubscribeTypeEnum[] var1 = values();
        int var2 = var1.length;

        for (int var3 = 0; var3 < var2; ++var3) {
            SubscribeTypeEnum creativeStyleEnum = var1[var3];
            if (creativeStyleEnum.code.equals(code)) {
                return Optional.of(creativeStyleEnum);
            }
        }

        return Optional.empty();
    }

    public String getValue() {
        return this.name;
    }

    public Integer getKey() {
        return this.code;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }
}
