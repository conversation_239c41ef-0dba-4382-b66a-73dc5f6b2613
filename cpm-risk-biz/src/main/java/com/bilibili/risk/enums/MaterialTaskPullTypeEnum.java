package com.bilibili.risk.enums;

import java.util.Optional;

/**
 * 素材任务领取方式
 **/
public enum MaterialTaskPullTypeEnum {

    UNKNOWN(0, "未知"),
    BY_QUEUES(1, "逐个队列领取"),
    BY_CTIME_ASC(2, "时间正序领取");

    private final Integer code;
    private final String name;

    private MaterialTaskPullTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static Optional<MaterialTaskPullTypeEnum> getByCode(Integer code) {
        MaterialTaskPullTypeEnum[] var1 = values();
        int var2 = var1.length;

        for (int var3 = 0; var3 < var2; ++var3) {
            MaterialTaskPullTypeEnum creativeStyleEnum = var1[var3];
            if (creativeStyleEnum.code.equals(code)) {
                return Optional.of(creativeStyleEnum);
            }
        }

        return Optional.empty();
    }

    public String getValue() {
        return this.name;
    }

    public Integer getKey() {
        return this.code;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }
}
