package com.bilibili.risk.service.refresh;

import com.bilibili.risk.dao.risk.LauMaterialAuditDao;
import com.bilibili.risk.dao.risk.LauMaterialCreativeRelDao;
import com.bilibili.risk.service.material.LauMaterialAuditService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class DeleteShardingDataService {

    private final LauMaterialAuditDao lauMaterialAuditDao;
    private final LauMaterialCreativeRelDao lauMaterialCreativeRelDao;
    private final LauMaterialAuditService lauMaterialAuditService;

    public void deleteData() {

    }
}
