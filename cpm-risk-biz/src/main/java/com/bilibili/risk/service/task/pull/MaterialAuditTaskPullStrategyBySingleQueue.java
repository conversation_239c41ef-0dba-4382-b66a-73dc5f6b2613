package com.bilibili.risk.service.task.pull;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.rbac.api.dto.UserDto;
import com.bilibili.risk.bo.*;
import com.bilibili.risk.bo.msg.MaterialTaskTimeoutMsgBo;
import com.bilibili.risk.constant.DatabaseConstant;
import com.bilibili.risk.constant.RiskConstants;
import com.bilibili.risk.convertor.IMaterialAuditTaskConvertor;
import com.bilibili.risk.enums.LogTypeEnum;
import com.bilibili.risk.enums.MaterialTaskPullStrategyEnum;
import com.bilibili.risk.enums.MaterialTaskStatusEnum;
import com.bilibili.risk.enums.OperationTypeEnum;
import com.bilibili.risk.po.es.LauMaterialAuditTaskEsPo;
import com.bilibili.risk.po.risk.LauMaterialAuditTaskPo;
import com.bilibili.risk.po.risk.RiskMaterialTaskDoingPo;
import com.bilibili.risk.service.role.RiskRoleService;
import com.bilibili.risk.utils.DiffLogUtils;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.internal.util.Assert;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
public class MaterialAuditTaskPullStrategyBySingleQueue extends AbstractMaterialAuditTaskPullStrategy {

    /**
     * 根据队列 id 拉取一批任务
     *
     * @param pullQueryBo
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class, transactionManager = DatabaseConstant.RISK_TRANSACTION_MANAGER)
    public List<MaterialAuditTaskEsBo> pullOneBatchMaterialAuditTasks(MaterialAuditTaskPullQueryBo pullQueryBo) {
        log.info("pullOneBatchMaterialAuditTasks[根据队列], pullQueryBo:{}", JSON.toJSONString(pullQueryBo));
        Assert.notNull(pullQueryBo, "pullQueryBo is null");
        Assert.notNull(pullQueryBo.getPullStrategy(), "pullStrategy is null");

        Assert.isTrue(Objects.equals(pullQueryBo.getPullStrategy(), MaterialTaskPullStrategyEnum.BY_SINGLE_QUEUE_ID.getKey()), "pullStrategy is not BY_QUEUE_ID");
        Assert.isTrue(Utils.isPositive(pullQueryBo.getQueueId()), "queueId 未传");
        Assert.isTrue(!StringUtils.isEmpty(pullQueryBo.getUsername()), "username 未传");

        LauMaterialAuditQueueBo auditQueueBo = materialAuditQueueService.fetchQueueBo(pullQueryBo.getQueueId());
        Assert.notNull(auditQueueBo, "队列不存在！");
        Assert.isTrue(!StringUtils.isEmpty(auditQueueBo.getRoleIds()), "队列未绑定角色！");
        Assert.isTrue(!bizConfig.getSpecialQueueIds().contains(auditQueueBo.getId()), "等待队列和兜底队列的任务不允许领取！");

        checkRole(pullQueryBo, auditQueueBo);

        Long timeoutMin = materialTaskUtils.calculateTimeoutMin(auditQueueBo.getTaskTimeout());

        // 预拉取不走这里，直接拉取游离的，因为es延迟
        if (!Utils.is(pullQueryBo.isPrePull())) {
            // 已经分配了任务，直接返回
            List<RiskMaterialTaskDoingPo> riskMaterialTaskDoingPos = riskMaterialTaskDoingService.queryByUsernameAndQueueId(pullQueryBo.getUsername(), pullQueryBo.getQueueId());
            if (!CollectionUtils.isEmpty(riskMaterialTaskDoingPos)) {
                List<String> taskIds = riskMaterialTaskDoingPos.stream().map(t -> t.getTaskId()).distinct().collect(Collectors.toList());

                // redis 过滤，因为es延迟问题
//            redisFilterJustAuditTasks(notCompleteTaskPageResult);
                List<LauMaterialAuditTaskEsPo> esPos = queryDoingTasks(taskIds);

                // 多个batch的话，取第一个group的
                List<LauMaterialAuditTaskEsPo> notCompleteTaskEsPos = fetchOneBatchIfExistManyBatchDoing(esPos);
                if (!CollectionUtils.isEmpty(notCompleteTaskEsPos)) {
                    List<MaterialAuditTaskEsBo> auditTaskEsBos = IMaterialAuditTaskConvertor.INSTANCE.pos2bos(notCompleteTaskEsPos);
                    // 非预拉取，则异步拉取下一批
                    if (!pullQueryBo.isPrePull() && !pullQueryBo.isPullByRule()) {
                        MaterialAuditTaskPullQueryBo prePullQueryBo = IMaterialAuditTaskConvertor.INSTANCE.copy(pullQueryBo);
                        prePullQueryBo.setPrePull(true);
                        prePullQueryBo.setTaskIds(auditTaskEsBos.stream().map(t -> t.getId()).collect(Collectors.toList()));
                        materialTaskPrePullMsgPub.pub(prePullQueryBo);
                    }
                    // 超时任务处理延迟消息
                    materialTaskTimeoutPub.pub(MaterialTaskTimeoutMsgBo.builder()
                            .batchNo(notCompleteTaskEsPos.get(0).getReceiveBatchNo())
                            .timeoutMins(timeoutMin)
                            .build());
                    log.info("pullOneBatchMaterialAuditTasks[已经分配了任务，直接返回]，taskIds:{}", notCompleteTaskEsPos.stream().map(t -> t.getId()).collect(Collectors.toList()));
                    return auditTaskEsBos;
                }
            }
        }

        // todo simer: 最多预拉取一批

        // 从Redis获取该队列已完成的任务ID列表，用来拉取的时候not in
        Set<String> queuesJustCompleteTaskIdsFromRedis = pullRedisZsetService.getQueueJustCompleteTaskIdsFromRedis(pullQueryBo.getQueueId());
        if (!CollectionUtils.isEmpty(queuesJustCompleteTaskIdsFromRedis)) {
            pullQueryBo.setTaskIds(queuesJustCompleteTaskIdsFromRedis.stream().collect(Collectors.toList()));
        }

        // 没有分配任务，拉取一批任务
        MaterialAuditTaskCommonQueryBo pullOneBatchQueryBo = MaterialAuditTaskCommonQueryBo.builder()
                .queueIds(Arrays.asList(pullQueryBo.getQueueId()))
                .statusList(Arrays.asList(MaterialTaskStatusEnum.FREE.getCode())) // 游离
                .page(1)
                .pageSize(auditQueueBo.getPullNum().intValue())
                .excludeTaskIds(pullQueryBo.getTaskIds())
                .build();
        List<LauMaterialAuditTaskEsPo> auditTaskEsPosByAccount = materialAuditTaskEsService.queryAuditTasksAggAccountBySingleQueue(pullOneBatchQueryBo);
        log.info("pullOneBatchMaterialAuditTasks[拉取游离任务]，size:{}", auditTaskEsPosByAccount.size());
        if (!CollectionUtils.isEmpty(auditTaskEsPosByAccount)) {
            List<String> taskIds = auditTaskEsPosByAccount.stream().map(t -> t.getId()).collect(Collectors.toList());

            // 查询db任务
            Map<String, LauMaterialAuditTaskPo> auditTaskPoMap = materialAuditTaskDbService.queryMapByTaskIds(taskIds);

            // 乐观锁更新准备参数
            String batchNo = snowFlakeNoService.generateNo();
            List<LauMaterialAuditTaskUpdateBo> auditTaskUpdateBos = buildMaterialTaskUpdateBos(pullQueryBo, auditTaskEsPosByAccount, auditTaskPoMap, batchNo);
            Set<String> candidateTaskIdSet = auditTaskUpdateBos.stream().map(t -> t.getTaskId()).collect(Collectors.toSet());
//            auditTaskEsPosByAccount = auditTaskEsPosByAccount.stream().filter(t -> candidateTaskIdSet.contains(t.getId())).collect(Collectors.toList());

            // 乐观锁更新
            Integer count = materialAuditTaskDbService.updateMaterialAuditTaskBosOptimistic(auditTaskUpdateBos);

            // 更新数量小于拉取数量，说明有任务被人领取了
            Map<String, LauMaterialAuditTaskPo> newAuditTaskPoMap = null;
            if (count < taskIds.size()) {
                newAuditTaskPoMap = materialAuditTaskDbService.queryMapByTaskIds(taskIds);
            }
            // 过滤一下，防止并发领取情况
            for (LauMaterialAuditTaskUpdateBo updateBo : auditTaskUpdateBos) {
                if (newAuditTaskPoMap != null) {
                    LauMaterialAuditTaskPo auditTaskPo = newAuditTaskPoMap.get(updateBo.getTaskId());
                    if (auditTaskPo == null) {
                        candidateTaskIdSet.remove(updateBo.getTaskId());
                        log.error("pullOneBatchMaterialAuditTasks[拉取任务失败，db中没有任务]，taskId:{}", updateBo.getTaskId());
                        continue;
                    }
                    if (!Objects.equals(auditTaskPo.getReceiveBatchNo(), batchNo)) {
                        candidateTaskIdSet.remove(updateBo.getTaskId());
                        log.info("pullOneBatchMaterialAuditTasks[任务已被被人拉取]，taskId:{}, batchNo:{},acceptName={}", updateBo.getTaskId(), batchNo, auditTaskPo.getAcceptName());
                        continue;
                    }
                }

                LauMaterialAuditTaskPo oldAuditTaskPo = auditTaskPoMap.get(updateBo.getTaskId());
                LauMaterialAuditTaskUpdateBo oldUpdateBo = IMaterialAuditTaskConvertor.INSTANCE.po2UpdateBo(oldAuditTaskPo);
                String diffStr = DiffLogUtils.generateDiffField(oldUpdateBo, updateBo, "任务对象");

                // 按任务维度记操作日志
                MaterialAuditTaskOperationLogBo logBo = MaterialAuditTaskOperationLogBo.builder()
                        .objId(updateBo.getTaskId())
                        .operationType(OperationTypeEnum.ACCEPT.getCode())
                        .operatorUsername(pullQueryBo.getUsername())
                        .remark(JSON.toJSONString(updateBo))
                        .diff(diffStr)
                        .type(LogTypeEnum.TASK.getCode())
                        .systemType(RiskConstants.SYSTEM_TYPE_RISK)
                        .ctime(System.currentTimeMillis())
                        .build();
                operationLogService.saveLog(logBo);
            }
            // 过滤掉已经被领取的任务
            auditTaskEsPosByAccount = auditTaskEsPosByAccount.stream().filter(t -> candidateTaskIdSet.contains(t.getId())).collect(Collectors.toList());

            // 保存到doing表
            List<RiskMaterialTaskDoingPo> doingPos = new ArrayList<>();
            for (LauMaterialAuditTaskEsPo taskEsPo : auditTaskEsPosByAccount) {
                Timestamp now = Utils.getNow();
                RiskMaterialTaskDoingPo doingPo = RiskMaterialTaskDoingPo.builder()
                        .taskId(taskEsPo.getId())
                        .username(pullQueryBo.getUsername())
                        .queueId(taskEsPo.getQueueId())
                        .receiveBatchNo(taskEsPo.getReceiveBatchNo())
                        .ctime(now)
                        .mtime(now)
                        .build();
                doingPos.add(doingPo);
            }
            riskMaterialTaskDoingService.insert(doingPos);

            // 超时任务处理延迟消息
            materialTaskTimeoutPub.pub(MaterialTaskTimeoutMsgBo.builder()
                    .batchNo(batchNo)
                    .timeoutMins(timeoutMin)
                    .build());
        }

        List<MaterialAuditTaskEsBo> auditTaskEsBos = IMaterialAuditTaskConvertor.INSTANCE.pos2bos(auditTaskEsPosByAccount);

        // 拉取的任务id放入redis(解决拉取任务时，es数据延迟的问题)
        pullRedisZsetService.cacheCompletedTaskIds(IMaterialAuditTaskConvertor.INSTANCE.esPos2UpdateBos(auditTaskEsBos));

        // 非预拉取，则异步拉取下一批
        if (!pullQueryBo.isPrePull() && !pullQueryBo.isPullByRule()) {
            MaterialAuditTaskPullQueryBo prePullQueryBo = IMaterialAuditTaskConvertor.INSTANCE.copy(pullQueryBo);
            prePullQueryBo.setPrePull(true);
            prePullQueryBo.setTaskIds(auditTaskEsBos.stream().map(t -> t.getId()).collect(Collectors.toList()));
            materialTaskPrePullMsgPub.pub(prePullQueryBo);
        }
        return auditTaskEsBos;
    }

    protected void checkRole(MaterialAuditTaskPullQueryBo pullQueryBo, LauMaterialAuditQueueBo auditQueueBo) {
        // 角色权限，按逐个队列的话，跳过校验，因为规则的权限校验在前面已经根据规则层级绑定的角色校验过了
        if (Objects.equals(pullQueryBo.getPullStrategy(), MaterialTaskPullStrategyEnum.BY_SINGLE_QUEUE_ID.getKey()) && !pullQueryBo.isSkipRoleCheck() && !pullQueryBo.isPullByRule()) {
            UserDto user = userService.getUserByUserName(bizConfig.getTenantId(), pullQueryBo.getUsername());
            Assert.notNull(user, "用户不存在");

            List<Integer> roleIdsOfUser = user.getRoles().stream().map(r -> r.getId()).distinct().collect(Collectors.toList());

            // 业务管理员直接可以操作
            if (roleIdsOfUser.contains(bizConfig.getBusinessAdminRoleId())) {
                return;
            }

            // 非业务管理员，要求用户roleIds与队列roleIds存在交集
            List<Integer> queueRoleIds = Arrays.stream(auditQueueBo.getRoleIds().split(",")).map(String::trim).map(Integer::parseInt).collect(Collectors.toList());
            List<Integer> allRoleIds = Stream.concat(queueRoleIds.stream(), roleIdsOfUser.stream()).collect(Collectors.toList());
            List<Integer> intersectionRoleIds = roleIdsOfUser.stream()
                    .filter(queueRoleIds::contains)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(intersectionRoleIds)) {

                Map<Integer, RoleBo> roleBoMap = riskRoleService.queryRoleBoMapByIds(allRoleIds);
                String roleNameOfUser = roleIdsOfUser.stream().map(r -> roleBoMap.get(r)).filter(Objects::nonNull).map(t -> t.getName()).collect(Collectors.joining(","));
                String roleNameOfQueue = queueRoleIds.stream().map(r -> roleBoMap.get(r)).filter(Objects::nonNull).map(t -> t.getName()).collect(Collectors.joining(","));
                log.warn("用户没有队列权限，queueId:{}, username:{},用户权限={},队列权限={}", pullQueryBo.getQueueId(), pullQueryBo.getUsername(), roleNameOfUser, roleNameOfQueue);
                throw new RuntimeException("用户没有队列权限");
            }
        }
    }
}
