package com.bilibili.risk.service.material_creative;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.risk.bo.LauMaterialCreativeRelBo;
import com.bilibili.risk.constant.DatabaseConstant;
import com.bilibili.risk.convertor.IMaterialCreativeRelConvertor;
import com.bilibili.risk.dao.risk.LauMaterialCreativeRelDao;
import com.bilibili.risk.po.risk.LauMaterialCreativeRelPo;
import com.bilibili.risk.po.risk.LauMaterialCreativeRelPoExample;
import com.bilibili.risk.utils.MaterialTaskUtils;
import edu.emory.mathcs.backport.java.util.Collections;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class MaterialCreativeRelService {

    private final LauMaterialCreativeRelDao lauMaterialCreativeRelDao;

    /**
     * 根据material id查询rel列表
     * 注意：一个material对应创意可能较多，慢查询，
     *
     * @param materialId
     * @param creativeId 可能为空
     * @return
     */
    public LauMaterialCreativeRelPo queryListByMaterialId(String materialId, Integer creativeId) {

        if (StringUtils.isBlank(materialId) || !Utils.isPositive(creativeId)) {
            throw new RuntimeException("materialId or materialIds is not positive");
        }

        LauMaterialCreativeRelPoExample example = new LauMaterialCreativeRelPoExample();
        example.createCriteria().andMaterialIdEqualTo(materialId).andCreativeIdEqualTo(creativeId);

        List<LauMaterialCreativeRelPo> result = lauMaterialCreativeRelDao.selectByExample(example);
        if (CollectionUtils.isEmpty(result)) {
            return null;
        }

        // 条件是唯一索引
        return result.get(0);
    }

    public List<LauMaterialCreativeRelPo> scrollQuery(String materialId, Integer lastCreativeId, int limit, Integer isDeleted) {
        LauMaterialCreativeRelPoExample example = new LauMaterialCreativeRelPoExample();
        LauMaterialCreativeRelPoExample.Criteria criteria = example.createCriteria().andMaterialIdEqualTo(materialId);
        if (Utils.isPositive(lastCreativeId)) {
            criteria.andCreativeIdGreaterThan(lastCreativeId);
        }
        if (isDeleted != null) {
            criteria.andIsDeletedEqualTo(isDeleted);
        }

        example.setLimit(limit);
        example.setOrderByClause("creative_id asc");

        return lauMaterialCreativeRelDao.selectByExample(example);
    }

    public long countByMaterialId(String materialId, Integer isDeleted) {
        if (StringUtils.isBlank(materialId)) {
            return 0;
        }

        LauMaterialCreativeRelPoExample example = new LauMaterialCreativeRelPoExample();
        LauMaterialCreativeRelPoExample.Criteria criteria = example.createCriteria().andMaterialIdEqualTo(materialId);
        if (isDeleted != null) {
            criteria.andIsDeletedEqualTo(isDeleted);
        }
        return lauMaterialCreativeRelDao.countByExample(example);
    }

    public void insertUpdateBatch(List<LauMaterialCreativeRelBo> relBos){
        if(CollectionUtils.isEmpty(relBos)){
            return;
        }

        List<LauMaterialCreativeRelPo> pos = IMaterialCreativeRelConvertor.INSTANCE.bos2pos(relBos);
        lauMaterialCreativeRelDao.insertUpdateBatch(pos);

    }
}
