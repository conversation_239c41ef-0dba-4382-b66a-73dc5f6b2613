package com.bilibili.risk.service.refresh;

import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.risk.bo.LauMaterialAuditBo;
import com.bilibili.risk.bo.MaterialAuditTaskCommonQueryBo;
import com.bilibili.risk.enums.AuditLabelIdSpecialEnum;
import com.bilibili.risk.enums.MaterialTaskStatusEnum;
import com.bilibili.risk.po.es.LauMaterialAuditTaskEsPo;
import com.bilibili.risk.po.risk.LauMaterialAuditPo;
import com.bilibili.risk.po.risk.LauMaterialAuditTaskPo;
import com.bilibili.risk.service.material.LauMaterialAuditService;
import com.bilibili.risk.service.task.MaterialAuditTaskDbService;
import com.bilibili.risk.service.task.MaterialAuditTaskEsService;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class MaterialAuditDataSyncRefreshService {

    public static final int PAGE_SIZE = 200;
    private final MaterialAuditTaskEsService materialAuditTaskEsService;
    private final MaterialAuditTaskDbService materialAuditTaskDbService;

    public void asyncsyncDb2Es(List<String> taskIds) {
        new Thread(() -> {
            try {
                syncDb2Es(taskIds);
            } catch (Exception e) {
                log.error("asyncsyncDb2Es[异常]，taskIds.size:{}, error:{}", taskIds.size(), e);
            }
        }).start();
    }

    /**
     * 刷数据，将待审素材的三级标签用任务的进行刷
     */
    @SneakyThrows
    public void syncDb2Es(List<String> taskIds) {
        log.info("syncDb2Es[开始]，taskIds.size:{}", taskIds.size());
        // 分批查询 es 数据
        PageResult<LauMaterialAuditTaskEsPo> pageResult = null;
        int page = 1;
        int totalUpdateCount = 0;
        int totalTaskUpdateCount = 0;
        do {
            MaterialAuditTaskCommonQueryBo queryBo = MaterialAuditTaskCommonQueryBo.builder()
                    .page(page)
                    .pageSize(PAGE_SIZE)
                    .ids(taskIds)
                    .build();
            pageResult = materialAuditTaskEsService.queryMaterialAuditTaskEsPosCommon(queryBo);
            List<LauMaterialAuditTaskEsPo> records = pageResult.getRecords();
            if (CollectionUtils.isEmpty(records)) {
                log.info("syncDb2Es[没有查询到数据]，taskIds.size:{}, page:{}, pageSize:{}", taskIds.size(), page, PAGE_SIZE);
                break;
            }
            log.info("syncDb2Es[查询到数据]，taskIds.size:{}, page:{}, pageSize:{}, records.size:{}", taskIds.size(), page, PAGE_SIZE, records.size());
            List<String> taskIdsOneBatch = records.stream().map(t -> t.getId()).collect(Collectors.toList());
            // 获取这批任务的db数据
            List<LauMaterialAuditTaskPo> auditTaskPosOneBatch = materialAuditTaskDbService.queryListByTaskIds(taskIdsOneBatch);

            // 修改 mtime
            for (LauMaterialAuditTaskPo auditTaskPo : auditTaskPosOneBatch) {
                totalTaskUpdateCount++;
                LauMaterialAuditTaskPo taskPo = LauMaterialAuditTaskPo.builder()
                        .taskId(auditTaskPo.getTaskId())
                        .version(auditTaskPo.getVersion() + 1)
                        .build();
                int count = materialAuditTaskDbService.updateTask(taskPo);
            }

            page++;
            Thread.sleep(100);
        } while (!CollectionUtils.isEmpty(pageResult.getRecords()) && pageResult.getRecords().size() == PAGE_SIZE);
        log.info("syncDb2Es[完成]，page:{}, totalUpdateCount[素材]:{},totalTaskUpdateCount[任务]:{}", page, totalUpdateCount, totalTaskUpdateCount);
    }
}
