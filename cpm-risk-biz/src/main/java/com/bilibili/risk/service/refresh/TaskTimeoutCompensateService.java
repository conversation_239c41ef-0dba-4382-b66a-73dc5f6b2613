package com.bilibili.risk.service.refresh;

import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.risk.bo.LauMaterialAuditQueueBo;
import com.bilibili.risk.bo.MaterialAuditTaskCommonQueryBo;
import com.bilibili.risk.constant.RiskConstants;
import com.bilibili.risk.enums.MaterialTaskStatusEnum;
import com.bilibili.risk.po.es.LauMaterialAuditTaskEsPo;
import com.bilibili.risk.po.risk.LauMaterialAuditTaskPo;
import com.bilibili.risk.service.material.LauMaterialAuditService;
import com.bilibili.risk.service.queue.MaterialAuditQueueService;
import com.bilibili.risk.service.task.MaterialAuditTaskAuditService;
import com.bilibili.risk.service.task.MaterialAuditTaskDbService;
import com.bilibili.risk.service.task.MaterialAuditTaskEsService;
import com.bilibili.risk.service.task.MaterialAuditTaskTimeoutService;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 超时任务释放兜底，目前这个任务是关闭的
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TaskTimeoutCompensateService {

    public static final int PAGE_SIZE = 200;
    private final MaterialAuditTaskEsService materialAuditTaskEsService;
    private final MaterialAuditTaskDbService materialAuditTaskDbService;
    private final LauMaterialAuditService lauMaterialAuditService;
    private final MaterialAuditQueueService materialAuditQueueService;

    public void asyncRefreshTaskTimeoutData(List<String> taskIds) {
        new Thread(() -> {
            try {
                refreshTaskTimeoutData(taskIds);
            } catch (Exception e) {
                log.error("asyncrefreshTaskTimeoutData[异常]，taskIds.size:{}, error:{}", taskIds.size(), e);
            }
        }).start();
    }

    @SneakyThrows
    public Integer refreshTaskTimeoutData(List<String> taskIds) {
        log.info("refreshTaskTimeoutData[任务超时补偿]start，taskIds.size:{}", taskIds.size());

        List<LauMaterialAuditQueueBo> auditQueueBos = materialAuditQueueService.queryAllMaterialAuditQueue(0);
        Map<Long, LauMaterialAuditQueueBo> auditQueueBoMap = auditQueueBos.stream().collect(Collectors.toMap(t -> t.getId(), t -> t));

        // 分批查询 es 数据
        PageResult<LauMaterialAuditTaskEsPo> pageResult = null;
        int page = 1;
        int totalUpdateCount = 0;
        do {
            List<String> taskIdsOneBatch = new ArrayList<>();
            if (CollectionUtils.isEmpty(taskIds)) {
                MaterialAuditTaskCommonQueryBo queryBo = MaterialAuditTaskCommonQueryBo.builder()
                        .page(page)
                        .pageSize(PAGE_SIZE)
                        .statusList(Arrays.asList(MaterialTaskStatusEnum.DOING.getCode()))
                        .ids(taskIds)
                        .build();
                pageResult = materialAuditTaskEsService.queryMaterialAuditTaskEsPosCommon(queryBo);
                List<LauMaterialAuditTaskEsPo> records = pageResult.getRecords();
                if (CollectionUtils.isEmpty(records)) {
                    log.info("refreshTaskTimeoutData[没有查询到doing数据]，taskIds.size:{}, page:{}, pageSize:{}", taskIds.size(), page, PAGE_SIZE);
                    break;
                }
                log.info("refreshTaskTimeoutData[查询到doing数据]，taskIds.size:{}, page:{}, pageSize:{}, records.size:{}", taskIds.size(), page, PAGE_SIZE, records.size());
                taskIdsOneBatch = records.stream().map(t -> t.getId()).collect(Collectors.toList());
            }


            // 获取这批任务的db数据
            List<LauMaterialAuditTaskPo> auditTaskPosOneBatch = materialAuditTaskDbService.queryListByTaskIds(taskIdsOneBatch);

            for (LauMaterialAuditTaskPo auditTaskPo : auditTaskPosOneBatch) {
                if (!Objects.equals(auditTaskPo.getStatus(), MaterialTaskStatusEnum.DOING.getCode())) {
                    continue;
                }

                LauMaterialAuditQueueBo auditQueueBo = auditQueueBoMap.get(auditTaskPo.getQueueId());
                if (auditQueueBo == null) {
                    log.error("refreshTaskTimeoutData[没有查询到队列]，taskId:{}, queueId:{}", auditTaskPo.getTaskId(), auditTaskPo.getQueueId());
                    continue;
                }

                boolean isTimeout = MaterialAuditTaskTimeoutService.checkIfTimeout(auditTaskPo, auditQueueBo, auditTaskPo.getAcceptName());
                if (isTimeout) {
                    log.info("refreshTaskTimeoutData[任务超时]，taskId:{}, taskPo={}", auditTaskPo.getTaskId(), auditTaskPo);
                    LauMaterialAuditTaskPo taskPo = LauMaterialAuditTaskPo.builder()
                            .taskId(auditTaskPo.getTaskId())
                            .acceptName("")
                            .acceptTime(RiskConstants.BUSINESS_EARLIEST_TIMESTAMP)
                            .receiveBatchNo("")
                            .status(MaterialTaskStatusEnum.FREE.getCode())
                            .build();
                    int count = materialAuditTaskDbService.updateTask(taskPo);
                    totalUpdateCount++;
                    log.info("refreshTaskTimeoutData[任务超时,job释放]，taskId:{}, queueId:{}, taskStatus:{},count={}", auditTaskPo.getTaskId(), auditTaskPo.getQueueId(), auditTaskPo.getStatus(), count);
                }
            }
            page++;
            Thread.sleep(100);
        } while (!CollectionUtils.isEmpty(pageResult.getRecords()) && pageResult.getRecords().size() == PAGE_SIZE);
        log.info("refreshTaskTimeoutData[完成]，page:{}, totalTaskUpdateCount[任务]:{}", page, totalUpdateCount);
        return totalUpdateCount;
    }
}
