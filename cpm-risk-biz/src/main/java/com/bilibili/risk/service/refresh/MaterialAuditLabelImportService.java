package com.bilibili.risk.service.refresh;

import com.bilibili.risk.bo.LauMaterialAuditLabelImportBo;
import com.bilibili.risk.bo.LauMaterialAuditLabelsImportBo;
import com.bilibili.risk.constant.DatabaseConstant;
import com.bilibili.risk.dao.risk.LauMaterialAuditLabelDao;
import com.bilibili.risk.enums.AuditLabelLevelEnum;
import com.bilibili.risk.enums.MaterialHandleTypeEnum;
import com.bilibili.risk.enums.MaterialSubHandleTypeEnum;
import com.bilibili.risk.po.risk.LauMaterialAuditLabelPo;
import com.bilibili.risk.po.risk.LauMaterialAuditLabelPoExample;
import com.bilibili.risk.service.MaterialAuditLabelService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 标签树导入数据
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MaterialAuditLabelImportService {

    private final LauMaterialAuditLabelDao lauMaterialAuditLabelDao;
    private final MaterialAuditLabelService materialAuditLabelService;

    public void deleteAll() {
        int count = lauMaterialAuditLabelDao.deleteByExample(new LauMaterialAuditLabelPoExample());
        log.info("deleteAll, count: {}", count);
        // 清除缓存
        materialAuditLabelService.clearLabelCache();
    }

    // 读取 excel 解析并导入
    @Transactional(rollbackFor = Exception.class, transactionManager = DatabaseConstant.RISK_TRANSACTION_MANAGER)
    public void importData(LauMaterialAuditLabelsImportBo importBo) {
        List<LauMaterialAuditLabelImportBo> importBoList = importBo.getData();
        if (CollectionUtils.isEmpty(importBoList)) {
            return;
        }

        // 没有的新增，存在的更新
        for (int i = 0; i < importBoList.size(); i++) {
            LauMaterialAuditLabelImportBo labelImportBo = importBoList.get(i);
            if (StringUtils.isEmpty(labelImportBo.getFirstLevelName())) { // 处理合并单元格的，取上一行的数据
                LauMaterialAuditLabelImportBo preBo = importBoList.get(i - 1);
                labelImportBo.setFirstLevelName(preBo.getFirstLevelName());
            }
            if (StringUtils.isEmpty(labelImportBo.getSecondLevelName())) {
                LauMaterialAuditLabelImportBo preBo = importBoList.get(i - 1);
                labelImportBo.setSecondLevelName(preBo.getSecondLevelName());
            }

            Assert.isTrue(!StringUtils.isEmpty(labelImportBo.getFirstLevelName()), "first level name 不能为空");
            Assert.isTrue(!StringUtils.isEmpty(labelImportBo.getSecondLevelName()), "second level name 不能为空");
            Assert.isTrue(!StringUtils.isEmpty(labelImportBo.getThirdLevelName()), "third level name 不能为空");

            Optional<MaterialHandleTypeEnum> handleTypeEnumOptional = MaterialHandleTypeEnum.getByName(labelImportBo.getHandleTypeName());
            Optional<MaterialSubHandleTypeEnum> subHandleTypeEnumOptional = MaterialSubHandleTypeEnum.getByName(labelImportBo.getSubHandleTypeName());

            // 一级
            LauMaterialAuditLabelPo firstLevelPo = null;
            List<LauMaterialAuditLabelPo> firstLevelPos = this.queryListByName(labelImportBo.getFirstLevelName());
            if (CollectionUtils.isEmpty(firstLevelPos)) {
                firstLevelPo = LauMaterialAuditLabelPo.builder()
                        .name(labelImportBo.getFirstLevelName())
                        .level(AuditLabelLevelEnum.ONE.getCode())
                        .build();
                lauMaterialAuditLabelDao.insertSelective(firstLevelPo);
            } else {
                firstLevelPo = firstLevelPos.get(0);
            }

            Assert.isTrue(firstLevelPo != null, "first level po 不能为空");

            // 二级
            LauMaterialAuditLabelPo secondLevelPo = null;
            List<LauMaterialAuditLabelPo> secondLevelPos = this.queryListByName(labelImportBo.getSecondLevelName());
            if (CollectionUtils.isEmpty(secondLevelPos)) {
                secondLevelPo = LauMaterialAuditLabelPo.builder()
                        .name(labelImportBo.getSecondLevelName())
                        .level(AuditLabelLevelEnum.TWO.getCode())
                        .pid(firstLevelPo.getId())
                        .build();
                lauMaterialAuditLabelDao.insertSelective(secondLevelPo);
            } else {
                // 存在了需要看对应的上级是否一样
                LauMaterialAuditLabelPo finalFirstLevelPo = firstLevelPo;

                // 从同名二级里面找一个上级也一样的
                List<LauMaterialAuditLabelPo> candidateSecondLevelPos = secondLevelPos.stream()
                        .filter(t -> t.getLevel().equals(AuditLabelLevelEnum.TWO.getCode()) && Objects.equals(t.getPid(), finalFirstLevelPo.getId()))
                        .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(candidateSecondLevelPos)) {
                    secondLevelPo = candidateSecondLevelPos.get(0);
                } else {
                    secondLevelPo = LauMaterialAuditLabelPo.builder()
                            .name(labelImportBo.getSecondLevelName())
                            .level(AuditLabelLevelEnum.TWO.getCode())
                            .pid(firstLevelPo.getId())
                            .build();
                    lauMaterialAuditLabelDao.insertSelective(secondLevelPo);
                }
            }
            Assert.isTrue(secondLevelPo != null, "second level po 不能为空");

            // 三级
            LauMaterialAuditLabelPo thirdLevelPo = null;
            List<LauMaterialAuditLabelPo> thirdLevelPos = this.queryListByName(labelImportBo.getThirdLevelName());
            if (CollectionUtils.isEmpty(thirdLevelPos)) {
                thirdLevelPo = LauMaterialAuditLabelPo.builder()
                        .name(labelImportBo.getThirdLevelName())
                        .level(AuditLabelLevelEnum.THREE.getCode())
                        .pid(secondLevelPo.getId())
                        .reason(labelImportBo.getReason())
                        .handleType(handleTypeEnumOptional.get().getCode())
                        .subHandleType(subHandleTypeEnumOptional.get().getCode())
                        .build();
                lauMaterialAuditLabelDao.insertSelective(thirdLevelPo);
            } else {
                // 存在了需要看对应的上级是否一样
                // 从同名三级里面找一个上级也一样的
                LauMaterialAuditLabelPo finalSecondLevelPo = secondLevelPo;
                List<LauMaterialAuditLabelPo> candidateThirdLevelPos = thirdLevelPos.stream()
                        .filter(t -> t.getLevel().equals(AuditLabelLevelEnum.THREE.getCode()) && Objects.equals(t.getPid(), finalSecondLevelPo.getId()))
                        .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(candidateThirdLevelPos)) {
                    // 存在了，则不管
                } else {
                    thirdLevelPo = LauMaterialAuditLabelPo.builder()
                            .name(labelImportBo.getThirdLevelName())
                            .level(AuditLabelLevelEnum.THREE.getCode())
                            .pid(secondLevelPo.getId())
                            .reason(labelImportBo.getReason())
                            .handleType(handleTypeEnumOptional.get().getCode())
                            .subHandleType(subHandleTypeEnumOptional.get().getCode())
                            .build();
                    lauMaterialAuditLabelDao.insertSelective(thirdLevelPo);
                }
            }

            log.info("importData, firstLevelPo: {}, secondLevelPo: {}, thirdLevelPo: {}",
                    firstLevelPo, secondLevelPo, thirdLevelPo);
        }
        log.info("importData complete, size: {}", importBoList.size());

        // 清除缓存，确保数据更新后缓存也更新
        materialAuditLabelService.clearLabelCache();
    }

    public List<LauMaterialAuditLabelPo> queryListByName(String name) {
        LauMaterialAuditLabelPoExample example = new LauMaterialAuditLabelPoExample();
        example.createCriteria().andNameEqualTo(name);
        return lauMaterialAuditLabelDao.selectByExample(example);
    }

}
