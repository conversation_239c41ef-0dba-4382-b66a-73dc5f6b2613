package com.bilibili.risk.service.refresh;

import com.alibaba.fastjson.JSONArray;
import com.bilibili.risk.bo.msg.MaterialInfoPushToAuditMsgBo;
import com.bilibili.risk.service.material.CreativeMaterialPushToAuditService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class CreativePushRefreshService {

    private final CreativeMaterialPushToAuditService creativeMaterialPushToAuditService;

    public void refreshByJson(String json){
        List<MaterialInfoPushToAuditMsgBo> msgList = JSONArray.parseArray(json, MaterialInfoPushToAuditMsgBo.class);

        for (MaterialInfoPushToAuditMsgBo materialInfoPushToAuditMsgBo : msgList) {
            creativeMaterialPushToAuditService.processCreativeMaterialPushToAudit(materialInfoPushToAuditMsgBo);
        }
    }
}
