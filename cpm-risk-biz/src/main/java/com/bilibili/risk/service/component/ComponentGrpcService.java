package com.bilibili.risk.service.component;

import com.bapis.ad.component.StoryComponent;
import com.bapis.ad.component.StoryComponentIds;
import com.bapis.ad.component.StoryComponentServiceGrpc;
import com.bapis.ad.component.StoryComponents;
import com.bapis.ad.pandora.core.GetUnderframeComponentsReq;
import com.bapis.ad.pandora.core.GetUnderframeComponentsResp;
import com.bapis.ad.pandora.core.UnderframeComponent;
import com.bapis.ad.pandora.core.UnderframeComponentServiceGrpc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import pleiades.venus.starter.rpc.client.RPCClient;

import java.util.List;

/**
 * story 组件和框下组件
 */
@Slf4j
@Service
public class ComponentGrpcService {

    @RPCClient("sycpb.cpm.scv")
    private StoryComponentServiceGrpc.StoryComponentServiceBlockingStub storyComponentServiceStub;
    @RPCClient("sycpb.platform.cpm-pandora")
    private UnderframeComponentServiceGrpc.UnderframeComponentServiceBlockingStub underframeComponentServiceStub;

    public List<StoryComponent> queryStoryComponents(List<Long> componentIds, Integer componentType) {

        StoryComponentIds req = StoryComponentIds.newBuilder()
                .addAllId(componentIds)
                .setType(componentType)
                .build();
        StoryComponents byComponentIds = storyComponentServiceStub.getByComponentIds(req);
        List<StoryComponent> componentsList = byComponentIds.getComponentsList();
        return componentsList;
    }


    public List<UnderframeComponent> queryUnderframeComponents(List<Long> componentIds) {
        GetUnderframeComponentsReq req = GetUnderframeComponentsReq.newBuilder()
                .addAllComponentIds(componentIds).build();
        GetUnderframeComponentsResp underframeComponents = underframeComponentServiceStub.getUnderframeComponents(req);
        List<UnderframeComponent> entitiesList = underframeComponents.getEntitiesList();
        return entitiesList;
    }
}
