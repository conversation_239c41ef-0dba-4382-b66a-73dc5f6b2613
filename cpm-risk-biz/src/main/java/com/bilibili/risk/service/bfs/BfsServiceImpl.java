/*
 * Copyright (c) 2015-2021 BiliBili Inc.
 */

package com.bilibili.risk.service.bfs;

import com.bilibili.risk.exception.RiskRemoteAccessErr;
import com.google.common.net.HttpHeaders;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.codec.digest.HmacAlgorithms;
import org.apache.commons.codec.digest.HmacUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Base64Utils;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.text.MessageFormat;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Objects;

import static java.nio.charset.StandardCharsets.UTF_8;

@Slf4j
@Service
@RequiredArgsConstructor
public class BfsServiceImpl {
    private final String ID = "BfsServiceImpl";

    private final BfsProperties bfsProperties;
    private final OkHttpClient okHttpClient;

    @SneakyThrows
    public byte[] download(String url) {
        Request req = new Request.Builder()
                .url(url)
                .build();

        try {
            Response resp = okHttpClient.newCall(req).execute();
            if (!resp.isSuccessful()) throw RiskRemoteAccessErr.wrongStatus(resp.code(), resp.message());

            if (Objects.isNull(resp.body())) throw RiskRemoteAccessErr.noBody();

            return resp.body().bytes();
        } catch (IOException e) {
            log.error(ID, e);
            throw RiskRemoteAccessErr.networkFailure(url);
        }
    }

    public String upload(RawImageBo rawImageBo, String path) {
        String putUrl = buildBfsUrl(path);
        Request request = new Request.Builder()
                .header(HttpHeaders.CONNECTION, "close")
                .header(HttpHeaders.AUTHORIZATION, buildAuth(path))
                .header(HttpHeaders.CONTENT_TYPE, rawImageBo.getMimeType())
                .header(HttpHeaders.HOST, bfsProperties.getHost())
                .header(HttpHeaders.DATE, ZonedDateTime.now(ZoneId.of("GMT")).format(DateTimeFormatter.RFC_1123_DATE_TIME))
                .url(putUrl)
                .put(RequestBody.create(MediaType.parse(rawImageBo.getMimeType()), rawImageBo.getRawBytes()))
                .build();
        try {
            Response resp = okHttpClient.newCall(request).execute();
            if (!resp.isSuccessful()) throw RiskRemoteAccessErr.wrongStatus(resp.code(), resp.message());

            String getUrl = resp.header(HttpHeaders.LOCATION);
            if (StringUtils.isEmpty(getUrl)) throw RiskRemoteAccessErr.noBody();

            return getUrl.replace("http", "https");

        } catch (IOException e) {
            log.error(ID, e);
            throw RiskRemoteAccessErr.networkFailure(putUrl);
        }
    }

    private String buildBfsUrl(String subPath) {
        return MessageFormat.format("http://{0}/{1}/{2}", bfsProperties.getHost(), bfsProperties.getBucket(), subPath);
    }

    private String buildAuth(String name) {
        final long ts = System.currentTimeMillis() / 1000;
        final String text = "PUT" + "\n" + bfsProperties.getBucket() + "\n" + name + "\n" + ts + "\n";
        return bfsProperties.getAccessKey() +
                ":" +
                getHmacSha1(bfsProperties.getAccessSecret(), text) +
                ":" +
                ts;
    }

    private String getHmacSha1(String secret, String text) {
        final byte[] bytes = new HmacUtils(HmacAlgorithms.HMAC_SHA_1, secret).hmac(text.getBytes(UTF_8));
        return Base64Utils.encodeToString(bytes);
    }
}
