package com.bilibili.risk.service.task;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.mall.kraken.boot.autoconfigure.config.DynamicValue;
import com.bilibili.risk.bo.LauMaterialAuditQueueBo;
import com.bilibili.risk.bo.LauMaterialAuditTaskUpdateBo;
import com.bilibili.risk.config.BizConfig;
import com.bilibili.risk.constant.RiskConstants;
import com.bilibili.risk.service.queue.MaterialAuditQueueService;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Message;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RBatch;
import org.redisson.api.RScoredSortedSet;
import org.redisson.api.RScoredSortedSetAsync;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;

@Service
@Slf4j
@RequiredArgsConstructor
public class PullRedisZsetService {

    private final RedissonClient adpRedissonClient;
    private final MaterialAuditQueueService materialAuditQueueService;
    private final BizConfig bizConfig;

    @Value("${risk.pull.redis.auditCompleteTimeout:20}")
    @DynamicValue
    private Integer auditCompleteTimeout;

    public void cacheCompletedTaskIds(List<LauMaterialAuditTaskUpdateBo> completeUpdateBos) {

        if (!Utils.isPositive(bizConfig.getPullRedisZsetSwitch())) {
            return;
        }

        try {
            if (CollectionUtils.isEmpty(completeUpdateBos)) {
                return;
            }

            // 按照队列ID分组任务
            Map<Long, Set<String>> queueTasksMap = new HashMap<>();
            long currentTime = System.currentTimeMillis();

            // 将完成的任务按队列ID分组
            completeUpdateBos.forEach(t -> {
                Long queueId = t.getQueueId();

                if (queueId != null) {
                    queueTasksMap.computeIfAbsent(queueId, k -> new HashSet<>())
                            .add(t.getTaskId());
                }
            });

            // 批量操作Redis，使用ZSet存储，以时间戳为分数
            RBatch batch = adpRedissonClient.createBatch();
            queueTasksMap.forEach((queueId, taskIds) -> {
                String key = RiskConstants.CACHE_PREFIX_AUDIT_COMPLETE_QUEUE_ID + queueId;

                // 为每个任务ID创建一个分数-成员对，分数为当前时间戳
                Map<String, Double> scoreMembers = new HashMap<>();
                for (String taskId : taskIds) {
                    scoreMembers.put(taskId, Double.valueOf(currentTime));
                }

                // 将任务ID添加到ZSet中，不设置过期时间
                RScoredSortedSetAsync<String> scoredSortedSet = batch.getScoredSortedSet(key);
                scoredSortedSet.addAllAsync(scoreMembers);

                log.info("cacheCompletedTaskIds[添加任务到ZSet] queueId={}, 任务数量={}", queueId, taskIds.size());
            });

            // 同步执行批量操作
            batch.execute();

            Cat.logEvent("batchAuditTask", "redis缓存刚刚审核完的任务id end",
                    Message.SUCCESS,
                    "queueCount:" + queueTasksMap.size() +
                            ", taskCount:" + completeUpdateBos.size());
        } catch (Exception e) {
            log.error("cacheCompletedTaskIds", e);
        }
    }

    /**
     * 从Redis ZSet中获取指定队列最近5秒内完成的任务ID
     *
     * @param queueId 队列ID
     * @return 最近5秒内完成的任务ID集合
     */
    @NotNull
    public Set<String> getQueueJustCompleteTaskIdsFromRedis(Long queueId) {

        if (!Utils.isPositive(bizConfig.getPullRedisZsetSwitch())) {
            return Collections.emptySet();
        }

        try {
            if (queueId == null) {
                return Collections.emptySet();
            }

            // 构建Redis键
            String key = RiskConstants.CACHE_PREFIX_AUDIT_COMPLETE_QUEUE_ID + queueId;

            try {
                // 获取当前时间戳
                long currentTime = System.currentTimeMillis();
                // 计算5秒前的时间戳
                long fiveSecondsAgo = currentTime - auditCompleteTimeout * 1000;

                // 获取ZSet中分数在[fiveSecondsAgo, currentTime]范围内的所有成员
                RScoredSortedSet<String> sortedSet = adpRedissonClient.getScoredSortedSet(key);

                // 如果ZSet不存在或为空，返回空集合
                if (sortedSet.isEmpty()) {
                    return Collections.emptySet();
                }

                // 获取最近5秒内的任务ID
                Collection<String> recentTaskIds = sortedSet.valueRange(fiveSecondsAgo, true, currentTime, true);

                if (CollectionUtils.isEmpty(recentTaskIds)) {
                    return Collections.emptySet();
                }

                log.info("getQueueJustCompleteTaskIdsFromRedis[获取队列最近完成的任务] queueId={}, 获取到的任务数量={}",
                        queueId, recentTaskIds.size());

                return new HashSet<>(recentTaskIds);

            } catch (Exception e) {
                log.error("getQueueJustCompleteTaskIdsFromRedis[获取Redis数据异常] queueId={}", queueId, e);
                return Collections.emptySet();
            }
        } catch (Exception e) {
            log.error("getQueueJustCompleteTaskIdsFromRedis", e.getMessage());
            return Collections.emptySet();
        }
    }

    /**
     * 从Redis ZSet中获取多个队列最近5秒内完成的任务ID
     *
     * @param queueIds 队列ID列表
     * @return 最近5秒内完成的任务ID集合
     */
    @NotNull
    public Set<String> getQueuesJustCompleteTaskIdsFromRedis(Long ruleId, List<Long> queueIds) {

        if (!Utils.isPositive(bizConfig.getPullRedisZsetSwitch())) {
            return Collections.emptySet();
        }
        if (CollectionUtils.isEmpty(queueIds)) {
            return Collections.emptySet();
        }

        Set<String> allCompletedTaskIds = new HashSet<>();

        for (Long queueId : queueIds) {
            Set<String> queueCompletedTaskIds = getQueueJustCompleteTaskIdsFromRedis(queueId);
            if (!CollectionUtils.isEmpty(queueCompletedTaskIds)) {
                allCompletedTaskIds.addAll(queueCompletedTaskIds);
            }
        }

        if (!allCompletedTaskIds.isEmpty()) {
            log.info("getQueuesJustCompleteTaskIdsFromRedis[获取多队列最近完成的任务] ruleId={}, queueIds={}, 总任务数量={}",
                    ruleId, queueIds, allCompletedTaskIds.size());
        }

        return allCompletedTaskIds;
    }

    /**
     * 清理 Redis 中 ZSet 结构的已完成任务 ID，保留最近 5 秒内的数据
     */
    public Integer clean() {
        log.info("clean[开始清理ZSet中超过5秒的已完成任务ID]");
        int totalRemovedCount = 0;

        try {
            // 1. 获取所有队列
            List<LauMaterialAuditQueueBo> queueBos = materialAuditQueueService.queryAllMaterialAuditQueue(1);
            if (CollectionUtils.isEmpty(queueBos)) {
                log.warn("clean[没有获取到队列信息]");
                return 0;
            }

            // 2. 计算清理截止时间点（5秒前）
            long currentTime = System.currentTimeMillis();
            long expireTime = currentTime - auditCompleteTimeout * 1000;

            // 3. 统计数据
            int queueCount = 0;
            Map<Long, Integer> queueRemovedCountMap = new HashMap<>();

            // 4. 对每个队列进行清理
            for (LauMaterialAuditQueueBo queueBo : queueBos) {
                Long queueId = queueBo.getId();
                String key = RiskConstants.CACHE_PREFIX_AUDIT_COMPLETE_QUEUE_ID + queueId;

                try {
                    RScoredSortedSet<String> sortedSet = adpRedissonClient.getScoredSortedSet(key);
                    if (sortedSet.isEmpty()) {
                        continue;
                    }

                    // 获取需要清理的任务数量
                    Collection<String> tasksToRemove = sortedSet.valueRange(0, true, expireTime, true);
                    if (CollectionUtils.isEmpty(tasksToRemove)) {
                        continue;
                    }

                    // 记录清理前的数量
                    int beforeSize = sortedSet.size();

                    // 清理超过5秒的数据
                    long removeCount = sortedSet.removeRangeByScore(0, true, expireTime, true);

                    // 记录清理结果
                    if (removeCount > 0) {
                        queueCount++;
                        totalRemovedCount += removeCount;
                        queueRemovedCountMap.put(queueId, (int) removeCount);

                        log.info("clean[清理队列ZSet数据] queueId={}, 清理前数量={}, 清理数量={}, 清理后数量={}",
                                queueId, beforeSize, removeCount, sortedSet.size());
                    }
                } catch (Exception e) {
                    log.error("clean[清理队列ZSet数据异常] queueId={}", queueId, e);
                }
            }

            // 5. 记录总体清理结果
            log.info("clean[完成清理ZSet已完成任务ID] 处理队列数量={}, 总队列数量={}, 清理任务总数={}",
                    queueCount, queueBos.size(), totalRemovedCount);

            // 6. 使用CAT监控
            if (totalRemovedCount > 0) {
                Cat.logEvent("PullRedisZsetClean", "cleanExpiredTaskIds",
                        Message.SUCCESS,
                        "queueCount:" + queueCount +
                                ", totalQueueCount:" + queueBos.size() +
                                ", removedCount:" + totalRemovedCount);
            }
            return totalRemovedCount;
        } catch (Exception e) {
            log.error("clean[清理ZSet数据全局异常]", e);
            Cat.logError("PullRedisZsetClean_Error", e);
            throw e;
        }
    }
}