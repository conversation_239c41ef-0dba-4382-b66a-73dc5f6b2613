package com.bilibili.risk.service.task.pull;

import com.bilibili.rbac.api.service.IUserService;
import com.bilibili.risk.bo.LauMaterialAuditTaskUpdateBo;
import com.bilibili.risk.bo.MaterialAuditTaskPullQueryBo;
import com.bilibili.risk.config.BizConfig;
import com.bilibili.risk.convertor.IMaterialAuditTaskConvertor;
import com.bilibili.risk.databus.pull_task.MaterialTaskPrePullMsgPub;
import com.bilibili.risk.databus.timeout_task.MaterialTaskTimeoutPub;
import com.bilibili.risk.enums.MaterialTaskStatusEnum;
import com.bilibili.risk.po.es.LauMaterialAuditTaskEsPo;
import com.bilibili.risk.po.risk.LauMaterialAuditTaskPo;
import com.bilibili.risk.service.SnowFlakeNoService;
import com.bilibili.risk.service.log.OperationLogService;
import com.bilibili.risk.service.queue.MaterialAuditQueueService;
import com.bilibili.risk.service.role.RiskRoleService;
import com.bilibili.risk.service.rule.MaterialAuditRuleService;
import com.bilibili.risk.service.task.MaterialAuditTaskDbService;
import com.bilibili.risk.service.task.MaterialAuditTaskEsService;
import com.bilibili.risk.service.task.MaterialTaskEsPubService;
import com.bilibili.risk.service.task.PullRedisZsetService;
import com.bilibili.risk.utils.MaterialTaskUtils;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public abstract class AbstractMaterialAuditTaskPullStrategy implements IMaterialAuditTaskPullStrategy{

    @Autowired
    protected MaterialAuditTaskEsService materialAuditTaskEsService;
    @Autowired
    protected MaterialAuditQueueService materialAuditQueueService;
    @Autowired
    protected MaterialAuditRuleService materialAuditRuleService;
    @Autowired
    protected SnowFlakeNoService snowFlakeNoService;
    @Autowired
    protected MaterialAuditTaskDbService materialAuditTaskDbService;
    @Autowired
    protected MaterialTaskPrePullMsgPub materialTaskPrePullMsgPub;
    @Autowired
    protected MaterialTaskTimeoutPub materialTaskTimeoutPub;
    @Autowired
    protected MaterialTaskUtils materialTaskUtils;
    @Autowired
    protected IUserService userService;
    @Autowired
    protected BizConfig bizConfig;
    @Autowired
    protected OperationLogService operationLogService;
    @Autowired
    protected MaterialTaskEsPubService materialTaskEsPubService;
    @Autowired
    protected RiskRoleService riskRoleService;
    @Autowired
    protected RedissonClient adpRedissonClient;
    @Autowired
    protected PullRedisZsetService pullRedisZsetService;
    @Autowired
    protected RiskMaterialTaskDoingService riskMaterialTaskDoingService;

    protected List<LauMaterialAuditTaskUpdateBo> buildMaterialTaskUpdateBos(MaterialAuditTaskPullQueryBo pullQueryBo,
                                                                                     List<LauMaterialAuditTaskEsPo> auditTaskEsPosByAccount,
                                                                                     Map<String, LauMaterialAuditTaskPo> auditTaskPoMap,
                                                                                     String batchNo) {
        List<LauMaterialAuditTaskUpdateBo> auditTask00UpdateBos = new ArrayList<>();
        Timestamp acceptTime = new Timestamp(System.currentTimeMillis());
        for (LauMaterialAuditTaskEsPo auditTaskEsPo : auditTaskEsPosByAccount) {

            LauMaterialAuditTaskPo dbAuditPo = auditTaskPoMap.get(auditTaskEsPo.getId());
            if (dbAuditPo == null) {
                continue;
            }
            if (!Objects.equals(dbAuditPo.getStatus(), MaterialTaskStatusEnum.FREE.getKey())) {
                log.info("buildMaterialTaskUpdateBos[db任务非游离，过滤]，auditTaskEsPo:{}", auditTaskEsPo);
                continue;
            }

            LauMaterialAuditTaskUpdateBo taskUpdateBo = LauMaterialAuditTaskUpdateBo.builder()
                    .taskId(auditTaskEsPo.getId())
                    .acceptName(pullQueryBo.getUsername())
                    .acceptTime(acceptTime)
                    .receiveBatchNo(batchNo)
                    .version(dbAuditPo.getVersion())
                    .status(MaterialTaskStatusEnum.DOING.getKey())
                    .mtime(new Timestamp(System.currentTimeMillis()))
                    .ctime(dbAuditPo.getCtime())
                    .build();

            auditTaskEsPo.setReceiveBatchNo(batchNo);
            auditTaskEsPo.setAcceptName(pullQueryBo.getUsername());
            auditTaskEsPo.setAcceptTime(acceptTime);
            auditTaskEsPo.setStatus(MaterialTaskStatusEnum.DOING.getKey());
            auditTaskEsPo.setMtime(taskUpdateBo.getMtime());
            auditTask00UpdateBos.add(taskUpdateBo);
        }
        return auditTask00UpdateBos;
    }

    protected List<LauMaterialAuditTaskEsPo> queryDoingTasks(List<String> taskIds) {

        if (CollectionUtils.isEmpty(taskIds)) {
            return Collections.emptyList();
        }

        List<LauMaterialAuditTaskPo> lauMaterialAuditTaskPos = materialAuditTaskDbService.queryListByTaskIds(taskIds);
        lauMaterialAuditTaskPos = lauMaterialAuditTaskPos.stream()
                .filter(t -> Objects.equals(t.getStatus(), MaterialTaskStatusEnum.DOING.getKey()))
                .sorted(Comparator.comparing(LauMaterialAuditTaskPo::getMtime))  // 按mtime升序排序
                .collect(Collectors.toList());        List<LauMaterialAuditTaskEsPo> auditTaskEsPos = IMaterialAuditTaskConvertor.INSTANCE.pos2EsPos(lauMaterialAuditTaskPos);
        return auditTaskEsPos;
    }

    protected List<LauMaterialAuditTaskEsPo> fetchOneBatchIfExistManyBatchDoing(List<LauMaterialAuditTaskEsPo> doingTaskEsPos) {

        if (CollectionUtils.isEmpty(doingTaskEsPos)) {
            return doingTaskEsPos;
        }

        // 多个批次的取一个，按批次ID升序排序后取第一个
        Map<String, List<LauMaterialAuditTaskEsPo>> taskEsPosByBatchNo = doingTaskEsPos.stream().collect(Collectors.groupingBy(LauMaterialAuditTaskEsPo::getReceiveBatchNo));
        if (taskEsPosByBatchNo.size() > 1) {
            // 按批次ID升序排序后取第一个批次
            String firstBatchNo = taskEsPosByBatchNo.keySet().stream()
                    .sorted()
                    .findFirst()
                    .orElseThrow(() -> new IllegalStateException("No batch found"));

            doingTaskEsPos = taskEsPosByBatchNo.get(firstBatchNo);
            log.info("fetchOneBatchIfExistManyBatchDoing[拉取到多个batch的doing任务，取第一个batch]，batchNo:{}, doingTaskEsPos.size:{}, doingTaskEsPos:{}",
                    firstBatchNo, doingTaskEsPos.size(), doingTaskEsPos);
        }

        // 多个队列取一个队列的，按队列ID升序排序后取第一个
        Map<Long, List<LauMaterialAuditTaskEsPo>> taskEsPosByQueue = doingTaskEsPos.stream().collect(Collectors.groupingBy(LauMaterialAuditTaskEsPo::getQueueId));
        if (taskEsPosByQueue.size() > 1) {
            // 按队列ID升序排序后取第一个队列
            Long firstQueueId = taskEsPosByQueue.keySet().stream()
                    .sorted()
                    .findFirst()
                    .orElseThrow(() -> new IllegalStateException("No queue found"));

            doingTaskEsPos = taskEsPosByQueue.get(firstQueueId);
            log.info("fetchOneBatchIfExistManyBatchDoing[拉取到多个queue的doing任务，取第一个队列]，queueId:{}, doingTaskEsPos.size:{}, doingTaskEsPos:{}",
                    firstQueueId, doingTaskEsPos.size(), doingTaskEsPos);
        }
        return doingTaskEsPos;
    }
}
