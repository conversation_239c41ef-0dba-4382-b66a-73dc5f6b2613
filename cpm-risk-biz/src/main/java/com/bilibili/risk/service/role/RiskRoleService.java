package com.bilibili.risk.service.role;

import com.bilibili.rbac.api.dto.UserBaseDto;
import com.bilibili.rbac.biz.common.UserStatus;
import com.bilibili.rbac.biz.dao.RoleDao;
import com.bilibili.rbac.biz.handler.UserHandler;
import com.bilibili.rbac.biz.handler.UserRoleHandler;
import com.bilibili.rbac.biz.po.RolePo;
import com.bilibili.rbac.biz.po.RolePoExample;
import com.bilibili.rbac.biz.service.RoleService;
import com.bilibili.risk.bo.RoleBo;
import com.bilibili.risk.config.BizConfig;
import com.bilibili.risk.convertor.IRoleConvertor;
import edu.emory.mathcs.backport.java.util.Collections;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class RiskRoleService {

    private final RoleService roleService;
    private final UserHandler userHandler;
    private final BizConfig bizConfig;
    private final RoleDao roleDao;
    private final UserRoleHandler userRoleHandler;

    public List<RoleBo> getRoleIds() {

        RolePoExample example = new RolePoExample();
        example.setOrderByClause("id desc");
        example.or().andTenantIdEqualTo(bizConfig.getTenantId()).andLevelEqualTo(1);
        List<RolePo> rolePos = this.roleDao.selectByExample(example);
        return IRoleConvertor.INSTANCE.pos2Bos(rolePos);
    }

    public List<RoleBo> getRoleIdsByUsername(String username) {
        Assert.notNull(username);

        UserBaseDto userBaseDto = this.userHandler.getUserByUsername(bizConfig.getTenantId(), username);
        Assert.notNull(userBaseDto);
        Assert.isTrue(UserStatus.ON.getCode().equals(userBaseDto.getStatus()), "user is freeze");

        List<Integer> roleIds = this.userRoleHandler.getRoleIdsByUserId(bizConfig.getTenantId(), userBaseDto.getId());
        if (CollectionUtils.isEmpty(roleIds)) {
            return Collections.emptyList();
        }

        return queryRoleBosByIds(roleIds);
    }

    public List<RoleBo> queryRoleBosByIds(List<Integer> roleIds) {

        if (CollectionUtils.isEmpty(roleIds)) {
            return Collections.emptyList();
        }

        RolePoExample example = new RolePoExample();
        example.setOrderByClause("id desc");
        example.or().andIdIn(roleIds);
        List<RolePo> rolePos = this.roleDao.selectByExample(example);
        return IRoleConvertor.INSTANCE.pos2Bos(rolePos);
    }

    public Map<Integer, RoleBo> queryRoleBoMapByIds(List<Integer> roleIds) {
        return this.queryRoleBosByIds(roleIds).stream()
                .collect(Collectors.toMap(RoleBo::getId, roleBo -> roleBo, (oldValue, newValue) -> oldValue));
    }

}
