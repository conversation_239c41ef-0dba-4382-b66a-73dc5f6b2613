package com.bilibili.risk.service.creative;

import com.bapis.ad.creative.CreativeServiceGrpc;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.risk.bo.LauProgrammaticCreativeDetailBo;
import com.bilibili.risk.convertor.ICreativeConvertor;
import com.bilibili.risk.dao.ad_core.LauProgrammaticCreativeDetailDao;
import com.bilibili.risk.po.ad.LauProgrammaticCreativeDetailPo;
import com.bilibili.risk.po.ad.LauProgrammaticCreativeDetailPoExample;
import edu.emory.mathcs.backport.java.util.Collections;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import pleiades.venus.starter.rpc.client.RPCClient;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ProgramCreativeService {

    @RPCClient("sycpb.cpm.cpm-adp")
    private CreativeServiceGrpc.CreativeServiceBlockingStub creativeServiceBlockingStub;
    @Autowired
    private LauProgrammaticCreativeDetailDao lauProgrammaticCreativeDetailDao;

    public List<LauProgrammaticCreativeDetailBo> queryDetailListByCreativeIds(List<Integer> creativeIds) {
        if (CollectionUtils.isEmpty(creativeIds)) {
            return Collections.emptyList();
        }

//        QueryProgramCreativeDetailListReq request = QueryProgramCreativeDetailListReq.newBuilder().addAllCreativeIds(creativeIds).build();
//        QueryProgramCreativeDetailListRep detailListRep = creativeServiceBlockingStub.queryProgramCreativeDetailList(request);

        LauProgrammaticCreativeDetailPoExample example = new LauProgrammaticCreativeDetailPoExample();
        example.createCriteria().andCreativeIdIn(creativeIds);
        List<LauProgrammaticCreativeDetailPo> creativeDetailPos = lauProgrammaticCreativeDetailDao.selectByExample(example);
        return ICreativeConvertor.INSTANCE.pos2Bo(creativeDetailPos);
    }

    public Map<Integer, List<LauProgrammaticCreativeDetailBo>> queryDetailMapByCreativeIds(List<Integer> creativeIds) {
        return this.queryDetailListByCreativeIds(creativeIds).stream().collect(Collectors.groupingBy(LauProgrammaticCreativeDetailBo::getCreativeId));
    }

    public List<LauProgrammaticCreativeDetailBo> queryDetailsByCreativeId(Integer creativeId) {

        if (!Utils.isPositive(creativeId)) {
            return Collections.emptyList();
        }

        return queryDetailMapByCreativeIds(Arrays.asList(creativeId)).get(creativeId);
    }


}
