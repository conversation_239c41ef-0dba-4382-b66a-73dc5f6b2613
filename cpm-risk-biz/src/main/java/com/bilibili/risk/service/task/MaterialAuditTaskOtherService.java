package com.bilibili.risk.service.task;

import com.bapis.ad.mng.creative.MngCreativeServiceGrpc;
import com.bapis.ad.mng.creative.QueryCreativeOtherInfosReq;
import com.bapis.ad.mng.creative.QueryCreativeOtherInfosResp;
import com.bapis.ad.mng.creative.SingleQueryCreativeOtherInfosResp;
import com.bilibili.risk.bo.CreativeOtherBo;
import com.bilibili.risk.convertor.ICreativeConvertor;
import com.bilibili.risk.po.risk.LauMaterialAuditTaskPo;
import edu.emory.mathcs.backport.java.util.Collections;
import io.github.resilience4j.circuitbreaker.annotation.CircuitBreaker;
import io.github.resilience4j.timelimiter.annotation.TimeLimiter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import pleiades.venus.starter.rpc.client.RPCClient;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

@Slf4j
@Service
public class MaterialAuditTaskOtherService {

    @RPCClient("sycpb.cpm.cpm-mng")
    private MngCreativeServiceGrpc.MngCreativeServiceBlockingStub mngCreativeServiceBlockingStub;
    @Autowired
    private MaterialAuditTaskDbService materialAuditTaskDbService;

    @SneakyThrows
    public List<CreativeOtherBo> queryTaskOtherInfo(List<String> taskIds) {

        if (CollectionUtils.isEmpty(taskIds)) {
            return Collections.emptyList();
        }

        List<LauMaterialAuditTaskPo> lauMaterialAuditTaskPos = materialAuditTaskDbService.queryListByTaskIds(taskIds);
        List<Integer> creativeIds = lauMaterialAuditTaskPos.stream().map(t -> t.getCreativeId()).distinct().collect(Collectors.toList());
        Map<String, LauMaterialAuditTaskPo> auditTaskPoMap = lauMaterialAuditTaskPos.stream().collect(Collectors.toMap(t -> t.getTaskId(), t -> t));

//        Map<Integer, CreativeOtherBo> creativeOtherBoMap = queryCreativeOtherInfo(creativeIds);
        CompletableFuture<Map<Integer, CreativeOtherBo>> mapCompletableFuture = queryCreativeOtherInfoWithFallback(creativeIds);
        Map<Integer, CreativeOtherBo> creativeOtherBoMap = mapCompletableFuture.get();

        Map<String, CreativeOtherBo> allCreativeOtherBoMap = new HashMap<>();
        for (String taskId : taskIds) {
            LauMaterialAuditTaskPo auditTaskPo = auditTaskPoMap.get(taskId);
            if (auditTaskPo == null) {
                continue;
            }
            CreativeOtherBo creativeOtherBo = creativeOtherBoMap.get(auditTaskPo.getCreativeId());
            if (creativeOtherBo == null) {
                continue;
            }
            CreativeOtherBo copy = ICreativeConvertor.INSTANCE.copy(creativeOtherBo);
            copy.setTaskId(taskId);
            allCreativeOtherBoMap.put(taskId, copy);
        }
        return new ArrayList<>(allCreativeOtherBoMap.values());
    }

    @NotNull
    public Map<Integer, CreativeOtherBo> queryCreativeOtherInfo(List<Integer> creativeIds) {
        QueryCreativeOtherInfosResp queryCreativeOtherInfosResp = mngCreativeServiceBlockingStub
                .withDeadlineAfter(5000, TimeUnit.MILLISECONDS)
                .queryCreativeOtherInfos(QueryCreativeOtherInfosReq.newBuilder().addAllCreativeIds(creativeIds).build());
        List<SingleQueryCreativeOtherInfosResp> dataList = queryCreativeOtherInfosResp.getDataList();
        List<CreativeOtherBo> creativeOtherBos = ICreativeConvertor.INSTANCE.grpcs2bos(dataList);
        Map<Integer, CreativeOtherBo> creativeOtherBoMap = creativeOtherBos.stream().collect(Collectors.toMap(t -> t.getCreativeId(), t -> t, (v1, v2) -> v2));
        return creativeOtherBoMap;
    }

    @NotNull
    @TimeLimiter(name = "creativeGrpcService", fallbackMethod = "queryCreativeOtherInfoTimeoutFallback")
    @CircuitBreaker(name = "creativeGrpcService", fallbackMethod = "queryCreativeOtherInfoFallback")
    public CompletableFuture<Map<Integer, CreativeOtherBo>> queryCreativeOtherInfoWithFallback(List<Integer> creativeIds) {

        return CompletableFuture.supplyAsync(() -> {
            try {
                QueryCreativeOtherInfosResp queryCreativeOtherInfosResp = mngCreativeServiceBlockingStub
                        .withDeadlineAfter(5000, TimeUnit.MILLISECONDS)
                        .queryCreativeOtherInfos(QueryCreativeOtherInfosReq.newBuilder()
                                .addAllCreativeIds(creativeIds)
                                .build());

                List<SingleQueryCreativeOtherInfosResp> dataList = queryCreativeOtherInfosResp.getDataList();
                List<CreativeOtherBo> creativeOtherBos = ICreativeConvertor.INSTANCE.grpcs2bos(dataList);

                return creativeOtherBos.stream()
                        .collect(Collectors.toMap(
                                CreativeOtherBo::getCreativeId,
                                t -> t,
                                (v1, v2) -> v2
                        ));
            } catch (Exception e) {
                throw new RuntimeException("gRPC调用失败", e);
            }
        });
    }

    // 超时降级方法
    private CompletableFuture<Map<Integer, CreativeOtherBo>> queryCreativeOtherInfoTimeoutFallback(
            List<Integer> creativeIds, TimeoutException e) {
        log.error("queryCreativeOtherInfoTimeoutFallback", e);
        return CompletableFuture.completedFuture(Collections.emptyMap());
    }

    // 通用降级方法（用于断路器等其他异常情况）
    private CompletableFuture<Map<Integer, CreativeOtherBo>> queryCreativeOtherInfoFallback(
            List<Integer> creativeIds, Exception e) {
        log.error("queryCreativeOtherInfoFallback", e);
        return CompletableFuture.completedFuture(Collections.emptyMap());
    }

}
