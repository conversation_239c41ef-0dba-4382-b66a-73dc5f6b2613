package com.bilibili.risk.service.black_word;

import com.bilibili.risk.constant.RiskConstants;
import com.bilibili.risk.dao.ad.BlackGrayWordDao;
import com.bilibili.risk.po.ad.BlackGrayWordPo;
import com.bilibili.risk.po.ad.BlackGrayWordPoExample;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class BlackGrayWordService {

    private final BlackGrayWordDao grayBlackWordDao;

    @Autowired
    private RedissonClient adpRedissonClient;

    private static final String BLACK_GRAY_WORD_CACHE_KEY = "risk:black_gray_word:all";
    private static final long CACHE_EXPIRE_TIME = 30; // 缓存过期时间，单位分钟

    /**
     * 查询所有黑灰词，使用Redis缓存优化
     *
     * @return 所有有效的黑灰词集合
     */
    public Set<String> queryAllWords() {
        // 1. 尝试从Redis缓存中获取
        RBucket<Set<String>> bucket = adpRedissonClient.getBucket(BLACK_GRAY_WORD_CACHE_KEY);
        Set<String> cachedWords = bucket.get();

        // 2. 如果缓存中存在且不为空，直接返回缓存数据
        if (cachedWords != null && !cachedWords.isEmpty()) {
            log.info("queryAllWords from cache, size: {}", cachedWords.size());
            return cachedWords;
        }

        // 3. 缓存不存在或为空，从数据库查询
        log.info("queryAllWords from database");
        BlackGrayWordPoExample example = new BlackGrayWordPoExample();
        example.createCriteria().andIsDeletedEqualTo(0);
        List<BlackGrayWordPo> blackGrayWordPos = grayBlackWordDao.selectByExample(example);
        Set<String> words = blackGrayWordPos.stream().map(BlackGrayWordPo::getWord).collect(Collectors.toSet());

        // 4. 将查询结果存入Redis缓存
        if (!words.isEmpty()) {
            bucket.set(words, CACHE_EXPIRE_TIME, TimeUnit.MINUTES);
            log.info("queryAllWords cache updated, size: {}", words.size());
        }

        return words;
    }

    /**
     * 清除黑灰词缓存
     * 当黑灰词数据发生变化时调用此方法
     */
    public void clearWordsCache() {
        RBucket<Set<String>> bucket = adpRedissonClient.getBucket(BLACK_GRAY_WORD_CACHE_KEY);
        bucket.delete();
        log.info("Black gray word cache cleared");
    }

    /**
     * 添加黑灰词
     *
     * @param wordType 词类型（1-黑词 2-灰词）
     * @param word 词项
     * @return 添加结果
     */
    @Transactional(rollbackFor = Exception.class)
    public int addWord(Integer wordType, String word) {
        log.info("Adding black/gray word: {}, type: {}", word, wordType);

        // 检查是否已存在
        BlackGrayWordPoExample example = new BlackGrayWordPoExample();
        example.createCriteria().andWordEqualTo(word).andIsDeletedEqualTo(0);
        List<BlackGrayWordPo> existingWords = grayBlackWordDao.selectByExample(example);

        if (!CollectionUtils.isEmpty(existingWords)) {
            log.info("Word already exists: {}", word);
            return 0;
        }

        // 添加新词
        BlackGrayWordPo wordPo = BlackGrayWordPo.builder()
                .wordType(wordType)
                .word(word)
                .isDeleted(0)
                .ctime(new Timestamp(System.currentTimeMillis()))
                .mtime(new Timestamp(System.currentTimeMillis()))
                .build();

        int result = grayBlackWordDao.insertSelective(wordPo);

        // 清除缓存
        if (result > 0) {
            clearWordsCache();
        }

        return result;
    }

    /**
     * 批量添加黑灰词
     *
     * @param wordType 词类型（1-黑词 2-灰词）
     * @param words 词项列表
     * @return 添加成功的数量
     */
    @Transactional(rollbackFor = Exception.class)
    public int batchAddWords(Integer wordType, List<String> words) {
        if (CollectionUtils.isEmpty(words)) {
            return 0;
        }

        log.info("Batch adding black/gray words, count: {}, type: {}", words.size(), wordType);

        // 过滤已存在的词
        BlackGrayWordPoExample example = new BlackGrayWordPoExample();
        example.createCriteria().andWordIn(words).andIsDeletedEqualTo(0);
        List<BlackGrayWordPo> existingWords = grayBlackWordDao.selectByExample(example);

        Set<String> existingWordSet = existingWords.stream()
                .map(BlackGrayWordPo::getWord)
                .collect(Collectors.toSet());

        List<BlackGrayWordPo> newWords = words.stream()
                .filter(word -> !existingWordSet.contains(word))
                .map(word -> BlackGrayWordPo.builder()
                        .wordType(wordType)
                        .word(word)
                        .isDeleted(0)
                        .ctime(new Timestamp(System.currentTimeMillis()))
                        .mtime(new Timestamp(System.currentTimeMillis()))
                        .build())
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(newWords)) {
            log.info("No new words to add");
            return 0;
        }

        int result = grayBlackWordDao.insertBatch(newWords);

        // 清除缓存
        if (result > 0) {
            clearWordsCache();
        }

        return result;
    }

    /**
     * 删除黑灰词（软删除）
     *
     * @param id 词ID
     * @return 删除结果
     */
    @Transactional(rollbackFor = Exception.class)
    public int deleteWord(Long id) {
        log.info("Deleting black/gray word, id: {}", id);

        BlackGrayWordPo wordPo = BlackGrayWordPo.builder()
                .id(id)
                .isDeleted(1) // 标记为已删除
                .mtime(new Timestamp(System.currentTimeMillis()))
                .build();

        int result = grayBlackWordDao.updateByPrimaryKeySelective(wordPo);

        // 清除缓存
        if (result > 0) {
            clearWordsCache();
        }

        return result;
    }

    /**
     * 更新黑灰词
     *
     * @param id 词ID
     * @param wordType 词类型
     * @param word 词项
     * @return 更新结果
     */
    @Transactional(rollbackFor = Exception.class)
    public int updateWord(Long id, Integer wordType, String word) {
        log.info("Updating black/gray word, id: {}, type: {}, word: {}", id, wordType, word);

        BlackGrayWordPo wordPo = BlackGrayWordPo.builder()
                .id(id)
                .wordType(wordType)
                .word(word)
                .mtime(new Timestamp(System.currentTimeMillis()))
                .build();

        int result = grayBlackWordDao.updateByPrimaryKeySelective(wordPo);

        // 清除缓存
        if (result > 0) {
            clearWordsCache();
        }

        return result;
    }

    /**
     * 查询所有黑灰词（包括详细信息）
     *
     * @return 黑灰词列表
     */
    public List<BlackGrayWordPo> queryAllWordsWithDetails() {
        BlackGrayWordPoExample example = new BlackGrayWordPoExample();
        example.createCriteria().andIsDeletedEqualTo(0);
        example.setOrderByClause("word_type asc, id desc");
        return grayBlackWordDao.selectByExample(example);
    }
}
