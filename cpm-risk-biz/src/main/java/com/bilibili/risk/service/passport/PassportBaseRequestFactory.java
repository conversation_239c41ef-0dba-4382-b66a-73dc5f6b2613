package com.bilibili.risk.service.passport;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * Created by <PERSON><PERSON><PERSON> on 2017/2/27.
 */
@Component
public class PassportBaseRequestFactory {
    @Value("${passport.app.key}")
    private String APP_KEY;

    public PassportBaseRequest createPassportBaseRequestWithOutSign() {
        PassportBaseRequest passportBaseRequest = new PassportBaseRequest();
        passportBaseRequest.setAppkey(APP_KEY);
        passportBaseRequest.setTs((int) (System.currentTimeMillis() / 1000));
        return passportBaseRequest;
    }
}
