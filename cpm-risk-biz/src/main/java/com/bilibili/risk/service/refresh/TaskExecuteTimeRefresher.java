package com.bilibili.risk.service.refresh;

import com.bilibili.adp.common.util.Page;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.risk.constant.RiskConstants;
import com.bilibili.risk.dao.risk.LauMaterialAuditTaskExtDao;
import com.bilibili.risk.enums.MaterialTaskStatusEnum;
import com.bilibili.risk.enums.MaterialTaskTypeEnum;
import com.bilibili.risk.po.risk.LauMaterialAuditTaskPo;
import com.bilibili.risk.po.risk.LauMaterialAuditTaskPoExample;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
@RequiredArgsConstructor
public class TaskExecuteTimeRefresher {

    private final LauMaterialAuditTaskExtDao lauMaterialAuditTaskExtDao;

    public void refresh(Integer isUpdate, Integer pointShadingNo, List<String> taskIds) {
        new Thread(() -> {
            try {
                doRefresh(isUpdate, pointShadingNo, taskIds);
            } catch (Exception e) {
                log.error("refresh[异常]", e);
            }
        }).start();
    }

    public void doRefresh(Integer isUpdate, Integer pointShadingNo, List<String> taskIds) {
        log.info("refresh[start], isUpdate: {},pointShadingNo={},taskIds={}", isUpdate, pointShadingNo, taskIds);

        long startTime = System.currentTimeMillis();
        final int MAX_SHARDING = 128; // 最大分片数

        // 遍历所有分片表
        Integer updateCount = 0;
        int endShadingNo = pointShadingNo != null ? pointShadingNo + 1 : MAX_SHARDING;
        int shadingNo = pointShadingNo != null ? pointShadingNo : 0;

        while (shadingNo < endShadingNo) {
            int page = 1;
            int pageSize = 300;
            int shadingUpdateCount = 0;
            boolean hasData = true;

            // 分页查询当前分片的数据
            while (hasData) {
                // 查询复用类型、已完成状态且执行时间为初始值的任务
                LauMaterialAuditTaskPoExample example = new LauMaterialAuditTaskPoExample();
                LauMaterialAuditTaskPoExample.Criteria criteria = example.createCriteria()
                        .andTypeEqualTo(MaterialTaskTypeEnum.REUSE.getCode())
                        .andExecuteTimeEqualTo(RiskConstants.BUSINESS_EARLIEST_TIMESTAMP)
                        .andStatusEqualTo(MaterialTaskStatusEnum.COMPLETE.getCode());

                if (!CollectionUtils.isEmpty(taskIds)) {
                    criteria.andTaskIdIn(taskIds);
                }

                Page pg = Page.valueOf(page, pageSize);
                example.setLimit(pg.getLimit());
                example.setOffset(pg.getOffset());

                // 查询数据
                List<LauMaterialAuditTaskPo> taskPos = lauMaterialAuditTaskExtDao.selectByExample(shadingNo, example);
                log.info("refresh[获取一批数据], shadingNo: {}, size={},page={}", shadingNo, taskPos.size(), page);

                // 如果没有数据，处理下一个分片
                if (taskPos == null || taskPos.isEmpty()) {
                    hasData = false;
                    log.info("refresh[该页没有数据了], shadingNo: {}, updateCount: {}, shadingUpdateCount: {},page={}", shadingNo, updateCount, shadingUpdateCount, page);
                    break;
                }

                // 更新每条记录的执行时间为创建时间
                for (LauMaterialAuditTaskPo taskPo : taskPos) {
                    LauMaterialAuditTaskPo copyTaskPo = new LauMaterialAuditTaskPo();
                    // 更新执行时间为创建时间
                    copyTaskPo.setId(taskPo.getId());
                    copyTaskPo.setExecuteTime(taskPo.getEnterAuditTime());
                    // mtime不变
                    copyTaskPo.setMtime(taskPo.getMtime());

                    // 调用更新方法
                    if (Utils.isPositive(isUpdate)) {
                        shadingUpdateCount += lauMaterialAuditTaskExtDao.updateByPrimaryKeySelective(shadingNo, copyTaskPo);
                        sleep();
                    } else {
                        // 模拟更新
                        updateCount++;
                        shadingUpdateCount++;
                        sleep();
                    }
                }
                log.info("refresh[更新一批数据], shadingNo: {}, updateCount: {}, shadingUpdateCount: {},page={}", shadingNo, updateCount, shadingUpdateCount, page);

                // 处理下一页
                page++;
            }

            // 处理下一个分片
            shadingNo++;
        }

        long endTime = System.currentTimeMillis();
        log.info("refresh[所有分表刷完了], shadingNo: {}, updateCount: {},cost={}", shadingNo, updateCount, endTime - startTime);

    }

    private static void sleep() {
        try {
            Thread.sleep(50);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }
}
