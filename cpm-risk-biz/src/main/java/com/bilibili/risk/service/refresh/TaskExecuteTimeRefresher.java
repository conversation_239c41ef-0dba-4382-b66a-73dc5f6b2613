package com.bilibili.risk.service.refresh;

import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.util.Page;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.risk.bo.MaterialAuditTaskCommonQueryBo;
import com.bilibili.risk.bo.MaterialAuditTaskEsBo;
import com.bilibili.risk.constant.RiskConstants;
import com.bilibili.risk.dao.risk.LauMaterialAuditTaskExtDao;
import com.bilibili.risk.enums.MaterialTaskStatusEnum;
import com.bilibili.risk.enums.MaterialTaskTypeEnum;
import com.bilibili.risk.po.risk.LauMaterialAuditTaskPo;
import com.bilibili.risk.po.risk.LauMaterialAuditTaskPoExample;
import com.bilibili.risk.service.task.MaterialAuditTaskEsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
@Slf4j
@RequiredArgsConstructor
public class TaskExecuteTimeRefresher {

    private final LauMaterialAuditTaskExtDao lauMaterialAuditTaskExtDao;
    private final MaterialAuditTaskEsService materialAuditTaskEsService;

    public void refresh(Integer isUpdate, Integer pointShadingNo, List<String> taskIds) {
        new Thread(() -> {
            try {
                doRefresh(isUpdate, pointShadingNo, taskIds);
            } catch (Exception e) {
                log.error("refresh[异常]", e);
            }
        }).start();
    }

    public void doRefresh(Integer isUpdate, Integer pointShadingNo, List<String> taskIds) {
        log.info("refresh[start], isUpdate: {},pointShadingNo={},taskIds={}", isUpdate, pointShadingNo, taskIds);

        long startTime = System.currentTimeMillis();
        final int MAX_SHARDING = 128; // 最大分片数

        // 遍历所有分片表
        Integer updateCount = 0;
        int endShadingNo = pointShadingNo != null ? pointShadingNo + 1 : MAX_SHARDING;
        int shadingNo = pointShadingNo != null ? pointShadingNo : 0;

        while (shadingNo < endShadingNo) {
            int shadingUpdateCount = 0;
            boolean hasData = true;

            // 分页查询当前分片的数据
            Long minId = 0L;
            while (hasData) {
                sleep();

                // mtime 3:00 到 3:50
                LauMaterialAuditTaskPoExample example = new LauMaterialAuditTaskPoExample();
                LauMaterialAuditTaskPoExample.Criteria criteria = example.createCriteria()
                        .andMtimeGreaterThan(Timestamp.valueOf("2025-06-11 15:10:00"))
                        .andMtimeLessThan(Timestamp.valueOf("2025-06-11 15:50:00"))
                        .andIdGreaterThan(minId);
                example.setOrderByClause("id asc");

                if (!CollectionUtils.isEmpty(taskIds)) {
                    criteria.andTaskIdIn(taskIds);
                }

//                Page pg = Page.valueOf(page, pageSize);
                example.setLimit(100);
//                example.setOffset(pg.getOffset());

                // 查询数据
                List<LauMaterialAuditTaskPo> taskPos = lauMaterialAuditTaskExtDao.selectByExample(shadingNo, example);
                log.info("refresh[获取一批数据], shadingNo: {}, size={},minId={}", shadingNo, taskPos.size(), minId);

                // 如果没有数据，处理下一个分片
                if (taskPos == null || taskPos.isEmpty()) {
                    hasData = false;
                    log.info("refresh[该页没有数据了], shadingNo: {}, updateCount: {}, shadingUpdateCount: {},minId={}", shadingNo, updateCount, shadingUpdateCount, minId);
                    break;
                }
                LauMaterialAuditTaskPo lastTask = taskPos.get(taskPos.size() - 1);
                minId = lastTask.getId();
                List<String> dbTaskIds = taskPos.stream().map(t -> t.getTaskId()).collect(Collectors.toList());

                MaterialAuditTaskCommonQueryBo queryBo = MaterialAuditTaskCommonQueryBo.builder()
                        .ids(dbTaskIds)
                        .page(1)
                        .pageSize(500)
                        .build();
                PageResult<MaterialAuditTaskEsBo> pageResult = materialAuditTaskEsService.queryMaterialAuditTaskEsBosCommon(queryBo);
                Map<String, MaterialAuditTaskEsBo> taskEsMap = pageResult.getRecords().stream().collect(Collectors.toMap(t -> t.getId(), t -> t, (t1, t2) -> t1));

                // 更新每条记录的执行时间为创建时间
                for (LauMaterialAuditTaskPo taskPo : taskPos) {

                    MaterialAuditTaskEsBo taskEsBo = taskEsMap.get(taskPo.getTaskId());
                    if (taskEsBo == null) {
                        continue;
                    }
                    if (Objects.equals(taskEsBo.getStatus(), taskPo.getStatus())) {
                        continue;
                    }

                    LauMaterialAuditTaskPo copyTaskPo = new LauMaterialAuditTaskPo();
                    // 更新执行时间为创建时间
                    copyTaskPo.setId(taskPo.getId());
                    copyTaskPo.setMtime(Utils.getNow());

                    // 调用更新方法
                    if (Utils.isPositive(isUpdate)) {
                        shadingUpdateCount += lauMaterialAuditTaskExtDao.updateByPrimaryKeySelective(shadingNo, copyTaskPo);
                    } else {
                        // 模拟更新
                        updateCount++;
                        shadingUpdateCount++;
                    }
                }
                log.info("refresh[更新一批数据], shadingNo: {}, updateCount: {}, shadingUpdateCount: {},minId={}", shadingNo, updateCount, shadingUpdateCount, minId);

            }

            // 处理下一个分片
            shadingNo++;
        }

        long endTime = System.currentTimeMillis();
        log.info("refresh[所有分表刷完了], shadingNo: {}, updateCount: {},cost={}", shadingNo, updateCount, endTime - startTime);

    }

    private static void sleep() {
        try {
            Thread.sleep(50);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }
}
