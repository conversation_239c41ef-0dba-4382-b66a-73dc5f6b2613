package com.bilibili.risk.service.task.pull;

import com.bilibili.risk.bo.MaterialAuditTaskPullQueryBo;
import com.bilibili.risk.enums.MaterialTaskPullStrategyEnum;
import com.bilibili.risk.enums.MetricsCodeEnum;
import com.bilibili.risk.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 审核任务拉取策略工厂类
 * 根据拉取策略枚举选择对应的策略实现
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class MaterialAuditTaskPullStrategyFactory {

    private final MaterialAuditTaskPullStrategyBySingleQueue pullStrategyBySingleQueue;
    private final MaterialAuditTaskPullStrategyByRulePerQueue pullStrategyByRulePerQueue;
    private final MaterialAuditTaskPullStrategyByRuleOldestTime pullStrategyByRuleOldestTime;

    /**
     * 根据拉取策略类型获取对应的策略实现
     *
     * @param pullQueryBo 拉取查询参数
     * @return 对应的策略实现
     */
    public IMaterialAuditTaskPullStrategy getStrategy(MaterialAuditTaskPullQueryBo pullQueryBo) {
        if (pullQueryBo == null || pullQueryBo.getPullStrategy() == null) {
            log.error("获取拉取策略失败，参数为空: {}", pullQueryBo);
            throw new BusinessException(MetricsCodeEnum.DomainType.CREATIVE_MATERIAL_TASK, MetricsCodeEnum.Type.BIZ_ERR, MetricsCodeEnum.SubCode.PARAM_INVALID, "拉取策略参数不能为空");
        }

        Integer pullStrategy = pullQueryBo.getPullStrategy();

        // 根据单个队列ID拉取
        if (Objects.equals(pullStrategy, MaterialTaskPullStrategyEnum.BY_SINGLE_QUEUE_ID.getKey())) {
            log.info("使用单队列拉取策略，queueId: {}", pullQueryBo.getQueueId());
            pullQueryBo.setPullByRule(false);
            return pullStrategyBySingleQueue;
        }

        // 根据规则按队列优先级拉取
        else if (Objects.equals(pullStrategy, MaterialTaskPullStrategyEnum.BY_RULE_PER_QUEUE.getKey())) {
            log.info("使用规则按队列顺序拉取策略，ruleId: {}", pullQueryBo.getRuleId());
            pullQueryBo.setPullByRule(true);
            return pullStrategyByRulePerQueue;


        } else if (Objects.equals(pullStrategy, MaterialTaskPullStrategyEnum.BY_RULE_CTIME_ASC.getKey())) {
            log.info("使用规则按队列顺序拉取策略，ruleId: {}", pullQueryBo.getRuleId());
            pullQueryBo.setPullByRule(true);
            return pullStrategyByRuleOldestTime;
        }

        log.error("未找到匹配的拉取策略: {}", pullStrategy);
        throw new BusinessException(MetricsCodeEnum.DomainType.CREATIVE_MATERIAL_TASK, MetricsCodeEnum.Type.BIZ_ERR, MetricsCodeEnum.SubCode.PARAM_INVALID, "未找到匹配的拉取策略: " + pullStrategy);
    }
}