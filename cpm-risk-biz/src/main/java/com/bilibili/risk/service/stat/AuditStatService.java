package com.bilibili.risk.service.stat;

import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.risk.bo.*;
import com.bilibili.risk.constant.RiskConstants;
import com.bilibili.risk.enums.MaterialTaskStatusEnum;
import com.bilibili.risk.po.es.LauMaterialAuditTaskEsPo;
import com.bilibili.risk.po.risk.LauMaterialAuditRuleQueueRelPo;
import com.bilibili.risk.service.rule.MaterialAuditRuleQueueRelService;
import com.bilibili.risk.service.task.MaterialAuditTaskEsService;
import edu.emory.mathcs.backport.java.util.Collections;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class AuditStatService {

    private final MaterialAuditTaskEsService materialAuditTaskEsService;
    private final MaterialAuditRuleQueueRelService materialAuditRuleQueueRelService;

    public OneDayAuditCountBo queryTodayAuditCount(String username, Long startTime) {

        OneDayAuditCountBo oneDayAuditCountBo = new OneDayAuditCountBo();
        oneDayAuditCountBo.setTodayAuditCount(getOneDayCount(username, Utils.getToday()));
        oneDayAuditCountBo.setYesterdayAuditCount(getOneDayCount(username, Utils.getYesteday()));
        return oneDayAuditCountBo;
    }

    private int getOneDayCount(String username, Timestamp today) {
        Timestamp todayStart = Utils.getBeginOfDay(today);
        Timestamp todayEnd = Utils.getEndOfDay(today);
        // 今天审核量
        MaterialAuditTaskCommonQueryBo todayQueryBo = MaterialAuditTaskCommonQueryBo.builder()
                .executeNames(Arrays.asList(username))
                .executeTimeStart(todayStart.getTime())
                .executeTimeEnd(todayEnd.getTime())
                .page(1)
                .pageSize(0)
                .sortOrder(SortOrder.ASC)
                .sortField(RiskConstants.SORT_FIELD_MTIME)
                .build();
        PageResult<LauMaterialAuditTaskEsPo> pageResult = materialAuditTaskEsService.queryMaterialAuditTaskEsPosCommon(todayQueryBo);
        int count = pageResult.getTotal();
        return count;
    }

    public OneDayAuditAvgTimeBo queryTodayAuditAvgSecs(String username, Long startTime) {
        OneDayAuditAvgTimeBo avgTimeBo = new OneDayAuditAvgTimeBo();
        // 今天平均审核时长
        avgTimeBo.setTodayAuditSecs(getOneDayAvgSecsByBatchNo(username, Utils.getToday()) / 1000);
        // 昨天平均审核时长
        avgTimeBo.setYesterdayAuditSecs(getOneDayAvgSecsByBatchNo(username, Utils.getYesteday()) / 1000);
        return avgTimeBo;
    }

    private long getOneDayAvgSecs(String username, Timestamp today) {
        Timestamp todayStart = Utils.getBeginOfDay(today);
        Timestamp todayEnd = Utils.getEndOfDay(today);
        // 今天审核量
        MaterialAuditTaskCommonQueryBo queryBo = MaterialAuditTaskCommonQueryBo.builder()
                .executeNames(Arrays.asList(username))
                .executeTimeStart(todayStart.getTime())
                .executeTimeEnd(todayEnd.getTime())
                .statusList(Arrays.asList(MaterialTaskStatusEnum.COMPLETE.getCode()))
                .build();
        AvgSecsResultBo avgSecsResultBo = materialAuditTaskEsService.queryAvgSecs(queryBo);
        return avgSecsResultBo.getAvgSecs() != null ? avgSecsResultBo.getAvgSecs() : 0L;
    }

    /**
     * 计算用户当日审核平均时长（使用分页查询优化）
     * 计算方式：
     * 1. 有批次号，用每批的时长累加，再除以总的条数
     * 2. 没有批次号的，直接用每条记录的时长累加(没有领取时间，用进审时间)，除以总条数
     *
     * @param username 用户名
     * @param today 日期
     * @return 平均审核时长（毫秒）
     */
    private long getOneDayAvgSecsByBatchNo(String username, Timestamp today) {
        if (StringUtils.isEmpty(username)) {
            return 0L;
        }

        final int PAGE_SIZE = 500;
        int currentPage = 1;
        boolean hasMore = true;

        long totalItemCount = 0L;  // 总条数，而不是批次数
        long totalSecs = 0L;

        // 记录已处理的批次号，避免重复计算
        Set<String> processedBatchNos = new HashSet<>();

        // 设置基础查询条件
        MaterialAuditTaskCommonQueryBo.MaterialAuditTaskCommonQueryBoBuilder queryBoBuilder = MaterialAuditTaskCommonQueryBo.builder()
                .executeNames(Arrays.asList(username))
                .executeTimeStart(Utils.getBeginOfDay(today).getTime())
                .executeTimeEnd(Utils.getEndOfDay(today).getTime())
                .statusList(Arrays.asList(MaterialTaskStatusEnum.COMPLETE.getCode()))
                .sortOrder(SortOrder.ASC)
                .sortField(RiskConstants.SORT_FIELD_ID);

        log.info("getOneDayAvgSecsByBatchNo, 开始分页查询用户{}在{}的审核数据，每页{}条", username, today, PAGE_SIZE);

        // 分页查询处理
        while (hasMore) {
            MaterialAuditTaskCommonQueryBo queryBo = queryBoBuilder
                    .page(currentPage)
                    .pageSize(PAGE_SIZE)
                    .build();
            // 查询es数据
            PageResult<LauMaterialAuditTaskEsPo> pageResult = materialAuditTaskEsService.queryMaterialAuditTaskEsPosCommon(queryBo);
            List<LauMaterialAuditTaskEsPo> records = pageResult.getRecords();

            if (CollectionUtils.isEmpty(records)) {
                break;
            }
            log.debug("getOneDayAvgSecsByBatchNo, 处理第{}页数据，获取到{}条记录", currentPage, records.size());

            // 分成有批次和无批次号的
            Map<String, List<LauMaterialAuditTaskEsPo>> taskEsBoMapByBatchNo = new HashMap<>();
            List<LauMaterialAuditTaskEsPo> noBatchNoTaskBos = new ArrayList<>();

            for (LauMaterialAuditTaskEsPo taskEsBo : records) {
                if (StringUtils.isEmpty(taskEsBo.getReceiveBatchNo())) {
                    noBatchNoTaskBos.add(taskEsBo);
                } else {
                    // 对于有批次号的，先分组
                    String batchNo = taskEsBo.getReceiveBatchNo();
                    if (!processedBatchNos.contains(batchNo)) {
                        taskEsBoMapByBatchNo.computeIfAbsent(batchNo, k -> new ArrayList<>()).add(taskEsBo);
                    }
                }
            }

            // 处理有批次号的任务
            for (Map.Entry<String, List<LauMaterialAuditTaskEsPo>> entry : taskEsBoMapByBatchNo.entrySet()) {
                String batchNo = entry.getKey();
                List<LauMaterialAuditTaskEsPo> taskEsBos = entry.getValue();

                if (CollectionUtils.isEmpty(taskEsBos)) {
                    continue;
                }

                LauMaterialAuditTaskEsPo oneTaskEsBo = taskEsBos.get(0);
                if (oneTaskEsBo.getExecuteTime() == null || oneTaskEsBo.getAcceptTime() == null) {
                    continue;
                }

                // 统计批次中的实际任务数量
                int batchItemCount = taskEsBos.size();
                totalItemCount += batchItemCount;  // 累计总条数而不是批次数

                // 没有领取时间，用进审时间代替
                long startTime = oneTaskEsBo.getAcceptTime().getTime() == RiskConstants.BUSINESS_EARLIEST_TIMESTAMP_LONG
                        ? oneTaskEsBo.getEnterAuditTime().getTime()
                        : oneTaskEsBo.getAcceptTime().getTime();
                long batchTotalSecs = oneTaskEsBo.getExecuteTime().getTime() - startTime;

                // 将批次总时长添加到总时长
                totalSecs += batchTotalSecs;

                // 记录已处理的批次号
                processedBatchNos.add(batchNo);
            }

            // 处理无批次号的任务
            for (LauMaterialAuditTaskEsPo taskEsBo : noBatchNoTaskBos) {
                if (taskEsBo.getExecuteTime() == null || taskEsBo.getAcceptTime() == null) {
                    continue;
                }

                // 无批次号的任务，每个任务算作一条
                totalItemCount++;

                // 没有领取时间，用进审时间代替
                long startTime = taskEsBo.getAcceptTime().getTime() == RiskConstants.BUSINESS_EARLIEST_TIMESTAMP_LONG
                        ? taskEsBo.getEnterAuditTime().getTime()
                        : taskEsBo.getAcceptTime().getTime();
                long taskSecs = taskEsBo.getExecuteTime().getTime() - startTime;
                totalSecs += taskSecs;
            }

            // 判断是否有更多数据
            hasMore = records.size() == PAGE_SIZE && pageResult.getTotal() > currentPage * PAGE_SIZE;
            currentPage++;
        }

        log.info("getOneDayAvgSecsByBatchNo, 用户{}在{}的审核数据处理完成，共处理{}条记录,总耗时{}", username, today, totalItemCount, totalSecs);
        if (totalItemCount == 0L) {
            return 0L;
        }
        // 使用总条数而不是总批次数来计算平均时长
        return totalSecs / totalItemCount;
    }

    public OneMonthAuditCountBo queryMonthAuditCount(String username, Long startTime) {

        Timestamp thisMonthBeginDayOfMonth = Utils.getBeginDayOfMonth(Utils.getToday());
        Timestamp lastMonthBeginDayOfMonth = Utils.getBeginDayOfMonth(Utils.getSomeDayAfter(thisMonthBeginDayOfMonth, -1));

        OneMonthAuditCountBo oneDayAuditCountBo = new OneMonthAuditCountBo();
        oneDayAuditCountBo.setThisMonthAuditCount(getOneMonthCount(username, thisMonthBeginDayOfMonth));
        oneDayAuditCountBo.setLastMonthAuditCount(getOneMonthCount(username, lastMonthBeginDayOfMonth));
        return oneDayAuditCountBo;
    }

    private int getOneMonthCount(String username, Timestamp beginDayOfMonth) {
        Timestamp monthStart = Utils.getBeginDayOfMonth(beginDayOfMonth);
        Timestamp monthEnd = Utils.getEndDayOfMonth(beginDayOfMonth);
        // 今天审核量
        MaterialAuditTaskCommonQueryBo todayQueryBo = MaterialAuditTaskCommonQueryBo.builder()
                .executeNames(Arrays.asList(username))
                .executeTimeStart(monthStart.getTime())
                .executeTimeEnd(monthEnd.getTime())
                .page(1)
                .pageSize(0)
                .build();
        PageResult<LauMaterialAuditTaskEsPo> pageResult = materialAuditTaskEsService.queryMaterialAuditTaskEsPosCommon(todayQueryBo);
        int count = pageResult.getTotal();
        return count;
    }

    public List<QueueStatBo> queryQueueStat(List<Long> queueIds, String username, boolean withOldestAuditTime) {

        if (CollectionUtils.isEmpty(queueIds)) {
            return Collections.emptyList();
        }

        // 查询游离的，未审核的量
        MaterialAuditTaskCommonQueryBo freeQueryBo = MaterialAuditTaskCommonQueryBo.builder()
                .queueIds(queueIds)
                .statusList(Arrays.asList(MaterialTaskStatusEnum.FREE.getCode()))
                .withOldestAuditTime(withOldestAuditTime)
                .build();
        log.info("queryQueueStat[游离未审核统计] freeQueryBo: {}", freeQueryBo);
        List<QueueStatBo> tuAuditQueueStatBos = materialAuditTaskEsService.queryStatGroupByQueueId(freeQueryBo);

        // 查询这个用户今天已审核的量
        MaterialAuditTaskCommonQueryBo hasAuditQueryBo = MaterialAuditTaskCommonQueryBo.builder()
                .queueIds(queueIds)
                .statusList(Arrays.asList(MaterialTaskStatusEnum.COMPLETE.getCode()))
                .executeTimeStart(Utils.getBeginOfDay(Utils.getNow()).getTime())
                .executeTimeEnd(Utils.getEndOfDay(Utils.getNow()).getTime())
                .withOldestAuditTime(false)
                .build();
//        if (!StringUtils.isEmpty(username)) {
//            hasAuditQueryBo.setExecuteNames(Arrays.asList(username));
//        }
        log.info("queryQueueStat[已审核量统计] hasAuditQueryBo: {}", hasAuditQueryBo);
        List<QueueStatBo> hasAuditQueueStatBos = materialAuditTaskEsService.queryStatGroupByQueueId(hasAuditQueryBo);
        // 合并结果
        return mergeQueueStats(tuAuditQueueStatBos, hasAuditQueueStatBos);
    }

    public List<QueueStatBo> mergeQueueStats(List<QueueStatBo> toAuditQueueStatBos,
                                             List<QueueStatBo> hasAuditQueueStatBos) {
        // 创建以queueId为键的映射表
        Map<Long, QueueStatBo> mergedMap = new HashMap<>();

        // 处理待审核队列
        for (QueueStatBo toAuditBo : toAuditQueueStatBos) {
            QueueStatBo queueStatBo = mergedMap.get(toAuditBo.getQueueId());
            if (queueStatBo == null) {
                queueStatBo = QueueStatBo.builder().queueId(toAuditBo.getQueueId()).build();
                mergedMap.put(toAuditBo.getQueueId(), queueStatBo);
            }
            queueStatBo.setToAuditCount(toAuditBo.getAuditCount());
            queueStatBo.setOldestAuditTime(toAuditBo.getOldestAuditTime());
        }

        // 处理已审核队列
        for (QueueStatBo hasAuditBo : hasAuditQueueStatBos) {
            QueueStatBo queueStatBo = mergedMap.get(hasAuditBo.getQueueId());
            if (queueStatBo == null) {
                queueStatBo = QueueStatBo.builder().queueId(hasAuditBo.getQueueId()).build();
                mergedMap.put(hasAuditBo.getQueueId(), queueStatBo);
            }
            queueStatBo.setHasAuditCount(hasAuditBo.getAuditCount());
        }

        // 返回合并后的列表
        return new ArrayList<>(mergedMap.values());
    }

    public List<RuleStatBo> queryRulesStat(List<Long> ruleIds, String username) {

        if (CollectionUtils.isEmpty(ruleIds)) {
            return Collections.emptyList();
        }

        List<LauMaterialAuditRuleQueueRelPo> relPos = materialAuditRuleQueueRelService.queryAuditRuleQueueRelListByRuleIds(ruleIds);
        Map<Long, List<LauMaterialAuditRuleQueueRelPo>> queueRelPoMap = relPos.stream().collect(Collectors.groupingBy(t -> t.getRuleId()));
        List<Long> queueIds = relPos.stream().map(t -> t.getQueueId()).distinct().collect(Collectors.toList());

        // 查询队列的统计指标(规则需要查询游离最早的进审时间，队列不需要)
        List<QueueStatBo> queueStatBos = this.queryQueueStat(queueIds, username, true);
        Map<Long, QueueStatBo> queueStatBoMap = queueStatBos.stream().collect(Collectors.toMap(QueueStatBo::getQueueId, t -> t));

        List<RuleStatBo> ruleStatBos = new ArrayList<>();
        for (Long ruleId : ruleIds) {
            RuleStatBo ruleStatBo = RuleStatBo.builder().ruleId(ruleId).toAuditCount(0L).hasAuditCount(0L).oldestAuditTime(null).build();
            List<LauMaterialAuditRuleQueueRelPo> relPosOfRule = queueRelPoMap.get(ruleId);
            if (CollectionUtils.isEmpty(relPosOfRule)) {
                continue;
            }

            for (LauMaterialAuditRuleQueueRelPo relPo : relPosOfRule) {
                QueueStatBo queueStatBo = queueStatBoMap.get(relPo.getQueueId());
                if (queueStatBo == null) {
                    continue;
                }
                ruleStatBo.setToAuditCount(ruleStatBo.getToAuditCount() + (queueStatBo.getToAuditCount() == null ? 0L : queueStatBo.getToAuditCount()));
                ruleStatBo.setHasAuditCount(ruleStatBo.getHasAuditCount() + (queueStatBo.getHasAuditCount() == null ? 0L : queueStatBo.getHasAuditCount()));

                if (ruleStatBo.getOldestAuditTime() == null) {
                    ruleStatBo.setOldestAuditTime(queueStatBo.getOldestAuditTime());
                } else if (ruleStatBo.getOldestAuditTime() != null && queueStatBo.getOldestAuditTime() != null && queueStatBo.getOldestAuditTime() < ruleStatBo.getOldestAuditTime()) {
                    ruleStatBo.setOldestAuditTime(queueStatBo.getOldestAuditTime());
                }
            }

            ruleStatBos.add(ruleStatBo);
        }
        return ruleStatBos;
    }

}
