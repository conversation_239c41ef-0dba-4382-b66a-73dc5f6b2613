package com.bilibili.risk.service.unit;

import com.bilibili.risk.bo.LauUnitExtraBo;
import com.bilibili.risk.convertor.IUnitConvertor;
import com.bilibili.risk.dao.ad_core.LauUnitExtraDao;
import com.bilibili.risk.po.ad.LauUnitExtraPo;
import com.bilibili.risk.po.ad.LauUnitExtraPoExample;
import edu.emory.mathcs.backport.java.util.Collections;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class UnitExtraService {

    private final LauUnitExtraDao lauUnitExtraDao;

    public List<LauUnitExtraBo> queryUnitExtraBos(List<Integer> unitIds) {

        if (unitIds == null || unitIds.isEmpty()) {
            return Collections.emptyList();
        }

        LauUnitExtraPoExample example = new LauUnitExtraPoExample();
        example.createCriteria().andUnitIdIn(unitIds);
        List<LauUnitExtraPo> lauUnitExtraPos = lauUnitExtraDao.selectByExample(example);
        return IUnitConvertor.INSTANCE.pos2bos(lauUnitExtraPos);
    }

    public LauUnitExtraBo fetchUnitExtraBo(Integer unitId) {

        Map<Integer, LauUnitExtraBo> map = this.queryUnitExtraBoMap(Arrays.asList(unitId));
        return map.get(unitId);
    }

    public Map<Integer, LauUnitExtraBo> queryUnitExtraBoMap(List<Integer> unitIds) {
        List<LauUnitExtraBo> lauUnitExtraBos = this.queryUnitExtraBos(unitIds);
        return lauUnitExtraBos.stream().collect(Collectors.toMap(LauUnitExtraBo::getUnitId, lauUnitExtraBo -> lauUnitExtraBo, (a, b) -> b));
    }
}
