package com.bilibili.risk.service.creative;

import com.bilibili.risk.bo.QueryMaterialCreativeBo;
import com.bilibili.risk.dao.risk.LauMaterialToAuditCreativeDao;
import com.bilibili.risk.po.risk.LauMaterialToAuditCreativePo;
import com.bilibili.risk.po.risk.LauMaterialToAuditCreativePoExample;
import com.google.common.collect.Lists;
import edu.emory.mathcs.backport.java.util.Collections;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class LauMaterialToAuditCreativeService {

    private final LauMaterialToAuditCreativeDao lauMaterialToAuditCreativeDao;

    public List<LauMaterialToAuditCreativePo> queryMaterialToAuditCreativeIds(List<Integer> creativeIds) {
        if (creativeIds == null || creativeIds.isEmpty()) {
            return Lists.newArrayList();
        }
        LauMaterialToAuditCreativePoExample example = new LauMaterialToAuditCreativePoExample();
        example.createCriteria().andCreativeIdIn(creativeIds);
        return lauMaterialToAuditCreativeDao.selectByExample(example);
    }

    public List<QueryMaterialCreativeBo> queryMaterialCreatives(List<Integer> creativeIds) {
        if (creativeIds == null || creativeIds.isEmpty()) {
            return Lists.newArrayList();
        }

        List<LauMaterialToAuditCreativePo> toAuditCreativePos = this.queryMaterialToAuditCreativeIds(creativeIds);
        Set<Integer> creativeIdSet = toAuditCreativePos.stream().map(t -> t.getCreativeId()).collect(Collectors.toSet());
        return creativeIds.stream().map(c -> QueryMaterialCreativeBo.builder().creativeId(c).isSplitCreative(creativeIdSet.contains(c)).build()).collect(Collectors.toList());
    }

    public LauMaterialToAuditCreativePo fetchByCreativeId(Integer creativeId) {
        LauMaterialToAuditCreativePoExample example = new LauMaterialToAuditCreativePoExample();
        example.createCriteria().andCreativeIdEqualTo(creativeId);
        List<LauMaterialToAuditCreativePo> list = lauMaterialToAuditCreativeDao.selectByExample(example);
        if (list.isEmpty()) {
            return null;
        }
        return list.get(0);
    }

    public Integer save(LauMaterialToAuditCreativePo po) {
        return lauMaterialToAuditCreativeDao.insertUpdateSelective(po);
    }

}
