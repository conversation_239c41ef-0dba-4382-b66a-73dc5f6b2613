package com.bilibili.risk.service.component;

import com.bilibili.risk.bo.ComponentMaterialBo;
import com.bilibili.risk.bo.CreativeDetailInfoBo;
import com.bilibili.risk.bo.CreativeDetailMaterialBo;
import com.bilibili.risk.bo.LauMaterialAuditBo;
import com.bilibili.risk.bo.msg.ComponentMsgBo;
import com.bilibili.risk.bo.msg.OtherSourceFirePushAuditMsgBo;
import com.bilibili.risk.convertor.IMaterialAuditConvertor;
import com.bilibili.risk.enums.FirePushAuditSourceEnum;
import com.bilibili.risk.po.ad.LauCreativeComponentPo;
import com.bilibili.risk.service.material.CreativeMaterialPushToAuditService;
import com.bilibili.risk.service.material.LauMaterialAuditService;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class ComponentMaterialSyncService {

    private final LauMaterialAuditService lauMaterialAuditService;
    private final CreativeComponentService creativeComponentService;
    private final CreativeMaterialPushToAuditService creativeMaterialPushToAuditService;

    /**
     * 查询组件关联的所有的创意，同步素材创意关系等
     * 消费的时候一次处理一个组件
     */
    public void relatedCreativeSplitMaterialsPushToAudit(List<Long> componentIds, Integer componentType) {
        log.info("relatedCreativeSplitMaterialsPushToAudit componentIds: {}, componentType={}", componentIds, componentType);
        if (CollectionUtils.isEmpty(componentIds)) {
            return;
        }
        Long componentId = componentIds.get(0);
        if (componentId == null) {
            log.error("componentId is null");
            return;
        }

        // 1、先根据componentId落一次素材表
        ComponentMaterialBo componentMaterialBo = creativeComponentService.fetchComponentMaterialInfo(componentId, componentType);

        if(null == componentMaterialBo) {
            log.error("componentMaterialBo is null, componentId: {}, componentType: {}", componentId, componentType);
            return;
        }

        CreativeDetailInfoBo creativeDetailInfoBo = CreativeDetailInfoBo.builder()
                .componentTitleBos(componentMaterialBo.getComponentTitleBos())
                .componentDescBos(componentMaterialBo.getComponentDescBos())
                .componentImageBos(componentMaterialBo.getComponentImageBos())
                .underThirdPartyLandingPageUrlBos(componentMaterialBo.getUnderThirdPartyLandingPageUrlBos())
                .build();

        creativeMaterialPushToAuditService.processAllMaterialsContent(creativeDetailInfoBo);

        // 2、落素材创意关系表
        List<CreativeDetailMaterialBo> componentMaterialBos = creativeMaterialPushToAuditService.splitAndMergeMaterials(creativeDetailInfoBo);
        List<LauMaterialAuditBo> materialBos = IMaterialAuditConvertor.INSTANCE.creativeDetailInfoOfMaterialBo2MaterialAuditBos(componentMaterialBos);
        lauMaterialAuditService.genMd5AndSaveMaterials(materialBos);

        List<LauCreativeComponentPo> creativeComponentPos;
        Integer size = 0, creativeId = null;
        do {
            creativeComponentPos = creativeComponentService.scrollQuery(componentId, componentType, creativeId, 100);
            Set<Integer> creativeIds = creativeComponentPos.stream().map(LauCreativeComponentPo::getCreativeId).collect(Collectors.toSet());

            // 查询创意关联的组件 creative_component

            if (CollectionUtils.isEmpty(creativeIds)) {
                break;
            }
            OtherSourceFirePushAuditMsgBo msgBo = OtherSourceFirePushAuditMsgBo.builder().source(FirePushAuditSourceEnum.STORY.getCode())
                    .componentId(componentId).componentType(componentType).build();
            creativeMaterialPushToAuditService.firePushAuditIfNeed(creativeIds, msgBo);

            creativeId = creativeComponentPos.get(creativeComponentPos.size() - 1).getCreativeId();
            size += creativeComponentPos.size();
            log.info("relatedCreativeSplitMaterialsPushToAudit size: {}", size);
        } while (creativeComponentPos.size() == 100);

        log.info("relatedCreativeSplitMaterialsPushToAudit end");
    }
}
