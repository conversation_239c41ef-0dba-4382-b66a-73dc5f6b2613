package com.bilibili.risk.service.dynamic;

import com.bapis.account.service.relation.MidsReq;
import com.bapis.account.service.relation.RelationGrpc;
import com.bapis.account.service.relation.StatReply;
import com.bapis.account.service.relation.StatsReply;
import com.bapis.cosmo.conn.common.OpusCard;
import com.bapis.dynamic.service.feed.*;
import com.bilibili.risk.bo.*;
import com.bilibili.risk.config.DynamicProperties;
import com.bilibili.risk.constant.HttpConstants;
import com.bilibili.risk.constant.HttpResponse;
import com.bilibili.risk.convertor.MiscMapper;
import com.bilibili.risk.exception.RiskRemoteAccessErr;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import pleiades.venus.starter.rpc.client.RPCClient;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class BiliDynamicService {
    // 图文
    public static final int DYN_TYPE_PIC_AND_TXT = 2;
    // 文字
    public static final int DYN_TYPE_TXT = 4;
    // 稿件
    public static final int DYN_TYPE_ARCHIVE = 8;

    @RPCClient("main.dynamic.feed-service")
    private FeedGrpc.FeedBlockingStub feedBlockingStub;
    @RPCClient("account.service.relation")
    private RelationGrpc.RelationBlockingStub relationBlockingStub;

    private final OkHttpClient okHttpClient;
    private final ObjectMapper objectMapper;

    @Autowired
    private DynamicProperties dynamicProperties;

    public Map<Long, BiliDynamicBo> getMap(Collection<Long> dynamicIds, boolean includeStats) {
        if (CollectionUtils.isEmpty(dynamicIds)) {
            return Collections.emptyMap();
        }
        DynSimpleInfosReq.Builder reqBuilder = DynSimpleInfosReq.newBuilder()
                .addAllDynIds(dynamicIds);
        if (includeStats) {
            reqBuilder.setOption(DynSimpleInfoOption.newBuilder()
                    .setStats(true)
                    .build());
        }
        DynSimpleInfosRsp resp = feedBlockingStub.dynSimpleInfos(reqBuilder.build());
        return resp.getDynSimpleInfosMap().keySet()
                .stream()
                .collect(Collectors.toMap(Function.identity(),
                        key -> MiscMapper.MAPPER.fromRo(resp.getDynSimpleInfosMap().get(key))));
    }

    /**
     * 获取动态封面信息
     *
     * @param dynamicIds
     * @return
     */
    @SneakyThrows
    public Map<Long, BiliDynamicWithPicBo> getDynamicWithPic(List<Long> dynamicIds) {
        if (CollectionUtils.isEmpty(dynamicIds)) {
            return Collections.emptyMap();
        }

        final DynamicReqBo dynamicReqBo = new DynamicReqBo();
        dynamicReqBo.setDyn_ids(dynamicIds);
        Call call = okHttpClient.newCall(new Request.Builder()
                .url(dynamicProperties.getManagerHost() + dynamicProperties.getDynContentsUrl())
                .post(RequestBody.create(HttpConstants.JSON, objectMapper.writeValueAsBytes(dynamicReqBo)))
                .build());
        try {
            Response resp = call.execute();
            List<BiliDynamicWithPicBo> biliDynamicWithPicBos = extractFromResponseWithPic(resp);
            return biliDynamicWithPicBos.stream().collect(Collectors.toMap(BiliDynamicWithPicBo::getDyn_id, Function.identity()));
        } catch (Exception e) {
            log.error("getDynamicWithPic error, dynamicIds = {}", dynamicIds, e);
            throw e;
        }
    }

    @SneakyThrows
    public List<DynamicResponseBo> getDynamicInfoByAvIds(Collection<Long> avIds) {
        if (CollectionUtils.isEmpty(avIds)) return Collections.emptyList();

        List<DynamicReqBo.RevBo> revBos = avIds.stream()
                .map(x -> {
                    DynamicReqBo.RevBo bo = new DynamicReqBo.RevBo();
                    bo.setRid(x);
                    bo.setType(DYN_TYPE_ARCHIVE);
                    return bo;
                }).collect(Collectors.toList());
        DynamicReqBo dynamicReqBo = new DynamicReqBo();
        dynamicReqBo.setRevs(revBos);
        Call call = okHttpClient.newCall(new Request.Builder()
                .url(dynamicProperties.getManagerHost() + dynamicProperties.getFetchByIdUrl())
                .post(RequestBody.create(HttpConstants.JSON, objectMapper.writeValueAsBytes(dynamicReqBo)))
                .build());
        try {
            Response resp = call.execute();
            return extractFromResponse(resp);
        } catch (Exception e) {
            log.error("getDynamicInfoByAvIds error, avIds = {}", avIds, e);
            throw e;
        }
    }

    @SneakyThrows
    public List<DynamicResponseBo> getDynamicInfoByIds(List<Long> dynamicIds) {
        String url = dynamicProperties.getManagerHost() + dynamicProperties.getFetchByIdUrl();
        DynamicReqBo reqBo = DynamicReqBo.builder()
                .dyn_ids(dynamicIds)
                .build();
        RequestBody body = RequestBody.create(HttpConstants.JSON, objectMapper.writeValueAsString(reqBo));
        Request req = new Request.Builder()
                .url(url)
                .post(body)
                .build();
        Response response = okHttpClient.newCall(req).execute();
        return extractFromResponse(response);
    }

    @SneakyThrows
    public List<DynamicResponseBo> getDynamicInfo(DynamicReqBo reqBo) {

        if (CollectionUtils.isEmpty(reqBo.getDyn_ids())) {
            return Collections.emptyList();
        }

        String url = dynamicProperties.getManagerHost() + dynamicProperties.getFetchByIdUrl();
        RequestBody body = RequestBody.create(HttpConstants.JSON, objectMapper.writeValueAsString(reqBo));
        Request req = new Request.Builder()
                .url(url)
                .post(body)
                .build();
        Response response = okHttpClient.newCall(req).execute();
        return extractFromResponse(response);
    }

    public Map<Long, DynamicResponseBo> getDynamicInfoMap(DynamicReqBo reqBo) {
        List<DynamicResponseBo> dynamicInfo = this.getDynamicInfo(reqBo);
        return dynamicInfo.stream().collect(Collectors.toMap(t -> t.getDyn_id(), t -> t));
    }

    public Map<Long, OpusCardBo> fetchOpusCardMap(Collection<Long> dynamicIds) {
        final Map<Long, OpusCardBo> map = new HashMap<>();
        for (List<Long> subDynamicIds : Lists.partition(new ArrayList<>(dynamicIds), 20)) {
            OpusCardRsp resp = feedBlockingStub.opusCard(OpusCardReq.newBuilder()
                    .addAllOpusIds(subDynamicIds)
                    .setOption(OpusCardReqOption.newBuilder()
                            .setSummary(true)
                            .build())
                    .build());
            for (OpusCard opsCard : resp.getCardsMap().values()) {
                OpusCardBo bo = new OpusCardBo();
                bo.setDynamicId(opsCard.getOpusId());
                bo.setMusicId(opsCard.getSummary().getMusicId());
                map.put(bo.getDynamicId(), bo);
            }
        }
        return map;
    }

    @SneakyThrows
    private List<DynamicResponseBo> extractFromResponse(Response resp) {
        if (Objects.isNull(resp.body())) {
            throw RiskRemoteAccessErr.noBody();
        }
        final HttpResponse<DynamicResponseListBo> dynamicListBo = objectMapper.readValue(resp.body().bytes(), new TypeReference<HttpResponse<DynamicResponseListBo>>() {
        });
        if (Objects.isNull(dynamicListBo)) {
            return Collections.emptyList();
        }
        if (Objects.isNull(dynamicListBo.getData())) {
            return Collections.emptyList();
        }
        if (CollectionUtils.isEmpty(dynamicListBo.getData().getDyn_infos())) {
            return Collections.emptyList();
        }
        return dynamicListBo.getData().getDyn_infos();
    }


    @SneakyThrows
    private List<BiliDynamicWithPicBo> extractFromResponseWithPic(Response resp) {
        if (Objects.isNull(resp.body())) {
            throw RiskRemoteAccessErr.noBody();
        }
        final HttpResponse<DynamicResponseWithPicListBo> dynamicListBo = objectMapper.readValue(resp.body().bytes(),
                new TypeReference<HttpResponse<DynamicResponseWithPicListBo>>() {
                });
        if (Objects.isNull(dynamicListBo)) {
            return Collections.emptyList();
        }
        if (Objects.isNull(dynamicListBo.getData())) {
            return Collections.emptyList();
        }
        if (CollectionUtils.isEmpty(dynamicListBo.getData().getContents())) {
            return Collections.emptyList();
        }
        return dynamicListBo.getData().getContents().values().stream().collect(Collectors.toList());
    }


    public Map<Long, StatReply> queryMidFans(List<Long> mids) {
        if (CollectionUtils.isEmpty(mids)) {
            return Collections.emptyMap();
        }

        MidsReq midsReq = MidsReq.newBuilder()
                .addAllMids(mids)
                .build();
        try {
            StatsReply reply = relationBlockingStub.withDeadlineAfter(2000, TimeUnit.MILLISECONDS)
                    .withWaitForReady()
                    .stats(midsReq);
            return reply != null && !CollectionUtils.isEmpty(reply.getStatReplyMapMap()) ?
                    reply.getStatReplyMapMap() : Collections.emptyMap();
        } catch (Exception e) {
            log.info("queryMidFans error! mids = {}", mids);
            log.error("queryMidFans error!", e);
            return Collections.emptyMap();
        }
    }
}
