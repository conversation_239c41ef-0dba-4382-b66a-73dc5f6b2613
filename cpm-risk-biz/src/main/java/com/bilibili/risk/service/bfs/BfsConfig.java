package com.bilibili.risk.service.bfs;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

@Configuration
@PropertySource(value = "paladin://bfs.yaml")
public class BfsConfig {
    @Bean
    @ConfigurationProperties(prefix = "bfs")
    public BfsProperties bfsProperties() {
        return new BfsProperties();
    }
}
