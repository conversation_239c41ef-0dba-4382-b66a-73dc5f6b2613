package com.bilibili.risk.service.ratelimit;

import com.bilibili.risk.config.RedisConfig;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * 通用限流服务
 * 基于Redis实现的滑动窗口限流
 */
@Slf4j
@Service
public class RateLimitService {

    private static final String RATE_LIMIT_KEY_PREFIX = "risk:rate_limit:";

    @Resource(name = RedisConfig.ADP_CLUSTER)
    private RedissonClient adpRedissonClient;

    /**
     * 限流结果
     */
    public static class RateLimitResult {
        private final boolean allowed;
        private final long currentCount;
        private final long limit;
        private final long windowSeconds;

        public RateLimitResult(boolean allowed, long currentCount, long limit, long windowSeconds) {
            this.allowed = allowed;
            this.currentCount = currentCount;
            this.limit = limit;
            this.windowSeconds = windowSeconds;
        }

        public boolean isAllowed() {
            return allowed;
        }

        public long getCurrentCount() {
            return currentCount;
        }

        public long getLimit() {
            return limit;
        }

        public long getWindowSeconds() {
            return windowSeconds;
        }

        @Override
        public String toString() {
            return String.format("RateLimitResult{allowed=%s, currentCount=%d, limit=%d, windowSeconds=%d}",
                    allowed, currentCount, limit, windowSeconds);
        }
    }

    /**
     * 检查是否被限流
     *
     * @param businessType  业务类型
     * @param identifier    标识符（如用户ID、IP等）
     * @param limit         限流数量
     * @param windowSeconds 时间窗口（秒）
     * @return 限流结果
     */
    public RateLimitResult checkRateLimit(String businessType, String identifier, long limit, long windowSeconds) {
        String key = buildKey(businessType, identifier, windowSeconds);
        RAtomicLong counter = adpRedissonClient.getAtomicLong(key);

        long currentCount = counter.get();
        boolean allowed = currentCount < limit;

        log.debug("检查限流状态: businessType={}, identifier={}, currentCount={}, limit={}, allowed={}",
                businessType, identifier, currentCount, limit, allowed);

        return new RateLimitResult(allowed, currentCount, limit, windowSeconds);
    }

    /**
     * 增加限流计数
     *
     * @param businessType  业务类型
     * @param identifier    标识符
     * @param windowSeconds 时间窗口（秒）
     * @return 增加后的计数
     */
    public long incrementRateLimit(String businessType, String identifier, long windowSeconds) {
        String key = buildKey(businessType, identifier, windowSeconds);
        RAtomicLong counter = adpRedissonClient.getAtomicLong(key);

        long newCount = counter.incrementAndGet();

        // 设置过期时间，确保计数器会自动清理
        if (newCount == 1) {
            counter.expire(windowSeconds, TimeUnit.SECONDS);
        }

        log.debug("增加限流计数: businessType={}, identifier={}, newCount={}, windowSeconds={}",
                businessType, identifier, newCount, windowSeconds);

        return newCount;
    }

    /**
     * 获取当前限流计数
     *
     * @param businessType  业务类型
     * @param identifier    标识符
     * @param windowSeconds 时间窗口（秒）
     * @return 当前计数
     */
    public long getCurrentCount(String businessType, String identifier, long windowSeconds) {
        String key = buildKey(businessType, identifier, windowSeconds);
        RAtomicLong counter = adpRedissonClient.getAtomicLong(key);
        long currentCount = counter.get();

        log.debug("获取当前限流计数: businessType={}, identifier={}, currentCount={}",
                businessType, identifier, currentCount);

        return currentCount;
    }

    /**
     * 清除限流计数
     *
     * @param businessType  业务类型
     * @param identifier    标识符
     * @param windowSeconds 时间窗口（秒）
     */
    public void clearRateLimit(String businessType, String identifier, long windowSeconds) {
        String key = buildKey(businessType, identifier, windowSeconds);
        RAtomicLong counter = adpRedissonClient.getAtomicLong(key);
        counter.delete();

        log.info("清除限流计数: businessType={}, identifier={}", businessType, identifier);
    }

    /**
     * 尝试执行（检查限流 + 增加计数）
     *
     * @param businessType  业务类型
     * @param identifier    标识符
     * @param limit         限流数量
     * @param windowSeconds 时间窗口（秒）
     * @return 限流结果（包含执行后的计数）
     */
    public RateLimitResult tryExecute(String businessType, String identifier, long limit, long windowSeconds) {
        String key = buildKey(businessType, identifier, windowSeconds);
        RAtomicLong counter = adpRedissonClient.getAtomicLong(key);

        long currentCount = counter.get();

        if (currentCount >= limit) {
            // 已达到限流上限
            log.warn("限流拦截: businessType={}, identifier={}, currentCount={}, limit={}",
                    businessType, identifier, currentCount, limit);
            return new RateLimitResult(false, currentCount, limit, windowSeconds);
        }

        // 增加计数
        long newCount = counter.incrementAndGet();

        // 设置过期时间
        if (newCount == 1) {
            counter.expire(windowSeconds, TimeUnit.SECONDS);
        }

        log.info("限流通过: businessType={}, identifier={}, newCount={}, limit={}",
                businessType, identifier, newCount, limit);

        return new RateLimitResult(true, newCount, limit, windowSeconds);
    }

    /**
     * 构建Redis键
     */
    private String buildKey(String businessType, String identifier, long windowSeconds) {

        if (windowSeconds <= 0L) {
            return RATE_LIMIT_KEY_PREFIX + businessType + ":" + identifier;
        } else {
            long currentWindow = System.currentTimeMillis() / (windowSeconds * 1000);
            return RATE_LIMIT_KEY_PREFIX + businessType + ":" + identifier + ":" + currentWindow;
        }
    }
}
