package com.bilibili.risk.service.task;

import com.bilibili.risk.bo.LauMaterialAuditQueueBo;
import com.bilibili.risk.config.BizConfig;
import com.bilibili.risk.po.risk.LauMaterialAuditTaskPo;
import com.bilibili.risk.service.wechat.WechatService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 已经废弃，该流程已经被改造；改成了：在推审的时候，会根据情况决定是否进入等待队列
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class TaskMatchQueueService {

    private final BizConfig bizConfig;
    private final WechatService wechatService;


    public Long matchQueueId(String taskId, List<LauMaterialAuditQueueBo> auditQueueBos, LauMaterialAuditTaskPo auditTaskPo) {
        Long machQueueId = 0L;
        List<LauMaterialAuditQueueBo> matchQueues = auditQueueBos.stream()
                // 业务类型，素材类型匹配
                .filter(q -> q.getBizType().equals(auditTaskPo.getBizType()) && q.getMaterialType().equals(auditTaskPo.getMaterialType()))
                // 淘汰【标签队列】,线上补丁 for acms
                .filter(q -> !q.getQueueType().equals(1))
                .collect(Collectors.toList());
        // 没有匹配到进入兜底队列
        if (CollectionUtils.isEmpty(matchQueues)) {
            machQueueId = bizConfig.getFallbackQueueId();
            log.error("match queue, 匹配不到队列，进入兜底, taskId={}, queueId={}", taskId, auditTaskPo.getQueueId());

            // 异步机器人告警
            wechatService.asyncSendMsg("素材审核任务匹配队列失败，进入兜底队列, taskId=" + taskId + ", queueId=" + auditTaskPo.getQueueId(), bizConfig.getWechatRobotEnterFallbackQueue());
        } else {
            machQueueId = matchQueues.get(0).getId();
            log.info("match queue, 匹配到队列, taskId={}, preQueueId={},machQueueId={}", taskId, auditTaskPo.getQueueId(), machQueueId);
        }
        log.info("match queue, 最终queueId, taskId={}, queueId={}", taskId, machQueueId);
        return machQueueId;
    }
}
