package com.bilibili.risk.service.refresh;

import com.bilibili.risk.constant.DatabaseConstant;
import com.bilibili.risk.po.risk.LauMaterialAuditTaskPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.api.hint.HintManager;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Service;

import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import javax.sql.DataSource;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
@Service
public class RefreshShardingDataService {

    @Resource(name = DatabaseConstant.RISK_JDBC_TEMPLATE)
    private JdbcTemplate jdbcTemplate;

    private final ExecutorService refreshShardingDbExecutor;

    private static final int TOTAL_SHARDS = 128; // 总分表数

    public RefreshShardingDataService(@Qualifier(value = DatabaseConstant.RISK_DATASOURCE_SHARDING) DataSource dataSource) {
        this.jdbcTemplate = new JdbcTemplate(dataSource);
        // 根据服务器核心数设置线程池大小
        this.refreshShardingDbExecutor = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors() * 2);
    }

    /**
     * 刷新所有分表的mtime为当前时间（基于task_id分片键）
     */
    public void refreshAllShardsMtime() {
        Timestamp currentTime = Timestamp.valueOf(LocalDateTime.now());
        CompletionService<Void> completionService = new ExecutorCompletionService<>(refreshShardingDbExecutor);

        // 提交所有分片任务
        for (int shard = 0; shard < TOTAL_SHARDS; shard++) {
            final int shardValue = shard;
            completionService.submit(() -> {
//                updateSingleShard(shardValue, currentTime);
                queryAllShardsSample(shardValue);
                return null;
            });
        }

        // 等待所有任务完成
        for (int i = 0; i < TOTAL_SHARDS; i++) {
            try {
                completionService.take().get();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("刷新任务被中断", e);
            } catch (ExecutionException e) {
                throw new RuntimeException("刷新任务执行失败", e.getCause());
            }
        }
    }

    /**
     * 更新单个分片的数据
     */
    private void updateSingleShard(int shardValue, Timestamp currentTime) {
        try (HintManager hintManager = HintManager.getInstance()) {
            // 设置分片值，这里使用task_id的分片值
            hintManager.addTableShardingValue("lau_material_audit_task", shardValue);

            // 使用更高效的更新方式
            String updateSql = "UPDATE lau_material_audit_task SET mtime = ?";
            int updatedRows = jdbcTemplate.update(updateSql, currentTime);

            System.out.printf("分片 %d 更新完成，影响行数: %d%n", shardValue, updatedRows);
        } catch (Exception e) {
            System.err.printf("分片 %d 更新失败: %s%n", shardValue, e.getMessage());
            // 这里可以添加重试逻辑或记录失败分片
        }
    }

    /**
     * 关闭资源
     */
    @PreDestroy
    public void destroy() {
        refreshShardingDbExecutor.shutdown();
        try {
            if (!refreshShardingDbExecutor.awaitTermination(60, TimeUnit.SECONDS)) {
                refreshShardingDbExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            refreshShardingDbExecutor.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }


    // 行映射器，用于将结果集转换为DTO对象
    private static final RowMapper<LauMaterialAuditTaskPo> TASK_ROW_MAPPER = (rs, rowNum) -> {
        LauMaterialAuditTaskPo task = new LauMaterialAuditTaskPo();
        task.setId(rs.getLong("id"));
        task.setTaskId(rs.getString("task_id"));
        task.setMaterialId(rs.getString("material_id"));
        task.setStatus(rs.getInt("status"));
        task.setMtime(rs.getTimestamp("mtime"));
        task.setCtime(rs.getTimestamp("ctime"));
        return task;
    };

    /**
     * 查询单个分表的数据（用于测试验证）
     *
     * @param shardValue 分片值 (0-127)
     * @param limit      查询条数限制
     * @return 任务信息列表
     */
    public List<LauMaterialAuditTaskPo> queryShardData(int shardValue, int limit) {
        log.info("查询分片 {} 的数据，限制条数: {}", shardValue, limit);
        try (HintManager hintManager = HintManager.getInstance()) {
            // 明确指定分片值类型（重要！）
            hintManager.addTableShardingValue("lau_material_audit_task", shardValue);

            String sql = "SELECT id, task_id, material_id, status, mtime, ctime " +
                    "FROM lau_material_audit_task " +
                    "ORDER BY id DESC LIMIT ?";

            log.info("执行查询: {}", sql);
            List<LauMaterialAuditTaskPo> taskPos = jdbcTemplate.query(sql, TASK_ROW_MAPPER, limit);
            log.info("查询结果: {}", taskPos);
            return taskPos;
        }
    }

    /**
     * 查询所有分表的数据样本（用于测试）
     *
     * @param perShardLimit 每个分表查询的条数
     * @return 所有分表的数据样本
     */
    public List<LauMaterialAuditTaskPo> queryAllShardsSample(int perShardLimit) {
        CompletionService<List<LauMaterialAuditTaskPo>> completionService = new ExecutorCompletionService<>(refreshShardingDbExecutor);
        final AtomicInteger completedShards = new AtomicInteger(0);

        // 提交所有分片查询任务
        for (int shard = 0; shard < TOTAL_SHARDS; shard++) {
            final int shardValue = shard;
            completionService.submit(() -> {
                List<LauMaterialAuditTaskPo> result = queryShardData(shardValue, perShardLimit);
                System.out.printf("分片 %d 查询完成，获取 %d 条数据%n",
                        shardValue, result.size());
                completedShards.incrementAndGet();
                return result;
            });
        }

        // 合并所有结果
        List<LauMaterialAuditTaskPo> allResults = new CopyOnWriteArrayList<>();
        for (int i = 0; i < TOTAL_SHARDS; i++) {
            try {
                List<LauMaterialAuditTaskPo> shardResults = completionService.take().get();
                allResults.addAll(shardResults);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                System.err.println("查询被中断");
                break;
            } catch (ExecutionException e) {
                System.err.println("查询失败: " + e.getCause().getMessage());
            }
        }

        System.out.printf("查询完成，共从 %d/%d 个分片获取 %d 条数据%n",
                completedShards.get(), TOTAL_SHARDS, allResults.size());

        return allResults;
    }

    /**
     * 打印分表数据（测试用）
     *
     * @param shardValue 分片值
     * @param limit      显示条数
     */
    public void printShardData(int shardValue, int limit) {
        List<LauMaterialAuditTaskPo> tasks = queryShardData(shardValue, limit);
        System.out.printf("\n=== 分片 %d 的数据样本（显示 %d/%d 条） ===\n",
                shardValue, tasks.size(), limit);

        tasks.forEach(task -> System.out.printf(
                "ID: %d | 任务ID: %s | 状态: %d | 素材ID: %s | 修改时间: %s | 创建时间: %s\n",
                task.getId(), task.getTaskId(), task.getStatus(),
                task.getMaterialId(), task.getMtime(), task.getCtime()));
    }
}
