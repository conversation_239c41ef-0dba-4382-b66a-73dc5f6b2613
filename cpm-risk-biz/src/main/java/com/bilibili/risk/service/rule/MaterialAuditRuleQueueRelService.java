package com.bilibili.risk.service.rule;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.risk.dao.risk.LauMaterialAuditRuleQueueRelDao;
import com.bilibili.risk.po.risk.LauMaterialAuditRuleQueueRelPo;
import com.bilibili.risk.po.risk.LauMaterialAuditRuleQueueRelPoExample;
import edu.emory.mathcs.backport.java.util.Collections;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class MaterialAuditRuleQueueRelService {

    private final LauMaterialAuditRuleQueueRelDao lauMaterialAuditRuleQueueRelDao;

    public List<LauMaterialAuditRuleQueueRelPo> queryAuditRuleQueueRelListByRuleId(Long ruleId) {

        if (!Utils.isPositive(ruleId)) {
            return Collections.emptyList();
        }

        LauMaterialAuditRuleQueueRelPoExample queueRelPoExample = new LauMaterialAuditRuleQueueRelPoExample();
        queueRelPoExample.createCriteria().andRuleIdEqualTo(ruleId);
        List<LauMaterialAuditRuleQueueRelPo> relPos = lauMaterialAuditRuleQueueRelDao.selectByExample(queueRelPoExample);
        return relPos;
    }

    public List<LauMaterialAuditRuleQueueRelPo> queryAuditRuleQueueRelListByQueueId(Long queueId) {

        if (!Utils.isPositive(queueId)) {
            return Collections.emptyList();
        }

        LauMaterialAuditRuleQueueRelPoExample queueRelPoExample = new LauMaterialAuditRuleQueueRelPoExample();
        queueRelPoExample.createCriteria().andQueueIdEqualTo(queueId);
        List<LauMaterialAuditRuleQueueRelPo> relPos = lauMaterialAuditRuleQueueRelDao.selectByExample(queueRelPoExample);
        return relPos;
    }

    public List<LauMaterialAuditRuleQueueRelPo> queryAuditRuleQueueRelListByRuleIds(List<Long> ruleIds) {

        if (CollectionUtils.isEmpty(ruleIds)) {
            return Collections.emptyList();
        }

        LauMaterialAuditRuleQueueRelPoExample queueRelPoExample = new LauMaterialAuditRuleQueueRelPoExample();
        queueRelPoExample.createCriteria().andRuleIdIn(ruleIds);
        List<LauMaterialAuditRuleQueueRelPo> relPos = lauMaterialAuditRuleQueueRelDao.selectByExample(queueRelPoExample);
        return relPos;
    }


    public void deleteRuleAndQueueRelsByRuleId(Long ruleId) {
        LauMaterialAuditRuleQueueRelPoExample deleteExample = new LauMaterialAuditRuleQueueRelPoExample();
        deleteExample.createCriteria().andRuleIdEqualTo(ruleId);
        int deleteCount = lauMaterialAuditRuleQueueRelDao.deleteByExample(deleteExample);
    }
}
