package com.bilibili.risk.service.wechat;

import com.bilibili.risk.config.BizConfig;
import com.bilibili.risk.constant.HttpConstants;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.IOException;

@Slf4j
@Service
@RequiredArgsConstructor
public class WechatService {

    private final OkHttpClient okHttpClient;
    private final ObjectMapper objectMapper;
    private final BizConfig bizConfig;

    @Async("wechatSendExecutor")
    public void asyncSendMsg(String msg, String url) {

        WeixinMsg weixinMsg = new WeixinMsg(msg);
        try {
            doSendMsg(weixinMsg, url);
        } catch (Exception e) {
            log.error("send wechat msg error, msg={}, url={}", msg, url, e);
        }
    }

    @Async("wechatSendExecutor")
    public void asyncSendMsg(String msg) {

        WeixinMsg weixinMsg = new WeixinMsg(msg);
        try {
            doSendMsg(weixinMsg, bizConfig.getRobotCommon());
        } catch (Exception e) {
            log.error("send wechat msg error, msg={}, url={}", msg, bizConfig.getRobotCommon(), e);
        }
    }

    public void doSendMsg(WeixinMsg msg, String url) throws IOException {
        log.info("send wechat msg, msg={}, url={}", msg, url);
        Call call = okHttpClient.newCall(new Request.Builder()
                .url(url)
                .post(RequestBody.create(HttpConstants.JSON, objectMapper.writeValueAsBytes(msg)))
                .build());
        try {
            // 可能会超时，
            Response resp = call.execute();
        } catch (Exception e) {
            log.error("send wechat msg error, msg={}, url={}", msg, url, e);
        }
    }

}
