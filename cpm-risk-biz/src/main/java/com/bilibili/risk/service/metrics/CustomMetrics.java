package com.bilibili.risk.service.metrics;

import io.opentelemetry.api.common.Attributes;

public interface CustomMetrics {
    // 计数器：只能增加，不会减少。使用场景eg: 请求数。
    default void count(String key, Attributes attributes) {}
    default void count(String key, long value, Attributes attributes){}
    // 增减计数器：可以增加或减少。 使用场景eg: 活跃连接数：记录当前活跃的客户端连接数；队列长度：统计任务队列中的当前任务数。
    default void upDownCounter(String key, long value, Attributes attributes){}
    // 直方图：记录观测值的分布直方图。使用场景eg: 请求耗时。
    default void histogram(String key, long value, Attributes attributes){}
}
