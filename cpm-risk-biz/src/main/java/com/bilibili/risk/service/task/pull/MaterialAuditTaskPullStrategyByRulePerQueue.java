package com.bilibili.risk.service.task.pull;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.rbac.api.dto.UserDto;
import com.bilibili.risk.bo.*;
import com.bilibili.risk.constant.DatabaseConstant;
import com.bilibili.risk.convertor.IMaterialAuditTaskConvertor;
import com.bilibili.risk.enums.MaterialTaskPullStrategyEnum;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Message;
import edu.emory.mathcs.backport.java.util.Collections;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.internal.util.Assert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
public class MaterialAuditTaskPullStrategyByRulePerQueue extends AbstractMaterialAuditTaskPullStrategy {

    @Autowired
    private MaterialAuditTaskPullStrategyBySingleQueue materialAuditTaskPullStrategyBySingleQueue;

    /**
     * 根据规则[by queue]拉取一批任务
     *
     * @param pullQueryBo
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class, transactionManager = DatabaseConstant.RISK_TRANSACTION_MANAGER)
    public List<MaterialAuditTaskEsBo> pullOneBatchMaterialAuditTasks(MaterialAuditTaskPullQueryBo pullQueryBo) {
        log.info("pullOneBatchMaterialAuditTasks[根据规则-按队列], pullQueryBo:{}", pullQueryBo);
        Assert.notNull(pullQueryBo, "pullQueryBo is null");
        Assert.notNull(pullQueryBo.getPullStrategy(), "pullStrategy is null");

        Assert.isTrue(Objects.equals(pullQueryBo.getPullStrategy(), MaterialTaskPullStrategyEnum.BY_RULE_PER_QUEUE.getKey()), "pullStrategy is not BY_RULE_PER_QUEUE");
        Assert.isTrue(Utils.isPositive(pullQueryBo.getRuleId()), "ruleId 未传");
        Assert.isTrue(!StringUtils.isEmpty(pullQueryBo.getUsername()), "username 未传");

        MaterialAuditRuleInfoBo ruleInfoBo = materialAuditRuleService.fetchMaterialAuditRule(pullQueryBo.getRuleId());
        Assert.notNull(ruleInfoBo, "规则不存在！");

        List<LauMaterialAuditQueueBo> bindQueues = ruleInfoBo.getBindQueues();
        Assert.isTrue(!CollectionUtils.isEmpty(bindQueues), "规则未绑定队列！");
        Assert.isTrue(!CollectionUtils.isEmpty(ruleInfoBo.getRolesIds()), "规则未绑定角色！");
        for (LauMaterialAuditQueueBo bindQueue : bindQueues) {
            Assert.isTrue(!bizConfig.getSpecialQueueIds().contains(bindQueue.getId()), "等待队列和兜底队列的任务不允许领取！");
        }

        // 规则角色权限
        checkRole(pullQueryBo, ruleInfoBo);
        Cat.logEvent("pullOneBatchMaterialAuditTasks", "checkRole end");

        // 每个队列逐个拉取
        for (LauMaterialAuditQueueBo bindQueue : bindQueues) {
            List<MaterialAuditTaskEsBo> auditTaskEsBos = materialAuditTaskPullStrategyBySingleQueue.pullOneBatchMaterialAuditTasks(MaterialAuditTaskPullQueryBo.builder()
                    .queueId(bindQueue.getId())
                    .username(pullQueryBo.getUsername())
                    .pullStrategy(MaterialTaskPullStrategyEnum.BY_SINGLE_QUEUE_ID.getKey())
                    .skipRoleCheck(pullQueryBo.isSkipRoleCheck())
                    .isPullByRule(true)
                    .build());
            log.info("pullOneBatchMaterialAuditTasks[根据规则-按队列], queueId:{}, ruleId:{}", bindQueue.getId(), ruleInfoBo.getId());
            Cat.logEvent("pullOneBatchMaterialAuditTasks", "byQueue,拉取完一个队列", Message.SUCCESS, "queueId:"+ bindQueue.getId() + ",size:" + auditTaskEsBos.size());

            // 拉取到一批就返回
            if (!CollectionUtils.isEmpty(auditTaskEsBos)) {
                if (!pullQueryBo.isPrePull()) {
                    MaterialAuditTaskPullQueryBo prePullQueryBo = IMaterialAuditTaskConvertor.INSTANCE.copy(pullQueryBo);
                    prePullQueryBo.setPrePull(true);
                    prePullQueryBo.setTaskIds(auditTaskEsBos.stream().map(t -> t.getId()).collect(Collectors.toList()));
                    materialTaskPrePullMsgPub.pub(prePullQueryBo);
                }
                return auditTaskEsBos;
            }
        }

        // 防止死循环，预拉取发起的，不需要继续预拉取
        if (!pullQueryBo.isPrePull()) {
            MaterialAuditTaskPullQueryBo prePullQueryBo = IMaterialAuditTaskConvertor.INSTANCE.copy(pullQueryBo);
            prePullQueryBo.setPrePull(true);
            materialTaskPrePullMsgPub.pub(prePullQueryBo);
        } else {
            log.info("pullOneBatchMaterialAuditTasks[根据规则-按队列], 预拉取消触发的，不需要继续预拉取，pullQueryBo:{}", pullQueryBo);
        }
        return Collections.emptyList();
    }

    private void checkRole(MaterialAuditTaskPullQueryBo pullQueryBo, MaterialAuditRuleInfoBo ruleInfoBo) {
        if (Objects.equals(pullQueryBo.getPullStrategy(), MaterialTaskPullStrategyEnum.BY_RULE_PER_QUEUE.getKey()) && !pullQueryBo.isSkipRoleCheck()) {
            UserDto user = userService.getUserByUserName(bizConfig.getTenantId(), pullQueryBo.getUsername());
            Assert.notNull(user, "用户不存在");

            List<Integer> roleIdsOfUser = user.getRoles().stream().map(r -> r.getId()).distinct().collect(Collectors.toList());

            // 业务管理员直接可以操作
            if (roleIdsOfUser.contains(bizConfig.getBusinessAdminRoleId())) {
                return;
            }

            // 非业务管理员，要求用户roleIds与队列roleIds存在交集
            List<Integer> allRoleIds = Stream.concat(ruleInfoBo.getRolesIds().stream(), roleIdsOfUser.stream()).collect(Collectors.toList());
            List<Integer> intersectionRoleIds = ruleInfoBo.getRolesIds().stream()
                    .filter(roleIdsOfUser::contains)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(intersectionRoleIds)) {
                Map<Integer, RoleBo> roleBoMap = riskRoleService.queryRoleBoMapByIds(allRoleIds);
                String roleNameOfUser = roleIdsOfUser.stream().map(r -> roleBoMap.get(r)).filter(Objects::nonNull).map(t -> t.getName()).collect(Collectors.joining(","));
                String roleNameOfRule = ruleInfoBo.getRolesIds().stream().map(r -> roleBoMap.get(r)).filter(Objects::nonNull).map(t -> t.getName()).collect(Collectors.joining(","));
                log.warn("用户没有队列权限，queueId:{}, username:{},用户权限={},规则权限={}", pullQueryBo.getQueueId(), pullQueryBo.getUsername(), roleNameOfUser, roleNameOfRule);
                throw new RuntimeException("用户没有规则权限");
            }
        }
    }
}
