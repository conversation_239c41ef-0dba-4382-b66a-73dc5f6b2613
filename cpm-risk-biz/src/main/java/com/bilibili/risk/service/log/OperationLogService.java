package com.bilibili.risk.service.log;

import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.risk.bo.MaterialAuditTaskOperationLogBo;
import com.bilibili.risk.config.BizConfig;
import com.bilibili.risk.constant.RiskConstants;
import com.bilibili.risk.databus.MaterialAuditTaskOperationLogEsPub;
import com.bilibili.risk.enums.OperationTypeEnum;
import com.bilibili.risk.es.ElasticsearchCloudConfig;
import com.bilibili.risk.es.ElasticsearchQueryUtil;
import com.bilibili.risk.service.task.MaterialAuditTaskEsService;
import com.dianping.cat.Cat;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * es 操作日志
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OperationLogService {
    private final BizConfig bizConfig;
    private final MaterialAuditTaskOperationLogEsPub materialAuditTaskOperationLogEsPub;

    private static ElasticsearchCloudConfig config;

    @PostConstruct
    public void init() {
        if (config == null) {
            synchronized (MaterialAuditTaskEsService.class) {
                if (config == null) {
                    config = ElasticsearchCloudConfig.builder()
                            .token(bizConfig.getTaskLogEsToken())
                            .index(bizConfig.getTaskLogEsIndex())
                            .domain(bizConfig.getEsDomain())
                            .indexShardingType(bizConfig.getTaskLogEsIndexShardingType())
                            .build();
                    log.info("log ElasticsearchCloudConfig init, config:{}", config);
                }
            }
        }
    }

    private ElasticsearchCloudConfig getESConfig() {
        return config;
    }

    // 添加日志
    public void saveLog(MaterialAuditTaskOperationLogBo logBo) {
        try {
            materialAuditTaskOperationLogEsPub.pub(logBo);
        } catch (Exception e) {
            log.error("saveLogs error, logBo={}", logBo, e);
        }
    }

    public void saveLogs(List<MaterialAuditTaskOperationLogBo> logBos) {

        for (MaterialAuditTaskOperationLogBo logBo : logBos) {
            try {
                materialAuditTaskOperationLogEsPub.pub(logBo);
            } catch (Exception e) {
                log.error("saveLogs error, logBo={}", logBo, e);
            }
        }
        Cat.logEvent("batchAuditTask", "saveLogs end");

    }

    // 查询任务的日志列表，分页
    public PageResult<MaterialAuditTaskOperationLogBo> queryList(Integer page, Integer pageSize, String order, String sortField, String taskId, Integer logType) {
        if (!StringUtils.isNotEmpty(taskId)) {
            return PageResult.emptyPageResult();
        }

        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();

        // 单个精确查询
        ElasticsearchQueryUtil.addTermsQueryIfNotEmpty(boolQuery, "objId", Arrays.asList(taskId));
        ElasticsearchQueryUtil.addMatchQueryIfNotEmpty(boolQuery, "systemType", RiskConstants.SYSTEM_TYPE_RISK + "");
        ElasticsearchQueryUtil.addMatchQueryIfNotEmpty(boolQuery, "type", logType + "");

        SearchSourceBuilder searchSourceBuilder = ElasticsearchQueryUtil.buildPaginationAndSort(boolQuery, page, pageSize, sortField
                , SortOrder.DESC.toString().equals(order) ? SortOrder.DESC : SortOrder.ASC, "id");
        PageResult<MaterialAuditTaskOperationLogBo> taskOperationLogBoPageResult = ElasticsearchQueryUtil.getPageResultFromEsCloud(searchSourceBuilder, getESConfig(), null, MaterialAuditTaskOperationLogBo.class);

        for (MaterialAuditTaskOperationLogBo operationLogBo : taskOperationLogBoPageResult.getRecords()) {
            Optional<OperationTypeEnum> optionalOperationTypeEnum = OperationTypeEnum.getByCode(operationLogBo.getOperationType());
            if (optionalOperationTypeEnum.isPresent()) {
                OperationTypeEnum operationTypeEnum = optionalOperationTypeEnum.get();
//                // 未填写操作内容，则用操作类型填充
//                if (StringUtils.isEmpty(operationLogBo.getValue())) {
//                    operationLogBo.setValue(operationTypeEnum.getName());
//                }

                // 有value, 用类型: value; 没有value, 只显示类型
                if (StringUtils.isNotEmpty(operationLogBo.getValue())) {
                    operationLogBo.setValue(operationTypeEnum.getName() + ":" + operationLogBo.getValue());
                } else {
                    operationLogBo.setValue(operationTypeEnum.getName());
                }
            } else {
                operationLogBo.setValue("未知操作类型");
            }
        }

        return taskOperationLogBoPageResult;
    }
}
