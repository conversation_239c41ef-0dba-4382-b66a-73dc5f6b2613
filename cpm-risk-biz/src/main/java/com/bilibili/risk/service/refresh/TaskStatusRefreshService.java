package com.bilibili.risk.service.refresh;

import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.risk.bo.LauMaterialAuditQueueBo;
import com.bilibili.risk.bo.MaterialAuditTaskCommonQueryBo;
import com.bilibili.risk.constant.RiskConstants;
import com.bilibili.risk.enums.MaterialTaskStatusEnum;
import com.bilibili.risk.po.es.LauMaterialAuditTaskEsPo;
import com.bilibili.risk.po.risk.LauMaterialAuditTaskPo;
import com.bilibili.risk.service.material.LauMaterialAuditService;
import com.bilibili.risk.service.queue.MaterialAuditQueueService;
import com.bilibili.risk.service.task.MaterialAuditTaskDbService;
import com.bilibili.risk.service.task.MaterialAuditTaskEsService;
import com.bilibili.risk.service.task.MaterialAuditTaskTimeoutService;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.persistence.criteria.CriteriaBuilder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 超时任务释放兜底，目前这个任务是关闭的
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TaskStatusRefreshService {

    public static final int PAGE_SIZE = 200;
    private final MaterialAuditTaskEsService materialAuditTaskEsService;
    private final MaterialAuditTaskDbService materialAuditTaskDbService;
    private final LauMaterialAuditService lauMaterialAuditService;
    private final MaterialAuditQueueService materialAuditQueueService;

    public void asyncRefreshTaskTimeoutData(List<String> taskIds, Integer isUpdate) {
        new Thread(() -> {
            try {
                refreshTaskTimeoutData(taskIds, isUpdate);
            } catch (Exception e) {
                log.error("asyncrefreshTaskTimeoutData[异常]，taskIds.size:{}, error:{}", taskIds.size(), e);
            }
        }).start();
    }

    @SneakyThrows
    public Integer refreshTaskTimeoutData(List<String> taskIds, Integer isUpdate) {
        log.info("refreshTaskTimeoutData[任务超时补偿]start，taskIds.size:{}", taskIds.size());

        // 分批查询 es 数据
        PageResult<LauMaterialAuditTaskEsPo> pageResult = null;
        int page = 1;
        int totalUpdateCount = 0;
        do {
            List<String> taskIdsOneBatch = new ArrayList<>();
            if (CollectionUtils.isEmpty(taskIds)) {
                MaterialAuditTaskCommonQueryBo queryBo = MaterialAuditTaskCommonQueryBo.builder()
                        .page(page)
                        .pageSize(PAGE_SIZE)
                        .statusList(Arrays.asList(MaterialTaskStatusEnum.FREE.getCode(), MaterialTaskStatusEnum.DOING.getCode()))
                        .mtimeStart(1749625200000L)
                        .mtimeEnd(1749628200000L)
                        .excludeQueueIds(Arrays.asList(19L))
                        .ids(taskIds)
                        .build();
                pageResult = materialAuditTaskEsService.queryMaterialAuditTaskEsPosCommon(queryBo);
                List<LauMaterialAuditTaskEsPo> records = pageResult.getRecords();
                if (CollectionUtils.isEmpty(records)) {
                    log.info("refreshTaskTimeoutData[没有查询到doing数据]，taskIds.size:{}, page:{}, pageSize:{}", taskIds.size(), page, PAGE_SIZE);
                    break;
                }
                log.info("refreshTaskTimeoutData[查询到doing数据]，taskIds.size:{}, page:{}, pageSize:{}, records.size:{}", taskIds.size(), page, PAGE_SIZE, records.size());
                taskIdsOneBatch = records.stream().map(t -> t.getId()).collect(Collectors.toList());
            }


            // 获取这批任务的db数据
            List<LauMaterialAuditTaskPo> auditTaskPosOneBatch = materialAuditTaskDbService.queryListByTaskIds(taskIdsOneBatch);

            if (Utils.isPositive(isUpdate)) {
                for (LauMaterialAuditTaskPo auditTaskPo : auditTaskPosOneBatch) {

                    LauMaterialAuditTaskPo taskPo = LauMaterialAuditTaskPo.builder()
                            .taskId(auditTaskPo.getTaskId())
                            .mtime(Utils.getNow())
                            .build();
                    int count = materialAuditTaskDbService.updateTask(taskPo);
                    totalUpdateCount++;
                    log.info("refreshTaskTimeoutData[任务超时,job释放]，taskId:{}, queueId:{}, taskStatus:{},count={}", auditTaskPo.getTaskId(), auditTaskPo.getQueueId(), auditTaskPo.getStatus(), count);
                }
            }
            page++;
            Thread.sleep(100);
        } while (!CollectionUtils.isEmpty(pageResult.getRecords()) && pageResult.getRecords().size() == PAGE_SIZE);
        log.info("refreshTaskTimeoutData[完成]，page:{}, totalTaskUpdateCount[任务]:{}", page, totalUpdateCount);
        return totalUpdateCount;
    }
}
