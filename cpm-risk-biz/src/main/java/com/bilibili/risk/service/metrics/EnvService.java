package com.bilibili.risk.service.metrics;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class EnvService {

    private static final String METRIC_DATA_UNKNOWN = "UNKNOWN";

    /**
     * 获取当前部署环境标识 (从环境变量 DEPLOY_ENV 读取)
     *
     * @return 环境标识，未设置时返回 "UNKNOWN"
     */
    public String getEnv() {
        String env = System.getenv("DEPLOY_ENV");
        return env == null ? METRIC_DATA_UNKNOWN : env;
    }
}
