package com.bilibili.risk.service.metrics;

import com.bilibili.risk.enums.MetricsCodeEnum;
import lombok.Data;

/**
 * 用于在 gRPC Context 中传递指标数据的Holder.
 * 由 Interceptor 创建并放入 Context, 由 AspectService 修改其内部状态.
 */
@Data
public class MetricDataHolder {

    public static final String UNKNOWN = "UNKNOWN";
    /**
     * 错误码, 默认为成功
     */
    public String code = MetricsCodeEnum.SubCode.SUCCESS.getCode();
    /**
     * 错误信息, 默认为成功
     */
    public String msg = MetricsCodeEnum.SubCode.SUCCESS.getDesc();
    /**
     * 业务域类型, 默认为 OTHER
     */
    public String domainType = MetricsCodeEnum.DomainType.OTHER.name();

    /**
     * 错误类型 (BIZ_ERR, SYS_ERR, NONE), 默认为 NONE
     */
    public String type = MetricsCodeEnum.DomainType.NONE.name();

    /**
     * 默认为 UNKNOWN
     */
    public String name = UNKNOWN;

    /** gRPC 方法名, 默认为 UNKNOWN */
    public String methodName = "UNKNOWN";

    public Boolean isMapi = false;

    // 用户，素材类型，size
    public String username = UNKNOWN;
    public String materialTypeName;
    private String size = "0";
    private String subSize = "0";
    private String pullStrategy = "0";
    private String isProgram = "0";
    private String success = MetricsCodeEnum.SubCode.SUCCESS.getDesc();

    @Override
    public String toString() {
        return "MetricDataHolder{" +
                "code='" + code + '\'' +
                ", errorMsg='" + msg + '\'' +
                ", domainType='" + domainType + '\'' +
                ", errType='" + type + '\'' +
                ", methodName='" + name + '\'' +
                '}';
    }
}
