package com.bilibili.risk.service;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.risk.bo.LauMaterialAuditLabelBo;
import com.bilibili.risk.bo.MaterialAuditLabelTreeNode;
import com.bilibili.risk.config.RedisConfig;
import com.bilibili.risk.convertor.IMaterialAuditLabelConvertor;
import com.bilibili.risk.dao.risk.LauMaterialAuditLabelDao;
import com.bilibili.risk.enums.AuditLabelLevelEnum;
import com.bilibili.risk.po.risk.LauMaterialAuditLabelPo;
import com.bilibili.risk.po.risk.LauMaterialAuditLabelPoExample;
import edu.emory.mathcs.backport.java.util.Collections;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class MaterialAuditLabelService {

    private final LauMaterialAuditLabelDao lauMaterialAuditLabelDao;

    @Resource(name = RedisConfig.ADP_CLUSTER)
    private RedissonClient adpRedissonClient;

    private static final String MATERIAL_AUDIT_LABEL_ALL_CACHE_KEY = "risk:material_audit_label:all";
    private static final String MATERIAL_AUDIT_LABEL_TREE_CACHE_KEY = "risk:material_audit_label:tree:";
    private static final long CACHE_EXPIRE_TIME = 30; // 缓存过期时间，单位分钟

    public List<LauMaterialAuditLabelBo> queryListByIds(List<Long> labelIds) {

        if (CollectionUtils.isEmpty(labelIds)) {
            return Collections.emptyList();
        }

        LauMaterialAuditLabelPoExample example = new LauMaterialAuditLabelPoExample();
        example.createCriteria().andIdIn(labelIds);
        List<LauMaterialAuditLabelPo> auditLabelPos = lauMaterialAuditLabelDao.selectByExample(example);
        return IMaterialAuditLabelConvertor.INSTANCE.pos2bos(auditLabelPos);
    }

    /**
     * 查询所有审核标签，使用Redis缓存优化
     *
     * @return 所有审核标签列表
     */
    public List<LauMaterialAuditLabelBo> queryAllList() {
        // 1. 尝试从Redis缓存中获取
        RBucket<List<LauMaterialAuditLabelBo>> bucket = adpRedissonClient.getBucket(MATERIAL_AUDIT_LABEL_ALL_CACHE_KEY);
        List<LauMaterialAuditLabelBo> cachedLabels = bucket.get();

        // 2. 如果缓存中存在且不为空，直接返回缓存数据
        if (cachedLabels != null && !cachedLabels.isEmpty()) {
            log.info("queryAllList from cache, size: {}", cachedLabels.size());
            return cachedLabels;
        }

        // 3. 缓存不存在或为空，从数据库查询
        log.info("queryAllList from database");
        LauMaterialAuditLabelPoExample example = new LauMaterialAuditLabelPoExample();
        example.createCriteria();
        List<LauMaterialAuditLabelPo> auditLabelPos = lauMaterialAuditLabelDao.selectByExample(example);
        List<LauMaterialAuditLabelBo> labelBos = IMaterialAuditLabelConvertor.INSTANCE.pos2bos(auditLabelPos);

        // 4. 将查询结果存入Redis缓存
        if (!labelBos.isEmpty()) {
            bucket.set(labelBos, CACHE_EXPIRE_TIME, TimeUnit.MINUTES);
            log.info("queryAllList cache updated, size: {}", labelBos.size());
        }

        return labelBos;
    }

    public List<LauMaterialAuditLabelBo> queryListByIds(List<Long> labelIds, Long level) {

        if (CollectionUtils.isEmpty(labelIds)) {
            return Collections.emptyList();
        }

        LauMaterialAuditLabelPoExample example = new LauMaterialAuditLabelPoExample();
        LauMaterialAuditLabelPoExample.Criteria criteria = example.createCriteria().andIdIn(labelIds);

        if (Utils.isPositive(level)) {
            criteria.andLevelEqualTo(level);
        }

        List<LauMaterialAuditLabelPo> auditLabelPos = lauMaterialAuditLabelDao.selectByExample(example);
        return IMaterialAuditLabelConvertor.INSTANCE.pos2bos(auditLabelPos);
    }

    public Map<Long, LauMaterialAuditLabelBo> queryMapByIds(List<Long> labelIds) {
        return this.queryListByIds(labelIds).stream().collect(Collectors.toMap(LauMaterialAuditLabelBo::getId, t -> t));
    }

    public Map<Long, LauMaterialAuditLabelBo> queryAllMap() {
        return this.queryAllList().stream().collect(Collectors.toMap(LauMaterialAuditLabelBo::getId, t -> t));
    }

    /**
     * 获取标签树
     * 思路，查询所有的，内存里构建 tree
     * 使用Redis缓存优化
     *
     * @param needSpecialLabel 是否需要特殊标签
     * @return 标签树结构
     */
    public List<MaterialAuditLabelTreeNode> queryLabelTree(Integer needSpecialLabel) {
        log.info("Start querying material audit label tree, needSpecialLabel: {}", needSpecialLabel);

        try {
            // 1. 尝试从Redis缓存中获取
            String cacheKey = MATERIAL_AUDIT_LABEL_TREE_CACHE_KEY + (Utils.isPositive(needSpecialLabel) ? "with_special" : "normal");
            RBucket<List<MaterialAuditLabelTreeNode>> bucket = adpRedissonClient.getBucket(cacheKey);
            List<MaterialAuditLabelTreeNode> cachedTree = bucket.get();

            // 2. 如果缓存中存在且不为空，直接返回缓存数据
            if (cachedTree != null && !cachedTree.isEmpty()) {
                log.info("queryLabelTree from cache, root nodes size: {}", cachedTree.size());
                return cachedTree;
            }

            // 3. 缓存不存在或为空，从数据库查询
            log.info("queryLabelTree from database");
            // 查询所有标签
            LauMaterialAuditLabelPoExample example = new LauMaterialAuditLabelPoExample();
            LauMaterialAuditLabelPoExample.Criteria criteria = example.createCriteria();
            if (!Utils.isPositive(needSpecialLabel)) {
                criteria.andIdGreaterThan(0L);
            }

            example.setOrderByClause("level asc, id asc"); // 按层级处理
            List<LauMaterialAuditLabelPo> allLabels = lauMaterialAuditLabelDao.selectByExample(example);
            if (CollectionUtils.isEmpty(allLabels)) {
                log.info("No material audit labels found");
                return Collections.emptyList();
            }

            List<MaterialAuditLabelTreeNode> rootNodes = buildTree(allLabels);
            log.info("Successfully built material audit label tree with {} root nodes", rootNodes.size());

            // 4. 将查询结果存入Redis缓存
            if (!rootNodes.isEmpty()) {
                bucket.set(rootNodes, CACHE_EXPIRE_TIME, TimeUnit.MINUTES);
                log.info("queryLabelTree cache updated, root nodes size: {}", rootNodes.size());
            }

            return rootNodes;
        } catch (Exception e) {
            log.error("Failed to query material audit label tree", e);
            return Collections.emptyList();
        }
    }

    public static @NotNull List<MaterialAuditLabelTreeNode> buildTree(List<LauMaterialAuditLabelPo> allLabels) {
        // 构建树结构
        List<MaterialAuditLabelTreeNode> rootNodes = new ArrayList<>();
        Map<Long, MaterialAuditLabelTreeNode> auditLabelTreeNodeMap = new HashMap<>();

        // 按层级处理，处理下一级的时候，上一级的已经加入到树了
        for (LauMaterialAuditLabelPo auditLabelPo : allLabels) {
            MaterialAuditLabelTreeNode labelTreeNode = IMaterialAuditLabelConvertor.INSTANCE.po2NodeBo(auditLabelPo);
            labelTreeNode.setChildrenNodes(new ArrayList<>());
            // 如果是根节点（parentId为0或null），则添加到根节点列表
            if (Objects.equals(auditLabelPo.getLevel(), AuditLabelLevelEnum.ONE.getCode())) {
                rootNodes.add(labelTreeNode);
                auditLabelTreeNodeMap.put(auditLabelPo.getId(), labelTreeNode);
            } else {
                // 否则，将当前节点添加到其父节点的children列表中
                MaterialAuditLabelTreeNode parentTreeNode = auditLabelTreeNodeMap.get(auditLabelPo.getPid());
                if (parentTreeNode != null) {
                    parentTreeNode.getChildrenNodes().add(labelTreeNode);
                    auditLabelTreeNodeMap.put(auditLabelPo.getId(), labelTreeNode);
                } else {
                    // 如果父节点不存在，作为根节点处理
                    log.warn("Parent node with id {} not found for node {}, treating as root", auditLabelPo.getPid(), auditLabelPo.getId());
                    rootNodes.add(labelTreeNode);
                    auditLabelTreeNodeMap.put(auditLabelPo.getId(), labelTreeNode);
                }
            }
        }
        return rootNodes;
    }

    /**
     * 清除审核标签缓存
     * 当审核标签数据发生变化时调用此方法
     */
    public void clearLabelCache() {
        log.info("Clearing material audit label cache");
        // 清除所有标签列表缓存
        RBucket<List<LauMaterialAuditLabelBo>> allLabelsBucket = adpRedissonClient.getBucket(MATERIAL_AUDIT_LABEL_ALL_CACHE_KEY);
        allLabelsBucket.delete();

        // 清除标签树缓存（普通和带特殊标签的两种情况）
        RBucket<List<MaterialAuditLabelTreeNode>> normalTreeBucket = adpRedissonClient.getBucket(MATERIAL_AUDIT_LABEL_TREE_CACHE_KEY + "normal");
        normalTreeBucket.delete();

        RBucket<List<MaterialAuditLabelTreeNode>> specialTreeBucket = adpRedissonClient.getBucket(MATERIAL_AUDIT_LABEL_TREE_CACHE_KEY + "with_special");
        specialTreeBucket.delete();

        log.info("Material audit label cache cleared");
    }
}
