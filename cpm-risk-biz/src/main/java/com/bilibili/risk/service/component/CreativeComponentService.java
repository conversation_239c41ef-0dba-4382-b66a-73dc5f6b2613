package com.bilibili.risk.service.component;

import com.alibaba.fastjson.JSON;
import com.bilibili.risk.bo.ComponentMaterialBo;
import com.bilibili.risk.bo.CreativeDetailMaterialBo;
import com.bilibili.risk.constant.RiskConstants;
import com.bilibili.risk.dao.ad.*;
import com.bilibili.risk.dao.ad_core.LauCreativeComponentDao;
import com.bilibili.risk.enums.MaterialSourceEnum;
import com.bilibili.risk.enums.RiskMaterialTypeEnum;
import com.bilibili.risk.po.ad.*;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.common.util.set.Sets;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
@RequiredArgsConstructor
public class CreativeComponentService {

    private final LauStoryCommonComponentDao lauStoryCommonComponentDao;
    private final LauStoryCouponComponentDao lauStoryCouponComponentDao;
    private final LauStoryImageComponentDao lauStoryImageComponentDao;
    private final LauCreativeComponentDao lauCreativeComponentDao;
    private final LauUnderframeComponentDao lauUnderframeComponentDao;

    /**
     * 根据组件id查询关联的创意列表
     *
     * @param componentId
     * @param limit
     * @return
     */
    public List<LauCreativeComponentPo> scrollQuery(Long componentId, Integer componentType, Integer creativeId, int limit) {
        LauCreativeComponentPoExample example = new LauCreativeComponentPoExample();
        LauCreativeComponentPoExample.Criteria criteria = example.createCriteria().andComponentIdEqualTo(componentId).andComponentTypeEqualTo(componentType);
        if(null != creativeId){
            criteria.andCreativeIdGreaterThan(creativeId);
        }

        example.setLimit(limit);
        example.setOrderByClause("creative_id asc");
        return lauCreativeComponentDao.selectByExample(example);
    }

    public List<ComponentMaterialBo> queryComponentMaterialBo(Integer creativeId) {
        LauCreativeComponentPoExample example = new LauCreativeComponentPoExample();
        example.createCriteria().andCreativeIdEqualTo(creativeId);
        List<LauCreativeComponentPo> creativeComponentPos = lauCreativeComponentDao.selectByExample(example);
        if (creativeComponentPos.isEmpty()) {
            return null;
        }
        if (creativeComponentPos.size() > 1) {
            log.error("queryComponentMaterialBo, creativeId={}, creativeComponentPos.size() > 1", creativeId);
        }
        List<ComponentMaterialBo> componentMaterialBos = Lists.newArrayList();

        for (LauCreativeComponentPo creativeComponentPo : creativeComponentPos) {
            ComponentMaterialBo componentMaterialBo = fetchComponentMaterialInfo(creativeComponentPo.getComponentId(), creativeComponentPo.getComponentType());
            componentMaterialBos.add(componentMaterialBo);
        }

        // 获取组件的素材信息

        log.info("queryComponentMaterialBo, creativeId={}, componentMaterialBo={}", creativeId, JSON.toJSONString(componentMaterialBos));
        return componentMaterialBos;
    }

    public ComponentMaterialBo fetchComponentMaterialInfo(Long componentId, Integer componentType) {
        ComponentMaterialBo componentMaterialBo = ComponentMaterialBo.builder()
                .componentId(componentId)
                .build();
        // 组件类型: 0-未定义, 1-框下组件, 2-story通用组件, 3-story优惠券组件, 4-story图片组件
        if (Objects.equals(componentType, RiskConstants.COMPONENT_TYPE_UNDERFRAME)) {
            LauUnderframeComponentPoExample componentPoExample = new LauUnderframeComponentPoExample();
            componentPoExample.createCriteria().andIdEqualTo(componentId);
            List<LauUnderframeComponentPo> lauUnderframeComponentPos = lauUnderframeComponentDao.selectByExample(componentPoExample);
            if (lauUnderframeComponentPos.isEmpty()) {
                return null;
            }
            LauUnderframeComponentPo underframeComponentPo = lauUnderframeComponentPos.get(0);
            if (StringUtils.isNotEmpty(underframeComponentPo.getTitle())) {
                CreativeDetailMaterialBo componentTitleBo = CreativeDetailMaterialBo.builder()
                        .materialType(RiskMaterialTypeEnum.TEXT.getCode())
                        .materialSourceSet(Sets.newHashSet(MaterialSourceEnum.STORY.getCode()))
                        .rawContent(underframeComponentPo.getTitle())
                        .materialContent(underframeComponentPo.getTitle())
                        .materialMd5(DigestUtils.md5Hex(underframeComponentPo.getTitle()))
                        .build();
                componentMaterialBo.setComponentTitleBos(Arrays.asList(componentTitleBo));
            }
            if (StringUtils.isNotEmpty(underframeComponentPo.getDescription())) {
                CreativeDetailMaterialBo componentDescBo = CreativeDetailMaterialBo.builder()
                        .materialType(RiskMaterialTypeEnum.TEXT.getCode())
                        .materialSourceSet(Sets.newHashSet(MaterialSourceEnum.STORY.getCode()))
                        .rawContent(underframeComponentPo.getDescription())
                        .materialContent(underframeComponentPo.getDescription())
                        .materialMd5(DigestUtils.md5Hex(underframeComponentPo.getDescription()))
                        .build();
                componentMaterialBo.setComponentDescBos(Arrays.asList(componentDescBo));
            }
            if (StringUtils.isNotEmpty(underframeComponentPo.getImageUrl())) {
                CreativeDetailMaterialBo componentImageBo = CreativeDetailMaterialBo.builder()
                        .materialType(RiskMaterialTypeEnum.IMAGE.getCode())
                        .materialSourceSet(Sets.newHashSet(MaterialSourceEnum.STORY.getCode()))
                        .rawContent(underframeComponentPo.getImageUrl())
                        .materialContent(underframeComponentPo.getImageUrl())
                        .materialMd5(underframeComponentPo.getImageMd5())
                        .build();
                componentMaterialBo.setComponentImageBos(Arrays.asList(componentImageBo));
            }
            if (StringUtils.isNotEmpty(underframeComponentPo.getRawJumpUrl())) {
                CreativeDetailMaterialBo underThirdPartyLandingPageUrlBo = CreativeDetailMaterialBo.builder()
                        .materialType(RiskMaterialTypeEnum.LINK.getCode())
                        .materialSourceSet(Sets.newHashSet(MaterialSourceEnum.STORY.getCode()))
                        .rawContent(underframeComponentPo.getRawJumpUrl())
                        .materialContent(underframeComponentPo.getRawJumpUrl())
                        .build();
                componentMaterialBo.setUnderThirdPartyLandingPageUrlBos(Arrays.asList(underThirdPartyLandingPageUrlBo));
            }

        } else if (Objects.equals(componentType, RiskConstants.COMPONENT_TYPE_STORY_COMMON)) {
            LauStoryCommonComponentPoExample componentPoExample = new LauStoryCommonComponentPoExample();
            componentPoExample.createCriteria().andComponentIdEqualTo(componentId);
            List<LauStoryCommonComponentPo> lauStoryCommonComponentPos = lauStoryCommonComponentDao.selectByExample(componentPoExample);
            if (lauStoryCommonComponentPos.isEmpty()) {
                return null;
            }
            LauStoryCommonComponentPo storyCommonComponentPo = lauStoryCommonComponentPos.get(0);
            if (StringUtils.isNotEmpty(storyCommonComponentPo.getTitle())) {
                CreativeDetailMaterialBo componentTitleBo = CreativeDetailMaterialBo.builder()
                        .materialType(RiskMaterialTypeEnum.TEXT.getCode())
                        .materialSourceSet(Sets.newHashSet(MaterialSourceEnum.STORY.getCode()))
                        .rawContent(storyCommonComponentPo.getTitle())
                        .materialContent(storyCommonComponentPo.getTitle())
                        .materialMd5(DigestUtils.md5Hex(storyCommonComponentPo.getTitle()))
                        .build();
                componentMaterialBo.setComponentTitleBos(Arrays.asList(componentTitleBo));
            }
            if (StringUtils.isNotEmpty(storyCommonComponentPo.getDescription())) {
                CreativeDetailMaterialBo componentDescBo = CreativeDetailMaterialBo.builder()
                        .materialType(RiskMaterialTypeEnum.TEXT.getCode())
                        .materialSourceSet(Sets.newHashSet(MaterialSourceEnum.STORY.getCode()))
                        .rawContent(storyCommonComponentPo.getDescription())
                        .materialContent(storyCommonComponentPo.getDescription())
                        .materialMd5(DigestUtils.md5Hex(storyCommonComponentPo.getDescription()))
                        .build();
                componentMaterialBo.setComponentDescBos(Arrays.asList(componentDescBo));
            }
            if (StringUtils.isNotEmpty(storyCommonComponentPo.getImageUrl())) {
                CreativeDetailMaterialBo componentImageBo = CreativeDetailMaterialBo.builder()
                        .materialType(RiskMaterialTypeEnum.IMAGE.getCode())
                        .materialSourceSet(Sets.newHashSet(MaterialSourceEnum.STORY.getCode()))
                        .rawContent(storyCommonComponentPo.getImageUrl())
                        .materialContent(storyCommonComponentPo.getImageUrl())
                        .materialMd5(storyCommonComponentPo.getImageMd5())
                        .build();
                componentMaterialBo.setComponentImageBos(Arrays.asList(componentImageBo));
            }

        } else if (Objects.equals(componentType, RiskConstants.COMPONENT_TYPE_STORY_COUPON)) {
            LauStoryCouponComponentPoExample componentPoExample = new LauStoryCouponComponentPoExample();
            componentPoExample.createCriteria().andComponentIdEqualTo(componentId);
            List<LauStoryCouponComponentPo> lauStoryCouponComponentPos = lauStoryCouponComponentDao.selectByExample(componentPoExample);
            if (lauStoryCouponComponentPos.isEmpty()) {
                return null;
            }
            LauStoryCouponComponentPo storyCouponComponentPo = lauStoryCouponComponentPos.get(0);
            if (StringUtils.isNotEmpty(storyCouponComponentPo.getDescription())) {
                CreativeDetailMaterialBo componentDescBo = CreativeDetailMaterialBo.builder()
                        .materialType(RiskMaterialTypeEnum.TEXT.getCode())
                        .materialSourceSet(Sets.newHashSet(MaterialSourceEnum.STORY.getCode()))
                        .rawContent(storyCouponComponentPo.getDescription())
                        .materialContent(storyCouponComponentPo.getDescription())
                        .materialMd5(DigestUtils.md5Hex(storyCouponComponentPo.getDescription()))
                        .build();
                componentMaterialBo.setComponentDescBos(Arrays.asList(componentDescBo));
            }
        } else if (Objects.equals(componentType, RiskConstants.COMPONENT_TYPE_STORY_IMAGE)) {
            LauStoryImageComponentPoExample componentPoExample = new LauStoryImageComponentPoExample();
            componentPoExample.createCriteria().andComponentIdEqualTo(componentId);
            List<LauStoryImageComponentPo> lauStoryImageComponentPos = lauStoryImageComponentDao.selectByExample(componentPoExample);
            if (lauStoryImageComponentPos.isEmpty()) {
                return null;
            }
            LauStoryImageComponentPo storyImageComponentPo = lauStoryImageComponentPos.get(0);
            if (StringUtils.isNotEmpty(storyImageComponentPo.getImageUrl())) {
                CreativeDetailMaterialBo componentImageBo = CreativeDetailMaterialBo.builder()
                        .materialType(RiskMaterialTypeEnum.IMAGE.getCode())
                        .materialSourceSet(Sets.newHashSet(MaterialSourceEnum.STORY.getCode()))
                        .rawContent(storyImageComponentPo.getImageUrl())
                        .materialContent(storyImageComponentPo.getImageUrl())
                        .materialMd5(storyImageComponentPo.getImageMd5())
                        .build();
                componentMaterialBo.setComponentImageBos(Arrays.asList(componentImageBo));
            }
        }
        return componentMaterialBo;
    }

}
