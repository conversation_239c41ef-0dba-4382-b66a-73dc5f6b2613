package com.bilibili.risk.service.creative;

import com.bapis.ad.creative.CreativeServiceGrpc;
import com.bapis.ad.creative.QueryCreativeListRep;
import com.bapis.ad.creative.QueryCreativeListReq;
import com.bapis.ad.creative.SingleQueryCreativeListRep;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.risk.bo.CreativeBo;
import com.bilibili.risk.bo.CreativeDetailInfoBo;
import com.bilibili.risk.config.BizConfig;
import com.bilibili.risk.convertor.ICreativeConvertor;
import com.bilibili.risk.dao.ad_core.LauProgrammaticCreativeDetailDao;
import com.bilibili.risk.dao.ad_core.LauShadowCreativeDao;
import com.bilibili.risk.dao.ad_core.LauUnitCreativeDao;
import com.bilibili.risk.po.ad.LauShadowCreativePo;
import com.bilibili.risk.po.ad.LauShadowCreativePoExample;
import com.bilibili.risk.po.ad.LauUnitCreativePo;
import com.bilibili.risk.po.ad.LauUnitCreativePoExample;
import edu.emory.mathcs.backport.java.util.Collections;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import pleiades.venus.starter.rpc.client.RPCClient;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CreativeService {

    @RPCClient("sycpb.cpm.cpm-adp")
    private CreativeServiceGrpc.CreativeServiceBlockingStub creativeServiceBlockingStub;
    @Autowired
    private LauUnitCreativeDao lauUnitCreativeDao;
    @Autowired
    private LauProgrammaticCreativeDetailDao lauProgrammaticCreativeDetailDao;
    @Autowired
    private LauShadowCreativeDao lauShadowCreativeDao;
    @Autowired
    private BizConfig bizConfig;

    /**
     * 查询创意列表
     *
     * @param creativeId
     * @return
     */
    public CreativeBo fetchCreativeById(Integer creativeId) {

        if (!Utils.isPositive(creativeId)) {
            return null;
        }

        if (Utils.isPositive(bizConfig.getAdpGrpcSwitch())) {
            QueryCreativeListReq request = QueryCreativeListReq.newBuilder().addCreativeIds(creativeId).build();
            QueryCreativeListRep queryCreativeListRep = creativeServiceBlockingStub
                    .withDeadlineAfter(5000L, TimeUnit.MILLISECONDS)
                    .withWaitForReady().queryCreativeList(request);
            List<SingleQueryCreativeListRep> creativeListRepDataList = queryCreativeListRep.getDataList();
            List<CreativeBo> creativeBos = ICreativeConvertor.INSTANCE.grpcs2Bos(creativeListRepDataList);

            if (CollectionUtils.isEmpty(creativeBos)) {
                return null;
            }
            return creativeBos.get(0);
        }
        LauUnitCreativePoExample example = new LauUnitCreativePoExample();
        example.createCriteria().andCreativeIdEqualTo(creativeId);
        List<LauUnitCreativePo> unitCreativePos = lauUnitCreativeDao.selectByExample(example);
        if (CollectionUtils.isEmpty(unitCreativePos)) {
            return null;
        }
        return ICreativeConvertor.INSTANCE.unitCreativePos2Bos(unitCreativePos).get(0);
    }

    /**
     * 查询创意列表
     *
     * @param creativeIds
     * @return
     */
    public List<CreativeBo> queryListByCreativeIds(List<Integer> creativeIds) {

        if (CollectionUtils.isEmpty(creativeIds)) {
            return Collections.emptyList();
        }

        if (Utils.isPositive(bizConfig.getAdpGrpcSwitch())) {
            QueryCreativeListReq request = QueryCreativeListReq.newBuilder().addAllCreativeIds(creativeIds).build();
            QueryCreativeListRep queryCreativeListRep = creativeServiceBlockingStub
                    .withDeadlineAfter(5000L, TimeUnit.MILLISECONDS)
                    .withWaitForReady().queryCreativeList(request);
            List<SingleQueryCreativeListRep> creativeListRepDataList = queryCreativeListRep.getDataList();
            return ICreativeConvertor.INSTANCE.grpcs2Bos(creativeListRepDataList);
        }
        LauUnitCreativePoExample example = new LauUnitCreativePoExample();
        example.createCriteria().andCreativeIdIn(creativeIds);
        List<LauUnitCreativePo> unitCreativePos = lauUnitCreativeDao.selectByExample(example);
        if (CollectionUtils.isEmpty(unitCreativePos)) {
            return Collections.emptyList();
        }
        return ICreativeConvertor.INSTANCE.unitCreativePos2Bos(unitCreativePos);
    }



    public Map<Integer, CreativeBo> queryMapByCreativeIds(List<Integer> creativeIds) {

        List<CreativeBo> creativeBos = this.queryListByCreativeIds(creativeIds);
        return creativeBos.stream().collect(Collectors.toMap(CreativeBo::getCreativeId, t -> t));
    }

    // 查询创意的素材列表信息
    public List<CreativeDetailInfoBo> queryCreativeDetailInfoList(List<Integer> creativeIds) {

        if (CollectionUtils.isEmpty(creativeIds)) {
            return Collections.emptyList();
        }

        return null;
    }

    public Map<Integer, CreativeDetailInfoBo> queryCreativeDetailInfoMap(List<Integer> creativeIds) {

        return this.queryCreativeDetailInfoList(creativeIds).stream().collect(Collectors.toMap(CreativeDetailInfoBo::getCreativeId, t -> t));
    }

    /**
     * 查询创意的素材列表
     *
     * @param creativeId
     * @return
     */
    public CreativeDetailInfoBo fetchCreativeDetailInfoByCreativeId(Integer creativeId) {

        return null;
    }

    public LauShadowCreativePo queryShadow(Integer creativeId) {

        if (!Utils.isPositive(creativeId)) {
            return null;
        }

        LauShadowCreativePoExample example = new LauShadowCreativePoExample();
        example.createCriteria().andCreativeIdEqualTo(creativeId);
        List<LauShadowCreativePo> lauShadowCreativePos = lauShadowCreativeDao.selectByExampleWithBLOBs(example);
        if (CollectionUtils.isEmpty(lauShadowCreativePos)) {
            return null;
        }
        return lauShadowCreativePos.get(0);
    }

}
