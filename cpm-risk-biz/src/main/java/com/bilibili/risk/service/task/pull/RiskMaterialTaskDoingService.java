package com.bilibili.risk.service.task.pull;

import com.bilibili.risk.dao.risk.RiskMaterialTaskDoingDao;
import com.bilibili.risk.po.risk.RiskMaterialTaskDoingPo;
import com.bilibili.risk.po.risk.RiskMaterialTaskDoingPoExample;
import edu.emory.mathcs.backport.java.util.Collections;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class RiskMaterialTaskDoingService {

    private final RiskMaterialTaskDoingDao riskMaterialTaskDoingDao;

    public void insert(List<RiskMaterialTaskDoingPo> poList) {
        if (poList.isEmpty()) {
            return;
        }
        int insertCount = riskMaterialTaskDoingDao.insertBatch(poList);
        log.info("insert risk_material_task_doing count: {}", insertCount);
    }

    public void deleteByTaskIds(List<String> taskIds) {
        if (taskIds.isEmpty()) {
            return;
        }
        RiskMaterialTaskDoingPoExample example = new RiskMaterialTaskDoingPoExample();
        example.createCriteria().andTaskIdIn(taskIds);
        int deleteCount = riskMaterialTaskDoingDao.deleteByExample(example);
        log.info("delete risk_material_task_doing count: {},taskIds={}", deleteCount, taskIds);
    }

    public Integer deleteByTaskId(String taskId) {
        if (StringUtils.isEmpty(taskId)) {
            return 0 ;
        }
        RiskMaterialTaskDoingPoExample example = new RiskMaterialTaskDoingPoExample();
        example.createCriteria().andTaskIdEqualTo(taskId);
        int deleteCount = riskMaterialTaskDoingDao.deleteByExample(example);
        log.info("delete risk_material_task_doing count: {},taskIds={}", deleteCount, taskId);
        return deleteCount;
    }

    public List<RiskMaterialTaskDoingPo> queryByUsernameAndQueueId(String username, Long queueId) {

        RiskMaterialTaskDoingPoExample example = new RiskMaterialTaskDoingPoExample();
        example.createCriteria()
                .andUsernameEqualTo(username)
                .andQueueIdEqualTo(queueId);
        return riskMaterialTaskDoingDao.selectByExample(example);
    }

    public List<RiskMaterialTaskDoingPo> queryByUsernameAndQueueIds(String username, List<Long> queueIds) {

        RiskMaterialTaskDoingPoExample example = new RiskMaterialTaskDoingPoExample();
        example.createCriteria()
                .andUsernameEqualTo(username)
                .andQueueIdIn(queueIds);
        return riskMaterialTaskDoingDao.selectByExample(example);
    }

    public List<RiskMaterialTaskDoingPo> queryByTaskIds(List<String> taskIds) {

        if (CollectionUtils.isEmpty(taskIds)) {
            return Collections.emptyList();
        }

        RiskMaterialTaskDoingPoExample example = new RiskMaterialTaskDoingPoExample();
        example.createCriteria()
                .andTaskIdIn(taskIds);
        return riskMaterialTaskDoingDao.selectByExample(example);
    }

}
