package com.bilibili.risk.service.task;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.id.service.SegmentService;
import com.bilibili.risk.bo.LauMaterialAuditTaskUpdateBo;
import com.bilibili.risk.constant.DatabaseConstant;
import com.bilibili.risk.convertor.IMaterialAuditTaskConvertor;
import com.bilibili.risk.dao.risk.LauMaterialAuditTaskDao;
import com.bilibili.risk.dao.risk.LauMaterialAuditTaskExtDao;
import com.bilibili.risk.enums.MaterialTaskStatusEnum;
import com.bilibili.risk.enums.ShardingTableEnum;
import com.bilibili.risk.po.risk.LauMaterialAuditTaskPo;
import com.bilibili.risk.po.risk.LauMaterialAuditTaskPoExample;
import com.bilibili.risk.utils.MaterialTaskUtils;
import edu.emory.mathcs.backport.java.util.Collections;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 审核任务数据库操作
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MaterialAuditTaskDbService {

    private final LauMaterialAuditTaskDao lauMaterialAuditTaskDao;
    private final LauMaterialAuditTaskExtDao lauMaterialAuditTaskExtDao;
    private final SegmentService segmentService;
    private final MaterialTaskEsPubService materialTaskEsPubService;

    public Integer updateMaterialAuditTaskPos(List<LauMaterialAuditTaskPo> pos) {

        if (CollectionUtils.isEmpty(pos)) {
            return 0;
        }

        // 排序下更新，防止死锁
        pos = pos.stream().sorted(Comparator.comparing(LauMaterialAuditTaskPo::getTaskId)).collect(Collectors.toList());

        Integer count = 0;
        for (LauMaterialAuditTaskPo auditTaskPo : pos) {
            LauMaterialAuditTaskPoExample example = new LauMaterialAuditTaskPoExample();
            example.createCriteria().andTaskIdEqualTo(auditTaskPo.getTaskId());

            LauMaterialAuditTaskPo newTaskPo = IMaterialAuditTaskConvertor.INSTANCE.copy(auditTaskPo);
            // 清空分表键字段，禁止更新
            newTaskPo.setTaskId(null);
            newTaskPo.setCreativeId(null);
            newTaskPo.setId(null);
            count += lauMaterialAuditTaskDao.updateByExampleSelective(newTaskPo, example);
        }
        return count;
    }

    public Integer insertMaterialAuditTaskPos(List<LauMaterialAuditTaskPo> pos) {
        for (LauMaterialAuditTaskPo auditTaskPo : pos) {
            lauMaterialAuditTaskDao.insertSelective(auditTaskPo);
        }
        return 0;
    }

    public Integer deleteMaterialAuditTaskPos(List<LauMaterialAuditTaskPo> needDeleteAuditTaskPos) {
        if (CollectionUtils.isEmpty(needDeleteAuditTaskPos)) {
            return 0;
        }

        Map<Integer, List<LauMaterialAuditTaskPo>> needDelete_partition = needDeleteAuditTaskPos.stream()
                .collect(Collectors.groupingBy(e -> MaterialTaskUtils.calculateShardingKeyByTaskId(e.getTaskId(), ShardingTableEnum.TABLE_LAU_MATERIAL_AUDIT_TASK.getShardingAlgorithm())));

        Integer count = 0;
        for (Map.Entry<Integer, List<LauMaterialAuditTaskPo>> entry : needDelete_partition.entrySet()) {
            LauMaterialAuditTaskPoExample example = new LauMaterialAuditTaskPoExample();
            example.createCriteria().andTaskIdIn(entry.getValue().stream().map(LauMaterialAuditTaskPo::getTaskId).collect(Collectors.toList()));

            LauMaterialAuditTaskPo newTaskPo = new LauMaterialAuditTaskPo();
            newTaskPo.setIsDeleted(1);
            newTaskPo.setStatus(MaterialTaskStatusEnum.STOP.getCode());
            count += lauMaterialAuditTaskDao.updateByExampleSelective(newTaskPo, example);
        }

        return count;
    }

    // 乐观锁方式更新
    @Transactional(rollbackFor = Exception.class, transactionManager = DatabaseConstant.RISK_TRANSACTION_MANAGER)
    public Integer updateMaterialAuditTaskBosOptimistic(List<LauMaterialAuditTaskUpdateBo> updateBos) {
        if (CollectionUtils.isEmpty(updateBos)) {
            return 0;
        }
        updateBos = updateBos.stream().sorted(Comparator.comparing(LauMaterialAuditTaskUpdateBo::getTaskId)).collect(Collectors.toList());

        Map<Integer, List<LauMaterialAuditTaskUpdateBo>> update_partition = updateBos.stream()
                .collect(Collectors.groupingBy(e -> MaterialTaskUtils.calculateShardingKeyByTaskId(e.getTaskId(), ShardingTableEnum.TABLE_LAU_MATERIAL_AUDIT_TASK.getShardingAlgorithm())));

        int affect = 0;
        for (Map.Entry<Integer, List<LauMaterialAuditTaskUpdateBo>> entry : update_partition.entrySet()) {
            // todo simer: 乐观锁更新，批量处理
//            affect += lauMaterialAuditTaskExtDao.updateMaterialAuditTaskBosOptimistic(entry.getValue());

            for (LauMaterialAuditTaskUpdateBo updateBo : entry.getValue()) {
                LauMaterialAuditTaskPo copy = IMaterialAuditTaskConvertor.INSTANCE.updateBo2Po(updateBo);
                copy.setVersion(updateBo.getVersion() + 1);
                copy.setTaskId(null);
                copy.setMaterialId(null);
                copy.setCreativeId(null);
                LauMaterialAuditTaskPoExample example = new LauMaterialAuditTaskPoExample();
                example.createCriteria().andTaskIdEqualTo(updateBo.getTaskId()).andVersionEqualTo(updateBo.getVersion());
                affect += lauMaterialAuditTaskDao.updateByExampleSelective(copy, example);
            }
        }
        log.info("updateMaterialAuditTaskBosOptimistic, 可能修改数量={}", affect);
        return affect;
    }

    public List<LauMaterialAuditTaskPo> queryListByTaskIds(List<String> taskIds) {

        if (CollectionUtils.isEmpty(taskIds)) {
            return Collections.emptyList();
        }

        Map<Integer, List<String>> taskIdMap = taskIds.stream().collect(Collectors.groupingBy(t -> MaterialTaskUtils.calculateShardingKeyByTaskId(t, ShardingTableEnum.TABLE_LAU_MATERIAL_AUDIT_TASK.getShardingAlgorithm())));

        List<LauMaterialAuditTaskPo> auditTaskPos = new ArrayList<>();
        for (Map.Entry<Integer, List<String>> entry : taskIdMap.entrySet()) {
            LauMaterialAuditTaskPoExample example = new LauMaterialAuditTaskPoExample();
            example.createCriteria().andTaskIdIn(entry.getValue());
            List<LauMaterialAuditTaskPo> tmpAuditTaskPos = lauMaterialAuditTaskDao.selectByExample(example);
            if (!CollectionUtils.isEmpty(tmpAuditTaskPos)) {
                auditTaskPos.addAll(tmpAuditTaskPos);
            }
        }
        return auditTaskPos;
    }

    public List<LauMaterialAuditTaskPo> queryListByTaskIdsWithBlobs(List<String> taskIds) {

        if (CollectionUtils.isEmpty(taskIds)) {
            return Collections.emptyList();
        }

        Map<Integer, List<String>> taskIdMap = taskIds.stream().collect(Collectors.groupingBy(t -> MaterialTaskUtils.calculateShardingKeyByTaskId(t, ShardingTableEnum.TABLE_LAU_MATERIAL_AUDIT_TASK.getShardingAlgorithm())));

        List<LauMaterialAuditTaskPo> auditTaskPos = new ArrayList<>();
        for (Map.Entry<Integer, List<String>> entry : taskIdMap.entrySet()) {
            LauMaterialAuditTaskPoExample example = new LauMaterialAuditTaskPoExample();
            example.createCriteria().andTaskIdIn(entry.getValue());
            List<LauMaterialAuditTaskPo> tmpAuditTaskPos = lauMaterialAuditTaskDao.selectByExampleWithBLOBs(example);
            if (!CollectionUtils.isEmpty(tmpAuditTaskPos)) {
                auditTaskPos.addAll(tmpAuditTaskPos);
            }
        }
        return auditTaskPos;
    }

    public LauMaterialAuditTaskPo fetchByTaskId(String taskId) {

        if (StringUtils.isEmpty(taskId)) {
            return null;
        }

        LauMaterialAuditTaskPoExample example = new LauMaterialAuditTaskPoExample();
        example.createCriteria().andTaskIdEqualTo(taskId);
        List<LauMaterialAuditTaskPo> taskPos = lauMaterialAuditTaskDao.selectByExample(example);
        if (CollectionUtils.isEmpty(taskPos)) {
            return null;
        }
        return taskPos.get(0);
    }

    public Map<String, LauMaterialAuditTaskPo> queryMapByTaskIds(List<String> taskIds) {

        List<LauMaterialAuditTaskPo> auditTask00Pos = this.queryListByTaskIds(taskIds);
        return auditTask00Pos.stream().collect(Collectors.toMap(LauMaterialAuditTaskPo::getTaskId, t -> t));
    }

    public List<LauMaterialAuditTaskPo> queryListByCreativeIds(List<Integer> creativeIds) {
        Assert.isTrue(!CollectionUtils.isEmpty(creativeIds), "分表键creativeId不能为空");

        Map<Integer, List<Integer>> creativeIdsMap = creativeIds.stream().collect(Collectors.groupingBy(
                creativeId -> MaterialTaskUtils.calculateShardingKeyByCreativeId(creativeId)
        ));
        List<LauMaterialAuditTaskPo> auditTaskPos = new ArrayList<>();
        for (Map.Entry<Integer, List<Integer>> creativeEntry : creativeIdsMap.entrySet()) {
            LauMaterialAuditTaskPoExample example = new LauMaterialAuditTaskPoExample();
            LauMaterialAuditTaskPoExample.Criteria criteria = example.createCriteria().andCreativeIdIn(creativeEntry.getValue());

            List<LauMaterialAuditTaskPo> taskPos = lauMaterialAuditTaskDao.selectByExample(example);
            if (!CollectionUtils.isEmpty(taskPos)) {
                auditTaskPos.addAll(taskPos);
            }
        }
        return auditTaskPos;
    }

    public List<LauMaterialAuditTaskPo> queryListByCreativeId(Integer creativeId, Integer isDeleted) {
        if (!Utils.isPositive(creativeId)) {
            return Collections.emptyList();
        }

        LauMaterialAuditTaskPoExample example = new LauMaterialAuditTaskPoExample();
        LauMaterialAuditTaskPoExample.Criteria criteria = example.createCriteria().andCreativeIdEqualTo(creativeId);
        if (isDeleted != null) {
            criteria.andIsDeletedEqualTo(isDeleted);
        }
        List<LauMaterialAuditTaskPo> taskPos = lauMaterialAuditTaskDao.selectByExample(example);
        return taskPos;
    }

    public List<LauMaterialAuditTaskPo> queryListByCreativeIdAndMaterialId(Integer creativeId, String materialId) {
        if (!Utils.isPositive(creativeId)) {
            return Collections.emptyList();
        }

        LauMaterialAuditTaskPoExample example = new LauMaterialAuditTaskPoExample();
        LauMaterialAuditTaskPoExample.Criteria criteria = example.createCriteria().andCreativeIdEqualTo(creativeId);
        if (StringUtils.isNotEmpty(materialId)) {
            criteria.andMaterialIdEqualTo(materialId);
        }
        List<LauMaterialAuditTaskPo> taskPos = lauMaterialAuditTaskDao.selectByExample(example);
        return taskPos;
    }

    public List<LauMaterialAuditTaskPo> queryTasksByCreativeIds(List<Integer> creativeIds, String materialId, List<Integer> statusList, Integer isDeleted) {
        Assert.isTrue(!CollectionUtils.isEmpty(creativeIds), "分表键creativeId不能为空");

        Map<Integer, List<Integer>> creativeIdsMap = creativeIds.stream().collect(Collectors.groupingBy(
                creativeId -> MaterialTaskUtils.calculateShardingKeyByCreativeId(creativeId)
        ));

        List<LauMaterialAuditTaskPo> auditTaskPos = new ArrayList<>();
        for (Map.Entry<Integer, List<Integer>> creativeEntry : creativeIdsMap.entrySet()) {
            LauMaterialAuditTaskPoExample example = new LauMaterialAuditTaskPoExample();
            LauMaterialAuditTaskPoExample.Criteria criteria = example.createCriteria().andCreativeIdIn(creativeEntry.getValue()).andMaterialIdEqualTo(materialId);
            if (!CollectionUtils.isEmpty(statusList)) {
                criteria.andStatusIn(statusList);
            }
            if(null != isDeleted){
                criteria.andIsDeletedEqualTo(isDeleted);
            }

            List<LauMaterialAuditTaskPo> taskPos = lauMaterialAuditTaskDao.selectByExample(example);
            if (!CollectionUtils.isEmpty(taskPos)) {
                auditTaskPos.addAll(taskPos);
            }
        }
        return auditTaskPos;
    }

    public int updateTask(LauMaterialAuditTaskPo po) {
        Assert.isTrue(StringUtils.isNotEmpty(po.getTaskId()), "taskId is null");

        LauMaterialAuditTaskPoExample example = new LauMaterialAuditTaskPoExample();
        example.createCriteria().andTaskIdEqualTo(po.getTaskId());
        LauMaterialAuditTaskPo copy = IMaterialAuditTaskConvertor.INSTANCE.copy(po);
        copy.setTaskId(null);
        int count = lauMaterialAuditTaskDao.updateByExampleSelective(copy, example);
        return count;
    }

    public List<LauMaterialAuditTaskPo> scrollFixedTable(int shardingNo, LauMaterialAuditTaskPo po, int pageSize, Pair<Timestamp, Timestamp> mtimePair) {
        LauMaterialAuditTaskPoExample example = new LauMaterialAuditTaskPoExample();
        LauMaterialAuditTaskPoExample.Criteria criteria = example.createCriteria().andMtimeLessThan(mtimePair.getLeft())
                .andMtimeGreaterThan(mtimePair.getRight())
                .andIsDeletedEqualTo(po.getIsDeleted()).andAuditLabelThirdIdEqualTo(po.getAuditLabelThirdId());

        if(StringUtils.isNotBlank(po.getTaskId())){
            criteria.andTaskIdGreaterThan(po.getTaskId());
        }

        example.setOrderByClause("task_id asc");
        example.setLimit(pageSize);
        return lauMaterialAuditTaskExtDao.selectByExample(shardingNo, example);
    }
}
