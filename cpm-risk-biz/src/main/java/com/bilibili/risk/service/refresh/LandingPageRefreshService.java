package com.bilibili.risk.service.refresh;

import com.bilibili.risk.bo.msg.OtherSourceFirePushAuditMsgBo;
import com.bilibili.risk.enums.FirePushAuditSourceEnum;
import com.bilibili.risk.po.ad.LauCreativeLandingPageGroupPo;
import com.bilibili.risk.service.material.CreativeMaterialPushToAuditService;
import com.bilibili.risk.service.page_group.PageGroupService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class LandingPageRefreshService {

    private final PageGroupService pageGroupService;

    private final CreativeMaterialPushToAuditService creativeMaterialPushToAuditService;

    public void refreshMgkGroup(){
        List<LauCreativeLandingPageGroupPo> creativeLandingPageGroupPos;
        Integer size = 0, creativeId = null;
        do {
            // 1、滚动拉取建站创建的落地页组
            creativeLandingPageGroupPos = pageGroupService.scrollQuery(creativeId, 0, 100);

            if (CollectionUtils.isEmpty(creativeLandingPageGroupPos)) {
                break;
            }

            Map<Long, Set<Integer>> groupId_creative = creativeLandingPageGroupPos.stream().collect(Collectors.groupingBy(
                    LauCreativeLandingPageGroupPo::getGroupId, Collectors.mapping(LauCreativeLandingPageGroupPo::getCreativeId, Collectors.toSet())));

            for (Map.Entry<Long, Set<Integer>> entry : groupId_creative.entrySet()) {
                OtherSourceFirePushAuditMsgBo msgBo = OtherSourceFirePushAuditMsgBo.builder().source(FirePushAuditSourceEnum.PAGE_GROUP.getCode())
                        .pageGroupId(entry.getKey()).build();

                creativeMaterialPushToAuditService.firePushAuditIfNeed(entry.getValue(), msgBo);
            }

            creativeId = creativeLandingPageGroupPos.get(creativeLandingPageGroupPos.size() - 1).getCreativeId();
            size += creativeLandingPageGroupPos.size();

            log.info("refreshMgkGroup, size: {}", size);
        } while (creativeLandingPageGroupPos.size() == 100);

        log.info("refreshMgkGroup, end");
    }
}
