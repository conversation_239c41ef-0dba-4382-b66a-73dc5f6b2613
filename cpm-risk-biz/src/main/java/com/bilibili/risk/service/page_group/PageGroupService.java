package com.bilibili.risk.service.page_group;

import com.bapis.ad.mgk.page.group.*;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.risk.bo.AuditPageGroupBo;
import com.bilibili.risk.bo.LandingPageGroupBaseBo;
import com.bilibili.risk.bo.LandingPageGroupBo;
import com.bilibili.risk.convertor.IPageGroupConvertor;
import com.bilibili.risk.dao.ad_core.LauCreativeLandingPageGroupDao;
import com.bilibili.risk.po.ad.LauCreativeLandingPageGroupPo;
import com.bilibili.risk.po.ad.LauCreativeLandingPageGroupPoExample;
import com.google.common.collect.Lists;
import com.google.protobuf.Empty;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import pleiades.venus.starter.rpc.client.RPCClient;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 落地页组
 */
@Slf4j
@Service
public class PageGroupService {

    @RPCClient("sycpb.cpm.mgk-portal")
    private LandingPageGroupServiceGrpc.LandingPageGroupServiceBlockingStub landingPageGroupServiceBlockingStub;
    @Autowired
    private LauCreativeLandingPageGroupDao lauCreativeLandingPageGroupDao;

    public List<LandingPageGroupBaseBo> queryPageGroupBaseDtoList(List<Long> pageGroupIdList) {
        if (CollectionUtils.isEmpty(pageGroupIdList)) {
            return Collections.emptyList();
        }

        QueryPageGroupBaseReq req = QueryPageGroupBaseReq.newBuilder()
                .addAllPageGroupId(Lists.newArrayList(pageGroupIdList))
                .build();
        QueryPageGroupBaseReply reply = landingPageGroupServiceBlockingStub.withWaitForReady()
                .withDeadlineAfter(2000L, TimeUnit.MILLISECONDS)
                .queryPageGroupBase(req);
        if (CollectionUtils.isEmpty(reply.getBaseEntitiesList())) {
            return null;
        }
        return reply.getBaseEntitiesList().stream()
                .map(t -> IPageGroupConvertor.INSTANCE.grpc2Bo(t))
                .collect(Collectors.toList());
    }

    public List<LandingPageGroupBo> queryPageGroupBoList(List<Long> pageGroupIdList) {
        if (CollectionUtils.isEmpty(pageGroupIdList)) {
            return Collections.emptyList();
        }

        QueryPageGroupReq req = QueryPageGroupReq.newBuilder()
                .addAllPageGroupId(Lists.newArrayList(pageGroupIdList))
                .build();
        QueryPageGroupReply reply = landingPageGroupServiceBlockingStub.withWaitForReady()
                .withDeadlineAfter(2000L, TimeUnit.MILLISECONDS)
                .queryPageGroup(req);
        if (CollectionUtils.isEmpty(reply.getPageGroupList())) {
            return Collections.emptyList();
        }
        return reply.getPageGroupList().stream()
                .map(t -> IPageGroupConvertor.INSTANCE.grpc2Bo(t))
                .collect(Collectors.toList());
    }

    public LandingPageGroupBo fetchPageGroupBo(Long pageGroupId) {
        if (!Utils.isPositive(pageGroupId)) {
            return null;
        }

        QueryPageGroupReq req = QueryPageGroupReq.newBuilder()
                .addAllPageGroupId(Lists.newArrayList(pageGroupId))
                .build();
        QueryPageGroupReply reply = landingPageGroupServiceBlockingStub.withWaitForReady()
                .withDeadlineAfter(2000L, TimeUnit.MILLISECONDS)
                .queryPageGroup(req);
        if (CollectionUtils.isEmpty(reply.getPageGroupList())) {
            return null;
        }

        try {
            log.info("fetchPageGroupBo pageGroupId[获取落地页组]: {}, reply: {}", pageGroupId, JsonFormat.printer().print(reply));
        } catch (InvalidProtocolBufferException e) {
            log.error("fetchPageGroupBo pageGroupId[获取落地页组]: {}, 打日志产生异常", pageGroupId, e);
        }
        return reply.getPageGroupList().stream()
                .map(t -> IPageGroupConvertor.INSTANCE.grpc2Bo(t))
                .collect(Collectors.toList()).get(0);
    }

    /**
     * 根据组件id查询关联的创意列表
     *
     * @param groupId
     * @param limit
     * @return
     */
    public List<LauCreativeLandingPageGroupPo> scrollQuery(Long groupId, Integer creativeId, int limit) {
        LauCreativeLandingPageGroupPoExample example = new LauCreativeLandingPageGroupPoExample();
        LauCreativeLandingPageGroupPoExample.Criteria criteria = example.createCriteria().andGroupIdEqualTo(groupId);
        if(null != creativeId){
            criteria.andCreativeIdGreaterThan(creativeId);
        }

        example.setLimit(limit);
        example.setOrderByClause("creative_id asc");
        return lauCreativeLandingPageGroupDao.selectByExample(example);
    }

    public List<LauCreativeLandingPageGroupPo> scrollQuery(Integer creativeId, Integer groupSource, int limit) {
        LauCreativeLandingPageGroupPoExample example = new LauCreativeLandingPageGroupPoExample();
        LauCreativeLandingPageGroupPoExample.Criteria criteria = example.createCriteria().andGroupSourceEqualTo(groupSource);
        if(null != creativeId){
            criteria.andCreativeIdGreaterThan(creativeId);
        }

        example.setLimit(limit);
        example.setOrderByClause("creative_id asc");
        return lauCreativeLandingPageGroupDao.selectByExample(example);
    }

    public void auditLandingPageGroup(AuditPageGroupBo auditPageGroupBo) {

        AuditPageGroupReq auditPageGroupReq = AuditPageGroupReq.newBuilder()
                .setGroupId(auditPageGroupBo.getGroupId())
                .setOperatorName(auditPageGroupBo.getOperatorName())
                .addAllAuditMapping(IPageGroupConvertor.INSTANCE.bo2grpcs(auditPageGroupBo.getAuditMappings()))
                .setOperatorType(OperatorType.OPERATING_PERSONNEL)
                .setModifyVersion(auditPageGroupBo.getModifyVersion())
                .build();
        log.info("auditLandingPageGroup auditPageGroupReq[落地页组审核]: {}", auditPageGroupReq);
        Empty empty = landingPageGroupServiceBlockingStub
                .withWaitForReady()
                .withDeadlineAfter(2000L, TimeUnit.MILLISECONDS)
                .auditPageGroup(auditPageGroupReq);

    }
}
