package com.bilibili.risk.service.lancer;


import com.bilibili.risk.enums.AuditTaskLancerEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import pleiades.venus.infoc.InfocClient;

import java.util.Collections;

@Component
@Slf4j
public class LancerReportService {

    // 上报的logId，从数据平台申请
    public static final String logId = "027115";

    @Autowired
    private InfocClient infocClient;

    public void report(AuditTaskLancerEnum auditTaskLancerEnum, Integer size) {
        try {
            if(null == auditTaskLancerEnum) {
                log.error("auditTaskLancerEnum is null");
                return;
            }
            // 可以传递任意类型的参数，最终会调用对象的toString()方法输出
            // 注意顺序必须和数据平台的定义一致，空值必须占位
            boolean success = infocClient.log(logId, Collections.emptyMap(), null, auditTaskLancerEnum.getBizType(), auditTaskLancerEnum.getOptType(), size);
        } catch (Exception e) {
            log.error("infocClient invoke error", e);
        }
    }

    public boolean report(AuditTaskLancerEnum auditTaskLancerEnum, String objId, String ext) {
        try {
            if(null == auditTaskLancerEnum) {
                log.error("auditTaskLancerEnum is null");
                return false;
            }

            return infocClient.log(logId, objId, auditTaskLancerEnum.getBizType(), auditTaskLancerEnum.getOptType(), ext);
        } catch (Exception e) {
            log.error("infocClient invoke error", e);
        }
        return false;
    }


}
