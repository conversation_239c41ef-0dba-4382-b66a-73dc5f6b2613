package com.bilibili.risk.service.task;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.rbac.api.dto.UserDto;
import com.bilibili.rbac.api.service.IUserService;
import com.bilibili.risk.bo.*;
import com.bilibili.risk.bo.msg.AuditLabelNodeMsgBo;
import com.bilibili.risk.bo.msg.ExternalMaterialAuditMsgBo;
import com.bilibili.risk.bo.msg.LandingPageTaskRpcCallTimeoutMsgBo;
import com.bilibili.risk.bo.msg.MaterialAuditMsgBo;
import com.bilibili.risk.config.BizConfig;
import com.bilibili.risk.constant.RiskConstants;
import com.bilibili.risk.convertor.IMaterialAuditTaskConvertor;
import com.bilibili.risk.databus.material_audit.ExternalMaterialAuditPub;
import com.bilibili.risk.databus.material_audit.MaterialAuditToJudgeAllCreativePub;
import com.bilibili.risk.databus.timeout_task.LandingPageTaskRpcCallTimeoutPub;
import com.bilibili.risk.enums.*;
import com.bilibili.risk.po.risk.LauMaterialAuditRuleQueueRelPo;
import com.bilibili.risk.po.risk.LauMaterialAuditRuleRoleRelPo;
import com.bilibili.risk.po.risk.LauMaterialAuditTaskPo;
import com.bilibili.risk.service.MaterialAuditLabelService;
import com.bilibili.risk.service.creative.CreativeService;
import com.bilibili.risk.service.log.OperationLogService;
import com.bilibili.risk.service.material.LauMaterialAuditService;
import com.bilibili.risk.metrics_report.metrics.MetricDataHolder;
import com.bilibili.risk.metrics_report.metrics.TaskMetricsReportService;
import com.bilibili.risk.service.page_group.PageGroupService;
import com.bilibili.risk.service.queue.MaterialAuditQueueService;
import com.bilibili.risk.service.rule.MaterialAuditRuleQueueRelService;
import com.bilibili.risk.service.rule.MaterialAuditRuleRoleRelService;
import com.bilibili.risk.utils.DiffLogUtils;
import com.bilibili.risk.utils.JacksonUtils;
import com.bilibili.risk.utils.MaterialTaskUtils;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.common.util.set.Sets;
import org.modelmapper.internal.util.Assert;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 素材审核功能
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MaterialAuditTaskAuditService {

    private final MaterialAuditTaskDbService materialAuditTaskDbService;
    private final MaterialAuditLabelService materialAuditLabelService;
    private final LauMaterialAuditService lauMaterialAuditService;
    private final CreativeService creativeService;
    private final MaterialAuditToJudgeAllCreativePub materialAuditToJudgeAllCreativePub;
    private final OperationLogService operationLogService;
    private final PageGroupService pageGroupService;
    private final TaskAuditExecuteDbUpdateService taskAuditExecuteDbUpdateService;
    private final LandingPageTaskRpcCallTimeoutPub landingPageTaskRpcCallTimeoutPub;
    private final RedissonClient adpRedissonClient;
    private final MaterialAuditQueueService materialAuditQueueService;
    private final BizConfig bizConfig;
    private final IUserService userService;
    private final MaterialAuditRuleRoleRelService materialAuditRuleRoleRelService;
    private final MaterialAuditRuleQueueRelService materialAuditRuleQueueRelService;
    private final ExternalMaterialAuditPub externalMaterialAuditPub;
    private final TaskMetricsReportService taskMetricsReportService;
    private final PullRedisZsetService pullRedisZsetService;

    /**
     * 审核一批素材列表
     * 特殊队列的也能审，目前没有限制
     *
     * @param materialTaskAuditBo
     * @return
     */
    @SneakyThrows
    public MaterialTaskAuditResultBo batchAuditTask(MaterialTaskAuditBo materialTaskAuditBo) {
        log.info("batchAuditTask[批量审核任务], materialTaskAuditBo={}", materialTaskAuditBo);
        Long startTime = System.currentTimeMillis();
        MetricDataHolder data = new MetricDataHolder();
        data.setDomainType(MetricsCodeEnum.DomainType.CREATIVE_MATERIAL_TASK.name());
        data.setType(MetricsCodeEnum.Type.BIZ_INDEX.name());
        data.setUsername(materialTaskAuditBo.getExecuteName());
        data.setCode(MetricsCodeEnum.SubCode.BIZ_MATERIAL_TASK_AUDIT.getCode());
        data.setMsg(MetricsCodeEnum.SubCode.BIZ_MATERIAL_TASK_AUDIT.getDesc());
        data.setSuccess(MetricsCodeEnum.SubCode.SUCCESS.getDesc());

        RLock lock = adpRedissonClient.getLock(RiskConstants.LOCK_TASK_AUDIT + materialTaskAuditBo.getExecuteName());
        try {
            if (lock.tryLock(3, 10, TimeUnit.SECONDS)) {

                preValidate(materialTaskAuditBo);

                MaterialTaskAuditContext context = buildContext(materialTaskAuditBo);

                validate(context);

                // 落地页组仅执行同步调用，由 binlog 触发异步同步
                landingPageGroupCall(context);

                // build 任务和素材修改列表
                processData(materialTaskAuditBo, context);

                taskAuditExecuteDbUpdateService.doSaveDbData(context);

                operationLogService.saveLogs(context.getOperationLogBos());

                // 消息发送可以通过 监听 素材表 binlog
                sendDatabusMsg(materialTaskAuditBo, context);

                // 审核完成的任务id放入redis(解决拉取任务时，es数据延迟的问题)
                pullRedisZsetService.cacheCompletedTaskIds(context.getTaskUpdateBos());

                Long endTime = System.currentTimeMillis();
                data.setMaterialTypeName(context.getMaterialType() + "");
                data.setSize(String.valueOf(materialTaskAuditBo.getTaskLabelBos().size()));
                taskMetricsReportService.addBizMetricCountAndCost(endTime - startTime, data);
                return MaterialTaskAuditResultBo.builder()
                        .completeCount(context.getCompleteUpdateBos().size())
                        .stopCount(context.getStopCount())
                        .hasBeStopCount(context.getHasBeStopCount())
                        .doNothingCount(context.getDoNothingCount())
                        .build();
            } else {
                throw new RuntimeException("当前用户正在审核，请稍后重试");
            }
        } catch (Exception e) {
            Long endTime = System.currentTimeMillis();
            log.error("batchAuditTask error, materialTaskAuditBo={}", materialTaskAuditBo, e);
            data.setSuccess(MetricsCodeEnum.SubCode.FAIL.getDesc());
            taskMetricsReportService.addBizMetricCountAndCost(endTime - startTime, data);
            throw e;
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    private static void preValidate(MaterialTaskAuditBo materialTaskAuditBo) {
        Assert.notNull(materialTaskAuditBo, "materialTaskAuditBo参数不能为空");
        Assert.isTrue(!CollectionUtils.isEmpty(materialTaskAuditBo.getTaskLabelBos()), "未选择任务");
        // 三级标签可以为空，为空默认表示通过
    }

    private void landingPageGroupCall(MaterialTaskAuditContext context) {

        if (!RiskMaterialTypeEnum.PAGE_GROUP_PAGE.getCode().equals(context.getMaterialType())) {
            return;
        }

        // 落地页组调用外部接口 grpc
        LandingPageGroupBo landingPageGroupBo = context.getLandingPageGroupBo();
        Map<Long, LandingPageGroupMappingBo> pageGroupMappingBoMap = landingPageGroupBo.getMappingBos().stream().collect(Collectors.toMap(t -> t.getPageId(), t -> t));
        Map<String, TaskLabelBo> pageAuditTaskPoMap = context.getTaskLabelBoMap();
        List<AuditPageGroupMappingEntityBo> mappingEntityBos = new ArrayList<>();
        Map<String, LauMaterialAuditTaskPo> auditTaskPoMap = context.getAuditTaskPos().stream().collect(Collectors.toMap(t -> t.getTaskId(), t -> t, (t1, t2) -> t1));

        for (Map.Entry<String, TaskLabelBo> entry : pageAuditTaskPoMap.entrySet()) {
            TaskLabelBo taskLabelBo = entry.getValue();
            LauMaterialAuditTaskPo auditTaskPo = auditTaskPoMap.get(taskLabelBo.getTaskId());
            String pageId = RiskMaterialTypeEnum.PAGE_GROUP_PAGE.parse(auditTaskPo.getMaterialContent());

            mappingEntityBos.add(AuditPageGroupMappingEntityBo.builder()
                    .pageId(Long.parseLong(pageId))
                    .auditPass(Objects.equals(taskLabelBo.getAuditStatus(), CreativeAuditStatusEnum.AUDIT_PADDED.getCode()) ? true : false)
                    .reason(taskLabelBo.getReason())
                    .build());
        }

        if (CollectionUtils.isEmpty(mappingEntityBos)) {
            log.warn("landingPageGroupCall[落地页组调用外部接口] mappingEntityBos is empty, materialTaskAuditBo={}", context);
            return;
        }
        AuditPageGroupBo auditPageGroupBo = AuditPageGroupBo.builder()
                .groupId(context.getPageGroupId())
                .operatorName(context.getExecuteName())
                .modifyVersion(landingPageGroupBo.getModifyVersion())
                .auditMappings(mappingEntityBos)
                .build();
        log.info("landingPageGroupCall[落地页组调用外部接口] start, auditPageGroupBo={}", auditPageGroupBo);
        pageGroupService.auditLandingPageGroup(auditPageGroupBo);
        log.info("landingPageGroupCall[落地页组调用外部接口] end, auditPageGroupBo={}", auditPageGroupBo);
        Cat.logEvent("batchAuditTask", "landingPageGroupCall end");
    }

    private void sendDatabusMsg(MaterialTaskAuditBo materialTaskAuditBo, MaterialTaskAuditContext context) {

        // 落地页组的等待回调才会通知处理创意
        List<LauMaterialAuditTaskUpdateBo> completeUpdateBos = context.getCompleteUpdateBos();
        if (isLandingPageGroupAudit(materialTaskAuditBo, context)) {
            for (LauMaterialAuditTaskUpdateBo completeUpdateBo : completeUpdateBos) {
                landingPageTaskRpcCallTimeoutPub.pub(LandingPageTaskRpcCallTimeoutMsgBo.builder()
                        .materialId(completeUpdateBo.getMaterialId()).executeName(materialTaskAuditBo.getExecuteName())
                        .taskId(completeUpdateBo.getTaskId()).build());
            }
        } else {
            // 发送消息判定每个素材关联创意审核状态，以及创意审核判定
            for (LauMaterialAuditTaskUpdateBo completeUpdateBo : completeUpdateBos) {

                MaterialAuditMsgBo materialAuditMsgBo = MaterialAuditMsgBo.builder()
                        .materialId(completeUpdateBo.getMaterialId())
                        .executeName(materialTaskAuditBo.getExecuteName())
                        .auditLabelThirdId(completeUpdateBo.getAuditLabelThirdId())
                        .reason(completeUpdateBo.getReason())
                        .onlyJudgeOneCreative(materialTaskAuditBo.getOnlyJudgeOneCreative())
                        .creativeId(completeUpdateBo.getCreativeId())
                        .triggerTaskId(completeUpdateBo.getTaskId())
                        .build();
                log.info("sendDatabusMsg[异步判定素材的创意的情况] materialAuditMsgBo={}", materialAuditMsgBo);
                materialAuditToJudgeAllCreativePub.pub(materialAuditMsgBo);
            }
        }
        Cat.logEvent("batchAuditTask", "sendDatabusMsg end");
    }


    private void processData(MaterialTaskAuditBo materialTaskAuditBo, MaterialTaskAuditContext context) {
        List<LauMaterialAuditTaskUpdateBo> taskUpdateBos = new ArrayList<>();
        List<LauMaterialAuditTaskUpdateBo> completeUpdateBos = new ArrayList<>();
        List<MaterialAuditTaskOperationLogBo> logBos = new ArrayList<>();
        Integer stopCount = 0;
        Integer hasBeStopStatusCount = 0;
        Integer doNothingCount = 0;

        // 针对要修改的任务，进行处理
        for (LauMaterialAuditTaskPo auditTaskPo : context.getAuditTaskPos()) {
            LauMaterialAuditBo materialAuditBo = context.getMaterialAuditBoMap().get(auditTaskPo.getMaterialId());
            CreativeBo creativeBo = context.getCreativeBoMap().get(auditTaskPo.getCreativeId());
            TaskLabelBo taskLabelBo = context.getTaskLabelBoMap().get(auditTaskPo.getTaskId());
            Map<Long, LauMaterialAuditQueueBo> auditQueueBoMap = context.getAuditQueueBoMap();
            List<Long> thirdLabelIds = taskLabelBo.getAuditLabelThirdIds();
            LauMaterialAuditQueueBo auditQueueBo = auditQueueBoMap.get(auditTaskPo.getQueueId());

            LauMaterialAuditTaskUpdateBo updateBo = new LauMaterialAuditTaskUpdateBo();
            updateBo.setTaskId(auditTaskPo.getTaskId());
            updateBo.setCreativeId(auditTaskPo.getCreativeId());
            updateBo.setQueueId(auditTaskPo.getQueueId());
            updateBo.setMaterialId(auditTaskPo.getMaterialId());
            updateBo.setVersion(auditTaskPo.getVersion());
            // es分区是根据ctime
            updateBo.setCtime(auditTaskPo.getCtime());
            String reason = taskLabelBo.getReason();

            // 素材不存在，创意不存在，则stop
            Integer operationType = OperationTypeEnum.AUDIT.getCode();
            String logValue = "";
            if (materialAuditBo == null) { // 一般不会出现的
                updateBo.setStatus(MaterialTaskStatusEnum.STOP.getCode());
                stopCount++;
                logValue = "任务被废弃";
                log.error("processData[素材不存在] materialAuditBo is null, taskId={}, creativeId={}", auditTaskPo.getTaskId(), auditTaskPo.getCreativeId());
            } else if (creativeBo == null) {
                updateBo.setStatus(MaterialTaskStatusEnum.STOP.getCode());
                materialAuditBo.setMtime(Utils.getNow());
                stopCount++;
                logValue = "任务被废弃";
                log.info("processData[创意不存在] creativeBo is null, taskId={}, creativeId={}", auditTaskPo.getTaskId(), auditTaskPo.getCreativeId());
            }
            // 任务是废弃状态，则不处理
            else if (auditTaskPo.getStatus().equals(MaterialTaskStatusEnum.STOP.getCode())) {
                log.info("processData[任务是废弃状态,不处理] taskId={}, creativeId={}", auditTaskPo.getTaskId(), auditTaskPo.getCreativeId());
                doNothingCount++;
                continue;
            }
            else {
                boolean isContinue = true;
                // 工作台审核
                if (Utils.isPositive(context.getIsFromWorkBench())) {
                    // 工作台情况下，游离态，说明已经被自动释放了
                    if (auditTaskPo.getStatus().equals(MaterialTaskStatusEnum.FREE.getCode())) {
                        log.info("processData[来自工作台&任务是游离态,不处理] taskId={}, creativeId={}", auditTaskPo.getTaskId(), auditTaskPo.getCreativeId());
                        doNothingCount++;
                        continue;
                    }
                    // 完成态（理论上不做处理）前面check接口会挡住
                    else if (auditTaskPo.getStatus().equals(MaterialTaskStatusEnum.COMPLETE.getCode())) {
                        log.info("processData[来自工作台&任务是完成态,不处理] taskId={}, creativeId={}", auditTaskPo.getTaskId(), auditTaskPo.getCreativeId());
                        doNothingCount++;
                        continue;
                    }
                    // 检查任务超时: doing，看领取人
                    else if (auditTaskPo.getStatus().equals(MaterialTaskStatusEnum.DOING.getCode())) {
                        if (Objects.equals(context.getExecuteName(), auditTaskPo.getAcceptName())) {
                            // 任务是进行中，且领取人是当前操作人
                            // 当前时间 > 领取时间+超时时间*60*1000 && 状态是进行中
                            if (MaterialAuditTaskTimeoutService.checkIfTimeout(auditTaskPo, auditQueueBo, context.getExecuteName())) {
                                updateBo.setAcceptName("");
                                updateBo.setAcceptTime(RiskConstants.BUSINESS_EARLIEST_TIMESTAMP);
                                updateBo.setReceiveBatchNo("");
                                updateBo.setStatus(MaterialTaskStatusEnum.FREE.getCode());
                                operationType = OperationTypeEnum.AUDIT.getCode();
                                logValue = "任务超时释放，超时时间: " + auditQueueBo.getTaskTimeout() + "分钟" + "，当时的领取人: " + auditTaskPo.getAcceptName();
                                log.info("processData[任务超时] taskId={}, creativeId={},当时batchNo={}", auditTaskPo.getTaskId(), auditTaskPo.getCreativeId(), auditTaskPo.getReceiveBatchNo());
                                isContinue = false;
                            }
                        } else {
                            // 任务是进行中，且领取人不是当前操作人，则超时,但是什么都不做
                            log.info("processData[来自工作台&任务doing，领取人别人,不处理] taskId={}, creativeId={}", auditTaskPo.getTaskId(), auditTaskPo.getCreativeId());
                            doNothingCount++;
                            continue;
                        }
                    }
                }
                // 非工作台审核，不关心是否超时

                if (isContinue) {
                    String auditLabelThirdIdStr = MaterialTaskUtils.longList2String(thirdLabelIds);
                    updateBo.setAuditLabelThirdId(auditLabelThirdIdStr);
                    updateBo.setReason(reason);
                    // 落地页组素材类型，但是非回调
                    if (isLandingPageGroupAudit(materialTaskAuditBo, context)) {
                        updateBo.setStatus(MaterialTaskStatusEnum.TO_CALLBACK_AUDIT.getCode());
                        updateBo.setAuditLabelThirdId(org.apache.commons.lang3.StringUtils.EMPTY);
                        updateBo.setReason(org.apache.commons.lang3.StringUtils.EMPTY);
                        operationType = OperationTypeEnum.AUDIT_TO_EXECUTE.getCode();
                    } else {
                        updateBo.setStatus(MaterialTaskStatusEnum.COMPLETE.getCode());
                    }
                    // 第一次审核才会记录执行人执行时间，后面就不会改了; 游离和进行中的操作才会记录执行人执行时间
                    if (auditTaskPo.getStatus().equals(MaterialTaskStatusEnum.FREE.getCode()) || auditTaskPo.getStatus().equals(MaterialTaskStatusEnum.DOING.getCode())) {
                        updateBo.setExecuteTime(Utils.getNow());
                        updateBo.setExecuteName(materialTaskAuditBo.getExecuteName());
                    }

                    // 修改了 material 表的状态
                    if (!isLandingPageGroupAudit(materialTaskAuditBo, context)) {
                        materialAuditBo.setAuditLabelThirdId(auditLabelThirdIdStr);
                        materialAuditBo.setReason(reason);
                    } else {
                        // 落地页组的，由异步任务更新
                        materialAuditBo.setAuditLabelThirdId(org.apache.commons.lang3.StringUtils.EMPTY);
                        materialAuditBo.setReason(org.apache.commons.lang3.StringUtils.EMPTY);
                    }

                    materialAuditBo.setMtime(Utils.getNow());
                    completeUpdateBos.add(updateBo);
                    log.info("processData[素材审核] materialAuditBo, taskId={}, creativeId={}", auditTaskPo.getTaskId(), auditTaskPo.getCreativeId());
                    logValue = MaterialTaskUtils.genAuditLogValue(auditLabelThirdIdStr, context.getAuditLabelBoMap());
                }
            }

            LauMaterialAuditTaskUpdateBo oldUpdateBo = IMaterialAuditTaskConvertor.INSTANCE.po2UpdateBo(auditTaskPo);
            String diffLogStr = DiffLogUtils.generateDiffField(oldUpdateBo, updateBo, "任务对象");

            MaterialAuditTaskOperationLogBo logBo = MaterialAuditTaskOperationLogBo.builder()
                    .objId(auditTaskPo.getTaskId())
                    .operatorUsername(materialTaskAuditBo.getExecuteName())
                    .operationType(operationType)
                    .value(logValue)
                    .diff(diffLogStr)
                    .remark(JSON.toJSONString(updateBo))
                    .ctime(System.currentTimeMillis())
                    .type(LogTypeEnum.TASK.getCode())
                    .systemType(RiskConstants.SYSTEM_TYPE_RISK)
                    .build();
            logBos.add(logBo);
            taskUpdateBos.add(updateBo);
        }
        context.setTaskUpdateBos(taskUpdateBos);
        context.setCompleteUpdateBos(completeUpdateBos);
        context.setOperationLogBos(logBos);
        context.setStopCount(stopCount);
        context.setHasBeStopCount(hasBeStopStatusCount);
        context.setDoNothingCount(doNothingCount);
        Cat.logEvent("batchAuditTask", "processData end");
    }



    private static class Result {
        public final List<LauMaterialAuditTaskUpdateBo> taskUpdateBos;
        public final List<LauMaterialAuditTaskUpdateBo> completeUpdateBos;
        public final List<MaterialAuditTaskOperationLogBo> logBos;
        public final Integer stopCount;

        public Result(List<LauMaterialAuditTaskUpdateBo> taskUpdateBos, List<LauMaterialAuditTaskUpdateBo> completeUpdateBos, List<MaterialAuditTaskOperationLogBo> logBos, Integer stopCount) {
            this.taskUpdateBos = taskUpdateBos;
            this.completeUpdateBos = completeUpdateBos;
            this.logBos = logBos;
            this.stopCount = stopCount;
        }
    }

    /**
     * 是否是进入中间临时待回调状态
     * 要求: 落地页组类型 & 非其他系统调用 & 非其他方的回调(落地页binlog的回调)
     *
     * @param materialTaskAuditBo
     * @param context
     * @return
     */
    private static boolean isLandingPageGroupAudit(MaterialTaskAuditBo materialTaskAuditBo, MaterialTaskAuditContext context) {
        return Objects.equals(context.getMaterialType(), RiskMaterialTypeEnum.PAGE_GROUP_PAGE.getCode());
    }

    private void validate(MaterialTaskAuditContext context) {

        // 任务是否存在
        List<LauMaterialAuditTaskPo> auditTaskPos = context.getAuditTaskPos();
        if (CollectionUtils.isEmpty(auditTaskPos)) {
            log.warn("batchAuditTask, auditTaskPos is empty, materialTaskAuditBo={}", context);
            throw new RuntimeException("任务不存在");
        }

        // 权限校验
        Long oneTaskQueueId = auditTaskPos.get(0).getQueueId();

        // 任务权限校验: 管理员, 队列，规则，挨着判断
        boolean hasAuth = checkHashAuth(context, oneTaskQueueId);
        if (!hasAuth) {
            throw new RuntimeException("没有该任务的权限");
        }

        // 中间临时状态的任务不允许审核，一般不会出现
        List<String> tmpStatusTaskIds = auditTaskPos.stream().filter(t -> MaterialTaskStatusEnum.getTmpStatusList().contains(t.getStatus())).map(t -> t.getTaskId()).collect(Collectors.toList());
        Assert.isTrue(CollectionUtils.isEmpty(tmpStatusTaskIds), "任务处于中间临时状态，不能审核，taskId=" + tmpStatusTaskIds);

        // 工作台审核，要求任务是自己的
        if (Utils.isPositive(context.getIsFromWorkBench())) {
            // 游离释放的任务不算，一般是因为超时自动释放了，对于这种任务什么都不做
            List<String> notMyTaskIds = auditTaskPos.stream().filter(t -> !StringUtils.isEmpty(t.getAcceptName()) && !t.getAcceptName().equals(context.getExecuteName())).map(t -> t.getTaskId()).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(notMyTaskIds)) {
                throw new RuntimeException("任务不属于你，不允许操作，taskId=" + notMyTaskIds);
            }

            // 工作台审核的时候，特殊队列的任务不允许审核
            List<String> specialQueueTaskIds = auditTaskPos.stream().filter(t -> bizConfig.getSpecialQueueIds().contains(t.getQueueId())).map(t -> t.getTaskId()).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(specialQueueTaskIds)) {
                throw new RuntimeException("等待队列和兜底队列的任务不允许审核！taskId=" + notMyTaskIds);
            }
        }

        // 由于查 es 数据存在延迟，可能会拉到不同创意的相同素材，如果审核状态不一致，要提醒订正
        Map<String, List<LauMaterialAuditTaskPo>> materialTaskMap = auditTaskPos.stream()
                .collect(Collectors.groupingBy(LauMaterialAuditTaskPo::getMaterialId));

        for (Map.Entry<String, List<LauMaterialAuditTaskPo>> entry : materialTaskMap.entrySet()) {
            if (entry.getValue().size() == 1) {
                continue;
            }
            Set<Long> auditLabelThirdIds = null;
            for (LauMaterialAuditTaskPo taskPo : entry.getValue()) {
                if (null == auditLabelThirdIds) {
                    auditLabelThirdIds = Sets.newHashSet(context.getTaskLabelBoMap().get(taskPo.getTaskId()).getAuditLabelThirdIds());
                } else if (!auditLabelThirdIds.equals(Sets.newHashSet(context.getTaskLabelBoMap().get(taskPo.getTaskId()).getAuditLabelThirdIds()))) {
                    throw new RuntimeException("任务 id 为: "
                            + entry.getValue().stream().map(LauMaterialAuditTaskPo::getTaskId).collect(Collectors.toList())
                            + "的任务，对素材的审核状态不一致，请重新审核");
                }
            }
        }

        // 三级标签树校验
        thirdLabelTreeValidate(context);

        // 落地页组的要求不能多个组一起审核
        landingPageGroupSameGroupValidate(context);

        Cat.logEvent("batchAuditTask", "validate end");
    }

    private boolean checkHashAuth(MaterialTaskAuditContext context, Long oneTaskQueueId) {

        // 开关关闭
        if (!Utils.isPositive(bizConfig.getAuditTaskRoleCheckSwitch())) {
            return true;
        }

        boolean hasAuth = false;
        UserDto user = userService.getUserByUserName(bizConfig.getTenantId(), context.getExecuteName());
        Assert.notNull(user, "用户不存在");
        List<Integer> roleIdsOfUser = user.getRoles().stream().map(r -> r.getId()).distinct().collect(Collectors.toList());
        // 业务管理员直接可以操作
        if (roleIdsOfUser.contains(bizConfig.getBusinessAdminRoleId())) {
            hasAuth = true;
        } else {
            LauMaterialAuditQueueBo auditQueueBo = context.getAuditQueueBoMap().get(oneTaskQueueId);
            // 队列是否有权限, 如果没有看规则
            // 非业务管理员，要求用户roleIds与队列roleIds存在交集
            List<Integer> queueRoleIds = Arrays.stream(auditQueueBo.getRoleIds().split(",")).map(String::trim).map(Integer::parseInt).collect(Collectors.toList());
            List<Integer> intersectionRoleIds = roleIdsOfUser.stream()
                    .filter(queueRoleIds::contains)
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(intersectionRoleIds)) {
                hasAuth = true;
            } else {
                List<LauMaterialAuditRuleQueueRelPo> queueRelPos = materialAuditRuleQueueRelService.queryAuditRuleQueueRelListByQueueId(oneTaskQueueId);
                List<Long> ruleIdsOfQueue = queueRelPos.stream().map(t -> t.getRuleId()).distinct().collect(Collectors.toList());
                List<LauMaterialAuditRuleRoleRelPo> ruleRoleRelPos = materialAuditRuleRoleRelService.queryAuditRuleRoleRelListByRuleIds(ruleIdsOfQueue);
                List<Integer> roleIdsOfRules = ruleRoleRelPos.stream().map(t -> t.getRoleId()).distinct().collect(Collectors.toList());
                List<Integer> intersectionRoleIdsOfRules = roleIdsOfUser.stream()
                        .filter(roleIdsOfRules::contains)
                        .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(intersectionRoleIdsOfRules)) {
                    hasAuth = true;
                }
            }
        }
        return hasAuth;
    }

    private MaterialTaskAuditContext buildContext(MaterialTaskAuditBo materialTaskAuditBo) {

        MaterialTaskAuditContext context = MaterialTaskAuditContext.builder()
                .taskLabelBos(materialTaskAuditBo.getTaskLabelBos())
                .executeName(materialTaskAuditBo.getExecuteName())
                .onlyJudgeOneCreative(materialTaskAuditBo.getOnlyJudgeOneCreative())
                .isPageGroupCallback(materialTaskAuditBo.getIsPageGroupCallback())
                .isFromWorkBench(materialTaskAuditBo.getIsFromWorkBench())
                .build();

        // 不填写标签默认是通过，塞默认值
        List<TaskLabelBo> taskLabelBos = context.getTaskLabelBos();
        context.setTaskLabelBoMap(taskLabelBos.stream().collect(Collectors.toMap(t -> t.getTaskId(), t -> t, (t1, t2) -> t1)));
        for (TaskLabelBo taskLabelBo : taskLabelBos) {
            if (CollectionUtils.isEmpty(taskLabelBo.getAuditLabelThirdIds())) {
                taskLabelBo.setAuditLabelThirdIds(Arrays.asList(Long.valueOf(AuditLabelIdSpecialEnum.DEFAULT_PASS.getCode())));
            }
        }

        List<String> taskIds = taskLabelBos.stream().map(t -> t.getTaskId()).collect(Collectors.toList());

        // 查询任务
        Map<Integer, List<String>> taskIds_partition = taskIds.stream()
                .collect(Collectors.groupingBy(e -> MaterialTaskUtils.calculateShardingKeyByTaskId(e, ShardingTableEnum.TABLE_LAU_MATERIAL_AUDIT_TASK.getShardingAlgorithm())));
        List<LauMaterialAuditTaskPo> auditTaskPos = Lists.newArrayList();
        for (Map.Entry<Integer, List<String>> entry : taskIds_partition.entrySet()) {
            auditTaskPos.addAll(materialAuditTaskDbService.queryListByTaskIds(entry.getValue()));
        }
        context.setAuditTaskPos(auditTaskPos);

        List<Integer> creativeIds = auditTaskPos.stream().map(LauMaterialAuditTaskPo::getCreativeId).distinct().collect(Collectors.toList());
        List<String> materialIds = auditTaskPos.stream().map(LauMaterialAuditTaskPo::getMaterialId).distinct().collect(Collectors.toList());
        List<Long> queueIds = auditTaskPos.stream().map(t -> t.getQueueId()).distinct().collect(Collectors.toList());

        Map<Integer, CreativeBo> creativeBoMap = creativeService.queryMapByCreativeIds(creativeIds);
        List<LauMaterialAuditLabelBo> auditLabelBos = materialAuditLabelService.queryAllList();
        Map<Long, LauMaterialAuditLabelBo> auditLabelBoMap = auditLabelBos.stream().collect(Collectors.toMap(LauMaterialAuditLabelBo::getId, t -> t, (t1, t2) -> t1));
        context.setCreativeBoMap(creativeBoMap);
        context.setAuditLabelBoMap(auditLabelBoMap);

        Map<Long, LauMaterialAuditQueueBo> auditQueueBoMap = materialAuditQueueService.queryMapByIds(queueIds);
        context.setAuditQueueBoMap(auditQueueBoMap);

        Map<Integer, List<String>> materialIds_partition = materialIds.stream()
                .collect(Collectors.groupingBy(e -> MaterialTaskUtils.calculateShardingKeyByMaterialId(e, ShardingTableEnum.TABLE_LAU_MATERIAL_AUDIT.getShardingAlgorithm())));
        List<LauMaterialAuditBo> materialAuditBos = Lists.newArrayList();
        for (Map.Entry<Integer, List<String>> entry : materialIds_partition.entrySet()) {
            materialAuditBos.addAll(lauMaterialAuditService.queryListByMaterialIds(entry.getValue()));
        }
        Map<String, LauMaterialAuditBo> materialAuditBoMap = materialAuditBos.stream().collect(Collectors.toMap(LauMaterialAuditBo::getMaterialId, t -> t, (t1, t2) -> t1));
        context.setMaterialAuditBoMap(materialAuditBoMap);

        Cat.logEvent("batchAuditTask", "buildContext end");
        return context;
    }


    private static void thirdLabelTreeValidate(MaterialTaskAuditContext context) {

        Set<Integer> materialTypeSet = context.getAuditTaskPos().stream().map(t -> t.getMaterialType()).collect(Collectors.toSet());
        Assert.isTrue(materialTypeSet.size() == 1, "素材类型不一致，不能一起审核");

        for (TaskLabelBo taskLabelBo : context.getTaskLabelBos()) {
            StringBuilder reasonSb = new StringBuilder();
            for (Long auditLabelThirdId : taskLabelBo.getAuditLabelThirdIds()) {
                LauMaterialAuditLabelBo auditLabelBo = context.getAuditLabelBoMap().get(auditLabelThirdId);
                Assert.isTrue(auditLabelBo != null, "三级标签不存在，id=" + auditLabelThirdId);
                Assert.isTrue(auditLabelBo.getLevel().equals(AuditLabelLevelEnum.THREE.getCode()), "非三级标签，id=" + auditLabelThirdId);
                if (!StringUtils.isEmpty(auditLabelBo.getReason())) {
                    reasonSb.append(auditLabelBo.getReason());
                }
            }
            if (CollectionUtils.isEmpty(taskLabelBo.getAuditLabelThirdIds())) {
                taskLabelBo.setAuditStatus(CreativeAuditStatusEnum.AUDIT_PADDED.getCode());
            } else {
                LauMaterialAuditLabelBo auditLabelBo = context.getAuditLabelBoMap().get(taskLabelBo.getAuditLabelThirdIds().get(0));
                // handle type 与 audit status 枚举一致的
                taskLabelBo.setAuditStatus(auditLabelBo.getHandleType());
            }
        }
    }

    private void landingPageGroupSameGroupValidate(MaterialTaskAuditContext context) {

        Set<Integer> materialTypeSet = context.getAuditTaskPos().stream().map(LauMaterialAuditTaskPo::getMaterialType).collect(Collectors.toSet());
        if (!CollectionUtils.isEmpty(materialTypeSet)) {
            context.setMaterialType(materialTypeSet.iterator().next());
        }

        if (materialTypeSet.contains(RiskMaterialTypeEnum.PAGE_GROUP_PAGE.getCode())) {
            Set<Long> pageGroupIdSet = new HashSet<>();
            for (LauMaterialAuditTaskPo auditTaskPo : context.getAuditTaskPos()) {
                Long pageGroupId = MaterialTaskUtils.parsePageGroupId(auditTaskPo.getMaterialContent());
                LandingPageGroupBo landingPageGroupBo = pageGroupService.fetchPageGroupBo(pageGroupId);
                Assert.notNull(landingPageGroupBo, "落地页组不存在，id=" + pageGroupId);
                context.setLandingPageGroupBo(landingPageGroupBo);

                if (Utils.isPositive(pageGroupId)) {
                    pageGroupIdSet.add(pageGroupId);
                    context.setPageGroupId(pageGroupId);
                }
            }
            Assert.isTrue(pageGroupIdSet.size() == 1, "这批落地页组的素材属于不同的组，不能一起审核");
            log.info("landingPageGroupSameGroupValidate[落地页组任务], pageGroupIdSet={}", pageGroupIdSet);
        }
    }

    @Deprecated
    public void rejectByMaterialMd5(List<String> md5s, Integer materialType) {
        log.info("rejectByMaterialMd5, md5s={}", md5s);
        // 根据md5查询素材列表
        List<LauMaterialAuditBo> materialBos = lauMaterialAuditService.queryLauMaterialAuditBosByMd5s(md5s, materialType);

        if (CollectionUtils.isEmpty(materialBos)) {
            log.warn("rejectByMaterialMd5, materialBos is empty, md5s={}", md5s);
            return;
        }

        List<String> materialIds = materialBos.stream().map(e -> e.getId().toString()).collect(Collectors.toList());
        Map<String, LauMaterialAuditBo> materialAuditBoMap = lauMaterialAuditService.queryMapByMaterialIds(materialIds);
        for (LauMaterialAuditBo materialBo : materialBos) {
            LauMaterialAuditBo materialAuditBo = materialAuditBoMap.get(materialBo.getId());
            if (materialAuditBo == null) {
                log.warn("rejectByMaterialMd5, materialAuditBo is null, md5s={}, materialId={}", md5s, materialBo.getId());
                continue;
            }

            // 发消息处理素材的所有的创意
            MaterialAuditMsgBo materialAuditMsgBo = MaterialAuditMsgBo.builder()
                    .materialId(materialAuditBo.getMaterialId())
                    .executeName(RiskConstants.SYSTEM_USERNAME)
//                    .auditLabelThirdIds(materialTaskAuditBo.getAuditLabelThirdIds())
//                    .onlyJudgeOneCreative(materialTaskAuditBo.isOnlyJudgeOneCreative())
//                    .creativeId(compelateUpdateBo.getCreativeId())
                    .build();
            materialAuditToJudgeAllCreativePub.pub(materialAuditMsgBo);
        }

    }

    public void sendExternalMaterialAuditMsg(ExternalMaterialAuditMsgBo msgBo) {

        LauMaterialAuditBo materialAuditBo = lauMaterialAuditService.fetchByMaterialId(msgBo.getMaterialId());
        if(null == materialAuditBo) {
            log.error("sendExternalMaterialAuditMsg[素材不存在], msgBo={}", JacksonUtils.toJson(msgBo));
            return;
        }

        msgBo.setMaterialType(materialAuditBo.getMaterialType());
        msgBo.setContent(materialAuditBo.getRawContent());
        msgBo.setRiskMaterialMd5(materialAuditBo.getMaterialMd5());
        msgBo.setReason(materialAuditBo.getReason());
        // 落地页组特殊处理
        if(RiskMaterialTypeEnum.PAGE_GROUP_PAGE == RiskMaterialTypeEnum.getByCode(materialAuditBo.getMaterialType())){
            if(StringUtils.isBlank(materialAuditBo.getAuditLabelThirdId())){
                log.error("sendExternalMaterialAuditMsg[落地页组素材没有三级标签], msgBo={}", JacksonUtils.toJson(msgBo));
                return;
            }

            msgBo.setAuditLabelThirdId(Lists.newArrayList(materialAuditBo.getAuditLabelThirdId().split(",")));
        }

        // 构建三级结构
        List<MaterialAuditLabelTreeNode> treeNodes = materialAuditLabelService.queryLabelTree(1);
        msgBo.setAuditLabelTree(buildMsg(treeNodes, msgBo.getAuditLabelThirdId()));
        // 输出处置类型，拒绝理由
        List<LauMaterialAuditLabelBo> auditLabelBoList = materialAuditLabelService.queryListByIds(
                msgBo.getAuditLabelThirdId().stream().map(Long::valueOf).collect(Collectors.toList()));

        // 存在拒绝则为拒绝
        boolean rejectFlag = auditLabelBoList.stream().anyMatch(e -> MaterialHandleTypeEnum.REJECT == MaterialHandleTypeEnum.getByCode(e.getHandleType()).orElse(null));
        msgBo.setHandleType(rejectFlag ? MaterialHandleTypeEnum.REJECT.getCode() : MaterialHandleTypeEnum.PASS.getCode());
        msgBo.setPandoraReason(MaterialHandleTypeEnum.pandoraReason(auditLabelBoList));

        externalMaterialAuditPub.pub(msgBo);
    }


    private List<AuditLabelNodeMsgBo> buildMsg(List<MaterialAuditLabelTreeNode> treeNodes, List<String> thirdLabelIds){
        List<AuditLabelNodeMsgBo> result = Lists.newArrayList();
        Set<Long> labelIds = Sets.newHashSet(thirdLabelIds.stream().map(Long::valueOf).collect(Collectors.toList()));

        for (MaterialAuditLabelTreeNode treeNode : treeNodes) {
            AuditLabelNodeMsgBo first = buildChildren(treeNode, labelIds, 1);
            if(null != first) {
                result.add(first);
            }
        }

        return result;
    }

    /**
     * 递归构建三级标签树
     * @param node
     * @param labelId
     * @param level
     * @return
     */
    private AuditLabelNodeMsgBo buildChildren(MaterialAuditLabelTreeNode node, Set<Long> labelId, int level){
        if(3 == level){
            if(labelId.contains(node.getId())){
                return AuditLabelNodeMsgBo.builder().id(node.getId()).name(node.getName()).build();
            } else {
                return null;
            }
        }

        List<AuditLabelNodeMsgBo> children = Lists.newArrayList();;
        for (MaterialAuditLabelTreeNode childrenNode : node.getChildrenNodes()) {
            AuditLabelNodeMsgBo child = buildChildren(childrenNode, labelId, level + 1);
            if(null != child) {
                children.add(child);
            }
        }

        // 子集存在，则当前层级存在
        if(!CollectionUtils.isEmpty(children)){
            return AuditLabelNodeMsgBo.builder().id(node.getId()).name(node.getName()).children(children).build();
        } else {
            return null;
        }
    }
}
