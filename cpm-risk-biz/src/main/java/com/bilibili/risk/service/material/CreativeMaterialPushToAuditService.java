package com.bilibili.risk.service.material;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.risk.bo.*;
import com.bilibili.risk.bo.msg.*;
import com.bilibili.risk.constant.RiskConstants;
import com.bilibili.risk.convertor.IMaterialAuditConvertor;
import com.bilibili.risk.databus.push_to_audit.OtherSourceFirePushAuditPub;
import com.bilibili.risk.enums.*;
import com.bilibili.risk.po.risk.LauMaterialAuditTaskPo;
import com.bilibili.risk.po.risk.LauMaterialToAuditCreativePo;
import com.bilibili.risk.service.bfs.BfsServiceImpl;
import com.bilibili.risk.service.component.CreativeComponentService;
import com.bilibili.risk.service.creative.CreativeService;
import com.bilibili.risk.service.creative.LauMaterialToAuditCreativeService;
import com.bilibili.risk.service.log.OperationLogService;
import com.bilibili.risk.service.metrics.MetricDataHolder;
import com.bilibili.risk.service.metrics.TaskMetricsReportService;
import com.bilibili.risk.service.page_group.PageGroupService;
import com.bilibili.risk.service.task.MaterialAuditTaskDbService;
import com.bilibili.risk.utils.MaterialTaskUtils;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.elasticsearch.common.util.set.Sets;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.bilibili.risk.constant.RiskConstants.PAGE_GROUP_URL_KEY;

/**
 * 素材推审处理
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CreativeMaterialPushToAuditService {

    private final CreativeService creativeService;
    private final PageGroupService pageGroupService;
    private final LauMaterialAuditService lauMaterialAuditService;
    private final MaterialAuditTaskDbService materialAuditTaskDbService;
    private final OperationLogService operationLogService;
    private final CreativeComponentService creativeComponentService;
    private final LauMaterialToAuditCreativeService lauMaterialToAuditCreativeService;
    private final RedissonClient adpRedissonClient;
    private final CreativeMaterialSyncService creativeMaterialSyncService;
    private final BfsServiceImpl bfsServiceImpl;
    private final OtherSourceFirePushAuditPub otherSourceFirePushAuditPub;
    private final TaskMetricsReportService taskMetricsReportService;

    /**
     * 每次处理一个创意，否则会有多个创意的多个素材在不同的分表问题
     * <p>
     * 主要流程：
     * 1. 数据转换与验证
     * 将输入消息 MaterialInfoPushToAuditMsgBo 转换为 CreativeDetailInfoBo
     * 验证创意ID和事件时间是否有效
     * 2. 分布式锁控制
     * 使用 RedissonClient 获取锁，确保同一创意的素材处理不会并发执行
     * 锁超时设置为5秒获取，10秒持有
     * 3. 创意信息处理
     * 获取创意基本信息 (creativeService.fetchCreativeById)
     * 验证和过滤：仅处理新三连父创意(adpVersion=6且无parentCreativeId)
     * 4. 素材数据填充
     * 查询并填充创意组件素材 (queryAndFillComponentMaterialsIfNecessary)
     * 查询并填充第三方落地页组 (queryAndFillThirdPartyLandingPageGroup)
     * 5. 素材内容处理
     * 处理各类素材内容，生成materialContent和md5 (processAllMaterialsContent)
     * 涵盖文本、图片、视频、直播间等多种素材类型
     * 6. 素材合并与去重
     * 将素材按类型拆分并合并去重 (splitAndMergeMaterials)
     * 根据md5和materialType确保素材唯一性
     * 7. 同步到素材库
     * 同步处理后的素材到相关数据表 (sync2MaterialTables)
     * 发布待匹配队列消息和操作日志
     * 触发创意审核判定流程
     */
    @SneakyThrows
    public void processCreativeMaterialPushToAudit(MaterialInfoPushToAuditMsgBo msgBo) {
        log.info("processCreativeMaterialPushToAudit[推审], param={}", JSON.toJSONString(msgBo));

        // 推审消息报文格式转换
        CreativeDetailInfoBo creativeDetailInfoBo = convertToCreativeDetailInfoBo(msgBo);
        if (creativeDetailInfoBo == null || !Utils.isPositive(msgBo.getCreative_id())) {
            log.error("processCreativeMaterialPushToAudit[推审], creativeDetailInfoBo is null,creativeId={}", msgBo.getCreative_id());
            return;
        }
        if (!Utils.isPositive(msgBo.getEvent_time())) {
            log.error("processCreativeMaterialPushToAudit[推审], event_time is empty,creativeId={}", msgBo.getCreative_id());
            return;
        }
        // dpa 不用拆素材
        if (Utils.isPositive(msgBo.getIs_dpa())) {
            log.warn("processCreativeMaterialPushToAudit[推审], is_dpa creative,creativeId={}", msgBo.getCreative_id());
            return;
        }
        // 闪屏 不用拆素材
        if (Objects.equals(msgBo.getAd_type(), 2)) {
            log.warn("processCreativeMaterialPushToAudit[推审], 闪屏 creative,creativeId={}", msgBo.getCreative_id());
            return;
        }

        // 1、根据投放素材初始化 risk 素材，此处只保留映射关系，并设置入 materialId；
        initRiskMaterialIfNeed(creativeDetailInfoBo);

        RLock lock = adpRedissonClient.getLock(RiskConstants.LOCK_CREATIVE_MATERIAL_PUSH_TO_AUDIT + msgBo.getCreative_id());
        Long startTime = System.currentTimeMillis();
        MetricDataHolder data = new MetricDataHolder();
        data.setDomainType(MetricsCodeEnum.DomainType.CREATIVE_MATERIAL_TASK.name());
        data.setType(MetricsCodeEnum.Type.BIZ_INDEX.name());
        data.setCode(MetricsCodeEnum.SubCode.BIZ_CREATIVE_PUSH_TO_AUDIT.getCode());
        data.setMsg(MetricsCodeEnum.SubCode.BIZ_CREATIVE_PUSH_TO_AUDIT.getDesc());
        data.setUsername("");
        data.setMaterialTypeName("");
        data.setSize(String.valueOf(1));

        try {
            if (lock.tryLock(3, 10, TimeUnit.SECONDS)) {

                CreativeBo creativeBo = creativeService.fetchCreativeById(creativeDetailInfoBo.getCreativeId());
                if (creativeBo == null) {
                   throw new RuntimeException("创意推审，创意不存在");
                }
                creativeDetailInfoBo.setAccountId(creativeBo.getAccountId());
                creativeDetailInfoBo.setIsProgrammatic(creativeBo.getIsProgrammatic());
                creativeBo.setParentCreativeId(creativeDetailInfoBo.getParentCreativeId());
                creativeBo.setBilibiliUserId(creativeDetailInfoBo.getBilibiliUserId());
                if (Utils.isPositive(creativeBo.getParentCreativeId()) || !Objects.equals(creativeBo.getAdpVersion(), 6)) {
                    // 只处理新三连父创意
                    log.info("processCreativeMaterialPushToAudit[推审,不需要处理], creativeId={},adpVersion={},parentCreativeId={}", creativeDetailInfoBo.getCreativeId(), creativeBo.getAdpVersion(), creativeBo.getParentCreativeId());
                    return;
                }

                // 查询创意组件素材列表并填充
                queryAndFillComponentMaterialsIfNecessary(creativeDetailInfoBo);
                // 查询落地页组的落地页并填充
                queryAndFillThirdPartyLandingPageGroup(creativeDetailInfoBo);

                // 处理每种素材类型内容: material content, md5
                // 这边需要再次填充 story&落地页组 的素材
                processAllMaterialsContent(creativeDetailInfoBo);

                // 将素材按素材类型拆分并去重
                List<CreativeDetailMaterialBo> mergedMaterialBos = splitAndMergeMaterials(creativeDetailInfoBo);

                // 同步到素材库相关的表
                sync2MaterialTables(creativeDetailInfoBo.getCreativeId(), creativeDetailInfoBo.getEventTime(), creativeBo, mergedMaterialBos);

                Long endTime = System.currentTimeMillis();
                data.setIsProgram(creativeBo.getIsProgrammatic() + "");
                data.setSuccess(MetricsCodeEnum.SubCode.SUCCESS.getDesc());
                taskMetricsReportService.addBizMetricCountAndCost(endTime - startTime, data);
            } else {
                // 重新推送，判断是否要消费
                throw new RuntimeException("创意推审，获取锁失败");
            }
        } catch (Exception e) {
            Long endTime = System.currentTimeMillis();
            log.error("processCreativeMaterialPushToAudit[推审,异常], creativeId={}, eventTime={}", msgBo.getCreative_id(), msgBo.getEvent_time(), e);
            taskMetricsReportService.addBizMetricCount(MetricsCodeEnum.SubCode.BIZ_CREATIVE_PUSH_TO_AUDIT, "", 0, 1);
            data.setSuccess(MetricsCodeEnum.SubCode.FAIL.getDesc());
            taskMetricsReportService.addBizMetricCountAndCost(endTime - startTime, data);
            throw e;
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    private void initRiskMaterialIfNeed(CreativeDetailInfoBo creativeDetailInfoBo) {

        // 查询创意组件素材列表并填充
        queryAndFillComponentMaterialsIfNecessary(creativeDetailInfoBo);
        // 查询落地页组的落地页并填充
        queryAndFillThirdPartyLandingPageGroup(creativeDetailInfoBo);

        // 处理每种素材类型内容: material content, md5
        processAllMaterialsContent(creativeDetailInfoBo);

        // 将素材按素材类型拆分并去重
        List<CreativeDetailMaterialBo> mergedMaterialBos = splitAndMergeMaterials(creativeDetailInfoBo);

        List<LauMaterialAuditBo> materialBos = IMaterialAuditConvertor.INSTANCE.creativeDetailInfoOfMaterialBo2MaterialAuditBos(mergedMaterialBos);
        Map<String, LauMaterialAuditBo> materialBoMapByMd5 = lauMaterialAuditService.genMd5AndSaveMaterials(materialBos);
        for (CreativeDetailMaterialBo materialBo : mergedMaterialBos) {
            materialBo.setMaterialSourceSet(Sets.newHashSet(MaterialSourceEnum.CREATIVE.getCode()));
            materialBo.setMaterialId(materialBoMapByMd5.get(MaterialTaskUtils.genKey(materialBo.getMaterialMd5(), materialBo.getMaterialType())).getMaterialId());
        }
    }

    public CreativeDetailInfoBo convertToCreativeDetailInfoBo(MaterialInfoPushToAuditMsgBo msgBo) {

        // video, archive 合并
        List<CreativeDetailMaterialBo> videoMaterialBos = IMaterialAuditConvertor.INSTANCE.toVideoMaterialBos(msgBo.getVideos());
        List<CreativeDetailMaterialBo> archiveMaterialBos = IMaterialAuditConvertor.INSTANCE.toArchiveMaterialBos(msgBo.getArchives());
        List<CreativeDetailMaterialBo> mergeVideoBos = Stream.of(videoMaterialBos.stream(), archiveMaterialBos.stream()).flatMap(Function.identity()).collect(Collectors.toList());

        // 落地页组
        Long pageGroupId = null;
        if (!CollectionUtils.isEmpty(msgBo.getLanding_page_groups())) {
            pageGroupId = msgBo.getLanding_page_groups().get(0).getGroup_id();
        }
        // link replace 过滤
        List<LinkReplaceUrlMsgBo> validLinkReplaceUrlMsgBos = msgBo.getLink_replaces().stream().filter(t -> t.getUrl_type() == 1 && StringUtils.isNotEmpty(t.getUrl())).collect(Collectors.toList());
        // 直播间过滤
        List<LiveRoomMsgBo> validRoomMsgBos = msgBo.getSubjects().stream().filter(t -> t.getType().equals(1)).collect(Collectors.toList());
        List<CreativeDetailMaterialBo> imageMaterialBos = IMaterialAuditConvertor.INSTANCE.toImageMaterialBos(msgBo.getImages());

        // 图片的没有md5则下载
        for (CreativeDetailMaterialBo imageMaterialBo : imageMaterialBos) {
            if (StringUtils.isEmpty(imageMaterialBo.getMaterialMd5()) && StringUtils.isNotEmpty(imageMaterialBo.getRawContent())) {
                byte[] imageBytes = bfsServiceImpl.download(imageMaterialBo.getRawContent());
                imageMaterialBo.setMaterialMd5(DigestUtils.md5Hex(imageBytes));
            }
        }

        // tab url 落地页组的过滤掉，因为落地页组是根据groupId查询拼接的
        List<RawJumpUrlMsgBo> tabUrls = msgBo.getTab_urls();
        if (!CollectionUtils.isEmpty(tabUrls)) {
            // https://__PAGE_GROUP_URL__966726950676033536?from_bcg=bcg_12___CREATIVEID__
            tabUrls = tabUrls.stream().filter(t -> !t.getUrl().contains(PAGE_GROUP_URL_KEY)).collect(Collectors.toList());
        }

        return CreativeDetailInfoBo.builder()
                .creativeId(msgBo.getCreative_id())
                .adpVersion(msgBo.getAdp_version())
                .version(msgBo.getVersion())
                .mtime(msgBo.getMtime())
                .eventTime(msgBo.getEvent_time())
                .titleBos(IMaterialAuditConvertor.INSTANCE.toTitleMaterialBos(msgBo.getTitles()))
                .descBos(IMaterialAuditConvertor.INSTANCE.toDescMaterialBos(msgBo.getDescriptions()))
                .imageBos(imageMaterialBos)
                .videoBos(mergeVideoBos)
                .dynamicBos(IMaterialAuditConvertor.INSTANCE.toDynamicMaterialBos(msgBo.getDynamics()))
                .liveRoomBos(IMaterialAuditConvertor.INSTANCE.toLiveRoomMaterialBos(validRoomMsgBos))
                .thirdPartyLandingPageUrlBos(IMaterialAuditConvertor.INSTANCE.toTabUrlMaterialBos(msgBo.getRaw_jump_urls()))
                .tabUrlBos(IMaterialAuditConvertor.INSTANCE.toTabUrlMaterialBos(tabUrls))
                .replaceUrlBo(IMaterialAuditConvertor.INSTANCE.toReplaceUrlMaterialBos(validLinkReplaceUrlMsgBos))
                .gameCardBos(IMaterialAuditConvertor.INSTANCE.gameCardBos2MaterialBos(msgBo.getGame_cards()))
                .componentList(msgBo.getComponents())
                .pageGroupId(pageGroupId)
                .build();
    }

    public void queryAndFillThirdPartyLandingPageGroup(CreativeDetailInfoBo creativeDetailInfoBo) {
        if (null == creativeDetailInfoBo.getPageGroupId()) {
            return;
        }

        LandingPageGroupBo landingPageGroupBo = pageGroupService.fetchPageGroupBo(creativeDetailInfoBo.getPageGroupId());
        if (null == landingPageGroupBo) {
            log.info("processCreativeMaterialPushToAudit[推审,落地页组不存在], pageGroupId={}", creativeDetailInfoBo.getPageGroupId());
            return;
        }

        // 只处理三方落地页组
        if (landingPageGroupBo != null && Objects.equals(landingPageGroupBo.getGroupSource(), 1)) {
            List<LandingPageGroupMappingBo> mappingBos = landingPageGroupBo.getMappingBos();
            if (CollectionUtils.isNotEmpty(mappingBos)) {
                List<CreativeDetailMaterialBo> thirdPartyPageGroupUrlBos = new ArrayList<>();
                for (LandingPageGroupMappingBo mappingBo : mappingBos) {
                    AuditLabelIdSpecialEnum labelIdEnum = AuditLabelIdSpecialEnum.getByGroupPageStatus(
                            CreativeAuditStatusEnum.getByCode(mappingBo.getStatus()).orElse(null));

                    CreativeDetailMaterialBo thirdPartyPageUrlBo = CreativeDetailMaterialBo.builder()
                            .materialType(RiskMaterialTypeEnum.PAGE_GROUP_PAGE.getCode())
                            .materialSourceSet(Sets.newHashSet(MaterialSourceEnum.PAGE_GROUP_PAGE.getCode()))
                            .rawContent(RiskMaterialTypeEnum.splitPageGroupBodyId(mappingBo.getGroupId(), mappingBo.getPageId()))
                            .materialMd5(DigestUtils.md5Hex(RiskMaterialTypeEnum.splitPageGroupBodyId(mappingBo.getGroupId(), mappingBo.getPageId())))
                            .pageAuditStatus(mappingBo.getStatus())
                            .auditLabelThirdId(null == labelIdEnum ? "" : labelIdEnum.getCode())
                            .pageReason(mappingBo.getReason())
                            .build();
                    thirdPartyPageGroupUrlBos.add(thirdPartyPageUrlBo);
                }
                creativeDetailInfoBo.setThirdPartyLandingPageGroupBos(thirdPartyPageGroupUrlBos);
            }
        }
    }

    public void sync2MaterialTables(Integer creativeId, Long eventTime, CreativeBo creativeBo, List<CreativeDetailMaterialBo> mergedMaterialBos) {

        // 同步素材到素材库相关的表
        PostMaterialPushToAuditResultBo postMaterialPushToAuditResultBo = creativeMaterialSyncService.doSync2MaterialTables(
                CreativeMaterialBo.builder().creativeBo(creativeBo).creativeId(creativeId).list(mergedMaterialBos).eventTime(eventTime).build());

       /* // 待匹配队列消息
        for (TaskMatchQueueMsgBo taskMatchQueueMsgBo : postMaterialPushToAuditResultBo.getTaskMatchQueueMsgBos()) {
            log.info("processCreativeMaterialPushToAudit[待匹配队列消息], taskMatchQueueMsgBo={}", JSON.toJSONString(taskMatchQueueMsgBo));
            taskMatchQueuePub.pub(taskMatchQueueMsgBo);
        }*/
        // 日志消息
        for (MaterialAuditTaskOperationLogBo operationLogBo : postMaterialPushToAuditResultBo.getOperationLogBos()) {
            operationLogService.saveLog(operationLogBo);
        }
        // 创意存在素材复用，需要重新触发判定
    }

    /**
     * 处理每种素材类型内容: material content, md5
     *
     * @param creativeDetailInfoBo
     */
    public void processAllMaterialsContent(CreativeDetailInfoBo creativeDetailInfoBo) {

        processMaterialsContent(creativeDetailInfoBo.getTitleBos(), RiskMaterialTypeEnum.TEXT.getCode());
        processMaterialsContent(creativeDetailInfoBo.getImageBos(), RiskMaterialTypeEnum.IMAGE.getCode());
        processMaterialsContent(creativeDetailInfoBo.getDescBos(), RiskMaterialTypeEnum.TEXT.getCode());
        processMaterialsContent(creativeDetailInfoBo.getVideoBos(), RiskMaterialTypeEnum.VIDEO.getCode());
        processMaterialsContent(creativeDetailInfoBo.getLiveRoomBos(), RiskMaterialTypeEnum.LIVE.getCode());
        processMaterialsContent(creativeDetailInfoBo.getDynamicBos(), RiskMaterialTypeEnum.DYNAMIC.getCode());
        processMaterialsContent(creativeDetailInfoBo.getButtonBos(), RiskMaterialTypeEnum.TEXT.getCode());
        processMaterialsContent(creativeDetailInfoBo.getThirdPartyLandingPageUrlBos(), RiskMaterialTypeEnum.LINK.getCode());
        processMaterialsContent(creativeDetailInfoBo.getGameCardBos(), RiskMaterialTypeEnum.LINK.getCode());
        processMaterialsContent(creativeDetailInfoBo.getTabUrlBos(), RiskMaterialTypeEnum.LINK.getCode());
        processMaterialsContent(creativeDetailInfoBo.getReplaceUrlBo(), RiskMaterialTypeEnum.LINK.getCode());
        processMaterialsContent(creativeDetailInfoBo.getThirdPartyLandingPageGroupBos(), RiskMaterialTypeEnum.PAGE_GROUP_PAGE.getCode());
        processMaterialsContent(creativeDetailInfoBo.getComponentTitleBos(), RiskMaterialTypeEnum.TEXT.getCode());
        processMaterialsContent(creativeDetailInfoBo.getComponentDescBos(), RiskMaterialTypeEnum.TEXT.getCode());
        processMaterialsContent(creativeDetailInfoBo.getComponentImageBos(), RiskMaterialTypeEnum.IMAGE.getCode());
        processMaterialsContent(creativeDetailInfoBo.getUnderThirdPartyLandingPageUrlBos(), RiskMaterialTypeEnum.LINK.getCode());
    }

    /**
     * 查询创意组件素材列表并填充
     *
     * @param creativeDetailInfoBo
     */
    private void queryAndFillComponentMaterialsIfNecessary(CreativeDetailInfoBo creativeDetailInfoBo) {
        if (CollectionUtils.isEmpty(creativeDetailInfoBo.getComponentList())) {
            return;
        }

        List<ComponentMaterialBo> componentMaterialBos = creativeComponentService.queryComponentMaterialBo(creativeDetailInfoBo.getCreativeId());
        if (CollectionUtils.isEmpty(componentMaterialBos)) {
            log.info("processCreativeMaterialPushToAudit[推审,创意组件素材列表为空], creativeId={}", creativeDetailInfoBo.getCreativeId());
            return;
        }

        creativeDetailInfoBo.setComponentTitleBos(Lists.newArrayList());
        creativeDetailInfoBo.setComponentDescBos(Lists.newArrayList());
        creativeDetailInfoBo.setComponentImageBos(Lists.newArrayList());
        creativeDetailInfoBo.setUnderThirdPartyLandingPageUrlBos(Lists.newArrayList());
        for (ComponentMaterialBo componentMaterialBo : componentMaterialBos) {
            if (CollectionUtils.isNotEmpty(componentMaterialBo.getComponentTitleBos())) {
                creativeDetailInfoBo.getComponentTitleBos().addAll(componentMaterialBo.getComponentTitleBos());
            }
            if (CollectionUtils.isNotEmpty(componentMaterialBo.getComponentImageBos())) {
                creativeDetailInfoBo.getComponentImageBos().addAll(componentMaterialBo.getComponentImageBos());
            }
            if (CollectionUtils.isNotEmpty(componentMaterialBo.getComponentDescBos())) {
                creativeDetailInfoBo.getComponentDescBos().addAll(componentMaterialBo.getComponentDescBos());
            }
            if (CollectionUtils.isNotEmpty(componentMaterialBo.getUnderThirdPartyLandingPageUrlBos())) {
                creativeDetailInfoBo.getUnderThirdPartyLandingPageUrlBos().addAll(componentMaterialBo.getUnderThirdPartyLandingPageUrlBos());
            }
        }

    }


    /**
     * 归投放端的素材按素材类型合并去重处理
     *
     * @param creativeDetailInfoBo
     * @return
     */
    public List<CreativeDetailMaterialBo> splitAndMergeMaterials(CreativeDetailInfoBo creativeDetailInfoBo) {
        // 文本处理: 标题，描述，按钮文案，替链，story组件，框下，影子
        List<CreativeDetailMaterialBo> textMaterialBos = Stream.of(
                        CollectionUtils.emptyIfNull(creativeDetailInfoBo.getTitleBos()).stream(),
                        CollectionUtils.emptyIfNull(creativeDetailInfoBo.getDescBos()).stream(),
                        CollectionUtils.emptyIfNull(creativeDetailInfoBo.getButtonBos()).stream(),
                        CollectionUtils.emptyIfNull(creativeDetailInfoBo.getReplaceUrlBo()).stream(),
                        CollectionUtils.emptyIfNull(creativeDetailInfoBo.getComponentTitleBos()).stream(),
                        CollectionUtils.emptyIfNull(creativeDetailInfoBo.getComponentDescBos()).stream()
//                        CollectionUtils.emptyIfNull(creativeDetailInfoBo.getShadowTitleBos()).stream()
                )
                .flatMap(Function.identity())
                .filter(Objects::nonNull) // 过滤掉可能为null的元素
                .collect(Collectors.toMap(
                        t -> t.getMaterialMd5() + "-" + t.getMaterialType(),
                        Function.identity(),
                        (existing, replacement) -> {
                            existing.getMaterialSourceSet().addAll(replacement.getMaterialSourceSet());
                            return existing;
                        } // 保留先出现的元素
                ))
                .values()
                .stream()
                .collect(Collectors.toList());

        // 图片处理:图片，story图片，框下图片，影子图片
        List<CreativeDetailMaterialBo> imageMaterialBos = Stream.of(
                        CollectionUtils.emptyIfNull(creativeDetailInfoBo.getImageBos()).stream(),
                        CollectionUtils.emptyIfNull(creativeDetailInfoBo.getComponentImageBos()).stream()
//                        CollectionUtils.emptyIfNull(creativeDetailInfoBo.getShadowImageBos()).stream()
                )
                .flatMap(Function.identity())
                .filter(Objects::nonNull) // 过滤掉可能为null的元素
                .collect(Collectors.toMap(
                        t -> t.getMaterialMd5() + "-" + t.getMaterialType(),
                        Function.identity(),
                        (existing, replacement) -> {
                            existing.getMaterialSourceSet().addAll(replacement.getMaterialSourceSet());
                            return existing;
                        } // 保留先出现的元素
                ))
                .values()
                .stream()
                .collect(Collectors.toList());

        // url: 落地页url, 程序化第三方落地页组, tab url, 替链url, 影子url, 影子落地页组url
        List<CreativeDetailMaterialBo> urlMaterialBos = Stream.of(
                        CollectionUtils.emptyIfNull(creativeDetailInfoBo.getThirdPartyLandingPageUrlBos()).stream(),
                        CollectionUtils.emptyIfNull(creativeDetailInfoBo.getThirdPartyPageGroupUrlBos()).stream(),
                        CollectionUtils.emptyIfNull(creativeDetailInfoBo.getTabUrlBos()).stream(),
                        CollectionUtils.emptyIfNull(creativeDetailInfoBo.getGameCardBos()).stream(),
                        CollectionUtils.emptyIfNull(creativeDetailInfoBo.getReplaceUrlBo()).stream(),
                        CollectionUtils.emptyIfNull(creativeDetailInfoBo.getThirdPartyLandingPageGroupBos()).stream(),
                        CollectionUtils.emptyIfNull(creativeDetailInfoBo.getUnderThirdPartyLandingPageUrlBos()).stream()
//                        safeStream(creativeDetailInfoBo.getShadowThirdPartyLandingPageUrlBo()),
//                        CollectionUtils.emptyIfNull(creativeDetailInfoBo.getShadowThirdPartyPageGroupUrlBos()).stream()
                )
                .flatMap(Function.identity())
                .filter(Objects::nonNull) // 过滤掉可能为null的元素
                .collect(Collectors.toMap(
                        t -> t.getMaterialMd5() + "-" + t.getMaterialType(),
                        Function.identity(),
                        (existing, replacement) -> {
                            existing.getMaterialSourceSet().addAll(replacement.getMaterialSourceSet());
                            return existing;
                        } // 保留先出现的元素
                ))
                .values()
                .stream()
                .collect(Collectors.toList());

        CreativeDetailInfoByMaterialTypeBo creativeDetailInfoByMaterialTypeBo = CreativeDetailInfoByMaterialTypeBo.builder()
                .creativeId(creativeDetailInfoBo.getCreativeId())
                .textBos(textMaterialBos)
                .imageBos(imageMaterialBos)
                .videoBos(creativeDetailInfoBo.getVideoBos())
                .liveRoomBos(creativeDetailInfoBo.getLiveRoomBos())
                .dynamicBos(creativeDetailInfoBo.getDynamicBos())
                .urlBos(urlMaterialBos)
                .build();

        // 创意下的素材队列去重合并
        List<CreativeDetailMaterialBo> mergedMaterialBos = Stream.of(
                        CollectionUtils.emptyIfNull(creativeDetailInfoByMaterialTypeBo.getTextBos()).stream(),
                        CollectionUtils.emptyIfNull(creativeDetailInfoByMaterialTypeBo.getImageBos()).stream(),
                        CollectionUtils.emptyIfNull(creativeDetailInfoByMaterialTypeBo.getVideoBos()).stream(),
                        CollectionUtils.emptyIfNull(creativeDetailInfoByMaterialTypeBo.getLiveRoomBos()).stream(),
                        CollectionUtils.emptyIfNull(creativeDetailInfoByMaterialTypeBo.getDynamicBos()).stream(),
                        CollectionUtils.emptyIfNull(creativeDetailInfoByMaterialTypeBo.getUrlBos()).stream()
                )
                .flatMap(Function.identity())
                .filter(Objects::nonNull) // 过滤掉可能为null的元素
                .collect(Collectors.toMap(
                        t -> t.getMaterialMd5() + "-" + t.getMaterialType(),
                        Function.identity(),
                        (existing, replacement) -> {
                            existing.getMaterialSourceSet().addAll(replacement.getMaterialSourceSet());
                            return existing;
                        } // 保留先出现的元素
                ))
                .values()
                .stream()
                .collect(Collectors.toList());
        return mergedMaterialBos;
    }

    // 自定义安全方法（兼容 Java 8）
    public static <T> Stream<T> safeStream(T obj) {
        return obj == null ? Stream.empty() : Stream.of(obj);
    }


    private void processMaterialsContent(List<CreativeDetailMaterialBo> materialList, int materialType) {
        if (!CollectionUtils.isEmpty(materialList)) {
            RiskMaterialTypeEnum typeEnum = RiskMaterialTypeEnum.getByCode(materialType);
            for (CreativeDetailMaterialBo materialBo : materialList) {
                materialBo.setMaterialType(materialType);
                Assert.isTrue(StringUtils.isNotEmpty(materialBo.getRawContent()), "raw content不能为空");

                // 非链接类型统一处理
                if (!RiskMaterialTypeEnum.LINK.equals(typeEnum)) {
                    String materialContent = typeEnum.generateMaterialContent(materialBo.getRawContent());
                    materialBo.setMaterialContent(materialContent);
                    // 图片的存在md5则不生成
                    if (RiskMaterialTypeEnum.IMAGE.equals(typeEnum) && StringUtils.isNotEmpty(materialBo.getMaterialMd5())) {

                    } else {
                        materialBo.setMaterialMd5(DigestUtils.md5Hex(materialContent));
                    }
                }
                // 链接类型需要单独的处理器解析
                else {
                    PromotionPurposeContentProcessorEnum processorEnum = PromotionPurposeContentProcessorEnum.searchProcessorEnum(materialBo.getRawContent());
                    String bodyId = processorEnum.getContextToMatch(materialBo.getRawContent());
                    // link-{subType}-{url}
                    String materialContent = "link" + "-" + processorEnum.getCode() + "-" + bodyId;
                    materialBo.setMaterialContent(materialContent);
                    materialBo.setMaterialMd5(DigestUtils.md5Hex(materialContent));
                }
            }
        }
    }

    private void processMaterialContent(Integer bizType, CreativeDetailMaterialBo materialBo, int materialType) {
        if (materialBo != null) {
            RiskMaterialTypeEnum typeEnum = RiskMaterialTypeEnum.getByCode(materialType);
            materialBo.setMaterialType(materialType);
            materialBo.setBizType(bizType);
            Assert.isTrue(StringUtils.isNotEmpty(materialBo.getRawContent()), "raw content不能为空");

            if (!RiskMaterialTypeEnum.LINK.equals(typeEnum)) {
                String materialContent = typeEnum.generateMaterialContent(materialBo.getRawContent());
                materialBo.setMaterialContent(materialContent);
                materialBo.setMaterialMd5(DigestUtils.md5Hex(materialContent));
            } else {
                PromotionPurposeContentProcessorEnum processorEnum = PromotionPurposeContentProcessorEnum.searchProcessorEnum(materialBo.getRawContent());
                String bodyId = processorEnum.getContextToMatch(materialBo.getRawContent());
                // link-{subType}-{url}
                String materialContent = "link" + "-" + processorEnum.getCode() + "-" + bodyId;
                materialBo.setMaterialContent(materialContent);
                materialBo.setMaterialMd5(DigestUtils.md5Hex(materialContent));
            }
        }
    }

    /**
     * 触发创意推审，如果需要的话
     *
     * @param creativeIds
     * @param msgBo
     */
    public void firePushAuditIfNeed(Set<Integer> creativeIds, OtherSourceFirePushAuditMsgBo msgBo) {
        // 拆素材送审的创意才需要处理
        // 拿锁判断是否要推审
        // 到这儿要判断 当前创意是否 是否需要推审
        //      1.在这儿能读到的，都要推审；
        //      2.如果并行中的版本，未读到：即先看锁是否存在，存在就并行，推审；不存在读到了版本就推审；

        // 1、先读取一个快照的创意 id 集合
        List<LauMaterialToAuditCreativePo> creativePoList = lauMaterialToAuditCreativeService
                .queryMaterialToAuditCreativeIds(Lists.newArrayList(creativeIds));

        Set<Integer> toAuditCreativeIds = creativePoList.stream().map(LauMaterialToAuditCreativePo::getCreativeId).collect(Collectors.toSet());
        // 2、id 都查得到就直接发消息；
        if (!creativeIds.equals(toAuditCreativeIds)) {
            for (Integer creativeId : creativeIds) {
                // 2.1 不存在先看是否在处理中，处理中可能未读到，直接推审，这种数据不带 eventTime，无论如何都触发一次同步
                if (!toAuditCreativeIds.contains(creativeId)) {
                    RLock lock = adpRedissonClient.getLock(RiskConstants.LOCK_CREATIVE_MATERIAL_PUSH_TO_AUDIT + creativeId);
                    if (lock.isLocked()) {
                        creativePoList.add(LauMaterialToAuditCreativePo.builder().creativeId(creativeId).build());
                        toAuditCreativeIds.add(creativeId);
                    }
                }
            }

            creativeIds.removeAll(toAuditCreativeIds);
            // 2.2 这里还没拿到，再查一遍，最后得到了，已查到&处理中的版本，还没查到，就真没了
            if (CollectionUtils.isNotEmpty(creativeIds)) {
                List<LauMaterialToAuditCreativePo> newList = lauMaterialToAuditCreativeService
                        .queryMaterialToAuditCreativeIds(Lists.newArrayList(creativeIds));
                if (CollectionUtils.isNotEmpty(newList)) {
                    creativePoList.addAll(newList);
                }
            }
        }

        // 3、发消息
        for (LauMaterialToAuditCreativePo lauMaterialToAuditCreativePo : creativePoList) {
            msgBo.setCreativeId(lauMaterialToAuditCreativePo.getCreativeId());
            msgBo.setEventTime(lauMaterialToAuditCreativePo.getEventTime());
            otherSourceFirePushAuditPub.pub(msgBo);
        }
    }

    /**
     * 根据任务数据推审
     *
     * @param msg
     */
    public void pushAuditByTask(OtherSourceFirePushAuditMsgBo msg) throws Exception {
        if (FirePushAuditSourceEnum.STORY != FirePushAuditSourceEnum.getByCode(msg.getSource())
                && FirePushAuditSourceEnum.PAGE_GROUP != FirePushAuditSourceEnum.getByCode(msg.getSource())) {
            return;
        }

        RLock lock = adpRedissonClient.getLock(RiskConstants.LOCK_CREATIVE_MATERIAL_PUSH_TO_AUDIT + msg.getCreativeId());
        try {
            if (lock.tryLock(3, 10, TimeUnit.SECONDS)) {
                // 1、根据任务拉取之前的素材
                List<LauMaterialAuditTaskPo> tasks = materialAuditTaskDbService.queryListByCreativeId(msg.getCreativeId(), IsDeleted.VALID.getCode());
                List<CreativeDetailMaterialBo> exists = IMaterialAuditConvertor.INSTANCE.toCreativeMaterialBos(tasks);
                // 1.2 md5+type -> bo
                Map<String, CreativeDetailMaterialBo> existsMap = exists.stream().collect(
                        Collectors.toMap(e -> MaterialTaskUtils.genKey(e.getMaterialMd5(), e.getMaterialType()), Function.identity()));

                CreativeDetailInfoBo creativeDetailInfoBo = CreativeDetailInfoBo.builder().creativeId(msg.getCreativeId())
                        .componentList(Lists.newArrayList(ComponentMsgBo.builder().component_id(msg.getComponentId()).component_type(msg.getComponentType()).build()))
                        .pageGroupId(msg.getPageGroupId()).build();

                // 2、分场景合并数据：story 查询所有 story 覆盖当前 story任务； pageGroup 查询当前 pageGroup 覆盖当前 pageGroup 任务（创意只会存在一个 pageGroup）
                if (FirePushAuditSourceEnum.STORY == FirePushAuditSourceEnum.getByCode(msg.getSource())) {
                    boolean changed = false;

                    queryAndFillComponentMaterialsIfNecessary(creativeDetailInfoBo);
                    processAllMaterialsContent(creativeDetailInfoBo);
                    List<CreativeDetailMaterialBo> componentMaterialBos = splitAndMergeMaterials(creativeDetailInfoBo);
                    // 2.1 md5+type -> bo
                    Map<String, CreativeDetailMaterialBo> componentMap = componentMaterialBos.stream().collect(
                            Collectors.toMap(e -> MaterialTaskUtils.genKey(e.getMaterialMd5(), e.getMaterialType()), Function.identity()));

                    Iterator<Map.Entry<String, CreativeDetailMaterialBo>> iterator = existsMap.entrySet().iterator();
                    while (iterator.hasNext()) {
                        Map.Entry<String, CreativeDetailMaterialBo> entry = iterator.next();

                        // 2.2 story 的素材已经存在，不修改
                        if (null != componentMap.remove(entry.getKey())) {
                            continue;
                        }

                        // 2.3 story 的素材仅存在于当前版本，删除掉
                        if (CollectionUtils.isNotEmpty(entry.getValue().getMaterialSourceSet())
                                && entry.getValue().getMaterialSourceSet().remove(MaterialSourceEnum.STORY.getCode())
                                && CollectionUtils.isEmpty(entry.getValue().getMaterialSourceSet())) {
                            changed = true;
                            iterator.remove();
                        }
                    }

                    if (MapUtils.isNotEmpty(componentMap)) {
                        changed = true;
                        // 2.4 story 的素材不存在于当前版本，添加到素材列表
                        existsMap.putAll(componentMap);
                    }

                    if (!changed) {
                        log.info("processCreativeMaterialPushToAudit[推审,story素材没有新增的], creativeId={}", msg.getCreativeId());
                        return;
                    }
                } else {
                    // 查询落地页组的落地页并填充
                    queryAndFillThirdPartyLandingPageGroup(creativeDetailInfoBo);

                    processAllMaterialsContent(creativeDetailInfoBo);
                    List<CreativeDetailMaterialBo> componentMaterialBos = splitAndMergeMaterials(creativeDetailInfoBo);

                    // 3.1 md5+type -> bo
                    Map<String, CreativeDetailMaterialBo> pageGroupMap = componentMaterialBos.stream().collect(
                            Collectors.toMap(e -> MaterialTaskUtils.genKey(e.getMaterialMd5(), e.getMaterialType()), Function.identity()));

                    Iterator<Map.Entry<String, CreativeDetailMaterialBo>> iterator = existsMap.entrySet().iterator();
                    while (iterator.hasNext()) {
                        Map.Entry<String, CreativeDetailMaterialBo> entry = iterator.next();

                        if (RiskMaterialTypeEnum.PAGE_GROUP_PAGE != RiskMaterialTypeEnum.getByCode(entry.getValue().getMaterialType())) {
                            continue;
                        }

                        // 3.2 pageGroup 的素材已经存在，不修改
                        if (null != pageGroupMap.remove(entry.getKey())) {
                            continue;
                        }

                        Long groupId = RiskMaterialTypeEnum.getPageGroupId(entry.getValue().getMaterialContent());

                        // 3.3 pageGroup 的素材仅存在于当前版本，删除掉
                        if (Objects.equals(msg.getPageGroupId(), groupId)) {
                            iterator.remove();
                        }
                    }

                    if (MapUtils.isNotEmpty(pageGroupMap)) {
                        // 2.4 pageGroup 的素材不存在于当前版本，添加到素材列表
                        existsMap.putAll(pageGroupMap);
                    }
                }

                // 每个创意的组件触发一次推审，构建全量的素材列表
                sync2MaterialTables(msg.getCreativeId(), msg.getEventTime(), null, Lists.newArrayList(existsMap.values()));
            } else {
                throw new RuntimeException("story || 落地页组 触发创意推审，获取锁失败");
            }
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }
}
