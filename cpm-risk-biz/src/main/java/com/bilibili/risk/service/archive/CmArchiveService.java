package com.bilibili.risk.service.archive;

import com.bapis.ad.archive.ArchiveQueryReq;
import com.bapis.ad.archive.CmArchiveFullInfo;
import com.bapis.ad.archive.CmArchiveFullInfoResp;
import com.bapis.ad.archive.CmArchiveServiceGrpc;
import com.bilibili.risk.bo.CmArchiveBo;
import com.bilibili.risk.config.RedisConfig;
import com.bilibili.risk.convertor.IArchiveConvertor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import pleiades.venus.starter.rpc.client.RPCClient;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CmArchiveService {

    private static final String CM_ARCHIVE_CACHE_KEY_PREFIX = "risk:archive:cm_archive:";
    private static final long CACHE_EXPIRE_TIME = 5; // 缓存过期时间，单位分钟

    @RPCClient("sycpb.cpm.scv")
    private CmArchiveServiceGrpc.CmArchiveServiceBlockingStub cmArchiveServiceBlockingStub;

    @Resource(name = RedisConfig.ADP_CLUSTER)
    private RedissonClient adpRedissonClient;

    /**
     * 查询CM稿件信息，使用Redis缓存优化
     *
     * @param aids 稿件ID列表
     * @return 稿件ID到CM稿件信息的映射
     */
    public Map<Long, CmArchiveBo> queryCmArchiveBoMap(List<Long> aids) {
        if (CollectionUtils.isEmpty(aids)) {
            return Collections.emptyMap();
        }

        // 1. 创建结果Map
        Map<Long, CmArchiveBo> resultMap = new HashMap<>();
        // 2. 需要从RPC获取的ID列表
        List<Long> needFetchAids = new ArrayList<>();

        // 3. 尝试从Redis缓存中获取
        for (Long aid : aids) {
            String cacheKey = CM_ARCHIVE_CACHE_KEY_PREFIX + aid;
            RBucket<CmArchiveBo> bucket = adpRedissonClient.getBucket(cacheKey);
            CmArchiveBo cachedData = bucket.get();

            if (cachedData != null) {
                // 缓存命中，添加到结果Map
                resultMap.put(aid, cachedData);
                log.debug("从缓存获取CM稿件信息 aid={}", aid);
            } else {
                // 缓存未命中，添加到需要获取的列表
                needFetchAids.add(aid);
            }
        }

        // 4. 如果所有数据都从缓存获取到了，直接返回
        if (needFetchAids.isEmpty()) {
            log.info("所有CM稿件信息从缓存获取成功 aids={}", aids);
            return resultMap;
        }

        // 5. 从RPC获取未缓存的数据
        log.info("从RPC获取CM稿件信息 aids={}", needFetchAids);
        Map<Long, CmArchiveBo> rpcResultMap = queryCmArchiveBoMapByRpc(needFetchAids);

        // 6. 将RPC获取的数据添加到结果Map并缓存
        for (Map.Entry<Long, CmArchiveBo> entry : rpcResultMap.entrySet()) {
            Long aid = entry.getKey();
            CmArchiveBo cmArchiveBo = entry.getValue();
            resultMap.put(aid, cmArchiveBo);

            // 缓存数据
            String cacheKey = CM_ARCHIVE_CACHE_KEY_PREFIX + aid;
            RBucket<CmArchiveBo> bucket = adpRedissonClient.getBucket(cacheKey);
            bucket.set(cmArchiveBo, CACHE_EXPIRE_TIME, TimeUnit.MINUTES);
            log.debug("CM稿件信息已缓存 aid={}, 过期时间={}分钟", aid, CACHE_EXPIRE_TIME);
        }

        return resultMap;
    }

    /**
     * 通过RPC调用获取CM稿件信息（原始实现逻辑）
     *
     * @param aids 稿件ID列表
     * @return 稿件ID到CM稿件信息的映射
     */
    private Map<Long, CmArchiveBo> queryCmArchiveBoMapByRpc(List<Long> aids) {
        if (CollectionUtils.isEmpty(aids)) {
            return Collections.emptyMap();
        }

        ArchiveQueryReq archiveQueryReq = ArchiveQueryReq.newBuilder().addAllAvids(aids).build();
        CmArchiveFullInfoResp cmArchiveFullInfoResp = cmArchiveServiceBlockingStub
                .withDeadlineAfter(5000L, TimeUnit.MILLISECONDS)
                .queryCmArchiveFullInfoByAvid(archiveQueryReq);
        Map<Long, CmArchiveFullInfo> cmArchiveFullInfoMap = cmArchiveFullInfoResp.getResultMap();
        if (CollectionUtils.isEmpty(cmArchiveFullInfoMap)) {
            return Collections.emptyMap();
        }
        List<CmArchiveBo> cmArchiveBos = IArchiveConvertor.MAPPER.grpc2Bos(cmArchiveFullInfoMap.values());
        return cmArchiveBos.stream().collect(Collectors.toMap(CmArchiveBo::getAvid, cmArchiveBo -> cmArchiveBo, (v1, v2) -> v2));
    }

    /**
     * 清除CM稿件缓存
     * 当稿件信息发生变化时可调用此方法清除特定稿件的缓存
     *
     * @param aid 稿件ID
     */
    public void clearCmArchiveCache(Long aid) {
        if (aid == null) {
            return;
        }
        String cacheKey = CM_ARCHIVE_CACHE_KEY_PREFIX + aid;
        RBucket<CmArchiveBo> bucket = adpRedissonClient.getBucket(cacheKey);
        bucket.delete();
        log.info("CM稿件缓存已清除 aid={}", aid);
    }

    /**
     * 批量清除CM稿件缓存
     *
     * @param aids 稿件ID列表
     */
    public void clearCmArchiveCache(List<Long> aids) {
        if (CollectionUtils.isEmpty(aids)) {
            return;
        }
        for (Long aid : aids) {
            clearCmArchiveCache(aid);
        }
        log.info("批量清除CM稿件缓存完成 aids={}", aids);
    }

}
