package com.bilibili.risk.service.archive;

import com.bapis.ad.archive.ArchiveQueryReq;
import com.bapis.ad.archive.CmArchiveFullInfo;
import com.bapis.ad.archive.CmArchiveFullInfoResp;
import com.bapis.ad.archive.CmArchiveServiceGrpc;
import com.bilibili.risk.bo.CmArchiveBo;
import com.bilibili.risk.convertor.IArchiveConvertor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import pleiades.venus.starter.rpc.client.RPCClient;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CmArchiveService {

    @RPCClient("sycpb.cpm.scv")
    private CmArchiveServiceGrpc.CmArchiveServiceBlockingStub cmArchiveServiceBlockingStub;

    public Map<Long, CmArchiveBo> queryCmArchiveBoMap(List<Long> aids) {
        if (CollectionUtils.isEmpty(aids)) {
            return Collections.emptyMap();
        }

        ArchiveQueryReq archiveQueryReq = ArchiveQueryReq.newBuilder().addAllAvids(aids).build();
        CmArchiveFullInfoResp cmArchiveFullInfoResp = cmArchiveServiceBlockingStub
                .withDeadlineAfter(5000L, TimeUnit.MILLISECONDS)
                .queryCmArchiveFullInfoByAvid(archiveQueryReq);
        Map<Long, CmArchiveFullInfo> cmArchiveFullInfoMap = cmArchiveFullInfoResp.getResultMap();
        if (CollectionUtils.isEmpty(cmArchiveFullInfoMap)) {
            return Collections.emptyMap();
        }
        List<CmArchiveBo> cmArchiveBos = IArchiveConvertor.MAPPER.grpc2Bos(cmArchiveFullInfoMap.values());
        return cmArchiveBos.stream().collect(Collectors.toMap(CmArchiveBo::getAvid, cmArchiveBo -> cmArchiveBo, (v1, v2) -> v2));
    }

}
