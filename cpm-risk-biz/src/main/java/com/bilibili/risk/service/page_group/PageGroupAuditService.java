package com.bilibili.risk.service.page_group;

import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.risk.bo.LandingPageGroupBo;
import com.bilibili.risk.bo.LandingPageGroupMappingBo;
import com.bilibili.risk.bo.LauMaterialAuditBo;
import com.bilibili.risk.bo.msg.CreativeMaterialJudgeAuditMsgBo;
import com.bilibili.risk.constant.RiskConstants;
import com.bilibili.risk.convertor.IMaterialAuditConvertor;
import com.bilibili.risk.enums.*;
import com.bilibili.risk.po.risk.LauMaterialAuditPo;
import com.bilibili.risk.po.risk.LauMaterialAuditTaskPo;
import com.bilibili.risk.po.risk.LauMaterialCreativeRelPo;
import com.bilibili.risk.service.material.LauMaterialAuditService;
import com.bilibili.risk.service.material_creative.MaterialCreativeRelService;
import com.bilibili.risk.service.task.MaterialAuditTaskDbService;
import com.bilibili.risk.utils.MaterialTaskUtils;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import com.bilibili.risk.databus.material_audit.OneCreativeJudgeAuditPub;

@Slf4j
@Service
@RequiredArgsConstructor
public class PageGroupAuditService {

    private final LauMaterialAuditService lauMaterialAuditService;
    private final MaterialAuditTaskDbService materialAuditTaskDbService;
    private final MaterialCreativeRelService materialCreativeRelService;
    private final OneCreativeJudgeAuditPub oneCreativeJudgeAuditPub;
    private final RedissonClient adpRedissonClient;
    private final PageGroupService pageGroupService;

    public void auditLandingPageGroupMaterial(Long pageId, Long pageGroupId) throws Exception{
        log.info("auditLandingPageGroupMaterial[落地页组mapping] pageId: {}, pageGroupId: {}", pageId, pageGroupId);
        if (!Utils.isPositive(pageId) || !Utils.isPositive(pageGroupId)) {
            return;
        }

        // 查找素材
        String materialContent = RiskMaterialTypeEnum.splitPageGroupPageMaterialContent(RiskMaterialTypeEnum.splitPageGroupBodyId(pageGroupId, pageId));
        String md5 = DigestUtils.md5Hex(materialContent);

        LauMaterialAuditBo materialBo = null;
        RLock lock = adpRedissonClient.getLock(RiskConstants.PAGE_GROUP_STATUS_SYNC + pageGroupId);
        try {
            if(lock.tryLock(3,5, TimeUnit.SECONDS)){
                LandingPageGroupBo groupBo = pageGroupService.fetchPageGroupBo(pageGroupId);
                LandingPageGroupMappingBo mappingBo = null;
                if(!CollectionUtils.isEmpty(groupBo.getMappingBos())){
                    mappingBo = groupBo.getMappingBos().stream().filter(e -> pageId.equals(e.getPageId())).findAny().orElse(null);
                }
                if(null == mappingBo || !CreativeAuditStatusEnum.getCompletedStatus().contains(mappingBo.getStatus())){
                    return;
                }

                materialBo = lauMaterialAuditService.fetchLauMaterialAuditBosByMd5(md5, RiskMaterialTypeEnum.PAGE_GROUP_PAGE.getCode());

                if(null == materialBo){
                    // 素材就不存在，不需要触发审核
                    return;
                }

                CreativeAuditStatusEnum statusEnum = CreativeAuditStatusEnum.getByCode(mappingBo.getStatus()).orElse(null);
                AuditLabelIdSpecialEnum labelIdEnum = AuditLabelIdSpecialEnum.getByGroupPageStatus(statusEnum);
                String labelId = (null == labelIdEnum ? null : labelIdEnum.getCode());

                // 相同不处理
                if(Objects.equals(labelId, materialBo.getAuditLabelThirdId())){
                    return;
                }

                LauMaterialAuditBo materialAuditBo = LauMaterialAuditBo.builder()
                        .materialId(materialBo.getMaterialId())
                        .auditLabelThirdId(labelId)
                        .reason(mappingBo.getReason())
                        .build();
                LauMaterialAuditPo materialAuditPo = IMaterialAuditConvertor.INSTANCE.bo2po2(materialAuditBo);
                lauMaterialAuditService.updateMaterialAuditPo(materialAuditPo);
                materialBo.setAuditLabelThirdId(labelId);
            } else {
                throw new RuntimeException("落地页组审核状态变更，修改状态，获取锁失败");
            }
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

        // 滚动查询素材对应的创意
        List<LauMaterialCreativeRelPo> creativeRelPos;
        Integer lastCreativeId = null;
        // 没有的话，随便找个任务，做一样的操作
        do {
            creativeRelPos = materialCreativeRelService.scrollQuery(materialBo.getMaterialId(), lastCreativeId, 100, IsDeleted.VALID.getCode());

            if (CollectionUtils.isEmpty(creativeRelPos)) {
                break;
            }

            List<Integer> creativeIds = creativeRelPos.stream().map(t -> t.getCreativeId()).distinct().collect(Collectors.toList());
            lastCreativeId = creativeRelPos.get(creativeRelPos.size() - 1).getCreativeId();

            Map<Integer, List<Integer>> shardingCreativeIdMap = creativeIds.stream()
                    .collect(Collectors.groupingBy(MaterialTaskUtils::calculateShardingKeyByCreativeId));

            List<LauMaterialAuditTaskPo> auditTaskPos = Lists.newArrayList();
            for (Map.Entry<Integer, List<Integer>> entry : shardingCreativeIdMap.entrySet()) {
                List<LauMaterialAuditTaskPo> taskPos = materialAuditTaskDbService.queryTasksByCreativeIds(entry.getValue(), materialBo.getMaterialId(), null, IsDeleted.VALID.getCode());
                if(!CollectionUtils.isEmpty(taskPos)) {
                    auditTaskPos.addAll(taskPos);
                }
            }

            if (CollectionUtils.isEmpty(auditTaskPos)) {
                continue;
            }

            // 发消息，处理素材下所有任务
            for (LauMaterialAuditTaskPo auditTaskPo : auditTaskPos) {
                CreativeMaterialJudgeAuditMsgBo msgBo = CreativeMaterialJudgeAuditMsgBo.builder()
                        .creativeId(auditTaskPo.getCreativeId()).materialId(auditTaskPo.getMaterialId())
                        .needProcessTask(1)
                        .executeName(RiskConstants.SYSTEM_USERNAME).build();

                oneCreativeJudgeAuditPub.pub(msgBo);
            }

        } while (!CollectionUtils.isEmpty(creativeRelPos) && creativeRelPos.size() == 100);
    }
}
