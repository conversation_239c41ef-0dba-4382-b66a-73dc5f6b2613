package com.bilibili.risk.service.task;

import com.bapis.ad.mgk.page.group.PageGroupMappingStatus;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.risk.bo.LandingPageGroupBo;
import com.bilibili.risk.bo.LandingPageGroupMappingBo;
import com.bilibili.risk.constant.RiskConstants;
import com.bilibili.risk.enums.MaterialTaskStatusEnum;
import com.bilibili.risk.enums.RiskMaterialTypeEnum;
import com.bilibili.risk.po.risk.LauMaterialAuditTaskPo;
import com.bilibili.risk.service.page_group.PageGroupAuditService;
import com.bilibili.risk.service.page_group.PageGroupMaterialSyncService;
import com.bilibili.risk.service.page_group.PageGroupService;
import com.bilibili.risk.utils.MaterialTaskUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 处理落地页rpc调用兜底处理超时的服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LandingPageTaskRpcCallTimeoutService {

    private final MaterialAuditTaskDbService materialAuditTaskDbService;
    private final PageGroupAuditService pageGroupAuditService;

    public void processTimeoutCallTasks(List<String> taskIds) throws Exception{
        log.info("process landing page task rpc call timeout[落地页rpc调用兜底处理], taskIds:{}", taskIds);
        if (CollectionUtils.isEmpty(taskIds)) {
            log.warn("process landing page task rpc call timeout[落地页rpc调用兜底处理], taskIds is empty");
            return;
        }

        // 获取任务db数据
        List<LauMaterialAuditTaskPo> auditTaskPos = materialAuditTaskDbService.queryListByTaskIds(taskIds);
        if (auditTaskPos.isEmpty()) {
            log.warn("process landing page task rpc call timeout[落地页rpc调用兜底处理], task not exist, taskIds:{}", taskIds);
            return;
        }

        // 如果非中间状态，则处理过了
        List<LauMaterialAuditTaskPo> toCallbackAuditStatusPos = auditTaskPos.stream().filter(t -> Objects.equals(t.getStatus(), MaterialTaskStatusEnum.TO_CALLBACK_AUDIT.getCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(toCallbackAuditStatusPos)) {
            log.info("process landing page task rpc call timeout[落地页rpc调用兜底处理], no TO_CALLBACK_AUDIT status[正常], taskIds:{}", taskIds);
            return;
        }

        // 补偿
        // 返回三方落地页的 url
        // pageId url:格式: pageGroupPage-{groupId}-{pageId}

        for (LauMaterialAuditTaskPo toCallbackAuditStatusPo : toCallbackAuditStatusPos) {
            Long groupId = MaterialTaskUtils.parsePageGroupId(toCallbackAuditStatusPo.getMaterialContent());
            Long pageId =MaterialTaskUtils.parseGroupPageId(toCallbackAuditStatusPo.getMaterialContent());
            if (!Utils.isPositive(groupId) || !Utils.isPositive(pageId)) {
                log.error("process landing page task rpc call timeout[落地页rpc调用兜底处理], groupId not positive, pageId:{}, groupId:{}", pageId, groupId);
                continue;
            }
            pageGroupAuditService.auditLandingPageGroupMaterial(pageId, groupId);
        }

    }
}
