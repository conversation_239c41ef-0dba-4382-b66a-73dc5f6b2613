package com.bilibili.risk.service.task;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.risk.bo.LauMaterialAuditQueueBo;
import com.bilibili.risk.bo.LauMaterialAuditTaskUpdateBo;
import com.bilibili.risk.bo.MaterialAuditTaskOperationLogBo;
import com.bilibili.risk.bo.TaskTimeoutCheckResultBo;
import com.bilibili.risk.bo.msg.MaterialTaskTimeoutMsgBo;
import com.bilibili.risk.config.BizConfig;
import com.bilibili.risk.constant.DatabaseConstant;
import com.bilibili.risk.constant.RiskConstants;
import com.bilibili.risk.convertor.IMaterialAuditTaskConvertor;
import com.bilibili.risk.enums.LogTypeEnum;
import com.bilibili.risk.enums.MaterialTaskStatusEnum;
import com.bilibili.risk.enums.OperationTypeEnum;
import com.bilibili.risk.po.es.LauMaterialAuditTaskEsPo;
import com.bilibili.risk.po.risk.LauMaterialAuditTaskPo;
import com.bilibili.risk.po.risk.RiskMaterialTaskDoingPo;
import com.bilibili.risk.service.log.OperationLogService;
import com.bilibili.risk.service.metrics.EnvService;
import com.bilibili.risk.service.queue.MaterialAuditQueueService;
import com.bilibili.risk.service.task.pull.RiskMaterialTaskDoingService;
import com.bilibili.risk.service.wechat.WechatService;
import com.bilibili.risk.utils.DiffLogUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class MaterialAuditTaskTimeoutService {

    private final MaterialAuditTaskEsService materialAuditTaskEsService;
    private final MaterialAuditTaskDbService materialAuditTaskDbService;
    private final OperationLogService operationLogService;
    private final WechatService wechatService;
    private final MaterialAuditQueueService materialAuditQueueService;
    private final EnvService envService;
    private final BizConfig bizConfig;
    private final RiskMaterialTaskDoingService riskMaterialTaskDoingService;

    @Transactional(rollbackFor = Exception.class, transactionManager = DatabaseConstant.RISK_TRANSACTION_MANAGER)
    public void processTimeoutTask(MaterialTaskTimeoutMsgBo msgBo) {
        log.info("processTimeoutTask[任务超时处理], msgBo={}", msgBo);
        // 根据batchNo获取未完成的任务
        List<LauMaterialAuditTaskEsPo> auditTaskEsPos = materialAuditTaskEsService.queryNotCompletedMaterialAuditTaskEsPosByBatchNo(msgBo.getBatchNo());
        if (CollectionUtils.isEmpty(auditTaskEsPos)) {
            log.info("processTimeoutTask[任务超时处理], auditTaskEsPos is empty, msgBo={}", msgBo);
            return;
        }

        List<String> taskIds = auditTaskEsPos.stream().map(LauMaterialAuditTaskEsPo::getId).collect(Collectors.toList());

        // 查询 db
        List<LauMaterialAuditTaskPo> auditTaskPos = materialAuditTaskDbService.queryListByTaskIds(taskIds);
        if (CollectionUtils.isEmpty(auditTaskPos)) {
            log.info("processTimeoutTask[任务超时处理], auditTaskPos is empty, msgBo={}, taskIds={}", msgBo, taskIds);
            return;
        }

        // 释放任务
        LauMaterialAuditTaskPo auditTaskPo0 = auditTaskPos.get(0);
        LauMaterialAuditQueueBo auditQueueBo = materialAuditQueueService.fetchQueueBo(auditTaskPo0.getQueueId());
        String queueName = auditQueueBo != null ? auditQueueBo.getName() : "";

        List<LauMaterialAuditTaskUpdateBo> updateBos = new ArrayList<>();
        List<MaterialAuditTaskOperationLogBo> logBos = new ArrayList<>();
        for (LauMaterialAuditTaskPo auditTaskPo : auditTaskPos) {
            // 要求是进行中才需要处理
            if (!Objects.equals(auditTaskPo.getStatus(), MaterialTaskStatusEnum.DOING.getCode())) {
                log.info("processTimeoutTask[任务超时处理], task is not doing[不需要处理], taskId={}, status={}", auditTaskPo.getTaskId(), auditTaskPo.getStatus());
                continue;
            }

            String oldAcceptName = auditTaskPo.getAcceptName();
            LauMaterialAuditTaskUpdateBo updateBo = LauMaterialAuditTaskUpdateBo.builder().taskId(auditTaskPo.getTaskId()).acceptName("")
                    // 时间设置为 1900-01-01 00:00:00
                    .acceptTime(RiskConstants.BUSINESS_EARLIEST_TIMESTAMP).receiveBatchNo("").status(MaterialTaskStatusEnum.FREE.getCode()).version(auditTaskPo.getVersion()).build();
            updateBos.add(updateBo);

            LauMaterialAuditTaskUpdateBo oldUpdateBo = IMaterialAuditTaskConvertor.INSTANCE.po2UpdateBo(auditTaskPo);
            String diffLogStr = DiffLogUtils.generateDiffField(oldUpdateBo, updateBo, "任务对象");

            log.info("processTimeoutTask[超时自动释放], taskId={}, oldAcceptName={}, 当时batchNo={}", auditTaskPo.getTaskId(), oldAcceptName, auditTaskPo.getReceiveBatchNo());
            MaterialAuditTaskOperationLogBo logBo = MaterialAuditTaskOperationLogBo.builder().objId(updateBo.getTaskId()).operationType(OperationTypeEnum.TIMEOUT.getCode()).ctime(System.currentTimeMillis())
                    // 操作人记录当时的领取人
                    .operatorUsername(oldAcceptName).value("任务超时释放，超时时间: " + msgBo.getTimeoutMins() + "分钟" + "，当时的领取人: " + oldAcceptName).remark(JSON.toJSONString(updateBo)).diff(diffLogStr).type(LogTypeEnum.TASK.getCode()).systemType(RiskConstants.SYSTEM_TYPE_RISK).build();
            logBos.add(logBo);
        }

        riskMaterialTaskDoingService.deleteByTaskIds(taskIds);

        if (!CollectionUtils.isEmpty(updateBos)) {
            materialAuditTaskDbService.updateMaterialAuditTaskBosOptimistic(updateBos);
            String msg = String.format("素材审核任务超时释放，批次号:%s, 队列:%s, 领取人:%s, 超时时间: %s分钟,领取时间: %s, 成功释放任务数量: %s, 可能需要释放任务ID: %s, env=%s", msgBo.getBatchNo(),
                    "id:" + auditTaskPo0.getQueueId() + ",name:" + queueName, auditTaskPo0.getAcceptName(), msgBo.getTimeoutMins(), auditTaskPo0.getAcceptTime(), updateBos.size(), taskIds, envService.getEnv());
            log.info("processTimeoutTask[通知], msg={}", msg);
            if (Utils.isPositive(bizConfig.getTimeoutWechatSendSwitch())) {
                wechatService.asyncSendMsg(msg);
            }
        }

        // 操作日志
        operationLogService.saveLogs(logBos);

    }

    /**
     * 检查是否超时
     *
     * @param auditTaskPo
     * @param auditQueueBo
     * @param username
     * @return
     */
    public static boolean checkIfTimeout(LauMaterialAuditTaskPo auditTaskPo, LauMaterialAuditQueueBo auditQueueBo, String username) {
        log.info("checkIfTimeout, taskId={},status={},acceptName={},acceptTime={},username={}", auditTaskPo.getTaskId(), auditTaskPo.getStatus(), auditTaskPo.getAcceptName(), auditTaskPo.getAcceptTime(), username);
        // 非doing状态，则超时
        if (!auditTaskPo.getStatus().equals(MaterialTaskStatusEnum.DOING.getCode())) {
            log.info("checkIfTimeout[非doing状态,故超时], taskId={}, status={}, acceptName={}, acceptTime={}, username={}", auditTaskPo.getTaskId(), auditTaskPo.getStatus(), auditTaskPo.getAcceptName(), auditTaskPo.getAcceptTime(), username);
            return true;
        }

        // doing状态，看领取人，别人则超时
        if (!auditTaskPo.getAcceptName().equals(username)) {
            log.info("checkIfTimeout[doing状态,领取人别人,故超时], taskId={}, status={}, acceptName={}, acceptTime={}, username={}", auditTaskPo.getTaskId(), auditTaskPo.getStatus(), auditTaskPo.getAcceptName(), auditTaskPo.getAcceptTime(), username);
            return true;
        }

        // doing状态，领取人自己，看是否超时
        return auditQueueBo != null && auditTaskPo.getAcceptTime() != null && auditTaskPo.getAcceptTime().getTime() + auditQueueBo.getTaskTimeout() * 60 * 1000 < System.currentTimeMillis();
    }

    /**
     * 前端主动检查这批任务是否超时
     * 规则：前端传任务ids和批次号，如果前端的批次号与表中的不一致，则说明超时了
     *
     * @param receiveBatchNo
     * @param taskIds
     * @return
     */
    public TaskTimeoutCheckResultBo checkTaskIfTimeout(String receiveBatchNo, List<String> taskIds, String username) {
        log.info("checkTaskIfTimeout, receiveBatchNo={},taskIds={}", receiveBatchNo, taskIds);

        if (CollectionUtils.isEmpty(taskIds) && StringUtils.isEmpty(receiveBatchNo)) {
            return TaskTimeoutCheckResultBo.builder().isTimeout(false).build();
        }

        // 查询任务
        List<LauMaterialAuditTaskPo> auditTaskPos = materialAuditTaskDbService.queryListByTaskIds(taskIds);
        if (CollectionUtils.isEmpty(auditTaskPos)) {
            log.info("checkTaskIfTimeout[没有任务,不超时], taskIds={}", taskIds);
            return TaskTimeoutCheckResultBo.builder().isTimeout(false).build();
        }

        // 检查批次号是否一致，不一样说明超时了
        LauMaterialAuditTaskPo auditTaskPo0 = auditTaskPos.get(0);
        if (!StringUtils.isEmpty(receiveBatchNo)) {
            return TaskTimeoutCheckResultBo.builder().isTimeout(!auditTaskPo0.getReceiveBatchNo().equals(receiveBatchNo)).build();
        }

        // 兼容没有批次号情况
        List<Long> queueIds = auditTaskPos.stream().map(t -> t.getQueueId()).distinct().collect(Collectors.toList());
        // 获取队列
        List<LauMaterialAuditQueueBo> auditQueueBos = materialAuditQueueService.queryListByIds(queueIds);
        if (CollectionUtils.isEmpty(auditQueueBos)) {
            return TaskTimeoutCheckResultBo.builder().isTimeout(false).build();
        }
        Map<Long, LauMaterialAuditQueueBo> auditQueueBoMap = auditQueueBos.stream().collect(Collectors.toMap(t -> t.getId(), t -> t, (t1, t2) -> t1));
        for (LauMaterialAuditTaskPo auditTaskPo : auditTaskPos) {
            LauMaterialAuditQueueBo auditQueueBo = auditQueueBoMap.get(auditTaskPo.getQueueId());
            if (auditQueueBo == null) {
                continue;
            }

            // 检查是否超时
            if (MaterialAuditTaskTimeoutService.checkIfTimeout(auditTaskPo, auditQueueBo, username)) {
                return TaskTimeoutCheckResultBo.builder().isTimeout(true).build();
            }
        }
        return TaskTimeoutCheckResultBo.builder().isTimeout(false).build();
    }
}
