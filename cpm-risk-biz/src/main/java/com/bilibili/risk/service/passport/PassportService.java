package com.bilibili.risk.service.passport;

import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.risk.constant.HttpConstants;
import com.bilibili.risk.dto.AuthInfo;
import com.bilibili.risk.dto.AuthInfoDto;
import com.bilibili.risk.dto.PassportResponse;
import com.bilibili.risk.exception.ExceptionCode;
import com.bilibili.risk.utils.SignUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/11/16 11:08
 */
@Component
public class PassportService {

    private final static Logger LOGGER = LoggerFactory.getLogger(PassportService.class);

    @Value("${passport.auth.url}")
    private String AUTH_URL;

    @Autowired
    private PassportBaseRequestFactory passportBaseRequestFactory;
    @Autowired
    private SignUtil signUtil;
    @Autowired
    private OkHttpClient okHttpClient;
    private ObjectMapper objectMapper;

    public AuthInfoDto validateCookie(String cookies) throws ServiceException {

        PassportBaseRequest request = passportBaseRequestFactory.createPassportBaseRequestWithOutSign();
        request.setSign(signUtil.getSign(request));
        PassportResponse<AuthInfo> response = null;
        try {

            Call call = okHttpClient.newCall(new Request.Builder()
                    .url(AUTH_URL)
                    .header("Cookie", cookies)
                    .post(RequestBody.create(HttpConstants.JSON, objectMapper.writeValueAsBytes(request)))
                    .build());

            Response resp = call.execute();
            response = objectMapper.readValue(resp.body().bytes(), new TypeReference<PassportResponse<AuthInfo>>() {});
        } catch (Exception e) {
            LOGGER.error("userInfoHandler.doGet.error", e);
            throw new ServiceException(ExceptionCode.SYSTEM_ERROR);
        }
        if (null == response) {
            LOGGER.error("response is null");
            throw new ServiceException(ExceptionCode.SYSTEM_ERROR);
        }
        if (!PassportResponse.SUCCESS.equals(response.getCode())) {
            LOGGER.error("validateCookie.code{},message{}", response.getCode(), response.getMessage());
            throw new ServiceException(response.getCode(), response.getMessage());
        }
        AuthInfo authInfo = response.getData();

        return AuthInfoDto.builder()
                .csrfToken(authInfo.getCsrfToken())
                .expires(authInfo.getExpires())
                .mid(authInfo.getMid())
                .token(authInfo.getToken())
                .build();
    }

}
