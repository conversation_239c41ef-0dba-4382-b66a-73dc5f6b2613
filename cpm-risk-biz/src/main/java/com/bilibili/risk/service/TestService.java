package com.bilibili.risk.service;

import com.bapis.infra.service.sequence.*;
import com.bilibili.risk.config.BizConfig;
import com.bilibili.risk.config.RedisConfig;
import com.bilibili.risk.dao.risk.LauMaterialAuditRuleDao;
import com.bilibili.risk.po.risk.LauMaterialAuditRulePo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import pleiades.venus.starter.rpc.client.RPCClient;

import javax.annotation.Resource;

@Slf4j
@Service
@RequiredArgsConstructor
public class TestService {

    public static final String SEQUENCE_RISK_MATERIAL_TASK_ID = "risk_material_task_id";
    public static final String TOKEN = "a0a6e02b5aa4795d38510be8061c8647";
    @Autowired
    private LauMaterialAuditRuleDao lauMaterialAuditRuleDao;
    @Resource(name = RedisConfig.ADP_CLUSTER)
    private RedissonClient redissonClient;

    @RPCClient("main.common-arch.sequence")
    private SeqZoneGrpc.SeqZoneBlockingStub seqZoneBlockingStub;
    @Autowired
    private BizConfig bizConfig;

    public String testBizConfig() {
        log.info("testBizConfig, bizConfig={}", bizConfig);
        return bizConfig.toString();
    }

    public void testRedis() {
        RBucket<Object> bucket = redissonClient.getBucket("test", new StringCodec());
        log.info("bucket0:{}", bucket.get());

        redissonClient.getBucket("test", new StringCodec()).set("test");

        RBucket<Object> buckt1 = redissonClient.getBucket("test", new StringCodec());
        log.info("bucket1:{}", buckt1.get());
    }

    public void testMysql(Long id) {
        LauMaterialAuditRulePo lauMaterialAuditRulePo = lauMaterialAuditRuleDao.selectByPrimaryKey(id);
        log.info("lauMaterialAuditRulePo:{}", lauMaterialAuditRulePo);
    }

    public void testSnowFlakeNo1() {
        IDZoneReply idZoneReply = seqZoneBlockingStub.autoIncrement(BusinessZoneReq.newBuilder().setBizTag(SEQUENCE_RISK_MATERIAL_TASK_ID).setToken(TOKEN).build());
        long id = idZoneReply.getId();
        log.info("autoIncrement id:{}", id);
    }

    public void testSnowFlakeNo2() {

        SnowFlakeV2Reply snowFlakeV2Reply = seqZoneBlockingStub.snowFlakeV2(SnowFlakeV2Req.newBuilder().setBizTag(SEQUENCE_RISK_MATERIAL_TASK_ID).setBizToken(TOKEN).build());
        long id1 = snowFlakeV2Reply.getId();
        log.info("snowFlakeV2 id:{}", id1);

    }

    public void testSnowFlakeNo3() {

        IDZoneReply idZoneReply1 = seqZoneBlockingStub.snowFlake(BusinessZoneReq.newBuilder().setBizTag(SEQUENCE_RISK_MATERIAL_TASK_ID).setToken(TOKEN).build());
        log.info("snowFlake id:{}", idZoneReply1.getId());
    }

    public void testException() {
        int a = 1 / 0;
    }

}
