package com.bilibili.risk.service.refresh;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.risk.bo.CreativeDetailInfoBo;
import com.bilibili.risk.bo.CreativeDetailMaterialBo;
import com.bilibili.risk.bo.LauMaterialAuditBo;
import com.bilibili.risk.bo.msg.RawJumpUrlMsgBo;
import com.bilibili.risk.constant.RiskConstants;
import com.bilibili.risk.convertor.IMaterialAuditConvertor;
import com.bilibili.risk.dao.ad_core.LauCreativeExtraDao;
import com.bilibili.risk.dao.risk.LauMaterialAuditExtDao;
import com.bilibili.risk.dao.risk.LauMaterialCreativeRelDao;
import com.bilibili.risk.enums.MaterialBizTypeEnum;
import com.bilibili.risk.enums.MaterialSourceEnum;
import com.bilibili.risk.enums.PromotionPurposeContentProcessorEnum;
import com.bilibili.risk.enums.RiskMaterialTypeEnum;
import com.bilibili.risk.po.ad.LauCreativeExtraPo;
import com.bilibili.risk.po.ad.LauCreativeExtraPoExample;
import com.bilibili.risk.po.risk.*;
import com.bilibili.risk.service.creative.LauMaterialToAuditCreativeService;
import com.bilibili.risk.service.material.CreativeMaterialPushToAuditService;
import com.bilibili.risk.service.material_creative.MaterialCreativeRelService;
import com.bilibili.risk.service.task.MaterialAuditTaskDbService;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.elasticsearch.common.util.set.Sets;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class MiniAppMaterialRefresher {

    private final LauMaterialAuditExtDao lauMaterialAuditExtDao;
    private final MaterialCreativeRelService materialCreativeRelService;
    private final MaterialAuditTaskDbService materialAuditTaskDbService;
    private final LauCreativeExtraDao lauCreativeExtraDao;
    private final CreativeMaterialPushToAuditService creativeMaterialPushToAuditService;
    private final LauMaterialToAuditCreativeService lauMaterialToAuditCreativeService;
    private final RedissonClient adpRedissonClient;
    private final LauMaterialCreativeRelDao lauMaterialCreativeRelDao;

    public void refresh(Integer isUpdate, Integer pointShadingNo, List<String> materialIds, List<Integer> creativeIds, Integer needUpdateCount) {
        new Thread(() -> {
            try {
                doRefresh(isUpdate, pointShadingNo, materialIds, creativeIds, needUpdateCount);
            } catch (Exception e) {
                log.error("refresh[异常]", e);
            }
        }).start();
    }

    public List<LauMaterialCreativeRelPo> scrollQuery(String materialId, Integer lastCreativeId, List<Integer> creativeIds, int limit, Integer isDeleted) {
        LauMaterialCreativeRelPoExample example = new LauMaterialCreativeRelPoExample();
        LauMaterialCreativeRelPoExample.Criteria criteria = example.createCriteria().andMaterialIdEqualTo(materialId).andCreativeIdIn(creativeIds);
        if (Utils.isPositive(lastCreativeId)) {
            criteria.andCreativeIdGreaterThan(lastCreativeId);
        }
        if (isDeleted != null) {
            criteria.andIsDeletedEqualTo(isDeleted);
        }

        example.setLimit(limit);
        example.setOrderByClause("creative_id asc");

        return lauMaterialCreativeRelDao.selectByExample(example);
    }

    // com.bilibili.risk.service.refresh.MiniAppMaterialRefresher.doRefresh(0, 30, ["03000000000000000014421"])
    // 30分区
    public void doRefresh(Integer isUpdate, Integer pointShadingNo, List<String> materialIds, List<Integer> pointCreativeIds, Integer needUpdateCount) throws Exception {
        log.info("refresh[start], isUpdate: {},pointShadingNo={},materialIds={}", isUpdate, pointShadingNo, materialIds);

        long startTime = System.currentTimeMillis();
        final int MAX_SHARDING = 32; // 最大分片数

        // 遍历所有分片表
        Integer updateCount = 0;
        int endShadingNo = pointShadingNo != null ? pointShadingNo + 1 : MAX_SHARDING;
        int shadingNo = pointShadingNo != null ? pointShadingNo : 0;

        while (shadingNo < endShadingNo) {
            int shadingUpdateCount = 0;
            boolean hasData = true;

            // 分页查询当前分片的数据
            Long minId = 0L;
            while (hasData) {
                sleep();

                // 获取有问题的素材
                List<LauMaterialAuditPo> materialAuditPos = queryProblemMaterials(materialIds, minId, shadingNo);
                log.info("refresh[获取一批符合要求素材], shadingNo: {}, size={},minId={}", shadingNo, materialAuditPos.size(), minId);

                // 如果没有数据，处理下一个分片
                if (materialAuditPos == null || materialAuditPos.isEmpty()) {
                    hasData = false;
                    log.info("refresh[该页没有数据了], shadingNo: {}, updateCount: {}, shadingUpdateCount: {},minId={}", shadingNo, updateCount, shadingUpdateCount, minId);
                    break;
                }
                LauMaterialAuditPo lastPo = materialAuditPos.get(materialAuditPos.size() - 1);
                minId = lastPo.getId();

                // 处理每个素材
                for (LauMaterialAuditPo materialAuditPo : materialAuditPos) {
                    // 滚动查询素材的rel
                    List<LauMaterialCreativeRelPo> creativeRelPos;
                    Integer fromCreativeId = 0;
                    do {
                        if (CollectionUtils.isEmpty(pointCreativeIds)) {
                            creativeRelPos = materialCreativeRelService.scrollQuery(materialAuditPo.getMaterialId(), fromCreativeId, 100, IsDeleted.VALID.getCode());

                        } else {
                            creativeRelPos = this.scrollQuery(materialAuditPo.getMaterialId(), fromCreativeId, pointCreativeIds, 100, IsDeleted.VALID.getCode());
                        }

                        log.info("refresh[滚动查询素材的rel], materialId: {}, fromCreativeId: {}, size: {}", materialAuditPo.getMaterialId(), fromCreativeId, creativeRelPos.size());
                        if (CollectionUtils.isEmpty(creativeRelPos)) {
                            break;
                        }

                        List<Integer> creativeIds = creativeRelPos.stream().map(t -> t.getCreativeId()).distinct().collect(Collectors.toList());
                        Map<Integer, List<Integer>> creativeId_partitionMap = creativeRelPos.stream().map(LauMaterialCreativeRelPo::getCreativeId)
                                .collect(Collectors.groupingBy(t -> t));

                        LauCreativeExtraPoExample lauCreativeExtraPoExample = new LauCreativeExtraPoExample();
                        lauCreativeExtraPoExample.createCriteria().andCreativeIdIn(creativeIds);
                        List<LauCreativeExtraPo> creativeExtraPos = lauCreativeExtraDao.selectByExample(lauCreativeExtraPoExample);
                        Map<Integer, LauCreativeExtraPo> creativeExtraPoMap = creativeExtraPos.stream().collect(Collectors.toMap(t -> t.getCreativeId(), t -> t, (t1, t2) -> t1));

                        // key 是creativeId
                        for (Map.Entry<Integer, List<Integer>> entry : creativeId_partitionMap.entrySet()) {
                            Integer creativeId = entry.getKey();

                            List<LauMaterialToAuditCreativePo> toAuditCreativePos = lauMaterialToAuditCreativeService.queryMaterialToAuditCreativeIds(Arrays.asList(creativeId));
                            Map<Integer, LauMaterialToAuditCreativePo> toAuditCreativePoMap = toAuditCreativePos.stream().collect(Collectors.toMap(t -> t.getCreativeId(), t -> t, (t1, t2) -> t1));

                            LauMaterialToAuditCreativePo toAuditCreativePo = toAuditCreativePoMap.get(creativeId);
                            if (toAuditCreativePo == null) {
                                log.error("refresh[toAuditCreativePo不存在], creativeId: {}", creativeId);
                                continue;
                            }

                            if (Utils.isPositive(needUpdateCount)) {
                                if (shadingUpdateCount >= needUpdateCount) {
                                    log.info("refresh[达到需要更新的数量], shadingNo: {}, updateCount: {}, shadingUpdateCount: {}", shadingNo, updateCount, shadingUpdateCount);
                                    return;
                                }
                            }

                            // 创意维度锁
                            RLock lock = adpRedissonClient.getLock(RiskConstants.LOCK_CREATIVE_MATERIAL_PUSH_TO_AUDIT + creativeId);
                            try {
                                if (lock.tryLock(3, 10, TimeUnit.SECONDS)) {

                                    // 查询rel创意的所有的任务
                                    List<LauMaterialAuditTaskPo> taskPos = materialAuditTaskDbService.queryListByCreativeId(entry.getKey(), IsDeleted.VALID.getCode());
                                    log.info("refresh[查询rel创意的所有的任务], creativeId: {}, size: {}", entry.getKey(), taskPos.size());
                                    if (CollectionUtils.isEmpty(taskPos)) {
                                        log.error("refresh[任务不存在], creativeId: {}", entry.getKey());
                                        continue;
                                    }

                                    // 模拟推审
                                    // 构建某个创意推审素材列表
                                    List<CreativeDetailMaterialBo> existTaskMaterialBos = IMaterialAuditConvertor.INSTANCE.toCreativeMaterialBos(taskPos);

                                    // 去掉有问题素材，补新素材
                                    List<CreativeDetailMaterialBo> newMaterialBos = new ArrayList<>();
                                    boolean needRePushToAudit = false;
                                    for (CreativeDetailMaterialBo existMaterialBo : existTaskMaterialBos) {


                                        if (!Objects.equals(existMaterialBo.getMaterialType(), RiskMaterialTypeEnum.LINK.getCode())) {
                                            newMaterialBos.add(existMaterialBo);
                                            continue;
                                        }
                                        // 非问题link todo 改成通用
                                        if (!existMaterialBo.getMaterialContent().equals("link-miniapp.bilibili.com/applet-")) {
                                            newMaterialBos.add(existMaterialBo);
                                            continue;
                                        }

                                        // 问题 url
                                        if (existMaterialBo.getMaterialSourceSet().contains(MaterialSourceEnum.STORY.getCode())) {
                                            // todo 万一其他来源的，先不管
                                            log.error("refresh[有问题的素材，但是是story的，跳过], creativeId={}", existMaterialBo.getCreativeId());
                                            break;
                                        }

                                        LauCreativeExtraPo creativeExtraPo = creativeExtraPoMap.get(existMaterialBo.getCreativeId());
                                        if (creativeExtraPo == null) {
                                            log.error("refresh[creativeExtraPo不存在], creativeId={}", existMaterialBo.getCreativeId());
                                            break;
                                        }

                                        String rawJumpUrl = creativeExtraPo.getRawJumpUrl();
                                        if (StringUtils.isEmpty(rawJumpUrl)) {
                                            log.error("refresh[getRawJumpUrl is null], creativeId={}", existMaterialBo.getCreativeId());
                                            break;
                                        }

                                        // 处理素材
                                        RawJumpUrlMsgBo jumpUrlMsgBo = new RawJumpUrlMsgBo();
                                        jumpUrlMsgBo.setUrl(rawJumpUrl);
                                        CreativeDetailMaterialBo creativeDetailMaterialBo = IMaterialAuditConvertor.INSTANCE.toTabUrlMaterialBo(jumpUrlMsgBo);
                                        CreativeDetailInfoBo creativeDetailInfoBo = new CreativeDetailInfoBo();
                                        creativeDetailInfoBo.setTabUrlBos(Arrays.asList(creativeDetailMaterialBo));
                                        Map<String, LauMaterialAuditBo> materialAuditBoMap = creativeMaterialPushToAuditService.initRiskMaterialIfNeed(creativeDetailInfoBo);
                                        LauMaterialAuditBo lauMaterialAuditBo = materialAuditBoMap.entrySet().stream().map(t -> t.getValue()).collect(Collectors.toList()).get(0);
                                        if (lauMaterialAuditBo == null) {
                                            log.error("refresh[初始化后的素材materialAuditBo不存在], creativeId={}", existMaterialBo.getCreativeId());
                                            break;
                                        }

                                        PromotionPurposeContentProcessorEnum processorEnum = PromotionPurposeContentProcessorEnum.searchProcessorEnum(rawJumpUrl);
//                                        String bodyId = processorEnum.getContextToMatch(rawJumpUrl);
                                        // link-{subType}-{url}
//                                        String materialContent = "link" + "-" + processorEnum.getCode() + "-" + bodyId;

                                        CreativeDetailMaterialBo materialBo = new CreativeDetailMaterialBo();
                                        materialBo.setMaterialType(RiskMaterialTypeEnum.LINK.getCode());
                                        materialBo.setRawContent(rawJumpUrl);
                                        materialBo.setMaterialContent(lauMaterialAuditBo.getMaterialContent());
                                        materialBo.setMaterialMd5(lauMaterialAuditBo.getMaterialMd5());
                                        materialBo.setMaterialId(lauMaterialAuditBo.getMaterialId());
                                        materialBo.setMaterialSourceSet(Sets.newHashSet(MaterialSourceEnum.CREATIVE.getCode()));
                                        materialBo.setCreativeId(creativeId);
                                        materialBo.setBizType(MaterialBizTypeEnum.SANLIAN.getCode());

                                        newMaterialBos.add(materialBo);
                                        needRePushToAudit = true;
                                        log.info("refresh[替换新的链接], creativeId={}, materialId={}, 旧url={},新url={}", creativeId, lauMaterialAuditBo.getMaterialId(), existMaterialBo.getMaterialContent(), rawJumpUrl);
                                    }


                                    if (needRePushToAudit) {
                                        // 调用更新方法
                                        if (Utils.isPositive(isUpdate)) {
                                            creativeMaterialPushToAuditService.sync2MaterialTables(entry.getKey(), toAuditCreativePo.getEventTime(), null, Lists.newArrayList(newMaterialBos));
                                        } else {
                                            // 模拟更新
                                            updateCount++;
                                            shadingUpdateCount++;
                                            log.info("refresh[模拟更新]，creativeId={}", entry.getKey(), JSON.toJSONString(newMaterialBos));
                                        }
                                    }
                                }
                            } catch (Exception e) {
                                log.error("refresh[error], creativeId={}", creativeId, e);
                                throw new RuntimeException(e);
                            } finally {
                                if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                                    lock.unlock();
                                }
                            }
                        }

                        fromCreativeId = creativeRelPos.get(creativeRelPos.size() - 1).getCreativeId();

                    } while (!CollectionUtils.isEmpty(creativeRelPos) && creativeRelPos.size() == 100);

                }
                log.info("refresh[更新一批数据], shadingNo: {}, updateCount: {}, shadingUpdateCount: {},minId={}", shadingNo, updateCount, shadingUpdateCount, minId);

            }

            // 处理下一个分片
            shadingNo++;
        }

        long endTime = System.currentTimeMillis();
        log.info("refresh[所有分表刷完了], shadingNo: {}, updateCount: {},cost={}", shadingNo, updateCount, endTime - startTime);

    }

    private List<LauMaterialAuditPo> queryProblemMaterials(List<String> materialIds, Long minId, int shadingNo) {
        LauMaterialAuditPoExample example = new LauMaterialAuditPoExample();
        LauMaterialAuditPoExample.Criteria criteria = example.createCriteria()
                .andMaterialTypeEqualTo(RiskMaterialTypeEnum.LINK.getCode())
                // link-miniapp.bilibili.com/applet-
                .andMaterialContentEqualTo("link-miniapp.bilibili.com/applet-")
                .andIdGreaterThan(minId);
        example.setOrderByClause("id asc");

        if (!CollectionUtils.isEmpty(materialIds)) {
            criteria.andMaterialIdIn(materialIds);
        }
        example.setLimit(100);
        List<LauMaterialAuditPo> auditPos = lauMaterialAuditExtDao.selectByExampleWithShardingNo(shadingNo, example);
        return auditPos;
    }

    private static void sleep() {
        try {
            Thread.sleep(50);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }

}
