package com.bilibili.risk.service.rule;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.util.Page;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.rbac.api.dto.RoleBaseDto;
import com.bilibili.rbac.biz.service.RoleService;
import com.bilibili.risk.bo.*;
import com.bilibili.risk.config.BizConfig;
import com.bilibili.risk.constant.DatabaseConstant;
import com.bilibili.risk.constant.RiskConstants;
import com.bilibili.risk.convertor.IMaterialAuditRuleConvertor;
import com.bilibili.risk.convertor.IRoleConvertor;
import com.bilibili.risk.dao.risk.LauMaterialAuditRuleDao;
import com.bilibili.risk.dao.risk.LauMaterialAuditRuleQueueRelDao;
import com.bilibili.risk.dao.risk.LauMaterialAuditRuleRoleRelDao;
import com.bilibili.risk.enums.DiffTypeEnum;
import com.bilibili.risk.enums.LogTypeEnum;
import com.bilibili.risk.enums.OperationTypeEnum;
import com.bilibili.risk.po.risk.*;
import com.bilibili.risk.service.log.OperationLogService;
import com.bilibili.risk.service.queue.MaterialAuditQueueService;
import com.bilibili.risk.service.role.RiskRoleService;
import com.bilibili.risk.utils.DiffLogUtils;
import com.bilibili.risk.utils.ListDiffUtil;
import edu.emory.mathcs.backport.java.util.Collections;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class MaterialAuditRuleService {

    private final LauMaterialAuditRuleDao lauMaterialAuditRuleDao;
    private final MaterialAuditQueueService materialAuditQueueService;
    private final BizConfig bizConfig;
    private final RoleService roleService;
    private final MaterialAuditRuleQueueRelService materialAuditRuleQueueRelService;
    private final MaterialAuditRuleRoleRelService materialAuditRuleRoleRelService;
    private final LauMaterialAuditRuleQueueRelDao lauMaterialAuditRuleQueueRelDao;
    private final LauMaterialAuditRuleRoleRelDao lauMaterialAuditRuleRoleRelDao;
    private final RedissonClient adpRedissonClient;
    private final OperationLogService operationLogService;
    private final RiskRoleService riskRoleService;

    /**
     * 获取规则详情
     *
     * @param id
     * @return
     */
    public MaterialAuditRuleInfoBo fetchMaterialAuditRule(Long id) {
        Assert.notNull(id, "id不能为空");

        LauMaterialAuditRulePo auditRulePo = lauMaterialAuditRuleDao.selectByPrimaryKey(id);
        Assert.notNull(auditRulePo, "规则不存在");
        MaterialAuditRuleInfoBo auditRuleBo = IMaterialAuditRuleConvertor.INSTANCE.po2InfoBo(auditRulePo);

        List<LauMaterialAuditRuleRoleRelPo> ruleRoleRelPos = materialAuditRuleRoleRelService.queryAuditRuleRoleRelListByRuleId(id);
        List<Integer> roleIds = ruleRoleRelPos.stream().map(t -> t.getRoleId()).distinct().collect(Collectors.toList());
        auditRuleBo.setRolesIds(roleIds);

        // 绑定的队列
        List<LauMaterialAuditRuleQueueRelPo> relPos = materialAuditRuleQueueRelService.queryAuditRuleQueueRelListByRuleId(id);
        List<Long> queueIds = relPos.stream().map(LauMaterialAuditRuleQueueRelPo::getQueueId).collect(Collectors.toList());

        Map<Long, LauMaterialAuditQueueBo> auditQueueBoMap = materialAuditQueueService.queryMapByIds(queueIds);
        List<LauMaterialAuditQueueBo> bindAuditQueueBos = queueIds.stream().map(queueId -> auditQueueBoMap.get(queueId)).filter(Objects::nonNull).collect(Collectors.toList());
        auditRuleBo.setBindQueues(bindAuditQueueBos);

        // 角色
        List<RoleBaseDto> roleBaseDtos = roleService.getRolesByRoleIds(bizConfig.getTenantId(), auditRuleBo.getRolesIds());
        String roleNames = roleBaseDtos.stream().map(t -> t.getName()).collect(Collectors.joining(","));
        auditRuleBo.setRoleBos(IRoleConvertor.INSTANCE.dtos2Bos(roleBaseDtos));
        auditRuleBo.setRoleNames(roleNames);
        return auditRuleBo;
    }

    @SneakyThrows
    @Transactional(rollbackFor = Exception.class, transactionManager = DatabaseConstant.RISK_TRANSACTION_MANAGER)
    public Integer saveMaterialAuditRule(MaterialAuditRuleSaveBo auditRuleBo, OperatorBo operatorBo) {

        if (Utils.isPositive(auditRuleBo.getId())) {
            RLock lock = adpRedissonClient.getLock(RiskConstants.LOCK_RULE_UPDATE + auditRuleBo.getId());
            if (lock.tryLock(3, 10, TimeUnit.SECONDS)) {
                return updateMaterialAuditRule(auditRuleBo, operatorBo);
            } else {
                throw new RuntimeException("当前规则正在被修改，请稍后重试");
            }
        } else {
            RLock lock = adpRedissonClient.getLock(RiskConstants.LOCK_RULE_CREATE + operatorBo.getBilibiliUserName());
            try {
                if (lock.tryLock(3, 10, TimeUnit.SECONDS)) {
                    return createMaterialAuditRule(auditRuleBo, operatorBo);
                } else {
                    throw new RuntimeException("当前用户正在改规则，请稍后重试");
                }
            } finally {
                if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }
        }
    }

    /**
     * 新建规则
     *
     * @param auditRuleBo
     * @param operatorBo
     * @return
     */
    @Transactional(rollbackFor = Exception.class, transactionManager = DatabaseConstant.RISK_TRANSACTION_MANAGER)
    public Integer createMaterialAuditRule(MaterialAuditRuleSaveBo auditRuleBo, OperatorBo operatorBo) {
        log.info("createMaterialAuditRule[新建规则]: auditRuleBo={}, operatorBo={}", auditRuleBo, operatorBo);
        validate(auditRuleBo);

        // 规则
        LauMaterialAuditRulePo auditRulePo = IMaterialAuditRuleConvertor.INSTANCE.saveBo2po(auditRuleBo);
        int count = lauMaterialAuditRuleDao.insertSelective(auditRulePo);
        auditRuleBo.setId(auditRulePo.getId());

        // 规则队列关系
        List<LauMaterialAuditRuleQueueRelPo> queueRelPos = buildRuleQueueRelPos(auditRuleBo, auditRulePo);
        lauMaterialAuditRuleQueueRelDao.insertBatch(queueRelPos);
        // 规则角色关系
        List<LauMaterialAuditRuleRoleRelPo> roleRelPos = buildRuleRoleRelPos(auditRuleBo, auditRulePo);
        lauMaterialAuditRuleRoleRelDao.insertBatch(roleRelPos);

        String diffLog = DiffLogUtils.generateDiffField(null, auditRuleBo,
                auditRuleBo.getClass().getSimpleName());
        MaterialAuditTaskOperationLogBo operationLogBo = MaterialAuditTaskOperationLogBo.builder()
                .objId(auditRuleBo.getId() + "")
                .operatorUsername(RiskConstants.SYSTEM_USERNAME)
                .operationType(OperationTypeEnum.ADD.getCode())
                .value(diffLog)
                .diff(diffLog)
                .ctime(System.currentTimeMillis())
                .type(LogTypeEnum.RULE.getCode())
                .systemType(RiskConstants.SYSTEM_TYPE_RISK)
                .build();
        operationLogService.saveLog(operationLogBo);
        return Math.toIntExact(auditRuleBo.getId());
    }

    private static @NotNull List<LauMaterialAuditRuleRoleRelPo> buildRuleRoleRelPos(MaterialAuditRuleSaveBo auditRuleBo, LauMaterialAuditRulePo auditRulePo) {
        List<LauMaterialAuditRuleRoleRelPo> roleRelPos = auditRuleBo.getRolesIds().stream().map(queueId -> {
            Timestamp now = Utils.getNow();
            return LauMaterialAuditRuleRoleRelPo.builder()
                    .ruleId(auditRulePo.getId())
                    .isDeleted(0)
                    .ctime(now)
                    .mtime(now)
                    .roleId(queueId)
                    .build();
        }).collect(Collectors.toList());
        return roleRelPos;
    }

    private void validate(MaterialAuditRuleSaveBo auditRuleBo) {
        Assert.isTrue(auditRuleBo != null, "auditRuleBo不能为空");
        Assert.isTrue(StringUtils.isNotEmpty(auditRuleBo.getName()), "规则名称不能为空");
//        Assert.isTrue(auditRuleBo.getOrder() != null, "顺序不能为空");
        Assert.isTrue(!CollectionUtils.isEmpty(auditRuleBo.getQueueIds()), "关联队列不能为空");
        Assert.isTrue(!CollectionUtils.isEmpty(auditRuleBo.getRolesIds()), "授权用户组不能为空");
        Assert.notNull(auditRuleBo.getPullType(), "领取规则类型不能为空");
        Assert.isTrue(auditRuleBo.getQueueIds().stream().collect(Collectors.toSet()).size() == auditRuleBo.getQueueIds().size(), "存在重复的队列，请检查");
        Assert.isTrue(auditRuleBo.getQueueIds().size() <= bizConfig.getRuleBindQueueMaxNum(), "规则绑定队列数量超过" + bizConfig.getRuleBindQueueMaxNum() + "，请检查");

        // 不允许绑定两个特殊队列
        if (auditRuleBo.getQueueIds().contains(bizConfig.getWaitQueueId())) {
            throw new RuntimeException("不允许绑定等待队列");
        }
        if (auditRuleBo.getQueueIds().contains(bizConfig.getFallbackQueueId())) {
            throw new RuntimeException("不允许兜底等待队列");
        }
    }

    /**
     * 修改规则
     *
     * @param auditRuleBo
     * @param operatorBo
     * @return
     */
    @Transactional(rollbackFor = Exception.class, transactionManager = DatabaseConstant.RISK_TRANSACTION_MANAGER)
    public Integer updateMaterialAuditRule(MaterialAuditRuleSaveBo auditRuleBo, OperatorBo operatorBo) {
        log.info("updateMaterialAuditRule[修改规则]: auditRuleBo={}, operatorBo={}", auditRuleBo, operatorBo);
        Assert.isTrue(Utils.isPositive(auditRuleBo.getId()), "id不能为空");

        validate(auditRuleBo);

        // 已有规则
        LauMaterialAuditRulePo oldRulePo = lauMaterialAuditRuleDao.selectByPrimaryKey(auditRuleBo.getId());
        MaterialAuditRuleSaveBo oldAuditRuleBo = IMaterialAuditRuleConvertor.INSTANCE.po2SaveBo(oldRulePo);
        Assert.notNull(oldRulePo, "规则不存在");

        // 已有规则队列关系
        List<LauMaterialAuditRuleQueueRelPo> existQueueRelPos = materialAuditRuleQueueRelService.queryAuditRuleQueueRelListByRuleId(auditRuleBo.getId());
        oldAuditRuleBo.setQueueIds(existQueueRelPos.stream().map(t -> t.getQueueId()).collect(Collectors.toList()));

        // 已有队列角色关系
        List<LauMaterialAuditRuleRoleRelPo> existRoleRelPos = materialAuditRuleRoleRelService.queryAuditRuleRoleRelListByRuleId(auditRuleBo.getId());
        oldAuditRuleBo.setRolesIds(existRoleRelPos.stream().map(t -> t.getRoleId()).collect(Collectors.toList()));

        // 规则
        LauMaterialAuditRulePo auditRulePo = IMaterialAuditRuleConvertor.INSTANCE.saveBo2po(auditRuleBo);
        int count = lauMaterialAuditRuleDao.updateByPrimaryKeySelective(auditRulePo);

        // 保存与队列关系，与角色关系
        List<LauMaterialAuditRuleQueueRelPo> newQueueRelPos = buildRuleQueueRelPos(auditRuleBo, auditRulePo);
        List<LauMaterialAuditRuleRoleRelPo> newRoleRelPos = buildRuleRoleRelPos(auditRuleBo, auditRulePo);
        saveRuleQueueRels(newQueueRelPos, existQueueRelPos, auditRulePo);
        saveRuleRoleRels(newRoleRelPos, existRoleRelPos, auditRulePo);

        String diffLog = DiffLogUtils.generateDiffField(oldAuditRuleBo, auditRuleBo,
                auditRuleBo.getClass().getSimpleName());
        log.info("updateMaterialAuditRule[修改规则]: auditRuleBo={}, operatorBo={}, diffLog={}", auditRuleBo, operatorBo, diffLog);
        MaterialAuditTaskOperationLogBo operationLogBo = MaterialAuditTaskOperationLogBo.builder()
                .objId(auditRuleBo.getId() + "")
                .operatorUsername(RiskConstants.SYSTEM_USERNAME)
                .operationType(OperationTypeEnum.UPDATE.getCode())
                .value(diffLog)
                .diff(diffLog)
                .ctime(System.currentTimeMillis())
                .type(LogTypeEnum.RULE.getCode())
                .systemType(RiskConstants.SYSTEM_TYPE_RISK)
                .build();
        operationLogService.saveLog(operationLogBo);
        return count;
    }

    private void saveRuleQueueRels(List<LauMaterialAuditRuleQueueRelPo> newRelPos, List<LauMaterialAuditRuleQueueRelPo> existRelPos, LauMaterialAuditRulePo auditRulePo) {
        Map<Integer, List<LauMaterialAuditRuleQueueRelPo>> map = ListDiffUtil.calculateDiff(newRelPos, existRelPos, r -> r.getQueueId() + "_" + r.getRuleId());
        List<LauMaterialAuditRuleQueueRelPo> addRelPos = map.get(DiffTypeEnum.ADDED.getKey());
        List<LauMaterialAuditRuleQueueRelPo> updatedRelPos = map.get(DiffTypeEnum.UPDATED.getKey());
        List<LauMaterialAuditRuleQueueRelPo> deletedRelPos = map.get(DiffTypeEnum.DELETED.getKey());

        if (!CollectionUtils.isEmpty(addRelPos)) {
            lauMaterialAuditRuleQueueRelDao.insertBatch(addRelPos);
            log.info("saveRuleQueueRels[保存规规则队列关系]: addRelPos={}", JSON.toJSONString(addRelPos));
        }

        if (!CollectionUtils.isEmpty(deletedRelPos)) {
            LauMaterialAuditRuleQueueRelPoExample deleteExample = new LauMaterialAuditRuleQueueRelPoExample();
            deleteExample.createCriteria().andRuleIdEqualTo(auditRulePo.getId()).andQueueIdIn(deletedRelPos.stream().map(LauMaterialAuditRuleQueueRelPo::getQueueId).collect(Collectors.toList()));
            lauMaterialAuditRuleQueueRelDao.deleteByExample(deleteExample);
            log.info("saveRuleQueueRels[保存规规则队列关系]: deletedRelPos={}", JSON.toJSONString(deletedRelPos));
        }

        if (!CollectionUtils.isEmpty(updatedRelPos)) {
            lauMaterialAuditRuleQueueRelDao.insertUpdateBatch(updatedRelPos);
            log.info("saveRuleQueueRels[保存规规则队列关系]: updatedRelPos={}", JSON.toJSONString(updatedRelPos));
        }
    }

    private void saveRuleRoleRels(List<LauMaterialAuditRuleRoleRelPo> newRelPos, List<LauMaterialAuditRuleRoleRelPo> existRelPos, LauMaterialAuditRulePo auditRulePo) {
        Map<Integer, List<LauMaterialAuditRuleRoleRelPo>> map = ListDiffUtil.calculateDiff(newRelPos, existRelPos, r -> r.getRoleId() + "_" + r.getRuleId());
        List<LauMaterialAuditRuleRoleRelPo> addRelPos = map.get(DiffTypeEnum.ADDED.getKey());
        List<LauMaterialAuditRuleRoleRelPo> updatedRelPos = map.get(DiffTypeEnum.UPDATED.getKey());
        List<LauMaterialAuditRuleRoleRelPo> deletedRelPos = map.get(DiffTypeEnum.DELETED.getKey());

        if (!CollectionUtils.isEmpty(addRelPos)) {
            lauMaterialAuditRuleRoleRelDao.insertBatch(addRelPos);
            log.info("saveRuleRoleRels: addRelPos={}", JSON.toJSONString(addRelPos));
        }

        if (!CollectionUtils.isEmpty(deletedRelPos)) {
            LauMaterialAuditRuleRoleRelPoExample deleteExample = new LauMaterialAuditRuleRoleRelPoExample();
            deleteExample.createCriteria().andRuleIdEqualTo(auditRulePo.getId()).andRoleIdIn(deletedRelPos.stream().map(LauMaterialAuditRuleRoleRelPo::getRoleId).collect(Collectors.toList()));
            lauMaterialAuditRuleRoleRelDao.deleteByExample(deleteExample);
            log.info("saveRuleRoleRels: deletedRelPos={}", JSON.toJSONString(deletedRelPos));
        }

        if (!CollectionUtils.isEmpty(updatedRelPos)) {
            lauMaterialAuditRuleRoleRelDao.insertUpdateBatch(updatedRelPos);
            log.info("saveRuleRoleRels: updatedRelPos={}", JSON.toJSONString(updatedRelPos));
        }
    }

    private static @NotNull List<LauMaterialAuditRuleQueueRelPo> buildRuleQueueRelPos(MaterialAuditRuleSaveBo auditRuleBo, LauMaterialAuditRulePo auditRulePo) {
        List<LauMaterialAuditRuleQueueRelPo> newRelPos = auditRuleBo.getQueueIds().stream().map(queueId -> {
            Timestamp now = Utils.getNow();
            return LauMaterialAuditRuleQueueRelPo.builder()
                    .ruleId(auditRulePo.getId())
                    .remark("")
                    .extra("")
                    .isDeleted(0)
                    .ctime(now)
                    .mtime(now)
                    .queueId(queueId)
                    .build();
        }).collect(Collectors.toList());
        return newRelPos;
    }

    /**
     * 规则列表
     * 根据用户角色显示有权限的规则
     *
     * @param page
     * @param pageSize
     * @param queryBo
     * @return
     */
    public PageResult<MaterialAuditRuleInfoBo> queryList(Integer page, Integer pageSize, MaterialAuditRuleQueryBo queryBo) {

        // 根据用户名查询用户可以看到的规则ids
        List<Long> ruleIdsCondition = queryRuleIdsByUserName(queryBo);

        LauMaterialAuditRulePoExample example = new LauMaterialAuditRulePoExample();
        LauMaterialAuditRulePoExample.Criteria criteria = example.createCriteria();
        Page pg = Page.valueOf(page, pageSize);
        example.setLimit(pg.getLimit());
        example.setOffset(pg.getOffset());
        // seq 倒序, mtime 倒序
        example.setOrderByClause("seq desc, mtime desc");

        if (!CollectionUtils.isEmpty(ruleIdsCondition)) {
            criteria.andIdIn(ruleIdsCondition);
        }
        if (!CollectionUtils.isEmpty(queryBo.getIds())) {
            criteria.andIdIn(queryBo.getIds());
        }
        if (StringUtils.isNotEmpty(queryBo.getName())) {
            criteria.andNameLike("%" + queryBo.getName() + "%");
        }

        long total = lauMaterialAuditRuleDao.countByExample(example);
        if (total == 0) {
            return PageResult.emptyPageResult();
        }
        List<LauMaterialAuditRulePo> auditRulePos = lauMaterialAuditRuleDao.selectByExample(example);
        List<MaterialAuditRuleInfoBo> auditRuleBos = auditRulePos.stream().map(IMaterialAuditRuleConvertor.INSTANCE::po2InfoBo).collect(Collectors.toList());
        List<Long> ruleIds = auditRuleBos.stream().map(MaterialAuditRuleInfoBo::getId).collect(Collectors.toList());

        // 绑定的队列
        if (!CollectionUtils.isEmpty(ruleIds) && queryBo.isNeedBindQueue()) {
            LauMaterialAuditRuleQueueRelPoExample queueRelPoExample = new LauMaterialAuditRuleQueueRelPoExample();
            queueRelPoExample.createCriteria().andRuleIdIn(ruleIds);
            List<LauMaterialAuditRuleQueueRelPo> relPos = lauMaterialAuditRuleQueueRelDao.selectByExample(queueRelPoExample);
            Map<Long, List<LauMaterialAuditRuleQueueRelPo>> ruleQueueRelPos = relPos.stream().collect(Collectors.groupingBy(LauMaterialAuditRuleQueueRelPo::getRuleId));
            List<Long> allQueueIds = relPos.stream().map(LauMaterialAuditRuleQueueRelPo::getQueueId).distinct().collect(Collectors.toList());
            Map<Long, LauMaterialAuditQueueBo> auditQueueBoMap = materialAuditQueueService.queryMapByIds(allQueueIds);

            for (MaterialAuditRuleInfoBo auditRuleBo : auditRuleBos) {
                List<LauMaterialAuditRuleQueueRelPo> ruleQueueRelPoList = ruleQueueRelPos.getOrDefault(auditRuleBo.getId(), Collections.emptyList());
                List<Long> queueIds = ruleQueueRelPoList.stream().map(LauMaterialAuditRuleQueueRelPo::getQueueId).collect(Collectors.toList());
                List<LauMaterialAuditQueueBo> bindAuditQueueBos = queueIds.stream().map(queueId -> auditQueueBoMap.get(queueId)).filter(Objects::nonNull).collect(Collectors.toList());
                auditRuleBo.setBindQueues(bindAuditQueueBos);
            }
        }

        // 绑定的角色
        if (!CollectionUtils.isEmpty(ruleIds)) {
            LauMaterialAuditRuleRoleRelPoExample queueRelPoExample = new LauMaterialAuditRuleRoleRelPoExample();
            queueRelPoExample.createCriteria().andRuleIdIn(ruleIds);
            List<LauMaterialAuditRuleRoleRelPo> relPos = lauMaterialAuditRuleRoleRelDao.selectByExample(queueRelPoExample);
            Map<Long, List<LauMaterialAuditRuleRoleRelPo>> ruleRoleRelPoMap = relPos.stream().collect(Collectors.groupingBy(t -> t.getRuleId()));
            List<Integer> allRoleIds = relPos.stream().map(LauMaterialAuditRuleRoleRelPo::getRoleId).distinct().collect(Collectors.toList());

            List<RoleBaseDto> roleBaseDtos = roleService.getRolesByRoleIds(bizConfig.getTenantId(), allRoleIds);
            Map<Integer, RoleBaseDto> roleBaseDtoMap = roleBaseDtos.stream().collect(Collectors.toMap(t -> t.getId(), t -> t));
            for (MaterialAuditRuleInfoBo auditRuleBo : auditRuleBos) {
                List<LauMaterialAuditRuleRoleRelPo> ruleRoleRelPoList = ruleRoleRelPoMap.getOrDefault(auditRuleBo.getId(), Collections.emptyList());
                List<Integer> ruleRoleIds = ruleRoleRelPoList.stream().map(t -> t.getRoleId()).collect(Collectors.toList());
                auditRuleBo.setRolesIds(ruleRoleIds);

                List<RoleBaseDto> roleBaseDtoList = ruleRoleIds.stream().map(roleId -> roleBaseDtoMap.get(roleId)).filter(Objects::nonNull).collect(Collectors.toList());
                auditRuleBo.setRoleBos(IRoleConvertor.INSTANCE.dtos2Bos(roleBaseDtoList));
            }
        }
        return PageResult.<MaterialAuditRuleInfoBo>builder()
                .total((int) total)
                .records(auditRuleBos)
                .build();
    }

    public List<MaterialAuditRuleInfoBo> queryListByIds(List<Long> ruleIds) {
        if (CollectionUtils.isEmpty(ruleIds)) {
            return Collections.emptyList();
        }
        LauMaterialAuditRulePoExample example = new LauMaterialAuditRulePoExample();
        example.createCriteria().andIdIn(ruleIds);
        List<LauMaterialAuditRulePo> auditRulePos = lauMaterialAuditRuleDao.selectByExample(example);
        List<MaterialAuditRuleInfoBo> auditRuleBos = auditRulePos.stream().map(IMaterialAuditRuleConvertor.INSTANCE::po2InfoBo).collect(Collectors.toList());
        return auditRuleBos;
    }

    private @NotNull List<Long> queryRuleIdsByUserName(MaterialAuditRuleQueryBo queryBo) {
        List<Long> ruleIds = new ArrayList<>();
        // 根据角色获取有权限的规则
        List<RoleBo> roleBos = riskRoleService.getRoleIdsByUsername(queryBo.getUsername());
        // 业务管理员可以看到所有的规则

        List<Integer> roleIds = roleBos.stream().map(t -> t.getId()).collect(Collectors.toList());
        // 是否是系统管理员，系统管理员也可以看到所有的rule
        boolean isSystemAdmin = roleBos.stream().anyMatch(r -> r.getLevel().equals(0));
        // 业务管理员和系统管理员都可以看到所有的规则
        if (!roleIds.contains(bizConfig.getBusinessAdminRoleId()) && !isSystemAdmin) {
            List<LauMaterialAuditRuleRoleRelPo> ruleRoleRelPos = materialAuditRuleRoleRelService.queryAuditRuleRoleRelListByRoleIds(roleIds);
            ruleIds = ruleRoleRelPos.stream().map(t -> t.getRuleId()).distinct().collect(Collectors.toList());
            // 非管理员，没有绑定任何规则
            if (CollectionUtils.isEmpty(ruleIds)) {
                ruleIds.add(-1L);
            }
        }
        return ruleIds;
    }

}
