package com.bilibili.risk.service.task;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bapis.ad.archive.CmArchiveMode;
import com.bapis.ad.crm.industry.BatchQueryUnitedIndustryByIdsReq;
import com.bapis.ad.crm.industry.IndustryInfo;
import com.bapis.ad.crm.industry.IndustryInfoAgg;
import com.bapis.ad.crm.industry.IndustryServiceGrpc;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.risk.bo.*;
import com.bilibili.risk.bo.msg.CreativeBinlogBo;
import com.bilibili.risk.config.BizConfig;
import com.bilibili.risk.constant.RiskConstants;
import com.bilibili.risk.convertor.IMaterialAuditLabelConvertor;
import com.bilibili.risk.convertor.IMaterialAuditTaskConvertor;
import com.bilibili.risk.enums.MetricsCodeEnum;
import com.bilibili.risk.enums.PromotionPurposeContentProcessorEnum;
import com.bilibili.risk.enums.RiskMaterialTypeEnum;
import com.bilibili.risk.enums.ShardingTableEnum;
import com.bilibili.risk.es.ElasticsearchCloudConfig;
import com.bilibili.risk.es.ElasticsearchQueryUtil;
import com.bilibili.risk.po.es.LauMaterialAuditTaskEsPo;
import com.bilibili.risk.po.risk.LauMaterialAuditTaskPo;
import com.bilibili.risk.service.MaterialAuditLabelService;
import com.bilibili.risk.service.archive.BiliArchiveService;
import com.bilibili.risk.service.archive.CmArchiveService;
import com.bilibili.risk.service.archive.RiskVideoService;
import com.bilibili.risk.service.black_word.BlackGrayWordService;
import com.bilibili.risk.service.creative.CreativeService;
import com.bilibili.risk.service.dynamic.BiliDynamicService;
import com.bilibili.risk.service.material.LauMaterialAuditService;
import com.bilibili.risk.metrics_report.metrics.TaskMetricsReportService;
import com.bilibili.risk.service.page_group.PageGroupService;
import com.bilibili.risk.service.queue.MaterialAuditQueueService;
import com.bilibili.risk.utils.MaterialTaskUtils;
import com.dianping.cat.Cat;
import edu.emory.mathcs.backport.java.util.Collections;
import joptsimple.internal.Strings;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.PrefixQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.WildcardQueryBuilder;
import org.elasticsearch.script.Script;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.AvgAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.MinAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.ValueCountAggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import pleiades.venus.starter.rpc.client.RPCClient;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.elasticsearch.index.query.QueryBuilders.existsQuery;
import static org.elasticsearch.index.query.QueryBuilders.rangeQuery;

@Slf4j
@Service
@RequiredArgsConstructor
public class MaterialAuditTaskEsService {

    private final BizConfig bizConfig;
    private final MaterialTaskEsPubService materialTaskEsPubService;
    private final CreativeService creativeService;
    private final MaterialAuditTaskDbService materialAuditTaskDbService;
    private static ElasticsearchCloudConfig config;
    private final MaterialAuditLabelService materialAuditLabelService;
    private final LauMaterialAuditService lauMaterialAuditService;
    private final BlackGrayWordService blackGrayWordService;
    private final MaterialAuditQueueService materialAuditQueueService;
    private final CmArchiveService cmArchiveService;
    private final BiliDynamicService biliDynamicService;
    private final BiliArchiveService biliArchiveService;
    private final RiskVideoService riskVideoService;
    private final PageGroupService pageGroupService;
    private final TaskMetricsReportService taskMetricsReportService;

    @RPCClient("sycpb.cpm.crm-portal")
    private IndustryServiceGrpc.IndustryServiceBlockingStub industryServiceBlockingStub;

    @PostConstruct
    public void init() {
        if (config == null) {
            synchronized (MaterialAuditTaskEsService.class) {
                if (config == null) {
                    config = ElasticsearchCloudConfig.builder()
                            .token(bizConfig.getEsMaterialAuditTaskToken())
                            .index(bizConfig.getEsMaterialAuditTaskIndex())
                            .domain(bizConfig.getEsDomain())
                            .indexShardingType(bizConfig.getEsMaterialAuditTaskIndexShardingType())
                            .build();
                    log.info("material task ElasticsearchCloudConfig init, config:{}", config);
                }
            }
        }
    }

    private ElasticsearchCloudConfig getESConfig() {
        return config;
    }

    /**
     * 查询一批任务，默认 ctime asc
     *
     * @param commonQueryBo
     * @return
     */
    public PageResult<MaterialAuditTaskEsBo> queryMaterialAuditTaskEsBosCommon(MaterialAuditTaskCommonQueryBo commonQueryBo) {

        Long startTime = System.currentTimeMillis();
        // 查询es数据
        PageResult<LauMaterialAuditTaskEsPo> pageResult = this.queryMaterialAuditTaskEsPosCommon(commonQueryBo);
        Cat.logEvent("queryMaterialAuditTaskEsBosCommon", "es查询完");

        List<MaterialAuditTaskEsBo> auditTaskEsBos = IMaterialAuditTaskConvertor.INSTANCE.pos2bos(pageResult.getRecords());
        // 拼接其他字段
        spliceOtherFields(auditTaskEsBos);
        Long endTime = System.currentTimeMillis();
        taskMetricsReportService.addBizMetricCountAndCost(endTime - startTime, MetricsCodeEnum.SubCode.BIZ_MATERIAL_TASK_PAGE_LIST, "", 0, auditTaskEsBos.size());
        return new PageResult<>(pageResult.getTotal(), auditTaskEsBos);
    }

    private TaskListContext buildContext(List<MaterialAuditTaskEsBo> auditTaskEsBos) {
        TaskListContext context = new TaskListContext();

        List<String> taskIds = auditTaskEsBos.stream().map(t -> t.getId()).collect(Collectors.toList());
        Map<String, LauMaterialAuditTaskPo> lauMaterialAuditTaskPoMap = materialAuditTaskDbService.queryMapByTaskIds(taskIds);
        context.setLauMaterialAuditTaskPoMap(lauMaterialAuditTaskPoMap);

        List<Integer> creativeIds = auditTaskEsBos.stream().map(t -> t.getCreativeId()).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<Integer, CreativeBo> creativeBoMap = creativeService.queryMapByCreativeIds(creativeIds);
        context.setCreativeBoMap(creativeBoMap);

        // 标签树
        List<Long> thirdLevelLabelIds = lauMaterialAuditTaskPoMap.values().stream()
                .map(LauMaterialAuditTaskPo::getAuditLabelThirdId)
                .filter(str -> !StringUtils.isEmpty(str)) // 过滤掉空字符串
                .flatMap(str -> Arrays.stream(str.split(",")))     // 按逗号分割并扁平化
                .map(Long::valueOf)
                .collect(Collectors.toList());
        Map<Long, LauMaterialAuditLabelBo> auditLabelBoMap = materialAuditLabelService.queryMapByIds(thirdLevelLabelIds);
        context.setAuditLabelBoMap(auditLabelBoMap);

        // 行业名称
        List<Integer> industryIds = auditTaskEsBos.stream()
                .flatMap(vo -> Stream.of(
                        vo.getUnitedFirstIndustryId(),
                        vo.getUnitedSecondIndustryId(),
                        vo.getUnitedThirdIndustryId()
                ))
                .filter(Objects::nonNull)
                .filter(t -> Utils.isPositive(t))
                .collect(Collectors.toList());
        Map<Integer, String> industryInfoMap = new HashMap<>();
        if (!industryIds.isEmpty()) {
            IndustryInfoAgg industryInfoAgg = industryServiceBlockingStub.batchQueryUnitedIndustryByIds(BatchQueryUnitedIndustryByIdsReq.newBuilder().addAllIds(industryIds).build()).getData();
            List<IndustryInfo> industryInfosList = industryInfoAgg.getIndustryInfosList();
            industryInfoMap = industryInfosList.stream().collect(Collectors.toMap(IndustryInfo::getId, t -> t.getName(), (t1, t2) -> t2));
        }
        context.setIndustryInfoMap(industryInfoMap);

        // 队列
        List<LauMaterialAuditQueueBo> auditQueueBos = materialAuditQueueService.queryAllMaterialAuditQueue(0);
        Map<Long, LauMaterialAuditQueueBo> auditQueueBoMap = auditQueueBos.stream().collect(Collectors.toMap(t -> t.getId(), t -> t, (t1, t2) -> t2));
        context.setAuditQueueBoMap(auditQueueBoMap);
        return context;
    }

    /**
     * 拼接任务上需要的其他信息字段
     *
     * @param auditTaskEsBos
     */
    public TaskListContext spliceOtherFields(List<MaterialAuditTaskEsBo> auditTaskEsBos) {
        if (CollectionUtils.isEmpty(auditTaskEsBos)) {
            return null;
        }

        // 构建上下文
        TaskListContext context = buildContext(auditTaskEsBos);
        Cat.logEvent("queryMaterialAuditTaskEsBosCommon", "buildContext完");

        // 拼接数据
        spliceOtherFields(auditTaskEsBos, context);
        Cat.logEvent("queryMaterialAuditTaskEsBosCommon", "spliceOtherFields完");
        return context;
    }

    private void spliceOtherFields(List<MaterialAuditTaskEsBo> auditTaskEsBos, TaskListContext context) {

        // 构建拉取任务场景的公共信息，第一个任务的一些公用信息
        buildDataForPull(auditTaskEsBos, context);

        // 拼接信息
        spliceOtherFieldsByTask(context, auditTaskEsBos);
        Cat.logEvent("queryMaterialAuditTaskEsBosCommon", "spliceOtherFieldsByTask完");

        // 根据类型处理每个类型特有信息，如文本，稿件，动态...
        spliceOtherFieldsByMaterialType(auditTaskEsBos, context);
        Cat.logEvent("queryMaterialAuditTaskEsBosCommon", "spliceOtherFieldsByMaterialType完");
    }

    private void spliceOtherFieldsByMaterialType(List<MaterialAuditTaskEsBo> auditTaskEsBos, TaskListContext context) {

        Map<Integer, List<MaterialAuditTaskEsBo>> taskVoMap = auditTaskEsBos.stream().filter(t -> t != null && t.getMaterialType() != null).collect(Collectors.groupingBy(t -> t.getMaterialType()));
        Iterator<Map.Entry<Integer, List<MaterialAuditTaskEsBo>>> itr = taskVoMap.entrySet().iterator();
        while (itr.hasNext()) {
            Map.Entry<Integer, List<MaterialAuditTaskEsBo>> taskVosOfType = itr.next();
            List<MaterialAuditTaskEsBo> taskEsBos = taskVosOfType.getValue();
            RiskMaterialTypeEnum materialTypeEnum = RiskMaterialTypeEnum.getByCode(taskVosOfType.getKey());
            switch (taskVosOfType.getKey()) {
                case 1:
                    // 图片，图片地址
                    for (MaterialAuditTaskEsBo taskEsBo : taskEsBos) {
                        taskEsBo.setParsedMaterialContent(taskEsBo.getMaterialContent());
                    }
                    break;
                case 4:
                    // 文本
                    spliceTextMaterialInfo(taskEsBos);
                    break;
                case 9:
                    // 动态
                    spliceDynamicMaterialInfo(taskEsBos, materialTypeEnum);
                    Cat.logEvent("queryMaterialAuditTaskEsBosCommon", "spliceDynamicMaterialInfo完");
                    break;
                case 10:
                    // 直播间
                    spliceLiveRoomMaterialInfo(taskEsBos, materialTypeEnum);
                    break;
                case 11:
                    // url
                    spliceUrlMaterialInfo(taskEsBos);
                    break;
                case 12:
                    // 稿件
                    spliceArchiveMaterialInfo(taskEsBos, materialTypeEnum);
                    Cat.logEvent("queryMaterialAuditTaskEsBosCommon", "spliceArchiveMaterialInfo完");
                    break;
                case 14:
                    // 三方落地页
                    spliceThirdPartyLandingPageMaterialInfo(taskEsBos, materialTypeEnum);
                    Cat.logEvent("queryMaterialAuditTaskEsBosCommon", "spliceThirdPartyLandingPageMaterialInfo完");
                default:
                    break;
            }

        }
    }

    // 优先从素材的 rawContent 中获取 url; 没有的话从 materialContent 中获取
    private void spliceUrlMaterialInfo(List<MaterialAuditTaskEsBo> taskEsBos) {
        List<String> materialIds = taskEsBos.stream().map(t -> t.getMaterialId()).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<String, LauMaterialAuditBo> materialAuditBoMap = lauMaterialAuditService.queryMapByMaterialIds(materialIds);

        // url, 格式: url-{subType}-{id}
        for (MaterialAuditTaskEsBo taskEsBo : taskEsBos) {
            LauMaterialAuditBo materialAuditBo = materialAuditBoMap.get(taskEsBo.getMaterialId());
            if (materialAuditBo != null) {
                String content = materialAuditBo.getMaterialContent();
                if (!StringUtils.isEmpty(materialAuditBo.getRawContent())) {
                    content = materialAuditBo.getRawContent();
                }

                // 解析
                PromotionPurposeContentProcessorEnum promotionPurposeContentProcessorEnum = PromotionPurposeContentProcessorEnum.searchProcessorEnum(content);
                if (promotionPurposeContentProcessorEnum != null) {
                    String parsedMaterialContent = promotionPurposeContentProcessorEnum.splitUrl(content);
                    taskEsBo.setParsedMaterialContent(parsedMaterialContent);
                    taskEsBo.setSplitLink(parsedMaterialContent);
                }
            }
        }
    }

    private void spliceThirdPartyLandingPageMaterialInfo(List<MaterialAuditTaskEsBo> taskEsBos, RiskMaterialTypeEnum materialTypeEnum) {
        // pageId url:格式: pageGroupPage-{groupId}-{pageId}
        Set<Long> groupIdSet = new HashSet<>();
        taskEsBos.forEach(taskEsBo -> {
            String pageId = materialTypeEnum.parse(taskEsBo.getMaterialContent());
            taskEsBo.setPageId(pageId);
            taskEsBo.setParsedMaterialContent(taskEsBo.getPageId());
            groupIdSet.add(MaterialTaskUtils.parsePageGroupId(taskEsBo.getMaterialContent()));
        });

        // 提取组和pageIds
        List<Long> groupIds = groupIdSet.stream().filter(Objects::nonNull).collect(Collectors.toList());
        // 返回三方落地页的 url
        List<LandingPageGroupBo> landingPageGroupBos = pageGroupService.queryPageGroupBoList(groupIds);
        Map<Long, LandingPageGroupMappingBo> pageGroupMappingBoMap = landingPageGroupBos.stream()
                .filter(t -> Objects.equals(t.getGroupSource(), 1))
                .flatMap(t -> t.getMappingBos().stream())
                .collect(Collectors.toMap(t -> t.getPageId(), t -> t, (t1, t2) -> t2));

        for (MaterialAuditTaskEsBo taskEsBo : taskEsBos) {
            if (StringUtils.isEmpty(taskEsBo.getPageId())) {
                continue;
            }

            LandingPageGroupMappingBo pageGroupMappingBo = pageGroupMappingBoMap.get(Long.parseLong(taskEsBo.getPageId()));
            if (pageGroupMappingBo != null) {
                taskEsBo.setLandingPageGroupMappingBo(pageGroupMappingBo);
                taskEsBo.setSplitLink(pageGroupMappingBo.getPcLaunchUrl());
                taskEsBo.setParsedMaterialContent(pageGroupMappingBo.getPcLaunchUrl());
            }
        }
    }

    private void spliceArchiveMaterialInfo(List<MaterialAuditTaskEsBo> taskEsBos, RiskMaterialTypeEnum materialTypeEnum) {
        // 稿件视频 avid-{avid}
        taskEsBos.forEach(taskEsBo -> {
            taskEsBo.setAvid(materialTypeEnum.parse(taskEsBo.getMaterialContent()));
            taskEsBo.setSplitLink(materialTypeEnum.splitJumpLink(taskEsBo.getAvid()));
            taskEsBo.setParsedMaterialContent(taskEsBo.getAvid());
        });

        List<Long> avids = taskEsBos.stream().map(t -> Long.parseLong(t.getAvid())).filter(t -> Utils.isPositive(t)).distinct().collect(Collectors.toList());
        // bili稿件信息
        Map<Long, BiliArchiveBo> biliArchiveBoMap = biliArchiveService.fetchMap(avids);

        // 创意中心稿件
//        Map<Long, CmArchiveBo> cmArchiveBoMap = cmArchiveService.queryCmArchiveBoMap(avids);

        // 风控限流等信息
        Map<Long, ArchiveRiskVideoBo> archiveRiskVideoBoMap = riskVideoService.fetchRiskVideoMap(avids);

        for (MaterialAuditTaskEsBo taskEsBo : taskEsBos) {
            Long avid = Long.parseLong(taskEsBo.getAvid());
            taskEsBo.setBiliArchiveBo(biliArchiveBoMap.get(avid));

//            // 创意中心稿件
//            CmArchiveBo cmArchiveBo = cmArchiveBoMap.get(avid);
//            taskEsBo.setIsUserSpaceArchive(false);
//            if (cmArchiveBo != null) {
//                taskEsBo.setIsUserSpaceArchive(Objects.equals(cmArchiveBo.getArchiveMode(), CmArchiveMode.USER_SPACE_VALUE));
//            }
            // 风控限流等信息
            ArchiveRiskVideoBo archiveRiskVideoBo = archiveRiskVideoBoMap.get(avid);
            taskEsBo.setArchiveRiskVideoBo(archiveRiskVideoBo);
        }
    }

    private static void spliceLiveRoomMaterialInfo(List<MaterialAuditTaskEsBo> taskEsBos, RiskMaterialTypeEnum materialTypeEnum) {
        // 直播 live-{roomId}
        taskEsBos.forEach(taskEsBo -> {
            taskEsBo.setRoomId(materialTypeEnum.parse(taskEsBo.getMaterialContent()));
            taskEsBo.setSplitLink(materialTypeEnum.splitJumpLink(taskEsBo.getRoomId()));
            taskEsBo.setParsedMaterialContent(taskEsBo.getRoomId());
        });
    }

    private void spliceDynamicMaterialInfo(List<MaterialAuditTaskEsBo> taskEsBos, RiskMaterialTypeEnum materialTypeEnum) {
        // 动态 dynamic-{dynamicId}
        List<Long> dynamicIds = new ArrayList<>();
        taskEsBos.forEach(taskEsBo -> {
            String dynamicId = materialTypeEnum.parse(taskEsBo.getMaterialContent());
            taskEsBo.setDynamicId(dynamicId);
            dynamicIds.add(Long.parseLong(dynamicId));
            taskEsBo.setSplitLink(materialTypeEnum.splitJumpLink(dynamicId));
            taskEsBo.setParsedMaterialContent(dynamicId);
        });
        Map<Long, DynamicResponseBo> dynamicInfoMap = biliDynamicService.getDynamicInfoMap(DynamicReqBo.builder().dyn_ids(dynamicIds).build());
        for (MaterialAuditTaskEsBo taskEsVo : taskEsBos) {
            if (!StringUtils.isEmpty(taskEsVo.getDynamicId())) {
                taskEsVo.setDynamicResponseBo(dynamicInfoMap.get(Long.parseLong(taskEsVo.getDynamicId())));
            }
        }
    }

    @SneakyThrows
    private static void spliceOtherFieldsByTask(TaskListContext context, List<MaterialAuditTaskEsBo> auditTaskEsBos) {

        Map<Long, LauMaterialAuditQueueBo> auditQueueBoMap = context.getAuditQueueBoMap();
        Map<Long, LauMaterialAuditLabelBo> auditLabelBoMap = context.getAuditLabelBoMap();
        Map<Integer, String> industryInfoMap = context.getIndustryInfoMap();
        Map<Integer, CreativeBo> creativeBoMap = context.getCreativeBoMap();
        Map<String, LauMaterialAuditTaskPo> lauMaterialAuditTaskPoMap = context.getLauMaterialAuditTaskPoMap();

        for (MaterialAuditTaskEsBo auditTaskEsBo : auditTaskEsBos) {
            LauMaterialAuditTaskPo auditTaskPo = lauMaterialAuditTaskPoMap.get(auditTaskEsBo.getId());
            if (auditTaskPo == null) {
                continue;
            }
            if (auditTaskEsBo.getMaterialType() == null) {
                continue;
            }
            BeanUtils.copyProperties(auditTaskPo, auditTaskEsBo, "id");

            // 解析 body id
            RiskMaterialTypeEnum riskMaterialTypeEnum = RiskMaterialTypeEnum.getByCode(auditTaskEsBo.getMaterialType());
            auditTaskEsBo.setMaterialTypeName(riskMaterialTypeEnum.getDesc());
            if (riskMaterialTypeEnum != null && riskMaterialTypeEnum.equals(RiskMaterialTypeEnum.UNKNOWN) && RiskMaterialTypeEnum.getCanParseBodyIdTypes().contains(auditTaskEsBo.getMaterialType())) {
                // 解析出素材id
                String materialContent = auditTaskEsBo.getMaterialContent();
                String bodyId = riskMaterialTypeEnum.parse(materialContent);
                if (!StringUtils.isEmpty(bodyId)) {
                    try {
                        auditTaskEsBo.setBodyId(Long.parseLong(bodyId));
                    } catch (Exception e) {
                        log.error("parse bodyId error, materialContent:{}, e:{}", materialContent, e);
                    }
                }
            }

            // 队列
            LauMaterialAuditQueueBo queueVo = auditQueueBoMap.get(auditTaskEsBo.getQueueId());
            if (queueVo != null) {
                auditTaskEsBo.setQueueName(queueVo.getName());
            }

            // 行业
            auditTaskEsBo.setUnitedFirstIndustryName(industryInfoMap.getOrDefault(auditTaskEsBo.getUnitedFirstIndustryId(), Strings.EMPTY));
            auditTaskEsBo.setUnitedSecondIndustryName(industryInfoMap.getOrDefault(auditTaskEsBo.getUnitedSecondIndustryId(), Strings.EMPTY));
            auditTaskEsBo.setUnitedThirdIndustryName(industryInfoMap.getOrDefault(auditTaskEsBo.getUnitedThirdIndustryId(), Strings.EMPTY));

            // 创意信息
            CreativeBo creativeBo = creativeBoMap.get(auditTaskEsBo.getCreativeId());
            auditTaskEsBo.setUnitId(creativeBo != null ? creativeBo.getUnitId() : null);

            // 拼接三级标签结构
            String auditLabelThirdId = auditTaskEsBo.getAuditLabelThirdId();
            if (StringUtils.isEmpty(auditLabelThirdId)) {
                continue;
            }
            List<LauMaterialAuditLabelBo> auditLabelBos = Arrays.stream(auditLabelThirdId.split(",")).map(Long::parseLong).map(auditLabelBoMap::get).filter(t -> t != null).collect(Collectors.toList());
            List<ThirdLevelAuditLabelBo> auditLabelThirdIds = IMaterialAuditLabelConvertor.INSTANCE.bos2bos(auditLabelBos);
            auditTaskEsBo.setAuditLabelThirdBos(auditLabelThirdIds);
        }
    }

    private static void buildDataForPull(List<MaterialAuditTaskEsBo> auditTaskEsBos, TaskListContext context) {

        Map<Long, LauMaterialAuditQueueBo> auditQueueBoMap = context.getAuditQueueBoMap();

        MaterialAuditTaskEsBo taskEsBo0 = auditTaskEsBos.get(0);
        context.setQueueId(taskEsBo0.getQueueId());
        LauMaterialAuditQueueBo auditQueueBo = auditQueueBoMap.get(taskEsBo0.getQueueId());
        if (auditQueueBo != null) {
            context.setTaskTimeout(auditQueueBo.getTaskTimeout());
        }
        context.setAcceptTime(taskEsBo0.getAcceptTime() != null ? taskEsBo0.getAcceptTime().getTime() : null);
        context.setReceiveBatchNo(taskEsBo0.getReceiveBatchNo());
    }

    private void spliceTextMaterialInfo(List<MaterialAuditTaskEsBo> taskEsVos) {
        // 敏感词处理，根据文本是否包含敏感词

        Set<String> sensitiveWords = blackGrayWordService.queryAllWords();
        for (MaterialAuditTaskEsBo taskEsBo : taskEsVos) {
            if (org.apache.commons.lang3.StringUtils.isEmpty(taskEsBo.getMaterialContent())) {
                continue;
            }

            List<String> creativeGrayWords = new ArrayList<>();
            for (String word : sensitiveWords) {
                if (taskEsBo.getMaterialContent().contains(word)) {
                    creativeGrayWords.add(word);
                }
            }
            taskEsBo.setSensitiveWords(creativeGrayWords);
            taskEsBo.setParsedMaterialContent(taskEsBo.getMaterialContent());
        }
    }

    /**
     * 查询一批任务，默认 ctime asc
     *
     * @param commonQueryBo
     * @return
     */
    public PageResult<LauMaterialAuditTaskEsPo> queryMaterialAuditTaskEsPosCommon(MaterialAuditTaskCommonQueryBo commonQueryBo) {
        log.info("queryMaterialAuditTaskEsPosCommon, commonQueryBo:{}", commonQueryBo);
        BoolQueryBuilder boolQuery = buildBoolQueryBuilder(commonQueryBo);

        SearchSourceBuilder searchSourceBuilder = ElasticsearchQueryUtil.buildPaginationAndSort(boolQuery, commonQueryBo.getPage(), commonQueryBo.getPageSize()
                , commonQueryBo.getSortField(), commonQueryBo.getSortOrder(), "id");
        return ElasticsearchQueryUtil.getPageResultFromEsCloud(searchSourceBuilder,
                getESConfig(), null, LauMaterialAuditTaskEsPo.class);
    }

    @NotNull
    private static BoolQueryBuilder buildBoolQueryBuilder(MaterialAuditTaskCommonQueryBo commonQueryBo) {
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();

        // 多值in
        ElasticsearchQueryUtil.addTermsQueryIfNotEmpty(boolQuery, "id", commonQueryBo.getIds());
        // 排除指定的任务ID
        if (!CollectionUtils.isEmpty(commonQueryBo.getExcludeTaskIds())) {
            boolQuery.mustNot(QueryBuilders.termsQuery("id", commonQueryBo.getExcludeTaskIds()));
        }
        if (!CollectionUtils.isEmpty(commonQueryBo.getExcludeQueueIds())) {
            boolQuery.mustNot(QueryBuilders.termsQuery("queueId", commonQueryBo.getExcludeQueueIds()));
        }
        if(!CollectionUtils.isEmpty(commonQueryBo.getExcludeExecuteNames())){
            boolQuery.mustNot(QueryBuilders.termsQuery("executeName", commonQueryBo.getExcludeExecuteNames()));
        }
        ElasticsearchQueryUtil.addTermsQueryIfNotEmpty(boolQuery, "materialId", commonQueryBo.getMaterialIds());
        ElasticsearchQueryUtil.addTermsQueryIfNotEmpty(boolQuery, "materialType", commonQueryBo.getMaterialTypes());
        ElasticsearchQueryUtil.addTermsQueryIfNotEmpty(boolQuery, "materialMd5", commonQueryBo.getMaterialMd5s());
        ElasticsearchQueryUtil.addTermsQueryIfNotEmpty(boolQuery, "creativeId", commonQueryBo.getCreativeIds());
        ElasticsearchQueryUtil.addTermsQueryIfNotEmpty(boolQuery, "bizType", commonQueryBo.getBizTypes());
        ElasticsearchQueryUtil.addTermsQueryIfNotEmpty(boolQuery, "queueId", commonQueryBo.getQueueIds());
        ElasticsearchQueryUtil.addTermsQueryIfNotEmpty(boolQuery, "handleType", commonQueryBo.getHandleTypes());
        ElasticsearchQueryUtil.addTermsQueryIfNotEmpty(boolQuery, "status", commonQueryBo.getStatusList());
        ElasticsearchQueryUtil.addTermsQueryIfNotEmpty(boolQuery, "creativeAuditStatus", commonQueryBo.getCreativeAuditStatusList());
        ElasticsearchQueryUtil.addTermsQueryIfNotEmpty(boolQuery, "accountId", commonQueryBo.getAccountIds());
        ElasticsearchQueryUtil.addTermsQueryIfNotEmpty(boolQuery, "acceptName", commonQueryBo.getAcceptNames());
        ElasticsearchQueryUtil.addTermsQueryIfNotEmpty(boolQuery, "executeName", commonQueryBo.getExecuteNames());
        ElasticsearchQueryUtil.addTermsQueryIfNotEmpty(boolQuery, "auditLabelThirdIds", commonQueryBo.getAuditLabelThirdIds());
        ElasticsearchQueryUtil.addTermsQueryIfNotEmpty(boolQuery, "unitedFirstIndustryId", commonQueryBo.getUnitedFirstIndustryIds());
        ElasticsearchQueryUtil.addTermsQueryIfNotEmpty(boolQuery, "unitedSecondIndustryId", commonQueryBo.getUnitedSecondIndustryIds());
        ElasticsearchQueryUtil.addTermsQueryIfNotEmpty(boolQuery, "unitedThirdIndustryId", commonQueryBo.getUnitedThirdIndustryIds());
        ElasticsearchQueryUtil.addTermsQueryIfNotEmpty(boolQuery, "isDeleted", commonQueryBo.getIsDeleted());
        ElasticsearchQueryUtil.addTermsQueryIfNotEmpty(boolQuery, "isUserSpaceArchive", commonQueryBo.getIsUserSpaceArchiveList());

        // 前缀匹配
        if (!CollectionUtils.isEmpty(commonQueryBo.getAvids())) {
            List<String> spliceAvids = commonQueryBo.getAvids().stream().map(t -> RiskMaterialTypeEnum.VIDEO.generateMaterialContent(t)).collect(Collectors.toList());
            ElasticsearchQueryUtil.addTermsQueryIfNotEmpty(boolQuery, "materialContent", spliceAvids);
            if (CollectionUtils.isEmpty(commonQueryBo.getMaterialTypes())) {
                ElasticsearchQueryUtil.addTermsQueryIfNotEmpty(boolQuery, "materialType", Arrays.asList(RiskMaterialTypeEnum.VIDEO.getCode()));
            }
        }
        if (!CollectionUtils.isEmpty(commonQueryBo.getDynamicIds())) {
            List<String> spliceAvids = commonQueryBo.getDynamicIds().stream().map(t -> RiskMaterialTypeEnum.DYNAMIC.generateMaterialContent(t)).collect(Collectors.toList());
            ElasticsearchQueryUtil.addTermsQueryIfNotEmpty(boolQuery, "materialContent", spliceAvids);
            if (CollectionUtils.isEmpty(commonQueryBo.getMaterialTypes())) {
                ElasticsearchQueryUtil.addTermsQueryIfNotEmpty(boolQuery, "materialType", Arrays.asList(RiskMaterialTypeEnum.DYNAMIC.getCode()));
            }
        }
        if (!CollectionUtils.isEmpty(commonQueryBo.getRoomIds())) {
            List<String> spliceAvids = commonQueryBo.getRoomIds().stream().map(t -> RiskMaterialTypeEnum.LIVE.generateMaterialContent(t)).collect(Collectors.toList());
            ElasticsearchQueryUtil.addTermsQueryIfNotEmpty(boolQuery, "materialContent", spliceAvids);
            if (CollectionUtils.isEmpty(commonQueryBo.getMaterialTypes())) {
                ElasticsearchQueryUtil.addTermsQueryIfNotEmpty(boolQuery, "materialType", Arrays.asList(RiskMaterialTypeEnum.LIVE.getCode()));
            }
        }
        if (!StringUtils.isEmpty(commonQueryBo.getPageGroupId())) {
            // pageGroupPage-{groupId}
            String pageGroupPrefix = RiskMaterialTypeEnum.PAGE_GROUP_PAGE.generateMaterialContent(commonQueryBo.getPageGroupId());
            PrefixQueryBuilder prefixQuery = new PrefixQueryBuilder("materialContent", pageGroupPrefix);
            boolQuery.filter(prefixQuery);
            if (CollectionUtils.isEmpty(commonQueryBo.getMaterialTypes())) {
                ElasticsearchQueryUtil.addTermsQueryIfNotEmpty(boolQuery, "materialType", Arrays.asList(RiskMaterialTypeEnum.PAGE_GROUP_PAGE.getCode()));
            }
        }
        if (!StringUtils.isEmpty(commonQueryBo.getTextLike())) {
            // 建议使用.keyword避免分词
            WildcardQueryBuilder wildcardQuery = QueryBuilders.wildcardQuery("materialContent", "*" + commonQueryBo.getTextLike() + "*");
            boolQuery.filter(wildcardQuery);
            if (CollectionUtils.isEmpty(commonQueryBo.getMaterialTypes())) {
                ElasticsearchQueryUtil.addTermsQueryIfNotEmpty(boolQuery, "materialType", Arrays.asList(RiskMaterialTypeEnum.TEXT.getCode()));
            }
        }

        // 单个精确查询
        ElasticsearchQueryUtil.addMatchQueryIfNotEmpty(boolQuery, "receiveBatchNo", commonQueryBo.getReceiveBatchNo());
        ElasticsearchQueryUtil.addMatchQueryIfNotEmpty(boolQuery, "materialContent", commonQueryBo.getMaterialContent());

        // 单个模糊查询
//        ElasticsearchQueryUtil.addPrefixFuzzyQueryIfNotEmpty(boolQuery, "materialContent", commonQueryBo.getMaterialContentPrefixMatch());

        // 范围
        if (Utils.isPositive(commonQueryBo.getExecuteTimeStart())) {
            boolQuery.filter(rangeQuery("executeTime").gte(commonQueryBo.getExecuteTimeStart()));
        }
        if (Utils.isPositive(commonQueryBo.getExecuteTimeEnd())) {
            boolQuery.filter(rangeQuery("executeTime").lte(commonQueryBo.getExecuteTimeEnd()));
        }
        if (Utils.isPositive(commonQueryBo.getAcceptTimeStart())) {
            boolQuery.filter(rangeQuery("acceptTime").gte(commonQueryBo.getAcceptTimeStart()));
        }
        if (Utils.isPositive(commonQueryBo.getAcceptTimeEnd())) {
            boolQuery.filter(rangeQuery("acceptTime").lte(commonQueryBo.getAcceptTimeEnd()));
        }
        if (Utils.isPositive(commonQueryBo.getCtimeStart())) {
            boolQuery.filter(rangeQuery("ctime").gte(commonQueryBo.getCtimeStart()));
        }
        if (Utils.isPositive(commonQueryBo.getCtimeEnd())) {
            boolQuery.filter(rangeQuery("ctime").lte(commonQueryBo.getCtimeEnd()));
        }
        if (Utils.isPositive(commonQueryBo.getMtimeStart())) {
            boolQuery.filter(rangeQuery("mtime").gte(commonQueryBo.getMtimeStart()));
        }
        if (Utils.isPositive(commonQueryBo.getMtimeEnd())) {
            boolQuery.filter(rangeQuery("mtime").lte(commonQueryBo.getMtimeEnd()));
        }

        if(!CollectionUtils.isEmpty(commonQueryBo.getFieldExists())){
            for (String fieldExist : commonQueryBo.getFieldExists()) {
                boolQuery.must(existsQuery(fieldExist));
            }
        }

        if(!CollectionUtils.isEmpty(commonQueryBo.getFieldNotExists())){
            for (String fieldExist : commonQueryBo.getFieldNotExists()) {
                boolQuery.mustNot(existsQuery(fieldExist));
            }
        }

        return boolQuery;
    }

    public AvgSecsResultBo queryAvgSecs(MaterialAuditTaskCommonQueryBo commonQueryBo) {
        // 因为是领取时间默认值: 946656000000L, 2000-01-01 00:00:00
        commonQueryBo.setAcceptTimeStart(RiskConstants.BUSINESS_EARLIEST_TIMESTAMP_LONG);
        BoolQueryBuilder boolQuery = buildBoolQueryBuilder(commonQueryBo);

        // 创建平均时长聚合
        AvgAggregationBuilder avgAgg = AggregationBuilders
                .avg("avg_duration")
                // 要求有执行时间，没有领取时间按进审时间来
                .script(new Script(
                        "if (doc['executeTime'].size() == 0) { return 0; } " +
                                "if (doc['acceptTime'].size() == 0 || doc['acceptTime'].value == 946656000000L) { " +
                                "    return doc['executeTime'].value - doc['enterAuditTime'].value; " +
                                "} " +
                                "return doc['executeTime'].value - doc['acceptTime'].value;"
                ));

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(boolQuery);
        searchSourceBuilder.size(0);
        searchSourceBuilder.aggregation(avgAgg);

        JSONObject aggJsonObj = ElasticsearchQueryUtil.getAggResultFromEsCloud(searchSourceBuilder, getESConfig(), null);
        if (aggJsonObj == null) {
            return AvgSecsResultBo.builder().total(0L).avgSecs(0L).build();
        }

        JSONObject avgDuration = aggJsonObj.getJSONObject("avg_duration");
        Long value = avgDuration.getLong("value");

        return AvgSecsResultBo.builder().total(null).avgSecs(value).build();
    }

    public List<QueueStatBo> queryStatGroupByQueueId(MaterialAuditTaskCommonQueryBo commonQueryBo) {
        BoolQueryBuilder boolQuery = buildBoolQueryBuilder(commonQueryBo);

        // 创建按queueId分组的聚合
        TermsAggregationBuilder groupByQueue = AggregationBuilders
                .terms("group_by_queue")
                .field("queueId")
                .size(100);

        // 添加子聚合 - 最早进审时间
        if (commonQueryBo.isWithOldestAuditTime()) {
            MinAggregationBuilder earliestCtime = AggregationBuilders
                    .min("oldest_audit_time")
                    // 进审时间
                    .field(RiskConstants.SORT_FIELD_ENTER_AUDIT_TIME);
            groupByQueue.subAggregation(earliestCtime);
        }

        // 添加子聚合 - 任务总数
        ValueCountAggregationBuilder totalTasks = AggregationBuilders
                .count("audit_count")
                .field("id");
        groupByQueue.subAggregation(totalTasks);

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(boolQuery);
        searchSourceBuilder.size(0);
        searchSourceBuilder.aggregation(groupByQueue);

        JSONObject aggJsonObj = ElasticsearchQueryUtil.getAggResultFromEsCloud(searchSourceBuilder, getESConfig(), null);
        if (aggJsonObj == null) {
            return Collections.emptyList();
        }

        JSONObject groupByQueueJsonObj = aggJsonObj.getJSONObject("group_by_queue");
        JSONArray buckets = groupByQueueJsonObj.getJSONArray("buckets");
        List<QueueStatBo> queueStatBos = new ArrayList<>();
        for (Object bucket : buckets) {
            JSONObject obj = (JSONObject) bucket;
            Long queueId = obj.getLong("key");
//            Long docCount = obj.getLong("doc_count");
            JSONObject auditCountObj = obj.getJSONObject("audit_count");
            long toAuditCount = auditCountObj.getLongValue("value");

            QueueStatBo queueStatBo = QueueStatBo.builder()
                    .queueId(queueId)
                    .auditCount(toAuditCount)
                    .build();
            if (commonQueryBo.isWithOldestAuditTime()) {
                JSONObject oldestAuditTimeObj = obj.getJSONObject("oldest_audit_time");
                long oldestAuditTime = oldestAuditTimeObj.getLongValue("value");
                queueStatBo.setOldestAuditTime(oldestAuditTime);
            }
            queueStatBos.add(queueStatBo);
        }
        return queueStatBos;
    }


    // 根据batchNo获取未完成的任务
    public List<LauMaterialAuditTaskEsPo> queryNotCompletedMaterialAuditTaskEsPosByBatchNo(String batchNo) {

        MaterialAuditTaskCommonQueryBo queryBo = MaterialAuditTaskCommonQueryBo.builder().receiveBatchNo(batchNo)
                .page(1)
                .pageSize(100)
                .sortField(RiskConstants.SORT_FIELD_MTIME)
                .sortOrder(SortOrder.ASC)
                .build();
        PageResult<LauMaterialAuditTaskEsPo> pageResult = this.queryMaterialAuditTaskEsPosCommon(queryBo);
        return pageResult.getRecords();
    }

    /**
     * 根据某个队列id, 拉取任务，按账户聚合
     * 1. 只会拉取到一个队列的
     * 2. 拉取到的需要按账户聚合(落地页组的还需要根据groupId聚合)
     * 3. 需要按时间升序 ctime asc
     *
     * @param commonQueryBo
     * @return
     */
    public List<LauMaterialAuditTaskEsPo> queryAuditTasksAggAccountBySingleQueue(MaterialAuditTaskCommonQueryBo commonQueryBo) {

        // 先拉取一个
        MaterialAuditTaskCommonQueryBo pullOneQueryBo = IMaterialAuditTaskConvertor.INSTANCE.copy(commonQueryBo);
        pullOneQueryBo.setPage(1);
        pullOneQueryBo.setPageSize(1);
        pullOneQueryBo.setSortField(RiskConstants.SORT_FIELD_MTIME);
        pullOneQueryBo.setSortOrder(SortOrder.ASC);
        log.info("queryAuditTasksAggAccountBySingleQueue[拉取一个dsl], pullOneQueryBo:{}", pullOneQueryBo);
        PageResult<LauMaterialAuditTaskEsPo> pullOnePageResult = this.queryMaterialAuditTaskEsPosCommon(pullOneQueryBo);
        if (CollectionUtils.isEmpty(pullOnePageResult.getRecords())) {
            return Collections.emptyList();
        }

        // 根据账户id拉取一批
        LauMaterialAuditTaskEsPo oldestFreeTaskEsPo = pullOnePageResult.getRecords().get(0);

        MaterialAuditTaskCommonQueryBo pullOneBatchBo = IMaterialAuditTaskConvertor.INSTANCE.copy(commonQueryBo);
        pullOneBatchBo.setAccountIds(Arrays.asList(oldestFreeTaskEsPo.getAccountId()));

        // 落地页组的话，则根据groupId聚合
        if (Objects.equals(oldestFreeTaskEsPo.getMaterialType(), RiskMaterialTypeEnum.PAGE_GROUP_PAGE.getCode())) {
            // 提取 pageGroupPage-{groupId}-{pageId}
            Long groupId = MaterialTaskUtils.parsePageGroupId(oldestFreeTaskEsPo.getMaterialContent());
            if (Utils.isPositive(groupId)) {
                pullOneBatchBo.setPageGroupId(groupId + "");
            }
        }
        log.info("queryAuditTasksAggAccountBySingleQueue[聚合拉取一批dsl], pullOneBatchBo:{}", pullOneBatchBo);
        PageResult<LauMaterialAuditTaskEsPo> pullBatchPageResult = this.queryMaterialAuditTaskEsPosCommon(pullOneBatchBo);
        return pullBatchPageResult.getRecords();
    }

    /**
     * 从多个队列按时间拉取任务，按账户聚合
     * 1. 只会拉取到一个队列的
     * 2. 拉取到的需要按账户聚合(落地页组的还需要根据groupId聚合)
     * 3. 需要按时间升序
     * 4 时间排序决定最早的在哪个队列，然后获取那个队列的最早的游离的一批，按账户聚合
     *
     * @param commonQueryBo
     * @return
     */
    public List<LauMaterialAuditTaskEsPo> queryAuditTasksAggAccountByQueues(MaterialAuditTaskCommonQueryBo commonQueryBo) {

        // 先拉取一个最早的
        MaterialAuditTaskCommonQueryBo pullOneQueryBo = IMaterialAuditTaskConvertor.INSTANCE.copy(commonQueryBo);
        pullOneQueryBo.setPage(1);
        pullOneQueryBo.setPageSize(1);
        PageResult<LauMaterialAuditTaskEsPo> pullOnePageResult = this.queryMaterialAuditTaskEsPosCommon(pullOneQueryBo);
        log.info("queryAuditTasksAggAccountByQueues[拉取一个dsl], pullOneQueryBo:{}", pullOneQueryBo);
        if (CollectionUtils.isEmpty(pullOnePageResult.getRecords())) {
            return Collections.emptyList();
        }

        Map<Long, LauMaterialAuditQueueBo> auditQueueBoMap = commonQueryBo.getAuditQueueBoMap();
        // 确定队列id和账户id，再拉去一批
        LauMaterialAuditTaskEsPo oldestFreeTaskEsPo = pullOnePageResult.getRecords().get(0);

        LauMaterialAuditQueueBo auditQueueBo = auditQueueBoMap.get(oldestFreeTaskEsPo.getQueueId());
        MaterialAuditTaskCommonQueryBo pullOneBatchBo = IMaterialAuditTaskConvertor.INSTANCE.copy(commonQueryBo);
        pullOneBatchBo.setQueueIds(Arrays.asList(oldestFreeTaskEsPo.getQueueId()));
        pullOneBatchBo.setAccountIds(Arrays.asList(oldestFreeTaskEsPo.getAccountId()));
        pullOneBatchBo.setPage(1);
        pullOneBatchBo.setPageSize(auditQueueBo != null ? auditQueueBo.getPullNum().intValue() : bizConfig.getTaskFallbackPullNum());
        pullOneBatchBo.setSortField(RiskConstants.SORT_FIELD_ENTER_AUDIT_TIME);
        pullOneBatchBo.setSortOrder(SortOrder.ASC);

        // 落地页组的话，则根据groupId聚合
        if (Objects.equals(oldestFreeTaskEsPo.getMaterialType(), RiskMaterialTypeEnum.PAGE_GROUP_PAGE.getCode())) {
            // 提取 pageGroupPage-{groupId}-{pageId}
            Long groupId = MaterialTaskUtils.parsePageGroupId(oldestFreeTaskEsPo.getMaterialContent());
            if (Utils.isPositive(groupId)) {
                pullOneBatchBo.setPageGroupId(groupId + "");
            }
        }
        log.info("queryAuditTasksAggAccountByQueues[聚合拉取一批dsl], pullOneBatchBo:{}", pullOneBatchBo);
        PageResult<LauMaterialAuditTaskEsPo> pullBatchPageResult = this.queryMaterialAuditTaskEsPosCommon(pullOneBatchBo);
        return pullBatchPageResult.getRecords();
    }

    public void syncTask2Es(List<LauMaterialAuditTaskBo> auditTaskBos, boolean needCreative) {
        if (CollectionUtils.isEmpty(auditTaskBos)) {
            return;
        }
        List<String> taskIds = auditTaskBos.stream().map(LauMaterialAuditTaskBo::getTaskId).collect(Collectors.toList());
        log.info("syncTask2Es, taskIds:{}", taskIds);

        List<Integer> creativeIds = auditTaskBos.stream().map(t -> t.getCreativeId()).distinct().collect(Collectors.toList());
        Map<Integer, CreativeBo> creativeBoMap = new HashMap<>();
        if (needCreative) {
            creativeBoMap = creativeService.queryMapByCreativeIds(creativeIds);
        }

        Map<Integer, List<LauMaterialAuditTaskBo>> auditTaskPosMapByCreativeId = auditTaskBos.stream().collect(Collectors.groupingBy(e -> e.getCreativeId() % ShardingTableEnum.TABLE_LAU_MATERIAL_AUDIT_TASK.getShardingNum()));

        List<LauMaterialAuditTaskPo> auditTaskPos = new ArrayList<>();
        for (Map.Entry<Integer, List<LauMaterialAuditTaskBo>> entry : auditTaskPosMapByCreativeId.entrySet()) {

            List<LauMaterialAuditTaskBo> auditTaskBosOfCreativeId = entry.getValue();
            List<String> taskIdsOfCreative = auditTaskBosOfCreativeId.stream().map(t -> t.getTaskId()).collect(Collectors.toList());

            List<LauMaterialAuditTaskPo> tmpTaskPos = materialAuditTaskDbService.queryListByTaskIdsWithBlobs(taskIdsOfCreative);
            if (!CollectionUtils.isEmpty(tmpTaskPos)) {
                auditTaskPos.addAll(tmpTaskPos);
            }
        }

        // 稿件提取 avids
        List<Long> avids = auditTaskPos.stream()
                .filter(t -> Objects.equals(RiskMaterialTypeEnum.VIDEO.getCode(), t.getMaterialType()))
                .map(t -> {
                    String avid = RiskMaterialTypeEnum.VIDEO.parse(t.getMaterialContent());
                    if (!StringUtils.isEmpty(avid)) {
                        return Long.parseLong(avid);
                    }
                    return null;
                }).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<Long, CmArchiveBo> cmArchiveBoMap = cmArchiveService.queryCmArchiveBoMap(avids);

        List<LauMaterialAuditTaskEsPo> auditTaskEsPos = IMaterialAuditTaskConvertor.INSTANCE.pos2EsPos(auditTaskPos);

        Map<Integer, CreativeBo> finalCreativeBoMap = creativeBoMap;
        auditTaskEsPos.forEach(auditTaskEsPo -> {
            auditTaskEsPo.setIsUserSpaceArchive(false);

            if (needCreative) {
                // 任务需要保存创意审核状态，也会监听创意binlog 同步该状态
                CreativeBo creativeBo = finalCreativeBoMap.get(auditTaskEsPo.getCreativeId());
                if (creativeBo != null) {
                    auditTaskEsPo.setCreativeAuditStatus(creativeBo.getAuditStatus());
                }
            }

            // 是否创意中心稿件
            if (Objects.equals(auditTaskEsPo.getMaterialType(), RiskMaterialTypeEnum.VIDEO.getCode())) {
                try {
                    String avid = RiskMaterialTypeEnum.VIDEO.parse(auditTaskEsPo.getMaterialContent());
                    if (!StringUtils.isEmpty(avid)) {
                        CmArchiveBo cmArchiveBo = cmArchiveBoMap.get(Long.parseLong(avid));
                        if (cmArchiveBo != null) {
                            auditTaskEsPo.setIsUserSpaceArchive(Objects.equals(cmArchiveBo.getArchiveMode(), CmArchiveMode.USER_SPACE_VALUE));
                        }
                    }
                } catch (Exception e) {
                    log.error("syncTask2Es error, taskId={}", auditTaskEsPo.getId(), e);
                }
            }
            materialTaskEsPubService.pub(auditTaskEsPo);
        });
    }

    /**
     * 更新任务的创意审核状态同步到 es
     *
     * @param creativeBinlogBos
     */
    public void updateTaskCreativeAuditStatus2Es(List<CreativeBinlogBo> creativeBinlogBos) {
        log.info("updateTaskCreativeAuditStatus2Es, creativeBinlogBos:{}", creativeBinlogBos);

        if (CollectionUtils.isEmpty(creativeBinlogBos)) {
            return;
        }

        List<Integer> creativeIds = creativeBinlogBos.stream().map(t -> t.getCreativeId()).distinct().collect(Collectors.toList());
        Map<Integer, CreativeBinlogBo> creativeBinlogBoMap = creativeBinlogBos.stream().collect(Collectors.toMap(t -> t.getCreativeId(), t -> t, (t1, t2) -> t2));
        if (CollectionUtils.isEmpty(creativeIds)) {
            log.info("updateTaskCreativeAuditStatus2Es, creative ids is empty.");
            return;
        }

        // 查询任务
        List<LauMaterialAuditTaskPo> taskPos = materialAuditTaskDbService.queryListByCreativeIds(creativeIds);
        if (CollectionUtils.isEmpty(taskPos)) {
            log.warn("updateTaskCreativeAuditStatus2Es, task pos is empty.");
            return;
        }

        // 更新任务
        for (LauMaterialAuditTaskPo auditTaskPo : taskPos) {
            CreativeBinlogBo creativeBinlogBo = creativeBinlogBoMap.get(auditTaskPo.getCreativeId());
            if (creativeBinlogBo == null) {
                log.error("updateTaskCreativeAuditStatus2Es, creative binlog bo is null.,creativeId={}, taskId={}", auditTaskPo.getCreativeId(), auditTaskPo.getTaskId());
                continue;
            }

            // 投放端审核状态未改变不更新
            if (auditTaskPo.getCreativeAuditStatus().equals(creativeBinlogBo.getAuditStatus())) {
                continue;
            }
            LauMaterialAuditTaskPo auditTaskPoCopy = new LauMaterialAuditTaskPo();
            auditTaskPoCopy.setTaskId(auditTaskPo.getTaskId());
            auditTaskPoCopy.setCreativeAuditStatus(creativeBinlogBo.getAuditStatus());
            int count = materialAuditTaskDbService.updateTask(auditTaskPoCopy);
            log.info("updateTaskCreativeAuditStatus2Es, creative audit status changed.,creativeId={}, taskId={}, oldStatus={}, newStatus={},count={}", auditTaskPo.getCreativeId(), auditTaskPo.getTaskId(),
                    auditTaskPo.getCreativeAuditStatus(), creativeBinlogBo.getAuditStatus(), count);
        }

    }
}
