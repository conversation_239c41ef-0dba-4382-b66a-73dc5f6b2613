package com.bilibili.risk.service.refresh;

import com.bilibili.risk.dao.risk.LauMaterialAuditTaskDao;
import com.bilibili.risk.dao.risk.LauMaterialToAuditCreativeDao;
import com.bilibili.risk.databus.material_audit.OneCreativeJudgeAuditPub;
import com.bilibili.risk.service.task.MaterialAuditTaskDbService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
@RequiredArgsConstructor
public class PassHandleTypeReJudgeCreativeRefresher {

    private final LauMaterialToAuditCreativeDao lauMaterialToAuditCreativeDao;
    private final MaterialAuditTaskDbService materialAuditTaskDbService;
//    private final LauMaterialAuditTaskDao lauMaterialAuditTaskDao;
    private final OneCreativeJudgeAuditPub oneCreativeJudgeAuditPub;

    public static Set<Long> PASSED_THIRD_AUDIT_LABELS = Stream.of(668,669,670,671,672,673,674,702,703,704,705,706,845,851,853,856,861,864,865,871,882,888,889).map(Integer::longValue).collect(Collectors.toSet());

    /**
     * 刷数据
     * 逻辑：
     * 1. 查询创意，滚动查询,用 com.bilibili.risk.dao.risk.LauMaterialToAuditCreativeDao
     *  1.1 要求根据id范围去扫描表，防止慢查询
     * 2. 根据创意ids查询任务，用 com.bilibili.risk.service.task.MaterialAuditTaskDbService#queryListByCreativeIds(java.util.List)
     * 3. 该创意存在任务是有通过的标签，PASSED_THIRD_AUDIT_LABELS
     * 4. 有的话重新触发创意判定
     *
     */
    public void refresh() {

    }
}
