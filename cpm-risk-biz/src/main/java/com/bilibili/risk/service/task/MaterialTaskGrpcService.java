package com.bilibili.risk.service.task;

import com.bapis.ad.risk.material_task.*;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.risk.bo.*;
import com.bilibili.risk.convertor.IMaterialAuditTaskConvertor;
import com.bilibili.risk.exception.ExceptionCode;
import com.bilibili.risk.service.MaterialAuditLabelService;
import io.grpc.Status;
import io.grpc.stub.StreamObserver;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import pleiades.venus.starter.rpc.server.RPCService;

import java.util.List;

@RPCService
@RequiredArgsConstructor
@Slf4j
public class MaterialTaskGrpcService extends MaterialTaskServiceGrpc.MaterialTaskServiceImplBase {

    private static final String ID = "MaterialTaskGrpcService";

    private final MaterialAuditTaskEsService materialAuditTaskEsService;
    private final MaterialAuditTaskPullService materialAuditTaskPullService;
    private final MaterialAuditTaskAuditService materialAuditTaskAuditService;
    private final MaterialAuditLabelService materialAuditLabelService;

    @Override
    public void pullMaterialTask(PullMaterialTaskReq request, StreamObserver<PullMaterialTaskResp> responseObserver) {

        try {
            // 未实现
            responseObserver.onNext(PullMaterialTaskResp.newBuilder().build());
            responseObserver.onCompleted();
        } catch (IllegalArgumentException t) {
            responseObserver.onError(Status.INVALID_ARGUMENT
                    .withDescription(t.getMessage())
                    .asRuntimeException());
            log.error("{}:pullMaterialTask 失败,{}", ID, t);
        } catch (Throwable t) {
            responseObserver.onError(Status.INTERNAL
                    .withDescription(t.getMessage())
                    .asRuntimeException());
            log.error("{}:pullMaterialTask 失败,{}", ID, t);
        }
    }

}
