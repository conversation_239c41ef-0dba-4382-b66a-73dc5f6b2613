package com.bilibili.risk.service.material;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.id.enums.BizTagEnum;
import com.bilibili.id.service.SegmentService;
import com.bilibili.risk.bo.LauMaterialAuditBo;
import com.bilibili.risk.constant.DatabaseConstant;
import com.bilibili.risk.convertor.IMaterialAuditConvertor;
import com.bilibili.risk.dao.risk.LauMaterialAuditDao;
import com.bilibili.risk.dao.risk.LauMaterialAuditExtDao;
import com.bilibili.risk.enums.AuditLabelIdSpecialEnum;
import com.bilibili.risk.enums.RiskMaterialTypeEnum;
import com.bilibili.risk.enums.ShardingTableEnum;
import com.bilibili.risk.po.risk.LauMaterialAuditPo;
import com.bilibili.risk.po.risk.LauMaterialAuditPoExample;
import com.bilibili.risk.utils.MaterialTaskUtils;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class LauMaterialAuditService {

    private final LauMaterialAuditDao lauMaterialAuditDao;
    private final LauMaterialAuditExtDao lauMaterialAuditExtDao;
    private final SegmentService segmentService;

    @Deprecated
    public List<LauMaterialAuditBo> queryListByMaterialIds(List<String> materialIds) {

        if (CollectionUtils.isEmpty(materialIds)) {
            return Collections.emptyList();
        }

        LauMaterialAuditPoExample example = new LauMaterialAuditPoExample();
        example.createCriteria().andMaterialIdIn(materialIds);
        return IMaterialAuditConvertor.INSTANCE.pos2bos(lauMaterialAuditDao.selectByExample(example));
    }

    public Map<String, LauMaterialAuditBo> queryMapByMaterialIds(List<String> materialIds) {

        Map<Integer, List<String>> materialIds_partition = materialIds.stream()
                .collect(Collectors.groupingBy(e -> MaterialTaskUtils.calculateShardingKeyByMaterialId(e, ShardingTableEnum.TABLE_LAU_MATERIAL_AUDIT.getShardingAlgorithm())));

        Map<String, LauMaterialAuditBo> materialAuditBoMap = new HashMap<>();
        for (Map.Entry<Integer, List<String>> materialIdsOnePar : materialIds_partition.entrySet()) {
            List<LauMaterialAuditBo> materialAuditBos = this.queryListByMaterialIds(materialIdsOnePar.getValue());
            Map<String, LauMaterialAuditBo> map = materialAuditBos.stream().collect(Collectors.toMap(LauMaterialAuditBo::getMaterialId, t -> t));
            materialAuditBoMap.putAll(map);
        }
        return materialAuditBoMap;
    }

    public LauMaterialAuditBo fetchByMaterialId(String materialId) {
        LauMaterialAuditPo po = lauMaterialAuditExtDao.selectByPrimaryKey(materialId);

        return IMaterialAuditConvertor.INSTANCE.po2bo(po);
    }

    // 根据md5查询素材列表
    public List<LauMaterialAuditBo> queryLauMaterialAuditBosByMd5s(List<String> md5s, Integer materialType) {
        if (CollectionUtils.isEmpty(md5s)) {
            return new ArrayList<>();
        }
        LauMaterialAuditPoExample example = new LauMaterialAuditPoExample();
        LauMaterialAuditPoExample.Criteria criteria = example.createCriteria().andMaterialMd5In(md5s);

        if (Utils.isPositive(materialType)) {
            criteria.andMaterialTypeEqualTo(materialType);
        }
        List<LauMaterialAuditPo> materialPos = lauMaterialAuditDao.selectByExample(example);
        return IMaterialAuditConvertor.INSTANCE.pos2bos(materialPos);
    }

    public Map<String, List<LauMaterialAuditBo>> queryLauMaterialAuditBoMapByMd5s(List<String> md5s) {
        if (CollectionUtils.isEmpty(md5s)) {
            return new HashMap<>();
        }
        List<LauMaterialAuditBo> materialAuditBos = this.queryLauMaterialAuditBosByMd5s(md5s, null);
        return materialAuditBos.stream().collect(Collectors.groupingBy(t -> t.getMaterialMd5()));
    }

    public LauMaterialAuditBo fetchLauMaterialAuditBosByMd5(String md5, Integer materialType) {
        if (StringUtils.isBlank(md5) || null == materialType) {
            return null;
        }

        LauMaterialAuditPoExample example = new LauMaterialAuditPoExample();
        example.createCriteria().andMaterialMd5EqualTo(md5).andMaterialTypeEqualTo(materialType);
        List<LauMaterialAuditPo> materialPos = lauMaterialAuditDao.selectByExample(example);
        List<LauMaterialAuditBo> materialAuditBos = IMaterialAuditConvertor.INSTANCE.pos2bos(materialPos);
        if (CollectionUtils.isEmpty(materialAuditBos)) {
            return null;
        }
        return materialAuditBos.get(0);
    }

    /**
     * 注意坑：以防素材其他字段被更新掉
     *
     * @param materialAuditBos
     * @return
     */
    public Integer updateMaterialAuditBos(List<LauMaterialAuditBo> materialAuditBos, Boolean needUpdateOtherFields) {
        if (CollectionUtils.isEmpty(materialAuditBos)) {
            return 0;
        }

        // 有bug, 会生成空字符串的值，导致素材被更新掉
        List<LauMaterialAuditPo> auditPos = new ArrayList<>();
        if (needUpdateOtherFields != null && needUpdateOtherFields) {
            auditPos = IMaterialAuditConvertor.INSTANCE.bos2pos(materialAuditBos);
        } else {
            auditPos = IMaterialAuditConvertor.INSTANCE.bos2pos2(materialAuditBos);
        }

        auditPos = auditPos.stream().sorted(Comparator.comparing(LauMaterialAuditPo::getMaterialId)).collect(Collectors.toList());
        // 清楚不更新属性
        Integer count = 0;
        Map<Integer, List<LauMaterialAuditPo>> materialMap = auditPos.stream()
                .collect(Collectors.groupingBy(e -> MaterialTaskUtils.calculateShardingKeyByMaterialId(e.getMaterialId(), ShardingTableEnum.TABLE_LAU_MATERIAL_AUDIT.getShardingAlgorithm())));
        auditPos.forEach(this::clearForUpdate);

        // 未来可以用批量提交
        for (Map.Entry<Integer, List<LauMaterialAuditPo>> entry : materialMap.entrySet()) {
            for (LauMaterialAuditPo lauMaterialAuditPo : entry.getValue()) {
                count += lauMaterialAuditExtDao.updateByPrimaryKeySelective(lauMaterialAuditPo);
            }
        }
        return count;
    }

    public Integer updateValidIfDeleted(LauMaterialAuditBo materialAuditBo) {
        if (materialAuditBo == null) {
            return 0;
        }

        LauMaterialAuditPo lauMaterialAuditPo = LauMaterialAuditPo.builder()
                .mtime(materialAuditBo.getMtime()).isDeleted(IsDeleted.VALID.getCode()).build();

        LauMaterialAuditPoExample example = new LauMaterialAuditPoExample();

        example.createCriteria().andMaterialIdEqualTo(materialAuditBo.getMaterialId())
                .andIsDeletedEqualTo(IsDeleted.DELETED.getCode());

        return lauMaterialAuditDao.updateByExampleSelective(lauMaterialAuditPo, example);
    }

    public int updateSelectiveByMaterialId(LauMaterialAuditBo materialAuditBo){
        if (materialAuditBo == null) {
            return 0;
        }

        LauMaterialAuditPo lauMaterialAuditPo = IMaterialAuditConvertor.INSTANCE.bo2po2(materialAuditBo);

        LauMaterialAuditPoExample example = new LauMaterialAuditPoExample();

        example.createCriteria().andMaterialIdEqualTo(materialAuditBo.getMaterialId());

        return lauMaterialAuditDao.updateByExampleSelective(lauMaterialAuditPo, example);
    }

    private void clearForUpdate(LauMaterialAuditPo po) {
        po.setId(null);
        po.setMaterialMd5(null);
        po.setMaterialType(null);
        po.setMaterialContent(null);
        po.setRawContent(null);
        po.setExtra(null);
        po.setCtime(null);
    }



    public Integer updateMaterialAuditPo(LauMaterialAuditPo lauMaterialAuditPo) {
        if (lauMaterialAuditPo == null) {
            return 0;
        }

        return lauMaterialAuditDao.insertUpdateSelective(lauMaterialAuditPo);
    }

    /**
     * 生成素材的md5，并保存素材
     *
     * @param materialBos
     * @return
     */
    public Map<String, LauMaterialAuditBo> genMd5AndSaveMaterials(List<LauMaterialAuditBo> materialBos) {
        if (CollectionUtils.isEmpty(materialBos)) {
            return Collections.emptyMap();
        }

        for (LauMaterialAuditBo materialBo : materialBos) {
            Assert.isTrue(StringUtils.isNotEmpty(materialBo.getMaterialContent()), "material content is required");

            // 图片的已经通过上传页面处理了，不需要重新生成 md5
            if (StringUtils.isEmpty(materialBo.getMaterialMd5())) {
                materialBo.setMaterialMd5(DigestUtils.md5Hex(materialBo.getMaterialContent()));
            }
        }
        return saveIfNotExists(materialBos);
    }

    /**
     * 这个方法是素材库的核心去重机制，确保相同内容的素材只保存一份，同时维护素材的唯一标识
     *
     * @param materialBos
     * @return 返回key=md5-type, 的map
     */
    public Map<String, LauMaterialAuditBo> saveIfNotExists(List<LauMaterialAuditBo> materialBos) {
        log.info("saveIfNotExists materialBos: {}", materialBos);
        if (CollectionUtils.isEmpty(materialBos)) {
            return Collections.emptyMap();
        }

        // 校验
        Assert.isTrue(materialBos.size() <= 200, "materialBos size must be less than 200");
        for (LauMaterialAuditBo materialBo : materialBos) {
            Assert.isTrue(StringUtils.isNotEmpty(materialBo.getMaterialMd5()), "materialMd5 is required");
            Assert.isTrue(Utils.isPositive(materialBo.getMaterialType()), "materialType is required");
            // 因为图片md5需要基于上传后的二进制文件生成
//            Assert.isTrue(!RiskMaterialTypeEnum.IMAGE.getCode().equals(materialBo.getMaterialType()), "image暂不支持");
        }

        // 防止死锁！！ 素材有序插入
        materialBos = materialBos.stream()
                .sorted(Comparator.comparing(LauMaterialAuditBo::getMaterialMd5).thenComparing(LauMaterialAuditBo::getMaterialType))
                .collect(Collectors.toList());

        Map<String, LauMaterialAuditBo> finalAuditBoMap = new HashMap<>(); // 收集最终的素材 map
        Map<Integer, List<LauMaterialAuditBo>> materialAuditBosByMd5 = materialBos.stream()
                .collect(Collectors.groupingBy(t -> MaterialTaskUtils.calculateShardingKeyByMd5(t.getMaterialMd5(), ShardingTableEnum.TABLE_LAU_MATERIAL_AUDIT.getShardingAlgorithm())));
        Iterator<Map.Entry<Integer, List<LauMaterialAuditBo>>> itr = materialAuditBosByMd5.entrySet().iterator();
        while (itr.hasNext()) {
            Map.Entry<Integer, List<LauMaterialAuditBo>> next = itr.next();
            // 批量查询
            List<LauMaterialAuditBo> materialAuditBoList = next.getValue();

            // 如果这里已经有 materialId不需要查询与新增了，目前场景是，投放点推送的素材，会先生成 id
            Set<String> existKeys = materialAuditBoList.stream().filter(e -> StringUtils.isNotBlank(e.getMaterialId()))
                    .map(e -> MaterialTaskUtils.genKey(e.getMaterialMd5(), e.getMaterialType())).collect(Collectors.toSet());
            // 那些没有 materialId 的素材，可能是新同步的素材
            List<String> md5s = materialAuditBoList.stream()
                    .filter(e -> !existKeys.contains(MaterialTaskUtils.genKey(e.getMaterialMd5(), e.getMaterialType())))
                    .map(LauMaterialAuditBo::getMaterialMd5).distinct().collect(Collectors.toList());

            // 注意，同md5可能有多条
            // 查出来的是没有materialId的素材
            Map<String, List<LauMaterialAuditBo>> materialMapByMd5 = this.queryLauMaterialAuditBoMapByMd5s(md5s);
            // 1. 首先从 materialAuditBoList 提取所有需要匹配的 (md5, type) 对
            Set<Pair<String, Integer>> requiredMd5AndTypes = materialAuditBoList.stream()
                    .map(bo -> Pair.of(bo.getMaterialMd5(), bo.getMaterialType()))
                    .collect(Collectors.toSet());

            // 2. 从 materialMapByMd5 的所有 values 中筛选符合条件的记录
            List<LauMaterialAuditBo> existMaterialBos = materialMapByMd5.values().stream()
                    .flatMap(List::stream) // 将所有List合并成一个Stream
                    .filter(bo -> requiredMd5AndTypes.contains(
                            Pair.of(bo.getMaterialMd5(), bo.getMaterialType())))
                    .collect(Collectors.toList());

            List<LauMaterialAuditBo> needAddMaterialBos = new ArrayList<>();
            Map<String, LauMaterialAuditBo> existMaterialPoMap = existMaterialBos.stream().collect(Collectors.toMap(t -> MaterialTaskUtils.genKey(t.getMaterialMd5(), t.getMaterialType()), t -> t));
            for (LauMaterialAuditBo materialBo : materialAuditBoList) {
                String key = MaterialTaskUtils.genKey(materialBo.getMaterialMd5(), materialBo.getMaterialType());
                // 3 存在的素材判断
                if (existMaterialPoMap.containsKey(key)) {
                    LauMaterialAuditBo existMaterialAuditBo = existMaterialPoMap.get(key);
                    finalAuditBoMap.put(key, existMaterialAuditBo);
                    continue;
                } else if (existKeys.contains(key)) {
                    finalAuditBoMap.put(key, materialBo);
                    continue;
                }
                needAddMaterialBos.add(materialBo);
            }

            log.info("saveIfNotExists needAddMaterialBos: {}", needAddMaterialBos);
            // todo simer: 批量新增改造 ==
            if (!CollectionUtils.isEmpty(needAddMaterialBos)) {
                List<LauMaterialAuditPo> lauMaterialAuditPos = IMaterialAuditConvertor.INSTANCE.bos2pos(needAddMaterialBos);
                // 初始化不可见的
                for (LauMaterialAuditPo lauMaterialAuditPo : lauMaterialAuditPos) {
                    // 落地页组，同步外部的审核状态，可能在插入时已经有审核结果
                    // 其他素材，等待最终判断时，依赖修改为 valid的任务，决定复用结果
                    if (RiskMaterialTypeEnum.PAGE_GROUP_PAGE == RiskMaterialTypeEnum.getByCode(lauMaterialAuditPo.getMaterialType())
                            && AuditLabelIdSpecialEnum.pageGroupResult().contains(lauMaterialAuditPo.getAuditLabelThirdId())) {
                        lauMaterialAuditPo.setIsDeleted(IsDeleted.VALID.getCode());
                    } else {
                        lauMaterialAuditPo.setIsDeleted(IsDeleted.DELETED.getCode());
                    }
                }

                // 排序下防止死锁
                lauMaterialAuditPos = lauMaterialAuditPos.stream()
                        .sorted(Comparator.comparing(LauMaterialAuditPo::getMaterialMd5).thenComparing(LauMaterialAuditPo::getMaterialType))
                        .collect(Collectors.toList());
                for (LauMaterialAuditPo lauMaterialAuditPo : lauMaterialAuditPos) {
                    lauMaterialAuditPo.setMaterialId(segmentService.getId(BizTagEnum.MATERIAL, lauMaterialAuditPo.getMaterialMd5()));

                    // 这里插入违反唯一约束，即已经占位，查出来
                    try {
                        lauMaterialAuditDao.insertSelective(lauMaterialAuditPo);
                        log.info("saveIfNotExists[新同步的素材,此时不可见], materialAuditBo: {}", JSON.toJSONString(lauMaterialAuditPo));
                    } catch (DuplicateKeyException e) {
                        LauMaterialAuditPoExample example = new LauMaterialAuditPoExample();
                        example.createCriteria().andMaterialMd5EqualTo(lauMaterialAuditPo.getMaterialMd5())
                                .andMaterialTypeEqualTo(lauMaterialAuditPo.getMaterialType());

                        // 由于 mvcc 机制，不会读到新版本的数据，所以两个事务插入冲突，此处第二个事务可能会查不到
                        // 如果此处异常多，影响逻辑，可以在 CreativeMaterialPushToAuditService.initRiskMaterialIfNeed 处提前初始化落地页组&story 相关素材
                        lauMaterialAuditPo = lauMaterialAuditDao.selectByExample(example).get(0);
                        log.info("saveIfNotExists 冲突, 查询到的: {}", JSON.toJSONString(lauMaterialAuditPo));
                    }
                    LauMaterialAuditBo materialAuditBo = IMaterialAuditConvertor.INSTANCE.po2bo(lauMaterialAuditPo);
                    finalAuditBoMap.put(MaterialTaskUtils.genKey(materialAuditBo.getMaterialMd5(), materialAuditBo.getMaterialType()), materialAuditBo);
                }
            }
        }
        return finalAuditBoMap;
    }

    public String genMd5ByBodyId(String bodyId, RiskMaterialTypeEnum materialType) {

        if (StringUtils.isEmpty(bodyId)) {
            return null;
        }
        String materialContent = materialType.generateMaterialContent(bodyId);
        return DigestUtils.md5Hex(materialContent);
    }

    public List<LauMaterialAuditBo> scrollByMaterialIdWithShadingNo(Long id, int pageSize, int shadingNo) {
        LauMaterialAuditPoExample example = new LauMaterialAuditPoExample();
        LauMaterialAuditPoExample.Criteria criteria = example.createCriteria();
        if(null != id){
            criteria.andIdGreaterThan(id);
        }

        example.or().andMaterialContentEqualTo("");
        example.or().andRawContentEqualTo("");
        example.setOrderByClause(" id asc");
        example.setLimit(pageSize);
        List<LauMaterialAuditPo> poList = lauMaterialAuditExtDao.selectByExampleWithShardingNo(shadingNo, example);

        return IMaterialAuditConvertor.INSTANCE.pos2bos(poList);
    }
}
