package com.bilibili.risk.service.metrics;

import io.opentelemetry.api.common.Attributes;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.net.InetAddress;
import java.net.UnknownHostException;

@Slf4j
@Service
@RequiredArgsConstructor
public class MetricsReportService {

    private static final String METRIC_DATA_UNKNOWN = "UNKNOWN";

    @Autowired
    private CustomMetrics customMetrics;
    @Autowired
    private EnvService envService;

    public void reportMetricsCount(MetricDataHolder data) {
        String instanceName = getInstanceName();
        String env = envService.getEnv();

        // 上报请求计数 (Counter)
        Attributes countAttributes = Attributes.builder()
                .put("name", data.name)
                .put("domain_type", data.domainType)
                .put("type", data.type)
                .put("code", data.code)
                .put("msg", data.msg)
                .put("instance_name", instanceName)
                .put("env", env)
                .put("username", data.getUsername())
                .put("materialTypeName", data.getMaterialTypeName())
                .put("size", data.getSize())
                .put("subSize", data.getSubSize())
                .put("pullStrategy", data.getPullStrategy())
                .put("isProgram", data.getIsProgram())
                .put("success", data.getSuccess())
                .build();
        customMetrics.count(RiskMetricsImpl.METRIC_KEY_BIZ_REQUEST_COUNT, countAttributes);
    }

    public void reportMetricCostTime(MetricDataHolder data, long durationMs) {
        String instanceName = getInstanceName();
        String env = envService.getEnv();

        // 上报请求延迟 (Histogram)
        Attributes latencyAttributes = Attributes.builder()
                .put("name", data.name)
                .put("code", data.code)
                .put("msg", data.msg)
                .put("instance_name", instanceName)
                .put("env", env)
                .put("username", data.getUsername())
                .put("materialTypeName", data.getMaterialTypeName())
                .put("size", data.getSize())
                .put("subSize", data.getSubSize())
                .put("pullStrategy", data.getPullStrategy())
                .put("isProgram", data.getIsProgram())
                .put("success", data.getSuccess())
                .build();
        customMetrics.histogram(RiskMetricsImpl.METRIC_KEY_BIZ_LATENCY, durationMs, latencyAttributes);
    }

    /**
     * 获取当前服务实例的主机名
     *
     * @return 主机名，获取失败时返回 "UNKNOWN"
     */
    private String getInstanceName() {
        try {
            return InetAddress.getLocalHost().getHostName();
        } catch (UnknownHostException e) {
            log.error("获取主机名失败", e);
            return METRIC_DATA_UNKNOWN;
        }
    }

}
