package com.bilibili.risk.service.task;

import com.bilibili.risk.bo.LauMaterialAuditBo;
import com.bilibili.risk.bo.MaterialTaskAuditContext;
import com.bilibili.risk.constant.DatabaseConstant;
import com.bilibili.risk.service.material.LauMaterialAuditService;
import com.bilibili.risk.service.task.pull.RiskMaterialTaskDoingService;
import com.dianping.cat.Cat;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Service
public class TaskAuditExecuteDbUpdateService {

    private final MaterialAuditTaskDbService materialAuditTaskDbService;
    private final LauMaterialAuditService lauMaterialAuditService;
    private final RiskMaterialTaskDoingService riskMaterialTaskDoingService;

    /**
     * 要求所有更新这两表(素材表和任务表)处顺序必须一致，否则可能出现死锁
     * @param context
     */
    @Transactional(rollbackFor = Exception.class, transactionManager = DatabaseConstant.RISK_TRANSACTION_MANAGER)
    public void doSaveDbData(MaterialTaskAuditContext context) {
        // 1.批量更新素材
        List<LauMaterialAuditBo> materialAuditBos = context.getMaterialAuditBoMap().values().stream().collect(Collectors.toList());
        lauMaterialAuditService.updateMaterialAuditBos(materialAuditBos, true);
        Cat.logEvent("batchAuditTask", "doSaveDbData.updateMaterialAuditBos end");

        // 2.批量更新任务底表，乐观锁
        materialAuditTaskDbService.updateMaterialAuditTaskBosOptimistic(context.getTaskUpdateBos());
        Cat.logEvent("batchAuditTask", "doSaveDbData.updateMaterialAuditTaskBosOptimistic end");

        // 删除doing表数据
        List<String> taskIds = context.getTaskUpdateBos().stream().map(t -> t.getTaskId()).collect(Collectors.toList());
        riskMaterialTaskDoingService.deleteByTaskIds(taskIds);
    }
}
