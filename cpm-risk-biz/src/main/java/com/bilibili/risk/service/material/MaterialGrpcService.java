package com.bilibili.risk.service.material;

import com.bapis.ad.risk.material.MaterialServiceGrpc;
import com.bapis.ad.risk.material.QueryMaterialCreativesReq;
import com.bapis.ad.risk.material.QueryMaterialCreativesResp;
import com.bapis.ad.risk.material.SingleQueryMaterialCreativesResp;
import com.bilibili.risk.bo.QueryMaterialCreativeBo;
import com.bilibili.risk.convertor.ICreativeConvertor;
import com.bilibili.risk.service.creative.LauMaterialToAuditCreativeService;
import io.grpc.Status;
import io.grpc.stub.StreamObserver;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import pleiades.venus.starter.rpc.server.RPCService;

import java.util.List;

@RPCService
@RequiredArgsConstructor
@Slf4j
public class MaterialGrpcService extends MaterialServiceGrpc.MaterialServiceImplBase {

    private static final String ID = "MaterialGrpcService";
    private final LauMaterialToAuditCreativeService lauMaterialToAuditCreativeService;

    @Override
    public void queryMaterialCreatives(QueryMaterialCreativesReq request, StreamObserver<QueryMaterialCreativesResp> responseObserver) {
        try {
            List<QueryMaterialCreativeBo> queryMaterialCreativeBos = lauMaterialToAuditCreativeService.queryMaterialCreatives(request.getCreativeIdsList());
            List<SingleQueryMaterialCreativesResp> materialCreativesResps = ICreativeConvertor.INSTANCE.bos2grpcs(queryMaterialCreativeBos);
            responseObserver.onNext(QueryMaterialCreativesResp.newBuilder().addAllData(materialCreativesResps).build());
            responseObserver.onCompleted();
        } catch (IllegalArgumentException t) {
            responseObserver.onError(Status.INVALID_ARGUMENT
                    .withDescription(t.getMessage())
                    .asRuntimeException());
            log.error("{}:queryMaterialCreatives 失败,{}", ID, t);
        } catch (Throwable t) {
            responseObserver.onError(Status.INTERNAL
                    .withDescription(t.getMessage())
                    .asRuntimeException());
            log.error("{}:queryMaterialCreatives 失败,{}", ID, t);
        }
    }
}
