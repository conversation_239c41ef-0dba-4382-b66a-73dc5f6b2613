package com.bilibili.risk.service.task.pull;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.rbac.api.dto.UserDto;
import com.bilibili.risk.bo.*;
import com.bilibili.risk.bo.msg.MaterialTaskTimeoutMsgBo;
import com.bilibili.risk.constant.DatabaseConstant;
import com.bilibili.risk.constant.RiskConstants;
import com.bilibili.risk.convertor.IMaterialAuditTaskConvertor;
import com.bilibili.risk.enums.LogTypeEnum;
import com.bilibili.risk.enums.MaterialTaskPullStrategyEnum;
import com.bilibili.risk.enums.MaterialTaskStatusEnum;
import com.bilibili.risk.enums.OperationTypeEnum;
import com.bilibili.risk.po.es.LauMaterialAuditTaskEsPo;
import com.bilibili.risk.po.risk.LauMaterialAuditTaskPo;
import com.bilibili.risk.po.risk.RiskMaterialTaskDoingPo;
import com.bilibili.risk.utils.DiffLogUtils;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Message;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.search.sort.SortOrder;
import org.modelmapper.internal.util.Assert;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
public class MaterialAuditTaskPullStrategyByRuleOldestTime extends AbstractMaterialAuditTaskPullStrategy {

    /**
     * 根据规则[by time asc]拉取一批任务
     *
     * @param pullQueryBo
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class, transactionManager = DatabaseConstant.RISK_TRANSACTION_MANAGER)
    public List<MaterialAuditTaskEsBo> pullOneBatchMaterialAuditTasks(MaterialAuditTaskPullQueryBo pullQueryBo) {
        log.info("pullOneBatchMaterialAuditTasks[根据规则-按时间], pullQueryBo:{}", JSON.toJSONString(pullQueryBo));
        Assert.notNull(pullQueryBo, "pullQueryBo is null");
        Assert.notNull(pullQueryBo.getPullStrategy(), "pullStrategy is null");

        Assert.isTrue(Objects.equals(pullQueryBo.getPullStrategy(), MaterialTaskPullStrategyEnum.BY_RULE_CTIME_ASC.getKey()), "pullStrategy is not BY_RULE_CTIME_ASC");
        Assert.isTrue(Utils.isPositive(pullQueryBo.getRuleId()), "ruleId 未传");
        Assert.isTrue(!StringUtils.isEmpty(pullQueryBo.getUsername()), "username 未传");

        MaterialAuditRuleInfoBo ruleInfoBo = materialAuditRuleService.fetchMaterialAuditRule(pullQueryBo.getRuleId());
        Assert.notNull(ruleInfoBo, "规则不存在！");
        Assert.isTrue(!CollectionUtils.isEmpty(ruleInfoBo.getRolesIds()), "规则未绑定角色！");

        List<LauMaterialAuditQueueBo> bindQueues = ruleInfoBo.getBindQueues();
        Assert.isTrue(!CollectionUtils.isEmpty(bindQueues), "规则未绑定队列！");
        for (LauMaterialAuditQueueBo bindQueue : bindQueues) {
            Assert.isTrue(!bizConfig.getSpecialQueueIds().contains(bindQueue.getId()), "等待队列和兜底队列的任务不允许领取！");
        }
        List<Long> bindQueueIds = bindQueues.stream().map(t -> t.getId()).collect(Collectors.toList());
        List<Long> bindQueueIdsLong = bindQueues.stream().map(t -> t.getId()).collect(Collectors.toList());
        Map<Long, LauMaterialAuditQueueBo> auditQueueBoMap = materialAuditQueueService.queryMapByIds(bindQueueIdsLong);

        // 规则角色权限
        checkRole(pullQueryBo, ruleInfoBo);
        Cat.logEvent("pullOneBatchMaterialAuditTasks", "checkRole end");

        // 预拉取不走这里，直接拉取游离的，因为es延迟
        if (!Utils.is(pullQueryBo.isPrePull())) {
            // 规则已经分配了任务，直接返回
            List<RiskMaterialTaskDoingPo> riskMaterialTaskDoingPos = riskMaterialTaskDoingService.queryByUsernameAndQueueIds(pullQueryBo.getUsername(), bindQueueIds);
            if (!CollectionUtils.isEmpty(riskMaterialTaskDoingPos)) {
                List<String> taskIds = riskMaterialTaskDoingPos.stream().map(t -> t.getTaskId()).distinct().collect(Collectors.toList());

                // redis 过滤，因为es延迟问题
//            redisFilterJustAuditTasks(notCompleteTaskPageResult);

                // 审核出结果的需要过滤
                List<LauMaterialAuditTaskEsPo> esPos = queryDoingTasks(taskIds);

                // 多个batch的话，取第一个group的
                List<LauMaterialAuditTaskEsPo> notCompleteTaskEsPos = fetchOneBatchIfExistManyBatchDoing(esPos);
                log.info("pullOneBatchMaterialAuditTasks[已经分配了任务，直接返回]，size:{}", esPos.size());

                if (!CollectionUtils.isEmpty(notCompleteTaskEsPos)) {
                    LauMaterialAuditQueueBo auditQueueBo = auditQueueBoMap.getOrDefault(notCompleteTaskEsPos.get(0).getQueueId(), LauMaterialAuditQueueBo.builder().build());
                    if (notCompleteTaskEsPos.size() > auditQueueBo.getPullNum()) {
                        notCompleteTaskEsPos = notCompleteTaskEsPos.subList(0, auditQueueBo.getPullNum().intValue());
                        log.info("pullOneBatchMaterialAuditTasks[超过队列pullNum，截取]，size:{}", notCompleteTaskEsPos.size());
                    }
                    Long timeoutMin = materialTaskUtils.calculateTimeoutMin(auditQueueBo.getTaskTimeout());

                    List<MaterialAuditTaskEsBo> auditTaskEsBos = IMaterialAuditTaskConvertor.INSTANCE.pos2bos(notCompleteTaskEsPos);
                    // 非预拉取，则异步拉取下一批
                    if (!pullQueryBo.isPrePull()) {
                        MaterialAuditTaskPullQueryBo prePullQueryBo = IMaterialAuditTaskConvertor.INSTANCE.copy(pullQueryBo);
                        prePullQueryBo.setPrePull(true);
                        prePullQueryBo.setTaskIds(auditTaskEsBos.stream().map(t -> t.getId()).collect(Collectors.toList()));
                        materialTaskPrePullMsgPub.pub(prePullQueryBo);
                    }
                    // 超时任务处理延迟消息
                    materialTaskTimeoutPub.pub(MaterialTaskTimeoutMsgBo.builder()
                            .batchNo(notCompleteTaskEsPos.get(0).getReceiveBatchNo())
                            .timeoutMins(timeoutMin)
                            .build());
                    return auditTaskEsBos;
                }
            }
        }
        Cat.logEvent("pullOneBatchMaterialAuditTasks", "获取已分配任务end");

        // 从Redis获取该队列已完成的任务ID列表，用来拉取的时候not in
        Set<String> queuesJustCompleteTaskIdsFromRedis = pullRedisZsetService.getQueuesJustCompleteTaskIdsFromRedis(pullQueryBo.getRuleId(), bindQueueIdsLong);
        if (!CollectionUtils.isEmpty(queuesJustCompleteTaskIdsFromRedis)) {
            pullQueryBo.setTaskIds(queuesJustCompleteTaskIdsFromRedis.stream().collect(Collectors.toList()));
        }

        // 没有分配任务，拉取一批任务
        MaterialAuditTaskCommonQueryBo pullOneBatchQueryBo = MaterialAuditTaskCommonQueryBo.builder()
                .queueIds(bindQueueIds)
                .statusList(Arrays.asList(MaterialTaskStatusEnum.FREE.getCode())) // 游离
                .auditQueueBoMap(auditQueueBoMap)
                .excludeTaskIds(pullQueryBo.getTaskIds())
                .sortField(RiskConstants.SORT_FIELD_ENTER_AUDIT_TIME)
                .sortOrder(SortOrder.ASC)
                .build();
        List<LauMaterialAuditTaskEsPo> auditTaskEsPosByAccount = materialAuditTaskEsService.queryAuditTasksAggAccountByQueues(pullOneBatchQueryBo);
        List<String> taskIds = auditTaskEsPosByAccount.stream().map(t -> t.getId()).collect(Collectors.toList());
        log.info("pullOneBatchMaterialAuditTasks[拉取游离任务]，size:{}, taskIds:{}", auditTaskEsPosByAccount.size(), taskIds);
        Cat.logEvent("pullOneBatchMaterialAuditTasks", "获取游离任务end", Message.SUCCESS, "taskSize=" + auditTaskEsPosByAccount.size());

        if (!CollectionUtils.isEmpty(auditTaskEsPosByAccount)) {
            // 查询db任务
            Map<String, LauMaterialAuditTaskPo> auditTask00PoMap = materialAuditTaskDbService.queryMapByTaskIds(taskIds);
            Cat.logEvent("pullOneBatchMaterialAuditTasks", "查询db任务数据end", Message.SUCCESS, "taskSize=" + taskIds.size());

            // 乐观锁更新准备参数
            String batchNo = snowFlakeNoService.generateNo();
            List<LauMaterialAuditTaskUpdateBo> auditTaskUpdateBos = buildMaterialTaskUpdateBos(pullQueryBo, auditTaskEsPosByAccount, auditTask00PoMap, batchNo);
            Set<String> candidateTaskIdSet = auditTaskUpdateBos.stream().map(t -> t.getTaskId()).collect(Collectors.toSet());

            // 乐观锁更新
            Integer count = materialAuditTaskDbService.updateMaterialAuditTaskBosOptimistic(auditTaskUpdateBos);
            Cat.logEvent("pullOneBatchMaterialAuditTasks", "修改db任务数据end", Message.SUCCESS, "taskSize=" + auditTaskUpdateBos.size());

            // 更新数量小于拉取数量，说明有任务被人领取了
            Map<String, LauMaterialAuditTaskPo> newAuditTaskPoMap = null;
            if (count < taskIds.size()) {
                newAuditTaskPoMap = materialAuditTaskDbService.queryMapByTaskIds(taskIds);
                Cat.logEvent("pullOneBatchMaterialAuditTasks", "并发了重新查询任务数据end", Message.SUCCESS, "taskSize=" + taskIds.size());
            }
            // 过滤一下，防止并发领取情况
            for (LauMaterialAuditTaskUpdateBo updateBo : auditTaskUpdateBos) {
                LauMaterialAuditTaskPo auditTaskPo = null;
                if (newAuditTaskPoMap != null) {
                    auditTaskPo = newAuditTaskPoMap.get(updateBo.getTaskId());
                    if (auditTaskPo == null) {
                        log.error("pullOneBatchMaterialAuditTasks[拉取任务失败，db中没有任务]，taskId:{}", updateBo.getTaskId());
                        candidateTaskIdSet.remove(updateBo.getTaskId());
                        continue;
                    }
                    if (!Objects.equals(auditTaskPo.getReceiveBatchNo(), batchNo)) {
                        log.info("pullOneBatchMaterialAuditTasks[任务已被被人拉取]，taskId:{}, batchNo:{},acceptName={}", updateBo.getTaskId(), batchNo, auditTaskPo.getAcceptName());
                        candidateTaskIdSet.remove(updateBo.getTaskId());
                        continue;
                    }
                }


                LauMaterialAuditTaskUpdateBo oldUpdateBo = IMaterialAuditTaskConvertor.INSTANCE.po2UpdateBo(auditTaskPo);
                String diffStr = DiffLogUtils.generateDiffField(oldUpdateBo, updateBo, "任务对象");

                // 操作日志
                MaterialAuditTaskOperationLogBo logBo = MaterialAuditTaskOperationLogBo.builder()
                        .objId(updateBo.getTaskId())
                        .operationType(OperationTypeEnum.ACCEPT.getCode())
                        .operatorUsername(pullQueryBo.getUsername())
                        .remark(JSON.toJSONString(updateBo))
                        .diff(diffStr)
                        .type(LogTypeEnum.TASK.getCode())
                        .systemType(RiskConstants.SYSTEM_TYPE_RISK)
                        .ctime(System.currentTimeMillis())
                        .build();
                operationLogService.saveLog(logBo);
            }

            // 过滤掉已经被领取的任务
            auditTaskEsPosByAccount = auditTaskEsPosByAccount.stream().filter(t -> candidateTaskIdSet.contains(t.getId())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(auditTaskEsPosByAccount)) {
                // 保存到doing表
                List<RiskMaterialTaskDoingPo> doingPos = new ArrayList<>();
                for (LauMaterialAuditTaskEsPo taskEsPo : auditTaskEsPosByAccount) {
                    Timestamp now = Utils.getNow();
                    RiskMaterialTaskDoingPo doingPo = RiskMaterialTaskDoingPo.builder()
                            .taskId(taskEsPo.getId())
                            .username(pullQueryBo.getUsername())
                            .queueId(taskEsPo.getQueueId())
                            .receiveBatchNo(taskEsPo.getReceiveBatchNo())
                            .ctime(now)
                            .mtime(now)
                            .build();
                    doingPos.add(doingPo);
                }
                riskMaterialTaskDoingService.insert(doingPos);

                LauMaterialAuditQueueBo auditQueueBo = auditQueueBoMap.getOrDefault(auditTaskEsPosByAccount.get(0).getQueueId(), LauMaterialAuditQueueBo.builder().build());
                Long timeoutEndTimestamp = materialTaskUtils.calculateTimeoutMin(auditQueueBo.getTaskTimeout());

                // 超时任务处理延迟消息
                materialTaskTimeoutPub.pub(MaterialTaskTimeoutMsgBo.builder()
                        .batchNo(batchNo)
                        .timeoutMins(timeoutEndTimestamp)
                        .build());
            }
        }
        List<MaterialAuditTaskEsBo> auditTaskEsBos = IMaterialAuditTaskConvertor.INSTANCE.pos2bos(auditTaskEsPosByAccount);

        // 拉取的任务id放入redis(解决拉取任务时，es数据延迟的问题)
        pullRedisZsetService.cacheCompletedTaskIds(IMaterialAuditTaskConvertor.INSTANCE.esPos2UpdateBos(auditTaskEsBos));

        // 非预拉取，则异步拉取下一批
        if (!pullQueryBo.isPrePull()) {
            MaterialAuditTaskPullQueryBo prePullQueryBo = IMaterialAuditTaskConvertor.INSTANCE.copy(pullQueryBo);
            prePullQueryBo.setPrePull(true);
            prePullQueryBo.setTaskIds(auditTaskEsBos.stream().map(t -> t.getId()).collect(Collectors.toList()));
            materialTaskPrePullMsgPub.pub(prePullQueryBo);
        }
        return auditTaskEsBos;
    }


    private void checkRole(MaterialAuditTaskPullQueryBo pullQueryBo, MaterialAuditRuleInfoBo ruleInfoBo) {
        if (Objects.equals(pullQueryBo.getPullStrategy(), MaterialTaskPullStrategyEnum.BY_RULE_CTIME_ASC.getKey()) && !pullQueryBo.isSkipRoleCheck()) {
            UserDto user = userService.getUserByUserName(bizConfig.getTenantId(), pullQueryBo.getUsername());
            Assert.notNull(user, "用户不存在");

            List<Integer> roleIdsOfUser = user.getRoles().stream().map(r -> r.getId()).distinct().collect(Collectors.toList());

            // 业务管理员直接可以操作
            if (roleIdsOfUser.contains(bizConfig.getBusinessAdminRoleId())) {
                return;
            }

            // 非业务管理员，要求用户roleIds与队列roleIds存在交集
            List<Integer> allRoleIds = Stream.concat(ruleInfoBo.getRolesIds().stream(), roleIdsOfUser.stream()).collect(Collectors.toList());
            List<Integer> intersectionRoleIds = ruleInfoBo.getRolesIds().stream()
                    .filter(roleIdsOfUser::contains)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(intersectionRoleIds)) {
                Map<Integer, RoleBo> roleBoMap = riskRoleService.queryRoleBoMapByIds(allRoleIds);
                String roleNameOfUser = roleIdsOfUser.stream().map(r -> roleBoMap.get(r)).filter(Objects::nonNull).map(t -> t.getName()).collect(Collectors.joining(","));
                String roleNameOfRule = ruleInfoBo.getRolesIds().stream().map(r -> roleBoMap.get(r)).filter(Objects::nonNull).map(t -> t.getName()).collect(Collectors.joining(","));
                log.warn("用户没有队列权限，queueId:{}, username:{},用户权限={},规则权限={}", pullQueryBo.getQueueId(), pullQueryBo.getUsername(), roleNameOfUser, roleNameOfRule);
                throw new RuntimeException("用户没有规则权限");
            }
        }
    }
}
