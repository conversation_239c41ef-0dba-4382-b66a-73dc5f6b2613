package com.bilibili.risk.service.material;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.id.enums.BizTagEnum;
import com.bilibili.id.service.SegmentService;
import com.bilibili.risk.bo.*;
import com.bilibili.risk.bo.msg.CreativeMaterialJudgeAuditMsgBo;
import com.bilibili.risk.bo.msg.PostMaterialPushToAuditResultBo;
import com.bilibili.risk.bo.msg.TaskMaterialSyncMsgBo;
import com.bilibili.risk.config.BizConfig;
import com.bilibili.risk.constant.DatabaseConstant;
import com.bilibili.risk.constant.RiskConstants;
import com.bilibili.risk.convertor.IMaterialAuditConvertor;
import com.bilibili.risk.convertor.IMaterialAuditTaskConvertor;
import com.bilibili.risk.dao.risk.RiskMaterialTaskDoingDao;
import com.bilibili.risk.databus.material_audit.OneCreativeJudgeAuditPub;
import com.bilibili.risk.databus.push_to_audit.TaskMaterialSyncPub;
import com.bilibili.risk.enums.*;
import com.bilibili.risk.po.risk.LauMaterialAuditTaskPo;
import com.bilibili.risk.po.risk.LauMaterialCreativeRelPo;
import com.bilibili.risk.po.risk.LauMaterialToAuditCreativePo;
import com.bilibili.risk.service.MaterialAuditLabelService;
import com.bilibili.risk.service.account.AccountService;
import com.bilibili.risk.service.creative.CreativeService;
import com.bilibili.risk.service.creative.LauMaterialToAuditCreativeService;
import com.bilibili.risk.service.log.OperationLogService;
import com.bilibili.risk.service.material_creative.MaterialCreativeRelService;
import com.bilibili.risk.service.metrics.TaskMetricsReportService;
import com.bilibili.risk.service.queue.MaterialAuditQueueService;
import com.bilibili.risk.service.task.MaterialAuditTaskDbService;
import com.bilibili.risk.service.task.TaskMatchQueueService;
import com.bilibili.risk.service.task.pull.RiskMaterialTaskDoingService;
import com.bilibili.risk.service.unit.UnitExtraService;
import com.bilibili.risk.utils.DiffLogUtils;
import com.bilibili.risk.utils.ListDiffUtil;
import com.bilibili.risk.utils.MaterialTaskUtils;
import com.bilibili.risk.utils.TimeUtil;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class CreativeMaterialSyncService {


    private final BizConfig bizConfig;
    private final CreativeService creativeService;
    private final LauMaterialAuditService lauMaterialAuditService;
    private final MaterialCreativeRelService materialCreativeRelService;
    private final MaterialAuditTaskDbService materialAuditTaskDbService;
    private final MaterialAuditQueueService materialAuditQueueService;
    private final AccountService accountService;
    private final OperationLogService operationLogService;
    private final SegmentService segmentService;
    private final LauMaterialToAuditCreativeService lauMaterialToAuditCreativeService;
    private final RedissonClient adpRedissonClient;
    private final TaskMaterialSyncPub taskMaterialSyncPub;
    private final OneCreativeJudgeAuditPub oneCreativeJudgeAuditPub;
    private final UnitExtraService unitExtraService;
    private final MaterialAuditLabelService materialAuditLabelService;
    private final TaskMatchQueueService taskMatchQueueService;
    private final TaskMetricsReportService taskMetricsReportService;
    private final RiskMaterialTaskDoingService riskMaterialTaskDoingService;

    /**
     * 每次处理一个创意
     *
     * @param creativeMaterialBo
     */
    @Transactional(rollbackFor = Exception.class, transactionManager = DatabaseConstant.RISK_TRANSACTION_MANAGER)
    public PostMaterialPushToAuditResultBo doSync2MaterialTables(CreativeMaterialBo creativeMaterialBo) {
        log.info("processMaterials, param={}", JSON.toJSONString(creativeMaterialBo));

        MaterialPushToAuditContext context = buildContext(creativeMaterialBo);

        PostMaterialPushToAuditResultBo resultBo = new PostMaterialPushToAuditResultBo();
        if (CollectionUtils.isEmpty(creativeMaterialBo.getList())) {
            log.error("processMaterials, allMaterialBos is empty");
            return resultBo;
        }

        // 预处理
        preHandle(context);

        // 准备需要用到数据(此处会同步到素材库)
        prepareData(creativeMaterialBo, context, creativeMaterialBo.getList(), creativeMaterialBo.getCreativeId());
        if (context.isReturnEarly()) {
            return resultBo;
        }

        // 拆素材推审创意处理
        savePushToAuditCreative(context);
        // 创意素材任务处理(素材表和任务表，不可见)
        return processAuditTasks(creativeMaterialBo, context.getAllMaterialBos(), context, creativeMaterialBo.getCreativeId());
    }

    public void syncMaterialCreative(Integer creativeId) throws Exception {
        log.info("syncMaterialCreative, creativeId={}", creativeId);
        if (creativeId == null) {
            log.error("syncMaterialCreative creativeId is null");
            return;
        }

        Timestamp now = new Timestamp(System.currentTimeMillis());

        RLock lock = adpRedissonClient.getLock(RiskConstants.MATERIAL_CREATIVE_SYNC + creativeId);
        try {
            if (lock.tryLock(3, 10, TimeUnit.SECONDS)) {
                List<LauMaterialAuditTaskPo> existTaskPos = materialAuditTaskDbService.queryListByCreativeId(creativeId, null);

                List<LauMaterialCreativeRelBo> updateBos = Lists.newArrayList();

                for (LauMaterialAuditTaskPo existTaskPo : existTaskPos) {
                    LauMaterialCreativeRelBo relBo = LauMaterialCreativeRelBo.builder().creativeId(creativeId).materialMd5(existTaskPo.getMaterialMd5())
                            .materialType(existTaskPo.getMaterialType())
                            // rel 可见性跟着 task
                            .isDeleted(existTaskPo.getIsDeleted())
                            .materialId(existTaskPo.getMaterialId()).ctime(now).mtime(now).build();
                    updateBos.add(relBo);
                }

                Map<Integer, List<LauMaterialCreativeRelBo>> taskPosMap = updateBos.stream()
                        .collect(Collectors.groupingBy(e -> MaterialTaskUtils.calculateShardingKeyByMaterialId(e.getMaterialId()
                                , ShardingTableEnum.TABLE_LAU_MATERIAL_CREATIVE_REL.getShardingAlgorithm())));

                for (Map.Entry<Integer, List<LauMaterialCreativeRelBo>> entry : taskPosMap.entrySet()) {
                    materialCreativeRelService.insertUpdateBatch(entry.getValue());
                }

                taskMaterialSyncPub.pub(TaskMaterialSyncMsgBo.builder().creativeId(creativeId).build());
            } else {
                throw new RuntimeException("syncMaterialCreative lock failed");
            }
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    public void syncTaskMaterial(Integer creativeId) {
        log.info("syncTaskMaterial, creativeId={}", creativeId);
        if (null == creativeId) {
            return;
        }

        List<LauMaterialAuditQueueBo> auditQueueBos = materialAuditQueueService.queryAllMaterialAuditQueue(1);

        // 未删除的任务
        List<LauMaterialAuditTaskPo> existTaskPos = materialAuditTaskDbService.queryListByCreativeId(creativeId, IsDeleted.VALID.getCode());
        List<LauMaterialAuditLabelBo> auditLabelBos = materialAuditLabelService.queryAllList();
        Map<Long, LauMaterialAuditLabelBo> auditLabelBoMap = auditLabelBos.stream().collect(Collectors.toMap(t -> t.getId(), Function.identity()));

        List<MaterialAuditTaskOperationLogBo> operationLogBos = Lists.newArrayList();
        // 对创意下的所有的任务进行处理，让素材和任务可见
        for (LauMaterialAuditTaskPo existTaskPo : existTaskPos) {
            MaterialAuditTaskOperationLogBo log = ((CreativeMaterialSyncService) AopContext.currentProxy()).syncTaskLabelSingle(existTaskPo, auditQueueBos, auditLabelBoMap);
            if (log != null) {
                operationLogBos.add(log);
            }
        }

        CreativeMaterialJudgeAuditMsgBo msgBo = CreativeMaterialJudgeAuditMsgBo.builder()
                .creativeId(creativeId).build();
        oneCreativeJudgeAuditPub.pub(msgBo);

        for (MaterialAuditTaskOperationLogBo operationLogBo : operationLogBos) {
            operationLogService.saveLog(operationLogBo);
        }
    }

    /**
     * 撤销任务删除
     * 1、素材推审后，判断任务是否可见依赖谁先更新 素材审核记录为可见；
     * 2、存在场景：可见的任务，没被审核，任务删除了，素材审核记录还是可见的；
     * 3、所以要处理：a、素材审核记录更新为不可见；b、同时拉取当前素材一条有效任务，更新为可见；
     * @param taskId
     */
    public void unAuditTaskDelete(String taskId) {
        log.info("unAuditTaskDelete, taskId={}", taskId);
        // 1、找到任务，如果还是已删除，继续走
        LauMaterialAuditTaskPo existTaskPo = materialAuditTaskDbService.fetchByTaskId(taskId);
        if(null != existTaskPo && !Objects.equals(existTaskPo.getIsDeleted(), IsDeleted.DELETED.getCode())){
            return;
        }

        LauMaterialAuditBo lauMaterialAuditBo = lauMaterialAuditService.fetchByMaterialId(existTaskPo.getMaterialId());
        if(null != lauMaterialAuditBo && StringUtils.isNotBlank(lauMaterialAuditBo.getAuditLabelThirdId())){
            log.error("unAuditTaskDelete, materialId={} is already audit, taskId={}", existTaskPo.getMaterialId(), taskId);
            return;
        }

        // 2、先把素材置为无效
        LauMaterialAuditBo updateBp = LauMaterialAuditBo.builder().materialId(existTaskPo.getMaterialId())
                .isDeleted(IsDeleted.DELETED.getCode()).mtime(Utils.getNow()).build();
        lauMaterialAuditService.updateSelectiveByMaterialId(updateBp);

        List<LauMaterialAuditQueueBo> auditQueueBos = materialAuditQueueService.queryAllMaterialAuditQueue(1);
        Map<Long, LauMaterialAuditLabelBo> auditLabelBoMap = materialAuditLabelService.queryAllMap();

        List<LauMaterialCreativeRelPo> creativeRelPos;
        Integer creativeId = null;

        StopWatch sw = StopWatch.createStarted();
        do {
            // 2、滚动的拿到素材和创意的关系
            creativeRelPos = materialCreativeRelService.scrollQuery(existTaskPo.getMaterialId(), creativeId, 100, IsDeleted.VALID.getCode());
            if (CollectionUtils.isEmpty(creativeRelPos)) {
                break;
            }

            Map<Integer, List<Integer>> creativeId_partitionMap = creativeRelPos.stream().map(LauMaterialCreativeRelPo::getCreativeId)
                    .collect(Collectors.groupingBy(MaterialTaskUtils::calculateShardingKeyByCreativeId));

            for (Map.Entry<Integer, List<Integer>> entry : creativeId_partitionMap.entrySet()) {
                List<LauMaterialAuditTaskPo> taskPos = materialAuditTaskDbService.queryTasksByCreativeIds(entry.getValue(), existTaskPo.getMaterialId(), null, IsDeleted.VALID.getCode());
                if(CollectionUtils.isNotEmpty(taskPos)){
                    for (LauMaterialAuditTaskPo taskPo : taskPos) {
                        if(taskId.equals(taskPo.getTaskId())){
                            continue;
                        }

                        // 过滤掉已审的数据
                        if(Objects.equals(taskPo.getStatus(), MaterialTaskStatusEnum.COMPLETE.getCode())
                                || Objects.equals(taskPo.getStatus(), MaterialTaskStatusEnum.TO_CALLBACK_AUDIT.getCode())){
                            continue;
                        }

                        // 3、更新任务为可见
                        ((CreativeMaterialSyncService) AopContext.currentProxy()).syncTaskLabelSingle(taskPo
                                , auditQueueBos,  auditLabelBoMap);
                        log.info("可见任务未审出被删除，重置成功, taskId={}, taskPo={}， cost={}", taskId, JSON.toJSONString(taskPo), sw.getTime());
                        return;
                    }
                }
            }
            
            creativeId = creativeRelPos.get(creativeRelPos.size() - 1).getCreativeId();

        } while (!CollectionUtils.isEmpty(creativeRelPos) && creativeRelPos.size() == 100);

        log.info("可见任务未审出被删除，重置失败, taskId={}, cost={}", taskId, sw.getTime());
    }

    @Transactional(rollbackFor = Exception.class, transactionManager = DatabaseConstant.RISK_TRANSACTION_MANAGER)
    public MaterialAuditTaskOperationLogBo syncTaskLabelSingle(LauMaterialAuditTaskPo existTaskPo, List<LauMaterialAuditQueueBo> auditQueueBos, Map<Long, LauMaterialAuditLabelBo> auditLabelBoMap) {
        log.info("syncTaskLabelSingle, existTaskPo={}", JSON.toJSONString(existTaskPo));
        Timestamp mtime = TimeUtil.dateToTimestamp(new Date());

        // 1、更新素材审核任务表，通过记录锁，锁定当前行
        LauMaterialAuditBo existMaterialAuditBo = LauMaterialAuditBo.builder().materialId(existTaskPo.getMaterialId()).mtime(mtime).build();

        // 2、更新素材审核表，试着素材审核表的valid字段，成功了，task 可以被审核
        int effect = lauMaterialAuditService.updateValidIfDeleted(existMaterialAuditBo);
        log.info("updateValidIfDeleted,[素材改为可见]effect={}, existMaterialAuditBo={}", effect, JSON.toJSONString(existMaterialAuditBo));
        Integer status = null, type = null, operationType = null;
        String labelIds = null, executeName = null, operationValue = null, reason = null;
        Long queueId = null;
        Timestamp executeTime = null;

        boolean needLog = true;
        // 该素材不是由本线程添加
        if (effect == 0) {
            lauMaterialAuditService.updateMaterialAuditBos(Lists.newArrayList(existMaterialAuditBo), false);
            LauMaterialAuditBo materialAuditBo = lauMaterialAuditService.fetchByMaterialId(existTaskPo.getMaterialId());
            labelIds = materialAuditBo.getAuditLabelThirdId();

            // 说明已经有了该素材，看是否复用
            if (StringUtils.isNotBlank(labelIds) ) {
                if (Objects.equals(labelIds, existTaskPo.getAuditLabelThirdId())) {
                    return null;
                }
                queueId = taskMatchQueueService.matchQueueId(existTaskPo.getTaskId(), auditQueueBos, existTaskPo);
                type = MaterialTaskTypeEnum.REUSE.getCode();
                status = MaterialTaskStatusEnum.COMPLETE.getCode();
                reason = materialAuditBo.getReason();
                executeName = RiskConstants.SYSTEM_USERNAME;
                executeTime = Utils.getNow();
                operationType = OperationTypeEnum.REUSE_RESULT.getCode();
                operationValue = MaterialTaskUtils.genAuditLogValue(labelIds, auditLabelBoMap);
                log.info("processMaterials[素材改为可见失败,已打标,复用素材,任务完成], taskId={}, queueId={},materialId={},operationValue={}", existTaskPo.getTaskId(), queueId, existTaskPo.getMaterialId(),operationValue);
                if (Objects.equals(existTaskPo.getStatus(), status) && Objects.equals(existTaskPo.getAuditLabelThirdId(), labelIds)) {
                    needLog = false;
                }
                taskMetricsReportService.addBizMetricCount(MetricsCodeEnum.SubCode.BIZ_MATERIAL_TASK_REUSE_RESULT, "", existTaskPo.getMaterialType(), 1);
            } else {
                // 别人添加的素材，并且没有复用，则进入人审，前置位已经设置了等待队列
                // 删除的任务需要stop
                if (Objects.equals(existTaskPo.getIsDeleted(), IsDeleted.DELETED.getCode())) {
                    status = MaterialTaskStatusEnum.STOP.getCode();
                    operationValue = "素材被删除，停止任务";
                }
                operationType = OperationTypeEnum.ENTER_AUDIT.getCode();
                log.info("processMaterials[素材改为可见失败,素材未打标,不复用], taskId={}, queueId={},materialId={},operationValue={}", existTaskPo.getTaskId(), queueId, existTaskPo.getMaterialId(), operationValue);
                if (Objects.equals(existTaskPo.getStatus(), status) && Objects.equals(existTaskPo.getAuditLabelThirdId(), labelIds)) {
                    needLog = false;
                }
                taskMetricsReportService.addBizMetricCount(MetricsCodeEnum.SubCode.BIZ_MATERIAL_TASK_ENTER_AUDIT, "", existTaskPo.getMaterialType(), 1);
            }
        }
        else {
            // 该线程第一次添加了新的素材，进入人审
            // 删除的任务需要stop
            if (Objects.equals(existTaskPo.getIsDeleted(), IsDeleted.DELETED.getCode())) {
                status = MaterialTaskStatusEnum.STOP.getCode();
                operationValue = "素材被删除，停止任务";
            }
            queueId = taskMatchQueueService.matchQueueId(existTaskPo.getTaskId(), auditQueueBos, existTaskPo);
            operationType = OperationTypeEnum.ENTER_AUDIT.getCode();
            log.info("processMaterials[素材改为可见成功], taskId={}, queueId={},materialId={},status={},operationValue={}", existTaskPo.getTaskId(), queueId, existTaskPo.getMaterialId(), status, operationValue);
        }

        LauMaterialAuditTaskPo updatePo = LauMaterialAuditTaskPo.builder().taskId(existTaskPo.getTaskId())
                .mtime(mtime).status(status).auditLabelThirdId(labelIds).type(type).reason(reason).executeName(executeName).queueId(queueId).executeTime(executeTime).build();

        int count = materialAuditTaskDbService.updateTask(updatePo);

        LauMaterialAuditTaskUpdateBo oldBo = IMaterialAuditTaskConvertor.INSTANCE.po2UpdateBo(existTaskPo);
        LauMaterialAuditTaskUpdateBo newBo = IMaterialAuditTaskConvertor.INSTANCE.po2UpdateBo(updatePo);
        String diffStr = DiffLogUtils.generateDiffField(oldBo, newBo, "任务对象");

        if (needLog) {
            return MaterialAuditTaskOperationLogBo.builder()
                    .objId(existTaskPo.getTaskId())
                    .operatorUsername(RiskConstants.SYSTEM_USERNAME)
                    .operationType(operationType)
                    .value(operationValue)
                    .remark(JSON.toJSONString(updatePo))
                    .diff(diffStr)
                    .ctime(mtime.getTime())
                    .type(LogTypeEnum.TASK.getCode())
                    .systemType(RiskConstants.SYSTEM_TYPE_RISK)
                    .build();
        }
        return null;
    }


    private PostMaterialPushToAuditResultBo processAuditTasks(CreativeMaterialBo creativeMaterialBo, List<CreativeDetailMaterialBo> allMaterialBos, MaterialPushToAuditContext context, Integer creativeId) {
        List<LauMaterialAuditTaskPo> allTaskPos = IMaterialAuditTaskConvertor.INSTANCE.creativeDetailInfoOfMaterialBos2Pos(allMaterialBos);
        // 填充已有的任务信息
/*        for (LauMaterialAuditTaskPo auditTaskPo : allTaskPos) {
//            auditTaskPo.setIsDeleted(0);
            LauMaterialAuditTaskPo existAuditTaskPo = context.getAuditTaskPoMap().get(genKey(auditTaskPo.getCreativeId(), auditTaskPo.getMaterialId()));
            if (existAuditTaskPo != null) {
                BeanUtils.copyProperties(existAuditTaskPo, auditTaskPo);
            }
        }*/

        List<MaterialAuditTaskOperationLogBo> operationLogBos = new ArrayList<>();
        Map<Integer, List<LauMaterialAuditTaskPo>> allTaskPoMap = ListDiffUtil.calculateDiff(allTaskPos, context.getExistTaskPos(), r -> genKey(creativeId, r.getMaterialId()));
        List<LauMaterialAuditTaskPo> neeAddedAuditTaskPos = allTaskPoMap.get(DiffTypeEnum.ADDED.getKey());
        List<LauMaterialAuditTaskPo> needDeleteAuditTaskPos = allTaskPoMap.get(DiffTypeEnum.DELETED.getKey());
        List<LauMaterialAuditTaskPo> updateAuditTaskPos = allTaskPoMap.get(DiffTypeEnum.UPDATED.getKey());

        List<LauMaterialAuditTaskPo> needUpdateAuditTaskPos = Lists.newArrayListWithCapacity(updateAuditTaskPos.size());
        // 修改的准备账户等信息
        AccountIndustryItemBo accountIndustryItemBo = context.getAccountIndustryItemBo();
        // 新增的默认值设置
        CreativeMaterialJudgeAuditMsgBo judgeAuditMsgBo = null;
        Timestamp now = Utils.getNow();

        // 更新已有创意素材任务的几个字段
        for (LauMaterialAuditTaskPo auditTaskPo : updateAuditTaskPos) {
            LauMaterialAuditTaskPo copy = new LauMaterialAuditTaskPo();
            copy.setCreativeAuditStatus(context.getCreativeBo().getAuditStatus());
            copy.setAccountId(creativeMaterialBo.getAccountId());
            copy.setUnitedFirstIndustryId(accountIndustryItemBo.getUnitedFirstIndustryId());
            copy.setUnitedSecondIndustryId(accountIndustryItemBo.getUnitedSecondIndustryId());
            copy.setUnitedThirdIndustryId(accountIndustryItemBo.getUnitedThirdIndustryId());
            copy.setIsDeleted(IsDeleted.VALID.getCode());
            copy.setMaterialSource(auditTaskPo.getMaterialSource());
            needUpdateAuditTaskPos.add(copy);

            // 修改删除的数据未可见，清空他的历史字段
            LauMaterialAuditTaskPo existAuditTaskPo = context.getAuditTaskPoMap().get(genKey(auditTaskPo.getCreativeId(), auditTaskPo.getMaterialId()));
            copy.setTaskId(existAuditTaskPo.getTaskId());

            if (IsDeleted.DELETED == IsDeleted.getByCode(existAuditTaskPo.getIsDeleted())) {
                // 删除置为可见，现在默认
                copy.setStatus(MaterialTaskStatusEnum.FREE.getCode());
                copy.setAuditLabelThirdId("");
                copy.setEnterAuditTime(now);
                copy.setQueueId(bizConfig.getWaitQueueId());
                copy.setType(MaterialTaskTypeEnum.MANUAL_AUDIT.getCode());
                log.info("processMaterials[已存在任务,之前isDeleted=1,改成status=-1], taskId={}, queueId={},materialId={}", copy.getTaskId(), copy.getQueueId(), auditTaskPo.getMaterialId());
                taskMetricsReportService.addBizMetricCount(MetricsCodeEnum.SubCode.BIZ_MATERIAL_TASK_RECOVER, "", existAuditTaskPo.getMaterialType(), 1);

                // 日志
                LauMaterialAuditTaskUpdateBo newUpdateBo = IMaterialAuditTaskConvertor.INSTANCE.po2UpdateBo(auditTaskPo);
                String diffStr = DiffLogUtils.generateDiffField(null, newUpdateBo, "任务对象");
                // 进审操作日志
                MaterialAuditTaskOperationLogBo operationLogBo = MaterialAuditTaskOperationLogBo.builder()
                        .objId(copy.getTaskId())
                        .operatorUsername(RiskConstants.SYSTEM_USERNAME)
                        .operationType(OperationTypeEnum.ENTER_AUDIT_TO_MATCH.getCode())
                        .remark(JSON.toJSONString(auditTaskPo))
                        .value("删除的素材素材恢复")
                        .diff(diffStr)
                        .ctime(System.currentTimeMillis())
                        .type(LogTypeEnum.TASK.getCode())
                        .systemType(RiskConstants.SYSTEM_TYPE_RISK)
                        .build();
                operationLogBos.add(operationLogBo);
            }
            /*LauMaterialAuditBo existMaterialAuditBo = context.getExistMaterialAuditBoMapById().get(taskPo.getMaterialId());
            judgeAuditMsgBo = Optional.ofNullable(setReuseColumn(creativeId, context.getAuditQueueBos(), existMaterialAuditBo, taskPo))
                    .orElse(judgeAuditMsgBo);*/
        }

        for (LauMaterialAuditTaskPo taskPo : neeAddedAuditTaskPos) {
            taskPo.setTaskId(segmentService.getId(BizTagEnum.TASK, taskPo.getCreativeId()));
            taskPo.setCtime(now);
            taskPo.setMtime(now);
            // 默认值，用来表示空
            taskPo.setAcceptTime(RiskConstants.BUSINESS_EARLIEST_TIMESTAMP);
            taskPo.setExecuteTime(RiskConstants.BUSINESS_EARLIEST_TIMESTAMP);
            // 进审时间默认当前时间
            taskPo.setEnterAuditTime(now);
            taskPo.setVersion(1);
            taskPo.setType(MaterialTaskTypeEnum.MANUAL_AUDIT.getCode());
            taskPo.setCreativeAuditStatus(context.getCreativeBo().getAuditStatus());
            taskPo.setAccountId(creativeMaterialBo.getAccountId());
            taskPo.setUnitedFirstIndustryId(accountIndustryItemBo.getUnitedSecondIndustryId());
            taskPo.setUnitedSecondIndustryId(accountIndustryItemBo.getUnitedSecondIndustryId());
            taskPo.setUnitedThirdIndustryId(accountIndustryItemBo.getUnitedThirdIndustryId());
            taskPo.setStatus(MaterialTaskStatusEnum.FREE.getCode());
            // 默认先等待队列
            taskPo.setQueueId(bizConfig.getWaitQueueId());
            log.info("processMaterials[新增任务], taskId={}, queueId={},materialId={},creativeId:{}", taskPo.getTaskId(), taskPo.getQueueId(), taskPo.getMaterialId(), taskPo.getCreativeId());

            taskMetricsReportService.addBizMetricCount(MetricsCodeEnum.SubCode.BIZ_MATERIAL_TASK_ENTER_AUDIT_TO_MATCH, "", taskPo.getMaterialType(), 1);

            // 日志
            LauMaterialAuditTaskUpdateBo newUpdateBo = IMaterialAuditTaskConvertor.INSTANCE.po2UpdateBo(taskPo);
            String diffStr = DiffLogUtils.generateDiffField(null, newUpdateBo, "任务对象");
            // 进审操作日志
            MaterialAuditTaskOperationLogBo operationLogBo = MaterialAuditTaskOperationLogBo.builder()
                    .objId(taskPo.getTaskId())
                    .operatorUsername(RiskConstants.SYSTEM_USERNAME)
                    .operationType(OperationTypeEnum.ENTER_AUDIT_TO_MATCH.getCode())
                    .remark(JSON.toJSONString(taskPo))
                    .diff(diffStr)
                    .ctime(System.currentTimeMillis())
                    .type(LogTypeEnum.TASK.getCode())
                    .systemType(RiskConstants.SYSTEM_TYPE_RISK)
                    .build();
            operationLogBos.add(operationLogBo);
        }

        // 不存在的素材，需要删除任务
        needDeleteAuditTaskPos = needDeleteAuditTaskPos.stream().filter(e -> !Integer.valueOf(IsDeleted.DELETED.getCode()).equals(e.getIsDeleted()))
                .collect(Collectors.toList());
        for (LauMaterialAuditTaskPo needDeleteAuditTaskPo : needDeleteAuditTaskPos) {
            needDeleteAuditTaskPo.setIsDeleted(IsDeleted.DELETED.getCode());
            needDeleteAuditTaskPo.setStatus(MaterialTaskStatusEnum.STOP.getCode());
            // 领取人，bachNo, doing 表
            Integer deleteDoingCount = riskMaterialTaskDoingService.deleteByTaskId(needDeleteAuditTaskPo.getTaskId());
            if (deleteDoingCount > 0) {
                needDeleteAuditTaskPo.setReceiveBatchNo("");
                needDeleteAuditTaskPo.setAcceptName("");
                needDeleteAuditTaskPo.setAcceptTime(RiskConstants.BUSINESS_EARLIEST_TIMESTAMP);
                log.info("processMaterials[素材不存在,删除任务并清空doing表], taskId={}, queueId={},materialId={}", needDeleteAuditTaskPo.getTaskId(), needDeleteAuditTaskPo.getQueueId(), needDeleteAuditTaskPo.getMaterialId());
            }

            log.info("processMaterials[素材不存在,删除任务并stop], taskId={}, queueId={},materialId={}", needDeleteAuditTaskPo.getTaskId(), needDeleteAuditTaskPo.getQueueId(), needDeleteAuditTaskPo.getMaterialId());

            taskMetricsReportService.addBizMetricCount(MetricsCodeEnum.SubCode.BIZ_MATERIAL_TASK_DELETE, "", needDeleteAuditTaskPo.getMaterialType(), 1);

            // 日志
            LauMaterialAuditTaskUpdateBo newUpdateBo = IMaterialAuditTaskConvertor.INSTANCE.po2UpdateBo(needDeleteAuditTaskPo);
            String diffStr = DiffLogUtils.generateDiffField(null, newUpdateBo, "任务对象");
            // 进审操作日志
            MaterialAuditTaskOperationLogBo operationLogBo = MaterialAuditTaskOperationLogBo.builder()
                    .objId(needDeleteAuditTaskPo.getTaskId())
                    .operatorUsername(RiskConstants.SYSTEM_USERNAME)
                    .operationType(OperationTypeEnum.ENTER_AUDIT.getCode())
                    .remark(JSON.toJSONString(needDeleteAuditTaskPo))
                    .value("素材不存在,删除任务并废弃")
                    .diff(diffStr)
                    .ctime(System.currentTimeMillis())
                    .type(LogTypeEnum.TASK.getCode())
                    .systemType(RiskConstants.SYSTEM_TYPE_RISK)
                    .build();
            operationLogBos.add(operationLogBo);
        }

        materialAuditTaskDbService.updateMaterialAuditTaskPos(needUpdateAuditTaskPos);
        materialAuditTaskDbService.insertMaterialAuditTaskPos(neeAddedAuditTaskPos);
        materialAuditTaskDbService.deleteMaterialAuditTaskPos(needDeleteAuditTaskPos);
        return PostMaterialPushToAuditResultBo.builder().taskMatchQueueMsgBos(Collections.emptyList()).operationLogBos(operationLogBos).reuseCreativeJudgeAuditMsgBo(judgeAuditMsgBo).build();
    }

    private static MaterialPushToAuditContext buildContext(CreativeMaterialBo creativeMaterialBo) {
        MaterialPushToAuditContext context = new MaterialPushToAuditContext();
        context.setAccountId(creativeMaterialBo.getAccountId());
        context.setCreativeId(creativeMaterialBo.getCreativeId());
        context.setAllMaterialBos(creativeMaterialBo.getList());
        context.setEventTime(creativeMaterialBo.getEventTime());
        return context;
    }

    private void prepareData(CreativeMaterialBo creativeMaterialBo, MaterialPushToAuditContext context, List<CreativeDetailMaterialBo> allMaterialBos, Integer creativeId) {

        // 素材
        List<LauMaterialAuditBo> materialBos = IMaterialAuditConvertor.INSTANCE.creativeDetailInfoOfMaterialBo2MaterialAuditBos(allMaterialBos);

        if (null == context.getCreativeBo()) {
            CreativeBo creativeBo = creativeService.fetchCreativeById(creativeId);
            context.setCreativeBo(creativeBo);
        }

        Assert.notNull(context.getCreativeBo(), "creative is null");
        creativeMaterialBo.setAccountId(context.getCreativeBo().getAccountId());
        context.setAccountId(context.getCreativeBo().getAccountId());
        // material 相关数据查询
        LauMaterialToAuditCreativePo toAuditCreativePo = lauMaterialToAuditCreativeService.fetchByCreativeId(creativeId);
        context.setToAuditCreativePo(toAuditCreativePo);

        if (Utils.isPositive(context.getCreativeBo().getIsProgrammatic())) {
            LauUnitExtraBo lauUnitExtraBo = unitExtraService.fetchUnitExtraBo(context.getCreativeBo().getUnitId());
            // 程序化 general version为2才处理
            if (!Objects.equals(lauUnitExtraBo.getGeneralVersion(), 2)) {
                log.warn("processMaterials[程序化,gv!=2,不处理], creativeId={}, generalVersion={}", creativeId, lauUnitExtraBo.getGeneralVersion());
                context.setReturnEarly(true);
                return;
            }
        }

        // 推送的版本号小于db里创意上次版本号则过滤
        if (context.getToAuditCreativePo() != null && creativeMaterialBo.getEventTime() != null && creativeMaterialBo.getEventTime() < context.getToAuditCreativePo().getEventTime()) {
            log.info("processMaterials小版本不处理, eventTime={}, toAuditCreativePo.eventTime={}", creativeMaterialBo.getEventTime(), context.getToAuditCreativePo().getEventTime());
            context.setReturnEarly(true);
            return;
        }

        // 同步到素材表
        Map<String, LauMaterialAuditBo> materialBoMapByMd5 = lauMaterialAuditService.genMd5AndSaveMaterials(materialBos);
        context.setMaterialBoMapByMd5(materialBoMapByMd5);
        for (CreativeDetailMaterialBo materialBo : allMaterialBos) {
            materialBo.setBizType(MaterialBizTypeEnum.getBizTypeByBiliUserId(context.getCreativeBo().getBilibiliUserId()));
            Assert.isTrue(Utils.isPositive(materialBo.getBizType()), "biz type不合法");
            materialBo.setMaterialId(materialBoMapByMd5.get(MaterialTaskUtils.genKey(materialBo.getMaterialMd5(), materialBo.getMaterialType())).getMaterialId());
        }

        // 账户信息
        AccountIndustryItemBo accountIndustryItemBo = accountService.fetchAccountIndustry(creativeMaterialBo.getAccountId());
        Assert.notNull(accountIndustryItemBo, "account industry item is null");
        context.setAccountIndustryItemBo(accountIndustryItemBo);



/*        Map<String, LauMaterialAuditBo> existMaterialAuditBoMap = lauMaterialAuditService.queryMapByMaterialIds(materialIds);
        context.setExistMaterialAuditBoMapById(existMaterialAuditBoMap);
        List<LauMaterialCreativeRelPo> existMaterialCreativeRelPos = materialCreativeRelService.queryListByMaterialIds(materialIds);
        context.setExistMaterialCreativeRelPos(existMaterialCreativeRelPos);*/
        // 任务(不限制状态，查询了创意历史所有的任务)
        List<LauMaterialAuditTaskPo> existTaskPos = materialAuditTaskDbService.queryListByCreativeId(creativeId, null);
        context.setExistTaskPos(existTaskPos);
        Map<String, LauMaterialAuditTaskPo> auditTaskPoMap = existTaskPos.stream().collect(Collectors.toMap(t -> genKey(t.getCreativeId(), t.getMaterialId()), Function.identity()));
        context.setAuditTaskPoMap(auditTaskPoMap);

        // TODO chenquanqing 个人起飞场景
        // 稿件
        // avid 可以通过解析 materialContent 来获取
/*        Map<Long, BiliArchiveBo> biliArchiveBoMap = biliArchiveService.fetchMap(context.getAvids());
        context.setBiliArchiveBoMap(biliArchiveBoMap);*/

    }


    private static void preHandle(MaterialPushToAuditContext context) {
        for (CreativeDetailMaterialBo materialBo : context.getAllMaterialBos()) {
            if (StringUtils.isBlank(materialBo.getMaterialId())) {
                Assert.isTrue(Utils.isPositive(materialBo.getMaterialType()), "素材类型不合法");
                Assert.isTrue(StringUtils.isNotEmpty(materialBo.getMaterialContent()), "material content不合法");
                Assert.isTrue(StringUtils.isNotEmpty(materialBo.getRawContent()), "raw content不合法");
            }

            materialBo.setCreativeId(context.getCreativeId());
        }

        Map<String, CreativeDetailMaterialBo> creativeDetailMaterialBoMap = context.getAllMaterialBos().stream()
                .collect(Collectors.toMap(t -> MaterialTaskUtils.genKey(t.getMaterialMd5(), t.getMaterialType()), Function.identity()
                        , (v1, v2) -> {
                            // 输出存在 materialId 的素材，即保存过的
                            if (StringUtils.isNotBlank(v1.getMaterialId())) {
                                return v1;
                            }
                            return v2;
                        }));
        context.setCreativeDetailMaterialBoMap(creativeDetailMaterialBoMap);
    }

    private void savePushToAuditCreative(MaterialPushToAuditContext context) {
        LauMaterialToAuditCreativePo toAuditCreativePo = context.getToAuditCreativePo();

        if (toAuditCreativePo == null) {
            toAuditCreativePo = new LauMaterialToAuditCreativePo();
            toAuditCreativePo.setCreativeId(context.getCreativeId());
            toAuditCreativePo.setEventTime(context.getEventTime());
            log.info("processMaterials[新增toAuditCreativePo], creativeId={}, eventTime={}", context.getCreativeId(), context.getEventTime());
        } else {
            toAuditCreativePo.setEventTime(context.getEventTime()); // 会记录每个创意的最新推审时间
            log.info("processMaterials[更新toAuditCreativePo], creativeId={}, eventTime={}", context.getCreativeId(), context.getEventTime());
        }
        // 更新下时间，防止下游无法更新
        toAuditCreativePo.setMtime(TimeUtil.dateToTimestamp(new Date()));
        lauMaterialToAuditCreativeService.save(toAuditCreativePo);
    }


    private static String genKey(String md5, Integer materialType) {
        return md5 + "-" + materialType;
    }

    private static String genKey(Integer creativeId, String materialId) {
        return creativeId + "-" + materialId;
    }
}
