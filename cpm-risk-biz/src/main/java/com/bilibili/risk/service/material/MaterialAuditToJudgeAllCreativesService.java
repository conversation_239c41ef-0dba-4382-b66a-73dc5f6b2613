package com.bilibili.risk.service.material;

import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.risk.bo.msg.CreativeMaterialJudgeAuditMsgBo;
import com.bilibili.risk.bo.msg.MaterialAuditMsgBo;
import com.bilibili.risk.bo.msg.MaterialCreativeScrollMsgBo;
import com.bilibili.risk.config.DynamicProperties;
import com.bilibili.risk.constant.RiskConstants;
import com.bilibili.risk.databus.material_audit.MaterialCreativeScrollPub;
import com.bilibili.risk.databus.material_audit.OneCreativeJudgeAuditPub;
import com.bilibili.risk.enums.MaterialTaskTypeEnum;
import com.bilibili.risk.enums.MetricsCodeEnum;
import com.bilibili.risk.po.risk.LauMaterialCreativeRelPo;
import com.bilibili.risk.service.material_creative.MaterialCreativeRelService;
import com.bilibili.risk.service.metrics.TaskMetricsReportService;
import com.bilibili.risk.utils.JacksonUtils;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 判定素材下所有创意情况
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MaterialAuditToJudgeAllCreativesService {

    private final OneCreativeJudgeAuditPub oneCreativeJudgeAuditPub;
    private final MaterialCreativeRelService materialCreativeRelService;
    private final TaskMetricsReportService taskMetricsReportService;
    private final MaterialCreativeScrollPub materialCreativeScrollPub;
    private final DynamicProperties dynamicProperties;

    /**
     * 判定素材下所有创意情况
     * 处理素材的所有的创意，发消息进行创意判定处理
     *
     * @param materialAuditMsgBo
     */
    public void processMaterialCreative(MaterialAuditMsgBo materialAuditMsgBo) {
        log.info("processMaterialCreative start, materialAuditMsgBo={}", materialAuditMsgBo);
        String materialId = materialAuditMsgBo.getMaterialId();
        if (StringUtils.isBlank(materialId)) {
            log.error("processMaterialCreative, materialId is empty");
            return;
        }

        Long startTime = System.currentTimeMillis();
        Integer creativeId = null;
        int size = 0;
        // 仅仅处理当前当前创意
        if (Utils.isPositive(materialAuditMsgBo.getOnlyJudgeOneCreative())) {
            creativeId = materialAuditMsgBo.getCreativeId();
            if (!Utils.isPositive(creativeId)) {
                log.error("processMaterialCreative[仅仅处理当前当前创意], materialId={}, creativeId is empty", materialId);
                return;
            }

            LauMaterialCreativeRelPo creativeRelPo = materialCreativeRelService.queryListByMaterialId(materialId, creativeId);

            if (null == creativeRelPo) {
                log.info("processMaterialCreative, materialId={},creativeId={}, creativeRelPos is empty", materialId, creativeId);
                return;
            }
            size = 1;
            pub(Lists.newArrayList(creativeRelPo), materialAuditMsgBo);
        }
        // 滚动处理素材的所有的创意判定
        else {
            List<LauMaterialCreativeRelPo> creativeRelPos;
            StopWatch sw = StopWatch.createStarted();
            long count = materialCreativeRelService.countByMaterialId(materialId, IsDeleted.VALID.getCode());

            // 数据过多时，异步开滚
            if(count > dynamicProperties.getScrollSizeThreshold()){
                log.info("processMaterialCreative async scroll, materialId={}, size={}", materialId, size);
                materialCreativeScrollPub.pub(MaterialCreativeScrollMsgBo.builder()
                        .materialId(materialAuditMsgBo.getMaterialId())
                        .lastCreativeId(null)
                        .triggerCreativeId(materialAuditMsgBo.getCreativeId())
                        .executeName(materialAuditMsgBo.getExecuteName())
                        .triggerTaskId(materialAuditMsgBo.getTriggerTaskId())
                        .build());
            } else {

                do {
                    // 这边消费者慢了
                    // 1、可以把游标存在redis中，过期时间设长一点，重复消费也不会重复滚
                    // 2、通过消息投递传递游标，基本不会丢失
                    creativeRelPos = materialCreativeRelService.scrollQuery(materialId, creativeId, 100, IsDeleted.VALID.getCode());
                    if (CollectionUtils.isEmpty(creativeRelPos)) {
                        break;
                    }

                    creativeId = creativeRelPos.get(creativeRelPos.size() - 1).getCreativeId();
                    pub(creativeRelPos, materialAuditMsgBo);
                    size += creativeRelPos.size();
                } while (!CollectionUtils.isEmpty(creativeRelPos) && creativeRelPos.size() == 100);

                log.info("processMaterialCreative, materialId={}, creativeId={}, size={}, cost={}", materialId, creativeId, size, sw.getTime());
                if(sw.getTime() > 2 * 60 * 1000) {
                    log.error("processMaterialCreative, materialId={}, creativeId={}, size={}, cost={}", materialId, creativeId, size, sw.getTime());
                }
            }
        }
        Long endTime = System.currentTimeMillis();
        taskMetricsReportService.addBizMetricCountAndCost(endTime - startTime, MetricsCodeEnum.SubCode.BIZ_MATERIAL_ALL_CREATIVE_JUDGE, materialAuditMsgBo.getExecuteName(), 0, size);
        log.info("processMaterialCreative end, cost={},materialAuditMsgBo={}", endTime - startTime, materialAuditMsgBo);
    }

    public void scrollCreative(MaterialCreativeScrollMsgBo msg){
        log.info("scrollCreative start, msg={}", JacksonUtils.toJson(msg));

        StopWatch sw = StopWatch.createStarted();
        List<LauMaterialCreativeRelPo> creativeRelPos;
        Integer creativeId = msg.getLastCreativeId(), size = 0;
        do {
            // 这边消费者慢了
            // 1、可以把游标存在redis中，过期时间设长一点，重复消费也不会重复滚
            // 2、通过消息投递传递游标，基本不会丢失
            creativeRelPos = materialCreativeRelService.scrollQuery(msg.getMaterialId(), creativeId, 100, IsDeleted.VALID.getCode());
            if (CollectionUtils.isEmpty(creativeRelPos)) {
                break;
            }

            creativeId = creativeRelPos.get(creativeRelPos.size() - 1).getCreativeId();

            MaterialAuditMsgBo materialAuditMsgBo = MaterialAuditMsgBo.builder()
                    .materialId(msg.getMaterialId())
                    .creativeId(msg.getTriggerCreativeId())
                    .executeName(msg.getExecuteName())
                    .triggerTaskId(msg.getTriggerTaskId())
                    .build();
            pub(creativeRelPos, materialAuditMsgBo);
            size += creativeRelPos.size();

            if(sw.getTime(TimeUnit.SECONDS) > dynamicProperties.getScrollDurationThreshold()){
                materialCreativeScrollPub.pub(MaterialCreativeScrollMsgBo.builder()
                        .materialId(msg.getMaterialId())
                        .lastCreativeId(creativeId)
                        .triggerCreativeId(msg.getTriggerCreativeId())
                        .executeName(msg.getExecuteName())
                        .triggerTaskId(msg.getTriggerTaskId())
                        .build());
                break;
            }
        } while (!CollectionUtils.isEmpty(creativeRelPos) && creativeRelPos.size() == 100);

        log.info("scrollCreative end, materialId={}, size={}, cost={}", msg.getMaterialId(), size, sw.getTime());
    }

    public void pub(List<LauMaterialCreativeRelPo> creativeRelPos, MaterialAuditMsgBo materialAuditMsgBo) {
        if (CollectionUtils.isEmpty(creativeRelPos)) {
            return;
        }

        // 每个创意发消息判定
        for (LauMaterialCreativeRelPo creativeRelPo : creativeRelPos) {
            // 素材审核状态变更，其他任务被动处理
            Integer needProcessTask = 1;
            String executeName = RiskConstants.SYSTEM_USERNAME;
            Integer taskType = MaterialTaskTypeEnum.REUSE.getKey();

            // 页面处理的创意
            if (Objects.equals(materialAuditMsgBo.getCreativeId(), creativeRelPo.getCreativeId())) {
                taskType = MaterialTaskTypeEnum.MANUAL_AUDIT.getKey();
                executeName = materialAuditMsgBo.getExecuteName();
                needProcessTask = 0;
            }

            CreativeMaterialJudgeAuditMsgBo judgeAuditMsgBo = CreativeMaterialJudgeAuditMsgBo.builder()
                    .creativeId(creativeRelPo.getCreativeId())
                    .materialId(creativeRelPo.getMaterialId())
//                    .auditLabelThirdIds(materialAuditMsgBo.getAuditLabelThirdIds())
                    .executeName(executeName)
                    .taskType(taskType)
                    .needProcessTask(needProcessTask)
                    .triggerReuseTaskId(materialAuditMsgBo.getTriggerTaskId())
                    .build();
            oneCreativeJudgeAuditPub.pub(judgeAuditMsgBo);
        }

    }
}
