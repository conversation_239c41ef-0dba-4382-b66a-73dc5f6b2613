package com.bilibili.risk.service.creative;

import com.alibaba.fastjson.JSON;
import com.bapis.ad.audit.AuditInfo;
import com.bapis.ad.audit.OperatorType;
import com.bapis.ad.audit.ProgramAuditReply;
import com.bapis.ad.audit.ProgramAuditRequest;
import com.bapis.ad.pandora.service.PandoraAuditServiceGrpc;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.risk.biz.task.stat.databus.TaskFlowStatPub;
import com.bilibili.risk.biz.task.stat.databus.bean.TaskFlowStatMsg;
import com.bilibili.risk.biz.task.stat.enums.StatCountType;
import com.bilibili.risk.biz.task.stat.enums.StatTaskType;
import com.bilibili.risk.bo.*;
import com.bilibili.risk.bo.msg.CreativeJudgeAuditMsgBo;
import com.bilibili.risk.bo.msg.CreativeMaterialJudgeAuditMsgBo;
import com.bilibili.risk.config.BizConfig;
import com.bilibili.risk.constant.CreativeStatus;
import com.bilibili.risk.constant.RiskConstants;
import com.bilibili.risk.constants.AllStatus;
import com.bilibili.risk.convertor.ICreativeConvertor;
import com.bilibili.risk.convertor.IMaterialAuditTaskConvertor;
import com.bilibili.risk.enums.*;
import com.bilibili.risk.po.ad.LauShadowCreativePo;
import com.bilibili.risk.po.risk.LauMaterialAuditTaskPo;
import com.bilibili.risk.service.MaterialAuditLabelService;
import com.bilibili.risk.service.log.OperationLogService;
import com.bilibili.risk.service.material.LauMaterialAuditService;
import com.bilibili.risk.metrics_report.metrics.MetricDataHolder;
import com.bilibili.risk.metrics_report.metrics.TaskMetricsReportService;
import com.bilibili.risk.service.task.MaterialAuditTaskDbService;
import com.bilibili.risk.utils.DiffLogUtils;
import com.bilibili.risk.utils.MaterialTaskUtils;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.MutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import pleiades.venus.starter.rpc.client.RPCClient;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 一个创意判定审核情况
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OneCreativeJudgeAuditService {

    private final MaterialAuditTaskDbService materialAuditTaskDbService;
    private final CreativeService creativeService;
    private final ProgramCreativeService programCreativeService;
    private final MaterialAuditLabelService materialAuditLabelService;
    private final OperationLogService operationLogService;
    private final RedissonClient adpRedissonClient;
    private final LauMaterialAuditService lauMaterialAuditService;
    private final BizConfig bizConfig;
    private final TaskMetricsReportService taskMetricsReportService;
    private final TaskFlowStatPub taskFlowStatPub;

    @RPCClient("sycpb.platform.cpm-pandora")
    private PandoraAuditServiceGrpc.PandoraAuditServiceBlockingStub pandoraAuditServiceBlockingStub;

    /**
     * 1.获取素材状态
     * 2.将任务改成与素材一致
     * 3.判定该创意
     * 消费时一次处理一个创意
     */
    @SneakyThrows
    public void processCreativeMaterials(CreativeJudgeAuditMsgBo creativeJudgeAuditMsgBo) {
        log.info("processCreativeMaterials judge [创意判定]start, judgeAuditMsgBos={}", creativeJudgeAuditMsgBo);
        List<CreativeMaterialJudgeAuditMsgBo> judgeAuditMsgBos = creativeJudgeAuditMsgBo.getData();
        if (CollectionUtils.isEmpty(judgeAuditMsgBos)) {
            log.info("processCreativeMaterials, judgeAuditMsgBos is empty");
            return;
        }
        // 一次只会处理一个创意的，故judgeAuditMsgBos
        CreativeMaterialJudgeAuditMsgBo auditMsgBo = judgeAuditMsgBos.get(0);
        if (!Utils.isPositive(auditMsgBo.getCreativeId())) {
            log.error("processCreativeMaterials judge [创意判定], auditMsgBo.getCreativeId() is negative");
            return;
        }

        // 加锁
        RLock lock = adpRedissonClient.getLock(RiskConstants.LOCK_CREATIVE_JUDGE_AUDIT_RESULT + auditMsgBo.getCreativeId());
        Long startTime = System.currentTimeMillis();
        boolean isLocked = lock.tryLock(5, 10, TimeUnit.SECONDS);
        CreativeJudgeContext context = null;
        if (isLocked) {
            try {
                context = doProcessCreativeMaterials(auditMsgBo);
            } catch (Exception e) {
                log.error("processCreativeMaterials error, auditMsgBo={}", auditMsgBo, e);
                Long endTime = System.currentTimeMillis();
                MetricDataHolder data = new MetricDataHolder();
                data.setDomainType(MetricsCodeEnum.DomainType.CREATIVE_MATERIAL_TASK.name());
                data.setType(MetricsCodeEnum.Type.BIZ_INDEX.name());
                data.setCode(MetricsCodeEnum.SubCode.BIZ_CREATIVE_JUDGE.getCode());
                data.setMsg(MetricsCodeEnum.SubCode.BIZ_CREATIVE_JUDGE.getDesc());
                data.setUsername(auditMsgBo.getExecuteName());
                data.setMaterialTypeName("");
                data.setSize(String.valueOf(1));
                if (context != null) {
                    data.setIsProgram(context.getCreativeBo().getIsProgrammatic() + "");
                }
                data.setSuccess(MetricsCodeEnum.SubCode.FAIL.getDesc());
                taskMetricsReportService.addBizMetricCountAndCost(endTime - startTime, data);
                throw e;
            } finally {
                if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }
        } else {
            throw new RuntimeException("processCreativeMaterials judge [创意判定]获取锁失败, creativeId=" + auditMsgBo.getCreativeId());
        }
    }

    private CreativeJudgeContext doProcessCreativeMaterials(CreativeMaterialJudgeAuditMsgBo auditMsgBo) {
        Long startTime = System.currentTimeMillis();

        // 如果需要审核任务(针对由素材变动触发该素材其他创意任务审核和判定)
        auditTaskIfNecessary(auditMsgBo);

        // 查询用到的上下文
        CreativeJudgeContext context = buildContext(auditMsgBo);
        if (context == null) {
            return context;
        }

        // todo simer: 白名单创意则不进行判定处理

        // 处理创意维度的素材
        processCreativeMaterialAuditStatus(context);
        // 该创意存在待审素材，则不处理
        if (context.isExistMaterialNotAuditCreative()) {
            log.info("processCreativeMaterials, creativeId={}, existMaterialNotAudit", auditMsgBo.getCreativeId());
            return context;
        }

        // 构建程序化素材details
        buildProgramDetails(context);

        // 判定创意审核接口
        callCreativeAuditRpc(context);

        Long endTime = System.currentTimeMillis();

        MetricDataHolder data = new MetricDataHolder();
        data.setDomainType(MetricsCodeEnum.DomainType.CREATIVE_MATERIAL_TASK.name());
        data.setType(MetricsCodeEnum.Type.BIZ_INDEX.name());
        data.setCode(MetricsCodeEnum.SubCode.BIZ_CREATIVE_JUDGE.getCode());
        data.setMsg(MetricsCodeEnum.SubCode.BIZ_CREATIVE_JUDGE.getDesc());
        data.setUsername(auditMsgBo.getExecuteName());
        data.setMaterialTypeName("");
        data.setSize(String.valueOf(1));
        data.setSubSize(context.getAuditTaskPos().size() + "");
        data.setIsProgram(context.getCreativeBo().getIsProgrammatic() + "");
        data.setSuccess(MetricsCodeEnum.SubCode.SUCCESS.getDesc());
        taskMetricsReportService.addBizMetricCountAndCost(endTime - startTime, data);
        return context;
    }

    private void auditTaskIfNecessary(CreativeMaterialJudgeAuditMsgBo auditMsgBo) {
        // 任务修改为与素材一致(针对由素材变动触发其他创意需要处理并判定)
        if (StringUtils.isEmpty(auditMsgBo.getMaterialId())) {
            return;
        }
        List<LauMaterialAuditTaskPo> triggerTaskPos = materialAuditTaskDbService.queryListByCreativeIdAndMaterialId(auditMsgBo.getCreativeId(), auditMsgBo.getMaterialId());

        LauMaterialAuditTaskPo triggerMaterialAuditTaskPo = triggerTaskPos.get(0);

        // 1、更新素材审核任务表，通过记录锁，锁定当前行
        LauMaterialAuditBo existMaterialAuditBo = LauMaterialAuditBo.builder().materialId(triggerMaterialAuditTaskPo.getMaterialId()).mtime(Utils.getNow()).build();
        lauMaterialAuditService.updateSelectiveByMaterialId(existMaterialAuditBo);

        LauMaterialAuditBo triggerMaterialAuditBo = lauMaterialAuditService.fetchByMaterialId(auditMsgBo.getMaterialId());

        if (!Utils.isPositive(auditMsgBo.getNeedProcessTask())) {
            log.info("processCreativeMaterials, 该任务前置位应该审核处理了，不需要再处理, creativeId={},materialId={},taskId={}", auditMsgBo.getCreativeId(), auditMsgBo.getMaterialId(), triggerMaterialAuditTaskPo.getTaskId());
            return;
        }

        // 可能会重复审核
        if (null != triggerMaterialAuditTaskPo && triggerMaterialAuditBo != null) {
            log.info("auditTaskIfNecessary,creativeId={},materialId={},taskId={},material.labels={},taskOldLabels={}", auditMsgBo.getCreativeId(), auditMsgBo.getMaterialId(), triggerMaterialAuditTaskPo.getTaskId(), triggerMaterialAuditBo.getAuditLabelThirdId(), triggerMaterialAuditTaskPo.getAuditLabelThirdId());
            LauMaterialAuditTaskPo auditTaskPo = new LauMaterialAuditTaskPo();
            auditTaskPo.setTaskId(triggerMaterialAuditTaskPo.getTaskId());
            String auditLabelThirdId = triggerMaterialAuditBo.getAuditLabelThirdId();
            auditTaskPo.setAuditLabelThirdId(auditLabelThirdId);
            if (!StringUtils.isEmpty(triggerMaterialAuditBo.getReason())) {
                auditTaskPo.setReason(triggerMaterialAuditBo.getReason());
            }

            // 由 risk 触审时，执行人不更新
            if (!Objects.equals(triggerMaterialAuditTaskPo.getStatus(), MaterialTaskStatusEnum.TO_CALLBACK_AUDIT.getCode())
                    && !Objects.equals(triggerMaterialAuditTaskPo.getStatus(), MaterialTaskStatusEnum.COMPLETE.getCode())) {
                auditTaskPo.setExecuteName(auditMsgBo.getExecuteName());
            }

            if (auditMsgBo.getTaskType() != null && !Objects.equals(triggerMaterialAuditTaskPo.getStatus(), MaterialTaskStatusEnum.COMPLETE.getCode())) {
                auditTaskPo.setType(auditMsgBo.getTaskType());
                auditTaskPo.setExecuteTime(Utils.getNow());
            }

            auditTaskPo.setStatus(MaterialTaskStatusEnum.COMPLETE.getCode());
            materialAuditTaskDbService.updateTask(auditTaskPo);
            log.info("processCreativeMaterials judge [任务与素材三级标签不一致,修改任务], taskId={}, auditLabelThirdId={}, status=COMPLETE", triggerMaterialAuditTaskPo.getTaskId(), auditLabelThirdId);

            // 防止重复记录日志
            if (Objects.equals(auditTaskPo.getStatus(), triggerMaterialAuditTaskPo.getStatus())
                    && Objects.equals(auditTaskPo.getAuditLabelThirdId(), triggerMaterialAuditTaskPo.getAuditLabelThirdId())
                    && Objects.equals(auditTaskPo.getExecuteName(), triggerMaterialAuditTaskPo.getExecuteName())
                    && Objects.equals(auditTaskPo.getType(), triggerMaterialAuditTaskPo.getType())
                    && Objects.equals(auditTaskPo.getReason(), triggerMaterialAuditTaskPo.getReason())) {
                log.info("processCreativeMaterials judge [任务已审核,不再记录log], taskId={}, auditLabelThirdId={}, status=COMPLETE", triggerMaterialAuditTaskPo.getTaskId(), auditLabelThirdId);
                return;
            }

            // 素材任务-机审-流量上报(某个【素材任务】推审后，由此出发的其他【素材任务】的自动翻转逻辑)
            taskFlowStatPub.pub(TaskFlowStatMsg.builder()
                    .taskType(StatTaskType.MATERIAL_TASK.getCode())
                    .bizType(MaterialBizTypeEnum.SANLIAN.getCode())
                    .belongingDate(Utils.getToday().getTime())
                    .countType(StatCountType.ACMS.getCode())
                    .build());

            LauMaterialAuditTaskUpdateBo oldUpdateBo = IMaterialAuditTaskConvertor.INSTANCE.po2UpdateBo(triggerMaterialAuditTaskPo);
            LauMaterialAuditTaskUpdateBo newUpdateBo = IMaterialAuditTaskConvertor.INSTANCE.po2UpdateBo(auditTaskPo);
            String diffStr = DiffLogUtils.generateDiffField(oldUpdateBo, newUpdateBo, "任务对象");

            // 记录日志
            String logValue = MaterialTaskUtils.genAuditLogValue(auditLabelThirdId, materialAuditLabelService.queryAllMap());
            Integer operationType = OperationTypeEnum.AUDIT.getCode();
            if (Objects.equals(MaterialTaskTypeEnum.REUSE.getCode(), auditMsgBo.getTaskType())) {
                operationType = OperationTypeEnum.REUSE_RESULT.getCode();
                logValue += " 复用的任务id: " + auditMsgBo.getTriggerReuseTaskId();
            }

            MaterialAuditTaskOperationLogBo logBo = MaterialAuditTaskOperationLogBo.builder()
                    .objId(triggerMaterialAuditTaskPo.getTaskId())
                    .operatorUsername(auditMsgBo.getExecuteName())
                    .operationType(operationType)
                    .diff(diffStr)
                    .remark(JSON.toJSONString(auditTaskPo))
                    .value(logValue)
                    .ctime(System.currentTimeMillis())
                    .type(LogTypeEnum.TASK.getCode())
                    .systemType(RiskConstants.SYSTEM_TYPE_RISK)
                    .build();
            operationLogService.saveLog(logBo);
        }
    }

    private CreativeJudgeContext buildContext(CreativeMaterialJudgeAuditMsgBo auditMsgBo) {
        CreativeBo creativeBo = creativeService.fetchCreativeById(auditMsgBo.getCreativeId());
        if (creativeBo == null) {
            log.error("processCreativeMaterials, creativeBo is null, creativeId={}", auditMsgBo.getCreativeId());
            return null;
        }
        if (Objects.equals(creativeBo.getCreativeStatus(), CreativeStatus.DELETED)) {
            log.info("processCreativeMaterials, creativeBo is deleted, creative status=4, creativeId={}", auditMsgBo.getCreativeId());
            return null;
        }

        if(Objects.equals(creativeBo.getCreativeStatus(), AllStatus.CREATIVE_STATUS_DELETED)){
            log.info("processCreativeMaterials, creativeBo is deleted, creativeId={}", auditMsgBo.getCreativeId());
            return null;
        }

        if (StringUtils.isEmpty(auditMsgBo.getExecuteName())) {
            auditMsgBo.setExecuteName(RiskConstants.SYSTEM_USERNAME);
        }

        // 获取投放端创意素材列表
        ShadowCreativeBo shadowCreativeBo = null;
        List<LauProgrammaticCreativeDetailBo> creativeDetailBos = new ArrayList<>();
        if (Utils.isPositive(creativeBo.getIsProgrammatic())) {
            creativeDetailBos = programCreativeService.queryDetailsByCreativeId(creativeBo.getCreativeId());
            if(CollectionUtils.isEmpty(creativeDetailBos)){
                throw new RuntimeException("程序化创意" + creativeBo.getCreativeId() + "明细不存在");
            }

            LauShadowCreativePo lauShadowCreativePo = creativeService.queryShadow(creativeBo.getCreativeId());

            // 有影子则用影子替换 details
            if (lauShadowCreativePo != null && !StringUtils.isEmpty(lauShadowCreativePo.getShadowCreative())) {
                shadowCreativeBo = convertShadowPo2Bo(lauShadowCreativePo);
                if (!CollectionUtils.isEmpty(shadowCreativeBo.getCreativeDetailBos())) {
                    creativeDetailBos = shadowCreativeBo.getCreativeDetailBos();
                }
            }
        }

        // 获取创意下所有的素材(未删除的)
        List<LauMaterialAuditTaskPo> auditTaskPosOfCreative = materialAuditTaskDbService.queryListByCreativeId(creativeBo.getCreativeId(), IsDeleted.VALID.getCode());
        Optional<LauMaterialAuditTaskPo> taskPoOptional = auditTaskPosOfCreative.stream().filter(t -> t.getMaterialId().equals(auditMsgBo.getMaterialId())).findFirst();
        List<String> materialIds = auditTaskPosOfCreative.stream().map(t -> t.getMaterialId()).distinct().collect(Collectors.toList());
//        Map<String, LauMaterialAuditBo> materialAuditBoMapByMaterialId = lauMaterialAuditService.queryMapByMaterialIds(materialIds);
//        Map<String, LauMaterialAuditBo> materialAuditBoMapByMd5 = materialAuditBoMapByMaterialId.values().stream().collect(Collectors.toMap(t -> t.getMaterialMd5(), t -> t));
        Map<String, LauMaterialAuditTaskPo> auditTaskPoMapByMd5 = auditTaskPosOfCreative.stream().collect(Collectors.toMap(t -> MaterialTaskUtils.genKey(t.getMaterialMd5(), t.getMaterialType()), t -> t));
        List<Long> thirdLabelIds = auditTaskPosOfCreative.stream().map(t -> t.getAuditLabelThirdId()).filter(ids -> !StringUtils.isEmpty(ids))
                .flatMap(s -> Arrays.stream(s.split(","))) // 拆分每个字符串
                .map(String::trim) // 去除前后空格
                .map(Long::valueOf) // 转换为Integer
                .distinct()
                .collect(Collectors.toList());

        LauMaterialAuditBo materialAuditBo = null;
        if (!StringUtils.isEmpty(auditMsgBo.getMaterialId())) {
            materialAuditBo = lauMaterialAuditService.fetchByMaterialId(auditMsgBo.getMaterialId());
        }

        // 创意下的素材任务
        if (CollectionUtils.isEmpty(auditTaskPosOfCreative)) {
            log.info("processCreativeMaterials judge [创意判定], creativeId={}, creativeRelPosOfCreative is empty", creativeBo.getCreativeId());
            return null;
        }
        Set<String> detailKeys = creativeDetailBos.stream().map(t -> MaterialTaskUtils.genKey(t.getMaterialMd5(), t.getMaterialType())).collect(Collectors.toSet());
        log.info("processCreativeMaterials judge [创意判定], creativeId={}, auditTaskPoMapByMd5.key={},detailKeys={}", creativeBo.getCreativeId(), auditTaskPoMapByMd5.keySet(), detailKeys);

        // label
        Map<Long, LauMaterialAuditLabelBo> auditLabelBoMap = materialAuditLabelService.queryMapByIds(thirdLabelIds);

        return CreativeJudgeContext.builder()
                .creativeId(auditMsgBo.getCreativeId())
                .triggerMaterialId(auditMsgBo.getMaterialId())
                .executeName(auditMsgBo.getExecuteName())
                .creativeBo(creativeBo)
                .creativeDetailBos(creativeDetailBos)
                .auditTaskPos(auditTaskPosOfCreative)
                .materialAuditTaskPoMapByMd5(auditTaskPoMapByMd5)
                .auditLabelBoMap(auditLabelBoMap)
//                .triggerMaterialAuditTaskPo(taskPoOptional.orElse(null))
//                .triggerMaterialAuditBo(materialAuditBo)
                .shadowCreativeBo(shadowCreativeBo)
                .build();
    }

    private void callCreativeAuditRpc(CreativeJudgeContext context) {
        CreativeBo creativeBo = context.getCreativeBo();

        com.bapis.ad.audit.Operator rpcOperator = buildRpcOperator(context);

        // 自定义
        if (!Utils.isPositive(creativeBo.getIsProgrammatic())) {
            auditCustomizeCreative(context, creativeBo, rpcOperator);
        }
        // 程序化
        else {
            auditProgramCreative(context, creativeBo, rpcOperator);
        }
    }

    private void auditProgramCreative(CreativeJudgeContext context, CreativeBo creativeBo, com.bapis.ad.audit.Operator rpcOperator) {
        if (context.isExistMaterialNotAuditProgram()) {
            log.info("processCreativeMaterials judge [存在程序化元素未审核], creativeId={},is_programmatic={}, isExistMaterialNotAuditProgram", creativeBo.getCreativeId(), creativeBo.getIsProgrammatic());
            return;
        }
        if (CollectionUtils.isEmpty(context.getMaterialBosProgram())) {
            log.info("processCreativeMaterials, creativeId={},is_programmatic={}, materialBos is empty", creativeBo.getCreativeId(), creativeBo.getIsProgrammatic());
            return;
        }
        // 构建素材
        log.info("processCreativeMaterials judge [程序化进行审核], creativeId={},is_programmatic={},miscElem={},materialElems={}", creativeBo.getCreativeId(), creativeBo.getIsProgrammatic(), context.getMiscElemBoProgram(), JSON.toJSONString(context.getMaterialBosProgram()));

        ProgramAuditRequest auditRequest = ProgramAuditRequest.newBuilder()
                .setOperator(rpcOperator)
                .setMiscElemAuditInfo(ICreativeConvertor.INSTANCE.fromBo(context.getMiscElemBoProgram()))
                // 前端传的是 material.id 放到了 id字段里，后端将 id 和 material id 都设置成了 material id
                .addAllMaterialElem(ICreativeConvertor.INSTANCE.fromBos(context.getMaterialBosProgram()))
                .setNeedProcessParentCreative(true)
                .setRecheckFlag(false)
                .setIsSplitMaterial(1)
                .build();
        log.info("processCreativeMaterials judge [程序化进行审核], creativeId={},is_programmatic={}, auditRequest={}", creativeBo.getCreativeId(), creativeBo.getIsProgrammatic(), auditRequest);
        ProgramAuditReply programAuditReply = pandoraAuditServiceBlockingStub
                .withDeadlineAfter(5000, TimeUnit.MILLISECONDS)
                .pandoraAuditProgramCreative(auditRequest);
        log.info("processCreativeMaterials judge [程序化进行审核], creativeId={},is_programmatic={}, programAuditReply={}", creativeBo.getCreativeId(), creativeBo.getIsProgrammatic(), programAuditReply);
    }

    private void auditCustomizeCreative(CreativeJudgeContext context, CreativeBo creativeBo, com.bapis.ad.audit.Operator rpcOperator) {
        // 驳回
        if (Objects.equals(context.getCreativeMaterialHandleType(), MaterialHandleTypeEnum.REJECT.getCode())) {
            AuditInfo auditInfo = AuditInfo.newBuilder()
                    .setCreativeId(creativeBo.getCreativeId())
                    .setRejectReason(context.getCreativeReason())
                    .setVersion(creativeBo.getVersion())
                    .setOperator(rpcOperator)
                    .setReCheckFlag(0)
                    .setReCheckRemark("")
                    .setUnderFrameAuditFlag(0)
                    .build();
            pandoraAuditServiceBlockingStub
                    .withDeadlineAfter(5000, TimeUnit.MILLISECONDS)
                    .pandoraAuditReject(auditInfo);
            log.info("processCreativeMaterials judge [自定义创意], creativeId={}, auditStatus=REJECT", creativeBo.getCreativeId());

        } else if (Objects.equals(context.getCreativeMaterialHandleType(), MaterialHandleTypeEnum.PASS.getCode())) {
            AuditInfo auditInfo = AuditInfo.newBuilder()
                    .setCreativeId(creativeBo.getCreativeId())
                    .setRejectReason(context.getCreativeReason())
                    .setVersion(creativeBo.getVersion())
                    .setOperator(rpcOperator)
                    .setReCheckFlag(0)
                    .setReCheckRemark("")
                    .build();
            pandoraAuditServiceBlockingStub
                    .withDeadlineAfter(5000, TimeUnit.MILLISECONDS)
                    .pandoraAuditPass(auditInfo);
            log.info("processCreativeMaterials judge [自定义创意], creativeId={}, auditStatus=PADDED", creativeBo.getCreativeId());
        } else {
            log.warn("processCreativeMaterials judge [自定义创意], creativeId={},creativeMaterialHandleType={}, 除此以外", creativeBo.getCreativeId(), context.getCreativeMaterialHandleType());
        }
    }

    private com.bapis.ad.audit.Operator buildRpcOperator(CreativeJudgeContext context) {
        Operator operator = new Operator();
        operator.setOperatorName(context.getExecuteName());

        operator.setBilibiliUserName(context.getExecuteName());
        com.bapis.ad.audit.Operator rpcOperator = com.bapis.ad.audit.Operator.newBuilder()
                .setIp(org.apache.commons.lang3.StringUtils.isEmpty(operator.getIp()) ? "" : operator.getIp())
                .setOperatorType(OperatorType.OPERATING_PERSONNEL)
                .setOperatorId(Utils.isPositive(operator.getOperatorId()) ? operator.getOperatorId() : 0)
                .setOperatorName(org.apache.commons.lang3.StringUtils.isEmpty(operator.getOperatorName()) ? "" : operator.getOperatorName())
                .setBilibiliUserName(org.apache.commons.lang3.StringUtils.isEmpty(operator.getBilibiliUserName()) ? "" : operator.getBilibiliUserName())
                .setSystemType(com.bapis.ad.audit.SystemType.CPM)
                .build();
        return rpcOperator;
    }

    @SneakyThrows
    public ShadowCreativeBo convertShadowPo2Bo(LauShadowCreativePo po) {
        ShadowCreativeBo shadowCreativeBo = JSON.parseObject(po.getShadowCreative(), ShadowCreativeBo.class);
        shadowCreativeBo.setId(po.getId());
        shadowCreativeBo.setAdvertisingMode(Optional.ofNullable(shadowCreativeBo.getAdvertisingMode()).orElse(0));
        shadowCreativeBo.setJumpUrlSecondary(Optional.ofNullable(shadowCreativeBo.getJumpUrlSecondary()).orElse(org.apache.commons.lang3.StringUtils.EMPTY));
        shadowCreativeBo.setMgkPageId(Optional.ofNullable(shadowCreativeBo.getMgkPageId()).orElse(0L));
        shadowCreativeBo.setCmMark(Optional.ofNullable(shadowCreativeBo.getCmMark()).orElse(0));
        shadowCreativeBo.setBusMark(Optional.ofNullable(shadowCreativeBo.getBusMark()).orElse(0));
        shadowCreativeBo.setAuditStatus(po.getAuditStatus());
        shadowCreativeBo.setCreativeStatus(po.getCreativeStatus());
        shadowCreativeBo.setReason(po.getReason());
        shadowCreativeBo.setIsPageGroup(Optional.ofNullable(shadowCreativeBo.getIsPageGroup()).orElse(0));
        shadowCreativeBo.setExtDescription(Optional.ofNullable(shadowCreativeBo.getExtDescription()).orElse(org.apache.commons.lang3.StringUtils.EMPTY));
        shadowCreativeBo.setVideoId(Optional.ofNullable(shadowCreativeBo.getVideoId()).orElse(0L));
        shadowCreativeBo.setProgMiscElemAuditStatus(Optional.ofNullable(shadowCreativeBo.getProgMiscElemAuditStatus()).orElse(0));
        shadowCreativeBo.setProgAuditStatus(Optional.ofNullable(shadowCreativeBo.getProgAuditStatus()).orElse(0));
        return shadowCreativeBo;
    }

    private void buildProgramDetails(CreativeJudgeContext context) {
        CreativeBo creativeBo = context.getCreativeBo();
        // 构建程序化创意主副元素
        boolean existMaterialNotAuditProgram = false;
        List<LauProgrammaticCreativeDetailBo> creativeDetailBos = context.getCreativeDetailBos();
        List<MaterialBo> materialBosProgram = new ArrayList<>();
        if (Utils.isPositive(creativeBo.getIsProgrammatic())) {
            for (LauProgrammaticCreativeDetailBo creativeDetailBo : creativeDetailBos) {
                Integer materialType = creativeDetailBo.getMaterialType();
                // 兼容gif情况，将gif的3转成risk的1
                if (Objects.equals(RiskConstants.LAUNCH_GIF_MATERIAL_TYPE, materialType)) {
                    materialType = RiskMaterialTypeEnum.IMAGE.getCode();
                }
                String materialKey = MaterialTaskUtils.genKey(creativeDetailBo.getMaterialMd5(), materialType);

                LauMaterialAuditTaskPo auditTaskPo = context.getMaterialAuditTaskPoMapByMd5().get(materialKey);
                if (auditTaskPo == null) {
                    log.error("auditTaskPo is null, materialId={}, detailId={},creativeId={},key={}", creativeDetailBo.getMaterialId(), creativeDetailBo.getId(), creativeDetailBo.getCreativeId(), materialKey);
                    existMaterialNotAuditProgram = true;
                    continue;
                }

                // 程序化元素不会有落地页，只需要看标签
                List<LauMaterialAuditLabelBo> threeAuditLabelBosOfMaterial = context.getMaterialThreeAuditLabelMapByTaskId().get(auditTaskPo.getTaskId());
                if (CollectionUtils.isEmpty(threeAuditLabelBosOfMaterial)) {
                    log.error("threeAuditLabelBosOfMaterial is null[prog存在任务未审核], taskId={}, auditLabelThirdId={},materialMd5={}", auditTaskPo.getTaskId(), auditTaskPo.getAuditLabelThirdId(), auditTaskPo.getMaterialMd5());
                    existMaterialNotAuditProgram = true;
                    continue;
                }
                LauMaterialAuditLabelBo auditLabelBoOfMaterial = threeAuditLabelBosOfMaterial.get(0);
                Optional<MaterialHandleTypeEnum> handleTypeEnumOptional = MaterialHandleTypeEnum.getByCode(auditLabelBoOfMaterial.getHandleType());

                MaterialBo.MaterialBoBuilder materialBoBuilder = MaterialBo.builder().materialId(creativeDetailBo.getMaterialId());
                if (handleTypeEnumOptional.isPresent()) {
                    MaterialHandleTypeEnum materialHandleTypeEnum = handleTypeEnumOptional.get();
                    materialBoBuilder.status(materialHandleTypeEnum.getProgMaterialAuditStatus());
                    // 拼接多个原因
                    materialBoBuilder.reason(MaterialHandleTypeEnum.pandoraReason(threeAuditLabelBosOfMaterial));
                    materialBoBuilder.type(creativeDetailBo.getMaterialType());
                }
                materialBosProgram.add(materialBoBuilder.build());
            }
        }

        // 构建程序化额外元素(排除主副元素)
        Set<String> detailMaterialMd5s = creativeDetailBos.stream().map(t -> t.getMaterialMd5()).collect(Collectors.toSet());
        // 程序化创意审核状态: 0-审核通过, 1-待审核, 2-审核拒绝, 3-落地页待审核
        Integer miscElemStatus = ProgMaterialAuditStatusEnum.AUDIT_OK.getCode();
        List<LauMaterialAuditTaskPo> progMiscElemTaskPos = context.getAuditTaskPos().stream().filter(t -> !detailMaterialMd5s.contains(t.getMaterialMd5())).collect(Collectors.toList());
        for (LauMaterialAuditTaskPo progMiscElemTaskPo : progMiscElemTaskPos) {
            // 程序化元素不会有落地页，只需要看标签
            List<LauMaterialAuditLabelBo> threeAuditLabelBosOfMaterial = context.getMaterialThreeAuditLabelMapByTaskId().get(progMiscElemTaskPo.getTaskId());
            if (CollectionUtils.isEmpty(threeAuditLabelBosOfMaterial)) {
                continue;
            }
            if (threeAuditLabelBosOfMaterial.stream().anyMatch(t -> Objects.equals(t.getHandleType(), MaterialHandleTypeEnum.REJECT.getCode()))) {
                miscElemStatus = ProgMaterialAuditStatusEnum.AUDIT_REJECTED.getCode();
                break;
            }
        }

        MiscElemBo miscElemBo = MiscElemBo.builder()
                .creativeId(creativeBo.getCreativeId())
                // 判断额外素材状态
                .status(miscElemStatus)
                // 拼接额外素材的驳回理由
                .reason(context.getCreativeReason())
                .accountId(creativeBo.getAccountId())
                .build();

        context.setExistMaterialNotAuditProgram(existMaterialNotAuditProgram);
        context.setMaterialBosProgram(materialBosProgram);
        context.setMiscElemBoProgram(miscElemBo);
    }

    /**
     * @param context
     */
    private void processCreativeMaterialAuditStatus(CreativeJudgeContext context) {
        List<LauMaterialAuditTaskPo> auditTaskPosOfCreative = context.getAuditTaskPos();

        // 判断是否存在素材未审核
        boolean existMaterialNotAuditCreative = judgeIfExistTaskMaterialNotAudit(context, auditTaskPosOfCreative);
        log.info("processCreativeMaterials judge [判断是否有素材未审核], existMaterialNotAuditCreative={}", existMaterialNotAuditCreative);
        context.setExistMaterialNotAuditCreative(existMaterialNotAuditCreative);

        // 存在素材未审核
        if (existMaterialNotAuditCreative) {
            return;
        }

        // 构建素材的审核信息
        // 走到这里说明所有素材都审核了
        buildDetailMaterialsAuditInfos(context, auditTaskPosOfCreative);
        log.info("processCreativeMaterials judge [判断创意的处理类型], creativeId={}, creativeMaterialHandleType={}, creativeReason={}", context.getCreativeId(), context.getCreativeMaterialHandleType(), context.getCreativeReason());
    }

    private void buildDetailMaterialsAuditInfos(CreativeJudgeContext context, List<LauMaterialAuditTaskPo> auditTaskPosOfCreative) {
        Map<Integer, Set<String>> materialTypeReasons = new HashMap<>();
        Integer creativeHandlerType = MaterialHandleTypeEnum.NO_HANDLE.getCode();

        Set<Integer> materialHandleTypes = new HashSet<>();
        Map<String, List<LauMaterialAuditLabelBo>> materialThreeAuditLabelMapByTaskId = new HashMap<>();

        // 按素材类型分组，每个组拼接理由，拼接驳回理由：文本【理由1；理由2】，图片【理由1】
        Map<Integer, List<LauMaterialAuditTaskPo>> taskPoMapByMaterialType = auditTaskPosOfCreative.stream().collect(Collectors.groupingBy(t -> t.getMaterialType()));
        for (Map.Entry<Integer, List<LauMaterialAuditTaskPo>> entry : taskPoMapByMaterialType.entrySet()) {
            Set<String> reasonSet = new HashSet<>();

            // 非落地页组
            if (!Objects.equals(entry.getKey(), RiskMaterialTypeEnum.PAGE_GROUP_PAGE.getCode())) {
                for (LauMaterialAuditTaskPo auditTaskPo : entry.getValue()) {
                    Pair<Integer, Set<String>> pair = getAuditResult(context, auditTaskPo, materialThreeAuditLabelMapByTaskId);

                    materialHandleTypes.add(pair.getLeft());
                    reasonSet.addAll(pair.getRight());
                }
            }
            // 落地页组
            else {
                Map<Long, List<LauMaterialAuditTaskPo>> pageGroupMap = entry.getValue().stream().collect(Collectors.groupingBy(t -> RiskMaterialTypeEnum.getPageGroupId(t.getMaterialContent())));

                out:
                for (Map.Entry<Long, List<LauMaterialAuditTaskPo>> e : pageGroupMap.entrySet()) {
                    Set<String> pageGroupReason = new HashSet<>();
                    for (LauMaterialAuditTaskPo auditTaskPo : e.getValue()) {

                        Pair<Integer, Set<String>> pair = getAuditResult(context, auditTaskPo, materialThreeAuditLabelMapByTaskId);
                        if (Objects.equals(pair.getLeft(), MaterialHandleTypeEnum.PASS.getCode())) {
                            materialHandleTypes.add(MaterialHandleTypeEnum.PASS.getCode());
                            continue out;
                        }

                        pageGroupReason.addAll(pair.getRight());
                    }

                    // 到这儿说明组内全部驳回
                    materialHandleTypes.add(MaterialHandleTypeEnum.REJECT.getCode());
                    reasonSet.addAll(pageGroupReason);
                }
            }

            materialTypeReasons.put(entry.getKey(), reasonSet);
        }

        // 存在一个驳回则驳回，否则为通过
        if (!CollectionUtils.isEmpty(materialHandleTypes)) {
            if (materialHandleTypes.stream().anyMatch(t -> Objects.equals(t, MaterialHandleTypeEnum.REJECT.getCode()))) {
                creativeHandlerType = MaterialHandleTypeEnum.REJECT.getCode();
            } else {
                creativeHandlerType = materialHandleTypes.stream().findFirst().get();
            }
        }
        context.setCreativeMaterialHandleType(creativeHandlerType);
        String reason = sliceRejectReasons(materialTypeReasons);
        context.setCreativeReason(reason);
        context.setMaterialThreeAuditLabelMapByTaskId(materialThreeAuditLabelMapByTaskId);
    }

    private Pair<Integer, Set<String>> getAuditResult(CreativeJudgeContext context, LauMaterialAuditTaskPo auditTaskPo
            , Map<String, List<LauMaterialAuditLabelBo>> materialThreeAuditLabelMapByTaskId) {
        MutablePair<Integer, Set<String>> pair = new MutablePair(MaterialHandleTypeEnum.NO_HANDLE.getCode(), new HashSet<>());
        if (!StringUtils.isEmpty(auditTaskPo.getAuditLabelThirdId())) {
            // 解析出三级标签
            List<Long> thirdLabelIdListOfMaterial = Arrays.stream(auditTaskPo.getAuditLabelThirdId().split(",")).map(t -> Long.valueOf(t)).collect(Collectors.toList());

            List<LauMaterialAuditLabelBo> threeAuditLabelBosOfMaterial = thirdLabelIdListOfMaterial.stream().map(id -> context.getAuditLabelBoMap().get(id))
                    .filter(Objects::nonNull)
                    .filter(l -> l.getLevel().equals(AuditLabelLevelEnum.THREE.getCode())).
                    collect(Collectors.toList());

            if (!Objects.equals(auditTaskPo.getMaterialType(), RiskMaterialTypeEnum.PAGE_GROUP_PAGE.getCode())) {
                materialThreeAuditLabelMapByTaskId.put(auditTaskPo.getTaskId(), threeAuditLabelBosOfMaterial);
            }

            if (!CollectionUtils.isEmpty(threeAuditLabelBosOfMaterial)) {
                LauMaterialAuditLabelBo auditLabelBo = threeAuditLabelBosOfMaterial.get(0);
                // 任何一个的处置类型一样的
                pair.setLeft(auditLabelBo.getHandleType());
                // 驳回理由拼接
                for (LauMaterialAuditLabelBo tmpAuditLabelBo : threeAuditLabelBosOfMaterial) {
                    // 驳回理由优先用任务自定义的reason, 没有则用三级标签的
                    if (Objects.equals(MaterialHandleTypeEnum.REJECT.getCode(), tmpAuditLabelBo.getHandleType())) {
                        if (!StringUtils.isEmpty(auditTaskPo.getReason())) {
                            pair.getRight().add(auditTaskPo.getReason());
                        } else {
                            if (!StringUtils.isEmpty(tmpAuditLabelBo.getReason())) {
                                pair.getRight().add(tmpAuditLabelBo.getReason());
                            }
                        }
                    }
                }
            }
        } else {
            throw new RuntimeException("auditTaskPo.getAuditLabelThirdId() is empty");
        }
        return pair;
    }

    /**
     * 拼接驳回理由：文本【理由1；理由2】，图片【理由1】
     *
     * @param materialTypeReasons, key是素材类型，value是该类型的理由列表
     * @return
     */
    private static String sliceRejectReasons(Map<Integer, Set<String>> materialTypeReasons) {
        StringBuilder reasons = new StringBuilder();
        for (Map.Entry<Integer, Set<String>> entry : materialTypeReasons.entrySet()) {
            if (CollectionUtils.isEmpty(entry.getValue())) {
                continue;
            }
            String reasonThisMaterialType = entry.getValue().stream().collect(Collectors.joining("；", "【", "】"));
            RiskMaterialTypeEnum riskMaterialTypeEnum = RiskMaterialTypeEnum.getByCode(entry.getKey());
            reasonThisMaterialType = riskMaterialTypeEnum.getDesc() + reasonThisMaterialType;
            reasons.append(reasonThisMaterialType).append("，");
        }
        if (reasons.length() > 0) {
            reasons.deleteCharAt(reasons.length() - 1);
        }

        // 创意上目前最大支持255
        if (reasons.length() > 255) {
            reasons = new StringBuilder(reasons.substring(0, 255));
        }
        return reasons.toString();
    }

    private static boolean judgeIfExistTaskMaterialNotAudit(CreativeJudgeContext context, List<LauMaterialAuditTaskPo> auditTaskPosOfCreative) {
        boolean existMaterialNotAuditCreative = false;

        // 落地页组是否审出
        Map<Long, Boolean> pageGroupAudit = new HashMap<>();

        for (LauMaterialAuditTaskPo auditTaskPo : auditTaskPosOfCreative) {
            String materialId = auditTaskPo.getMaterialId();
            if (Objects.equals(auditTaskPo.getIsDeleted(), IsDeleted.DELETED)) {
                continue;
            }

            // 非落地页组 任务未审核
            if (!Objects.equals(auditTaskPo.getMaterialType(), RiskMaterialTypeEnum.PAGE_GROUP_PAGE.getCode())
                    && !Objects.equals(auditTaskPo.getStatus(), MaterialTaskStatusEnum.COMPLETE.getCode())) {
                log.info("processCreativeMaterials, taskId={}, materialId={}, status={}", auditTaskPo.getTaskId(), materialId, auditTaskPo.getStatus());
                existMaterialNotAuditCreative = true;
                break;
            }

            // 落地页组：1、组内有审核通过的落地页，过审；2、组内全部审核拒绝，不过审；3、组内有未审核的落地页，不审出
            if (Objects.equals(auditTaskPo.getMaterialType(), RiskMaterialTypeEnum.PAGE_GROUP_PAGE.getCode())) {
                Long groupId = RiskMaterialTypeEnum.getPageGroupId(auditTaskPo.getMaterialContent());
                if (Objects.equals(auditTaskPo.getAuditLabelThirdId(), AuditLabelIdSpecialEnum.PAGE_GROUP_PASS.getCode())
                        && Objects.equals(auditTaskPo.getStatus(), MaterialTaskStatusEnum.COMPLETE.getCode())) {
                    pageGroupAudit.put(groupId, true);
                } else if (!Boolean.TRUE.equals(pageGroupAudit.get(groupId))
                        && !Objects.equals(auditTaskPo.getStatus(), MaterialTaskStatusEnum.COMPLETE.getCode())) {
                    pageGroupAudit.put(groupId, false);
                }
            }
        }
        return existMaterialNotAuditCreative || pageGroupAudit.values().stream().anyMatch(Boolean.FALSE::equals);
    }


}
