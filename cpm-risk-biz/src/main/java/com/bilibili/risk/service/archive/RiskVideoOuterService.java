package com.bilibili.risk.service.archive;

import com.bapis.ad.mng.risk_video.QueryRiskVideoReq;
import com.bapis.ad.mng.risk_video.QueryRiskVideoResp;
import com.bapis.ad.mng.risk_video.RiskVideoServiceGrpc;
import com.bapis.ad.mng.risk_video.SingleQueryRiskVideoResp;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.mall.kraken.boot.autoconfigure.config.DynamicValue;
import com.bilibili.risk.bo.ArchiveRiskVideoBo;
import com.bilibili.risk.config.RedisConfig;
import com.bilibili.risk.convertor.IArchiveConvertor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import pleiades.venus.starter.rpc.client.RPCClient;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class RiskVideoOuterService {

    private static final String RISK_VIDEO_CACHE_KEY_PREFIX = "risk:archive:risk_video:";

    @Value("${risk.videoCacheExpireTime:2}")
    @DynamicValue
    private Integer CACHE_EXPIRE_TIME; // 缓存过期时间，单位分钟

    @Value("${risk.videoCacheExpireSwitch:1}")
    @DynamicValue
    private Integer cacheSwitch;

    @RPCClient("sycpb.cpm.cpm-mng")
    private RiskVideoServiceGrpc.RiskVideoServiceBlockingStub riskVideoServiceBlockingStub;

    @Resource(name = RedisConfig.ADP_CLUSTER)
    private RedissonClient adpRedissonClient;

    /**
     * 获取风控视频信息，使用Redis缓存优化
     *
     * @param avids 视频ID列表
     * @return 视频ID到风控信息的映射
     */
    public Map<Long, ArchiveRiskVideoBo> fetchRiskVideoMap(List<Long> avids) {
        if (CollectionUtils.isEmpty(avids)) {
            return Collections.emptyMap();
        }

        if (!Utils.isPositive(cacheSwitch)) {
            List<ArchiveRiskVideoBo> archiveRiskVideoBos = queryRiskVideoByRpc(avids);
            return archiveRiskVideoBos.stream().collect(Collectors.toMap(t -> t.getAvid(), t -> t, (v1, v2) -> v2));
        }

        // 1. 创建结果Map
        Map<Long, ArchiveRiskVideoBo> resultMap = new HashMap<>();
        // 2. 需要从RPC获取的ID列表
        List<Long> needFetchAvids = new ArrayList<>();

        // 3. 尝试从Redis缓存中获取
        for (Long avid : avids) {
            String cacheKey = RISK_VIDEO_CACHE_KEY_PREFIX + avid;
            RBucket<ArchiveRiskVideoBo> bucket = adpRedissonClient.getBucket(cacheKey);
            ArchiveRiskVideoBo cachedData = bucket.get();

            if (cachedData != null) {
                // 缓存命中，添加到结果Map
                resultMap.put(avid, cachedData);
                log.debug("从缓存获取风控视频信息 avid={}", avid);
            } else {
                // 缓存未命中，添加到需要获取的列表
                needFetchAvids.add(avid);
            }
        }

        // 4. 如果所有数据都从缓存获取到了，直接返回
        if (needFetchAvids.isEmpty()) {
            log.info("所有风控视频信息从缓存获取成功 avids={}", avids);
            return resultMap;
        }

        // 5. 从RPC获取未缓存的数据
        log.info("从RPC获取风控视频信息 avids={}", needFetchAvids);
        List<ArchiveRiskVideoBo> riskVideoBos = queryRiskVideoByRpc(needFetchAvids);

        // 6. 将RPC获取的数据添加到结果Map并缓存
        for (ArchiveRiskVideoBo riskVideoBo : riskVideoBos) {
            Long avid = riskVideoBo.getAvid();
            resultMap.put(avid, riskVideoBo);

            // 缓存数据
            String cacheKey = RISK_VIDEO_CACHE_KEY_PREFIX + avid;
            RBucket<ArchiveRiskVideoBo> bucket = adpRedissonClient.getBucket(cacheKey);
            bucket.set(riskVideoBo, CACHE_EXPIRE_TIME, TimeUnit.MINUTES);
            log.debug("风控视频信息已缓存 avid={}, 过期时间={}分钟", avid, CACHE_EXPIRE_TIME);
        }

        log.info("风控视频信息获取完成 总数={}, 缓存命中={}, RPC获取={}",
                resultMap.size(),
                resultMap.size() - riskVideoBos.size(),
                riskVideoBos.size());

        return resultMap;
    }

    private List<ArchiveRiskVideoBo> queryRiskVideoByRpc(List<Long> avids) {
        if (CollectionUtils.isEmpty(avids)) {
            return Collections.emptyList();
        }

        QueryRiskVideoResp queryRiskVideoResp = riskVideoServiceBlockingStub
                .withDeadlineAfter(5000, TimeUnit.MILLISECONDS)
                .queryRiskVideo(QueryRiskVideoReq.newBuilder().addAllAvids(avids).build());
        List<SingleQueryRiskVideoResp> riskVideoResps = queryRiskVideoResp.getListList();
        List<ArchiveRiskVideoBo> riskVideoBos = IArchiveConvertor.MAPPER.riskGrpcs2Bos(riskVideoResps);
        return riskVideoBos;
    }
}
