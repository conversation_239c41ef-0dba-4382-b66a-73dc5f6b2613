package com.bilibili.risk.service.rule;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.risk.dao.risk.LauMaterialAuditRuleRoleRelDao;
import com.bilibili.risk.po.risk.LauMaterialAuditRuleRoleRelPo;
import com.bilibili.risk.po.risk.LauMaterialAuditRuleRoleRelPoExample;
import edu.emory.mathcs.backport.java.util.Collections;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class MaterialAuditRuleRoleRelService {

    private final LauMaterialAuditRuleRoleRelDao lauMaterialAuditRuleRoleRelDao;

    public List<LauMaterialAuditRuleRoleRelPo> queryAuditRuleRoleRelListByRuleId(Long ruleId) {

        if (!Utils.isPositive(ruleId)) {
            return Collections.emptyList();
        }

        LauMaterialAuditRuleRoleRelPoExample queueRelPoExample = new LauMaterialAuditRuleRoleRelPoExample();
        queueRelPoExample.createCriteria().andRuleIdEqualTo(ruleId);
        List<LauMaterialAuditRuleRoleRelPo> relPos = lauMaterialAuditRuleRoleRelDao.selectByExample(queueRelPoExample);
        return relPos;
    }

    public List<LauMaterialAuditRuleRoleRelPo> queryAuditRuleRoleRelListByRoleId(Integer roleId) {

        if (!Utils.isPositive(roleId)) {
            return Collections.emptyList();
        }

        LauMaterialAuditRuleRoleRelPoExample queueRelPoExample = new LauMaterialAuditRuleRoleRelPoExample();
        queueRelPoExample.createCriteria().andRoleIdEqualTo(roleId);
        List<LauMaterialAuditRuleRoleRelPo> relPos = lauMaterialAuditRuleRoleRelDao.selectByExample(queueRelPoExample);
        return relPos;
    }

    public List<LauMaterialAuditRuleRoleRelPo> queryAuditRuleRoleRelListByRuleIds(List<Long> ruleIds) {

        if (CollectionUtils.isEmpty(ruleIds)) {
            return Collections.emptyList();
        }

        LauMaterialAuditRuleRoleRelPoExample queueRelPoExample = new LauMaterialAuditRuleRoleRelPoExample();
        queueRelPoExample.createCriteria().andRuleIdIn(ruleIds);
        List<LauMaterialAuditRuleRoleRelPo> relPos = lauMaterialAuditRuleRoleRelDao.selectByExample(queueRelPoExample);
        return relPos;
    }

    public List<LauMaterialAuditRuleRoleRelPo> queryAuditRuleRoleRelListByRoleIds(List<Integer> roleIds) {

        if (CollectionUtils.isEmpty(roleIds)) {
            return Collections.emptyList();
        }

        LauMaterialAuditRuleRoleRelPoExample queueRelPoExample = new LauMaterialAuditRuleRoleRelPoExample();
        queueRelPoExample.createCriteria().andRoleIdIn(roleIds);
        List<LauMaterialAuditRuleRoleRelPo> relPos = lauMaterialAuditRuleRoleRelDao.selectByExample(queueRelPoExample);
        return relPos;
    }

    public void deleteRuleAndRoleRelsByRuleId(Long ruleId) {
        LauMaterialAuditRuleRoleRelPoExample deleteExample = new LauMaterialAuditRuleRoleRelPoExample();
        deleteExample.createCriteria().andRuleIdEqualTo(ruleId);
        int deleteCount = lauMaterialAuditRuleRoleRelDao.deleteByExample(deleteExample);
    }
}
