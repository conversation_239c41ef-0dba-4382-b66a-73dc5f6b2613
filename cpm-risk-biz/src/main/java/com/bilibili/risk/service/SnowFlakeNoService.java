package com.bilibili.risk.service;

import com.bapis.infra.service.sequence.BusinessZoneReq;
import com.bapis.infra.service.sequence.IDZoneReply;
import com.bapis.infra.service.sequence.SeqZoneGrpc;
import com.bilibili.risk.config.BizConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import pleiades.venus.starter.rpc.client.RPCClient;

@Slf4j
@Service
public class SnowFlakeNoService {

    public static final String SEQUENCE_RISK_MATERIAL_TASK_ID = "risk_material_task_id";
    @Autowired
    private BizConfig bizConfig;

    @RPCClient("main.common-arch.sequence")
    private SeqZoneGrpc.SeqZoneBlockingStub seqZoneBlockingStub;

    public String generateNo() {

        IDZoneReply idZoneReply1 = seqZoneBlockingStub.snowFlake(BusinessZoneReq.newBuilder()
                .setBizTag(SEQUENCE_RISK_MATERIAL_TASK_ID)
                .setToken(bizConfig.getSnowFlakeNoToken())
                .build());
        return idZoneReply1.getId() + "";
    }

}
