package com.bilibili.risk.service.task;

import com.bilibili.risk.databus.MaterialAuditTaskEsForDataGroupPub;
import com.bilibili.risk.databus.MaterialAuditTaskEsPub;
import com.bilibili.risk.po.es.LauMaterialAuditTaskEsPo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class MaterialTaskEsPubService {

//    private final MaterialAuditTaskEsForDataGroupPub materialAuditTaskEsForDataGroupPub;
    private final MaterialAuditTaskEsPub materialAuditTaskEsPub;

    public void pub(LauMaterialAuditTaskEsPo esPo) {
        materialAuditTaskEsPub.pub(esPo);
//        materialAuditTaskEsForDataGroupPub.pub(esPo);
    }
}
