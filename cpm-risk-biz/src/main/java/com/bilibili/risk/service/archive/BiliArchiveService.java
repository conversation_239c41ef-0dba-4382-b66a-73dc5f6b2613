package com.bilibili.risk.service.archive;

import com.bapis.archive.service.Arc;
import com.bapis.archive.service.ArchiveGrpc;
import com.bapis.archive.service.ArcsReply;
import com.bapis.archive.service.ArcsRequest;
import com.bilibili.bvid.BVIDUtils;
import com.bilibili.risk.bo.BiliArchiveBo;
import com.bilibili.risk.convertor.BiliArchiveMapper;
import com.bilibili.risk.utils.NumberUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import pleiades.venus.starter.rpc.client.RPCClient;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BiliArchiveService {

    @RPCClient("archive.service")
    private ArchiveGrpc.ArchiveBlockingStub archiveBlockingStub;

    public Map<Long, BiliArchiveBo> fetchMap(Collection<Long> x) {
        final List<Long> validAvids = x.stream()
                .filter(NumberUtils::isPositive)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(validAvids)) return Collections.emptyMap();

        final Map<Long, BiliArchiveBo> map = new HashMap<>();
        for (List<Long> avids : Lists.partition(validAvids, 50)) {
            ArcsReply reply = archiveBlockingStub.arcs(ArcsRequest.newBuilder()
                    .addAllAids(avids)
                    .build());
            for (Map.Entry<Long, Arc> entry : reply.getArcsMap().entrySet()) {
                BiliArchiveBo biliArchiveBo = BiliArchiveMapper.MAPPER.fromRo(entry.getValue());
                biliArchiveBo.setBvid(BVIDUtils.avToBv(biliArchiveBo.getAvid()));
                map.put(entry.getKey(), biliArchiveBo);
            }
        }
        return map;
    }

}
