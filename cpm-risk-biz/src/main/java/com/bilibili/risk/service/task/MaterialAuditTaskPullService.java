package com.bilibili.risk.service.task;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.risk.bo.*;
import com.bilibili.risk.constant.RiskConstants;
import com.bilibili.risk.enums.MaterialTaskPullStrategyEnum;
import com.bilibili.risk.enums.MetricsCodeEnum;
import com.bilibili.risk.service.metrics.MetricDataHolder;
import com.bilibili.risk.service.metrics.TaskMetricsReportService;
import com.bilibili.risk.service.rule.MaterialAuditRuleService;
import com.bilibili.risk.service.task.pull.IMaterialAuditTaskPullStrategy;
import com.bilibili.risk.service.task.pull.MaterialAuditTaskPullStrategyFactory;
import com.dianping.cat.Cat;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
@RequiredArgsConstructor
public class MaterialAuditTaskPullService {

    private final MaterialAuditTaskPullStrategyFactory materialAuditTaskPullStrategyFactory;
    private final MaterialAuditTaskEsService materialAuditTaskEsService;
    private final MaterialAuditRuleService materialAuditRuleService;
    private final TaskMetricsReportService taskMetricsReportService;
    private final RedissonClient adpRedissonClient;

    @SneakyThrows
    public MaterialAuditTaskPullBo pullOneBatchMaterialAuditTasks(MaterialAuditTaskPullQueryBo pullQueryBo) {
        log.info("pullOneBatchMaterialAuditTasks begin, pullQueryBo={}", pullQueryBo);

        MetricDataHolder data = new MetricDataHolder();
        data.setDomainType(MetricsCodeEnum.DomainType.CREATIVE_MATERIAL_TASK.name());
        data.setType(MetricsCodeEnum.Type.BIZ_INDEX.name());
        data.setUsername(pullQueryBo.getUsername());

        MetricsCodeEnum.SubCode subCode = MetricsCodeEnum.SubCode.BIZ_MATERIAL_TASK_PRE_PULL;
        Long startTime = System.currentTimeMillis();
        RLock lock = adpRedissonClient.getLock(RiskConstants.LOCK_TASK_PULL + pullQueryBo.getUsername() + ":" + pullQueryBo.getQueueId() + ":" + pullQueryBo.getRuleId());
        try {
            if (lock.tryLock(1, 10, TimeUnit.SECONDS)) {
                Integer pullStrategy = MaterialTaskPullStrategyEnum.BY_SINGLE_QUEUE_ID.getKey();
                if (Utils.isPositive(pullQueryBo.getRuleId())) {
                    MaterialAuditRuleInfoBo ruleInfoBo = materialAuditRuleService.fetchMaterialAuditRule(pullQueryBo.getRuleId());
                    Assert.notNull(ruleInfoBo, "rule不存在");
                    pullStrategy = ruleInfoBo.getPullType();
                }
                pullQueryBo.setPullStrategy(pullStrategy);

                IMaterialAuditTaskPullStrategy strategy = materialAuditTaskPullStrategyFactory.getStrategy(pullQueryBo);
                List<MaterialAuditTaskEsBo> auditTaskEsBos = strategy.pullOneBatchMaterialAuditTasks(pullQueryBo);
                Cat.logEvent("pullOneBatchMaterialAuditTasks", "拉取到es数据");
                TaskListContext taskListContext = materialAuditTaskEsService.spliceOtherFields(auditTaskEsBos);

                MaterialAuditTaskPullBo materialAuditTaskPullBo = buildPullBo(taskListContext, auditTaskEsBos);

                Long endTime = System.currentTimeMillis();
                subCode = pullQueryBo.isPrePull() ? MetricsCodeEnum.SubCode.BIZ_MATERIAL_TASK_PRE_PULL : MetricsCodeEnum.SubCode.BIZ_MATERIAL_TASK_PULL;

                data.setCode(subCode.getCode());
                data.setMsg(subCode.getDesc());
                data.setMaterialTypeName(materialAuditTaskPullBo.getMaterialType() + "");
                data.setSize(String.valueOf(auditTaskEsBos.size()));
                data.setPullStrategy(pullStrategy + "");
                data.setSuccess(MetricsCodeEnum.SubCode.SUCCESS.getDesc());
                taskMetricsReportService.addBizMetricCountAndCost(endTime - startTime, data);
                return materialAuditTaskPullBo;
            } else {
                log.warn("pullOneBatchMaterialAuditTasks, 当前用户正在拉取数据，请稍后再试, pullQueryBo={}", pullQueryBo);
                return MaterialAuditTaskPullBo.empty();
            }
        } catch (Exception e) {
            Long endTime = System.currentTimeMillis();
            data.setCode(subCode.getCode());
            data.setMsg(subCode.getDesc());
            data.setSuccess(MetricsCodeEnum.SubCode.FAIL.getDesc());
            log.error("pullOneBatchMaterialAuditTasks error, pullQueryBo={}", pullQueryBo, e);
            taskMetricsReportService.addBizMetricCountAndCost(endTime - startTime, data);
            throw e;
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }


    private static MaterialAuditTaskPullBo buildPullBo(TaskListContext taskListContext, List<MaterialAuditTaskEsBo> auditTaskEsBos) {
        Long queueId = 0L;
        Integer materialType = 0;
        Integer bizType = 0;
        Integer accountId = 0;
        if (!CollectionUtils.isEmpty(auditTaskEsBos)) {
            MaterialAuditTaskEsBo taskEsBo = auditTaskEsBos.get(0);
            queueId = taskEsBo.getQueueId();
            materialType = taskEsBo.getMaterialType();
            bizType = taskEsBo.getBizType();
            accountId = taskEsBo.getAccountId();
        }

        MaterialAuditTaskPullBo taskPullBo = MaterialAuditTaskPullBo.builder()
                .queueId(queueId)
                .materialType(materialType)
                .bizType(bizType)
                .accountId(accountId)
                .list(auditTaskEsBos)
                .size(auditTaskEsBos.size())
                .build();

        if (taskListContext != null) {
            taskPullBo.setTaskTimeout(taskListContext.getTaskTimeout());
            taskPullBo.setReceiveBatchNo(taskListContext.getReceiveBatchNo());
            taskPullBo.setAcceptTime(taskListContext.getAcceptTime());
        }
        return taskPullBo;
    }

}
