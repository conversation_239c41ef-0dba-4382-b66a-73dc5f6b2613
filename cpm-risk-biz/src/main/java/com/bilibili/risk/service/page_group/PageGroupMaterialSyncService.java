package com.bilibili.risk.service.page_group;

import com.bilibili.risk.bo.CreativeDetailInfoBo;
import com.bilibili.risk.bo.CreativeDetailMaterialBo;
import com.bilibili.risk.bo.LandingPageGroupBo;
import com.bilibili.risk.bo.LauMaterialAuditBo;
import com.bilibili.risk.bo.msg.OtherSourceFirePushAuditMsgBo;
import com.bilibili.risk.constant.RiskConstants;
import com.bilibili.risk.convertor.IMaterialAuditConvertor;
import com.bilibili.risk.enums.FirePushAuditSourceEnum;
import com.bilibili.risk.po.ad.LauCreativeLandingPageGroupPo;
import com.bilibili.risk.service.material.CreativeMaterialPushToAuditService;
import com.bilibili.risk.service.material.LauMaterialAuditService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 落地页组素材同步
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PageGroupMaterialSyncService {

    private final LauMaterialAuditService lauMaterialAuditService;
    private final PageGroupService pageGroupService;
    private final CreativeMaterialPushToAuditService creativeMaterialPushToAuditService;
    private final RedissonClient adpRedissonClient;

    /**
     * 查询落地页组关联的所有的创意，同步素材创意关系等
     * 消费时，一次只会处理一个
     * @param pageGroupId
     */
    public void relatedCreativeSplitMaterialsPushToAudit(Long pageGroupId) throws Exception {
        log.info("relatedCreativeSplitMaterialsPushToAudit pageGroupIds: {}", pageGroupId);
        // 构建落地页组的素材
        if(null == pageGroupId){
            return;
        }

        // 2.同步到素材表，并更新审核状态
        RLock lock = adpRedissonClient.getLock(RiskConstants.PAGE_GROUP_STATUS_SYNC + pageGroupId);
        try {
            if(lock.tryLock(3,5, TimeUnit.SECONDS)){

                CreativeDetailInfoBo creativeDetailInfoBo = new CreativeDetailInfoBo();
                creativeDetailInfoBo.setPageGroupId(pageGroupId);
                // 查询落地页组的落地页并填充
                creativeMaterialPushToAuditService.queryAndFillThirdPartyLandingPageGroup(creativeDetailInfoBo);
                creativeMaterialPushToAuditService.processAllMaterialsContent(creativeDetailInfoBo);
                // 将素材按素材类型拆分并去重
                List<CreativeDetailMaterialBo> creativeDetailMaterialBos = creativeMaterialPushToAuditService.splitAndMergeMaterials(creativeDetailInfoBo);
                // 2.同步到素材表
                List<LauMaterialAuditBo> materialBos = IMaterialAuditConvertor.INSTANCE.creativeDetailInfoOfMaterialBo2MaterialAuditBos(creativeDetailMaterialBos);
                lauMaterialAuditService.genMd5AndSaveMaterials(materialBos);

            } else {
                throw new RuntimeException("落地页组审核状态变更，修改状态，获取锁失败");
            }
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }


        List<LauCreativeLandingPageGroupPo> creativeLandingPageGroupPos;
        Integer size = 0, creativeId = null;
        do {
            // 1、这里取的是 外部数据源的关联关系，理论上不会漏掉外部数据源删除的关联关系
            creativeLandingPageGroupPos = pageGroupService.scrollQuery(pageGroupId, creativeId, 100);

            Set<Integer> creativeIds = creativeLandingPageGroupPos.stream().map(LauCreativeLandingPageGroupPo::getCreativeId).collect(Collectors.toSet());

            if (CollectionUtils.isEmpty(creativeIds)) {
                break;
            }

            OtherSourceFirePushAuditMsgBo msgBo = OtherSourceFirePushAuditMsgBo.builder().source(FirePushAuditSourceEnum.PAGE_GROUP.getCode())
                    .pageGroupId(pageGroupId).build();

            creativeMaterialPushToAuditService.firePushAuditIfNeed(creativeIds, msgBo);

            creativeId = creativeLandingPageGroupPos.get(creativeLandingPageGroupPos.size() - 1).getCreativeId();
            size += creativeLandingPageGroupPos.size();

            log.info("relatedCreativeSplitMaterialsPushToAudit pageGroupIds: {}, size: {}", pageGroupId, size);
        } while (creativeLandingPageGroupPos.size() == 100);

        log.info("relatedCreativeSplitMaterialsPushToAudit pageGroupIds: {}, end", pageGroupId);
    }
}
