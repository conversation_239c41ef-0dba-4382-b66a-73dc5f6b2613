package com.bilibili.risk.service.refresh;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.risk.bo.LauMaterialAuditBo;
import com.bilibili.risk.bo.MaterialAuditTaskCommonQueryBo;
import com.bilibili.risk.bo.MaterialTaskAuditBo;
import com.bilibili.risk.bo.TaskLabelBo;
import com.bilibili.risk.bo.msg.MaterialAuditMsgBo;
import com.bilibili.risk.constant.RiskConstants;
import com.bilibili.risk.enums.AuditLabelIdSpecialEnum;
import com.bilibili.risk.enums.MaterialTaskStatusEnum;
import com.bilibili.risk.enums.RiskMaterialTypeEnum;
import com.bilibili.risk.enums.ShardingTableEnum;
import com.bilibili.risk.po.es.LauMaterialAuditTaskEsPo;
import com.bilibili.risk.po.risk.LauMaterialAuditPo;
import com.bilibili.risk.po.risk.LauMaterialAuditTaskPo;
import com.bilibili.risk.po.risk.LauMaterialCreativeRelPo;
import com.bilibili.risk.service.material.LauMaterialAuditService;
import com.bilibili.risk.service.material.MaterialAuditToJudgeAllCreativesService;
import com.bilibili.risk.service.task.MaterialAuditTaskAuditService;
import com.bilibili.risk.service.task.MaterialAuditTaskDbService;
import com.bilibili.risk.service.task.MaterialAuditTaskEsService;
import com.bilibili.risk.utils.JacksonUtils;
import com.bilibili.risk.utils.MaterialTaskUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class MaterialAuditDataRefreshService {

    public static final int PAGE_SIZE = 200;
    private final MaterialAuditTaskEsService materialAuditTaskEsService;
    private final MaterialAuditTaskDbService materialAuditTaskDbService;
    private final LauMaterialAuditService lauMaterialAuditService;
    private final MaterialAuditToJudgeAllCreativesService materialAuditToJudgeAllCreativesService;

    @Autowired
    private MaterialAuditTaskAuditService materialAuditTaskAuditService;

    public void asyncRefreshMaterialThirdLabel(List<String> taskIds) {
        new Thread(() -> {
            try {
                refreshMaterialThirdLabel(taskIds);
            } catch (Exception e) {
                log.error("asyncRefreshMaterialThirdLabel[异常]，taskIds.size:{}, error:{}", taskIds.size(), e);
            }
        }).start();
    }

    /**
     * 刷数据，将待审素材的三级标签用任务的进行刷
     */
    public void refreshMaterialThirdLabel(List<String> taskIds) {
        log.info("refreshMaterialThirdLabel[开始]，taskIds.size:{}", taskIds.size());
        // 分批查询 es 数据
        PageResult<LauMaterialAuditTaskEsPo> pageResult = null;
        int page = 1;
        int totalUpdateCount = 0;
        int totalTaskUpdateCount = 0;
        do {
            MaterialAuditTaskCommonQueryBo queryBo = MaterialAuditTaskCommonQueryBo.builder()
                    .page(page)
                    .pageSize(PAGE_SIZE)
                    .ids(taskIds)
                    .build();
            pageResult = materialAuditTaskEsService.queryMaterialAuditTaskEsPosCommon(queryBo);
            List<LauMaterialAuditTaskEsPo> records = pageResult.getRecords();
            if (CollectionUtils.isEmpty(records)) {
                log.info("refreshMaterialThirdLabel[没有查询到数据]，taskIds.size:{}, page:{}, pageSize:{}", taskIds.size(), page, PAGE_SIZE);
                break;
            }
            log.info("refreshMaterialThirdLabel[查询到数据]，taskIds.size:{}, page:{}, pageSize:{}, records.size:{}", taskIds.size(), page, PAGE_SIZE, records.size());
            List<String> taskIdsOneBatch = records.stream().map(t -> t.getId()).collect(Collectors.toList());
            // 获取这批任务的db数据
            List<LauMaterialAuditTaskPo> auditTaskPosOneBatch = materialAuditTaskDbService.queryListByTaskIds(taskIdsOneBatch);
            List<String> materialIdsOneBatch = auditTaskPosOneBatch.stream().map(t -> t.getMaterialId()).distinct().collect(Collectors.toList());

            // 获取这批任务的素材db数据
            Map<String, LauMaterialAuditBo> materialAuditBoMap = lauMaterialAuditService.queryMapByMaterialIds(materialIdsOneBatch);

            // 任务有三级标签，但是素材没有的，则素材刷成语任务一致
            for (LauMaterialAuditTaskPo auditTaskPo : auditTaskPosOneBatch) {
                if (!Objects.equals(auditTaskPo.getStatus(), MaterialTaskStatusEnum.COMPLETE.getCode())) {
                    continue;
                }
                LauMaterialAuditBo materialAuditBo = materialAuditBoMap.get(auditTaskPo.getMaterialId());
                if (materialAuditBo == null) {
                    log.warn("refreshMaterialThirdLabel[素材不存在]，taskId:{}, materialId:{}", auditTaskPo.getTaskId(), auditTaskPo.getMaterialId());
                    continue;
                }
                if (StringUtils.isEmpty(auditTaskPo.getAuditLabelThirdId())) {
                    totalTaskUpdateCount++;
                    LauMaterialAuditTaskPo taskPo = LauMaterialAuditTaskPo.builder()
                            .taskId(auditTaskPo.getTaskId())
                            .auditLabelThirdId(AuditLabelIdSpecialEnum.DEFAULT_PASS.getCode())
                            .build();
                    int count = materialAuditTaskDbService.updateTask(taskPo);
                    log.warn("refreshMaterialThirdLabel[任务完成但是没有三级标签]，taskId:{}, materialId:{},count={}", auditTaskPo.getTaskId(), auditTaskPo.getMaterialId(), count);
                    continue;
                }
                if (!Objects.equals(auditTaskPo.getAuditLabelThirdId(), materialAuditBo.getAuditLabelThirdId())) {
                    LauMaterialAuditPo lauMaterialAuditPo = LauMaterialAuditPo.builder().materialId(materialAuditBo.getMaterialId()).auditLabelThirdId(auditTaskPo.getAuditLabelThirdId()).build();
                    Integer count = lauMaterialAuditService.updateMaterialAuditPo(lauMaterialAuditPo);
                    log.info("refreshMaterialThirdLabel[素材和任务三级标签不一致]，taskId:{}, materialId:{}, count:{},taskLabel={}", auditTaskPo.getTaskId(), auditTaskPo.getMaterialId(), count, auditTaskPo.getAuditLabelThirdId());
                    totalUpdateCount++;
                }
            }

            page++;
        } while (!CollectionUtils.isEmpty(pageResult.getRecords()) && pageResult.getRecords().size() == PAGE_SIZE);
        log.info("refreshMaterialThirdLabel[完成]，page:{}, totalUpdateCount[素材]:{},totalTaskUpdateCount[任务]:{}", page, totalUpdateCount, totalTaskUpdateCount);
    }

    public void refreshByTaskId(String json){
        log.info("refreshByMaterialId[开始]，taskIds.{}", json);

        List<String> taskIds = JSON.parseArray(json, String.class);
        MaterialAuditTaskCommonQueryBo queryBo = MaterialAuditTaskCommonQueryBo.builder()
                .page(1)
                .pageSize(200)
                .ids(taskIds)
                .build();
        PageResult<LauMaterialAuditTaskEsPo> pageResult = materialAuditTaskEsService.queryMaterialAuditTaskEsPosCommon(queryBo);

        if(CollectionUtils.isEmpty(pageResult.getRecords())){
            log.info("refreshByMaterialId[没有查询到数据]，taskIds.size:{}, pageSize:{}", taskIds.size(), 200);
            return;
        }

        for (LauMaterialAuditTaskEsPo record : pageResult.getRecords()) {
            MaterialAuditMsgBo materialAuditMsgBo = MaterialAuditMsgBo.builder()
                    .materialId(record.getMaterialId())
                    .executeName(record.getExecuteName())

                    .creativeId(record.getCreativeId())
                    .triggerTaskId(record.getId())
                    .build();
            log.info("sendDatabusMsg[异步判定素材的创意的情况] materialAuditMsgBo={}", materialAuditMsgBo);

            MaterialTaskAuditBo bo = MaterialTaskAuditBo.builder()
                    .taskLabelBos(Lists.newArrayList(TaskLabelBo.builder().taskId(record.getId())
                                    .auditLabelThirdIds(record.getAuditLabelThirdIds())
                            .reason(record.getReason()).build()))
                    .executeName(record.getExecuteName())
                    .isFromWorkBench(1)
                    .build();
            materialAuditTaskAuditService.batchAuditTask(bo);
        }
    }

    public void refreshAuditContentFromEs(){

        int size = 100, total = 0, linkUnknownTotal = 0;
        for(int i=0; i < 32; i ++){
            Long lastId = null;
            List<LauMaterialAuditBo> lauMaterialAuditBoList;
            do{
                lauMaterialAuditBoList = lauMaterialAuditService.scrollByMaterialIdWithShadingNo(lastId, size, i);
                if(!CollectionUtils.isEmpty(lauMaterialAuditBoList)){

                    for (LauMaterialAuditBo lauMaterialAuditBo : lauMaterialAuditBoList) {
                        if(StringUtils.isNotBlank(lauMaterialAuditBo.getMaterialContent()) && StringUtils.isNotBlank(lauMaterialAuditBo.getRawContent())){
                            log.info("refreshAuditContentFromEs[素材内容和原始内容都不为空]，materialId:{}, materialContent:{}, rawContent:{}", lauMaterialAuditBo.getMaterialId(), lauMaterialAuditBo.getMaterialContent(), lauMaterialAuditBo.getRawContent());
                            continue;
                        }

                        MaterialAuditTaskCommonQueryBo queryBo = MaterialAuditTaskCommonQueryBo.builder()
                                .page(1)
                                .pageSize(1)
                                .materialIds(Lists.newArrayList(lauMaterialAuditBo.getMaterialId()))
                                .build();
                        PageResult<LauMaterialAuditTaskEsPo> pageResult = materialAuditTaskEsService.queryMaterialAuditTaskEsPosCommon(queryBo);

                        if(CollectionUtils.isEmpty(pageResult.getRecords())){
                            log.info("refreshAuditContentFromEs[没有查询到数据]，materialId:{}, pageSize:{}", lauMaterialAuditBo.getMaterialId(), 1);
                            continue;
                        }

                        LauMaterialAuditTaskEsPo lauMaterialAuditTaskEsPo = pageResult.getRecords().get(0);
                        if(StringUtils.isBlank(lauMaterialAuditTaskEsPo.getMaterialContent())){
                            log.info("refreshAuditContentFromEs[素材内容和原始内容都为空]，materialId:{}, materialContent:{}", lauMaterialAuditBo.getMaterialId(), lauMaterialAuditTaskEsPo.getMaterialContent());
                            continue;
                        }

                        total ++;
                        LauMaterialAuditBo upd = new LauMaterialAuditBo();
                        upd.setMaterialId(lauMaterialAuditBo.getMaterialId());
                        upd.setMaterialContent(lauMaterialAuditTaskEsPo.getMaterialContent());

                        RiskMaterialTypeEnum materialTypeEnum = RiskMaterialTypeEnum.getByCode(lauMaterialAuditBo.getMaterialType());
                        if(RiskMaterialTypeEnum.PAGE_GROUP_PAGE == materialTypeEnum){
                            String a = lauMaterialAuditTaskEsPo.getMaterialContent();
                            upd.setRawContent(a.substring(a.indexOf("-") + 1));
                        } else if(RiskMaterialTypeEnum.UNKNOWN != materialTypeEnum && RiskMaterialTypeEnum.LINK != materialTypeEnum){
                            upd.setRawContent(materialTypeEnum.parse(lauMaterialAuditTaskEsPo.getMaterialContent()));
                        } else {

                            linkUnknownTotal ++;
                            log.info("refreshAuditContentFromEs[素材类型不匹配]，materialId:{}, materialType:{}, materialContent:{}", lauMaterialAuditBo.getMaterialId(), lauMaterialAuditBo.getMaterialType(), lauMaterialAuditTaskEsPo.getMaterialContent());
                        }

                        lauMaterialAuditService.updateSelectiveByMaterialId(upd);
                        log.info("refreshAuditContentFromEs[素材内容和原始内容都为空]，materialId:{}, materialContent:{}, rawContent:{}", lauMaterialAuditBo.getMaterialId(), upd.getMaterialContent(), upd.getRawContent());
                    }

                    lastId = lauMaterialAuditBoList.get(lauMaterialAuditBoList.size() - 1).getId();
                }

            } while(!CollectionUtils.isEmpty(lauMaterialAuditBoList) && lauMaterialAuditBoList.size() == size);

            log.info("refreshAuditContentFromEs[完成]，i:{}, total:{}, linkUnknownTotal:{}", i, total, linkUnknownTotal);
        }

    }


    public void refreshAuditContentFromEsByMaterialId(String json){
        List<String> materialIds = JSON.parseArray(json, String.class);

        int total = 0, linkUnknownTotal = 0;
        for (String materialId : materialIds) {
            MaterialAuditTaskCommonQueryBo queryBo = MaterialAuditTaskCommonQueryBo.builder()
                    .page(1)
                    .pageSize(1)
                    .materialIds(Lists.newArrayList(materialId))
                    .build();
            PageResult<LauMaterialAuditTaskEsPo> pageResult = materialAuditTaskEsService.queryMaterialAuditTaskEsPosCommon(queryBo);

            if(CollectionUtils.isEmpty(pageResult.getRecords())){
                log.info("refreshAuditContentFromEs[没有查询到数据]，materialId:{}, pageSize:{}", materialId, 1);
                continue;
            }

            LauMaterialAuditTaskEsPo lauMaterialAuditTaskEsPo = pageResult.getRecords().get(0);
            if(StringUtils.isBlank(lauMaterialAuditTaskEsPo.getMaterialContent())){
                log.info("refreshAuditContentFromEs[素材内容和原始内容都为空]，materialId:{}, materialContent:{}", materialId, lauMaterialAuditTaskEsPo.getMaterialContent());
                continue;
            }

            total ++;
            LauMaterialAuditBo upd = new LauMaterialAuditBo();
            upd.setMaterialId(materialId);
            upd.setMaterialContent(lauMaterialAuditTaskEsPo.getMaterialContent());

            RiskMaterialTypeEnum materialTypeEnum = RiskMaterialTypeEnum.getByCode(lauMaterialAuditTaskEsPo.getMaterialType());
            if(RiskMaterialTypeEnum.PAGE_GROUP_PAGE == materialTypeEnum){
                String a = lauMaterialAuditTaskEsPo.getMaterialContent();
                upd.setRawContent(a.substring(a.indexOf("-") + 1));
            } else if(RiskMaterialTypeEnum.UNKNOWN != materialTypeEnum && RiskMaterialTypeEnum.LINK != materialTypeEnum){
                upd.setRawContent(materialTypeEnum.parse(lauMaterialAuditTaskEsPo.getMaterialContent()));
            } else {

                linkUnknownTotal ++;
                log.info("refreshAuditContentFromEs[素材类型不匹配]，materialId:{}, materialType:{}, materialContent:{}", materialId, lauMaterialAuditTaskEsPo.getMaterialType(), lauMaterialAuditTaskEsPo.getMaterialContent());
            }

            lauMaterialAuditService.updateSelectiveByMaterialId(upd);
            log.info("refreshAuditContentFromEs[素材内容和原始内容都为空]，materialId:{}, materialContent:{}, rawContent:{}", materialId, upd.getMaterialContent(), upd.getRawContent());
        }
        log.info("refreshAuditContentFromEs[完成] total:{}, linkUnknownTotal:{}",  total, linkUnknownTotal);
    }

    public void queryLabelAndReason(String json) throws Exception {
        List<String> materialIds = JSON.parseArray(json, String.class);

        Map<Integer, List<String>> partitionMap = materialIds.stream().collect(
                Collectors.groupingBy(e -> MaterialTaskUtils.calculateShardingKeyByMaterialId(e, ShardingTableEnum.TABLE_LAU_MATERIAL_AUDIT.getShardingAlgorithm())));

        Map<String, LauMaterialAuditBo> map = new HashMap<>();
        for (Map.Entry<Integer, List<String>> entry : partitionMap.entrySet()) {
            Map<String, LauMaterialAuditBo> result = lauMaterialAuditService.queryMapByMaterialIds(entry.getValue());
            if(MapUtils.isNotEmpty(result)){
                map.putAll(result);
            }
        }

        int total = 0, updTotal = 0, allErrorTotal = 0, onlyOne = 0;
        List<String> ids = Lists.newArrayList(), onlyOneIds = Lists.newArrayList();
        for (String materialId : materialIds) {
            if(null == map.get(materialId) || StringUtils.isNotBlank(map.get(materialId).getAuditLabelThirdId())){
                continue;
            }

            MaterialAuditTaskCommonQueryBo queryBo = MaterialAuditTaskCommonQueryBo.builder()
                    .page(1)
                    .pageSize(1)
                    .materialIds(Lists.newArrayList(materialId))
                    .isDeleted(Lists.newArrayList(IsDeleted.VALID.getCode()))
                    .fieldExists(Sets.newHashSet("auditLabelThirdIds"))
                    .build();

            PageResult<LauMaterialAuditTaskEsPo> pageResult = materialAuditTaskEsService.queryMaterialAuditTaskEsPosCommon(queryBo);
            total ++;
            if(!CollectionUtils.isEmpty(pageResult.getRecords())){
                updTotal++;
                LauMaterialAuditTaskEsPo esPo = pageResult.getRecords().get(0);
                log.info("refreshLabelAndReason[查询结果] materialId:{}, label:{}, reason:{}", materialId
                        , JacksonUtils.toJson(esPo.getAuditLabelThirdIds()), esPo.getReason());

                MaterialAuditTaskCommonQueryBo query = MaterialAuditTaskCommonQueryBo.builder()
                        .page(1)
                        .pageSize(1)
                        .materialIds(Lists.newArrayList(materialId))
                        .excludeTaskIds(Lists.newArrayList(esPo.getId()))
                        .isDeleted(Lists.newArrayList(IsDeleted.VALID.getCode()))
                        .fieldExists(Sets.newHashSet("auditLabelThirdIds"))
                        .build();

                PageResult<LauMaterialAuditTaskEsPo> result = materialAuditTaskEsService.queryMaterialAuditTaskEsPosCommon(query);
                if(CollectionUtils.isEmpty(result.getRecords())){
                    allErrorTotal++;
                    ids.add(materialId);
                }

            } else {
                log.info("not exists:{}", materialId);
            }

            if(pageResult.getTotal() > 1){
                onlyOne++;
                onlyOneIds.add(materialId);
            }
            if(total % 10 == 9){
                Thread.sleep(100);
            }
        }

        log.info("refreshLabelAndReason[完成] total:{}, updTotal:{}， allErrorTotal:{}, ids:{}, onlyOne:{}, onlyOneIds:{}",
                total, updTotal, allErrorTotal, JacksonUtils.toJson(ids), onlyOne, JacksonUtils.toJson(onlyOneIds));
    }

    public void refreshLabelAndReason(String json){
        List<String> materialIds = JSON.parseArray(json, String.class);

        Map<Integer, List<String>> partitionMap = materialIds.stream().collect(
                Collectors.groupingBy(e -> MaterialTaskUtils.calculateShardingKeyByMaterialId(e, ShardingTableEnum.TABLE_LAU_MATERIAL_AUDIT.getShardingAlgorithm())));

        Map<String, LauMaterialAuditBo> map = new HashMap<>();
        for (Map.Entry<Integer, List<String>> entry : partitionMap.entrySet()) {
            Map<String, LauMaterialAuditBo> result = lauMaterialAuditService.queryMapByMaterialIds(entry.getValue());
            if(MapUtils.isNotEmpty(result)){
                map.putAll(result);
            }
        }

        int total = 0, updTotal = 0, allErrorTotal = 0, onlyOne = 0;
        List<String> ids = Lists.newArrayList(), onlyOneIds = Lists.newArrayList();
        for (String materialId : materialIds) {
            if(null == map.get(materialId) || StringUtils.isNotBlank(map.get(materialId).getAuditLabelThirdId())){
                continue;
            }

            MaterialAuditTaskCommonQueryBo queryBo = MaterialAuditTaskCommonQueryBo.builder()
                    .page(1)
                    .pageSize(1)
                    .materialIds(Lists.newArrayList(materialId))
                    .isDeleted(Lists.newArrayList(IsDeleted.VALID.getCode()))
                    .fieldExists(Sets.newHashSet("auditLabelThirdIds"))
                    .build();

            PageResult<LauMaterialAuditTaskEsPo> pageResult = materialAuditTaskEsService.queryMaterialAuditTaskEsPosCommon(queryBo);

            if(!CollectionUtils.isEmpty(pageResult.getRecords())){
                updTotal++;
                LauMaterialAuditTaskEsPo esPo = pageResult.getRecords().get(0);
                log.info("refreshLabelAndReason[查询结果] materialId:{}, label:{}, reason:{}", materialId
                        , JacksonUtils.toJson(esPo.getAuditLabelThirdIds()), esPo.getReason());

                if(pageResult.getTotal() == 1){
                    allErrorTotal++;
                    ids.add(materialId);


                } else {
                    log.info("not exists:{}", materialId);
                }

                LauMaterialAuditBo materialAuditBo = LauMaterialAuditBo.builder().materialId(materialId)
                        .auditLabelThirdId(String.join(",", esPo.getAuditLabelThirdIds().stream()
                                .map(e -> e.toString()).collect(Collectors.toList())))
                        .reason(esPo.getReason())
                        .build();
                lauMaterialAuditService.updateSelectiveByMaterialId(materialAuditBo);
            }
        }

        log.info("refreshLabelAndReason[完成] total:{}, updTotal:{}， allErrorTotal:{}, ids:{}, onlyOne:{}, onlyOneIds:{}",
                total, updTotal, allErrorTotal, JacksonUtils.toJson(ids), onlyOne, JacksonUtils.toJson(onlyOneIds));
    }

    public void refreshTask(String json){
        List<String> materialIds = JSON.parseArray(json, String.class);
        int total = 0;
        for (String materialId : materialIds) {

            MaterialAuditTaskCommonQueryBo queryBo1 = MaterialAuditTaskCommonQueryBo.builder()
                    .page(1)
                    .pageSize(1)
                    .materialIds(Lists.newArrayList(materialId))
                    .excludeExecuteNames(Lists.newArrayList(RiskConstants.SYSTEM_USERNAME))
                    .isDeleted(Lists.newArrayList(IsDeleted.VALID.getCode()))
                    .fieldExists(Sets.newHashSet("auditLabelThirdIds"))
                    .build();
            PageResult<LauMaterialAuditTaskEsPo> trigger = materialAuditTaskEsService.queryMaterialAuditTaskEsPosCommon(queryBo1);

            MaterialAuditTaskCommonQueryBo queryBo = MaterialAuditTaskCommonQueryBo.builder()
                    .page(1)
                    .pageSize(1000)
                    .materialIds(Lists.newArrayList(materialId))
                    .isDeleted(Lists.newArrayList(IsDeleted.VALID.getCode()))
                    .fieldNotExists(Sets.newHashSet("auditLabelThirdIds"))
                    .build();

            PageResult<LauMaterialAuditTaskEsPo> pageResult = materialAuditTaskEsService.queryMaterialAuditTaskEsPosCommon(queryBo);

            if(CollectionUtils.isEmpty(pageResult.getRecords())){
                log.info("refreshTask[没有查询到数据]，materialId:{}, pageSize:{}", materialId, 1000);
                continue;
            }

            if(1000 == pageResult.getRecords().size()){
                log.info("refreshTask[查询到数据刚好 1000]，materialId:{}", materialId);
            }

            for (LauMaterialAuditTaskEsPo record : pageResult.getRecords()) {
                MaterialAuditMsgBo materialAuditMsgBo = MaterialAuditMsgBo.builder()
                        .triggerTaskId(!CollectionUtils.isEmpty(trigger.getRecords()) ? trigger.getRecords().get(0).getId() : null).build();
                total++;
                materialAuditToJudgeAllCreativesService.pub(
                        Lists.newArrayList(LauMaterialCreativeRelPo.builder().creativeId(record.getCreativeId()).materialId(record.getMaterialId()).build())
                        , materialAuditMsgBo);
            }
        }
        log.info("refreshTask[完成] total:{}", total);
    }

}
