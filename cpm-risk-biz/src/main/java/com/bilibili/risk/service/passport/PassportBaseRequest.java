package com.bilibili.risk.service.passport;

/**
 * Created by <PERSON><PERSON><PERSON> on 2017/2/27.
 */
public class PassportBaseRequest {
    private String appkey;
    private String sign;
    private Integer ts;

    public PassportBaseRequest() {
    }

    public PassportBaseRequest(String appkey, String sign, Integer ts) {
        this.appkey = appkey;
        this.sign = sign;
        this.ts = ts;
    }

    public String getAppkey() {
        return appkey;
    }

    public void setAppkey(String appkey) {
        this.appkey = appkey;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public Integer getTs() {
        return ts;
    }

    public void setTs(Integer ts) {
        this.ts = ts;
    }

    @Override
    public String toString() {
        return "PassportBaseRequest{" +
                "appkey='" + appkey + '\'' +
                ", sign='" + sign + '\'' +
                ", ts=" + ts +
                '}';
    }
}
