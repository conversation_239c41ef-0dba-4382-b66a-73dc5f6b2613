package com.bilibili.risk.service.queue;

import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.util.Page;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.risk.bo.LauMaterialAuditQueueBo;
import com.bilibili.risk.bo.MaterialQueueQueryBo;
import com.bilibili.risk.config.BizConfig;
import com.bilibili.risk.constant.DatabaseConstant;
import com.bilibili.risk.convertor.IMaterialAuditQueueConvertor;
import com.bilibili.risk.dao.risk.LauMaterialAuditQueueDao;
import com.bilibili.risk.dao.risk.LauMaterialQueueSubscribeDao;
import com.bilibili.risk.enums.SubscribeTypeEnum;
import com.bilibili.risk.po.risk.LauMaterialAuditQueuePo;
import com.bilibili.risk.po.risk.LauMaterialAuditQueuePoExample;
import com.bilibili.risk.po.risk.LauMaterialQueueSubscribePo;
import com.bilibili.risk.po.risk.LauMaterialQueueSubscribePoExample;
import edu.emory.mathcs.backport.java.util.Collections;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class MaterialAuditQueueService {

    private final BizConfig bizConfig;
    private final LauMaterialAuditQueueDao lauMaterialAuditQueueDao;
    private final LauMaterialQueueSubscribeDao lauMaterialQueueSubscribeDao;

    /**
     * 所有队列列表
     *
     * @return
     */
    public List<LauMaterialAuditQueueBo> queryAllMaterialAuditQueue(Integer excludeSpecialQueues) {
        LauMaterialAuditQueuePoExample example = new LauMaterialAuditQueuePoExample();
        LauMaterialAuditQueuePoExample.Criteria criteria = example.createCriteria();
        example.setOrderByClause("id desc");

        // 是否排除特殊的队列，如等待队列，兜底队列
        if (Utils.isPositive(excludeSpecialQueues)) {
            criteria.andIdNotIn(bizConfig.getSpecialQueueIds());
        }
        List<LauMaterialAuditQueueBo> lauMaterialAuditQueueBos = IMaterialAuditQueueConvertor.INSTANCE.pos2bos(lauMaterialAuditQueueDao.selectByExample(example));
        return lauMaterialAuditQueueBos;
    }

    public Map<Long, LauMaterialAuditQueueBo> queryMapByIds(List<Long> ids) {

        List<LauMaterialAuditQueueBo> auditQueueBos = this.queryListByIds(ids);
        return auditQueueBos.stream().collect(Collectors.toMap(LauMaterialAuditQueueBo::getId, bo -> bo));
    }

    public List<LauMaterialAuditQueueBo> queryListByIds(List<Long> ids) {

        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }

        LauMaterialAuditQueuePoExample example = new LauMaterialAuditQueuePoExample();
        LauMaterialAuditQueuePoExample.Criteria criteria = example.createCriteria();
        criteria.andIdIn(ids);
        List<LauMaterialAuditQueueBo> lauMaterialAuditQueueBos = IMaterialAuditQueueConvertor.INSTANCE.pos2bos(lauMaterialAuditQueueDao.selectByExample(example));
        return lauMaterialAuditQueueBos;
    }

    public LauMaterialAuditQueueBo fetchQueueBo(Long id) {

        if (!Utils.isPositive(id)) {
            return null;
        }

        LauMaterialAuditQueuePoExample example = new LauMaterialAuditQueuePoExample();
        LauMaterialAuditQueuePoExample.Criteria criteria = example.createCriteria();
        criteria.andIdEqualTo(id);
        List<LauMaterialAuditQueueBo> lauMaterialAuditQueueBos = IMaterialAuditQueueConvertor.INSTANCE.pos2bos(lauMaterialAuditQueueDao.selectByExample(example));
        if (CollectionUtils.isEmpty(lauMaterialAuditQueueBos)) {
            return null;
        }
        return lauMaterialAuditQueueBos.get(0);
    }

    /**
     * 订阅或取消订阅队列
     *
     * @param queueId
     * @param username
     * @return
     * @see com.bilibili.risk.enums.SubscribeTypeEnum
     */
    @Transactional(rollbackFor = Exception.class, transactionManager = DatabaseConstant.RISK_TRANSACTION_MANAGER)
    public Integer subscribeOrNotQueue(Long queueId, String username, Integer subscribeType) {
        log.info("subscribeQueue[订阅队列/取消]: queueId={}, username={}", queueId, username);
        Assert.notNull(queueId, "queueId不能为空");
        Assert.notNull(username, "username不能为空");
        Assert.notNull(subscribeType, "subscribeType不能为空");

        LauMaterialAuditQueuePo auditQueuePo = lauMaterialAuditQueueDao.selectByPrimaryKey(queueId);
        Assert.notNull(auditQueuePo, "队列不存在");

        // 判断是否已经订阅
        LauMaterialQueueSubscribePoExample subscribePoExample = new LauMaterialQueueSubscribePoExample();
        subscribePoExample.createCriteria().andQueueIdEqualTo(queueId).andUsernameEqualTo(username);
        List<LauMaterialQueueSubscribePo> subscribePos = lauMaterialQueueSubscribeDao.selectByExample(subscribePoExample);
        if (Objects.equals(SubscribeTypeEnum.SUBSCRIBE.getCode(), subscribeType) && !CollectionUtils.isEmpty(subscribePos)) {
            log.info("subscribeQueue[已经订阅过了], queueId={}, roleId={}", queueId, username);
            return 0;
        }

        Integer count = 0;
        if (Objects.equals(SubscribeTypeEnum.SUBSCRIBE.getCode(), subscribeType)) {
            LauMaterialQueueSubscribePo subscribePo = new LauMaterialQueueSubscribePo();
            subscribePo.setQueueId(queueId);
            subscribePo.setUsername(username);
            count = lauMaterialQueueSubscribeDao.insertSelective(subscribePo);
        } else {
            count = lauMaterialQueueSubscribeDao.deleteByExample(subscribePoExample);
        }
        return count;
    }

    /**
     * 订阅队列的列表
     *
     * @param page
     * @param pageSize
     * @param queryBo
     * @return
     */
    public PageResult<LauMaterialAuditQueueBo> querySubscribeList(Integer page, Integer pageSize, MaterialQueueQueryBo queryBo) {
        Assert.isTrue(!StringUtils.isEmpty(queryBo.getUsername()), "username不能为空");

        // 存在搜索条件，则搜索后用queueIds进一步搜索
        List<Long> queueIdsCondition = new ArrayList<>();
        if (StringUtils.isNotEmpty(queryBo.getName()) || CollectionUtils.isNotEmpty(queryBo.getIds()) || Utils.isPositive(queryBo.getBizType()) || Utils.isPositive(queryBo.getMaterialType())) {
            PageResult<LauMaterialAuditQueueBo> pageResult = this.queryAllQueueList(1, 1000, queryBo);
            queueIdsCondition = pageResult.getRecords().stream().map(LauMaterialAuditQueueBo::getId).collect(Collectors.toList());
        }

        // 查询订阅关系
        LauMaterialQueueSubscribePoExample example = new LauMaterialQueueSubscribePoExample();
        LauMaterialQueueSubscribePoExample.Criteria criteria = example.createCriteria();
        criteria.andUsernameEqualTo(queryBo.getUsername());
        example.setOrderByClause("ctime desc");

        Page pg = Page.valueOf(page, pageSize);
        example.setLimit(pg.getLimit());
        example.setOffset(pg.getOffset());
        if (!CollectionUtils.isEmpty(queueIdsCondition)) {
            criteria.andQueueIdIn(queueIdsCondition);
        }
        long total = lauMaterialQueueSubscribeDao.countByExample(example);
        if (total == 0) {
            return PageResult.emptyPageResult();
        }

        List<LauMaterialQueueSubscribePo> queueSubscribePos = lauMaterialQueueSubscribeDao.selectByExample(example);
        List<Long> queueIds = queueSubscribePos.stream().map(LauMaterialQueueSubscribePo::getQueueId).collect(Collectors.toList());

        // 查询队列信息
        List<LauMaterialAuditQueueBo> auditQueueBos = this.queryListByIds(queueIds);
        auditQueueBos.forEach(q -> q.setIsSubscribed(1));
        return new PageResult<>((int) total, auditQueueBos);
    }

    /**
     * 订阅队列的列表
     *
     * @param page
     * @param pageSize
     * @param queryBo
     * @return
     */
    public PageResult<LauMaterialAuditQueueBo> queryQueueList(Integer page, Integer pageSize, MaterialQueueQueryBo queryBo) {
        Assert.notNull(queryBo, "queryBo不能为空");
        Assert.isTrue(!StringUtils.isEmpty(queryBo.getUsername()), "username不能为空");

        if (Utils.isPositive(queryBo.getIsSubscribe())) {
            return this.querySubscribeList(page, pageSize, queryBo);
        }

        return queryAllQueueList(page, pageSize, queryBo);
    }

    private PageResult<LauMaterialAuditQueueBo> queryAllQueueList(Integer page, Integer pageSize, MaterialQueueQueryBo queryBo) {
        LauMaterialAuditQueuePoExample example = new LauMaterialAuditQueuePoExample();
        LauMaterialAuditQueuePoExample.Criteria criteria = example.createCriteria();
        Page pg = Page.valueOf(page, pageSize);
        example.setLimit(pg.getLimit());
        example.setOffset(pg.getOffset());
        example.setOrderByClause("ctime desc");

        if (!CollectionUtils.isEmpty(queryBo.getIds())) {
            criteria.andIdIn(queryBo.getIds());
        }
        if (!StringUtils.isEmpty(queryBo.getName())) {
            criteria.andNameLike("%" + queryBo.getName() + "%");
        }
        if (Utils.isPositive(queryBo.getBizType())) {
            criteria.andBizTypeEqualTo(queryBo.getBizType());
        }
        if (Utils.isPositive(queryBo.getMaterialType())) {
            criteria.andMaterialTypeEqualTo(queryBo.getMaterialType());
        }
        // 排除特殊队列
//        criteria.andIdNotIn(bizConfig.getSpecialQueueIds());
        long total = lauMaterialAuditQueueDao.countByExample(example);
        if (total == 0) {
            return PageResult.emptyPageResult();
        }

        List<LauMaterialAuditQueuePo> queueSubscribePos = lauMaterialAuditQueueDao.selectByExample(example);

        // 是否已经队列
        LauMaterialQueueSubscribePoExample subscribePoExample = new LauMaterialQueueSubscribePoExample();
        subscribePoExample.createCriteria().andUsernameEqualTo(queryBo.getUsername());
        List<LauMaterialQueueSubscribePo> subscribePos = lauMaterialQueueSubscribeDao.selectByExample(subscribePoExample);
        Set<Long> subscribedQueueIdSet = subscribePos.stream().map(LauMaterialQueueSubscribePo::getQueueId).collect(Collectors.toSet());

        List<LauMaterialAuditQueueBo> auditQueueBos = IMaterialAuditQueueConvertor.INSTANCE.pos2bos(queueSubscribePos);
        auditQueueBos.forEach(q -> {
            if (subscribedQueueIdSet.contains(q.getId())) {
                q.setIsSubscribed(1);
            } else {
                q.setIsSubscribed(0);
            }
        });
        return new PageResult<>((int) total, auditQueueBos);
    }

    @Transactional(rollbackFor = Exception.class, transactionManager = DatabaseConstant.RISK_TRANSACTION_MANAGER)
    public void importQueueData(List<LauMaterialAuditQueueBo> queueBos) {
        if (CollectionUtils.isEmpty(queueBos)) {
            return;
        }
        for (LauMaterialAuditQueueBo queueBo : queueBos) {
            Assert.isTrue(StringUtils.isNotEmpty(queueBo.getName()), "队列名称不能为空");
//            Assert.isTrue(Utils.isPositive(queueBo.getBizType()), "业务类型不能为空");
//            Assert.isTrue(Utils.isPositive(queueBo.getMaterialType()), "素材类型不能为空");
            Assert.isTrue(Utils.isPositive(queueBo.getPullNum()), "拉取数量不能为空");
            Assert.isTrue(Utils.isPositive(queueBo.getTaskTimeout()), "任务超时时长不能为空");
//            Assert.isTrue(StringUtils.isNotEmpty(queueBo.getRoleIds()), "角色列表不能为空");

            LauMaterialAuditQueuePo auditQueuePo = IMaterialAuditQueueConvertor.INSTANCE.bo2po(queueBo);
            lauMaterialAuditQueueDao.insertSelective(auditQueuePo);
        }

    }

    public void mockDeleteQueueData() {
        LauMaterialAuditQueuePoExample example = new LauMaterialAuditQueuePoExample();
        LauMaterialAuditQueuePoExample.Criteria criteria = example.createCriteria();
        int count = lauMaterialAuditQueueDao.deleteByExample(example);
        log.info("mockDeleteQueueData: count={}", count);
    }

}
