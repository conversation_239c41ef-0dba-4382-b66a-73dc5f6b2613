package com.bilibili.risk.service.account;

import com.bapis.ad.crm.account.AccountIndustryItem;
import com.bapis.ad.crm.account.AccountReadServiceGrpc;
import com.bapis.ad.crm.account.QueryAccountIndustryByAccountIdRequest;
import com.bapis.ad.crm.account.QueryAccountIndustryByAccountIdResponse;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.risk.bo.AccountIndustryItemBo;
import com.bilibili.risk.convertor.IAccountConvertor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import pleiades.venus.starter.rpc.client.RPCClient;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AccountService {

    @RPCClient("sycpb.cpm.crm-portal")
    private AccountReadServiceGrpc.AccountReadServiceBlockingStub accountReadServiceStub;


    public List<AccountIndustryItemBo> queryAccountIndustryList(List<Integer> accountIds) {

        if (CollectionUtils.isEmpty(accountIds)) {
            return Collections.emptyList();
        }

        QueryAccountIndustryByAccountIdRequest request = QueryAccountIndustryByAccountIdRequest.newBuilder()
                .addAllAccountId(accountIds)
                .build();
        QueryAccountIndustryByAccountIdResponse response = accountReadServiceStub.queryAccountIndustryByAccountId(request);
        List<AccountIndustryItem> dataList = response.getDataList();
        return IAccountConvertor.INSTANCE.grpc2Bo(dataList);
    }

    public Map<Integer, AccountIndustryItemBo> queryAccountIndustryMap(List<Integer> accountIds) {

        return this.queryAccountIndustryList(accountIds).stream()
                .collect(Collectors.toMap(AccountIndustryItemBo::getAccountId, t -> t));
    }

    public AccountIndustryItemBo fetchAccountIndustry(Integer accountId) {

        if (!Utils.isPositive(accountId)) {
            return null;
        }

        Map<Integer, AccountIndustryItemBo> map = this.queryAccountIndustryMap(Collections.singletonList(accountId));
        if (map == null) {
            return null;
        }
        return map.get(accountId);
    }
}
