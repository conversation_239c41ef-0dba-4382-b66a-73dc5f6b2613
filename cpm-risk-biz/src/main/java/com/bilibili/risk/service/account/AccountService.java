package com.bilibili.risk.service.account;

import com.bapis.ad.crm.account.AccountIndustryItem;
import com.bapis.ad.crm.account.AccountReadServiceGrpc;
import com.bapis.ad.crm.account.QueryAccountIndustryByAccountIdRequest;
import com.bapis.ad.crm.account.QueryAccountIndustryByAccountIdResponse;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.risk.bo.AccountIndustryItemBo;
import com.bilibili.risk.config.RedisConfig;
import com.bilibili.risk.convertor.IAccountConvertor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import pleiades.venus.starter.rpc.client.RPCClient;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AccountService {

    private static final String ACCOUNT_INDUSTRY_CACHE_KEY_PREFIX = "risk:account:industry:";
    private static final long CACHE_EXPIRE_TIME = 30; // 缓存过期时间，单位分钟

    @RPCClient("sycpb.cpm.crm-portal")
    private AccountReadServiceGrpc.AccountReadServiceBlockingStub accountReadServiceStub;
    @Resource(name = RedisConfig.ADP_CLUSTER)
    private RedissonClient adpRedissonClient;

    /**
     * 查询账户行业信息，使用Redis缓存优化
     *
     * @param accountIds 账户ID列表
     * @return 账户行业信息列表
     */
    public List<AccountIndustryItemBo> queryAccountIndustryList(List<Integer> accountIds) {
        if (CollectionUtils.isEmpty(accountIds)) {
            return Collections.emptyList();
        }

        // 1. 创建结果列表
        List<AccountIndustryItemBo> resultList = new ArrayList<>();
        // 2. 需要从RPC获取的ID列表
        List<Integer> needFetchAccountIds = new ArrayList<>();

        // 3. 尝试从Redis缓存中获取
        for (Integer accountId : accountIds) {
            String cacheKey = ACCOUNT_INDUSTRY_CACHE_KEY_PREFIX + accountId;
            RBucket<AccountIndustryItemBo> bucket = adpRedissonClient.getBucket(cacheKey);
            AccountIndustryItemBo cachedData = bucket.get();

            if (cachedData != null) {
                // 缓存命中，添加到结果列表
                resultList.add(cachedData);
                log.debug("从缓存获取账户行业信息 accountId={}", accountId);
            } else {
                // 缓存未命中，添加到需要获取的列表
                needFetchAccountIds.add(accountId);
            }
        }

        // 4. 如果所有数据都从缓存获取到了，直接返回
        if (needFetchAccountIds.isEmpty()) {
            log.info("所有账户行业信息从缓存获取成功 accountIds={}", accountIds);
            return resultList;
        }

        // 5. 从RPC获取未缓存的数据
        log.info("从RPC获取账户行业信息 accountIds={}", needFetchAccountIds);
        List<AccountIndustryItemBo> rpcResultList = queryAccountIndustryListByRpc(needFetchAccountIds);

        // 6. 将RPC获取的数据添加到结果列表并缓存
        for (AccountIndustryItemBo accountIndustryBo : rpcResultList) {
            Integer accountId = accountIndustryBo.getAccountId();
            resultList.add(accountIndustryBo);

            // 缓存数据
            String cacheKey = ACCOUNT_INDUSTRY_CACHE_KEY_PREFIX + accountId;
            RBucket<AccountIndustryItemBo> bucket = adpRedissonClient.getBucket(cacheKey);
            bucket.set(accountIndustryBo, CACHE_EXPIRE_TIME, TimeUnit.MINUTES);
            log.debug("账户行业信息已缓存 accountId={}, 过期时间={}分钟", accountId, CACHE_EXPIRE_TIME);
        }

        return resultList;
    }

    /**
     * 通过RPC调用获取账户行业信息（原始实现逻辑）
     *
     * @param accountIds 账户ID列表
     * @return 账户行业信息列表
     */
    private List<AccountIndustryItemBo> queryAccountIndustryListByRpc(List<Integer> accountIds) {
        if (CollectionUtils.isEmpty(accountIds)) {
            return Collections.emptyList();
        }

        QueryAccountIndustryByAccountIdRequest request = QueryAccountIndustryByAccountIdRequest.newBuilder()
                .addAllAccountId(accountIds)
                .build();
        QueryAccountIndustryByAccountIdResponse response = accountReadServiceStub
                .withDeadlineAfter(3000, TimeUnit.MILLISECONDS)
                .queryAccountIndustryByAccountId(request);
        List<AccountIndustryItem> dataList = response.getDataList();
        return IAccountConvertor.INSTANCE.grpc2Bo(dataList);
    }

    public Map<Integer, AccountIndustryItemBo> queryAccountIndustryMap(List<Integer> accountIds) {
        return this.queryAccountIndustryList(accountIds).stream()
                .collect(Collectors.toMap(AccountIndustryItemBo::getAccountId, t -> t));
    }

    public AccountIndustryItemBo fetchAccountIndustry(Integer accountId) {
        if (!Utils.isPositive(accountId)) {
            return null;
        }

        Map<Integer, AccountIndustryItemBo> map = this.queryAccountIndustryMap(Collections.singletonList(accountId));
        if (map == null) {
            return null;
        }
        return map.get(accountId);
    }

    /**
     * 清除账户行业缓存
     * 当账户行业信息发生变化时可调用此方法清除特定账户的缓存
     *
     * @param accountId 账户ID
     */
    public void clearAccountIndustryCache(Integer accountId) {
        if (accountId == null) {
            return;
        }
        String cacheKey = ACCOUNT_INDUSTRY_CACHE_KEY_PREFIX + accountId;
        RBucket<AccountIndustryItemBo> bucket = adpRedissonClient.getBucket(cacheKey);
        bucket.delete();
        log.info("账户行业缓存已清除 accountId={}", accountId);
    }

    /**
     * 批量清除账户行业缓存
     *
     * @param accountIds 账户ID列表
     */
    public void clearAccountIndustryCache(List<Integer> accountIds) {
        if (CollectionUtils.isEmpty(accountIds)) {
            return;
        }
        for (Integer accountId : accountIds) {
            clearAccountIndustryCache(accountId);
        }
        log.info("批量清除账户行业缓存完成 accountIds={}", accountIds);
    }
}
