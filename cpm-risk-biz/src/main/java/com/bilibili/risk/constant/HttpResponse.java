package com.bilibili.risk.constant;

import com.bilibili.risk.exception.RiskRemoteAccessErr;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * @ClassName HttpResponse
 * <AUTHOR>
 * @Date 2024/4/18 8:00 下午
 * @Version 1.0
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class HttpResponse<T> {

    private Integer code;
    @JsonIgnoreProperties(ignoreUnknown = true)
    private String message;
    @JsonIgnoreProperties(ignoreUnknown = true)
    private Long ttl;
    private T data;

    public T getSuccessData() {
        if (!Objects.equals(0, code)) {
            throw RiskRemoteAccessErr.wrongStatus(code, message);
        }
        return data;
    }
}
