package com.bilibili.risk.constant;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

public interface RiskConstants {

    String PAGE_GROUP_URL_KEY = "__PAGE_GROUP_URL__";

    // 系统类型
    Integer SYSTEM_TYPE_RISK = 16;

    // 默认时间戳，表示空
    Timestamp BUSINESS_EARLIEST_TIMESTAMP = Timestamp.valueOf("2000-01-01 00:00:00");
    Long BUSINESS_EARLIEST_TIMESTAMP_LONG = BUSINESS_EARLIEST_TIMESTAMP.getTime();

    String HYPHEN = "-";

    String SYSTEM_USERNAME = "SYSTEM";
    String LANDING_PAGE_GROUP_REJECT_REASON = "没有可用的落地页";

    // redis 锁前缀
    String LOCK_RULE_CREATE = "risk:lock:rule:create:";
    String LOCK_RULE_UPDATE = "risk:lock:rule:update:";
    String LOCK_CREATIVE_MATERIAL_PUSH_TO_AUDIT = "risk:lock:material:creative:pushToAudit:";
    String LOCK_CREATIVE_JUDGE_AUDIT_RESULT = "risk:lock:material:creative:judgeAuditResult:";
    String LOCK_TASK_PULL = "risk:lock:task:pull:";
    String LOCK_TASK_AUDIT = "risk:lock:task:audit:";
    String MATERIAL_CREATIVE_SYNC = "risk:lock:material:creative:sync";
    String PAGE_GROUP_STATUS_SYNC = "risk:lock:pagegroup:status:sync";

    // redis缓存前缀
    String CACHE_PREFIX_AUDIT_COMPLETE_QUEUE_ID = "risk:cache:audit:complete:queueId:";

    // 组件类型: 0-未定义, 1-框下组件, 2-story通用组件, 3-story优惠券组件, 4-story图片组件
    Integer COMPONENT_TYPE_UNDERFRAME = 1;
    Integer COMPONENT_TYPE_STORY_COMMON = 2;
    Integer COMPONENT_TYPE_STORY_COUPON = 3;
    Integer COMPONENT_TYPE_STORY_IMAGE = 4;

    String SORT_FIELD_ID = "id";
    String SORT_FIELD_MTIME = "mtime";
    String SORT_FIELD_CTIME = "ctime";
    String SORT_FIELD_ENTER_AUDIT_TIME = "enterAuditTime";

    // 投放端gif素材类型
    Integer LAUNCH_GIF_MATERIAL_TYPE = 3;

    List<Integer> COMPONENT_TYPE_LIST = Arrays.asList(
            COMPONENT_TYPE_UNDERFRAME,
            COMPONENT_TYPE_STORY_COMMON,
            COMPONENT_TYPE_STORY_COUPON,
            COMPONENT_TYPE_STORY_IMAGE
    );
}
