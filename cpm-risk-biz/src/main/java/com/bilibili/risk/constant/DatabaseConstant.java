package com.bilibili.risk.constant;

public interface DatabaseConstant {

    // risk 数据源
    String RISK_DATASOURCE_SHARDING = "riskShardingDataSource";
    String RISK_TRANSACTION_MANAGER = "riskTransactionManager";

    // ad 数据源
    String AD_DATASOURCE = "adDataSource";
    String AD_TRANSACTION_MANAGER = "adTransactionManager";

    String AD_CORE_DATASOURCE = "adCoreDataSource";
    String AD_CORE_TRANSACTION_MANAGER = "adCoreTransactionManager";

    String RISK_JDBC_TEMPLATE = "riskJdbcTemplate";

    String MybatisPlugin = "catExecutorMybatisPlugin";

    String MybatisInterceptorPlugin = "mybatisInterceptorPlugin";


}
