package com.bilibili.risk.constant;


import com.bapis.ad.audit.AuditStatus;
import org.springframework.util.Assert;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

public class CreativeStatus {
    public static final int VALID = 1;
    public static final int PAUSED = 2;
    public static final int DELETED = 4;
    public static final int AUDITING = 5;
    public static final int AUDIT_REJECTED = 6;

    /**
     * 落地页待审核
     */
    public static final int LANDING_PAGE_AUDITING = 9;

    /**
     * 创建中
     */
    public static final int CREATING = 10;
}

