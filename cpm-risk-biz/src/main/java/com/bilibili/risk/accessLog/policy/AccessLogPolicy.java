package com.bilibili.risk.accessLog.policy;

import com.bilibili.risk.accessLog.enums.AccessLogField;
import com.bilibili.risk.accessLog.process.AccessLogProcess;
import com.bilibili.risk.accessLog.process.impl.BlankAccessLogProcess;
import com.bilibili.risk.accessLog.process.impl.DefaultAccessLogProcess;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025-04-25 14:43
 * 定义日志策略的接口
 */
public interface AccessLogPolicy {

    Logger log = LoggerFactory.getLogger(AccessLogPolicy.class);

    void before();

    void after();

    void catchError(Throwable e);

    // 是否打印日志
    boolean isLog();

    // 日志类型，见：AccessLogType 如HTTP、GRPC、SOA、JOB、DATABUS等
    String getLogType();

    // 获取URI
    String getUri();

    // 获取请求参数
    String getQuery();

    // 获取返回状态 见 AccessCode
    Integer getAccessCode();

    // 获取请求耗时
    Long getCost();

    // 额外负载
    Map<AccessLogField, String> getPayload();

    default AccessLogProcess initProcess() {

        try {
            return new DefaultAccessLogProcess(this);
        } catch (Exception e) {
            log.error("AccessLogPolicy 初始化失败, 启用BlankAccessLogProcess作为兜底", e);
            return new BlankAccessLogProcess();
        }
    }
}
