package com.bilibili.risk.accessLog.db.handler;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.mapping.SqlCommandType;

/**
 * <AUTHOR>
 * @date 2025-04-28 20:32
 * SQL执行上下文
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SqlExecutionContext {

    // SQL标识
    private String sqlId;

    // 表名
    private String tableName;

    // 实际执行的SQL
    private String actualSql;

    // 执行耗时
    private long costTime;

    // 影响行数
    private int affectedRows;

    // SQL类型
    private SqlCommandType sqlCommandType;

    // 可以扩展更多SQL执行过程中的信息
}
