package com.bilibili.risk.accessLog.aop;

import com.bilibili.risk.accessLog.ann.AccessLogModule;
import com.bilibili.risk.accessLog.enums.AccessLogField;
import com.bilibili.risk.accessLog.utils.AccessLogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

import java.lang.annotation.Annotation;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025-04-28 14:54
 * AccessLogModule的切面类
 * 用于写入module到MDC中
 */
@Aspect
@Component
@Slf4j
public class AccessLogModuleAspect {

    @Around(value = "@annotation(com.bilibili.risk.accessLog.ann.AccessLogModule)||@within(com.bilibili.risk.accessLog.ann.AccessLogModule)")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {

        AccessLogUtil.ignoreEx(() -> beforeProcess(joinPoint));

        //执行
        return joinPoint.proceed();
    }

    //执行前处理
    private void beforeProcess(ProceedingJoinPoint joinPoint) {
        //获取注解信息
        AccessLogModule accessLogModule = getAnn(joinPoint, AccessLogModule.class);
        //多个带该注解的方法互相调用时，取最早的一个
        if (Objects.nonNull(accessLogModule) && StringUtils.isEmpty(MDC.get(AccessLogField.module.name()))) {
            MDC.put(AccessLogField.module.name(), accessLogModule.value());
        }
    }

    private static <T extends Annotation> T getAnn (ProceedingJoinPoint joinPoint, Class<T> clazz) {
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        T accessLogModule = methodSignature.getMethod().getAnnotation(clazz);
        if (accessLogModule == null) {
            accessLogModule = joinPoint.getTarget().getClass().getAnnotation(clazz);
        }
        return accessLogModule;
    }
}
