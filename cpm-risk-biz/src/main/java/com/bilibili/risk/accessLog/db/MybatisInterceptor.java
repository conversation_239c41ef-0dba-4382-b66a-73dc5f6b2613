package com.bilibili.risk.accessLog.db;

import com.bilibili.risk.accessLog.db.handler.SqlExecutionContext;
import com.bilibili.risk.accessLog.db.handler.SqlMetricsProcessor;
import com.bilibili.risk.accessLog.db.handler.SqlReporter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Signature;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;

/**
 * <AUTHOR>
 * @date 2025-04-28 19:32
 * mybatis拦截器，主要负责拦截
 * 拦截Executor接口的select和update
 */
@Intercepts({
        @Signature(
                method = "query",
                type = Executor.class,
                args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class}),
        @Signature(
                method = "update",
                type = Executor.class,
                args = {MappedStatement.class, Object.class}
)})
@RequiredArgsConstructor
@Slf4j
public class MybatisInterceptor implements Interceptor {

    // 指标解析: 专注SQL指标处理
    private final SqlMetricsProcessor processor;
    // 指标打印: 专注日志记录和指标收集
    private final SqlReporter reporter;

    @Override
    public Object intercept(Invocation invocation) throws Throwable {

        MappedStatement mappedStatement = (MappedStatement) invocation.getArgs()[0];
        Object parameterObject = invocation.getArgs()[1];

        // 记录执行开始时间
        long startTime = System.currentTimeMillis();

        // 执行原始方法
        Object result = null;
        try {
            result = invocation.proceed();
            return result;
        } finally {
            try {
                // 记录执行结束时间
                long endTime = System.currentTimeMillis();

                // 1. 处理SQL执行数据，生成上下文
                SqlExecutionContext ctx = processor.processSqlExecution(
                        mappedStatement, parameterObject, result, startTime, endTime);

                // 2. 报告SQL执行情况（日志和指标）
                reporter.report(ctx);
            } catch (Exception e) {
                log.error("解析和上报SQL时报错: ", e);
            }

        }
    }

}
