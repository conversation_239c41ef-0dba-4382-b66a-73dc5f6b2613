package com.bilibili.risk.accessLog.db.handler;

import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.ParameterMapping;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.session.Configuration;
import org.apache.ibatis.type.TypeHandler;
import org.apache.ibatis.type.TypeHandlerRegistry;
import org.springframework.stereotype.Service;

import java.text.DateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2025-04-28 20:27
 * SQL指标处理器，负责解析SQL并提取相关指标
 */
@Slf4j
@Service
public class SqlMetricsProcessor {

    /**
     * 处理SQL执行并构建上下文
     */
    public SqlExecutionContext processSqlExecution(MappedStatement ms, Object parameter,
                                                   Object result, long startTime, long endTime) {

        // 创建SQL执行上下文
        SqlExecutionContext ctx = new SqlExecutionContext();

        // 获取SQL ID (namespace.method)
        String sqlId = ms.getId();
        ctx.setSqlId(sqlId);

        // 获取SQL类型
        SqlCommandType sqlCommandType = ms.getSqlCommandType();
        ctx.setSqlCommandType(sqlCommandType);

        // 获取带占位符的SQL
        BoundSql boundSql = ms.getBoundSql(parameter);
        String sql = boundSql.getSql();

        // 获取实际参数值(替换占位符)
        String actualSql = getSqlWithParams(boundSql, parameter, ms.getConfiguration());
        ctx.setActualSql(actualSql);


        // 计算执行时间
        long costTime = endTime - startTime;
        ctx.setCostTime(costTime);

        // 获取影响行数
        int affectedRows = getAffectedRows(result, sqlCommandType);
        ctx.setAffectedRows(affectedRows);

        // 解析表名
        String tableName = parseTableName(sql, sqlCommandType);
        ctx.setTableName(tableName);

        return ctx;
    }

    /**
     * 获取SQL影响的行数
     */
    private int getAffectedRows(Object result, SqlCommandType sqlCommandType) {
        if (result == null) {
            return 0;
        }

        if (SqlCommandType.INSERT.equals(sqlCommandType)
                || SqlCommandType.UPDATE.equals(sqlCommandType)
                || SqlCommandType.DELETE.equals(sqlCommandType)) {
            // 对于更新操作，返回的是影响行数
            return (int) result;
        } else if (result instanceof List) {
            // 对于查询操作，返回结果集大小
            return ((List<?>) result).size();
        }

        return 0;
    }

    /**
     * 从SQL语句中解析表名
     */
    private String parseTableName(String sql, SqlCommandType sqlCommandType) {
        // 简单实现，通过正则提取表名
        String regex = "";

        if (SqlCommandType.SELECT.equals(sqlCommandType)) {
            regex = "\\s+FROM\\s+(\\w+)";
        } else if (SqlCommandType.INSERT.equals(sqlCommandType)) {
            regex = "\\s+INTO\\s+(\\w+)";
        } else if (SqlCommandType.UPDATE.equals(sqlCommandType)) {
            regex = "\\s*UPDATE\\s+(\\w+)";
        } else if (SqlCommandType.DELETE.equals(sqlCommandType)) {
            regex = "\\s+FROM\\s+(\\w+)";
        }

        Pattern pattern = Pattern.compile(regex, Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(sql);

        if (matcher.find()) {
            return matcher.group(1);
        }

        return "unknown";
    }

    /**
     * 替换SQL中的参数占位符
     */
    private String getSqlWithParams(BoundSql boundSql, Object parameterObject, Configuration configuration) {

        // 去掉多余的换行符和多余的空格
        String sql = boundSql.getSql().replaceAll("\\s+", " ");

        // 获取参数映射
        List<ParameterMapping> parameterMappings = boundSql.getParameterMappings();

        if (Objects.isNull(parameterObject) || parameterMappings.isEmpty()) {
            return sql;
        }

        StringBuilder sqlBuilder = new StringBuilder();
        int lastIndex = 0;

        // 获取 TypeHandlerRegistry
        TypeHandlerRegistry typeHandlerRegistry = configuration.getTypeHandlerRegistry();

        for (int i = 0; i < parameterMappings.size(); i++) {
            ParameterMapping parameterMapping = parameterMappings.get(i);
            String propertyName = parameterMapping.getProperty();
            Object value;

            // 获取参数值
            if (boundSql.hasAdditionalParameter(propertyName)) {
                // 优先从附加参数中获取
                value = boundSql.getAdditionalParameter(propertyName);
            } else if (parameterObject instanceof List) {
                // 从List中按索引获取参数值
                value = ((List<?>) parameterObject).get(i);
            } else {
                // 从对象属性中获取参数值
                MetaObject metaObject = configuration.newMetaObject(parameterObject);
                value = metaObject.hasGetter(propertyName) ? metaObject.getValue(propertyName) : null;
            }

            // 定位下一个占位符的位置
            int questionMarkIndex = sql.indexOf('?', lastIndex);
            if (questionMarkIndex == -1) {
                break;
            }

            // 拼接占位符前的部分
            sqlBuilder.append(sql, lastIndex, questionMarkIndex);

            // 使用 TypeHandler 格式化参数值
            if (value != null && typeHandlerRegistry.hasTypeHandler(value.getClass())) {
                @SuppressWarnings("unchecked")
                TypeHandler<Object> typeHandler = (TypeHandler<Object>) typeHandlerRegistry.getTypeHandler(value.getClass());
                sqlBuilder.append(formatValueWithTypeHandler(typeHandler, value));
            } else {
                sqlBuilder.append(getParameterValue(value));
            }

            // 更新索引
            lastIndex = questionMarkIndex + 1;
        }

        // 拼接剩余的 SQL 部分
        sqlBuilder.append(sql.substring(lastIndex));

        return sqlBuilder.toString();
    }

    private String formatValueWithTypeHandler(TypeHandler<Object> typeHandler, Object value) {
        // 这里是简化处理，实际可根据不同类型提供更精确的格式化
        return value instanceof String ? "'" + value.toString().replace("'", "''") + "'" : String.valueOf(value);
    }

    private String getParameterValue(Object value) {
        if (value == null) {
            return "NULL";
        } else if (value instanceof String || value instanceof Character) {
            return "'" + value.toString().replace("'", "''") + "'";
        } else if (value instanceof Date) {
            DateFormat dateFormat = DateFormat.getDateTimeInstance(DateFormat.DEFAULT, DateFormat.DEFAULT, Locale.CHINA);
            return "'" + dateFormat.format(value) + "'";
        } else if (value instanceof Number || value instanceof Boolean) {
            return String.valueOf(value);
        } else {
            return "'" + value.toString().replace("'", "''") + "'";
        }
    }


}
