package com.bilibili.risk.accessLog.enums;

/**
 * <AUTHOR>
 * @date 2025-04-25 15:19
 * 日志中追加的字段
 */
public enum AccessLogField {

    // 日志来源、所属模块
    scope_name,

    // 日志类型，见：AccessLogType
    log_type,

    // 请求uri
    req_uri,

    // 所属业务模块
    module,

    // 请求参数
    req_query,

    // 接口访问的域账号(web请求才有意义)
    domain_username,

    // 请求耗时
    req_cost,

    // 响应状态
    access_code,

    slow_sql_id,

    slow_sql_table,

    slow_sql_time,


}
