package com.bilibili.risk.accessLog.process;

import lombok.Getter;
import lombok.Setter;

import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @date 2025-04-25 19:07
 * 日志上下文
 */
public class AccessLogContext {

    // 访问层数+1
    public static void increaseLevel () {

        CTX.get().getAccessLevel().incrementAndGet();
    }

    // 访问层数-1
    public static int decreaseLevel () {

        return CTX.get().getAccessLevel().decrementAndGet();
    }

    // 设置异常信息
    public static void setThrowable (Throwable throwable) {

        CTX.get().setThrowable(throwable);
    }

    // 获取异常信息
    public static Throwable getThrowable () {

        return CTX.get().getThrowable();
    }

    // 设置 process 到上下文
    public static void setProcess(AccessLogProcess process) {
        CTX.get().setProcess(process);
    }

    // 从上下文中获取 process
    public static AccessLogProcess getProcess() {
        return CTX.get().getProcess();
    }

    // 设置 traceId
    public static void setTraceId (String traceId) {
        CTX.get().setTraceId(traceId);
    }

    // 获取 traceId
    public static String getTraceId () {
        return CTX.get().getTraceId();
    }

    // 清空MDC
    public static void clean () {
        CTX.remove();
    }

    private static final ThreadLocal<Context> CTX = ThreadLocal.withInitial(Context::new);

    @Setter
    @Getter
    public static class Context {

        // traceId
        private String traceId;

        // accessLog的调用层数
        private AtomicInteger accessLevel = new AtomicInteger(0);

        // 当前线程的process实例
        private AccessLogProcess process;

        // 异常信息
        private Throwable throwable;
    }
}
