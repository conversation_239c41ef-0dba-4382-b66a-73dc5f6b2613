package com.bilibili.risk.accessLog.policy.impl;

import com.alibaba.fastjson.JSON;
import com.bilibili.risk.accessLog.enums.AccessCode;
import com.bilibili.risk.accessLog.enums.AccessLogField;
import com.bilibili.risk.accessLog.enums.AccessLogType;
import com.bilibili.risk.accessLog.policy.AccessLogPolicy;
import com.bilibili.risk.accessLog.process.AccessLogContext;
import com.bilibili.risk.accessLog.utils.AccessLogUtil;
import com.bilibili.risk.common.Context;
import com.google.common.collect.ImmutableMap;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.servlet.HandlerMapping;
import org.springframework.web.util.ContentCachingRequestWrapper;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025-04-25 14:55
 * http请求的日志策略
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Slf4j
public class HttpPolicy implements AccessLogPolicy {

    private HttpServletRequest request;

    private HttpServletResponse response;

    private Long startTime;
    private Long endTime;
    private Long cost;
    private Integer accessCode;
    private String module;


    @Override
    public void before() {

        // 请求开始时间
        startTime = System.currentTimeMillis();
    }

    @Override
    public void after() {

        // 请求结束时间
        endTime = System.currentTimeMillis();

        // 设置耗时
        setCost();

        // 设置access_code
        setAccessCode();

    }

    @Override
    public void catchError (Throwable e) {

    }

    @Override
    public boolean isLog() {
        return true;
    }

    @Override
    public String getLogType() {
        return AccessLogType.HTTP.name();
    }

    @Override
    public String getUri() {

        String method = request.getMethod();

        // 识别请求匹配的控制器模式（标准化路径模式），而不仅仅是实际的URL
        String uri = AccessLogUtil.toString(request.getAttribute(HandlerMapping.BEST_MATCHING_PATTERN_ATTRIBUTE));

        // 如果没有匹配的控制器模式，则使用请求的URL
        if (StringUtils.isEmpty(uri)) {
            uri = request.getRequestURL().toString();
        }

        return String.format("%s[%s]", uri, method);
    }

    @Override
    public String getQuery() {

        RequestQuery reqQuery = new RequestQuery();
        reqQuery.setPathVar(getPathVariables());
        reqQuery.setQueryString(request.getQueryString());
        reqQuery.setContext(getContext());
        reqQuery.setRequestBody(getBody());

        return JSON.toJSONString(reqQuery);
    }

    private void setAccessCode () {

        // 如果没有异常，说明请求成功
        if (Objects.isNull(AccessLogContext.getThrowable())) {
            this.accessCode = AccessCode.C_200.getCode();
            return;
        }

        // 这里可以根据异常类型设置不同的access_code进行扩展

        // 默认设置为500
        this.accessCode = AccessCode.C_500.getCode();
    }

    @Override
    public Integer getAccessCode() {

        return accessCode;
    }

    private void setCost () {
        if (Objects.isNull(startTime) || Objects.isNull(endTime)) {
            return;
        }
        cost = endTime - startTime;
    }

    @Override
    public Long getCost() {
        return cost;
    }

    @Override
    public Map<AccessLogField, String> getPayload() {

        return ImmutableMap.of(
                AccessLogField.domain_username, getDomainUsername(),
                AccessLogField.scope_name, getScopeName()
        );
    }

    private String getScopeName () {

        return this.getClass().getSimpleName();
    }

    private String getDomainUsername() {

        Context ctx = getContext();
        return StringUtils.isEmpty(ctx.getUsername()) ? "" : ctx.getUsername();
    }

    private String getPathVariables() {

        // 获取请求的路径变量
        Object pathVarMap = request.getAttribute(HandlerMapping.URI_TEMPLATE_VARIABLES_ATTRIBUTE);

        if (Objects.isNull(pathVarMap)) {
            return null;
        }

        String pathVar = pathVarMap.toString();

        if ("{}".equals(pathVar)) {
            return null;
        }
        return pathVar;
    }

    private Context getContext() {

        Object context = request.getAttribute("context");

        if (Objects.isNull(context)) {
            return Context.builder().build();
        }
        return JSON.parseObject(JSON.toJSONString(context), Context.class);
    }

    private String getBody() {

        String contentType = request.getContentType();
        if (Objects.isNull(contentType) || !(contentType.contains("json") || contentType.contains("text"))){
            return null;
        }

        // 不是ContentCachingRequestWrapper的请求，说明没有缓存请求体, 直接返回null
        if (!(request instanceof ContentCachingRequestWrapper)) {
            return null;
        }
        ContentCachingRequestWrapper contentCachingRequestWrapper = (ContentCachingRequestWrapper) request;
        // 获取缓存的请求体
        return new String(contentCachingRequestWrapper.getContentAsByteArray()).replace("\n","");
    }

    @Getter
    @Setter
    private static class RequestQuery {
        private String pathVar;
        private Context context;
        private String queryString;
        private String requestBody;
    }
}
