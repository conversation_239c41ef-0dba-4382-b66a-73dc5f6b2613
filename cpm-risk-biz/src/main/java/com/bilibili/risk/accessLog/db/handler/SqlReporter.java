package com.bilibili.risk.accessLog.db.handler;

import com.bilibili.risk.accessLog.enums.AccessLogField;
import com.bilibili.risk.accessLog.utils.AccessLogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025-04-28 20:28
 * SQL执行报告器，负责日志打印和指标收集
 */
@Slf4j
@Service
public class SqlReporter {

    // 定义慢SQL阈值（毫秒）
    @Value("${biz.slow.sql.threshold:1000}")
    private long slowSqlThreshold;

    /**
     * 处理SQL执行上下文，进行日志记录和指标收集
     */
    public void report(SqlExecutionContext ctx) {
        // 构建日志消息
        StringBuilder logMessage = new StringBuilder();
        logMessage.append("SQL执行 | ")
                .append("类型:").append(ctx.getSqlCommandType()).append(" | ")
                .append("表:").append(ctx.getTableName()).append(" | ")
                .append("耗时:").append(ctx.getCostTime()).append("ms").append(" | ")
                .append("影响行数:").append(ctx.getAffectedRows()).append(" | ")
                .append("ID:").append(ctx.getSqlId());

        // 根据SQL执行时间决定日志级别和处理方式
        if (ctx.getCostTime() > slowSqlThreshold) {

            // 处理慢SQL
            recordSlowSql(ctx, logMessage);

        } else {
            // 普通SQL使用debug级别，避免日志过多
            log.debug("{} | SQL: {}", logMessage, ctx.getActualSql());
        }

        // 收集SQL指标
        collectSqlMetrics(ctx);
    }

    /**
     * 记录慢SQL信息
     */
    private void recordSlowSql(SqlExecutionContext ctx, StringBuilder logMessage) {

        // MDC记录额外信息用于日志分析
        AccessLogUtil.putMdc(AccessLogField.slow_sql_id, ctx.getSqlId());
        AccessLogUtil.putMdc(AccessLogField.slow_sql_table, ctx.getTableName());
        AccessLogUtil.putMdc(AccessLogField.slow_sql_time, String.valueOf(ctx.getCostTime()));

        try {
            // 这里可以添加将慢SQL发送到监控系统的逻辑
            // 例如: 推送到Prometheus、ELK、定制监控系统等
            log.warn("{} | 慢SQL | 完整SQL: {}", logMessage, ctx.getActualSql());

        } finally {
            // 完成后清理MDC
            AccessLogUtil.cleanMdc(AccessLogField.slow_sql_id);
            AccessLogUtil.cleanMdc(AccessLogField.slow_sql_table);
            AccessLogUtil.cleanMdc(AccessLogField.slow_sql_time);
        }
    }

    /**
     * 收集SQL执行指标
     */
    private void collectSqlMetrics(SqlExecutionContext ctx) {
        // 这里实现将SQL执行指标推送到指标系统的逻辑
        // 例如: 通过Micrometer注册指标

        // 示例实现:
        // Timer.builder("sql.execution.time")
        //      .tag("sqlId", ctx.getSqlId())
        //      .tag("table", ctx.getTableName())
        //      .tag("type", ctx.getSqlCommandType().toString())
        //      .register(registry)
        //      .record(ctx.getCostTime(), TimeUnit.MILLISECONDS);
    }
}
