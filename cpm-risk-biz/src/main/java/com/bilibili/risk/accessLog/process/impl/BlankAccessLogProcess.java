package com.bilibili.risk.accessLog.process.impl;

import com.bilibili.risk.accessLog.process.AccessLogProcess;

/**
 * <AUTHOR>
 * @date 2025-04-25 17:14
 * 空实现
 * 当日志处理器初始化失败时
 * 此空实现会作为兜底
 * 不影响正常流程
 */
public class BlankAccessLogProcess implements AccessLogProcess {

    // 执行前处理
    @Override
    public void before() {
        // 空实现
    }

    // 出现异常时处理
    @Override
    public void catchError(Throwable e) {
        // 空实现
    }

    // 执行后处理
    @Override
    public void after() {
        // 空实现
    }
}
