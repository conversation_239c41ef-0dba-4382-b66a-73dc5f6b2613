package com.bilibili.risk.accessLog.process.impl;

import com.bilibili.risk.accessLog.enums.AccessCode;
import com.bilibili.risk.accessLog.enums.AccessLogField;
import com.bilibili.risk.accessLog.policy.AccessLogPolicy;
import com.bilibili.risk.accessLog.process.AccessLogContext;
import com.bilibili.risk.accessLog.process.AccessLogProcess;
import com.bilibili.risk.accessLog.utils.AccessLogUtil;
import io.opentelemetry.api.trace.Span;
import io.opentelemetry.api.trace.SpanContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;

/**
 * <AUTHOR>
 * @date 2025-04-25 16:24
 * 默认日志流程处理器
 */
@Slf4j
public class DefaultAccessLogProcess implements AccessLogProcess {

    private AccessLogPolicy accessLogPolicy;

    public DefaultAccessLogProcess(AccessLogPolicy accessLogPolicy) {
        this.accessLogPolicy = accessLogPolicy;
    }

    @Override
    public void before() {

        // 写入traceId
        setTraceId();

        // 各种策略的before方法
        accessLogPolicy.before();
    }

    @Override
    public void catchError(Throwable e) {

        // 设置异常信息
        AccessLogContext.setThrowable(e);

        // 各策略的异常处理
        accessLogPolicy.catchError(e);
    }

    @Override
    public void after() {

        AccessLogUtil.ignoreEx(() -> {
            // 各种策略的after方法
            accessLogPolicy.after();

            // 是否打印日志
            if (!accessLogPolicy.isLog()) {
                return;
            }

            // 设置MDC
            AccessLogUtil.putMdc(AccessLogField.log_type, accessLogPolicy.getLogType());
            AccessLogUtil.putMdc(AccessLogField.req_uri, accessLogPolicy.getUri());
            AccessLogUtil.putMdc(AccessLogField.req_query, accessLogPolicy.getQuery());
            AccessLogUtil.putMdc(AccessLogField.req_cost, AccessLogUtil.toString(accessLogPolicy.getCost()));
            AccessLogUtil.putMdc(AccessLogField.access_code, AccessLogUtil.toString(accessLogPolicy.getAccessCode()));
            // 设置payload中的字段
            AccessLogUtil.putMdc(accessLogPolicy.getPayload());

            // 打印此次请求的日志
            log();
        });

        // 清空MDC
        AccessLogUtil.forceCleanMdc();
    }

    private void setTraceId () {

        // 获取当前线程的Span
        Span currentSpan = Span.current();
        SpanContext spanContext = currentSpan.getSpanContext();
        String traceId = spanContext.getTraceId();
        AccessLogContext.setTraceId(traceId);
    }

    /**
     * 根据mdc中的信息，打印此次请求的日志
     */
    private void log () {

        Integer accessCode = accessLogPolicy.getAccessCode();

        String logStr = buildLog();

        // 成功请求
        if (accessCode.equals(AccessCode.C_200.getCode())) {
            log.info(logStr);
        }

        // 失败请求
        if (!accessCode.equals(AccessCode.C_200.getCode())) {
            log.error(logStr, AccessLogContext.getThrowable());
        }
    }

    private String buildLog() {

        Throwable ex = AccessLogContext.getThrowable();

        return String.format("type:%s, req_uri:%s, access_code:%s, req_cost:%s, req_query:%s, module:%s, ex:%s",

                AccessLogUtil.getMdc(AccessLogField.log_type),
                AccessLogUtil.getMdc(AccessLogField.req_uri),
                AccessLogUtil.getMdc(AccessLogField.access_code),
                AccessLogUtil.getMdc(AccessLogField.req_cost),
                AccessLogUtil.getMdc(AccessLogField.req_query),
                AccessLogUtil.getMdc(AccessLogField.module),
                ex == null ? Strings.EMPTY : ex.getMessage());
    }

}
