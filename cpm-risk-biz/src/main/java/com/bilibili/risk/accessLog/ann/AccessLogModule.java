package com.bilibili.risk.accessLog.ann;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @date 2025-04-28 14:51
 * 用于标记日志对应的业务模块，方便后续统计区分
 * 一般建议在Controller层使用
 */
@Target({ElementType.METHOD,ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface AccessLogModule {

    /**
     * 模块名称
     * @return 模块名称
     */
    String value() default "";

}
