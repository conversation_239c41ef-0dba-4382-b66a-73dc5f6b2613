package com.bilibili.risk.accessLog.utils;

import com.bilibili.risk.accessLog.enums.AccessLogField;
import com.bilibili.risk.accessLog.process.AccessLogContext;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;

import java.util.Map;
import java.util.Objects;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2025-04-25 16:47
 * 日志相关工具方法
 */
@Slf4j
public class AccessLogUtil {

    public static void ignoreEx (Runnable fun) {
        try {
            fun.run();
        } catch (Exception e) {
            log.error("accessLog 执行中被忽略的异常", e);
        }
    }

    public static String toString (Object obj) {

        if (Objects.isNull(obj)) {
            return Strings.EMPTY;
        }
        return obj.toString();
    }

    // 强制清空MDC
    public static void forceCleanMdc () {
        cleanMdc(0);
    }

    public static void cleanMdc (int level) {

        // 如果访问层数大于0，说明还有其他的日志在打印，不清空MDC
        if (level > 0) {
            return;
        }

        // 清空MDC
        Stream.of(AccessLogField.values()).forEach(it -> {
            MDC.remove(it.name());
        });

        // 清空线程本地变量
        AccessLogContext.clean();
    }

    public static void cleanMdc (AccessLogField field) {

        if (Objects.isNull(field)) {
            return;
        }
        MDC.remove(field.name());
    }

    public static void putMdc (AccessLogField field, String value) {

        if (Objects.isNull(value)) {
            value = "";
        }
        MDC.put(field.name(), value);
    }

    public static void putMdc (Map<AccessLogField, String> payload) {

        if (Objects.isNull(payload)) {
            return;
        }
        payload.forEach(AccessLogUtil::putMdc);
    }

    public static String getMdc (AccessLogField field) {

        if (Objects.isNull(field)) {
            return null;
        }
        return MDC.get(field.name());
    }

    public static void printMdcFields() {
        Map<String, String> mdcMap = MDC.getCopyOfContextMap();
        if (mdcMap == null || mdcMap.isEmpty()) {
            log.info("MDC 中没有设置任何字段");
            return;
        }

        log.info("当前 MDC 中的字段：");
        mdcMap.forEach((key, value) -> log.info("{} = {}", key, value));
    }
}
