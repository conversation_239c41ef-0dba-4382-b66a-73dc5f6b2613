package com.bilibili.risk.po.ad;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public class LauProgrammaticCreativeDetailPoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public LauProgrammaticCreativeDetailPoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNull() {
            addCriterion("ctime is null");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNotNull() {
            addCriterion("ctime is not null");
            return (Criteria) this;
        }

        public Criteria andCtimeEqualTo(Timestamp value) {
            addCriterion("ctime =", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotEqualTo(Timestamp value) {
            addCriterion("ctime <>", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThan(Timestamp value) {
            addCriterion("ctime >", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("ctime >=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThan(Timestamp value) {
            addCriterion("ctime <", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("ctime <=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeIn(List<Timestamp> values) {
            addCriterion("ctime in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotIn(List<Timestamp> values) {
            addCriterion("ctime not in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime not between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNull() {
            addCriterion("mtime is null");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNotNull() {
            addCriterion("mtime is not null");
            return (Criteria) this;
        }

        public Criteria andMtimeEqualTo(Timestamp value) {
            addCriterion("mtime =", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotEqualTo(Timestamp value) {
            addCriterion("mtime <>", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThan(Timestamp value) {
            addCriterion("mtime >", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("mtime >=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThan(Timestamp value) {
            addCriterion("mtime <", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("mtime <=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeIn(List<Timestamp> values) {
            addCriterion("mtime in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotIn(List<Timestamp> values) {
            addCriterion("mtime not in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime not between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andAccountIdIsNull() {
            addCriterion("account_id is null");
            return (Criteria) this;
        }

        public Criteria andAccountIdIsNotNull() {
            addCriterion("account_id is not null");
            return (Criteria) this;
        }

        public Criteria andAccountIdEqualTo(Integer value) {
            addCriterion("account_id =", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotEqualTo(Integer value) {
            addCriterion("account_id <>", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdGreaterThan(Integer value) {
            addCriterion("account_id >", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("account_id >=", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdLessThan(Integer value) {
            addCriterion("account_id <", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdLessThanOrEqualTo(Integer value) {
            addCriterion("account_id <=", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdIn(List<Integer> values) {
            addCriterion("account_id in", values, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotIn(List<Integer> values) {
            addCriterion("account_id not in", values, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdBetween(Integer value1, Integer value2) {
            addCriterion("account_id between", value1, value2, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotBetween(Integer value1, Integer value2) {
            addCriterion("account_id not between", value1, value2, "accountId");
            return (Criteria) this;
        }

        public Criteria andCampaignIdIsNull() {
            addCriterion("campaign_id is null");
            return (Criteria) this;
        }

        public Criteria andCampaignIdIsNotNull() {
            addCriterion("campaign_id is not null");
            return (Criteria) this;
        }

        public Criteria andCampaignIdEqualTo(Integer value) {
            addCriterion("campaign_id =", value, "campaignId");
            return (Criteria) this;
        }

        public Criteria andCampaignIdNotEqualTo(Integer value) {
            addCriterion("campaign_id <>", value, "campaignId");
            return (Criteria) this;
        }

        public Criteria andCampaignIdGreaterThan(Integer value) {
            addCriterion("campaign_id >", value, "campaignId");
            return (Criteria) this;
        }

        public Criteria andCampaignIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("campaign_id >=", value, "campaignId");
            return (Criteria) this;
        }

        public Criteria andCampaignIdLessThan(Integer value) {
            addCriterion("campaign_id <", value, "campaignId");
            return (Criteria) this;
        }

        public Criteria andCampaignIdLessThanOrEqualTo(Integer value) {
            addCriterion("campaign_id <=", value, "campaignId");
            return (Criteria) this;
        }

        public Criteria andCampaignIdIn(List<Integer> values) {
            addCriterion("campaign_id in", values, "campaignId");
            return (Criteria) this;
        }

        public Criteria andCampaignIdNotIn(List<Integer> values) {
            addCriterion("campaign_id not in", values, "campaignId");
            return (Criteria) this;
        }

        public Criteria andCampaignIdBetween(Integer value1, Integer value2) {
            addCriterion("campaign_id between", value1, value2, "campaignId");
            return (Criteria) this;
        }

        public Criteria andCampaignIdNotBetween(Integer value1, Integer value2) {
            addCriterion("campaign_id not between", value1, value2, "campaignId");
            return (Criteria) this;
        }

        public Criteria andUnitIdIsNull() {
            addCriterion("unit_id is null");
            return (Criteria) this;
        }

        public Criteria andUnitIdIsNotNull() {
            addCriterion("unit_id is not null");
            return (Criteria) this;
        }

        public Criteria andUnitIdEqualTo(Integer value) {
            addCriterion("unit_id =", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdNotEqualTo(Integer value) {
            addCriterion("unit_id <>", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdGreaterThan(Integer value) {
            addCriterion("unit_id >", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("unit_id >=", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdLessThan(Integer value) {
            addCriterion("unit_id <", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdLessThanOrEqualTo(Integer value) {
            addCriterion("unit_id <=", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdIn(List<Integer> values) {
            addCriterion("unit_id in", values, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdNotIn(List<Integer> values) {
            addCriterion("unit_id not in", values, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdBetween(Integer value1, Integer value2) {
            addCriterion("unit_id between", value1, value2, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdNotBetween(Integer value1, Integer value2) {
            addCriterion("unit_id not between", value1, value2, "unitId");
            return (Criteria) this;
        }

        public Criteria andCreativeIdIsNull() {
            addCriterion("creative_id is null");
            return (Criteria) this;
        }

        public Criteria andCreativeIdIsNotNull() {
            addCriterion("creative_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreativeIdEqualTo(Integer value) {
            addCriterion("creative_id =", value, "creativeId");
            return (Criteria) this;
        }

        public Criteria andCreativeIdNotEqualTo(Integer value) {
            addCriterion("creative_id <>", value, "creativeId");
            return (Criteria) this;
        }

        public Criteria andCreativeIdGreaterThan(Integer value) {
            addCriterion("creative_id >", value, "creativeId");
            return (Criteria) this;
        }

        public Criteria andCreativeIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("creative_id >=", value, "creativeId");
            return (Criteria) this;
        }

        public Criteria andCreativeIdLessThan(Integer value) {
            addCriterion("creative_id <", value, "creativeId");
            return (Criteria) this;
        }

        public Criteria andCreativeIdLessThanOrEqualTo(Integer value) {
            addCriterion("creative_id <=", value, "creativeId");
            return (Criteria) this;
        }

        public Criteria andCreativeIdIn(List<Integer> values) {
            addCriterion("creative_id in", values, "creativeId");
            return (Criteria) this;
        }

        public Criteria andCreativeIdNotIn(List<Integer> values) {
            addCriterion("creative_id not in", values, "creativeId");
            return (Criteria) this;
        }

        public Criteria andCreativeIdBetween(Integer value1, Integer value2) {
            addCriterion("creative_id between", value1, value2, "creativeId");
            return (Criteria) this;
        }

        public Criteria andCreativeIdNotBetween(Integer value1, Integer value2) {
            addCriterion("creative_id not between", value1, value2, "creativeId");
            return (Criteria) this;
        }

        public Criteria andTemplateGroupIdIsNull() {
            addCriterion("template_group_id is null");
            return (Criteria) this;
        }

        public Criteria andTemplateGroupIdIsNotNull() {
            addCriterion("template_group_id is not null");
            return (Criteria) this;
        }

        public Criteria andTemplateGroupIdEqualTo(Integer value) {
            addCriterion("template_group_id =", value, "templateGroupId");
            return (Criteria) this;
        }

        public Criteria andTemplateGroupIdNotEqualTo(Integer value) {
            addCriterion("template_group_id <>", value, "templateGroupId");
            return (Criteria) this;
        }

        public Criteria andTemplateGroupIdGreaterThan(Integer value) {
            addCriterion("template_group_id >", value, "templateGroupId");
            return (Criteria) this;
        }

        public Criteria andTemplateGroupIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("template_group_id >=", value, "templateGroupId");
            return (Criteria) this;
        }

        public Criteria andTemplateGroupIdLessThan(Integer value) {
            addCriterion("template_group_id <", value, "templateGroupId");
            return (Criteria) this;
        }

        public Criteria andTemplateGroupIdLessThanOrEqualTo(Integer value) {
            addCriterion("template_group_id <=", value, "templateGroupId");
            return (Criteria) this;
        }

        public Criteria andTemplateGroupIdIn(List<Integer> values) {
            addCriterion("template_group_id in", values, "templateGroupId");
            return (Criteria) this;
        }

        public Criteria andTemplateGroupIdNotIn(List<Integer> values) {
            addCriterion("template_group_id not in", values, "templateGroupId");
            return (Criteria) this;
        }

        public Criteria andTemplateGroupIdBetween(Integer value1, Integer value2) {
            addCriterion("template_group_id between", value1, value2, "templateGroupId");
            return (Criteria) this;
        }

        public Criteria andTemplateGroupIdNotBetween(Integer value1, Integer value2) {
            addCriterion("template_group_id not between", value1, value2, "templateGroupId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdIsNull() {
            addCriterion("material_id is null");
            return (Criteria) this;
        }

        public Criteria andMaterialIdIsNotNull() {
            addCriterion("material_id is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialIdEqualTo(Long value) {
            addCriterion("material_id =", value, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdNotEqualTo(Long value) {
            addCriterion("material_id <>", value, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdGreaterThan(Long value) {
            addCriterion("material_id >", value, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdGreaterThanOrEqualTo(Long value) {
            addCriterion("material_id >=", value, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdLessThan(Long value) {
            addCriterion("material_id <", value, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdLessThanOrEqualTo(Long value) {
            addCriterion("material_id <=", value, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdIn(List<Long> values) {
            addCriterion("material_id in", values, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdNotIn(List<Long> values) {
            addCriterion("material_id not in", values, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdBetween(Long value1, Long value2) {
            addCriterion("material_id between", value1, value2, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdNotBetween(Long value1, Long value2) {
            addCriterion("material_id not between", value1, value2, "materialId");
            return (Criteria) this;
        }

        public Criteria andBizStatusIsNull() {
            addCriterion("biz_status is null");
            return (Criteria) this;
        }

        public Criteria andBizStatusIsNotNull() {
            addCriterion("biz_status is not null");
            return (Criteria) this;
        }

        public Criteria andBizStatusEqualTo(Integer value) {
            addCriterion("biz_status =", value, "bizStatus");
            return (Criteria) this;
        }

        public Criteria andBizStatusNotEqualTo(Integer value) {
            addCriterion("biz_status <>", value, "bizStatus");
            return (Criteria) this;
        }

        public Criteria andBizStatusGreaterThan(Integer value) {
            addCriterion("biz_status >", value, "bizStatus");
            return (Criteria) this;
        }

        public Criteria andBizStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("biz_status >=", value, "bizStatus");
            return (Criteria) this;
        }

        public Criteria andBizStatusLessThan(Integer value) {
            addCriterion("biz_status <", value, "bizStatus");
            return (Criteria) this;
        }

        public Criteria andBizStatusLessThanOrEqualTo(Integer value) {
            addCriterion("biz_status <=", value, "bizStatus");
            return (Criteria) this;
        }

        public Criteria andBizStatusIn(List<Integer> values) {
            addCriterion("biz_status in", values, "bizStatus");
            return (Criteria) this;
        }

        public Criteria andBizStatusNotIn(List<Integer> values) {
            addCriterion("biz_status not in", values, "bizStatus");
            return (Criteria) this;
        }

        public Criteria andBizStatusBetween(Integer value1, Integer value2) {
            addCriterion("biz_status between", value1, value2, "bizStatus");
            return (Criteria) this;
        }

        public Criteria andBizStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("biz_status not between", value1, value2, "bizStatus");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeIsNull() {
            addCriterion("material_type is null");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeIsNotNull() {
            addCriterion("material_type is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeEqualTo(Integer value) {
            addCriterion("material_type =", value, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeNotEqualTo(Integer value) {
            addCriterion("material_type <>", value, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeGreaterThan(Integer value) {
            addCriterion("material_type >", value, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("material_type >=", value, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeLessThan(Integer value) {
            addCriterion("material_type <", value, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeLessThanOrEqualTo(Integer value) {
            addCriterion("material_type <=", value, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeIn(List<Integer> values) {
            addCriterion("material_type in", values, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeNotIn(List<Integer> values) {
            addCriterion("material_type not in", values, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeBetween(Integer value1, Integer value2) {
            addCriterion("material_type between", value1, value2, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("material_type not between", value1, value2, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialMd5IsNull() {
            addCriterion("material_md5 is null");
            return (Criteria) this;
        }

        public Criteria andMaterialMd5IsNotNull() {
            addCriterion("material_md5 is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialMd5EqualTo(String value) {
            addCriterion("material_md5 =", value, "materialMd5");
            return (Criteria) this;
        }

        public Criteria andMaterialMd5NotEqualTo(String value) {
            addCriterion("material_md5 <>", value, "materialMd5");
            return (Criteria) this;
        }

        public Criteria andMaterialMd5GreaterThan(String value) {
            addCriterion("material_md5 >", value, "materialMd5");
            return (Criteria) this;
        }

        public Criteria andMaterialMd5GreaterThanOrEqualTo(String value) {
            addCriterion("material_md5 >=", value, "materialMd5");
            return (Criteria) this;
        }

        public Criteria andMaterialMd5LessThan(String value) {
            addCriterion("material_md5 <", value, "materialMd5");
            return (Criteria) this;
        }

        public Criteria andMaterialMd5LessThanOrEqualTo(String value) {
            addCriterion("material_md5 <=", value, "materialMd5");
            return (Criteria) this;
        }

        public Criteria andMaterialMd5Like(String value) {
            addCriterion("material_md5 like", value, "materialMd5");
            return (Criteria) this;
        }

        public Criteria andMaterialMd5NotLike(String value) {
            addCriterion("material_md5 not like", value, "materialMd5");
            return (Criteria) this;
        }

        public Criteria andMaterialMd5In(List<String> values) {
            addCriterion("material_md5 in", values, "materialMd5");
            return (Criteria) this;
        }

        public Criteria andMaterialMd5NotIn(List<String> values) {
            addCriterion("material_md5 not in", values, "materialMd5");
            return (Criteria) this;
        }

        public Criteria andMaterialMd5Between(String value1, String value2) {
            addCriterion("material_md5 between", value1, value2, "materialMd5");
            return (Criteria) this;
        }

        public Criteria andMaterialMd5NotBetween(String value1, String value2) {
            addCriterion("material_md5 not between", value1, value2, "materialMd5");
            return (Criteria) this;
        }

        public Criteria andRejectedReasonIsNull() {
            addCriterion("rejected_reason is null");
            return (Criteria) this;
        }

        public Criteria andRejectedReasonIsNotNull() {
            addCriterion("rejected_reason is not null");
            return (Criteria) this;
        }

        public Criteria andRejectedReasonEqualTo(String value) {
            addCriterion("rejected_reason =", value, "rejectedReason");
            return (Criteria) this;
        }

        public Criteria andRejectedReasonNotEqualTo(String value) {
            addCriterion("rejected_reason <>", value, "rejectedReason");
            return (Criteria) this;
        }

        public Criteria andRejectedReasonGreaterThan(String value) {
            addCriterion("rejected_reason >", value, "rejectedReason");
            return (Criteria) this;
        }

        public Criteria andRejectedReasonGreaterThanOrEqualTo(String value) {
            addCriterion("rejected_reason >=", value, "rejectedReason");
            return (Criteria) this;
        }

        public Criteria andRejectedReasonLessThan(String value) {
            addCriterion("rejected_reason <", value, "rejectedReason");
            return (Criteria) this;
        }

        public Criteria andRejectedReasonLessThanOrEqualTo(String value) {
            addCriterion("rejected_reason <=", value, "rejectedReason");
            return (Criteria) this;
        }

        public Criteria andRejectedReasonLike(String value) {
            addCriterion("rejected_reason like", value, "rejectedReason");
            return (Criteria) this;
        }

        public Criteria andRejectedReasonNotLike(String value) {
            addCriterion("rejected_reason not like", value, "rejectedReason");
            return (Criteria) this;
        }

        public Criteria andRejectedReasonIn(List<String> values) {
            addCriterion("rejected_reason in", values, "rejectedReason");
            return (Criteria) this;
        }

        public Criteria andRejectedReasonNotIn(List<String> values) {
            addCriterion("rejected_reason not in", values, "rejectedReason");
            return (Criteria) this;
        }

        public Criteria andRejectedReasonBetween(String value1, String value2) {
            addCriterion("rejected_reason between", value1, value2, "rejectedReason");
            return (Criteria) this;
        }

        public Criteria andRejectedReasonNotBetween(String value1, String value2) {
            addCriterion("rejected_reason not between", value1, value2, "rejectedReason");
            return (Criteria) this;
        }

        public Criteria andMgkTemplateIdIsNull() {
            addCriterion("mgk_template_id is null");
            return (Criteria) this;
        }

        public Criteria andMgkTemplateIdIsNotNull() {
            addCriterion("mgk_template_id is not null");
            return (Criteria) this;
        }

        public Criteria andMgkTemplateIdEqualTo(Integer value) {
            addCriterion("mgk_template_id =", value, "mgkTemplateId");
            return (Criteria) this;
        }

        public Criteria andMgkTemplateIdNotEqualTo(Integer value) {
            addCriterion("mgk_template_id <>", value, "mgkTemplateId");
            return (Criteria) this;
        }

        public Criteria andMgkTemplateIdGreaterThan(Integer value) {
            addCriterion("mgk_template_id >", value, "mgkTemplateId");
            return (Criteria) this;
        }

        public Criteria andMgkTemplateIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("mgk_template_id >=", value, "mgkTemplateId");
            return (Criteria) this;
        }

        public Criteria andMgkTemplateIdLessThan(Integer value) {
            addCriterion("mgk_template_id <", value, "mgkTemplateId");
            return (Criteria) this;
        }

        public Criteria andMgkTemplateIdLessThanOrEqualTo(Integer value) {
            addCriterion("mgk_template_id <=", value, "mgkTemplateId");
            return (Criteria) this;
        }

        public Criteria andMgkTemplateIdIn(List<Integer> values) {
            addCriterion("mgk_template_id in", values, "mgkTemplateId");
            return (Criteria) this;
        }

        public Criteria andMgkTemplateIdNotIn(List<Integer> values) {
            addCriterion("mgk_template_id not in", values, "mgkTemplateId");
            return (Criteria) this;
        }

        public Criteria andMgkTemplateIdBetween(Integer value1, Integer value2) {
            addCriterion("mgk_template_id between", value1, value2, "mgkTemplateId");
            return (Criteria) this;
        }

        public Criteria andMgkTemplateIdNotBetween(Integer value1, Integer value2) {
            addCriterion("mgk_template_id not between", value1, value2, "mgkTemplateId");
            return (Criteria) this;
        }

        public Criteria andMgkMediaIdIsNull() {
            addCriterion("mgk_media_id is null");
            return (Criteria) this;
        }

        public Criteria andMgkMediaIdIsNotNull() {
            addCriterion("mgk_media_id is not null");
            return (Criteria) this;
        }

        public Criteria andMgkMediaIdEqualTo(Long value) {
            addCriterion("mgk_media_id =", value, "mgkMediaId");
            return (Criteria) this;
        }

        public Criteria andMgkMediaIdNotEqualTo(Long value) {
            addCriterion("mgk_media_id <>", value, "mgkMediaId");
            return (Criteria) this;
        }

        public Criteria andMgkMediaIdGreaterThan(Long value) {
            addCriterion("mgk_media_id >", value, "mgkMediaId");
            return (Criteria) this;
        }

        public Criteria andMgkMediaIdGreaterThanOrEqualTo(Long value) {
            addCriterion("mgk_media_id >=", value, "mgkMediaId");
            return (Criteria) this;
        }

        public Criteria andMgkMediaIdLessThan(Long value) {
            addCriterion("mgk_media_id <", value, "mgkMediaId");
            return (Criteria) this;
        }

        public Criteria andMgkMediaIdLessThanOrEqualTo(Long value) {
            addCriterion("mgk_media_id <=", value, "mgkMediaId");
            return (Criteria) this;
        }

        public Criteria andMgkMediaIdIn(List<Long> values) {
            addCriterion("mgk_media_id in", values, "mgkMediaId");
            return (Criteria) this;
        }

        public Criteria andMgkMediaIdNotIn(List<Long> values) {
            addCriterion("mgk_media_id not in", values, "mgkMediaId");
            return (Criteria) this;
        }

        public Criteria andMgkMediaIdBetween(Long value1, Long value2) {
            addCriterion("mgk_media_id between", value1, value2, "mgkMediaId");
            return (Criteria) this;
        }

        public Criteria andMgkMediaIdNotBetween(Long value1, Long value2) {
            addCriterion("mgk_media_id not between", value1, value2, "mgkMediaId");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}