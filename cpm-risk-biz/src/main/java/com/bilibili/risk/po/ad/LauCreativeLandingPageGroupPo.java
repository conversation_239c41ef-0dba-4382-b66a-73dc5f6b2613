package com.bilibili.risk.po.ad;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LauCreativeLandingPageGroupPo implements Serializable {
    /**
     * 自增id
     */
    private Long id;

    /**
     * 添加时间
     */
    private Timestamp ctime;

    /**
     * 修改时间
     */
    private Timestamp mtime;

    /**
     * 软删除
     */
    private Integer isDeleted;

    /**
     * 账号id
     */
    private Integer accountId;

    /**
     * 创意id
     */
    private Integer creativeId;

    /**
     * 落地页组id
     */
    private Long groupId;

    /**
     * 是否支持创意联投 0-不支持 1-支持
     */
    private Integer canJointLaunch;

    /**
     * 落地页组来源: 0-建站, 1-三方
     */
    private Integer groupSource;

    private static final long serialVersionUID = 1L;
}