package com.bilibili.risk.po.ad;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LauProgrammaticCreativeDetailPo implements Serializable {
    /**
     * 自增id
     */
    private Long id;

    /**
     * 添加时间
     */
    private Timestamp ctime;

    /**
     * 修改时间
     */
    private Timestamp mtime;

    /**
     * 账号ID
     */
    private Integer accountId;

    /**
     * 计划ID
     */
    private Integer campaignId;

    /**
     * 单元ID
     */
    private Integer unitId;

    /**
     * 创意ID
     */
    private Integer creativeId;

    /**
     * 模板组ID
     */
    private Integer templateGroupId;

    /**
     * 物料ID
     */
    private Long materialId;

    /**
     * 业务状态: 0-审核通过, 1-待审核, 2-审核驳回
     */
    private Integer bizStatus;

    /**
     * 物料类型, 0 - 未知, 1 - 图片, 2 - 视频, 3 - G<PERSON>, 4, 标题, 5 - 三图, 6 - 视频和封面, 7-稿件
     */
    private Integer materialType;

    /**
     * 物料内容的MD5值, 用来判断物料是否相同
     */
    private String materialMd5;

    /**
     * 审核拒绝原因
     */
    private String rejectedReason;

    /**
     * 创意中心模板id
     */
    private Integer mgkTemplateId;

    /**
     * 创意中心媒体id
     */
    private Long mgkMediaId;

    private static final long serialVersionUID = 1L;
}