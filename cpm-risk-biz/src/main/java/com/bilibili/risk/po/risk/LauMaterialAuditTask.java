package com.bilibili.risk.po.risk;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR> 素材审核任务表
 */
@Data
public class LauMaterialAuditTask implements Serializable {
    /**
     * 自增主键ID
     */
    private Long id;

    /**
     * 任务 id
     */
    private String taskId;

    /**
     * 素材id
     */
    private String materialId;

    /**
     * 素材md5
     */
    private String materialMd5;

    /**
     * 素材类型
     */
    private Byte materialType;

    /**
     * 素材内容
     */
    private String materialContent;

    /**
     * 领取批次号
     */
    private String receiveBatchNo;

    /**
     * 创意id
     */
    private Integer creativeId;

    /**
     * 业务类型1三连2必火
     */
    private Integer bizType;

    /**
     * 队列id
     */
    private Long queueId;

    /**
     * 创意审核状态:1待审2通过3驳回
     */
    private Integer creativeAuditStatus;

    /**
     * 任务状态:0游离1执行中2完成3stop
     */
    private Integer status;

    /**
     * 账户id
     */
    private Integer accountId;

    /**
     * version
     */
    private Integer version;

    /**
     * 领取时间
     */
    private Date acceptTime;

    /**
     * 领取人
     */
    private String acceptName;

    /**
     * 执行时间
     */
    private Date executeTime;

    /**
     * 执行人
     */
    private String executeName;

    /**
     * 审核三级标签ids
     */
    private String auditLabelThirdId;

    /**
     * 统一一级行业标签
     */
    private Integer unitedFirstIndustryId;

    /**
     * 统一二级行业标签
     */
    private Integer unitedSecondIndustryId;

    /**
     * 统一三级行业标签
     */
    private Integer unitedThirdIndustryId;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 更新时间
     */
    private Date mtime;

    /**
     * 扩展字段
     */
    private String extra;

    private static final long serialVersionUID = 1L;
}