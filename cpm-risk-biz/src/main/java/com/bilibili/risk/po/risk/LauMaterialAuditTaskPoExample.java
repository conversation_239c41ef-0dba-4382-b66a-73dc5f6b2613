package com.bilibili.risk.po.risk;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

public class LauMaterialAuditTaskPoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public LauMaterialAuditTaskPoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andTaskIdIsNull() {
            addCriterion("task_id is null");
            return (Criteria) this;
        }

        public Criteria andTaskIdIsNotNull() {
            addCriterion("task_id is not null");
            return (Criteria) this;
        }

        public Criteria andTaskIdEqualTo(String value) {
            addCriterion("task_id =", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotEqualTo(String value) {
            addCriterion("task_id <>", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdGreaterThan(String value) {
            addCriterion("task_id >", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdGreaterThanOrEqualTo(String value) {
            addCriterion("task_id >=", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdLessThan(String value) {
            addCriterion("task_id <", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdLessThanOrEqualTo(String value) {
            addCriterion("task_id <=", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdLike(String value) {
            addCriterion("task_id like", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotLike(String value) {
            addCriterion("task_id not like", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdIn(List<String> values) {
            addCriterion("task_id in", values, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotIn(List<String> values) {
            addCriterion("task_id not in", values, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdBetween(String value1, String value2) {
            addCriterion("task_id between", value1, value2, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotBetween(String value1, String value2) {
            addCriterion("task_id not between", value1, value2, "taskId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdIsNull() {
            addCriterion("material_id is null");
            return (Criteria) this;
        }

        public Criteria andMaterialIdIsNotNull() {
            addCriterion("material_id is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialIdEqualTo(String value) {
            addCriterion("material_id =", value, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdNotEqualTo(String value) {
            addCriterion("material_id <>", value, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdGreaterThan(String value) {
            addCriterion("material_id >", value, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdGreaterThanOrEqualTo(String value) {
            addCriterion("material_id >=", value, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdLessThan(String value) {
            addCriterion("material_id <", value, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdLessThanOrEqualTo(String value) {
            addCriterion("material_id <=", value, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdLike(String value) {
            addCriterion("material_id like", value, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdNotLike(String value) {
            addCriterion("material_id not like", value, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdIn(List<String> values) {
            addCriterion("material_id in", values, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdNotIn(List<String> values) {
            addCriterion("material_id not in", values, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdBetween(String value1, String value2) {
            addCriterion("material_id between", value1, value2, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdNotBetween(String value1, String value2) {
            addCriterion("material_id not between", value1, value2, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialMd5IsNull() {
            addCriterion("material_md5 is null");
            return (Criteria) this;
        }

        public Criteria andMaterialMd5IsNotNull() {
            addCriterion("material_md5 is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialMd5EqualTo(String value) {
            addCriterion("material_md5 =", value, "materialMd5");
            return (Criteria) this;
        }

        public Criteria andMaterialMd5NotEqualTo(String value) {
            addCriterion("material_md5 <>", value, "materialMd5");
            return (Criteria) this;
        }

        public Criteria andMaterialMd5GreaterThan(String value) {
            addCriterion("material_md5 >", value, "materialMd5");
            return (Criteria) this;
        }

        public Criteria andMaterialMd5GreaterThanOrEqualTo(String value) {
            addCriterion("material_md5 >=", value, "materialMd5");
            return (Criteria) this;
        }

        public Criteria andMaterialMd5LessThan(String value) {
            addCriterion("material_md5 <", value, "materialMd5");
            return (Criteria) this;
        }

        public Criteria andMaterialMd5LessThanOrEqualTo(String value) {
            addCriterion("material_md5 <=", value, "materialMd5");
            return (Criteria) this;
        }

        public Criteria andMaterialMd5Like(String value) {
            addCriterion("material_md5 like", value, "materialMd5");
            return (Criteria) this;
        }

        public Criteria andMaterialMd5NotLike(String value) {
            addCriterion("material_md5 not like", value, "materialMd5");
            return (Criteria) this;
        }

        public Criteria andMaterialMd5In(List<String> values) {
            addCriterion("material_md5 in", values, "materialMd5");
            return (Criteria) this;
        }

        public Criteria andMaterialMd5NotIn(List<String> values) {
            addCriterion("material_md5 not in", values, "materialMd5");
            return (Criteria) this;
        }

        public Criteria andMaterialMd5Between(String value1, String value2) {
            addCriterion("material_md5 between", value1, value2, "materialMd5");
            return (Criteria) this;
        }

        public Criteria andMaterialMd5NotBetween(String value1, String value2) {
            addCriterion("material_md5 not between", value1, value2, "materialMd5");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeIsNull() {
            addCriterion("material_type is null");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeIsNotNull() {
            addCriterion("material_type is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeEqualTo(Integer value) {
            addCriterion("material_type =", value, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeNotEqualTo(Integer value) {
            addCriterion("material_type <>", value, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeGreaterThan(Integer value) {
            addCriterion("material_type >", value, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("material_type >=", value, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeLessThan(Integer value) {
            addCriterion("material_type <", value, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeLessThanOrEqualTo(Integer value) {
            addCriterion("material_type <=", value, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeIn(List<Integer> values) {
            addCriterion("material_type in", values, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeNotIn(List<Integer> values) {
            addCriterion("material_type not in", values, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeBetween(Integer value1, Integer value2) {
            addCriterion("material_type between", value1, value2, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("material_type not between", value1, value2, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialContentIsNull() {
            addCriterion("material_content is null");
            return (Criteria) this;
        }

        public Criteria andMaterialContentIsNotNull() {
            addCriterion("material_content is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialContentEqualTo(String value) {
            addCriterion("material_content =", value, "materialContent");
            return (Criteria) this;
        }

        public Criteria andMaterialContentNotEqualTo(String value) {
            addCriterion("material_content <>", value, "materialContent");
            return (Criteria) this;
        }

        public Criteria andMaterialContentGreaterThan(String value) {
            addCriterion("material_content >", value, "materialContent");
            return (Criteria) this;
        }

        public Criteria andMaterialContentGreaterThanOrEqualTo(String value) {
            addCriterion("material_content >=", value, "materialContent");
            return (Criteria) this;
        }

        public Criteria andMaterialContentLessThan(String value) {
            addCriterion("material_content <", value, "materialContent");
            return (Criteria) this;
        }

        public Criteria andMaterialContentLessThanOrEqualTo(String value) {
            addCriterion("material_content <=", value, "materialContent");
            return (Criteria) this;
        }

        public Criteria andMaterialContentLike(String value) {
            addCriterion("material_content like", value, "materialContent");
            return (Criteria) this;
        }

        public Criteria andMaterialContentNotLike(String value) {
            addCriterion("material_content not like", value, "materialContent");
            return (Criteria) this;
        }

        public Criteria andMaterialContentIn(List<String> values) {
            addCriterion("material_content in", values, "materialContent");
            return (Criteria) this;
        }

        public Criteria andMaterialContentNotIn(List<String> values) {
            addCriterion("material_content not in", values, "materialContent");
            return (Criteria) this;
        }

        public Criteria andMaterialContentBetween(String value1, String value2) {
            addCriterion("material_content between", value1, value2, "materialContent");
            return (Criteria) this;
        }

        public Criteria andMaterialContentNotBetween(String value1, String value2) {
            addCriterion("material_content not between", value1, value2, "materialContent");
            return (Criteria) this;
        }

        public Criteria andReceiveBatchNoIsNull() {
            addCriterion("receive_batch_no is null");
            return (Criteria) this;
        }

        public Criteria andReceiveBatchNoIsNotNull() {
            addCriterion("receive_batch_no is not null");
            return (Criteria) this;
        }

        public Criteria andReceiveBatchNoEqualTo(String value) {
            addCriterion("receive_batch_no =", value, "receiveBatchNo");
            return (Criteria) this;
        }

        public Criteria andReceiveBatchNoNotEqualTo(String value) {
            addCriterion("receive_batch_no <>", value, "receiveBatchNo");
            return (Criteria) this;
        }

        public Criteria andReceiveBatchNoGreaterThan(String value) {
            addCriterion("receive_batch_no >", value, "receiveBatchNo");
            return (Criteria) this;
        }

        public Criteria andReceiveBatchNoGreaterThanOrEqualTo(String value) {
            addCriterion("receive_batch_no >=", value, "receiveBatchNo");
            return (Criteria) this;
        }

        public Criteria andReceiveBatchNoLessThan(String value) {
            addCriterion("receive_batch_no <", value, "receiveBatchNo");
            return (Criteria) this;
        }

        public Criteria andReceiveBatchNoLessThanOrEqualTo(String value) {
            addCriterion("receive_batch_no <=", value, "receiveBatchNo");
            return (Criteria) this;
        }

        public Criteria andReceiveBatchNoLike(String value) {
            addCriterion("receive_batch_no like", value, "receiveBatchNo");
            return (Criteria) this;
        }

        public Criteria andReceiveBatchNoNotLike(String value) {
            addCriterion("receive_batch_no not like", value, "receiveBatchNo");
            return (Criteria) this;
        }

        public Criteria andReceiveBatchNoIn(List<String> values) {
            addCriterion("receive_batch_no in", values, "receiveBatchNo");
            return (Criteria) this;
        }

        public Criteria andReceiveBatchNoNotIn(List<String> values) {
            addCriterion("receive_batch_no not in", values, "receiveBatchNo");
            return (Criteria) this;
        }

        public Criteria andReceiveBatchNoBetween(String value1, String value2) {
            addCriterion("receive_batch_no between", value1, value2, "receiveBatchNo");
            return (Criteria) this;
        }

        public Criteria andReceiveBatchNoNotBetween(String value1, String value2) {
            addCriterion("receive_batch_no not between", value1, value2, "receiveBatchNo");
            return (Criteria) this;
        }

        public Criteria andCreativeIdIsNull() {
            addCriterion("creative_id is null");
            return (Criteria) this;
        }

        public Criteria andCreativeIdIsNotNull() {
            addCriterion("creative_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreativeIdEqualTo(Integer value) {
            addCriterion("creative_id =", value, "creativeId");
            return (Criteria) this;
        }

        public Criteria andCreativeIdNotEqualTo(Integer value) {
            addCriterion("creative_id <>", value, "creativeId");
            return (Criteria) this;
        }

        public Criteria andCreativeIdGreaterThan(Integer value) {
            addCriterion("creative_id >", value, "creativeId");
            return (Criteria) this;
        }

        public Criteria andCreativeIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("creative_id >=", value, "creativeId");
            return (Criteria) this;
        }

        public Criteria andCreativeIdLessThan(Integer value) {
            addCriterion("creative_id <", value, "creativeId");
            return (Criteria) this;
        }

        public Criteria andCreativeIdLessThanOrEqualTo(Integer value) {
            addCriterion("creative_id <=", value, "creativeId");
            return (Criteria) this;
        }

        public Criteria andCreativeIdIn(List<Integer> values) {
            addCriterion("creative_id in", values, "creativeId");
            return (Criteria) this;
        }

        public Criteria andCreativeIdNotIn(List<Integer> values) {
            addCriterion("creative_id not in", values, "creativeId");
            return (Criteria) this;
        }

        public Criteria andCreativeIdBetween(Integer value1, Integer value2) {
            addCriterion("creative_id between", value1, value2, "creativeId");
            return (Criteria) this;
        }

        public Criteria andCreativeIdNotBetween(Integer value1, Integer value2) {
            addCriterion("creative_id not between", value1, value2, "creativeId");
            return (Criteria) this;
        }

        public Criteria andBizTypeIsNull() {
            addCriterion("biz_type is null");
            return (Criteria) this;
        }

        public Criteria andBizTypeIsNotNull() {
            addCriterion("biz_type is not null");
            return (Criteria) this;
        }

        public Criteria andBizTypeEqualTo(Integer value) {
            addCriterion("biz_type =", value, "bizType");
            return (Criteria) this;
        }

        public Criteria andBizTypeNotEqualTo(Integer value) {
            addCriterion("biz_type <>", value, "bizType");
            return (Criteria) this;
        }

        public Criteria andBizTypeGreaterThan(Integer value) {
            addCriterion("biz_type >", value, "bizType");
            return (Criteria) this;
        }

        public Criteria andBizTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("biz_type >=", value, "bizType");
            return (Criteria) this;
        }

        public Criteria andBizTypeLessThan(Integer value) {
            addCriterion("biz_type <", value, "bizType");
            return (Criteria) this;
        }

        public Criteria andBizTypeLessThanOrEqualTo(Integer value) {
            addCriterion("biz_type <=", value, "bizType");
            return (Criteria) this;
        }

        public Criteria andBizTypeIn(List<Integer> values) {
            addCriterion("biz_type in", values, "bizType");
            return (Criteria) this;
        }

        public Criteria andBizTypeNotIn(List<Integer> values) {
            addCriterion("biz_type not in", values, "bizType");
            return (Criteria) this;
        }

        public Criteria andBizTypeBetween(Integer value1, Integer value2) {
            addCriterion("biz_type between", value1, value2, "bizType");
            return (Criteria) this;
        }

        public Criteria andBizTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("biz_type not between", value1, value2, "bizType");
            return (Criteria) this;
        }

        public Criteria andQueueIdIsNull() {
            addCriterion("queue_id is null");
            return (Criteria) this;
        }

        public Criteria andQueueIdIsNotNull() {
            addCriterion("queue_id is not null");
            return (Criteria) this;
        }

        public Criteria andQueueIdEqualTo(Long value) {
            addCriterion("queue_id =", value, "queueId");
            return (Criteria) this;
        }

        public Criteria andQueueIdNotEqualTo(Long value) {
            addCriterion("queue_id <>", value, "queueId");
            return (Criteria) this;
        }

        public Criteria andQueueIdGreaterThan(Long value) {
            addCriterion("queue_id >", value, "queueId");
            return (Criteria) this;
        }

        public Criteria andQueueIdGreaterThanOrEqualTo(Long value) {
            addCriterion("queue_id >=", value, "queueId");
            return (Criteria) this;
        }

        public Criteria andQueueIdLessThan(Long value) {
            addCriterion("queue_id <", value, "queueId");
            return (Criteria) this;
        }

        public Criteria andQueueIdLessThanOrEqualTo(Long value) {
            addCriterion("queue_id <=", value, "queueId");
            return (Criteria) this;
        }

        public Criteria andQueueIdIn(List<Long> values) {
            addCriterion("queue_id in", values, "queueId");
            return (Criteria) this;
        }

        public Criteria andQueueIdNotIn(List<Long> values) {
            addCriterion("queue_id not in", values, "queueId");
            return (Criteria) this;
        }

        public Criteria andQueueIdBetween(Long value1, Long value2) {
            addCriterion("queue_id between", value1, value2, "queueId");
            return (Criteria) this;
        }

        public Criteria andQueueIdNotBetween(Long value1, Long value2) {
            addCriterion("queue_id not between", value1, value2, "queueId");
            return (Criteria) this;
        }

        public Criteria andCreativeAuditStatusIsNull() {
            addCriterion("creative_audit_status is null");
            return (Criteria) this;
        }

        public Criteria andCreativeAuditStatusIsNotNull() {
            addCriterion("creative_audit_status is not null");
            return (Criteria) this;
        }

        public Criteria andCreativeAuditStatusEqualTo(Integer value) {
            addCriterion("creative_audit_status =", value, "creativeAuditStatus");
            return (Criteria) this;
        }

        public Criteria andCreativeAuditStatusNotEqualTo(Integer value) {
            addCriterion("creative_audit_status <>", value, "creativeAuditStatus");
            return (Criteria) this;
        }

        public Criteria andCreativeAuditStatusGreaterThan(Integer value) {
            addCriterion("creative_audit_status >", value, "creativeAuditStatus");
            return (Criteria) this;
        }

        public Criteria andCreativeAuditStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("creative_audit_status >=", value, "creativeAuditStatus");
            return (Criteria) this;
        }

        public Criteria andCreativeAuditStatusLessThan(Integer value) {
            addCriterion("creative_audit_status <", value, "creativeAuditStatus");
            return (Criteria) this;
        }

        public Criteria andCreativeAuditStatusLessThanOrEqualTo(Integer value) {
            addCriterion("creative_audit_status <=", value, "creativeAuditStatus");
            return (Criteria) this;
        }

        public Criteria andCreativeAuditStatusIn(List<Integer> values) {
            addCriterion("creative_audit_status in", values, "creativeAuditStatus");
            return (Criteria) this;
        }

        public Criteria andCreativeAuditStatusNotIn(List<Integer> values) {
            addCriterion("creative_audit_status not in", values, "creativeAuditStatus");
            return (Criteria) this;
        }

        public Criteria andCreativeAuditStatusBetween(Integer value1, Integer value2) {
            addCriterion("creative_audit_status between", value1, value2, "creativeAuditStatus");
            return (Criteria) this;
        }

        public Criteria andCreativeAuditStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("creative_audit_status not between", value1, value2, "creativeAuditStatus");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andAccountIdIsNull() {
            addCriterion("account_id is null");
            return (Criteria) this;
        }

        public Criteria andAccountIdIsNotNull() {
            addCriterion("account_id is not null");
            return (Criteria) this;
        }

        public Criteria andAccountIdEqualTo(Integer value) {
            addCriterion("account_id =", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotEqualTo(Integer value) {
            addCriterion("account_id <>", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdGreaterThan(Integer value) {
            addCriterion("account_id >", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("account_id >=", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdLessThan(Integer value) {
            addCriterion("account_id <", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdLessThanOrEqualTo(Integer value) {
            addCriterion("account_id <=", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdIn(List<Integer> values) {
            addCriterion("account_id in", values, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotIn(List<Integer> values) {
            addCriterion("account_id not in", values, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdBetween(Integer value1, Integer value2) {
            addCriterion("account_id between", value1, value2, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotBetween(Integer value1, Integer value2) {
            addCriterion("account_id not between", value1, value2, "accountId");
            return (Criteria) this;
        }

        public Criteria andVersionIsNull() {
            addCriterion("version is null");
            return (Criteria) this;
        }

        public Criteria andVersionIsNotNull() {
            addCriterion("version is not null");
            return (Criteria) this;
        }

        public Criteria andVersionEqualTo(Integer value) {
            addCriterion("version =", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotEqualTo(Integer value) {
            addCriterion("version <>", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThan(Integer value) {
            addCriterion("version >", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThanOrEqualTo(Integer value) {
            addCriterion("version >=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThan(Integer value) {
            addCriterion("version <", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThanOrEqualTo(Integer value) {
            addCriterion("version <=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionIn(List<Integer> values) {
            addCriterion("version in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotIn(List<Integer> values) {
            addCriterion("version not in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionBetween(Integer value1, Integer value2) {
            addCriterion("version between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotBetween(Integer value1, Integer value2) {
            addCriterion("version not between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andAcceptTimeIsNull() {
            addCriterion("accept_time is null");
            return (Criteria) this;
        }

        public Criteria andAcceptTimeIsNotNull() {
            addCriterion("accept_time is not null");
            return (Criteria) this;
        }

        public Criteria andAcceptTimeEqualTo(Timestamp value) {
            addCriterion("accept_time =", value, "acceptTime");
            return (Criteria) this;
        }

        public Criteria andAcceptTimeNotEqualTo(Timestamp value) {
            addCriterion("accept_time <>", value, "acceptTime");
            return (Criteria) this;
        }

        public Criteria andAcceptTimeGreaterThan(Timestamp value) {
            addCriterion("accept_time >", value, "acceptTime");
            return (Criteria) this;
        }

        public Criteria andAcceptTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("accept_time >=", value, "acceptTime");
            return (Criteria) this;
        }

        public Criteria andAcceptTimeLessThan(Timestamp value) {
            addCriterion("accept_time <", value, "acceptTime");
            return (Criteria) this;
        }

        public Criteria andAcceptTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("accept_time <=", value, "acceptTime");
            return (Criteria) this;
        }

        public Criteria andAcceptTimeIn(List<Timestamp> values) {
            addCriterion("accept_time in", values, "acceptTime");
            return (Criteria) this;
        }

        public Criteria andAcceptTimeNotIn(List<Timestamp> values) {
            addCriterion("accept_time not in", values, "acceptTime");
            return (Criteria) this;
        }

        public Criteria andAcceptTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("accept_time between", value1, value2, "acceptTime");
            return (Criteria) this;
        }

        public Criteria andAcceptTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("accept_time not between", value1, value2, "acceptTime");
            return (Criteria) this;
        }

        public Criteria andAcceptNameIsNull() {
            addCriterion("accept_name is null");
            return (Criteria) this;
        }

        public Criteria andAcceptNameIsNotNull() {
            addCriterion("accept_name is not null");
            return (Criteria) this;
        }

        public Criteria andAcceptNameEqualTo(String value) {
            addCriterion("accept_name =", value, "acceptName");
            return (Criteria) this;
        }

        public Criteria andAcceptNameNotEqualTo(String value) {
            addCriterion("accept_name <>", value, "acceptName");
            return (Criteria) this;
        }

        public Criteria andAcceptNameGreaterThan(String value) {
            addCriterion("accept_name >", value, "acceptName");
            return (Criteria) this;
        }

        public Criteria andAcceptNameGreaterThanOrEqualTo(String value) {
            addCriterion("accept_name >=", value, "acceptName");
            return (Criteria) this;
        }

        public Criteria andAcceptNameLessThan(String value) {
            addCriterion("accept_name <", value, "acceptName");
            return (Criteria) this;
        }

        public Criteria andAcceptNameLessThanOrEqualTo(String value) {
            addCriterion("accept_name <=", value, "acceptName");
            return (Criteria) this;
        }

        public Criteria andAcceptNameLike(String value) {
            addCriterion("accept_name like", value, "acceptName");
            return (Criteria) this;
        }

        public Criteria andAcceptNameNotLike(String value) {
            addCriterion("accept_name not like", value, "acceptName");
            return (Criteria) this;
        }

        public Criteria andAcceptNameIn(List<String> values) {
            addCriterion("accept_name in", values, "acceptName");
            return (Criteria) this;
        }

        public Criteria andAcceptNameNotIn(List<String> values) {
            addCriterion("accept_name not in", values, "acceptName");
            return (Criteria) this;
        }

        public Criteria andAcceptNameBetween(String value1, String value2) {
            addCriterion("accept_name between", value1, value2, "acceptName");
            return (Criteria) this;
        }

        public Criteria andAcceptNameNotBetween(String value1, String value2) {
            addCriterion("accept_name not between", value1, value2, "acceptName");
            return (Criteria) this;
        }

        public Criteria andExecuteTimeIsNull() {
            addCriterion("execute_time is null");
            return (Criteria) this;
        }

        public Criteria andExecuteTimeIsNotNull() {
            addCriterion("execute_time is not null");
            return (Criteria) this;
        }

        public Criteria andExecuteTimeEqualTo(Timestamp value) {
            addCriterion("execute_time =", value, "executeTime");
            return (Criteria) this;
        }

        public Criteria andExecuteTimeNotEqualTo(Timestamp value) {
            addCriterion("execute_time <>", value, "executeTime");
            return (Criteria) this;
        }

        public Criteria andExecuteTimeGreaterThan(Timestamp value) {
            addCriterion("execute_time >", value, "executeTime");
            return (Criteria) this;
        }

        public Criteria andExecuteTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("execute_time >=", value, "executeTime");
            return (Criteria) this;
        }

        public Criteria andExecuteTimeLessThan(Timestamp value) {
            addCriterion("execute_time <", value, "executeTime");
            return (Criteria) this;
        }

        public Criteria andExecuteTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("execute_time <=", value, "executeTime");
            return (Criteria) this;
        }

        public Criteria andExecuteTimeIn(List<Timestamp> values) {
            addCriterion("execute_time in", values, "executeTime");
            return (Criteria) this;
        }

        public Criteria andExecuteTimeNotIn(List<Timestamp> values) {
            addCriterion("execute_time not in", values, "executeTime");
            return (Criteria) this;
        }

        public Criteria andExecuteTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("execute_time between", value1, value2, "executeTime");
            return (Criteria) this;
        }

        public Criteria andExecuteTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("execute_time not between", value1, value2, "executeTime");
            return (Criteria) this;
        }

        public Criteria andExecuteNameIsNull() {
            addCriterion("execute_name is null");
            return (Criteria) this;
        }

        public Criteria andExecuteNameIsNotNull() {
            addCriterion("execute_name is not null");
            return (Criteria) this;
        }

        public Criteria andExecuteNameEqualTo(String value) {
            addCriterion("execute_name =", value, "executeName");
            return (Criteria) this;
        }

        public Criteria andExecuteNameNotEqualTo(String value) {
            addCriterion("execute_name <>", value, "executeName");
            return (Criteria) this;
        }

        public Criteria andExecuteNameGreaterThan(String value) {
            addCriterion("execute_name >", value, "executeName");
            return (Criteria) this;
        }

        public Criteria andExecuteNameGreaterThanOrEqualTo(String value) {
            addCriterion("execute_name >=", value, "executeName");
            return (Criteria) this;
        }

        public Criteria andExecuteNameLessThan(String value) {
            addCriterion("execute_name <", value, "executeName");
            return (Criteria) this;
        }

        public Criteria andExecuteNameLessThanOrEqualTo(String value) {
            addCriterion("execute_name <=", value, "executeName");
            return (Criteria) this;
        }

        public Criteria andExecuteNameLike(String value) {
            addCriterion("execute_name like", value, "executeName");
            return (Criteria) this;
        }

        public Criteria andExecuteNameNotLike(String value) {
            addCriterion("execute_name not like", value, "executeName");
            return (Criteria) this;
        }

        public Criteria andExecuteNameIn(List<String> values) {
            addCriterion("execute_name in", values, "executeName");
            return (Criteria) this;
        }

        public Criteria andExecuteNameNotIn(List<String> values) {
            addCriterion("execute_name not in", values, "executeName");
            return (Criteria) this;
        }

        public Criteria andExecuteNameBetween(String value1, String value2) {
            addCriterion("execute_name between", value1, value2, "executeName");
            return (Criteria) this;
        }

        public Criteria andExecuteNameNotBetween(String value1, String value2) {
            addCriterion("execute_name not between", value1, value2, "executeName");
            return (Criteria) this;
        }

        public Criteria andAuditLabelThirdIdIsNull() {
            addCriterion("audit_label_third_id is null");
            return (Criteria) this;
        }

        public Criteria andAuditLabelThirdIdIsNotNull() {
            addCriterion("audit_label_third_id is not null");
            return (Criteria) this;
        }

        public Criteria andAuditLabelThirdIdEqualTo(String value) {
            addCriterion("audit_label_third_id =", value, "auditLabelThirdId");
            return (Criteria) this;
        }

        public Criteria andAuditLabelThirdIdNotEqualTo(String value) {
            addCriterion("audit_label_third_id <>", value, "auditLabelThirdId");
            return (Criteria) this;
        }

        public Criteria andAuditLabelThirdIdGreaterThan(String value) {
            addCriterion("audit_label_third_id >", value, "auditLabelThirdId");
            return (Criteria) this;
        }

        public Criteria andAuditLabelThirdIdGreaterThanOrEqualTo(String value) {
            addCriterion("audit_label_third_id >=", value, "auditLabelThirdId");
            return (Criteria) this;
        }

        public Criteria andAuditLabelThirdIdLessThan(String value) {
            addCriterion("audit_label_third_id <", value, "auditLabelThirdId");
            return (Criteria) this;
        }

        public Criteria andAuditLabelThirdIdLessThanOrEqualTo(String value) {
            addCriterion("audit_label_third_id <=", value, "auditLabelThirdId");
            return (Criteria) this;
        }

        public Criteria andAuditLabelThirdIdLike(String value) {
            addCriterion("audit_label_third_id like", value, "auditLabelThirdId");
            return (Criteria) this;
        }

        public Criteria andAuditLabelThirdIdNotLike(String value) {
            addCriterion("audit_label_third_id not like", value, "auditLabelThirdId");
            return (Criteria) this;
        }

        public Criteria andAuditLabelThirdIdIn(List<String> values) {
            addCriterion("audit_label_third_id in", values, "auditLabelThirdId");
            return (Criteria) this;
        }

        public Criteria andAuditLabelThirdIdNotIn(List<String> values) {
            addCriterion("audit_label_third_id not in", values, "auditLabelThirdId");
            return (Criteria) this;
        }

        public Criteria andAuditLabelThirdIdBetween(String value1, String value2) {
            addCriterion("audit_label_third_id between", value1, value2, "auditLabelThirdId");
            return (Criteria) this;
        }

        public Criteria andAuditLabelThirdIdNotBetween(String value1, String value2) {
            addCriterion("audit_label_third_id not between", value1, value2, "auditLabelThirdId");
            return (Criteria) this;
        }

        public Criteria andUnitedFirstIndustryIdIsNull() {
            addCriterion("united_first_industry_id is null");
            return (Criteria) this;
        }

        public Criteria andUnitedFirstIndustryIdIsNotNull() {
            addCriterion("united_first_industry_id is not null");
            return (Criteria) this;
        }

        public Criteria andUnitedFirstIndustryIdEqualTo(Integer value) {
            addCriterion("united_first_industry_id =", value, "unitedFirstIndustryId");
            return (Criteria) this;
        }

        public Criteria andUnitedFirstIndustryIdNotEqualTo(Integer value) {
            addCriterion("united_first_industry_id <>", value, "unitedFirstIndustryId");
            return (Criteria) this;
        }

        public Criteria andUnitedFirstIndustryIdGreaterThan(Integer value) {
            addCriterion("united_first_industry_id >", value, "unitedFirstIndustryId");
            return (Criteria) this;
        }

        public Criteria andUnitedFirstIndustryIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("united_first_industry_id >=", value, "unitedFirstIndustryId");
            return (Criteria) this;
        }

        public Criteria andUnitedFirstIndustryIdLessThan(Integer value) {
            addCriterion("united_first_industry_id <", value, "unitedFirstIndustryId");
            return (Criteria) this;
        }

        public Criteria andUnitedFirstIndustryIdLessThanOrEqualTo(Integer value) {
            addCriterion("united_first_industry_id <=", value, "unitedFirstIndustryId");
            return (Criteria) this;
        }

        public Criteria andUnitedFirstIndustryIdIn(List<Integer> values) {
            addCriterion("united_first_industry_id in", values, "unitedFirstIndustryId");
            return (Criteria) this;
        }

        public Criteria andUnitedFirstIndustryIdNotIn(List<Integer> values) {
            addCriterion("united_first_industry_id not in", values, "unitedFirstIndustryId");
            return (Criteria) this;
        }

        public Criteria andUnitedFirstIndustryIdBetween(Integer value1, Integer value2) {
            addCriterion("united_first_industry_id between", value1, value2, "unitedFirstIndustryId");
            return (Criteria) this;
        }

        public Criteria andUnitedFirstIndustryIdNotBetween(Integer value1, Integer value2) {
            addCriterion("united_first_industry_id not between", value1, value2, "unitedFirstIndustryId");
            return (Criteria) this;
        }

        public Criteria andUnitedSecondIndustryIdIsNull() {
            addCriterion("united_second_industry_id is null");
            return (Criteria) this;
        }

        public Criteria andUnitedSecondIndustryIdIsNotNull() {
            addCriterion("united_second_industry_id is not null");
            return (Criteria) this;
        }

        public Criteria andUnitedSecondIndustryIdEqualTo(Integer value) {
            addCriterion("united_second_industry_id =", value, "unitedSecondIndustryId");
            return (Criteria) this;
        }

        public Criteria andUnitedSecondIndustryIdNotEqualTo(Integer value) {
            addCriterion("united_second_industry_id <>", value, "unitedSecondIndustryId");
            return (Criteria) this;
        }

        public Criteria andUnitedSecondIndustryIdGreaterThan(Integer value) {
            addCriterion("united_second_industry_id >", value, "unitedSecondIndustryId");
            return (Criteria) this;
        }

        public Criteria andUnitedSecondIndustryIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("united_second_industry_id >=", value, "unitedSecondIndustryId");
            return (Criteria) this;
        }

        public Criteria andUnitedSecondIndustryIdLessThan(Integer value) {
            addCriterion("united_second_industry_id <", value, "unitedSecondIndustryId");
            return (Criteria) this;
        }

        public Criteria andUnitedSecondIndustryIdLessThanOrEqualTo(Integer value) {
            addCriterion("united_second_industry_id <=", value, "unitedSecondIndustryId");
            return (Criteria) this;
        }

        public Criteria andUnitedSecondIndustryIdIn(List<Integer> values) {
            addCriterion("united_second_industry_id in", values, "unitedSecondIndustryId");
            return (Criteria) this;
        }

        public Criteria andUnitedSecondIndustryIdNotIn(List<Integer> values) {
            addCriterion("united_second_industry_id not in", values, "unitedSecondIndustryId");
            return (Criteria) this;
        }

        public Criteria andUnitedSecondIndustryIdBetween(Integer value1, Integer value2) {
            addCriterion("united_second_industry_id between", value1, value2, "unitedSecondIndustryId");
            return (Criteria) this;
        }

        public Criteria andUnitedSecondIndustryIdNotBetween(Integer value1, Integer value2) {
            addCriterion("united_second_industry_id not between", value1, value2, "unitedSecondIndustryId");
            return (Criteria) this;
        }

        public Criteria andUnitedThirdIndustryIdIsNull() {
            addCriterion("united_third_industry_id is null");
            return (Criteria) this;
        }

        public Criteria andUnitedThirdIndustryIdIsNotNull() {
            addCriterion("united_third_industry_id is not null");
            return (Criteria) this;
        }

        public Criteria andUnitedThirdIndustryIdEqualTo(Integer value) {
            addCriterion("united_third_industry_id =", value, "unitedThirdIndustryId");
            return (Criteria) this;
        }

        public Criteria andUnitedThirdIndustryIdNotEqualTo(Integer value) {
            addCriterion("united_third_industry_id <>", value, "unitedThirdIndustryId");
            return (Criteria) this;
        }

        public Criteria andUnitedThirdIndustryIdGreaterThan(Integer value) {
            addCriterion("united_third_industry_id >", value, "unitedThirdIndustryId");
            return (Criteria) this;
        }

        public Criteria andUnitedThirdIndustryIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("united_third_industry_id >=", value, "unitedThirdIndustryId");
            return (Criteria) this;
        }

        public Criteria andUnitedThirdIndustryIdLessThan(Integer value) {
            addCriterion("united_third_industry_id <", value, "unitedThirdIndustryId");
            return (Criteria) this;
        }

        public Criteria andUnitedThirdIndustryIdLessThanOrEqualTo(Integer value) {
            addCriterion("united_third_industry_id <=", value, "unitedThirdIndustryId");
            return (Criteria) this;
        }

        public Criteria andUnitedThirdIndustryIdIn(List<Integer> values) {
            addCriterion("united_third_industry_id in", values, "unitedThirdIndustryId");
            return (Criteria) this;
        }

        public Criteria andUnitedThirdIndustryIdNotIn(List<Integer> values) {
            addCriterion("united_third_industry_id not in", values, "unitedThirdIndustryId");
            return (Criteria) this;
        }

        public Criteria andUnitedThirdIndustryIdBetween(Integer value1, Integer value2) {
            addCriterion("united_third_industry_id between", value1, value2, "unitedThirdIndustryId");
            return (Criteria) this;
        }

        public Criteria andUnitedThirdIndustryIdNotBetween(Integer value1, Integer value2) {
            addCriterion("united_third_industry_id not between", value1, value2, "unitedThirdIndustryId");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNull() {
            addCriterion("ctime is null");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNotNull() {
            addCriterion("ctime is not null");
            return (Criteria) this;
        }

        public Criteria andCtimeEqualTo(Timestamp value) {
            addCriterion("ctime =", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotEqualTo(Timestamp value) {
            addCriterion("ctime <>", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThan(Timestamp value) {
            addCriterion("ctime >", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("ctime >=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThan(Timestamp value) {
            addCriterion("ctime <", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("ctime <=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeIn(List<Timestamp> values) {
            addCriterion("ctime in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotIn(List<Timestamp> values) {
            addCriterion("ctime not in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime not between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNull() {
            addCriterion("mtime is null");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNotNull() {
            addCriterion("mtime is not null");
            return (Criteria) this;
        }

        public Criteria andMtimeEqualTo(Timestamp value) {
            addCriterion("mtime =", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotEqualTo(Timestamp value) {
            addCriterion("mtime <>", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThan(Timestamp value) {
            addCriterion("mtime >", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("mtime >=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThan(Timestamp value) {
            addCriterion("mtime <", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("mtime <=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeIn(List<Timestamp> values) {
            addCriterion("mtime in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotIn(List<Timestamp> values) {
            addCriterion("mtime not in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime not between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Integer value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Integer value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Integer value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Integer value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Integer value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Integer> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Integer> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andTypeIsNull() {
            addCriterion("type is null");
            return (Criteria) this;
        }

        public Criteria andTypeIsNotNull() {
            addCriterion("type is not null");
            return (Criteria) this;
        }

        public Criteria andTypeEqualTo(Integer value) {
            addCriterion("type =", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotEqualTo(Integer value) {
            addCriterion("type <>", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThan(Integer value) {
            addCriterion("type >", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("type >=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThan(Integer value) {
            addCriterion("type <", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThanOrEqualTo(Integer value) {
            addCriterion("type <=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeIn(List<Integer> values) {
            addCriterion("type in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotIn(List<Integer> values) {
            addCriterion("type not in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeBetween(Integer value1, Integer value2) {
            addCriterion("type between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("type not between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andMaterialSourceIsNull() {
            addCriterion("material_source is null");
            return (Criteria) this;
        }

        public Criteria andMaterialSourceIsNotNull() {
            addCriterion("material_source is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialSourceEqualTo(String value) {
            addCriterion("material_source =", value, "materialSource");
            return (Criteria) this;
        }

        public Criteria andMaterialSourceNotEqualTo(String value) {
            addCriterion("material_source <>", value, "materialSource");
            return (Criteria) this;
        }

        public Criteria andMaterialSourceGreaterThan(String value) {
            addCriterion("material_source >", value, "materialSource");
            return (Criteria) this;
        }

        public Criteria andMaterialSourceGreaterThanOrEqualTo(String value) {
            addCriterion("material_source >=", value, "materialSource");
            return (Criteria) this;
        }

        public Criteria andMaterialSourceLessThan(String value) {
            addCriterion("material_source <", value, "materialSource");
            return (Criteria) this;
        }

        public Criteria andMaterialSourceLessThanOrEqualTo(String value) {
            addCriterion("material_source <=", value, "materialSource");
            return (Criteria) this;
        }

        public Criteria andMaterialSourceLike(String value) {
            addCriterion("material_source like", value, "materialSource");
            return (Criteria) this;
        }

        public Criteria andMaterialSourceNotLike(String value) {
            addCriterion("material_source not like", value, "materialSource");
            return (Criteria) this;
        }

        public Criteria andMaterialSourceIn(List<String> values) {
            addCriterion("material_source in", values, "materialSource");
            return (Criteria) this;
        }

        public Criteria andMaterialSourceNotIn(List<String> values) {
            addCriterion("material_source not in", values, "materialSource");
            return (Criteria) this;
        }

        public Criteria andMaterialSourceBetween(String value1, String value2) {
            addCriterion("material_source between", value1, value2, "materialSource");
            return (Criteria) this;
        }

        public Criteria andMaterialSourceNotBetween(String value1, String value2) {
            addCriterion("material_source not between", value1, value2, "materialSource");
            return (Criteria) this;
        }

        public Criteria andReasonIsNull() {
            addCriterion("reason is null");
            return (Criteria) this;
        }

        public Criteria andReasonIsNotNull() {
            addCriterion("reason is not null");
            return (Criteria) this;
        }

        public Criteria andReasonEqualTo(String value) {
            addCriterion("reason =", value, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonNotEqualTo(String value) {
            addCriterion("reason <>", value, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonGreaterThan(String value) {
            addCriterion("reason >", value, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonGreaterThanOrEqualTo(String value) {
            addCriterion("reason >=", value, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonLessThan(String value) {
            addCriterion("reason <", value, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonLessThanOrEqualTo(String value) {
            addCriterion("reason <=", value, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonLike(String value) {
            addCriterion("reason like", value, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonNotLike(String value) {
            addCriterion("reason not like", value, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonIn(List<String> values) {
            addCriterion("reason in", values, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonNotIn(List<String> values) {
            addCriterion("reason not in", values, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonBetween(String value1, String value2) {
            addCriterion("reason between", value1, value2, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonNotBetween(String value1, String value2) {
            addCriterion("reason not between", value1, value2, "reason");
            return (Criteria) this;
        }

        public Criteria andEnterAuditTimeIsNull() {
            addCriterion("enter_audit_time is null");
            return (Criteria) this;
        }

        public Criteria andEnterAuditTimeIsNotNull() {
            addCriterion("enter_audit_time is not null");
            return (Criteria) this;
        }

        public Criteria andEnterAuditTimeEqualTo(Timestamp value) {
            addCriterion("enter_audit_time =", value, "enterAuditTime");
            return (Criteria) this;
        }

        public Criteria andEnterAuditTimeNotEqualTo(Timestamp value) {
            addCriterion("enter_audit_time <>", value, "enterAuditTime");
            return (Criteria) this;
        }

        public Criteria andEnterAuditTimeGreaterThan(Timestamp value) {
            addCriterion("enter_audit_time >", value, "enterAuditTime");
            return (Criteria) this;
        }

        public Criteria andEnterAuditTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("enter_audit_time >=", value, "enterAuditTime");
            return (Criteria) this;
        }

        public Criteria andEnterAuditTimeLessThan(Timestamp value) {
            addCriterion("enter_audit_time <", value, "enterAuditTime");
            return (Criteria) this;
        }

        public Criteria andEnterAuditTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("enter_audit_time <=", value, "enterAuditTime");
            return (Criteria) this;
        }

        public Criteria andEnterAuditTimeIn(List<Timestamp> values) {
            addCriterion("enter_audit_time in", values, "enterAuditTime");
            return (Criteria) this;
        }

        public Criteria andEnterAuditTimeNotIn(List<Timestamp> values) {
            addCriterion("enter_audit_time not in", values, "enterAuditTime");
            return (Criteria) this;
        }

        public Criteria andEnterAuditTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("enter_audit_time between", value1, value2, "enterAuditTime");
            return (Criteria) this;
        }

        public Criteria andEnterAuditTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("enter_audit_time not between", value1, value2, "enterAuditTime");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}