package com.bilibili.risk.po.risk;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LauMaterialAuditTaskPo implements Serializable {
    /**
     * 自增主键ID
     */
    private Long id;

    /**
     * 任务 id
     */
    private String taskId;

    private String materialId;

    /**
     * 素材md5
     */
    private String materialMd5;

    /**
     * 素材类型
     */
    private Integer materialType;

    /**
     * 素材内容
     */
    private String materialContent;

    /**
     * 领取批次号
     */
    private String receiveBatchNo;

    /**
     * 创意id
     */
    private Integer creativeId;

    /**
     * 业务类型1三连2必火
     */
    private Integer bizType;

    /**
     * 队列id
     */
    private Long queueId;

    /**
     * 创意审核状态:1待审2通过3驳回
     */
    private Integer creativeAuditStatus;

    /**
     * 任务状态:0游离1执行中2完成3stop
     */
    private Integer status;

    /**
     * 账户id
     */
    private Integer accountId;

    /**
     * version
     */
    private Integer version;

    /**
     * 领取时间
     */
    private Timestamp acceptTime;

    /**
     * 领取人
     */
    private String acceptName;

    /**
     * 执行时间
     */
    private Timestamp executeTime;

    /**
     * 执行人
     */
    private String executeName;

    /**
     * 审核三级标签ids
     */
    private String auditLabelThirdId;

    /**
     * 统一一级行业标签
     */
    private Integer unitedFirstIndustryId;

    /**
     * 统一二级行业标签
     */
    private Integer unitedSecondIndustryId;

    /**
     * 统一三级行业标签
     */
    private Integer unitedThirdIndustryId;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    /**
     * 创意素材关系是否有效
     */
    private Integer isDeleted;

    /**
     * 任务类型
     */
    private Integer type;

    /**
     * 素材来源:1创意2story
     */
    private String materialSource;

    /**
     * 驳回理由
     */
    private String reason;

    /**
     * 进审时间
     */
    private Timestamp enterAuditTime;

    /**
     * 扩展字段
     */
    private String extra;

    private static final long serialVersionUID = 1L;
}