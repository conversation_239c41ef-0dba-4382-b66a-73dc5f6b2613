package com.bilibili.risk.po.risk;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> 素材创意关系表
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LauMaterialCreativeRelPo implements Serializable {
    /**
     * 自增主键ID
     */
    private Long id;

    private String materialId;

    /**
     * 素材md5
     */
    private String materialMd5;

    /**
     * 素材类型
     */
    private Integer materialType;

    /**
     * 创意id
     */
    private Integer creativeId;

    /**
     * 是否删除
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    private static final long serialVersionUID = 1L;
}