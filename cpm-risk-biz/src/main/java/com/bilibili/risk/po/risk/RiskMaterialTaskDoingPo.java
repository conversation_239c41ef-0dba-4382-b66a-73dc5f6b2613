package com.bilibili.risk.po.risk;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RiskMaterialTaskDoingPo implements Serializable {
    /**
     * 自增主键ID
     */
    private Long id;

    /**
     * 领取人
     */
    private String username;

    /**
     * 队列id
     */
    private Long queueId;

    /**
     * 领取批次号
     */
    private String receiveBatchNo;

    /**
     * 任务id
     */
    private String taskId;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    private static final long serialVersionUID = 1L;
}