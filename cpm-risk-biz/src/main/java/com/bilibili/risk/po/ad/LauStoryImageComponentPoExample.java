package com.bilibili.risk.po.ad;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public class LauStoryImageComponentPoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public LauStoryImageComponentPoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNull() {
            addCriterion("ctime is null");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNotNull() {
            addCriterion("ctime is not null");
            return (Criteria) this;
        }

        public Criteria andCtimeEqualTo(Timestamp value) {
            addCriterion("ctime =", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotEqualTo(Timestamp value) {
            addCriterion("ctime <>", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThan(Timestamp value) {
            addCriterion("ctime >", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("ctime >=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThan(Timestamp value) {
            addCriterion("ctime <", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("ctime <=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeIn(List<Timestamp> values) {
            addCriterion("ctime in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotIn(List<Timestamp> values) {
            addCriterion("ctime not in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime not between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNull() {
            addCriterion("mtime is null");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNotNull() {
            addCriterion("mtime is not null");
            return (Criteria) this;
        }

        public Criteria andMtimeEqualTo(Timestamp value) {
            addCriterion("mtime =", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotEqualTo(Timestamp value) {
            addCriterion("mtime <>", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThan(Timestamp value) {
            addCriterion("mtime >", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("mtime >=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThan(Timestamp value) {
            addCriterion("mtime <", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("mtime <=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeIn(List<Timestamp> values) {
            addCriterion("mtime in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotIn(List<Timestamp> values) {
            addCriterion("mtime not in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime not between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Integer value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Integer value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Integer value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Integer value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Integer value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Integer> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Integer> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andAccountIdIsNull() {
            addCriterion("account_id is null");
            return (Criteria) this;
        }

        public Criteria andAccountIdIsNotNull() {
            addCriterion("account_id is not null");
            return (Criteria) this;
        }

        public Criteria andAccountIdEqualTo(Integer value) {
            addCriterion("account_id =", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotEqualTo(Integer value) {
            addCriterion("account_id <>", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdGreaterThan(Integer value) {
            addCriterion("account_id >", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("account_id >=", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdLessThan(Integer value) {
            addCriterion("account_id <", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdLessThanOrEqualTo(Integer value) {
            addCriterion("account_id <=", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdIn(List<Integer> values) {
            addCriterion("account_id in", values, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotIn(List<Integer> values) {
            addCriterion("account_id not in", values, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdBetween(Integer value1, Integer value2) {
            addCriterion("account_id between", value1, value2, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotBetween(Integer value1, Integer value2) {
            addCriterion("account_id not between", value1, value2, "accountId");
            return (Criteria) this;
        }

        public Criteria andComponentIdIsNull() {
            addCriterion("component_id is null");
            return (Criteria) this;
        }

        public Criteria andComponentIdIsNotNull() {
            addCriterion("component_id is not null");
            return (Criteria) this;
        }

        public Criteria andComponentIdEqualTo(Long value) {
            addCriterion("component_id =", value, "componentId");
            return (Criteria) this;
        }

        public Criteria andComponentIdNotEqualTo(Long value) {
            addCriterion("component_id <>", value, "componentId");
            return (Criteria) this;
        }

        public Criteria andComponentIdGreaterThan(Long value) {
            addCriterion("component_id >", value, "componentId");
            return (Criteria) this;
        }

        public Criteria andComponentIdGreaterThanOrEqualTo(Long value) {
            addCriterion("component_id >=", value, "componentId");
            return (Criteria) this;
        }

        public Criteria andComponentIdLessThan(Long value) {
            addCriterion("component_id <", value, "componentId");
            return (Criteria) this;
        }

        public Criteria andComponentIdLessThanOrEqualTo(Long value) {
            addCriterion("component_id <=", value, "componentId");
            return (Criteria) this;
        }

        public Criteria andComponentIdIn(List<Long> values) {
            addCriterion("component_id in", values, "componentId");
            return (Criteria) this;
        }

        public Criteria andComponentIdNotIn(List<Long> values) {
            addCriterion("component_id not in", values, "componentId");
            return (Criteria) this;
        }

        public Criteria andComponentIdBetween(Long value1, Long value2) {
            addCriterion("component_id between", value1, value2, "componentId");
            return (Criteria) this;
        }

        public Criteria andComponentIdNotBetween(Long value1, Long value2) {
            addCriterion("component_id not between", value1, value2, "componentId");
            return (Criteria) this;
        }

        public Criteria andComponentNameIsNull() {
            addCriterion("component_name is null");
            return (Criteria) this;
        }

        public Criteria andComponentNameIsNotNull() {
            addCriterion("component_name is not null");
            return (Criteria) this;
        }

        public Criteria andComponentNameEqualTo(String value) {
            addCriterion("component_name =", value, "componentName");
            return (Criteria) this;
        }

        public Criteria andComponentNameNotEqualTo(String value) {
            addCriterion("component_name <>", value, "componentName");
            return (Criteria) this;
        }

        public Criteria andComponentNameGreaterThan(String value) {
            addCriterion("component_name >", value, "componentName");
            return (Criteria) this;
        }

        public Criteria andComponentNameGreaterThanOrEqualTo(String value) {
            addCriterion("component_name >=", value, "componentName");
            return (Criteria) this;
        }

        public Criteria andComponentNameLessThan(String value) {
            addCriterion("component_name <", value, "componentName");
            return (Criteria) this;
        }

        public Criteria andComponentNameLessThanOrEqualTo(String value) {
            addCriterion("component_name <=", value, "componentName");
            return (Criteria) this;
        }

        public Criteria andComponentNameLike(String value) {
            addCriterion("component_name like", value, "componentName");
            return (Criteria) this;
        }

        public Criteria andComponentNameNotLike(String value) {
            addCriterion("component_name not like", value, "componentName");
            return (Criteria) this;
        }

        public Criteria andComponentNameIn(List<String> values) {
            addCriterion("component_name in", values, "componentName");
            return (Criteria) this;
        }

        public Criteria andComponentNameNotIn(List<String> values) {
            addCriterion("component_name not in", values, "componentName");
            return (Criteria) this;
        }

        public Criteria andComponentNameBetween(String value1, String value2) {
            addCriterion("component_name between", value1, value2, "componentName");
            return (Criteria) this;
        }

        public Criteria andComponentNameNotBetween(String value1, String value2) {
            addCriterion("component_name not between", value1, value2, "componentName");
            return (Criteria) this;
        }

        public Criteria andImageUrlIsNull() {
            addCriterion("image_url is null");
            return (Criteria) this;
        }

        public Criteria andImageUrlIsNotNull() {
            addCriterion("image_url is not null");
            return (Criteria) this;
        }

        public Criteria andImageUrlEqualTo(String value) {
            addCriterion("image_url =", value, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlNotEqualTo(String value) {
            addCriterion("image_url <>", value, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlGreaterThan(String value) {
            addCriterion("image_url >", value, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlGreaterThanOrEqualTo(String value) {
            addCriterion("image_url >=", value, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlLessThan(String value) {
            addCriterion("image_url <", value, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlLessThanOrEqualTo(String value) {
            addCriterion("image_url <=", value, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlLike(String value) {
            addCriterion("image_url like", value, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlNotLike(String value) {
            addCriterion("image_url not like", value, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlIn(List<String> values) {
            addCriterion("image_url in", values, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlNotIn(List<String> values) {
            addCriterion("image_url not in", values, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlBetween(String value1, String value2) {
            addCriterion("image_url between", value1, value2, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlNotBetween(String value1, String value2) {
            addCriterion("image_url not between", value1, value2, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageMd5IsNull() {
            addCriterion("image_md5 is null");
            return (Criteria) this;
        }

        public Criteria andImageMd5IsNotNull() {
            addCriterion("image_md5 is not null");
            return (Criteria) this;
        }

        public Criteria andImageMd5EqualTo(String value) {
            addCriterion("image_md5 =", value, "imageMd5");
            return (Criteria) this;
        }

        public Criteria andImageMd5NotEqualTo(String value) {
            addCriterion("image_md5 <>", value, "imageMd5");
            return (Criteria) this;
        }

        public Criteria andImageMd5GreaterThan(String value) {
            addCriterion("image_md5 >", value, "imageMd5");
            return (Criteria) this;
        }

        public Criteria andImageMd5GreaterThanOrEqualTo(String value) {
            addCriterion("image_md5 >=", value, "imageMd5");
            return (Criteria) this;
        }

        public Criteria andImageMd5LessThan(String value) {
            addCriterion("image_md5 <", value, "imageMd5");
            return (Criteria) this;
        }

        public Criteria andImageMd5LessThanOrEqualTo(String value) {
            addCriterion("image_md5 <=", value, "imageMd5");
            return (Criteria) this;
        }

        public Criteria andImageMd5Like(String value) {
            addCriterion("image_md5 like", value, "imageMd5");
            return (Criteria) this;
        }

        public Criteria andImageMd5NotLike(String value) {
            addCriterion("image_md5 not like", value, "imageMd5");
            return (Criteria) this;
        }

        public Criteria andImageMd5In(List<String> values) {
            addCriterion("image_md5 in", values, "imageMd5");
            return (Criteria) this;
        }

        public Criteria andImageMd5NotIn(List<String> values) {
            addCriterion("image_md5 not in", values, "imageMd5");
            return (Criteria) this;
        }

        public Criteria andImageMd5Between(String value1, String value2) {
            addCriterion("image_md5 between", value1, value2, "imageMd5");
            return (Criteria) this;
        }

        public Criteria andImageMd5NotBetween(String value1, String value2) {
            addCriterion("image_md5 not between", value1, value2, "imageMd5");
            return (Criteria) this;
        }

        public Criteria andIsContainsGoodsIsNull() {
            addCriterion("is_contains_goods is null");
            return (Criteria) this;
        }

        public Criteria andIsContainsGoodsIsNotNull() {
            addCriterion("is_contains_goods is not null");
            return (Criteria) this;
        }

        public Criteria andIsContainsGoodsEqualTo(Integer value) {
            addCriterion("is_contains_goods =", value, "isContainsGoods");
            return (Criteria) this;
        }

        public Criteria andIsContainsGoodsNotEqualTo(Integer value) {
            addCriterion("is_contains_goods <>", value, "isContainsGoods");
            return (Criteria) this;
        }

        public Criteria andIsContainsGoodsGreaterThan(Integer value) {
            addCriterion("is_contains_goods >", value, "isContainsGoods");
            return (Criteria) this;
        }

        public Criteria andIsContainsGoodsGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_contains_goods >=", value, "isContainsGoods");
            return (Criteria) this;
        }

        public Criteria andIsContainsGoodsLessThan(Integer value) {
            addCriterion("is_contains_goods <", value, "isContainsGoods");
            return (Criteria) this;
        }

        public Criteria andIsContainsGoodsLessThanOrEqualTo(Integer value) {
            addCriterion("is_contains_goods <=", value, "isContainsGoods");
            return (Criteria) this;
        }

        public Criteria andIsContainsGoodsIn(List<Integer> values) {
            addCriterion("is_contains_goods in", values, "isContainsGoods");
            return (Criteria) this;
        }

        public Criteria andIsContainsGoodsNotIn(List<Integer> values) {
            addCriterion("is_contains_goods not in", values, "isContainsGoods");
            return (Criteria) this;
        }

        public Criteria andIsContainsGoodsBetween(Integer value1, Integer value2) {
            addCriterion("is_contains_goods between", value1, value2, "isContainsGoods");
            return (Criteria) this;
        }

        public Criteria andIsContainsGoodsNotBetween(Integer value1, Integer value2) {
            addCriterion("is_contains_goods not between", value1, value2, "isContainsGoods");
            return (Criteria) this;
        }

        public Criteria andItemIdIsNull() {
            addCriterion("item_id is null");
            return (Criteria) this;
        }

        public Criteria andItemIdIsNotNull() {
            addCriterion("item_id is not null");
            return (Criteria) this;
        }

        public Criteria andItemIdEqualTo(Long value) {
            addCriterion("item_id =", value, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdNotEqualTo(Long value) {
            addCriterion("item_id <>", value, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdGreaterThan(Long value) {
            addCriterion("item_id >", value, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdGreaterThanOrEqualTo(Long value) {
            addCriterion("item_id >=", value, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdLessThan(Long value) {
            addCriterion("item_id <", value, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdLessThanOrEqualTo(Long value) {
            addCriterion("item_id <=", value, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdIn(List<Long> values) {
            addCriterion("item_id in", values, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdNotIn(List<Long> values) {
            addCriterion("item_id not in", values, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdBetween(Long value1, Long value2) {
            addCriterion("item_id between", value1, value2, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdNotBetween(Long value1, Long value2) {
            addCriterion("item_id not between", value1, value2, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemSourceIsNull() {
            addCriterion("item_source is null");
            return (Criteria) this;
        }

        public Criteria andItemSourceIsNotNull() {
            addCriterion("item_source is not null");
            return (Criteria) this;
        }

        public Criteria andItemSourceEqualTo(Integer value) {
            addCriterion("item_source =", value, "itemSource");
            return (Criteria) this;
        }

        public Criteria andItemSourceNotEqualTo(Integer value) {
            addCriterion("item_source <>", value, "itemSource");
            return (Criteria) this;
        }

        public Criteria andItemSourceGreaterThan(Integer value) {
            addCriterion("item_source >", value, "itemSource");
            return (Criteria) this;
        }

        public Criteria andItemSourceGreaterThanOrEqualTo(Integer value) {
            addCriterion("item_source >=", value, "itemSource");
            return (Criteria) this;
        }

        public Criteria andItemSourceLessThan(Integer value) {
            addCriterion("item_source <", value, "itemSource");
            return (Criteria) this;
        }

        public Criteria andItemSourceLessThanOrEqualTo(Integer value) {
            addCriterion("item_source <=", value, "itemSource");
            return (Criteria) this;
        }

        public Criteria andItemSourceIn(List<Integer> values) {
            addCriterion("item_source in", values, "itemSource");
            return (Criteria) this;
        }

        public Criteria andItemSourceNotIn(List<Integer> values) {
            addCriterion("item_source not in", values, "itemSource");
            return (Criteria) this;
        }

        public Criteria andItemSourceBetween(Integer value1, Integer value2) {
            addCriterion("item_source between", value1, value2, "itemSource");
            return (Criteria) this;
        }

        public Criteria andItemSourceNotBetween(Integer value1, Integer value2) {
            addCriterion("item_source not between", value1, value2, "itemSource");
            return (Criteria) this;
        }

        public Criteria andJumpTypeIsNull() {
            addCriterion("jump_type is null");
            return (Criteria) this;
        }

        public Criteria andJumpTypeIsNotNull() {
            addCriterion("jump_type is not null");
            return (Criteria) this;
        }

        public Criteria andJumpTypeEqualTo(Integer value) {
            addCriterion("jump_type =", value, "jumpType");
            return (Criteria) this;
        }

        public Criteria andJumpTypeNotEqualTo(Integer value) {
            addCriterion("jump_type <>", value, "jumpType");
            return (Criteria) this;
        }

        public Criteria andJumpTypeGreaterThan(Integer value) {
            addCriterion("jump_type >", value, "jumpType");
            return (Criteria) this;
        }

        public Criteria andJumpTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("jump_type >=", value, "jumpType");
            return (Criteria) this;
        }

        public Criteria andJumpTypeLessThan(Integer value) {
            addCriterion("jump_type <", value, "jumpType");
            return (Criteria) this;
        }

        public Criteria andJumpTypeLessThanOrEqualTo(Integer value) {
            addCriterion("jump_type <=", value, "jumpType");
            return (Criteria) this;
        }

        public Criteria andJumpTypeIn(List<Integer> values) {
            addCriterion("jump_type in", values, "jumpType");
            return (Criteria) this;
        }

        public Criteria andJumpTypeNotIn(List<Integer> values) {
            addCriterion("jump_type not in", values, "jumpType");
            return (Criteria) this;
        }

        public Criteria andJumpTypeBetween(Integer value1, Integer value2) {
            addCriterion("jump_type between", value1, value2, "jumpType");
            return (Criteria) this;
        }

        public Criteria andJumpTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("jump_type not between", value1, value2, "jumpType");
            return (Criteria) this;
        }

        public Criteria andJumpUrlIsNull() {
            addCriterion("jump_url is null");
            return (Criteria) this;
        }

        public Criteria andJumpUrlIsNotNull() {
            addCriterion("jump_url is not null");
            return (Criteria) this;
        }

        public Criteria andJumpUrlEqualTo(String value) {
            addCriterion("jump_url =", value, "jumpUrl");
            return (Criteria) this;
        }

        public Criteria andJumpUrlNotEqualTo(String value) {
            addCriterion("jump_url <>", value, "jumpUrl");
            return (Criteria) this;
        }

        public Criteria andJumpUrlGreaterThan(String value) {
            addCriterion("jump_url >", value, "jumpUrl");
            return (Criteria) this;
        }

        public Criteria andJumpUrlGreaterThanOrEqualTo(String value) {
            addCriterion("jump_url >=", value, "jumpUrl");
            return (Criteria) this;
        }

        public Criteria andJumpUrlLessThan(String value) {
            addCriterion("jump_url <", value, "jumpUrl");
            return (Criteria) this;
        }

        public Criteria andJumpUrlLessThanOrEqualTo(String value) {
            addCriterion("jump_url <=", value, "jumpUrl");
            return (Criteria) this;
        }

        public Criteria andJumpUrlLike(String value) {
            addCriterion("jump_url like", value, "jumpUrl");
            return (Criteria) this;
        }

        public Criteria andJumpUrlNotLike(String value) {
            addCriterion("jump_url not like", value, "jumpUrl");
            return (Criteria) this;
        }

        public Criteria andJumpUrlIn(List<String> values) {
            addCriterion("jump_url in", values, "jumpUrl");
            return (Criteria) this;
        }

        public Criteria andJumpUrlNotIn(List<String> values) {
            addCriterion("jump_url not in", values, "jumpUrl");
            return (Criteria) this;
        }

        public Criteria andJumpUrlBetween(String value1, String value2) {
            addCriterion("jump_url between", value1, value2, "jumpUrl");
            return (Criteria) this;
        }

        public Criteria andJumpUrlNotBetween(String value1, String value2) {
            addCriterion("jump_url not between", value1, value2, "jumpUrl");
            return (Criteria) this;
        }

        public Criteria andSchemaUrlIsNull() {
            addCriterion("schema_url is null");
            return (Criteria) this;
        }

        public Criteria andSchemaUrlIsNotNull() {
            addCriterion("schema_url is not null");
            return (Criteria) this;
        }

        public Criteria andSchemaUrlEqualTo(String value) {
            addCriterion("schema_url =", value, "schemaUrl");
            return (Criteria) this;
        }

        public Criteria andSchemaUrlNotEqualTo(String value) {
            addCriterion("schema_url <>", value, "schemaUrl");
            return (Criteria) this;
        }

        public Criteria andSchemaUrlGreaterThan(String value) {
            addCriterion("schema_url >", value, "schemaUrl");
            return (Criteria) this;
        }

        public Criteria andSchemaUrlGreaterThanOrEqualTo(String value) {
            addCriterion("schema_url >=", value, "schemaUrl");
            return (Criteria) this;
        }

        public Criteria andSchemaUrlLessThan(String value) {
            addCriterion("schema_url <", value, "schemaUrl");
            return (Criteria) this;
        }

        public Criteria andSchemaUrlLessThanOrEqualTo(String value) {
            addCriterion("schema_url <=", value, "schemaUrl");
            return (Criteria) this;
        }

        public Criteria andSchemaUrlLike(String value) {
            addCriterion("schema_url like", value, "schemaUrl");
            return (Criteria) this;
        }

        public Criteria andSchemaUrlNotLike(String value) {
            addCriterion("schema_url not like", value, "schemaUrl");
            return (Criteria) this;
        }

        public Criteria andSchemaUrlIn(List<String> values) {
            addCriterion("schema_url in", values, "schemaUrl");
            return (Criteria) this;
        }

        public Criteria andSchemaUrlNotIn(List<String> values) {
            addCriterion("schema_url not in", values, "schemaUrl");
            return (Criteria) this;
        }

        public Criteria andSchemaUrlBetween(String value1, String value2) {
            addCriterion("schema_url between", value1, value2, "schemaUrl");
            return (Criteria) this;
        }

        public Criteria andSchemaUrlNotBetween(String value1, String value2) {
            addCriterion("schema_url not between", value1, value2, "schemaUrl");
            return (Criteria) this;
        }

        public Criteria andMiniProgramIdIsNull() {
            addCriterion("mini_program_id is null");
            return (Criteria) this;
        }

        public Criteria andMiniProgramIdIsNotNull() {
            addCriterion("mini_program_id is not null");
            return (Criteria) this;
        }

        public Criteria andMiniProgramIdEqualTo(String value) {
            addCriterion("mini_program_id =", value, "miniProgramId");
            return (Criteria) this;
        }

        public Criteria andMiniProgramIdNotEqualTo(String value) {
            addCriterion("mini_program_id <>", value, "miniProgramId");
            return (Criteria) this;
        }

        public Criteria andMiniProgramIdGreaterThan(String value) {
            addCriterion("mini_program_id >", value, "miniProgramId");
            return (Criteria) this;
        }

        public Criteria andMiniProgramIdGreaterThanOrEqualTo(String value) {
            addCriterion("mini_program_id >=", value, "miniProgramId");
            return (Criteria) this;
        }

        public Criteria andMiniProgramIdLessThan(String value) {
            addCriterion("mini_program_id <", value, "miniProgramId");
            return (Criteria) this;
        }

        public Criteria andMiniProgramIdLessThanOrEqualTo(String value) {
            addCriterion("mini_program_id <=", value, "miniProgramId");
            return (Criteria) this;
        }

        public Criteria andMiniProgramIdLike(String value) {
            addCriterion("mini_program_id like", value, "miniProgramId");
            return (Criteria) this;
        }

        public Criteria andMiniProgramIdNotLike(String value) {
            addCriterion("mini_program_id not like", value, "miniProgramId");
            return (Criteria) this;
        }

        public Criteria andMiniProgramIdIn(List<String> values) {
            addCriterion("mini_program_id in", values, "miniProgramId");
            return (Criteria) this;
        }

        public Criteria andMiniProgramIdNotIn(List<String> values) {
            addCriterion("mini_program_id not in", values, "miniProgramId");
            return (Criteria) this;
        }

        public Criteria andMiniProgramIdBetween(String value1, String value2) {
            addCriterion("mini_program_id between", value1, value2, "miniProgramId");
            return (Criteria) this;
        }

        public Criteria andMiniProgramIdNotBetween(String value1, String value2) {
            addCriterion("mini_program_id not between", value1, value2, "miniProgramId");
            return (Criteria) this;
        }

        public Criteria andMiniProgramNameIsNull() {
            addCriterion("mini_program_name is null");
            return (Criteria) this;
        }

        public Criteria andMiniProgramNameIsNotNull() {
            addCriterion("mini_program_name is not null");
            return (Criteria) this;
        }

        public Criteria andMiniProgramNameEqualTo(String value) {
            addCriterion("mini_program_name =", value, "miniProgramName");
            return (Criteria) this;
        }

        public Criteria andMiniProgramNameNotEqualTo(String value) {
            addCriterion("mini_program_name <>", value, "miniProgramName");
            return (Criteria) this;
        }

        public Criteria andMiniProgramNameGreaterThan(String value) {
            addCriterion("mini_program_name >", value, "miniProgramName");
            return (Criteria) this;
        }

        public Criteria andMiniProgramNameGreaterThanOrEqualTo(String value) {
            addCriterion("mini_program_name >=", value, "miniProgramName");
            return (Criteria) this;
        }

        public Criteria andMiniProgramNameLessThan(String value) {
            addCriterion("mini_program_name <", value, "miniProgramName");
            return (Criteria) this;
        }

        public Criteria andMiniProgramNameLessThanOrEqualTo(String value) {
            addCriterion("mini_program_name <=", value, "miniProgramName");
            return (Criteria) this;
        }

        public Criteria andMiniProgramNameLike(String value) {
            addCriterion("mini_program_name like", value, "miniProgramName");
            return (Criteria) this;
        }

        public Criteria andMiniProgramNameNotLike(String value) {
            addCriterion("mini_program_name not like", value, "miniProgramName");
            return (Criteria) this;
        }

        public Criteria andMiniProgramNameIn(List<String> values) {
            addCriterion("mini_program_name in", values, "miniProgramName");
            return (Criteria) this;
        }

        public Criteria andMiniProgramNameNotIn(List<String> values) {
            addCriterion("mini_program_name not in", values, "miniProgramName");
            return (Criteria) this;
        }

        public Criteria andMiniProgramNameBetween(String value1, String value2) {
            addCriterion("mini_program_name between", value1, value2, "miniProgramName");
            return (Criteria) this;
        }

        public Criteria andMiniProgramNameNotBetween(String value1, String value2) {
            addCriterion("mini_program_name not between", value1, value2, "miniProgramName");
            return (Criteria) this;
        }

        public Criteria andMiniProgramPathIsNull() {
            addCriterion("mini_program_path is null");
            return (Criteria) this;
        }

        public Criteria andMiniProgramPathIsNotNull() {
            addCriterion("mini_program_path is not null");
            return (Criteria) this;
        }

        public Criteria andMiniProgramPathEqualTo(String value) {
            addCriterion("mini_program_path =", value, "miniProgramPath");
            return (Criteria) this;
        }

        public Criteria andMiniProgramPathNotEqualTo(String value) {
            addCriterion("mini_program_path <>", value, "miniProgramPath");
            return (Criteria) this;
        }

        public Criteria andMiniProgramPathGreaterThan(String value) {
            addCriterion("mini_program_path >", value, "miniProgramPath");
            return (Criteria) this;
        }

        public Criteria andMiniProgramPathGreaterThanOrEqualTo(String value) {
            addCriterion("mini_program_path >=", value, "miniProgramPath");
            return (Criteria) this;
        }

        public Criteria andMiniProgramPathLessThan(String value) {
            addCriterion("mini_program_path <", value, "miniProgramPath");
            return (Criteria) this;
        }

        public Criteria andMiniProgramPathLessThanOrEqualTo(String value) {
            addCriterion("mini_program_path <=", value, "miniProgramPath");
            return (Criteria) this;
        }

        public Criteria andMiniProgramPathLike(String value) {
            addCriterion("mini_program_path like", value, "miniProgramPath");
            return (Criteria) this;
        }

        public Criteria andMiniProgramPathNotLike(String value) {
            addCriterion("mini_program_path not like", value, "miniProgramPath");
            return (Criteria) this;
        }

        public Criteria andMiniProgramPathIn(List<String> values) {
            addCriterion("mini_program_path in", values, "miniProgramPath");
            return (Criteria) this;
        }

        public Criteria andMiniProgramPathNotIn(List<String> values) {
            addCriterion("mini_program_path not in", values, "miniProgramPath");
            return (Criteria) this;
        }

        public Criteria andMiniProgramPathBetween(String value1, String value2) {
            addCriterion("mini_program_path between", value1, value2, "miniProgramPath");
            return (Criteria) this;
        }

        public Criteria andMiniProgramPathNotBetween(String value1, String value2) {
            addCriterion("mini_program_path not between", value1, value2, "miniProgramPath");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}