package com.bilibili.risk.po.risk;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LauMaterialAuditLabelPo implements Serializable {
    /**
     * 自增主键ID
     */
    private Long id;

    /**
     * 标签名
     */
    private String name;

    /**
     * level:1,2,3
     */
    private Long level;

    /**
     * 驳回理由,三级才会有reason
     */
    private String reason;

    /**
     * 父级id，一级的pid=0
     */
    private Long pid;

    /**
     * 是否删除
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    /**
     * 素材处置类型:2通过3驳回，与状态审核状态对齐
     */
    private Integer handleType;

    /**
     * 处置类型子分类: 0无1更新评级标签
     */
    private Integer subHandleType;

    private static final long serialVersionUID = 1L;
}