package com.bilibili.risk.po.risk;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LauMaterialAuditRuleQueueRelPo implements Serializable {
    /**
     * 自增主键ID
     */
    private Long id;

    /**
     * rule id
     */
    private Long ruleId;

    /**
     * queue id
     */
    private Long queueId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 扩展字段
     */
    private String extra;

    /**
     * 是否删除
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    private static final long serialVersionUID = 1L;
}