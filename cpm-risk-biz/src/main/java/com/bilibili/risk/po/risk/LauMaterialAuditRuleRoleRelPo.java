package com.bilibili.risk.po.risk;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LauMaterialAuditRuleRoleRelPo implements Serializable {
    /**
     * 自增主键ID
     */
    private Long id;

    /**
     * rule id
     */
    private Long ruleId;

    /**
     * role id
     */
    private Integer roleId;

    /**
     * 是否删除
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    private static final long serialVersionUID = 1L;
}