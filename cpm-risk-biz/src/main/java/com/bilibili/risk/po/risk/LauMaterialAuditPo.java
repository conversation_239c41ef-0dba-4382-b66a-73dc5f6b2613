package com.bilibili.risk.po.risk;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> 素材审核表
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LauMaterialAuditPo implements Serializable {
    /**
     * 自增主键ID
     */
    private Long id;

    private String materialId;

    /**
     * 素材md5
     */
    private String materialMd5;

    /**
     * 素材类型
     */
    private Integer materialType;

    /**
     * 素材内容
     */
    private String materialContent;

    /**
     * 扩展字段
     */
    private String extra;

    /**
     * 审核三级标签ids
     */
    private String auditLabelThirdId;

    /**
     * 版本
     */
    private Integer version;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    /**
     * 原始物料内容
     */
    private String rawContent;

    private Integer auditStatus;

    private String reason;

    /**
     * 素材是否被占用
     */
    private Integer isDeleted;

    private static final long serialVersionUID = 1L;
}