package com.bilibili.risk.po.ad;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public class LauUnitCreativePoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public LauUnitCreativePoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andCreativeIdIsNull() {
            addCriterion("creative_id is null");
            return (Criteria) this;
        }

        public Criteria andCreativeIdIsNotNull() {
            addCriterion("creative_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreativeIdEqualTo(Integer value) {
            addCriterion("creative_id =", value, "creativeId");
            return (Criteria) this;
        }

        public Criteria andCreativeIdNotEqualTo(Integer value) {
            addCriterion("creative_id <>", value, "creativeId");
            return (Criteria) this;
        }

        public Criteria andCreativeIdGreaterThan(Integer value) {
            addCriterion("creative_id >", value, "creativeId");
            return (Criteria) this;
        }

        public Criteria andCreativeIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("creative_id >=", value, "creativeId");
            return (Criteria) this;
        }

        public Criteria andCreativeIdLessThan(Integer value) {
            addCriterion("creative_id <", value, "creativeId");
            return (Criteria) this;
        }

        public Criteria andCreativeIdLessThanOrEqualTo(Integer value) {
            addCriterion("creative_id <=", value, "creativeId");
            return (Criteria) this;
        }

        public Criteria andCreativeIdIn(List<Integer> values) {
            addCriterion("creative_id in", values, "creativeId");
            return (Criteria) this;
        }

        public Criteria andCreativeIdNotIn(List<Integer> values) {
            addCriterion("creative_id not in", values, "creativeId");
            return (Criteria) this;
        }

        public Criteria andCreativeIdBetween(Integer value1, Integer value2) {
            addCriterion("creative_id between", value1, value2, "creativeId");
            return (Criteria) this;
        }

        public Criteria andCreativeIdNotBetween(Integer value1, Integer value2) {
            addCriterion("creative_id not between", value1, value2, "creativeId");
            return (Criteria) this;
        }

        public Criteria andAccountIdIsNull() {
            addCriterion("account_id is null");
            return (Criteria) this;
        }

        public Criteria andAccountIdIsNotNull() {
            addCriterion("account_id is not null");
            return (Criteria) this;
        }

        public Criteria andAccountIdEqualTo(Integer value) {
            addCriterion("account_id =", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotEqualTo(Integer value) {
            addCriterion("account_id <>", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdGreaterThan(Integer value) {
            addCriterion("account_id >", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("account_id >=", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdLessThan(Integer value) {
            addCriterion("account_id <", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdLessThanOrEqualTo(Integer value) {
            addCriterion("account_id <=", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdIn(List<Integer> values) {
            addCriterion("account_id in", values, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotIn(List<Integer> values) {
            addCriterion("account_id not in", values, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdBetween(Integer value1, Integer value2) {
            addCriterion("account_id between", value1, value2, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotBetween(Integer value1, Integer value2) {
            addCriterion("account_id not between", value1, value2, "accountId");
            return (Criteria) this;
        }

        public Criteria andCampaignIdIsNull() {
            addCriterion("campaign_id is null");
            return (Criteria) this;
        }

        public Criteria andCampaignIdIsNotNull() {
            addCriterion("campaign_id is not null");
            return (Criteria) this;
        }

        public Criteria andCampaignIdEqualTo(Integer value) {
            addCriterion("campaign_id =", value, "campaignId");
            return (Criteria) this;
        }

        public Criteria andCampaignIdNotEqualTo(Integer value) {
            addCriterion("campaign_id <>", value, "campaignId");
            return (Criteria) this;
        }

        public Criteria andCampaignIdGreaterThan(Integer value) {
            addCriterion("campaign_id >", value, "campaignId");
            return (Criteria) this;
        }

        public Criteria andCampaignIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("campaign_id >=", value, "campaignId");
            return (Criteria) this;
        }

        public Criteria andCampaignIdLessThan(Integer value) {
            addCriterion("campaign_id <", value, "campaignId");
            return (Criteria) this;
        }

        public Criteria andCampaignIdLessThanOrEqualTo(Integer value) {
            addCriterion("campaign_id <=", value, "campaignId");
            return (Criteria) this;
        }

        public Criteria andCampaignIdIn(List<Integer> values) {
            addCriterion("campaign_id in", values, "campaignId");
            return (Criteria) this;
        }

        public Criteria andCampaignIdNotIn(List<Integer> values) {
            addCriterion("campaign_id not in", values, "campaignId");
            return (Criteria) this;
        }

        public Criteria andCampaignIdBetween(Integer value1, Integer value2) {
            addCriterion("campaign_id between", value1, value2, "campaignId");
            return (Criteria) this;
        }

        public Criteria andCampaignIdNotBetween(Integer value1, Integer value2) {
            addCriterion("campaign_id not between", value1, value2, "campaignId");
            return (Criteria) this;
        }

        public Criteria andCreativeTypeIsNull() {
            addCriterion("creative_type is null");
            return (Criteria) this;
        }

        public Criteria andCreativeTypeIsNotNull() {
            addCriterion("creative_type is not null");
            return (Criteria) this;
        }

        public Criteria andCreativeTypeEqualTo(Integer value) {
            addCriterion("creative_type =", value, "creativeType");
            return (Criteria) this;
        }

        public Criteria andCreativeTypeNotEqualTo(Integer value) {
            addCriterion("creative_type <>", value, "creativeType");
            return (Criteria) this;
        }

        public Criteria andCreativeTypeGreaterThan(Integer value) {
            addCriterion("creative_type >", value, "creativeType");
            return (Criteria) this;
        }

        public Criteria andCreativeTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("creative_type >=", value, "creativeType");
            return (Criteria) this;
        }

        public Criteria andCreativeTypeLessThan(Integer value) {
            addCriterion("creative_type <", value, "creativeType");
            return (Criteria) this;
        }

        public Criteria andCreativeTypeLessThanOrEqualTo(Integer value) {
            addCriterion("creative_type <=", value, "creativeType");
            return (Criteria) this;
        }

        public Criteria andCreativeTypeIn(List<Integer> values) {
            addCriterion("creative_type in", values, "creativeType");
            return (Criteria) this;
        }

        public Criteria andCreativeTypeNotIn(List<Integer> values) {
            addCriterion("creative_type not in", values, "creativeType");
            return (Criteria) this;
        }

        public Criteria andCreativeTypeBetween(Integer value1, Integer value2) {
            addCriterion("creative_type between", value1, value2, "creativeType");
            return (Criteria) this;
        }

        public Criteria andCreativeTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("creative_type not between", value1, value2, "creativeType");
            return (Criteria) this;
        }

        public Criteria andUnitIdIsNull() {
            addCriterion("unit_id is null");
            return (Criteria) this;
        }

        public Criteria andUnitIdIsNotNull() {
            addCriterion("unit_id is not null");
            return (Criteria) this;
        }

        public Criteria andUnitIdEqualTo(Integer value) {
            addCriterion("unit_id =", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdNotEqualTo(Integer value) {
            addCriterion("unit_id <>", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdGreaterThan(Integer value) {
            addCriterion("unit_id >", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("unit_id >=", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdLessThan(Integer value) {
            addCriterion("unit_id <", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdLessThanOrEqualTo(Integer value) {
            addCriterion("unit_id <=", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdIn(List<Integer> values) {
            addCriterion("unit_id in", values, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdNotIn(List<Integer> values) {
            addCriterion("unit_id not in", values, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdBetween(Integer value1, Integer value2) {
            addCriterion("unit_id between", value1, value2, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdNotBetween(Integer value1, Integer value2) {
            addCriterion("unit_id not between", value1, value2, "unitId");
            return (Criteria) this;
        }

        public Criteria andCreativeNameIsNull() {
            addCriterion("creative_name is null");
            return (Criteria) this;
        }

        public Criteria andCreativeNameIsNotNull() {
            addCriterion("creative_name is not null");
            return (Criteria) this;
        }

        public Criteria andCreativeNameEqualTo(String value) {
            addCriterion("creative_name =", value, "creativeName");
            return (Criteria) this;
        }

        public Criteria andCreativeNameNotEqualTo(String value) {
            addCriterion("creative_name <>", value, "creativeName");
            return (Criteria) this;
        }

        public Criteria andCreativeNameGreaterThan(String value) {
            addCriterion("creative_name >", value, "creativeName");
            return (Criteria) this;
        }

        public Criteria andCreativeNameGreaterThanOrEqualTo(String value) {
            addCriterion("creative_name >=", value, "creativeName");
            return (Criteria) this;
        }

        public Criteria andCreativeNameLessThan(String value) {
            addCriterion("creative_name <", value, "creativeName");
            return (Criteria) this;
        }

        public Criteria andCreativeNameLessThanOrEqualTo(String value) {
            addCriterion("creative_name <=", value, "creativeName");
            return (Criteria) this;
        }

        public Criteria andCreativeNameLike(String value) {
            addCriterion("creative_name like", value, "creativeName");
            return (Criteria) this;
        }

        public Criteria andCreativeNameNotLike(String value) {
            addCriterion("creative_name not like", value, "creativeName");
            return (Criteria) this;
        }

        public Criteria andCreativeNameIn(List<String> values) {
            addCriterion("creative_name in", values, "creativeName");
            return (Criteria) this;
        }

        public Criteria andCreativeNameNotIn(List<String> values) {
            addCriterion("creative_name not in", values, "creativeName");
            return (Criteria) this;
        }

        public Criteria andCreativeNameBetween(String value1, String value2) {
            addCriterion("creative_name between", value1, value2, "creativeName");
            return (Criteria) this;
        }

        public Criteria andCreativeNameNotBetween(String value1, String value2) {
            addCriterion("creative_name not between", value1, value2, "creativeName");
            return (Criteria) this;
        }

        public Criteria andPromotionPurposeContentIsNull() {
            addCriterion("promotion_purpose_content is null");
            return (Criteria) this;
        }

        public Criteria andPromotionPurposeContentIsNotNull() {
            addCriterion("promotion_purpose_content is not null");
            return (Criteria) this;
        }

        public Criteria andPromotionPurposeContentEqualTo(String value) {
            addCriterion("promotion_purpose_content =", value, "promotionPurposeContent");
            return (Criteria) this;
        }

        public Criteria andPromotionPurposeContentNotEqualTo(String value) {
            addCriterion("promotion_purpose_content <>", value, "promotionPurposeContent");
            return (Criteria) this;
        }

        public Criteria andPromotionPurposeContentGreaterThan(String value) {
            addCriterion("promotion_purpose_content >", value, "promotionPurposeContent");
            return (Criteria) this;
        }

        public Criteria andPromotionPurposeContentGreaterThanOrEqualTo(String value) {
            addCriterion("promotion_purpose_content >=", value, "promotionPurposeContent");
            return (Criteria) this;
        }

        public Criteria andPromotionPurposeContentLessThan(String value) {
            addCriterion("promotion_purpose_content <", value, "promotionPurposeContent");
            return (Criteria) this;
        }

        public Criteria andPromotionPurposeContentLessThanOrEqualTo(String value) {
            addCriterion("promotion_purpose_content <=", value, "promotionPurposeContent");
            return (Criteria) this;
        }

        public Criteria andPromotionPurposeContentLike(String value) {
            addCriterion("promotion_purpose_content like", value, "promotionPurposeContent");
            return (Criteria) this;
        }

        public Criteria andPromotionPurposeContentNotLike(String value) {
            addCriterion("promotion_purpose_content not like", value, "promotionPurposeContent");
            return (Criteria) this;
        }

        public Criteria andPromotionPurposeContentIn(List<String> values) {
            addCriterion("promotion_purpose_content in", values, "promotionPurposeContent");
            return (Criteria) this;
        }

        public Criteria andPromotionPurposeContentNotIn(List<String> values) {
            addCriterion("promotion_purpose_content not in", values, "promotionPurposeContent");
            return (Criteria) this;
        }

        public Criteria andPromotionPurposeContentBetween(String value1, String value2) {
            addCriterion("promotion_purpose_content between", value1, value2, "promotionPurposeContent");
            return (Criteria) this;
        }

        public Criteria andPromotionPurposeContentNotBetween(String value1, String value2) {
            addCriterion("promotion_purpose_content not between", value1, value2, "promotionPurposeContent");
            return (Criteria) this;
        }

        public Criteria andCustomizedImpUrlIsNull() {
            addCriterion("customized_imp_url is null");
            return (Criteria) this;
        }

        public Criteria andCustomizedImpUrlIsNotNull() {
            addCriterion("customized_imp_url is not null");
            return (Criteria) this;
        }

        public Criteria andCustomizedImpUrlEqualTo(String value) {
            addCriterion("customized_imp_url =", value, "customizedImpUrl");
            return (Criteria) this;
        }

        public Criteria andCustomizedImpUrlNotEqualTo(String value) {
            addCriterion("customized_imp_url <>", value, "customizedImpUrl");
            return (Criteria) this;
        }

        public Criteria andCustomizedImpUrlGreaterThan(String value) {
            addCriterion("customized_imp_url >", value, "customizedImpUrl");
            return (Criteria) this;
        }

        public Criteria andCustomizedImpUrlGreaterThanOrEqualTo(String value) {
            addCriterion("customized_imp_url >=", value, "customizedImpUrl");
            return (Criteria) this;
        }

        public Criteria andCustomizedImpUrlLessThan(String value) {
            addCriterion("customized_imp_url <", value, "customizedImpUrl");
            return (Criteria) this;
        }

        public Criteria andCustomizedImpUrlLessThanOrEqualTo(String value) {
            addCriterion("customized_imp_url <=", value, "customizedImpUrl");
            return (Criteria) this;
        }

        public Criteria andCustomizedImpUrlLike(String value) {
            addCriterion("customized_imp_url like", value, "customizedImpUrl");
            return (Criteria) this;
        }

        public Criteria andCustomizedImpUrlNotLike(String value) {
            addCriterion("customized_imp_url not like", value, "customizedImpUrl");
            return (Criteria) this;
        }

        public Criteria andCustomizedImpUrlIn(List<String> values) {
            addCriterion("customized_imp_url in", values, "customizedImpUrl");
            return (Criteria) this;
        }

        public Criteria andCustomizedImpUrlNotIn(List<String> values) {
            addCriterion("customized_imp_url not in", values, "customizedImpUrl");
            return (Criteria) this;
        }

        public Criteria andCustomizedImpUrlBetween(String value1, String value2) {
            addCriterion("customized_imp_url between", value1, value2, "customizedImpUrl");
            return (Criteria) this;
        }

        public Criteria andCustomizedImpUrlNotBetween(String value1, String value2) {
            addCriterion("customized_imp_url not between", value1, value2, "customizedImpUrl");
            return (Criteria) this;
        }

        public Criteria andCustomizedClickUrlIsNull() {
            addCriterion("customized_click_url is null");
            return (Criteria) this;
        }

        public Criteria andCustomizedClickUrlIsNotNull() {
            addCriterion("customized_click_url is not null");
            return (Criteria) this;
        }

        public Criteria andCustomizedClickUrlEqualTo(String value) {
            addCriterion("customized_click_url =", value, "customizedClickUrl");
            return (Criteria) this;
        }

        public Criteria andCustomizedClickUrlNotEqualTo(String value) {
            addCriterion("customized_click_url <>", value, "customizedClickUrl");
            return (Criteria) this;
        }

        public Criteria andCustomizedClickUrlGreaterThan(String value) {
            addCriterion("customized_click_url >", value, "customizedClickUrl");
            return (Criteria) this;
        }

        public Criteria andCustomizedClickUrlGreaterThanOrEqualTo(String value) {
            addCriterion("customized_click_url >=", value, "customizedClickUrl");
            return (Criteria) this;
        }

        public Criteria andCustomizedClickUrlLessThan(String value) {
            addCriterion("customized_click_url <", value, "customizedClickUrl");
            return (Criteria) this;
        }

        public Criteria andCustomizedClickUrlLessThanOrEqualTo(String value) {
            addCriterion("customized_click_url <=", value, "customizedClickUrl");
            return (Criteria) this;
        }

        public Criteria andCustomizedClickUrlLike(String value) {
            addCriterion("customized_click_url like", value, "customizedClickUrl");
            return (Criteria) this;
        }

        public Criteria andCustomizedClickUrlNotLike(String value) {
            addCriterion("customized_click_url not like", value, "customizedClickUrl");
            return (Criteria) this;
        }

        public Criteria andCustomizedClickUrlIn(List<String> values) {
            addCriterion("customized_click_url in", values, "customizedClickUrl");
            return (Criteria) this;
        }

        public Criteria andCustomizedClickUrlNotIn(List<String> values) {
            addCriterion("customized_click_url not in", values, "customizedClickUrl");
            return (Criteria) this;
        }

        public Criteria andCustomizedClickUrlBetween(String value1, String value2) {
            addCriterion("customized_click_url between", value1, value2, "customizedClickUrl");
            return (Criteria) this;
        }

        public Criteria andCustomizedClickUrlNotBetween(String value1, String value2) {
            addCriterion("customized_click_url not between", value1, value2, "customizedClickUrl");
            return (Criteria) this;
        }

        public Criteria andTitleIsNull() {
            addCriterion("title is null");
            return (Criteria) this;
        }

        public Criteria andTitleIsNotNull() {
            addCriterion("title is not null");
            return (Criteria) this;
        }

        public Criteria andTitleEqualTo(String value) {
            addCriterion("title =", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotEqualTo(String value) {
            addCriterion("title <>", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleGreaterThan(String value) {
            addCriterion("title >", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleGreaterThanOrEqualTo(String value) {
            addCriterion("title >=", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLessThan(String value) {
            addCriterion("title <", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLessThanOrEqualTo(String value) {
            addCriterion("title <=", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLike(String value) {
            addCriterion("title like", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotLike(String value) {
            addCriterion("title not like", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleIn(List<String> values) {
            addCriterion("title in", values, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotIn(List<String> values) {
            addCriterion("title not in", values, "title");
            return (Criteria) this;
        }

        public Criteria andTitleBetween(String value1, String value2) {
            addCriterion("title between", value1, value2, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotBetween(String value1, String value2) {
            addCriterion("title not between", value1, value2, "title");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNull() {
            addCriterion("description is null");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNotNull() {
            addCriterion("description is not null");
            return (Criteria) this;
        }

        public Criteria andDescriptionEqualTo(String value) {
            addCriterion("description =", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotEqualTo(String value) {
            addCriterion("description <>", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThan(String value) {
            addCriterion("description >", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("description >=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThan(String value) {
            addCriterion("description <", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThanOrEqualTo(String value) {
            addCriterion("description <=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLike(String value) {
            addCriterion("description like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotLike(String value) {
            addCriterion("description not like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionIn(List<String> values) {
            addCriterion("description in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotIn(List<String> values) {
            addCriterion("description not in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionBetween(String value1, String value2) {
            addCriterion("description between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotBetween(String value1, String value2) {
            addCriterion("description not between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andExtDescriptionIsNull() {
            addCriterion("ext_description is null");
            return (Criteria) this;
        }

        public Criteria andExtDescriptionIsNotNull() {
            addCriterion("ext_description is not null");
            return (Criteria) this;
        }

        public Criteria andExtDescriptionEqualTo(String value) {
            addCriterion("ext_description =", value, "extDescription");
            return (Criteria) this;
        }

        public Criteria andExtDescriptionNotEqualTo(String value) {
            addCriterion("ext_description <>", value, "extDescription");
            return (Criteria) this;
        }

        public Criteria andExtDescriptionGreaterThan(String value) {
            addCriterion("ext_description >", value, "extDescription");
            return (Criteria) this;
        }

        public Criteria andExtDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("ext_description >=", value, "extDescription");
            return (Criteria) this;
        }

        public Criteria andExtDescriptionLessThan(String value) {
            addCriterion("ext_description <", value, "extDescription");
            return (Criteria) this;
        }

        public Criteria andExtDescriptionLessThanOrEqualTo(String value) {
            addCriterion("ext_description <=", value, "extDescription");
            return (Criteria) this;
        }

        public Criteria andExtDescriptionLike(String value) {
            addCriterion("ext_description like", value, "extDescription");
            return (Criteria) this;
        }

        public Criteria andExtDescriptionNotLike(String value) {
            addCriterion("ext_description not like", value, "extDescription");
            return (Criteria) this;
        }

        public Criteria andExtDescriptionIn(List<String> values) {
            addCriterion("ext_description in", values, "extDescription");
            return (Criteria) this;
        }

        public Criteria andExtDescriptionNotIn(List<String> values) {
            addCriterion("ext_description not in", values, "extDescription");
            return (Criteria) this;
        }

        public Criteria andExtDescriptionBetween(String value1, String value2) {
            addCriterion("ext_description between", value1, value2, "extDescription");
            return (Criteria) this;
        }

        public Criteria andExtDescriptionNotBetween(String value1, String value2) {
            addCriterion("ext_description not between", value1, value2, "extDescription");
            return (Criteria) this;
        }

        public Criteria andImageUrlIsNull() {
            addCriterion("image_url is null");
            return (Criteria) this;
        }

        public Criteria andImageUrlIsNotNull() {
            addCriterion("image_url is not null");
            return (Criteria) this;
        }

        public Criteria andImageUrlEqualTo(String value) {
            addCriterion("image_url =", value, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlNotEqualTo(String value) {
            addCriterion("image_url <>", value, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlGreaterThan(String value) {
            addCriterion("image_url >", value, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlGreaterThanOrEqualTo(String value) {
            addCriterion("image_url >=", value, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlLessThan(String value) {
            addCriterion("image_url <", value, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlLessThanOrEqualTo(String value) {
            addCriterion("image_url <=", value, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlLike(String value) {
            addCriterion("image_url like", value, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlNotLike(String value) {
            addCriterion("image_url not like", value, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlIn(List<String> values) {
            addCriterion("image_url in", values, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlNotIn(List<String> values) {
            addCriterion("image_url not in", values, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlBetween(String value1, String value2) {
            addCriterion("image_url between", value1, value2, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlNotBetween(String value1, String value2) {
            addCriterion("image_url not between", value1, value2, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageMd5IsNull() {
            addCriterion("image_md5 is null");
            return (Criteria) this;
        }

        public Criteria andImageMd5IsNotNull() {
            addCriterion("image_md5 is not null");
            return (Criteria) this;
        }

        public Criteria andImageMd5EqualTo(String value) {
            addCriterion("image_md5 =", value, "imageMd5");
            return (Criteria) this;
        }

        public Criteria andImageMd5NotEqualTo(String value) {
            addCriterion("image_md5 <>", value, "imageMd5");
            return (Criteria) this;
        }

        public Criteria andImageMd5GreaterThan(String value) {
            addCriterion("image_md5 >", value, "imageMd5");
            return (Criteria) this;
        }

        public Criteria andImageMd5GreaterThanOrEqualTo(String value) {
            addCriterion("image_md5 >=", value, "imageMd5");
            return (Criteria) this;
        }

        public Criteria andImageMd5LessThan(String value) {
            addCriterion("image_md5 <", value, "imageMd5");
            return (Criteria) this;
        }

        public Criteria andImageMd5LessThanOrEqualTo(String value) {
            addCriterion("image_md5 <=", value, "imageMd5");
            return (Criteria) this;
        }

        public Criteria andImageMd5Like(String value) {
            addCriterion("image_md5 like", value, "imageMd5");
            return (Criteria) this;
        }

        public Criteria andImageMd5NotLike(String value) {
            addCriterion("image_md5 not like", value, "imageMd5");
            return (Criteria) this;
        }

        public Criteria andImageMd5In(List<String> values) {
            addCriterion("image_md5 in", values, "imageMd5");
            return (Criteria) this;
        }

        public Criteria andImageMd5NotIn(List<String> values) {
            addCriterion("image_md5 not in", values, "imageMd5");
            return (Criteria) this;
        }

        public Criteria andImageMd5Between(String value1, String value2) {
            addCriterion("image_md5 between", value1, value2, "imageMd5");
            return (Criteria) this;
        }

        public Criteria andImageMd5NotBetween(String value1, String value2) {
            addCriterion("image_md5 not between", value1, value2, "imageMd5");
            return (Criteria) this;
        }

        public Criteria andVideoIdIsNull() {
            addCriterion("video_id is null");
            return (Criteria) this;
        }

        public Criteria andVideoIdIsNotNull() {
            addCriterion("video_id is not null");
            return (Criteria) this;
        }

        public Criteria andVideoIdEqualTo(Long value) {
            addCriterion("video_id =", value, "videoId");
            return (Criteria) this;
        }

        public Criteria andVideoIdNotEqualTo(Long value) {
            addCriterion("video_id <>", value, "videoId");
            return (Criteria) this;
        }

        public Criteria andVideoIdGreaterThan(Long value) {
            addCriterion("video_id >", value, "videoId");
            return (Criteria) this;
        }

        public Criteria andVideoIdGreaterThanOrEqualTo(Long value) {
            addCriterion("video_id >=", value, "videoId");
            return (Criteria) this;
        }

        public Criteria andVideoIdLessThan(Long value) {
            addCriterion("video_id <", value, "videoId");
            return (Criteria) this;
        }

        public Criteria andVideoIdLessThanOrEqualTo(Long value) {
            addCriterion("video_id <=", value, "videoId");
            return (Criteria) this;
        }

        public Criteria andVideoIdIn(List<Long> values) {
            addCriterion("video_id in", values, "videoId");
            return (Criteria) this;
        }

        public Criteria andVideoIdNotIn(List<Long> values) {
            addCriterion("video_id not in", values, "videoId");
            return (Criteria) this;
        }

        public Criteria andVideoIdBetween(Long value1, Long value2) {
            addCriterion("video_id between", value1, value2, "videoId");
            return (Criteria) this;
        }

        public Criteria andVideoIdNotBetween(Long value1, Long value2) {
            addCriterion("video_id not between", value1, value2, "videoId");
            return (Criteria) this;
        }

        public Criteria andVideoUrlIsNull() {
            addCriterion("video_url is null");
            return (Criteria) this;
        }

        public Criteria andVideoUrlIsNotNull() {
            addCriterion("video_url is not null");
            return (Criteria) this;
        }

        public Criteria andVideoUrlEqualTo(String value) {
            addCriterion("video_url =", value, "videoUrl");
            return (Criteria) this;
        }

        public Criteria andVideoUrlNotEqualTo(String value) {
            addCriterion("video_url <>", value, "videoUrl");
            return (Criteria) this;
        }

        public Criteria andVideoUrlGreaterThan(String value) {
            addCriterion("video_url >", value, "videoUrl");
            return (Criteria) this;
        }

        public Criteria andVideoUrlGreaterThanOrEqualTo(String value) {
            addCriterion("video_url >=", value, "videoUrl");
            return (Criteria) this;
        }

        public Criteria andVideoUrlLessThan(String value) {
            addCriterion("video_url <", value, "videoUrl");
            return (Criteria) this;
        }

        public Criteria andVideoUrlLessThanOrEqualTo(String value) {
            addCriterion("video_url <=", value, "videoUrl");
            return (Criteria) this;
        }

        public Criteria andVideoUrlLike(String value) {
            addCriterion("video_url like", value, "videoUrl");
            return (Criteria) this;
        }

        public Criteria andVideoUrlNotLike(String value) {
            addCriterion("video_url not like", value, "videoUrl");
            return (Criteria) this;
        }

        public Criteria andVideoUrlIn(List<String> values) {
            addCriterion("video_url in", values, "videoUrl");
            return (Criteria) this;
        }

        public Criteria andVideoUrlNotIn(List<String> values) {
            addCriterion("video_url not in", values, "videoUrl");
            return (Criteria) this;
        }

        public Criteria andVideoUrlBetween(String value1, String value2) {
            addCriterion("video_url between", value1, value2, "videoUrl");
            return (Criteria) this;
        }

        public Criteria andVideoUrlNotBetween(String value1, String value2) {
            addCriterion("video_url not between", value1, value2, "videoUrl");
            return (Criteria) this;
        }

        public Criteria andExtImageUrlIsNull() {
            addCriterion("ext_image_url is null");
            return (Criteria) this;
        }

        public Criteria andExtImageUrlIsNotNull() {
            addCriterion("ext_image_url is not null");
            return (Criteria) this;
        }

        public Criteria andExtImageUrlEqualTo(String value) {
            addCriterion("ext_image_url =", value, "extImageUrl");
            return (Criteria) this;
        }

        public Criteria andExtImageUrlNotEqualTo(String value) {
            addCriterion("ext_image_url <>", value, "extImageUrl");
            return (Criteria) this;
        }

        public Criteria andExtImageUrlGreaterThan(String value) {
            addCriterion("ext_image_url >", value, "extImageUrl");
            return (Criteria) this;
        }

        public Criteria andExtImageUrlGreaterThanOrEqualTo(String value) {
            addCriterion("ext_image_url >=", value, "extImageUrl");
            return (Criteria) this;
        }

        public Criteria andExtImageUrlLessThan(String value) {
            addCriterion("ext_image_url <", value, "extImageUrl");
            return (Criteria) this;
        }

        public Criteria andExtImageUrlLessThanOrEqualTo(String value) {
            addCriterion("ext_image_url <=", value, "extImageUrl");
            return (Criteria) this;
        }

        public Criteria andExtImageUrlLike(String value) {
            addCriterion("ext_image_url like", value, "extImageUrl");
            return (Criteria) this;
        }

        public Criteria andExtImageUrlNotLike(String value) {
            addCriterion("ext_image_url not like", value, "extImageUrl");
            return (Criteria) this;
        }

        public Criteria andExtImageUrlIn(List<String> values) {
            addCriterion("ext_image_url in", values, "extImageUrl");
            return (Criteria) this;
        }

        public Criteria andExtImageUrlNotIn(List<String> values) {
            addCriterion("ext_image_url not in", values, "extImageUrl");
            return (Criteria) this;
        }

        public Criteria andExtImageUrlBetween(String value1, String value2) {
            addCriterion("ext_image_url between", value1, value2, "extImageUrl");
            return (Criteria) this;
        }

        public Criteria andExtImageUrlNotBetween(String value1, String value2) {
            addCriterion("ext_image_url not between", value1, value2, "extImageUrl");
            return (Criteria) this;
        }

        public Criteria andExtImageMd5IsNull() {
            addCriterion("ext_image_md5 is null");
            return (Criteria) this;
        }

        public Criteria andExtImageMd5IsNotNull() {
            addCriterion("ext_image_md5 is not null");
            return (Criteria) this;
        }

        public Criteria andExtImageMd5EqualTo(String value) {
            addCriterion("ext_image_md5 =", value, "extImageMd5");
            return (Criteria) this;
        }

        public Criteria andExtImageMd5NotEqualTo(String value) {
            addCriterion("ext_image_md5 <>", value, "extImageMd5");
            return (Criteria) this;
        }

        public Criteria andExtImageMd5GreaterThan(String value) {
            addCriterion("ext_image_md5 >", value, "extImageMd5");
            return (Criteria) this;
        }

        public Criteria andExtImageMd5GreaterThanOrEqualTo(String value) {
            addCriterion("ext_image_md5 >=", value, "extImageMd5");
            return (Criteria) this;
        }

        public Criteria andExtImageMd5LessThan(String value) {
            addCriterion("ext_image_md5 <", value, "extImageMd5");
            return (Criteria) this;
        }

        public Criteria andExtImageMd5LessThanOrEqualTo(String value) {
            addCriterion("ext_image_md5 <=", value, "extImageMd5");
            return (Criteria) this;
        }

        public Criteria andExtImageMd5Like(String value) {
            addCriterion("ext_image_md5 like", value, "extImageMd5");
            return (Criteria) this;
        }

        public Criteria andExtImageMd5NotLike(String value) {
            addCriterion("ext_image_md5 not like", value, "extImageMd5");
            return (Criteria) this;
        }

        public Criteria andExtImageMd5In(List<String> values) {
            addCriterion("ext_image_md5 in", values, "extImageMd5");
            return (Criteria) this;
        }

        public Criteria andExtImageMd5NotIn(List<String> values) {
            addCriterion("ext_image_md5 not in", values, "extImageMd5");
            return (Criteria) this;
        }

        public Criteria andExtImageMd5Between(String value1, String value2) {
            addCriterion("ext_image_md5 between", value1, value2, "extImageMd5");
            return (Criteria) this;
        }

        public Criteria andExtImageMd5NotBetween(String value1, String value2) {
            addCriterion("ext_image_md5 not between", value1, value2, "extImageMd5");
            return (Criteria) this;
        }

        public Criteria andCreativeJsonIsNull() {
            addCriterion("creative_json is null");
            return (Criteria) this;
        }

        public Criteria andCreativeJsonIsNotNull() {
            addCriterion("creative_json is not null");
            return (Criteria) this;
        }

        public Criteria andCreativeJsonEqualTo(String value) {
            addCriterion("creative_json =", value, "creativeJson");
            return (Criteria) this;
        }

        public Criteria andCreativeJsonNotEqualTo(String value) {
            addCriterion("creative_json <>", value, "creativeJson");
            return (Criteria) this;
        }

        public Criteria andCreativeJsonGreaterThan(String value) {
            addCriterion("creative_json >", value, "creativeJson");
            return (Criteria) this;
        }

        public Criteria andCreativeJsonGreaterThanOrEqualTo(String value) {
            addCriterion("creative_json >=", value, "creativeJson");
            return (Criteria) this;
        }

        public Criteria andCreativeJsonLessThan(String value) {
            addCriterion("creative_json <", value, "creativeJson");
            return (Criteria) this;
        }

        public Criteria andCreativeJsonLessThanOrEqualTo(String value) {
            addCriterion("creative_json <=", value, "creativeJson");
            return (Criteria) this;
        }

        public Criteria andCreativeJsonLike(String value) {
            addCriterion("creative_json like", value, "creativeJson");
            return (Criteria) this;
        }

        public Criteria andCreativeJsonNotLike(String value) {
            addCriterion("creative_json not like", value, "creativeJson");
            return (Criteria) this;
        }

        public Criteria andCreativeJsonIn(List<String> values) {
            addCriterion("creative_json in", values, "creativeJson");
            return (Criteria) this;
        }

        public Criteria andCreativeJsonNotIn(List<String> values) {
            addCriterion("creative_json not in", values, "creativeJson");
            return (Criteria) this;
        }

        public Criteria andCreativeJsonBetween(String value1, String value2) {
            addCriterion("creative_json between", value1, value2, "creativeJson");
            return (Criteria) this;
        }

        public Criteria andCreativeJsonNotBetween(String value1, String value2) {
            addCriterion("creative_json not between", value1, value2, "creativeJson");
            return (Criteria) this;
        }

        public Criteria andReasonIsNull() {
            addCriterion("reason is null");
            return (Criteria) this;
        }

        public Criteria andReasonIsNotNull() {
            addCriterion("reason is not null");
            return (Criteria) this;
        }

        public Criteria andReasonEqualTo(String value) {
            addCriterion("reason =", value, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonNotEqualTo(String value) {
            addCriterion("reason <>", value, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonGreaterThan(String value) {
            addCriterion("reason >", value, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonGreaterThanOrEqualTo(String value) {
            addCriterion("reason >=", value, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonLessThan(String value) {
            addCriterion("reason <", value, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonLessThanOrEqualTo(String value) {
            addCriterion("reason <=", value, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonLike(String value) {
            addCriterion("reason like", value, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonNotLike(String value) {
            addCriterion("reason not like", value, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonIn(List<String> values) {
            addCriterion("reason in", values, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonNotIn(List<String> values) {
            addCriterion("reason not in", values, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonBetween(String value1, String value2) {
            addCriterion("reason between", value1, value2, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonNotBetween(String value1, String value2) {
            addCriterion("reason not between", value1, value2, "reason");
            return (Criteria) this;
        }

        public Criteria andTemplateIdIsNull() {
            addCriterion("template_id is null");
            return (Criteria) this;
        }

        public Criteria andTemplateIdIsNotNull() {
            addCriterion("template_id is not null");
            return (Criteria) this;
        }

        public Criteria andTemplateIdEqualTo(Integer value) {
            addCriterion("template_id =", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdNotEqualTo(Integer value) {
            addCriterion("template_id <>", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdGreaterThan(Integer value) {
            addCriterion("template_id >", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("template_id >=", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdLessThan(Integer value) {
            addCriterion("template_id <", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdLessThanOrEqualTo(Integer value) {
            addCriterion("template_id <=", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdIn(List<Integer> values) {
            addCriterion("template_id in", values, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdNotIn(List<Integer> values) {
            addCriterion("template_id not in", values, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdBetween(Integer value1, Integer value2) {
            addCriterion("template_id between", value1, value2, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdNotBetween(Integer value1, Integer value2) {
            addCriterion("template_id not between", value1, value2, "templateId");
            return (Criteria) this;
        }

        public Criteria andAuditStatusIsNull() {
            addCriterion("audit_status is null");
            return (Criteria) this;
        }

        public Criteria andAuditStatusIsNotNull() {
            addCriterion("audit_status is not null");
            return (Criteria) this;
        }

        public Criteria andAuditStatusEqualTo(Integer value) {
            addCriterion("audit_status =", value, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andAuditStatusNotEqualTo(Integer value) {
            addCriterion("audit_status <>", value, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andAuditStatusGreaterThan(Integer value) {
            addCriterion("audit_status >", value, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andAuditStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("audit_status >=", value, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andAuditStatusLessThan(Integer value) {
            addCriterion("audit_status <", value, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andAuditStatusLessThanOrEqualTo(Integer value) {
            addCriterion("audit_status <=", value, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andAuditStatusIn(List<Integer> values) {
            addCriterion("audit_status in", values, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andAuditStatusNotIn(List<Integer> values) {
            addCriterion("audit_status not in", values, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andAuditStatusBetween(Integer value1, Integer value2) {
            addCriterion("audit_status between", value1, value2, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andAuditStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("audit_status not between", value1, value2, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andVersionIsNull() {
            addCriterion("version is null");
            return (Criteria) this;
        }

        public Criteria andVersionIsNotNull() {
            addCriterion("version is not null");
            return (Criteria) this;
        }

        public Criteria andVersionEqualTo(Integer value) {
            addCriterion("version =", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotEqualTo(Integer value) {
            addCriterion("version <>", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThan(Integer value) {
            addCriterion("version >", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThanOrEqualTo(Integer value) {
            addCriterion("version >=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThan(Integer value) {
            addCriterion("version <", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThanOrEqualTo(Integer value) {
            addCriterion("version <=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionIn(List<Integer> values) {
            addCriterion("version in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotIn(List<Integer> values) {
            addCriterion("version not in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionBetween(Integer value1, Integer value2) {
            addCriterion("version between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotBetween(Integer value1, Integer value2) {
            addCriterion("version not between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Integer value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Integer value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Integer value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Integer value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Integer value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Integer> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Integer> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNull() {
            addCriterion("ctime is null");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNotNull() {
            addCriterion("ctime is not null");
            return (Criteria) this;
        }

        public Criteria andCtimeEqualTo(Timestamp value) {
            addCriterion("ctime =", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotEqualTo(Timestamp value) {
            addCriterion("ctime <>", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThan(Timestamp value) {
            addCriterion("ctime >", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("ctime >=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThan(Timestamp value) {
            addCriterion("ctime <", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("ctime <=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeIn(List<Timestamp> values) {
            addCriterion("ctime in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotIn(List<Timestamp> values) {
            addCriterion("ctime not in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime not between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNull() {
            addCriterion("mtime is null");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNotNull() {
            addCriterion("mtime is not null");
            return (Criteria) this;
        }

        public Criteria andMtimeEqualTo(Timestamp value) {
            addCriterion("mtime =", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotEqualTo(Timestamp value) {
            addCriterion("mtime <>", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThan(Timestamp value) {
            addCriterion("mtime >", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("mtime >=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThan(Timestamp value) {
            addCriterion("mtime <", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("mtime <=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeIn(List<Timestamp> values) {
            addCriterion("mtime in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotIn(List<Timestamp> values) {
            addCriterion("mtime not in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime not between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNull() {
            addCriterion("order_id is null");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNotNull() {
            addCriterion("order_id is not null");
            return (Criteria) this;
        }

        public Criteria andOrderIdEqualTo(Integer value) {
            addCriterion("order_id =", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotEqualTo(Integer value) {
            addCriterion("order_id <>", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThan(Integer value) {
            addCriterion("order_id >", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_id >=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThan(Integer value) {
            addCriterion("order_id <", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanOrEqualTo(Integer value) {
            addCriterion("order_id <=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdIn(List<Integer> values) {
            addCriterion("order_id in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotIn(List<Integer> values) {
            addCriterion("order_id not in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdBetween(Integer value1, Integer value2) {
            addCriterion("order_id between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotBetween(Integer value1, Integer value2) {
            addCriterion("order_id not between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andSalesTypeIsNull() {
            addCriterion("sales_type is null");
            return (Criteria) this;
        }

        public Criteria andSalesTypeIsNotNull() {
            addCriterion("sales_type is not null");
            return (Criteria) this;
        }

        public Criteria andSalesTypeEqualTo(Integer value) {
            addCriterion("sales_type =", value, "salesType");
            return (Criteria) this;
        }

        public Criteria andSalesTypeNotEqualTo(Integer value) {
            addCriterion("sales_type <>", value, "salesType");
            return (Criteria) this;
        }

        public Criteria andSalesTypeGreaterThan(Integer value) {
            addCriterion("sales_type >", value, "salesType");
            return (Criteria) this;
        }

        public Criteria andSalesTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("sales_type >=", value, "salesType");
            return (Criteria) this;
        }

        public Criteria andSalesTypeLessThan(Integer value) {
            addCriterion("sales_type <", value, "salesType");
            return (Criteria) this;
        }

        public Criteria andSalesTypeLessThanOrEqualTo(Integer value) {
            addCriterion("sales_type <=", value, "salesType");
            return (Criteria) this;
        }

        public Criteria andSalesTypeIn(List<Integer> values) {
            addCriterion("sales_type in", values, "salesType");
            return (Criteria) this;
        }

        public Criteria andSalesTypeNotIn(List<Integer> values) {
            addCriterion("sales_type not in", values, "salesType");
            return (Criteria) this;
        }

        public Criteria andSalesTypeBetween(Integer value1, Integer value2) {
            addCriterion("sales_type between", value1, value2, "salesType");
            return (Criteria) this;
        }

        public Criteria andSalesTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("sales_type not between", value1, value2, "salesType");
            return (Criteria) this;
        }

        public Criteria andCmMarkIsNull() {
            addCriterion("cm_mark is null");
            return (Criteria) this;
        }

        public Criteria andCmMarkIsNotNull() {
            addCriterion("cm_mark is not null");
            return (Criteria) this;
        }

        public Criteria andCmMarkEqualTo(Short value) {
            addCriterion("cm_mark =", value, "cmMark");
            return (Criteria) this;
        }

        public Criteria andCmMarkNotEqualTo(Short value) {
            addCriterion("cm_mark <>", value, "cmMark");
            return (Criteria) this;
        }

        public Criteria andCmMarkGreaterThan(Short value) {
            addCriterion("cm_mark >", value, "cmMark");
            return (Criteria) this;
        }

        public Criteria andCmMarkGreaterThanOrEqualTo(Short value) {
            addCriterion("cm_mark >=", value, "cmMark");
            return (Criteria) this;
        }

        public Criteria andCmMarkLessThan(Short value) {
            addCriterion("cm_mark <", value, "cmMark");
            return (Criteria) this;
        }

        public Criteria andCmMarkLessThanOrEqualTo(Short value) {
            addCriterion("cm_mark <=", value, "cmMark");
            return (Criteria) this;
        }

        public Criteria andCmMarkIn(List<Short> values) {
            addCriterion("cm_mark in", values, "cmMark");
            return (Criteria) this;
        }

        public Criteria andCmMarkNotIn(List<Short> values) {
            addCriterion("cm_mark not in", values, "cmMark");
            return (Criteria) this;
        }

        public Criteria andCmMarkBetween(Short value1, Short value2) {
            addCriterion("cm_mark between", value1, value2, "cmMark");
            return (Criteria) this;
        }

        public Criteria andCmMarkNotBetween(Short value1, Short value2) {
            addCriterion("cm_mark not between", value1, value2, "cmMark");
            return (Criteria) this;
        }

        public Criteria andButtonCopyIsNull() {
            addCriterion("button_copy is null");
            return (Criteria) this;
        }

        public Criteria andButtonCopyIsNotNull() {
            addCriterion("button_copy is not null");
            return (Criteria) this;
        }

        public Criteria andButtonCopyEqualTo(String value) {
            addCriterion("button_copy =", value, "buttonCopy");
            return (Criteria) this;
        }

        public Criteria andButtonCopyNotEqualTo(String value) {
            addCriterion("button_copy <>", value, "buttonCopy");
            return (Criteria) this;
        }

        public Criteria andButtonCopyGreaterThan(String value) {
            addCriterion("button_copy >", value, "buttonCopy");
            return (Criteria) this;
        }

        public Criteria andButtonCopyGreaterThanOrEqualTo(String value) {
            addCriterion("button_copy >=", value, "buttonCopy");
            return (Criteria) this;
        }

        public Criteria andButtonCopyLessThan(String value) {
            addCriterion("button_copy <", value, "buttonCopy");
            return (Criteria) this;
        }

        public Criteria andButtonCopyLessThanOrEqualTo(String value) {
            addCriterion("button_copy <=", value, "buttonCopy");
            return (Criteria) this;
        }

        public Criteria andButtonCopyLike(String value) {
            addCriterion("button_copy like", value, "buttonCopy");
            return (Criteria) this;
        }

        public Criteria andButtonCopyNotLike(String value) {
            addCriterion("button_copy not like", value, "buttonCopy");
            return (Criteria) this;
        }

        public Criteria andButtonCopyIn(List<String> values) {
            addCriterion("button_copy in", values, "buttonCopy");
            return (Criteria) this;
        }

        public Criteria andButtonCopyNotIn(List<String> values) {
            addCriterion("button_copy not in", values, "buttonCopy");
            return (Criteria) this;
        }

        public Criteria andButtonCopyBetween(String value1, String value2) {
            addCriterion("button_copy between", value1, value2, "buttonCopy");
            return (Criteria) this;
        }

        public Criteria andButtonCopyNotBetween(String value1, String value2) {
            addCriterion("button_copy not between", value1, value2, "buttonCopy");
            return (Criteria) this;
        }

        public Criteria andCategoryFirstIdIsNull() {
            addCriterion("category_first_id is null");
            return (Criteria) this;
        }

        public Criteria andCategoryFirstIdIsNotNull() {
            addCriterion("category_first_id is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryFirstIdEqualTo(Integer value) {
            addCriterion("category_first_id =", value, "categoryFirstId");
            return (Criteria) this;
        }

        public Criteria andCategoryFirstIdNotEqualTo(Integer value) {
            addCriterion("category_first_id <>", value, "categoryFirstId");
            return (Criteria) this;
        }

        public Criteria andCategoryFirstIdGreaterThan(Integer value) {
            addCriterion("category_first_id >", value, "categoryFirstId");
            return (Criteria) this;
        }

        public Criteria andCategoryFirstIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("category_first_id >=", value, "categoryFirstId");
            return (Criteria) this;
        }

        public Criteria andCategoryFirstIdLessThan(Integer value) {
            addCriterion("category_first_id <", value, "categoryFirstId");
            return (Criteria) this;
        }

        public Criteria andCategoryFirstIdLessThanOrEqualTo(Integer value) {
            addCriterion("category_first_id <=", value, "categoryFirstId");
            return (Criteria) this;
        }

        public Criteria andCategoryFirstIdIn(List<Integer> values) {
            addCriterion("category_first_id in", values, "categoryFirstId");
            return (Criteria) this;
        }

        public Criteria andCategoryFirstIdNotIn(List<Integer> values) {
            addCriterion("category_first_id not in", values, "categoryFirstId");
            return (Criteria) this;
        }

        public Criteria andCategoryFirstIdBetween(Integer value1, Integer value2) {
            addCriterion("category_first_id between", value1, value2, "categoryFirstId");
            return (Criteria) this;
        }

        public Criteria andCategoryFirstIdNotBetween(Integer value1, Integer value2) {
            addCriterion("category_first_id not between", value1, value2, "categoryFirstId");
            return (Criteria) this;
        }

        public Criteria andCategorySecondIdIsNull() {
            addCriterion("category_second_id is null");
            return (Criteria) this;
        }

        public Criteria andCategorySecondIdIsNotNull() {
            addCriterion("category_second_id is not null");
            return (Criteria) this;
        }

        public Criteria andCategorySecondIdEqualTo(Integer value) {
            addCriterion("category_second_id =", value, "categorySecondId");
            return (Criteria) this;
        }

        public Criteria andCategorySecondIdNotEqualTo(Integer value) {
            addCriterion("category_second_id <>", value, "categorySecondId");
            return (Criteria) this;
        }

        public Criteria andCategorySecondIdGreaterThan(Integer value) {
            addCriterion("category_second_id >", value, "categorySecondId");
            return (Criteria) this;
        }

        public Criteria andCategorySecondIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("category_second_id >=", value, "categorySecondId");
            return (Criteria) this;
        }

        public Criteria andCategorySecondIdLessThan(Integer value) {
            addCriterion("category_second_id <", value, "categorySecondId");
            return (Criteria) this;
        }

        public Criteria andCategorySecondIdLessThanOrEqualTo(Integer value) {
            addCriterion("category_second_id <=", value, "categorySecondId");
            return (Criteria) this;
        }

        public Criteria andCategorySecondIdIn(List<Integer> values) {
            addCriterion("category_second_id in", values, "categorySecondId");
            return (Criteria) this;
        }

        public Criteria andCategorySecondIdNotIn(List<Integer> values) {
            addCriterion("category_second_id not in", values, "categorySecondId");
            return (Criteria) this;
        }

        public Criteria andCategorySecondIdBetween(Integer value1, Integer value2) {
            addCriterion("category_second_id between", value1, value2, "categorySecondId");
            return (Criteria) this;
        }

        public Criteria andCategorySecondIdNotBetween(Integer value1, Integer value2) {
            addCriterion("category_second_id not between", value1, value2, "categorySecondId");
            return (Criteria) this;
        }

        public Criteria andIsHistoryIsNull() {
            addCriterion("is_history is null");
            return (Criteria) this;
        }

        public Criteria andIsHistoryIsNotNull() {
            addCriterion("is_history is not null");
            return (Criteria) this;
        }

        public Criteria andIsHistoryEqualTo(Integer value) {
            addCriterion("is_history =", value, "isHistory");
            return (Criteria) this;
        }

        public Criteria andIsHistoryNotEqualTo(Integer value) {
            addCriterion("is_history <>", value, "isHistory");
            return (Criteria) this;
        }

        public Criteria andIsHistoryGreaterThan(Integer value) {
            addCriterion("is_history >", value, "isHistory");
            return (Criteria) this;
        }

        public Criteria andIsHistoryGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_history >=", value, "isHistory");
            return (Criteria) this;
        }

        public Criteria andIsHistoryLessThan(Integer value) {
            addCriterion("is_history <", value, "isHistory");
            return (Criteria) this;
        }

        public Criteria andIsHistoryLessThanOrEqualTo(Integer value) {
            addCriterion("is_history <=", value, "isHistory");
            return (Criteria) this;
        }

        public Criteria andIsHistoryIn(List<Integer> values) {
            addCriterion("is_history in", values, "isHistory");
            return (Criteria) this;
        }

        public Criteria andIsHistoryNotIn(List<Integer> values) {
            addCriterion("is_history not in", values, "isHistory");
            return (Criteria) this;
        }

        public Criteria andIsHistoryBetween(Integer value1, Integer value2) {
            addCriterion("is_history between", value1, value2, "isHistory");
            return (Criteria) this;
        }

        public Criteria andIsHistoryNotBetween(Integer value1, Integer value2) {
            addCriterion("is_history not between", value1, value2, "isHistory");
            return (Criteria) this;
        }

        public Criteria andTagsIsNull() {
            addCriterion("tags is null");
            return (Criteria) this;
        }

        public Criteria andTagsIsNotNull() {
            addCriterion("tags is not null");
            return (Criteria) this;
        }

        public Criteria andTagsEqualTo(String value) {
            addCriterion("tags =", value, "tags");
            return (Criteria) this;
        }

        public Criteria andTagsNotEqualTo(String value) {
            addCriterion("tags <>", value, "tags");
            return (Criteria) this;
        }

        public Criteria andTagsGreaterThan(String value) {
            addCriterion("tags >", value, "tags");
            return (Criteria) this;
        }

        public Criteria andTagsGreaterThanOrEqualTo(String value) {
            addCriterion("tags >=", value, "tags");
            return (Criteria) this;
        }

        public Criteria andTagsLessThan(String value) {
            addCriterion("tags <", value, "tags");
            return (Criteria) this;
        }

        public Criteria andTagsLessThanOrEqualTo(String value) {
            addCriterion("tags <=", value, "tags");
            return (Criteria) this;
        }

        public Criteria andTagsLike(String value) {
            addCriterion("tags like", value, "tags");
            return (Criteria) this;
        }

        public Criteria andTagsNotLike(String value) {
            addCriterion("tags not like", value, "tags");
            return (Criteria) this;
        }

        public Criteria andTagsIn(List<String> values) {
            addCriterion("tags in", values, "tags");
            return (Criteria) this;
        }

        public Criteria andTagsNotIn(List<String> values) {
            addCriterion("tags not in", values, "tags");
            return (Criteria) this;
        }

        public Criteria andTagsBetween(String value1, String value2) {
            addCriterion("tags between", value1, value2, "tags");
            return (Criteria) this;
        }

        public Criteria andTagsNotBetween(String value1, String value2) {
            addCriterion("tags not between", value1, value2, "tags");
            return (Criteria) this;
        }

        public Criteria andBeginTimeIsNull() {
            addCriterion("begin_time is null");
            return (Criteria) this;
        }

        public Criteria andBeginTimeIsNotNull() {
            addCriterion("begin_time is not null");
            return (Criteria) this;
        }

        public Criteria andBeginTimeEqualTo(Timestamp value) {
            addCriterion("begin_time =", value, "beginTime");
            return (Criteria) this;
        }

        public Criteria andBeginTimeNotEqualTo(Timestamp value) {
            addCriterion("begin_time <>", value, "beginTime");
            return (Criteria) this;
        }

        public Criteria andBeginTimeGreaterThan(Timestamp value) {
            addCriterion("begin_time >", value, "beginTime");
            return (Criteria) this;
        }

        public Criteria andBeginTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("begin_time >=", value, "beginTime");
            return (Criteria) this;
        }

        public Criteria andBeginTimeLessThan(Timestamp value) {
            addCriterion("begin_time <", value, "beginTime");
            return (Criteria) this;
        }

        public Criteria andBeginTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("begin_time <=", value, "beginTime");
            return (Criteria) this;
        }

        public Criteria andBeginTimeIn(List<Timestamp> values) {
            addCriterion("begin_time in", values, "beginTime");
            return (Criteria) this;
        }

        public Criteria andBeginTimeNotIn(List<Timestamp> values) {
            addCriterion("begin_time not in", values, "beginTime");
            return (Criteria) this;
        }

        public Criteria andBeginTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("begin_time between", value1, value2, "beginTime");
            return (Criteria) this;
        }

        public Criteria andBeginTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("begin_time not between", value1, value2, "beginTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeIsNull() {
            addCriterion("end_time is null");
            return (Criteria) this;
        }

        public Criteria andEndTimeIsNotNull() {
            addCriterion("end_time is not null");
            return (Criteria) this;
        }

        public Criteria andEndTimeEqualTo(Timestamp value) {
            addCriterion("end_time =", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotEqualTo(Timestamp value) {
            addCriterion("end_time <>", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeGreaterThan(Timestamp value) {
            addCriterion("end_time >", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("end_time >=", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeLessThan(Timestamp value) {
            addCriterion("end_time <", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("end_time <=", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeIn(List<Timestamp> values) {
            addCriterion("end_time in", values, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotIn(List<Timestamp> values) {
            addCriterion("end_time not in", values, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("end_time between", value1, value2, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("end_time not between", value1, value2, "endTime");
            return (Criteria) this;
        }

        public Criteria andCreativeStatusIsNull() {
            addCriterion("creative_status is null");
            return (Criteria) this;
        }

        public Criteria andCreativeStatusIsNotNull() {
            addCriterion("creative_status is not null");
            return (Criteria) this;
        }

        public Criteria andCreativeStatusEqualTo(Integer value) {
            addCriterion("creative_status =", value, "creativeStatus");
            return (Criteria) this;
        }

        public Criteria andCreativeStatusNotEqualTo(Integer value) {
            addCriterion("creative_status <>", value, "creativeStatus");
            return (Criteria) this;
        }

        public Criteria andCreativeStatusGreaterThan(Integer value) {
            addCriterion("creative_status >", value, "creativeStatus");
            return (Criteria) this;
        }

        public Criteria andCreativeStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("creative_status >=", value, "creativeStatus");
            return (Criteria) this;
        }

        public Criteria andCreativeStatusLessThan(Integer value) {
            addCriterion("creative_status <", value, "creativeStatus");
            return (Criteria) this;
        }

        public Criteria andCreativeStatusLessThanOrEqualTo(Integer value) {
            addCriterion("creative_status <=", value, "creativeStatus");
            return (Criteria) this;
        }

        public Criteria andCreativeStatusIn(List<Integer> values) {
            addCriterion("creative_status in", values, "creativeStatus");
            return (Criteria) this;
        }

        public Criteria andCreativeStatusNotIn(List<Integer> values) {
            addCriterion("creative_status not in", values, "creativeStatus");
            return (Criteria) this;
        }

        public Criteria andCreativeStatusBetween(Integer value1, Integer value2) {
            addCriterion("creative_status between", value1, value2, "creativeStatus");
            return (Criteria) this;
        }

        public Criteria andCreativeStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("creative_status not between", value1, value2, "creativeStatus");
            return (Criteria) this;
        }

        public Criteria andIsMarkIsNull() {
            addCriterion("is_mark is null");
            return (Criteria) this;
        }

        public Criteria andIsMarkIsNotNull() {
            addCriterion("is_mark is not null");
            return (Criteria) this;
        }

        public Criteria andIsMarkEqualTo(Integer value) {
            addCriterion("is_mark =", value, "isMark");
            return (Criteria) this;
        }

        public Criteria andIsMarkNotEqualTo(Integer value) {
            addCriterion("is_mark <>", value, "isMark");
            return (Criteria) this;
        }

        public Criteria andIsMarkGreaterThan(Integer value) {
            addCriterion("is_mark >", value, "isMark");
            return (Criteria) this;
        }

        public Criteria andIsMarkGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_mark >=", value, "isMark");
            return (Criteria) this;
        }

        public Criteria andIsMarkLessThan(Integer value) {
            addCriterion("is_mark <", value, "isMark");
            return (Criteria) this;
        }

        public Criteria andIsMarkLessThanOrEqualTo(Integer value) {
            addCriterion("is_mark <=", value, "isMark");
            return (Criteria) this;
        }

        public Criteria andIsMarkIn(List<Integer> values) {
            addCriterion("is_mark in", values, "isMark");
            return (Criteria) this;
        }

        public Criteria andIsMarkNotIn(List<Integer> values) {
            addCriterion("is_mark not in", values, "isMark");
            return (Criteria) this;
        }

        public Criteria andIsMarkBetween(Integer value1, Integer value2) {
            addCriterion("is_mark between", value1, value2, "isMark");
            return (Criteria) this;
        }

        public Criteria andIsMarkNotBetween(Integer value1, Integer value2) {
            addCriterion("is_mark not between", value1, value2, "isMark");
            return (Criteria) this;
        }

        public Criteria andIsTagIsNull() {
            addCriterion("is_tag is null");
            return (Criteria) this;
        }

        public Criteria andIsTagIsNotNull() {
            addCriterion("is_tag is not null");
            return (Criteria) this;
        }

        public Criteria andIsTagEqualTo(Integer value) {
            addCriterion("is_tag =", value, "isTag");
            return (Criteria) this;
        }

        public Criteria andIsTagNotEqualTo(Integer value) {
            addCriterion("is_tag <>", value, "isTag");
            return (Criteria) this;
        }

        public Criteria andIsTagGreaterThan(Integer value) {
            addCriterion("is_tag >", value, "isTag");
            return (Criteria) this;
        }

        public Criteria andIsTagGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_tag >=", value, "isTag");
            return (Criteria) this;
        }

        public Criteria andIsTagLessThan(Integer value) {
            addCriterion("is_tag <", value, "isTag");
            return (Criteria) this;
        }

        public Criteria andIsTagLessThanOrEqualTo(Integer value) {
            addCriterion("is_tag <=", value, "isTag");
            return (Criteria) this;
        }

        public Criteria andIsTagIn(List<Integer> values) {
            addCriterion("is_tag in", values, "isTag");
            return (Criteria) this;
        }

        public Criteria andIsTagNotIn(List<Integer> values) {
            addCriterion("is_tag not in", values, "isTag");
            return (Criteria) this;
        }

        public Criteria andIsTagBetween(Integer value1, Integer value2) {
            addCriterion("is_tag between", value1, value2, "isTag");
            return (Criteria) this;
        }

        public Criteria andIsTagNotBetween(Integer value1, Integer value2) {
            addCriterion("is_tag not between", value1, value2, "isTag");
            return (Criteria) this;
        }

        public Criteria andSchemeUrlIsNull() {
            addCriterion("scheme_url is null");
            return (Criteria) this;
        }

        public Criteria andSchemeUrlIsNotNull() {
            addCriterion("scheme_url is not null");
            return (Criteria) this;
        }

        public Criteria andSchemeUrlEqualTo(String value) {
            addCriterion("scheme_url =", value, "schemeUrl");
            return (Criteria) this;
        }

        public Criteria andSchemeUrlNotEqualTo(String value) {
            addCriterion("scheme_url <>", value, "schemeUrl");
            return (Criteria) this;
        }

        public Criteria andSchemeUrlGreaterThan(String value) {
            addCriterion("scheme_url >", value, "schemeUrl");
            return (Criteria) this;
        }

        public Criteria andSchemeUrlGreaterThanOrEqualTo(String value) {
            addCriterion("scheme_url >=", value, "schemeUrl");
            return (Criteria) this;
        }

        public Criteria andSchemeUrlLessThan(String value) {
            addCriterion("scheme_url <", value, "schemeUrl");
            return (Criteria) this;
        }

        public Criteria andSchemeUrlLessThanOrEqualTo(String value) {
            addCriterion("scheme_url <=", value, "schemeUrl");
            return (Criteria) this;
        }

        public Criteria andSchemeUrlLike(String value) {
            addCriterion("scheme_url like", value, "schemeUrl");
            return (Criteria) this;
        }

        public Criteria andSchemeUrlNotLike(String value) {
            addCriterion("scheme_url not like", value, "schemeUrl");
            return (Criteria) this;
        }

        public Criteria andSchemeUrlIn(List<String> values) {
            addCriterion("scheme_url in", values, "schemeUrl");
            return (Criteria) this;
        }

        public Criteria andSchemeUrlNotIn(List<String> values) {
            addCriterion("scheme_url not in", values, "schemeUrl");
            return (Criteria) this;
        }

        public Criteria andSchemeUrlBetween(String value1, String value2) {
            addCriterion("scheme_url between", value1, value2, "schemeUrl");
            return (Criteria) this;
        }

        public Criteria andSchemeUrlNotBetween(String value1, String value2) {
            addCriterion("scheme_url not between", value1, value2, "schemeUrl");
            return (Criteria) this;
        }

        public Criteria andJumpTypeIsNull() {
            addCriterion("jump_type is null");
            return (Criteria) this;
        }

        public Criteria andJumpTypeIsNotNull() {
            addCriterion("jump_type is not null");
            return (Criteria) this;
        }

        public Criteria andJumpTypeEqualTo(Integer value) {
            addCriterion("jump_type =", value, "jumpType");
            return (Criteria) this;
        }

        public Criteria andJumpTypeNotEqualTo(Integer value) {
            addCriterion("jump_type <>", value, "jumpType");
            return (Criteria) this;
        }

        public Criteria andJumpTypeGreaterThan(Integer value) {
            addCriterion("jump_type >", value, "jumpType");
            return (Criteria) this;
        }

        public Criteria andJumpTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("jump_type >=", value, "jumpType");
            return (Criteria) this;
        }

        public Criteria andJumpTypeLessThan(Integer value) {
            addCriterion("jump_type <", value, "jumpType");
            return (Criteria) this;
        }

        public Criteria andJumpTypeLessThanOrEqualTo(Integer value) {
            addCriterion("jump_type <=", value, "jumpType");
            return (Criteria) this;
        }

        public Criteria andJumpTypeIn(List<Integer> values) {
            addCriterion("jump_type in", values, "jumpType");
            return (Criteria) this;
        }

        public Criteria andJumpTypeNotIn(List<Integer> values) {
            addCriterion("jump_type not in", values, "jumpType");
            return (Criteria) this;
        }

        public Criteria andJumpTypeBetween(Integer value1, Integer value2) {
            addCriterion("jump_type between", value1, value2, "jumpType");
            return (Criteria) this;
        }

        public Criteria andJumpTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("jump_type not between", value1, value2, "jumpType");
            return (Criteria) this;
        }

        public Criteria andBilibiliUserIdIsNull() {
            addCriterion("bilibili_user_id is null");
            return (Criteria) this;
        }

        public Criteria andBilibiliUserIdIsNotNull() {
            addCriterion("bilibili_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andBilibiliUserIdEqualTo(Integer value) {
            addCriterion("bilibili_user_id =", value, "bilibiliUserId");
            return (Criteria) this;
        }

        public Criteria andBilibiliUserIdNotEqualTo(Integer value) {
            addCriterion("bilibili_user_id <>", value, "bilibiliUserId");
            return (Criteria) this;
        }

        public Criteria andBilibiliUserIdGreaterThan(Integer value) {
            addCriterion("bilibili_user_id >", value, "bilibiliUserId");
            return (Criteria) this;
        }

        public Criteria andBilibiliUserIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("bilibili_user_id >=", value, "bilibiliUserId");
            return (Criteria) this;
        }

        public Criteria andBilibiliUserIdLessThan(Integer value) {
            addCriterion("bilibili_user_id <", value, "bilibiliUserId");
            return (Criteria) this;
        }

        public Criteria andBilibiliUserIdLessThanOrEqualTo(Integer value) {
            addCriterion("bilibili_user_id <=", value, "bilibiliUserId");
            return (Criteria) this;
        }

        public Criteria andBilibiliUserIdIn(List<Integer> values) {
            addCriterion("bilibili_user_id in", values, "bilibiliUserId");
            return (Criteria) this;
        }

        public Criteria andBilibiliUserIdNotIn(List<Integer> values) {
            addCriterion("bilibili_user_id not in", values, "bilibiliUserId");
            return (Criteria) this;
        }

        public Criteria andBilibiliUserIdBetween(Integer value1, Integer value2) {
            addCriterion("bilibili_user_id between", value1, value2, "bilibiliUserId");
            return (Criteria) this;
        }

        public Criteria andBilibiliUserIdNotBetween(Integer value1, Integer value2) {
            addCriterion("bilibili_user_id not between", value1, value2, "bilibiliUserId");
            return (Criteria) this;
        }

        public Criteria andAdVersionControllIdIsNull() {
            addCriterion("ad_version_controll_id is null");
            return (Criteria) this;
        }

        public Criteria andAdVersionControllIdIsNotNull() {
            addCriterion("ad_version_controll_id is not null");
            return (Criteria) this;
        }

        public Criteria andAdVersionControllIdEqualTo(Integer value) {
            addCriterion("ad_version_controll_id =", value, "adVersionControllId");
            return (Criteria) this;
        }

        public Criteria andAdVersionControllIdNotEqualTo(Integer value) {
            addCriterion("ad_version_controll_id <>", value, "adVersionControllId");
            return (Criteria) this;
        }

        public Criteria andAdVersionControllIdGreaterThan(Integer value) {
            addCriterion("ad_version_controll_id >", value, "adVersionControllId");
            return (Criteria) this;
        }

        public Criteria andAdVersionControllIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("ad_version_controll_id >=", value, "adVersionControllId");
            return (Criteria) this;
        }

        public Criteria andAdVersionControllIdLessThan(Integer value) {
            addCriterion("ad_version_controll_id <", value, "adVersionControllId");
            return (Criteria) this;
        }

        public Criteria andAdVersionControllIdLessThanOrEqualTo(Integer value) {
            addCriterion("ad_version_controll_id <=", value, "adVersionControllId");
            return (Criteria) this;
        }

        public Criteria andAdVersionControllIdIn(List<Integer> values) {
            addCriterion("ad_version_controll_id in", values, "adVersionControllId");
            return (Criteria) this;
        }

        public Criteria andAdVersionControllIdNotIn(List<Integer> values) {
            addCriterion("ad_version_controll_id not in", values, "adVersionControllId");
            return (Criteria) this;
        }

        public Criteria andAdVersionControllIdBetween(Integer value1, Integer value2) {
            addCriterion("ad_version_controll_id between", value1, value2, "adVersionControllId");
            return (Criteria) this;
        }

        public Criteria andAdVersionControllIdNotBetween(Integer value1, Integer value2) {
            addCriterion("ad_version_controll_id not between", value1, value2, "adVersionControllId");
            return (Criteria) this;
        }

        public Criteria andMgkPageIdIsNull() {
            addCriterion("mgk_page_id is null");
            return (Criteria) this;
        }

        public Criteria andMgkPageIdIsNotNull() {
            addCriterion("mgk_page_id is not null");
            return (Criteria) this;
        }

        public Criteria andMgkPageIdEqualTo(Long value) {
            addCriterion("mgk_page_id =", value, "mgkPageId");
            return (Criteria) this;
        }

        public Criteria andMgkPageIdNotEqualTo(Long value) {
            addCriterion("mgk_page_id <>", value, "mgkPageId");
            return (Criteria) this;
        }

        public Criteria andMgkPageIdGreaterThan(Long value) {
            addCriterion("mgk_page_id >", value, "mgkPageId");
            return (Criteria) this;
        }

        public Criteria andMgkPageIdGreaterThanOrEqualTo(Long value) {
            addCriterion("mgk_page_id >=", value, "mgkPageId");
            return (Criteria) this;
        }

        public Criteria andMgkPageIdLessThan(Long value) {
            addCriterion("mgk_page_id <", value, "mgkPageId");
            return (Criteria) this;
        }

        public Criteria andMgkPageIdLessThanOrEqualTo(Long value) {
            addCriterion("mgk_page_id <=", value, "mgkPageId");
            return (Criteria) this;
        }

        public Criteria andMgkPageIdIn(List<Long> values) {
            addCriterion("mgk_page_id in", values, "mgkPageId");
            return (Criteria) this;
        }

        public Criteria andMgkPageIdNotIn(List<Long> values) {
            addCriterion("mgk_page_id not in", values, "mgkPageId");
            return (Criteria) this;
        }

        public Criteria andMgkPageIdBetween(Long value1, Long value2) {
            addCriterion("mgk_page_id between", value1, value2, "mgkPageId");
            return (Criteria) this;
        }

        public Criteria andMgkPageIdNotBetween(Long value1, Long value2) {
            addCriterion("mgk_page_id not between", value1, value2, "mgkPageId");
            return (Criteria) this;
        }

        public Criteria andAdMarkIsNull() {
            addCriterion("ad_mark is null");
            return (Criteria) this;
        }

        public Criteria andAdMarkIsNotNull() {
            addCriterion("ad_mark is not null");
            return (Criteria) this;
        }

        public Criteria andAdMarkEqualTo(String value) {
            addCriterion("ad_mark =", value, "adMark");
            return (Criteria) this;
        }

        public Criteria andAdMarkNotEqualTo(String value) {
            addCriterion("ad_mark <>", value, "adMark");
            return (Criteria) this;
        }

        public Criteria andAdMarkGreaterThan(String value) {
            addCriterion("ad_mark >", value, "adMark");
            return (Criteria) this;
        }

        public Criteria andAdMarkGreaterThanOrEqualTo(String value) {
            addCriterion("ad_mark >=", value, "adMark");
            return (Criteria) this;
        }

        public Criteria andAdMarkLessThan(String value) {
            addCriterion("ad_mark <", value, "adMark");
            return (Criteria) this;
        }

        public Criteria andAdMarkLessThanOrEqualTo(String value) {
            addCriterion("ad_mark <=", value, "adMark");
            return (Criteria) this;
        }

        public Criteria andAdMarkLike(String value) {
            addCriterion("ad_mark like", value, "adMark");
            return (Criteria) this;
        }

        public Criteria andAdMarkNotLike(String value) {
            addCriterion("ad_mark not like", value, "adMark");
            return (Criteria) this;
        }

        public Criteria andAdMarkIn(List<String> values) {
            addCriterion("ad_mark in", values, "adMark");
            return (Criteria) this;
        }

        public Criteria andAdMarkNotIn(List<String> values) {
            addCriterion("ad_mark not in", values, "adMark");
            return (Criteria) this;
        }

        public Criteria andAdMarkBetween(String value1, String value2) {
            addCriterion("ad_mark between", value1, value2, "adMark");
            return (Criteria) this;
        }

        public Criteria andAdMarkNotBetween(String value1, String value2) {
            addCriterion("ad_mark not between", value1, value2, "adMark");
            return (Criteria) this;
        }

        public Criteria andModifyOfflineCreativeIdIsNull() {
            addCriterion("modify_offline_creative_id is null");
            return (Criteria) this;
        }

        public Criteria andModifyOfflineCreativeIdIsNotNull() {
            addCriterion("modify_offline_creative_id is not null");
            return (Criteria) this;
        }

        public Criteria andModifyOfflineCreativeIdEqualTo(Integer value) {
            addCriterion("modify_offline_creative_id =", value, "modifyOfflineCreativeId");
            return (Criteria) this;
        }

        public Criteria andModifyOfflineCreativeIdNotEqualTo(Integer value) {
            addCriterion("modify_offline_creative_id <>", value, "modifyOfflineCreativeId");
            return (Criteria) this;
        }

        public Criteria andModifyOfflineCreativeIdGreaterThan(Integer value) {
            addCriterion("modify_offline_creative_id >", value, "modifyOfflineCreativeId");
            return (Criteria) this;
        }

        public Criteria andModifyOfflineCreativeIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("modify_offline_creative_id >=", value, "modifyOfflineCreativeId");
            return (Criteria) this;
        }

        public Criteria andModifyOfflineCreativeIdLessThan(Integer value) {
            addCriterion("modify_offline_creative_id <", value, "modifyOfflineCreativeId");
            return (Criteria) this;
        }

        public Criteria andModifyOfflineCreativeIdLessThanOrEqualTo(Integer value) {
            addCriterion("modify_offline_creative_id <=", value, "modifyOfflineCreativeId");
            return (Criteria) this;
        }

        public Criteria andModifyOfflineCreativeIdIn(List<Integer> values) {
            addCriterion("modify_offline_creative_id in", values, "modifyOfflineCreativeId");
            return (Criteria) this;
        }

        public Criteria andModifyOfflineCreativeIdNotIn(List<Integer> values) {
            addCriterion("modify_offline_creative_id not in", values, "modifyOfflineCreativeId");
            return (Criteria) this;
        }

        public Criteria andModifyOfflineCreativeIdBetween(Integer value1, Integer value2) {
            addCriterion("modify_offline_creative_id between", value1, value2, "modifyOfflineCreativeId");
            return (Criteria) this;
        }

        public Criteria andModifyOfflineCreativeIdNotBetween(Integer value1, Integer value2) {
            addCriterion("modify_offline_creative_id not between", value1, value2, "modifyOfflineCreativeId");
            return (Criteria) this;
        }

        public Criteria andFlowWeightStateIsNull() {
            addCriterion("flow_weight_state is null");
            return (Criteria) this;
        }

        public Criteria andFlowWeightStateIsNotNull() {
            addCriterion("flow_weight_state is not null");
            return (Criteria) this;
        }

        public Criteria andFlowWeightStateEqualTo(Integer value) {
            addCriterion("flow_weight_state =", value, "flowWeightState");
            return (Criteria) this;
        }

        public Criteria andFlowWeightStateNotEqualTo(Integer value) {
            addCriterion("flow_weight_state <>", value, "flowWeightState");
            return (Criteria) this;
        }

        public Criteria andFlowWeightStateGreaterThan(Integer value) {
            addCriterion("flow_weight_state >", value, "flowWeightState");
            return (Criteria) this;
        }

        public Criteria andFlowWeightStateGreaterThanOrEqualTo(Integer value) {
            addCriterion("flow_weight_state >=", value, "flowWeightState");
            return (Criteria) this;
        }

        public Criteria andFlowWeightStateLessThan(Integer value) {
            addCriterion("flow_weight_state <", value, "flowWeightState");
            return (Criteria) this;
        }

        public Criteria andFlowWeightStateLessThanOrEqualTo(Integer value) {
            addCriterion("flow_weight_state <=", value, "flowWeightState");
            return (Criteria) this;
        }

        public Criteria andFlowWeightStateIn(List<Integer> values) {
            addCriterion("flow_weight_state in", values, "flowWeightState");
            return (Criteria) this;
        }

        public Criteria andFlowWeightStateNotIn(List<Integer> values) {
            addCriterion("flow_weight_state not in", values, "flowWeightState");
            return (Criteria) this;
        }

        public Criteria andFlowWeightStateBetween(Integer value1, Integer value2) {
            addCriterion("flow_weight_state between", value1, value2, "flowWeightState");
            return (Criteria) this;
        }

        public Criteria andFlowWeightStateNotBetween(Integer value1, Integer value2) {
            addCriterion("flow_weight_state not between", value1, value2, "flowWeightState");
            return (Criteria) this;
        }

        public Criteria andBusMarkIdIsNull() {
            addCriterion("bus_mark_id is null");
            return (Criteria) this;
        }

        public Criteria andBusMarkIdIsNotNull() {
            addCriterion("bus_mark_id is not null");
            return (Criteria) this;
        }

        public Criteria andBusMarkIdEqualTo(Integer value) {
            addCriterion("bus_mark_id =", value, "busMarkId");
            return (Criteria) this;
        }

        public Criteria andBusMarkIdNotEqualTo(Integer value) {
            addCriterion("bus_mark_id <>", value, "busMarkId");
            return (Criteria) this;
        }

        public Criteria andBusMarkIdGreaterThan(Integer value) {
            addCriterion("bus_mark_id >", value, "busMarkId");
            return (Criteria) this;
        }

        public Criteria andBusMarkIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("bus_mark_id >=", value, "busMarkId");
            return (Criteria) this;
        }

        public Criteria andBusMarkIdLessThan(Integer value) {
            addCriterion("bus_mark_id <", value, "busMarkId");
            return (Criteria) this;
        }

        public Criteria andBusMarkIdLessThanOrEqualTo(Integer value) {
            addCriterion("bus_mark_id <=", value, "busMarkId");
            return (Criteria) this;
        }

        public Criteria andBusMarkIdIn(List<Integer> values) {
            addCriterion("bus_mark_id in", values, "busMarkId");
            return (Criteria) this;
        }

        public Criteria andBusMarkIdNotIn(List<Integer> values) {
            addCriterion("bus_mark_id not in", values, "busMarkId");
            return (Criteria) this;
        }

        public Criteria andBusMarkIdBetween(Integer value1, Integer value2) {
            addCriterion("bus_mark_id between", value1, value2, "busMarkId");
            return (Criteria) this;
        }

        public Criteria andBusMarkIdNotBetween(Integer value1, Integer value2) {
            addCriterion("bus_mark_id not between", value1, value2, "busMarkId");
            return (Criteria) this;
        }

        public Criteria andStyleAbilityIsNull() {
            addCriterion("style_ability is null");
            return (Criteria) this;
        }

        public Criteria andStyleAbilityIsNotNull() {
            addCriterion("style_ability is not null");
            return (Criteria) this;
        }

        public Criteria andStyleAbilityEqualTo(Integer value) {
            addCriterion("style_ability =", value, "styleAbility");
            return (Criteria) this;
        }

        public Criteria andStyleAbilityNotEqualTo(Integer value) {
            addCriterion("style_ability <>", value, "styleAbility");
            return (Criteria) this;
        }

        public Criteria andStyleAbilityGreaterThan(Integer value) {
            addCriterion("style_ability >", value, "styleAbility");
            return (Criteria) this;
        }

        public Criteria andStyleAbilityGreaterThanOrEqualTo(Integer value) {
            addCriterion("style_ability >=", value, "styleAbility");
            return (Criteria) this;
        }

        public Criteria andStyleAbilityLessThan(Integer value) {
            addCriterion("style_ability <", value, "styleAbility");
            return (Criteria) this;
        }

        public Criteria andStyleAbilityLessThanOrEqualTo(Integer value) {
            addCriterion("style_ability <=", value, "styleAbility");
            return (Criteria) this;
        }

        public Criteria andStyleAbilityIn(List<Integer> values) {
            addCriterion("style_ability in", values, "styleAbility");
            return (Criteria) this;
        }

        public Criteria andStyleAbilityNotIn(List<Integer> values) {
            addCriterion("style_ability not in", values, "styleAbility");
            return (Criteria) this;
        }

        public Criteria andStyleAbilityBetween(Integer value1, Integer value2) {
            addCriterion("style_ability between", value1, value2, "styleAbility");
            return (Criteria) this;
        }

        public Criteria andStyleAbilityNotBetween(Integer value1, Integer value2) {
            addCriterion("style_ability not between", value1, value2, "styleAbility");
            return (Criteria) this;
        }

        public Criteria andAdpVersionIsNull() {
            addCriterion("adp_version is null");
            return (Criteria) this;
        }

        public Criteria andAdpVersionIsNotNull() {
            addCriterion("adp_version is not null");
            return (Criteria) this;
        }

        public Criteria andAdpVersionEqualTo(Integer value) {
            addCriterion("adp_version =", value, "adpVersion");
            return (Criteria) this;
        }

        public Criteria andAdpVersionNotEqualTo(Integer value) {
            addCriterion("adp_version <>", value, "adpVersion");
            return (Criteria) this;
        }

        public Criteria andAdpVersionGreaterThan(Integer value) {
            addCriterion("adp_version >", value, "adpVersion");
            return (Criteria) this;
        }

        public Criteria andAdpVersionGreaterThanOrEqualTo(Integer value) {
            addCriterion("adp_version >=", value, "adpVersion");
            return (Criteria) this;
        }

        public Criteria andAdpVersionLessThan(Integer value) {
            addCriterion("adp_version <", value, "adpVersion");
            return (Criteria) this;
        }

        public Criteria andAdpVersionLessThanOrEqualTo(Integer value) {
            addCriterion("adp_version <=", value, "adpVersion");
            return (Criteria) this;
        }

        public Criteria andAdpVersionIn(List<Integer> values) {
            addCriterion("adp_version in", values, "adpVersion");
            return (Criteria) this;
        }

        public Criteria andAdpVersionNotIn(List<Integer> values) {
            addCriterion("adp_version not in", values, "adpVersion");
            return (Criteria) this;
        }

        public Criteria andAdpVersionBetween(Integer value1, Integer value2) {
            addCriterion("adp_version between", value1, value2, "adpVersion");
            return (Criteria) this;
        }

        public Criteria andAdpVersionNotBetween(Integer value1, Integer value2) {
            addCriterion("adp_version not between", value1, value2, "adpVersion");
            return (Criteria) this;
        }

        public Criteria andTemplateGroupIdIsNull() {
            addCriterion("template_group_id is null");
            return (Criteria) this;
        }

        public Criteria andTemplateGroupIdIsNotNull() {
            addCriterion("template_group_id is not null");
            return (Criteria) this;
        }

        public Criteria andTemplateGroupIdEqualTo(Integer value) {
            addCriterion("template_group_id =", value, "templateGroupId");
            return (Criteria) this;
        }

        public Criteria andTemplateGroupIdNotEqualTo(Integer value) {
            addCriterion("template_group_id <>", value, "templateGroupId");
            return (Criteria) this;
        }

        public Criteria andTemplateGroupIdGreaterThan(Integer value) {
            addCriterion("template_group_id >", value, "templateGroupId");
            return (Criteria) this;
        }

        public Criteria andTemplateGroupIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("template_group_id >=", value, "templateGroupId");
            return (Criteria) this;
        }

        public Criteria andTemplateGroupIdLessThan(Integer value) {
            addCriterion("template_group_id <", value, "templateGroupId");
            return (Criteria) this;
        }

        public Criteria andTemplateGroupIdLessThanOrEqualTo(Integer value) {
            addCriterion("template_group_id <=", value, "templateGroupId");
            return (Criteria) this;
        }

        public Criteria andTemplateGroupIdIn(List<Integer> values) {
            addCriterion("template_group_id in", values, "templateGroupId");
            return (Criteria) this;
        }

        public Criteria andTemplateGroupIdNotIn(List<Integer> values) {
            addCriterion("template_group_id not in", values, "templateGroupId");
            return (Criteria) this;
        }

        public Criteria andTemplateGroupIdBetween(Integer value1, Integer value2) {
            addCriterion("template_group_id between", value1, value2, "templateGroupId");
            return (Criteria) this;
        }

        public Criteria andTemplateGroupIdNotBetween(Integer value1, Integer value2) {
            addCriterion("template_group_id not between", value1, value2, "templateGroupId");
            return (Criteria) this;
        }

        public Criteria andPreferSceneIsNull() {
            addCriterion("prefer_scene is null");
            return (Criteria) this;
        }

        public Criteria andPreferSceneIsNotNull() {
            addCriterion("prefer_scene is not null");
            return (Criteria) this;
        }

        public Criteria andPreferSceneEqualTo(Integer value) {
            addCriterion("prefer_scene =", value, "preferScene");
            return (Criteria) this;
        }

        public Criteria andPreferSceneNotEqualTo(Integer value) {
            addCriterion("prefer_scene <>", value, "preferScene");
            return (Criteria) this;
        }

        public Criteria andPreferSceneGreaterThan(Integer value) {
            addCriterion("prefer_scene >", value, "preferScene");
            return (Criteria) this;
        }

        public Criteria andPreferSceneGreaterThanOrEqualTo(Integer value) {
            addCriterion("prefer_scene >=", value, "preferScene");
            return (Criteria) this;
        }

        public Criteria andPreferSceneLessThan(Integer value) {
            addCriterion("prefer_scene <", value, "preferScene");
            return (Criteria) this;
        }

        public Criteria andPreferSceneLessThanOrEqualTo(Integer value) {
            addCriterion("prefer_scene <=", value, "preferScene");
            return (Criteria) this;
        }

        public Criteria andPreferSceneIn(List<Integer> values) {
            addCriterion("prefer_scene in", values, "preferScene");
            return (Criteria) this;
        }

        public Criteria andPreferSceneNotIn(List<Integer> values) {
            addCriterion("prefer_scene not in", values, "preferScene");
            return (Criteria) this;
        }

        public Criteria andPreferSceneBetween(Integer value1, Integer value2) {
            addCriterion("prefer_scene between", value1, value2, "preferScene");
            return (Criteria) this;
        }

        public Criteria andPreferSceneNotBetween(Integer value1, Integer value2) {
            addCriterion("prefer_scene not between", value1, value2, "preferScene");
            return (Criteria) this;
        }

        public Criteria andIsProgrammaticIsNull() {
            addCriterion("is_programmatic is null");
            return (Criteria) this;
        }

        public Criteria andIsProgrammaticIsNotNull() {
            addCriterion("is_programmatic is not null");
            return (Criteria) this;
        }

        public Criteria andIsProgrammaticEqualTo(Integer value) {
            addCriterion("is_programmatic =", value, "isProgrammatic");
            return (Criteria) this;
        }

        public Criteria andIsProgrammaticNotEqualTo(Integer value) {
            addCriterion("is_programmatic <>", value, "isProgrammatic");
            return (Criteria) this;
        }

        public Criteria andIsProgrammaticGreaterThan(Integer value) {
            addCriterion("is_programmatic >", value, "isProgrammatic");
            return (Criteria) this;
        }

        public Criteria andIsProgrammaticGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_programmatic >=", value, "isProgrammatic");
            return (Criteria) this;
        }

        public Criteria andIsProgrammaticLessThan(Integer value) {
            addCriterion("is_programmatic <", value, "isProgrammatic");
            return (Criteria) this;
        }

        public Criteria andIsProgrammaticLessThanOrEqualTo(Integer value) {
            addCriterion("is_programmatic <=", value, "isProgrammatic");
            return (Criteria) this;
        }

        public Criteria andIsProgrammaticIn(List<Integer> values) {
            addCriterion("is_programmatic in", values, "isProgrammatic");
            return (Criteria) this;
        }

        public Criteria andIsProgrammaticNotIn(List<Integer> values) {
            addCriterion("is_programmatic not in", values, "isProgrammatic");
            return (Criteria) this;
        }

        public Criteria andIsProgrammaticBetween(Integer value1, Integer value2) {
            addCriterion("is_programmatic between", value1, value2, "isProgrammatic");
            return (Criteria) this;
        }

        public Criteria andIsProgrammaticNotBetween(Integer value1, Integer value2) {
            addCriterion("is_programmatic not between", value1, value2, "isProgrammatic");
            return (Criteria) this;
        }

        public Criteria andMaterialIdIsNull() {
            addCriterion("material_id is null");
            return (Criteria) this;
        }

        public Criteria andMaterialIdIsNotNull() {
            addCriterion("material_id is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialIdEqualTo(Long value) {
            addCriterion("material_id =", value, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdNotEqualTo(Long value) {
            addCriterion("material_id <>", value, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdGreaterThan(Long value) {
            addCriterion("material_id >", value, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdGreaterThanOrEqualTo(Long value) {
            addCriterion("material_id >=", value, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdLessThan(Long value) {
            addCriterion("material_id <", value, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdLessThanOrEqualTo(Long value) {
            addCriterion("material_id <=", value, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdIn(List<Long> values) {
            addCriterion("material_id in", values, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdNotIn(List<Long> values) {
            addCriterion("material_id not in", values, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdBetween(Long value1, Long value2) {
            addCriterion("material_id between", value1, value2, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdNotBetween(Long value1, Long value2) {
            addCriterion("material_id not between", value1, value2, "materialId");
            return (Criteria) this;
        }

        public Criteria andTitleIdIsNull() {
            addCriterion("title_id is null");
            return (Criteria) this;
        }

        public Criteria andTitleIdIsNotNull() {
            addCriterion("title_id is not null");
            return (Criteria) this;
        }

        public Criteria andTitleIdEqualTo(Long value) {
            addCriterion("title_id =", value, "titleId");
            return (Criteria) this;
        }

        public Criteria andTitleIdNotEqualTo(Long value) {
            addCriterion("title_id <>", value, "titleId");
            return (Criteria) this;
        }

        public Criteria andTitleIdGreaterThan(Long value) {
            addCriterion("title_id >", value, "titleId");
            return (Criteria) this;
        }

        public Criteria andTitleIdGreaterThanOrEqualTo(Long value) {
            addCriterion("title_id >=", value, "titleId");
            return (Criteria) this;
        }

        public Criteria andTitleIdLessThan(Long value) {
            addCriterion("title_id <", value, "titleId");
            return (Criteria) this;
        }

        public Criteria andTitleIdLessThanOrEqualTo(Long value) {
            addCriterion("title_id <=", value, "titleId");
            return (Criteria) this;
        }

        public Criteria andTitleIdIn(List<Long> values) {
            addCriterion("title_id in", values, "titleId");
            return (Criteria) this;
        }

        public Criteria andTitleIdNotIn(List<Long> values) {
            addCriterion("title_id not in", values, "titleId");
            return (Criteria) this;
        }

        public Criteria andTitleIdBetween(Long value1, Long value2) {
            addCriterion("title_id between", value1, value2, "titleId");
            return (Criteria) this;
        }

        public Criteria andTitleIdNotBetween(Long value1, Long value2) {
            addCriterion("title_id not between", value1, value2, "titleId");
            return (Criteria) this;
        }

        public Criteria andAutoAuditFlagIsNull() {
            addCriterion("auto_audit_flag is null");
            return (Criteria) this;
        }

        public Criteria andAutoAuditFlagIsNotNull() {
            addCriterion("auto_audit_flag is not null");
            return (Criteria) this;
        }

        public Criteria andAutoAuditFlagEqualTo(Integer value) {
            addCriterion("auto_audit_flag =", value, "autoAuditFlag");
            return (Criteria) this;
        }

        public Criteria andAutoAuditFlagNotEqualTo(Integer value) {
            addCriterion("auto_audit_flag <>", value, "autoAuditFlag");
            return (Criteria) this;
        }

        public Criteria andAutoAuditFlagGreaterThan(Integer value) {
            addCriterion("auto_audit_flag >", value, "autoAuditFlag");
            return (Criteria) this;
        }

        public Criteria andAutoAuditFlagGreaterThanOrEqualTo(Integer value) {
            addCriterion("auto_audit_flag >=", value, "autoAuditFlag");
            return (Criteria) this;
        }

        public Criteria andAutoAuditFlagLessThan(Integer value) {
            addCriterion("auto_audit_flag <", value, "autoAuditFlag");
            return (Criteria) this;
        }

        public Criteria andAutoAuditFlagLessThanOrEqualTo(Integer value) {
            addCriterion("auto_audit_flag <=", value, "autoAuditFlag");
            return (Criteria) this;
        }

        public Criteria andAutoAuditFlagIn(List<Integer> values) {
            addCriterion("auto_audit_flag in", values, "autoAuditFlag");
            return (Criteria) this;
        }

        public Criteria andAutoAuditFlagNotIn(List<Integer> values) {
            addCriterion("auto_audit_flag not in", values, "autoAuditFlag");
            return (Criteria) this;
        }

        public Criteria andAutoAuditFlagBetween(Integer value1, Integer value2) {
            addCriterion("auto_audit_flag between", value1, value2, "autoAuditFlag");
            return (Criteria) this;
        }

        public Criteria andAutoAuditFlagNotBetween(Integer value1, Integer value2) {
            addCriterion("auto_audit_flag not between", value1, value2, "autoAuditFlag");
            return (Criteria) this;
        }

        public Criteria andIsNewFlyIsNull() {
            addCriterion("is_new_fly is null");
            return (Criteria) this;
        }

        public Criteria andIsNewFlyIsNotNull() {
            addCriterion("is_new_fly is not null");
            return (Criteria) this;
        }

        public Criteria andIsNewFlyEqualTo(Integer value) {
            addCriterion("is_new_fly =", value, "isNewFly");
            return (Criteria) this;
        }

        public Criteria andIsNewFlyNotEqualTo(Integer value) {
            addCriterion("is_new_fly <>", value, "isNewFly");
            return (Criteria) this;
        }

        public Criteria andIsNewFlyGreaterThan(Integer value) {
            addCriterion("is_new_fly >", value, "isNewFly");
            return (Criteria) this;
        }

        public Criteria andIsNewFlyGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_new_fly >=", value, "isNewFly");
            return (Criteria) this;
        }

        public Criteria andIsNewFlyLessThan(Integer value) {
            addCriterion("is_new_fly <", value, "isNewFly");
            return (Criteria) this;
        }

        public Criteria andIsNewFlyLessThanOrEqualTo(Integer value) {
            addCriterion("is_new_fly <=", value, "isNewFly");
            return (Criteria) this;
        }

        public Criteria andIsNewFlyIn(List<Integer> values) {
            addCriterion("is_new_fly in", values, "isNewFly");
            return (Criteria) this;
        }

        public Criteria andIsNewFlyNotIn(List<Integer> values) {
            addCriterion("is_new_fly not in", values, "isNewFly");
            return (Criteria) this;
        }

        public Criteria andIsNewFlyBetween(Integer value1, Integer value2) {
            addCriterion("is_new_fly between", value1, value2, "isNewFly");
            return (Criteria) this;
        }

        public Criteria andIsNewFlyNotBetween(Integer value1, Integer value2) {
            addCriterion("is_new_fly not between", value1, value2, "isNewFly");
            return (Criteria) this;
        }

        public Criteria andProgAuditStatusIsNull() {
            addCriterion("prog_audit_status is null");
            return (Criteria) this;
        }

        public Criteria andProgAuditStatusIsNotNull() {
            addCriterion("prog_audit_status is not null");
            return (Criteria) this;
        }

        public Criteria andProgAuditStatusEqualTo(Integer value) {
            addCriterion("prog_audit_status =", value, "progAuditStatus");
            return (Criteria) this;
        }

        public Criteria andProgAuditStatusNotEqualTo(Integer value) {
            addCriterion("prog_audit_status <>", value, "progAuditStatus");
            return (Criteria) this;
        }

        public Criteria andProgAuditStatusGreaterThan(Integer value) {
            addCriterion("prog_audit_status >", value, "progAuditStatus");
            return (Criteria) this;
        }

        public Criteria andProgAuditStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("prog_audit_status >=", value, "progAuditStatus");
            return (Criteria) this;
        }

        public Criteria andProgAuditStatusLessThan(Integer value) {
            addCriterion("prog_audit_status <", value, "progAuditStatus");
            return (Criteria) this;
        }

        public Criteria andProgAuditStatusLessThanOrEqualTo(Integer value) {
            addCriterion("prog_audit_status <=", value, "progAuditStatus");
            return (Criteria) this;
        }

        public Criteria andProgAuditStatusIn(List<Integer> values) {
            addCriterion("prog_audit_status in", values, "progAuditStatus");
            return (Criteria) this;
        }

        public Criteria andProgAuditStatusNotIn(List<Integer> values) {
            addCriterion("prog_audit_status not in", values, "progAuditStatus");
            return (Criteria) this;
        }

        public Criteria andProgAuditStatusBetween(Integer value1, Integer value2) {
            addCriterion("prog_audit_status between", value1, value2, "progAuditStatus");
            return (Criteria) this;
        }

        public Criteria andProgAuditStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("prog_audit_status not between", value1, value2, "progAuditStatus");
            return (Criteria) this;
        }

        public Criteria andProgMiscElemAuditStatusIsNull() {
            addCriterion("prog_misc_elem_audit_status is null");
            return (Criteria) this;
        }

        public Criteria andProgMiscElemAuditStatusIsNotNull() {
            addCriterion("prog_misc_elem_audit_status is not null");
            return (Criteria) this;
        }

        public Criteria andProgMiscElemAuditStatusEqualTo(Integer value) {
            addCriterion("prog_misc_elem_audit_status =", value, "progMiscElemAuditStatus");
            return (Criteria) this;
        }

        public Criteria andProgMiscElemAuditStatusNotEqualTo(Integer value) {
            addCriterion("prog_misc_elem_audit_status <>", value, "progMiscElemAuditStatus");
            return (Criteria) this;
        }

        public Criteria andProgMiscElemAuditStatusGreaterThan(Integer value) {
            addCriterion("prog_misc_elem_audit_status >", value, "progMiscElemAuditStatus");
            return (Criteria) this;
        }

        public Criteria andProgMiscElemAuditStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("prog_misc_elem_audit_status >=", value, "progMiscElemAuditStatus");
            return (Criteria) this;
        }

        public Criteria andProgMiscElemAuditStatusLessThan(Integer value) {
            addCriterion("prog_misc_elem_audit_status <", value, "progMiscElemAuditStatus");
            return (Criteria) this;
        }

        public Criteria andProgMiscElemAuditStatusLessThanOrEqualTo(Integer value) {
            addCriterion("prog_misc_elem_audit_status <=", value, "progMiscElemAuditStatus");
            return (Criteria) this;
        }

        public Criteria andProgMiscElemAuditStatusIn(List<Integer> values) {
            addCriterion("prog_misc_elem_audit_status in", values, "progMiscElemAuditStatus");
            return (Criteria) this;
        }

        public Criteria andProgMiscElemAuditStatusNotIn(List<Integer> values) {
            addCriterion("prog_misc_elem_audit_status not in", values, "progMiscElemAuditStatus");
            return (Criteria) this;
        }

        public Criteria andProgMiscElemAuditStatusBetween(Integer value1, Integer value2) {
            addCriterion("prog_misc_elem_audit_status between", value1, value2, "progMiscElemAuditStatus");
            return (Criteria) this;
        }

        public Criteria andProgMiscElemAuditStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("prog_misc_elem_audit_status not between", value1, value2, "progMiscElemAuditStatus");
            return (Criteria) this;
        }

        public Criteria andIsRecheckIsNull() {
            addCriterion("is_recheck is null");
            return (Criteria) this;
        }

        public Criteria andIsRecheckIsNotNull() {
            addCriterion("is_recheck is not null");
            return (Criteria) this;
        }

        public Criteria andIsRecheckEqualTo(Integer value) {
            addCriterion("is_recheck =", value, "isRecheck");
            return (Criteria) this;
        }

        public Criteria andIsRecheckNotEqualTo(Integer value) {
            addCriterion("is_recheck <>", value, "isRecheck");
            return (Criteria) this;
        }

        public Criteria andIsRecheckGreaterThan(Integer value) {
            addCriterion("is_recheck >", value, "isRecheck");
            return (Criteria) this;
        }

        public Criteria andIsRecheckGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_recheck >=", value, "isRecheck");
            return (Criteria) this;
        }

        public Criteria andIsRecheckLessThan(Integer value) {
            addCriterion("is_recheck <", value, "isRecheck");
            return (Criteria) this;
        }

        public Criteria andIsRecheckLessThanOrEqualTo(Integer value) {
            addCriterion("is_recheck <=", value, "isRecheck");
            return (Criteria) this;
        }

        public Criteria andIsRecheckIn(List<Integer> values) {
            addCriterion("is_recheck in", values, "isRecheck");
            return (Criteria) this;
        }

        public Criteria andIsRecheckNotIn(List<Integer> values) {
            addCriterion("is_recheck not in", values, "isRecheck");
            return (Criteria) this;
        }

        public Criteria andIsRecheckBetween(Integer value1, Integer value2) {
            addCriterion("is_recheck between", value1, value2, "isRecheck");
            return (Criteria) this;
        }

        public Criteria andIsRecheckNotBetween(Integer value1, Integer value2) {
            addCriterion("is_recheck not between", value1, value2, "isRecheck");
            return (Criteria) this;
        }

        public Criteria andFlagIsNull() {
            addCriterion("flag is null");
            return (Criteria) this;
        }

        public Criteria andFlagIsNotNull() {
            addCriterion("flag is not null");
            return (Criteria) this;
        }

        public Criteria andFlagEqualTo(Integer value) {
            addCriterion("flag =", value, "flag");
            return (Criteria) this;
        }

        public Criteria andFlagNotEqualTo(Integer value) {
            addCriterion("flag <>", value, "flag");
            return (Criteria) this;
        }

        public Criteria andFlagGreaterThan(Integer value) {
            addCriterion("flag >", value, "flag");
            return (Criteria) this;
        }

        public Criteria andFlagGreaterThanOrEqualTo(Integer value) {
            addCriterion("flag >=", value, "flag");
            return (Criteria) this;
        }

        public Criteria andFlagLessThan(Integer value) {
            addCriterion("flag <", value, "flag");
            return (Criteria) this;
        }

        public Criteria andFlagLessThanOrEqualTo(Integer value) {
            addCriterion("flag <=", value, "flag");
            return (Criteria) this;
        }

        public Criteria andFlagIn(List<Integer> values) {
            addCriterion("flag in", values, "flag");
            return (Criteria) this;
        }

        public Criteria andFlagNotIn(List<Integer> values) {
            addCriterion("flag not in", values, "flag");
            return (Criteria) this;
        }

        public Criteria andFlagBetween(Integer value1, Integer value2) {
            addCriterion("flag between", value1, value2, "flag");
            return (Criteria) this;
        }

        public Criteria andFlagNotBetween(Integer value1, Integer value2) {
            addCriterion("flag not between", value1, value2, "flag");
            return (Criteria) this;
        }

        public Criteria andMaterialVideoIdIsNull() {
            addCriterion("material_video_id is null");
            return (Criteria) this;
        }

        public Criteria andMaterialVideoIdIsNotNull() {
            addCriterion("material_video_id is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialVideoIdEqualTo(Long value) {
            addCriterion("material_video_id =", value, "materialVideoId");
            return (Criteria) this;
        }

        public Criteria andMaterialVideoIdNotEqualTo(Long value) {
            addCriterion("material_video_id <>", value, "materialVideoId");
            return (Criteria) this;
        }

        public Criteria andMaterialVideoIdGreaterThan(Long value) {
            addCriterion("material_video_id >", value, "materialVideoId");
            return (Criteria) this;
        }

        public Criteria andMaterialVideoIdGreaterThanOrEqualTo(Long value) {
            addCriterion("material_video_id >=", value, "materialVideoId");
            return (Criteria) this;
        }

        public Criteria andMaterialVideoIdLessThan(Long value) {
            addCriterion("material_video_id <", value, "materialVideoId");
            return (Criteria) this;
        }

        public Criteria andMaterialVideoIdLessThanOrEqualTo(Long value) {
            addCriterion("material_video_id <=", value, "materialVideoId");
            return (Criteria) this;
        }

        public Criteria andMaterialVideoIdIn(List<Long> values) {
            addCriterion("material_video_id in", values, "materialVideoId");
            return (Criteria) this;
        }

        public Criteria andMaterialVideoIdNotIn(List<Long> values) {
            addCriterion("material_video_id not in", values, "materialVideoId");
            return (Criteria) this;
        }

        public Criteria andMaterialVideoIdBetween(Long value1, Long value2) {
            addCriterion("material_video_id between", value1, value2, "materialVideoId");
            return (Criteria) this;
        }

        public Criteria andMaterialVideoIdNotBetween(Long value1, Long value2) {
            addCriterion("material_video_id not between", value1, value2, "materialVideoId");
            return (Criteria) this;
        }

        public Criteria andUnderFrameAuditFlagIsNull() {
            addCriterion("under_frame_audit_flag is null");
            return (Criteria) this;
        }

        public Criteria andUnderFrameAuditFlagIsNotNull() {
            addCriterion("under_frame_audit_flag is not null");
            return (Criteria) this;
        }

        public Criteria andUnderFrameAuditFlagEqualTo(Integer value) {
            addCriterion("under_frame_audit_flag =", value, "underFrameAuditFlag");
            return (Criteria) this;
        }

        public Criteria andUnderFrameAuditFlagNotEqualTo(Integer value) {
            addCriterion("under_frame_audit_flag <>", value, "underFrameAuditFlag");
            return (Criteria) this;
        }

        public Criteria andUnderFrameAuditFlagGreaterThan(Integer value) {
            addCriterion("under_frame_audit_flag >", value, "underFrameAuditFlag");
            return (Criteria) this;
        }

        public Criteria andUnderFrameAuditFlagGreaterThanOrEqualTo(Integer value) {
            addCriterion("under_frame_audit_flag >=", value, "underFrameAuditFlag");
            return (Criteria) this;
        }

        public Criteria andUnderFrameAuditFlagLessThan(Integer value) {
            addCriterion("under_frame_audit_flag <", value, "underFrameAuditFlag");
            return (Criteria) this;
        }

        public Criteria andUnderFrameAuditFlagLessThanOrEqualTo(Integer value) {
            addCriterion("under_frame_audit_flag <=", value, "underFrameAuditFlag");
            return (Criteria) this;
        }

        public Criteria andUnderFrameAuditFlagIn(List<Integer> values) {
            addCriterion("under_frame_audit_flag in", values, "underFrameAuditFlag");
            return (Criteria) this;
        }

        public Criteria andUnderFrameAuditFlagNotIn(List<Integer> values) {
            addCriterion("under_frame_audit_flag not in", values, "underFrameAuditFlag");
            return (Criteria) this;
        }

        public Criteria andUnderFrameAuditFlagBetween(Integer value1, Integer value2) {
            addCriterion("under_frame_audit_flag between", value1, value2, "underFrameAuditFlag");
            return (Criteria) this;
        }

        public Criteria andUnderFrameAuditFlagNotBetween(Integer value1, Integer value2) {
            addCriterion("under_frame_audit_flag not between", value1, value2, "underFrameAuditFlag");
            return (Criteria) this;
        }

        public Criteria andIsAutoFillIsNull() {
            addCriterion("is_auto_fill is null");
            return (Criteria) this;
        }

        public Criteria andIsAutoFillIsNotNull() {
            addCriterion("is_auto_fill is not null");
            return (Criteria) this;
        }

        public Criteria andIsAutoFillEqualTo(Integer value) {
            addCriterion("is_auto_fill =", value, "isAutoFill");
            return (Criteria) this;
        }

        public Criteria andIsAutoFillNotEqualTo(Integer value) {
            addCriterion("is_auto_fill <>", value, "isAutoFill");
            return (Criteria) this;
        }

        public Criteria andIsAutoFillGreaterThan(Integer value) {
            addCriterion("is_auto_fill >", value, "isAutoFill");
            return (Criteria) this;
        }

        public Criteria andIsAutoFillGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_auto_fill >=", value, "isAutoFill");
            return (Criteria) this;
        }

        public Criteria andIsAutoFillLessThan(Integer value) {
            addCriterion("is_auto_fill <", value, "isAutoFill");
            return (Criteria) this;
        }

        public Criteria andIsAutoFillLessThanOrEqualTo(Integer value) {
            addCriterion("is_auto_fill <=", value, "isAutoFill");
            return (Criteria) this;
        }

        public Criteria andIsAutoFillIn(List<Integer> values) {
            addCriterion("is_auto_fill in", values, "isAutoFill");
            return (Criteria) this;
        }

        public Criteria andIsAutoFillNotIn(List<Integer> values) {
            addCriterion("is_auto_fill not in", values, "isAutoFill");
            return (Criteria) this;
        }

        public Criteria andIsAutoFillBetween(Integer value1, Integer value2) {
            addCriterion("is_auto_fill between", value1, value2, "isAutoFill");
            return (Criteria) this;
        }

        public Criteria andIsAutoFillNotBetween(Integer value1, Integer value2) {
            addCriterion("is_auto_fill not between", value1, value2, "isAutoFill");
            return (Criteria) this;
        }

        public Criteria andPromotionPurposeContentSecondaryIsNull() {
            addCriterion("promotion_purpose_content_secondary is null");
            return (Criteria) this;
        }

        public Criteria andPromotionPurposeContentSecondaryIsNotNull() {
            addCriterion("promotion_purpose_content_secondary is not null");
            return (Criteria) this;
        }

        public Criteria andPromotionPurposeContentSecondaryEqualTo(String value) {
            addCriterion("promotion_purpose_content_secondary =", value, "promotionPurposeContentSecondary");
            return (Criteria) this;
        }

        public Criteria andPromotionPurposeContentSecondaryNotEqualTo(String value) {
            addCriterion("promotion_purpose_content_secondary <>", value, "promotionPurposeContentSecondary");
            return (Criteria) this;
        }

        public Criteria andPromotionPurposeContentSecondaryGreaterThan(String value) {
            addCriterion("promotion_purpose_content_secondary >", value, "promotionPurposeContentSecondary");
            return (Criteria) this;
        }

        public Criteria andPromotionPurposeContentSecondaryGreaterThanOrEqualTo(String value) {
            addCriterion("promotion_purpose_content_secondary >=", value, "promotionPurposeContentSecondary");
            return (Criteria) this;
        }

        public Criteria andPromotionPurposeContentSecondaryLessThan(String value) {
            addCriterion("promotion_purpose_content_secondary <", value, "promotionPurposeContentSecondary");
            return (Criteria) this;
        }

        public Criteria andPromotionPurposeContentSecondaryLessThanOrEqualTo(String value) {
            addCriterion("promotion_purpose_content_secondary <=", value, "promotionPurposeContentSecondary");
            return (Criteria) this;
        }

        public Criteria andPromotionPurposeContentSecondaryLike(String value) {
            addCriterion("promotion_purpose_content_secondary like", value, "promotionPurposeContentSecondary");
            return (Criteria) this;
        }

        public Criteria andPromotionPurposeContentSecondaryNotLike(String value) {
            addCriterion("promotion_purpose_content_secondary not like", value, "promotionPurposeContentSecondary");
            return (Criteria) this;
        }

        public Criteria andPromotionPurposeContentSecondaryIn(List<String> values) {
            addCriterion("promotion_purpose_content_secondary in", values, "promotionPurposeContentSecondary");
            return (Criteria) this;
        }

        public Criteria andPromotionPurposeContentSecondaryNotIn(List<String> values) {
            addCriterion("promotion_purpose_content_secondary not in", values, "promotionPurposeContentSecondary");
            return (Criteria) this;
        }

        public Criteria andPromotionPurposeContentSecondaryBetween(String value1, String value2) {
            addCriterion("promotion_purpose_content_secondary between", value1, value2, "promotionPurposeContentSecondary");
            return (Criteria) this;
        }

        public Criteria andPromotionPurposeContentSecondaryNotBetween(String value1, String value2) {
            addCriterion("promotion_purpose_content_secondary not between", value1, value2, "promotionPurposeContentSecondary");
            return (Criteria) this;
        }

        public Criteria andIsManagedIsNull() {
            addCriterion("is_managed is null");
            return (Criteria) this;
        }

        public Criteria andIsManagedIsNotNull() {
            addCriterion("is_managed is not null");
            return (Criteria) this;
        }

        public Criteria andIsManagedEqualTo(Integer value) {
            addCriterion("is_managed =", value, "isManaged");
            return (Criteria) this;
        }

        public Criteria andIsManagedNotEqualTo(Integer value) {
            addCriterion("is_managed <>", value, "isManaged");
            return (Criteria) this;
        }

        public Criteria andIsManagedGreaterThan(Integer value) {
            addCriterion("is_managed >", value, "isManaged");
            return (Criteria) this;
        }

        public Criteria andIsManagedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_managed >=", value, "isManaged");
            return (Criteria) this;
        }

        public Criteria andIsManagedLessThan(Integer value) {
            addCriterion("is_managed <", value, "isManaged");
            return (Criteria) this;
        }

        public Criteria andIsManagedLessThanOrEqualTo(Integer value) {
            addCriterion("is_managed <=", value, "isManaged");
            return (Criteria) this;
        }

        public Criteria andIsManagedIn(List<Integer> values) {
            addCriterion("is_managed in", values, "isManaged");
            return (Criteria) this;
        }

        public Criteria andIsManagedNotIn(List<Integer> values) {
            addCriterion("is_managed not in", values, "isManaged");
            return (Criteria) this;
        }

        public Criteria andIsManagedBetween(Integer value1, Integer value2) {
            addCriterion("is_managed between", value1, value2, "isManaged");
            return (Criteria) this;
        }

        public Criteria andIsManagedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_managed not between", value1, value2, "isManaged");
            return (Criteria) this;
        }

        public Criteria andIsGdPlusIsNull() {
            addCriterion("is_gd_plus is null");
            return (Criteria) this;
        }

        public Criteria andIsGdPlusIsNotNull() {
            addCriterion("is_gd_plus is not null");
            return (Criteria) this;
        }

        public Criteria andIsGdPlusEqualTo(Integer value) {
            addCriterion("is_gd_plus =", value, "isGdPlus");
            return (Criteria) this;
        }

        public Criteria andIsGdPlusNotEqualTo(Integer value) {
            addCriterion("is_gd_plus <>", value, "isGdPlus");
            return (Criteria) this;
        }

        public Criteria andIsGdPlusGreaterThan(Integer value) {
            addCriterion("is_gd_plus >", value, "isGdPlus");
            return (Criteria) this;
        }

        public Criteria andIsGdPlusGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_gd_plus >=", value, "isGdPlus");
            return (Criteria) this;
        }

        public Criteria andIsGdPlusLessThan(Integer value) {
            addCriterion("is_gd_plus <", value, "isGdPlus");
            return (Criteria) this;
        }

        public Criteria andIsGdPlusLessThanOrEqualTo(Integer value) {
            addCriterion("is_gd_plus <=", value, "isGdPlus");
            return (Criteria) this;
        }

        public Criteria andIsGdPlusIn(List<Integer> values) {
            addCriterion("is_gd_plus in", values, "isGdPlus");
            return (Criteria) this;
        }

        public Criteria andIsGdPlusNotIn(List<Integer> values) {
            addCriterion("is_gd_plus not in", values, "isGdPlus");
            return (Criteria) this;
        }

        public Criteria andIsGdPlusBetween(Integer value1, Integer value2) {
            addCriterion("is_gd_plus between", value1, value2, "isGdPlus");
            return (Criteria) this;
        }

        public Criteria andIsGdPlusNotBetween(Integer value1, Integer value2) {
            addCriterion("is_gd_plus not between", value1, value2, "isGdPlus");
            return (Criteria) this;
        }

        public Criteria andAdvertisingModeIsNull() {
            addCriterion("advertising_mode is null");
            return (Criteria) this;
        }

        public Criteria andAdvertisingModeIsNotNull() {
            addCriterion("advertising_mode is not null");
            return (Criteria) this;
        }

        public Criteria andAdvertisingModeEqualTo(Integer value) {
            addCriterion("advertising_mode =", value, "advertisingMode");
            return (Criteria) this;
        }

        public Criteria andAdvertisingModeNotEqualTo(Integer value) {
            addCriterion("advertising_mode <>", value, "advertisingMode");
            return (Criteria) this;
        }

        public Criteria andAdvertisingModeGreaterThan(Integer value) {
            addCriterion("advertising_mode >", value, "advertisingMode");
            return (Criteria) this;
        }

        public Criteria andAdvertisingModeGreaterThanOrEqualTo(Integer value) {
            addCriterion("advertising_mode >=", value, "advertisingMode");
            return (Criteria) this;
        }

        public Criteria andAdvertisingModeLessThan(Integer value) {
            addCriterion("advertising_mode <", value, "advertisingMode");
            return (Criteria) this;
        }

        public Criteria andAdvertisingModeLessThanOrEqualTo(Integer value) {
            addCriterion("advertising_mode <=", value, "advertisingMode");
            return (Criteria) this;
        }

        public Criteria andAdvertisingModeIn(List<Integer> values) {
            addCriterion("advertising_mode in", values, "advertisingMode");
            return (Criteria) this;
        }

        public Criteria andAdvertisingModeNotIn(List<Integer> values) {
            addCriterion("advertising_mode not in", values, "advertisingMode");
            return (Criteria) this;
        }

        public Criteria andAdvertisingModeBetween(Integer value1, Integer value2) {
            addCriterion("advertising_mode between", value1, value2, "advertisingMode");
            return (Criteria) this;
        }

        public Criteria andAdvertisingModeNotBetween(Integer value1, Integer value2) {
            addCriterion("advertising_mode not between", value1, value2, "advertisingMode");
            return (Criteria) this;
        }

        public Criteria andIsMiddleAdIsNull() {
            addCriterion("is_middle_ad is null");
            return (Criteria) this;
        }

        public Criteria andIsMiddleAdIsNotNull() {
            addCriterion("is_middle_ad is not null");
            return (Criteria) this;
        }

        public Criteria andIsMiddleAdEqualTo(Integer value) {
            addCriterion("is_middle_ad =", value, "isMiddleAd");
            return (Criteria) this;
        }

        public Criteria andIsMiddleAdNotEqualTo(Integer value) {
            addCriterion("is_middle_ad <>", value, "isMiddleAd");
            return (Criteria) this;
        }

        public Criteria andIsMiddleAdGreaterThan(Integer value) {
            addCriterion("is_middle_ad >", value, "isMiddleAd");
            return (Criteria) this;
        }

        public Criteria andIsMiddleAdGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_middle_ad >=", value, "isMiddleAd");
            return (Criteria) this;
        }

        public Criteria andIsMiddleAdLessThan(Integer value) {
            addCriterion("is_middle_ad <", value, "isMiddleAd");
            return (Criteria) this;
        }

        public Criteria andIsMiddleAdLessThanOrEqualTo(Integer value) {
            addCriterion("is_middle_ad <=", value, "isMiddleAd");
            return (Criteria) this;
        }

        public Criteria andIsMiddleAdIn(List<Integer> values) {
            addCriterion("is_middle_ad in", values, "isMiddleAd");
            return (Criteria) this;
        }

        public Criteria andIsMiddleAdNotIn(List<Integer> values) {
            addCriterion("is_middle_ad not in", values, "isMiddleAd");
            return (Criteria) this;
        }

        public Criteria andIsMiddleAdBetween(Integer value1, Integer value2) {
            addCriterion("is_middle_ad between", value1, value2, "isMiddleAd");
            return (Criteria) this;
        }

        public Criteria andIsMiddleAdNotBetween(Integer value1, Integer value2) {
            addCriterion("is_middle_ad not between", value1, value2, "isMiddleAd");
            return (Criteria) this;
        }

        public Criteria andIsVideoBindIsNull() {
            addCriterion("is_video_bind is null");
            return (Criteria) this;
        }

        public Criteria andIsVideoBindIsNotNull() {
            addCriterion("is_video_bind is not null");
            return (Criteria) this;
        }

        public Criteria andIsVideoBindEqualTo(Integer value) {
            addCriterion("is_video_bind =", value, "isVideoBind");
            return (Criteria) this;
        }

        public Criteria andIsVideoBindNotEqualTo(Integer value) {
            addCriterion("is_video_bind <>", value, "isVideoBind");
            return (Criteria) this;
        }

        public Criteria andIsVideoBindGreaterThan(Integer value) {
            addCriterion("is_video_bind >", value, "isVideoBind");
            return (Criteria) this;
        }

        public Criteria andIsVideoBindGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_video_bind >=", value, "isVideoBind");
            return (Criteria) this;
        }

        public Criteria andIsVideoBindLessThan(Integer value) {
            addCriterion("is_video_bind <", value, "isVideoBind");
            return (Criteria) this;
        }

        public Criteria andIsVideoBindLessThanOrEqualTo(Integer value) {
            addCriterion("is_video_bind <=", value, "isVideoBind");
            return (Criteria) this;
        }

        public Criteria andIsVideoBindIn(List<Integer> values) {
            addCriterion("is_video_bind in", values, "isVideoBind");
            return (Criteria) this;
        }

        public Criteria andIsVideoBindNotIn(List<Integer> values) {
            addCriterion("is_video_bind not in", values, "isVideoBind");
            return (Criteria) this;
        }

        public Criteria andIsVideoBindBetween(Integer value1, Integer value2) {
            addCriterion("is_video_bind between", value1, value2, "isVideoBind");
            return (Criteria) this;
        }

        public Criteria andIsVideoBindNotBetween(Integer value1, Integer value2) {
            addCriterion("is_video_bind not between", value1, value2, "isVideoBind");
            return (Criteria) this;
        }

        public Criteria andTrackadfIsNull() {
            addCriterion("trackadf is null");
            return (Criteria) this;
        }

        public Criteria andTrackadfIsNotNull() {
            addCriterion("trackadf is not null");
            return (Criteria) this;
        }

        public Criteria andTrackadfEqualTo(String value) {
            addCriterion("trackadf =", value, "trackadf");
            return (Criteria) this;
        }

        public Criteria andTrackadfNotEqualTo(String value) {
            addCriterion("trackadf <>", value, "trackadf");
            return (Criteria) this;
        }

        public Criteria andTrackadfGreaterThan(String value) {
            addCriterion("trackadf >", value, "trackadf");
            return (Criteria) this;
        }

        public Criteria andTrackadfGreaterThanOrEqualTo(String value) {
            addCriterion("trackadf >=", value, "trackadf");
            return (Criteria) this;
        }

        public Criteria andTrackadfLessThan(String value) {
            addCriterion("trackadf <", value, "trackadf");
            return (Criteria) this;
        }

        public Criteria andTrackadfLessThanOrEqualTo(String value) {
            addCriterion("trackadf <=", value, "trackadf");
            return (Criteria) this;
        }

        public Criteria andTrackadfLike(String value) {
            addCriterion("trackadf like", value, "trackadf");
            return (Criteria) this;
        }

        public Criteria andTrackadfNotLike(String value) {
            addCriterion("trackadf not like", value, "trackadf");
            return (Criteria) this;
        }

        public Criteria andTrackadfIn(List<String> values) {
            addCriterion("trackadf in", values, "trackadf");
            return (Criteria) this;
        }

        public Criteria andTrackadfNotIn(List<String> values) {
            addCriterion("trackadf not in", values, "trackadf");
            return (Criteria) this;
        }

        public Criteria andTrackadfBetween(String value1, String value2) {
            addCriterion("trackadf between", value1, value2, "trackadf");
            return (Criteria) this;
        }

        public Criteria andTrackadfNotBetween(String value1, String value2) {
            addCriterion("trackadf not between", value1, value2, "trackadf");
            return (Criteria) this;
        }

        public Criteria andIsPageGroupIsNull() {
            addCriterion("is_page_group is null");
            return (Criteria) this;
        }

        public Criteria andIsPageGroupIsNotNull() {
            addCriterion("is_page_group is not null");
            return (Criteria) this;
        }

        public Criteria andIsPageGroupEqualTo(Integer value) {
            addCriterion("is_page_group =", value, "isPageGroup");
            return (Criteria) this;
        }

        public Criteria andIsPageGroupNotEqualTo(Integer value) {
            addCriterion("is_page_group <>", value, "isPageGroup");
            return (Criteria) this;
        }

        public Criteria andIsPageGroupGreaterThan(Integer value) {
            addCriterion("is_page_group >", value, "isPageGroup");
            return (Criteria) this;
        }

        public Criteria andIsPageGroupGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_page_group >=", value, "isPageGroup");
            return (Criteria) this;
        }

        public Criteria andIsPageGroupLessThan(Integer value) {
            addCriterion("is_page_group <", value, "isPageGroup");
            return (Criteria) this;
        }

        public Criteria andIsPageGroupLessThanOrEqualTo(Integer value) {
            addCriterion("is_page_group <=", value, "isPageGroup");
            return (Criteria) this;
        }

        public Criteria andIsPageGroupIn(List<Integer> values) {
            addCriterion("is_page_group in", values, "isPageGroup");
            return (Criteria) this;
        }

        public Criteria andIsPageGroupNotIn(List<Integer> values) {
            addCriterion("is_page_group not in", values, "isPageGroup");
            return (Criteria) this;
        }

        public Criteria andIsPageGroupBetween(Integer value1, Integer value2) {
            addCriterion("is_page_group between", value1, value2, "isPageGroup");
            return (Criteria) this;
        }

        public Criteria andIsPageGroupNotBetween(Integer value1, Integer value2) {
            addCriterion("is_page_group not between", value1, value2, "isPageGroup");
            return (Criteria) this;
        }

        public Criteria andParentCreativeIdIsNull() {
            addCriterion("parent_creative_id is null");
            return (Criteria) this;
        }

        public Criteria andParentCreativeIdIsNotNull() {
            addCriterion("parent_creative_id is not null");
            return (Criteria) this;
        }

        public Criteria andParentCreativeIdEqualTo(Integer value) {
            addCriterion("parent_creative_id =", value, "parentCreativeId");
            return (Criteria) this;
        }

        public Criteria andParentCreativeIdNotEqualTo(Integer value) {
            addCriterion("parent_creative_id <>", value, "parentCreativeId");
            return (Criteria) this;
        }

        public Criteria andParentCreativeIdGreaterThan(Integer value) {
            addCriterion("parent_creative_id >", value, "parentCreativeId");
            return (Criteria) this;
        }

        public Criteria andParentCreativeIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("parent_creative_id >=", value, "parentCreativeId");
            return (Criteria) this;
        }

        public Criteria andParentCreativeIdLessThan(Integer value) {
            addCriterion("parent_creative_id <", value, "parentCreativeId");
            return (Criteria) this;
        }

        public Criteria andParentCreativeIdLessThanOrEqualTo(Integer value) {
            addCriterion("parent_creative_id <=", value, "parentCreativeId");
            return (Criteria) this;
        }

        public Criteria andParentCreativeIdIn(List<Integer> values) {
            addCriterion("parent_creative_id in", values, "parentCreativeId");
            return (Criteria) this;
        }

        public Criteria andParentCreativeIdNotIn(List<Integer> values) {
            addCriterion("parent_creative_id not in", values, "parentCreativeId");
            return (Criteria) this;
        }

        public Criteria andParentCreativeIdBetween(Integer value1, Integer value2) {
            addCriterion("parent_creative_id between", value1, value2, "parentCreativeId");
            return (Criteria) this;
        }

        public Criteria andParentCreativeIdNotBetween(Integer value1, Integer value2) {
            addCriterion("parent_creative_id not between", value1, value2, "parentCreativeId");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}