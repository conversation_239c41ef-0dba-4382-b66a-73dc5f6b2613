package com.bilibili.risk.po.ad;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public class LauStoryCouponComponentPoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public LauStoryCouponComponentPoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNull() {
            addCriterion("ctime is null");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNotNull() {
            addCriterion("ctime is not null");
            return (Criteria) this;
        }

        public Criteria andCtimeEqualTo(Timestamp value) {
            addCriterion("ctime =", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotEqualTo(Timestamp value) {
            addCriterion("ctime <>", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThan(Timestamp value) {
            addCriterion("ctime >", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("ctime >=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThan(Timestamp value) {
            addCriterion("ctime <", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("ctime <=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeIn(List<Timestamp> values) {
            addCriterion("ctime in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotIn(List<Timestamp> values) {
            addCriterion("ctime not in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime not between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNull() {
            addCriterion("mtime is null");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNotNull() {
            addCriterion("mtime is not null");
            return (Criteria) this;
        }

        public Criteria andMtimeEqualTo(Timestamp value) {
            addCriterion("mtime =", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotEqualTo(Timestamp value) {
            addCriterion("mtime <>", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThan(Timestamp value) {
            addCriterion("mtime >", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("mtime >=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThan(Timestamp value) {
            addCriterion("mtime <", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("mtime <=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeIn(List<Timestamp> values) {
            addCriterion("mtime in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotIn(List<Timestamp> values) {
            addCriterion("mtime not in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime not between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Integer value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Integer value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Integer value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Integer value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Integer value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Integer> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Integer> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andAccountIdIsNull() {
            addCriterion("account_id is null");
            return (Criteria) this;
        }

        public Criteria andAccountIdIsNotNull() {
            addCriterion("account_id is not null");
            return (Criteria) this;
        }

        public Criteria andAccountIdEqualTo(Integer value) {
            addCriterion("account_id =", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotEqualTo(Integer value) {
            addCriterion("account_id <>", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdGreaterThan(Integer value) {
            addCriterion("account_id >", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("account_id >=", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdLessThan(Integer value) {
            addCriterion("account_id <", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdLessThanOrEqualTo(Integer value) {
            addCriterion("account_id <=", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdIn(List<Integer> values) {
            addCriterion("account_id in", values, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotIn(List<Integer> values) {
            addCriterion("account_id not in", values, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdBetween(Integer value1, Integer value2) {
            addCriterion("account_id between", value1, value2, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotBetween(Integer value1, Integer value2) {
            addCriterion("account_id not between", value1, value2, "accountId");
            return (Criteria) this;
        }

        public Criteria andComponentIdIsNull() {
            addCriterion("component_id is null");
            return (Criteria) this;
        }

        public Criteria andComponentIdIsNotNull() {
            addCriterion("component_id is not null");
            return (Criteria) this;
        }

        public Criteria andComponentIdEqualTo(Long value) {
            addCriterion("component_id =", value, "componentId");
            return (Criteria) this;
        }

        public Criteria andComponentIdNotEqualTo(Long value) {
            addCriterion("component_id <>", value, "componentId");
            return (Criteria) this;
        }

        public Criteria andComponentIdGreaterThan(Long value) {
            addCriterion("component_id >", value, "componentId");
            return (Criteria) this;
        }

        public Criteria andComponentIdGreaterThanOrEqualTo(Long value) {
            addCriterion("component_id >=", value, "componentId");
            return (Criteria) this;
        }

        public Criteria andComponentIdLessThan(Long value) {
            addCriterion("component_id <", value, "componentId");
            return (Criteria) this;
        }

        public Criteria andComponentIdLessThanOrEqualTo(Long value) {
            addCriterion("component_id <=", value, "componentId");
            return (Criteria) this;
        }

        public Criteria andComponentIdIn(List<Long> values) {
            addCriterion("component_id in", values, "componentId");
            return (Criteria) this;
        }

        public Criteria andComponentIdNotIn(List<Long> values) {
            addCriterion("component_id not in", values, "componentId");
            return (Criteria) this;
        }

        public Criteria andComponentIdBetween(Long value1, Long value2) {
            addCriterion("component_id between", value1, value2, "componentId");
            return (Criteria) this;
        }

        public Criteria andComponentIdNotBetween(Long value1, Long value2) {
            addCriterion("component_id not between", value1, value2, "componentId");
            return (Criteria) this;
        }

        public Criteria andComponentNameIsNull() {
            addCriterion("component_name is null");
            return (Criteria) this;
        }

        public Criteria andComponentNameIsNotNull() {
            addCriterion("component_name is not null");
            return (Criteria) this;
        }

        public Criteria andComponentNameEqualTo(String value) {
            addCriterion("component_name =", value, "componentName");
            return (Criteria) this;
        }

        public Criteria andComponentNameNotEqualTo(String value) {
            addCriterion("component_name <>", value, "componentName");
            return (Criteria) this;
        }

        public Criteria andComponentNameGreaterThan(String value) {
            addCriterion("component_name >", value, "componentName");
            return (Criteria) this;
        }

        public Criteria andComponentNameGreaterThanOrEqualTo(String value) {
            addCriterion("component_name >=", value, "componentName");
            return (Criteria) this;
        }

        public Criteria andComponentNameLessThan(String value) {
            addCriterion("component_name <", value, "componentName");
            return (Criteria) this;
        }

        public Criteria andComponentNameLessThanOrEqualTo(String value) {
            addCriterion("component_name <=", value, "componentName");
            return (Criteria) this;
        }

        public Criteria andComponentNameLike(String value) {
            addCriterion("component_name like", value, "componentName");
            return (Criteria) this;
        }

        public Criteria andComponentNameNotLike(String value) {
            addCriterion("component_name not like", value, "componentName");
            return (Criteria) this;
        }

        public Criteria andComponentNameIn(List<String> values) {
            addCriterion("component_name in", values, "componentName");
            return (Criteria) this;
        }

        public Criteria andComponentNameNotIn(List<String> values) {
            addCriterion("component_name not in", values, "componentName");
            return (Criteria) this;
        }

        public Criteria andComponentNameBetween(String value1, String value2) {
            addCriterion("component_name between", value1, value2, "componentName");
            return (Criteria) this;
        }

        public Criteria andComponentNameNotBetween(String value1, String value2) {
            addCriterion("component_name not between", value1, value2, "componentName");
            return (Criteria) this;
        }

        public Criteria andCostFenIsNull() {
            addCriterion("cost_fen is null");
            return (Criteria) this;
        }

        public Criteria andCostFenIsNotNull() {
            addCriterion("cost_fen is not null");
            return (Criteria) this;
        }

        public Criteria andCostFenEqualTo(Integer value) {
            addCriterion("cost_fen =", value, "costFen");
            return (Criteria) this;
        }

        public Criteria andCostFenNotEqualTo(Integer value) {
            addCriterion("cost_fen <>", value, "costFen");
            return (Criteria) this;
        }

        public Criteria andCostFenGreaterThan(Integer value) {
            addCriterion("cost_fen >", value, "costFen");
            return (Criteria) this;
        }

        public Criteria andCostFenGreaterThanOrEqualTo(Integer value) {
            addCriterion("cost_fen >=", value, "costFen");
            return (Criteria) this;
        }

        public Criteria andCostFenLessThan(Integer value) {
            addCriterion("cost_fen <", value, "costFen");
            return (Criteria) this;
        }

        public Criteria andCostFenLessThanOrEqualTo(Integer value) {
            addCriterion("cost_fen <=", value, "costFen");
            return (Criteria) this;
        }

        public Criteria andCostFenIn(List<Integer> values) {
            addCriterion("cost_fen in", values, "costFen");
            return (Criteria) this;
        }

        public Criteria andCostFenNotIn(List<Integer> values) {
            addCriterion("cost_fen not in", values, "costFen");
            return (Criteria) this;
        }

        public Criteria andCostFenBetween(Integer value1, Integer value2) {
            addCriterion("cost_fen between", value1, value2, "costFen");
            return (Criteria) this;
        }

        public Criteria andCostFenNotBetween(Integer value1, Integer value2) {
            addCriterion("cost_fen not between", value1, value2, "costFen");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNull() {
            addCriterion("description is null");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNotNull() {
            addCriterion("description is not null");
            return (Criteria) this;
        }

        public Criteria andDescriptionEqualTo(String value) {
            addCriterion("description =", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotEqualTo(String value) {
            addCriterion("description <>", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThan(String value) {
            addCriterion("description >", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("description >=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThan(String value) {
            addCriterion("description <", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThanOrEqualTo(String value) {
            addCriterion("description <=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLike(String value) {
            addCriterion("description like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotLike(String value) {
            addCriterion("description not like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionIn(List<String> values) {
            addCriterion("description in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotIn(List<String> values) {
            addCriterion("description not in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionBetween(String value1, String value2) {
            addCriterion("description between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotBetween(String value1, String value2) {
            addCriterion("description not between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andCommentIsNull() {
            addCriterion("comment is null");
            return (Criteria) this;
        }

        public Criteria andCommentIsNotNull() {
            addCriterion("comment is not null");
            return (Criteria) this;
        }

        public Criteria andCommentEqualTo(String value) {
            addCriterion("comment =", value, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentNotEqualTo(String value) {
            addCriterion("comment <>", value, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentGreaterThan(String value) {
            addCriterion("comment >", value, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentGreaterThanOrEqualTo(String value) {
            addCriterion("comment >=", value, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentLessThan(String value) {
            addCriterion("comment <", value, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentLessThanOrEqualTo(String value) {
            addCriterion("comment <=", value, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentLike(String value) {
            addCriterion("comment like", value, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentNotLike(String value) {
            addCriterion("comment not like", value, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentIn(List<String> values) {
            addCriterion("comment in", values, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentNotIn(List<String> values) {
            addCriterion("comment not in", values, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentBetween(String value1, String value2) {
            addCriterion("comment between", value1, value2, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentNotBetween(String value1, String value2) {
            addCriterion("comment not between", value1, value2, "comment");
            return (Criteria) this;
        }

        public Criteria andUsePeriodStartIsNull() {
            addCriterion("use_period_start is null");
            return (Criteria) this;
        }

        public Criteria andUsePeriodStartIsNotNull() {
            addCriterion("use_period_start is not null");
            return (Criteria) this;
        }

        public Criteria andUsePeriodStartEqualTo(Timestamp value) {
            addCriterion("use_period_start =", value, "usePeriodStart");
            return (Criteria) this;
        }

        public Criteria andUsePeriodStartNotEqualTo(Timestamp value) {
            addCriterion("use_period_start <>", value, "usePeriodStart");
            return (Criteria) this;
        }

        public Criteria andUsePeriodStartGreaterThan(Timestamp value) {
            addCriterion("use_period_start >", value, "usePeriodStart");
            return (Criteria) this;
        }

        public Criteria andUsePeriodStartGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("use_period_start >=", value, "usePeriodStart");
            return (Criteria) this;
        }

        public Criteria andUsePeriodStartLessThan(Timestamp value) {
            addCriterion("use_period_start <", value, "usePeriodStart");
            return (Criteria) this;
        }

        public Criteria andUsePeriodStartLessThanOrEqualTo(Timestamp value) {
            addCriterion("use_period_start <=", value, "usePeriodStart");
            return (Criteria) this;
        }

        public Criteria andUsePeriodStartIn(List<Timestamp> values) {
            addCriterion("use_period_start in", values, "usePeriodStart");
            return (Criteria) this;
        }

        public Criteria andUsePeriodStartNotIn(List<Timestamp> values) {
            addCriterion("use_period_start not in", values, "usePeriodStart");
            return (Criteria) this;
        }

        public Criteria andUsePeriodStartBetween(Timestamp value1, Timestamp value2) {
            addCriterion("use_period_start between", value1, value2, "usePeriodStart");
            return (Criteria) this;
        }

        public Criteria andUsePeriodStartNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("use_period_start not between", value1, value2, "usePeriodStart");
            return (Criteria) this;
        }

        public Criteria andUsePeriodEndIsNull() {
            addCriterion("use_period_end is null");
            return (Criteria) this;
        }

        public Criteria andUsePeriodEndIsNotNull() {
            addCriterion("use_period_end is not null");
            return (Criteria) this;
        }

        public Criteria andUsePeriodEndEqualTo(Timestamp value) {
            addCriterion("use_period_end =", value, "usePeriodEnd");
            return (Criteria) this;
        }

        public Criteria andUsePeriodEndNotEqualTo(Timestamp value) {
            addCriterion("use_period_end <>", value, "usePeriodEnd");
            return (Criteria) this;
        }

        public Criteria andUsePeriodEndGreaterThan(Timestamp value) {
            addCriterion("use_period_end >", value, "usePeriodEnd");
            return (Criteria) this;
        }

        public Criteria andUsePeriodEndGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("use_period_end >=", value, "usePeriodEnd");
            return (Criteria) this;
        }

        public Criteria andUsePeriodEndLessThan(Timestamp value) {
            addCriterion("use_period_end <", value, "usePeriodEnd");
            return (Criteria) this;
        }

        public Criteria andUsePeriodEndLessThanOrEqualTo(Timestamp value) {
            addCriterion("use_period_end <=", value, "usePeriodEnd");
            return (Criteria) this;
        }

        public Criteria andUsePeriodEndIn(List<Timestamp> values) {
            addCriterion("use_period_end in", values, "usePeriodEnd");
            return (Criteria) this;
        }

        public Criteria andUsePeriodEndNotIn(List<Timestamp> values) {
            addCriterion("use_period_end not in", values, "usePeriodEnd");
            return (Criteria) this;
        }

        public Criteria andUsePeriodEndBetween(Timestamp value1, Timestamp value2) {
            addCriterion("use_period_end between", value1, value2, "usePeriodEnd");
            return (Criteria) this;
        }

        public Criteria andUsePeriodEndNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("use_period_end not between", value1, value2, "usePeriodEnd");
            return (Criteria) this;
        }

        public Criteria andObtainPeriodStartIsNull() {
            addCriterion("obtain_period_start is null");
            return (Criteria) this;
        }

        public Criteria andObtainPeriodStartIsNotNull() {
            addCriterion("obtain_period_start is not null");
            return (Criteria) this;
        }

        public Criteria andObtainPeriodStartEqualTo(Timestamp value) {
            addCriterion("obtain_period_start =", value, "obtainPeriodStart");
            return (Criteria) this;
        }

        public Criteria andObtainPeriodStartNotEqualTo(Timestamp value) {
            addCriterion("obtain_period_start <>", value, "obtainPeriodStart");
            return (Criteria) this;
        }

        public Criteria andObtainPeriodStartGreaterThan(Timestamp value) {
            addCriterion("obtain_period_start >", value, "obtainPeriodStart");
            return (Criteria) this;
        }

        public Criteria andObtainPeriodStartGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("obtain_period_start >=", value, "obtainPeriodStart");
            return (Criteria) this;
        }

        public Criteria andObtainPeriodStartLessThan(Timestamp value) {
            addCriterion("obtain_period_start <", value, "obtainPeriodStart");
            return (Criteria) this;
        }

        public Criteria andObtainPeriodStartLessThanOrEqualTo(Timestamp value) {
            addCriterion("obtain_period_start <=", value, "obtainPeriodStart");
            return (Criteria) this;
        }

        public Criteria andObtainPeriodStartIn(List<Timestamp> values) {
            addCriterion("obtain_period_start in", values, "obtainPeriodStart");
            return (Criteria) this;
        }

        public Criteria andObtainPeriodStartNotIn(List<Timestamp> values) {
            addCriterion("obtain_period_start not in", values, "obtainPeriodStart");
            return (Criteria) this;
        }

        public Criteria andObtainPeriodStartBetween(Timestamp value1, Timestamp value2) {
            addCriterion("obtain_period_start between", value1, value2, "obtainPeriodStart");
            return (Criteria) this;
        }

        public Criteria andObtainPeriodStartNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("obtain_period_start not between", value1, value2, "obtainPeriodStart");
            return (Criteria) this;
        }

        public Criteria andObtainPeriodEndIsNull() {
            addCriterion("obtain_period_end is null");
            return (Criteria) this;
        }

        public Criteria andObtainPeriodEndIsNotNull() {
            addCriterion("obtain_period_end is not null");
            return (Criteria) this;
        }

        public Criteria andObtainPeriodEndEqualTo(Timestamp value) {
            addCriterion("obtain_period_end =", value, "obtainPeriodEnd");
            return (Criteria) this;
        }

        public Criteria andObtainPeriodEndNotEqualTo(Timestamp value) {
            addCriterion("obtain_period_end <>", value, "obtainPeriodEnd");
            return (Criteria) this;
        }

        public Criteria andObtainPeriodEndGreaterThan(Timestamp value) {
            addCriterion("obtain_period_end >", value, "obtainPeriodEnd");
            return (Criteria) this;
        }

        public Criteria andObtainPeriodEndGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("obtain_period_end >=", value, "obtainPeriodEnd");
            return (Criteria) this;
        }

        public Criteria andObtainPeriodEndLessThan(Timestamp value) {
            addCriterion("obtain_period_end <", value, "obtainPeriodEnd");
            return (Criteria) this;
        }

        public Criteria andObtainPeriodEndLessThanOrEqualTo(Timestamp value) {
            addCriterion("obtain_period_end <=", value, "obtainPeriodEnd");
            return (Criteria) this;
        }

        public Criteria andObtainPeriodEndIn(List<Timestamp> values) {
            addCriterion("obtain_period_end in", values, "obtainPeriodEnd");
            return (Criteria) this;
        }

        public Criteria andObtainPeriodEndNotIn(List<Timestamp> values) {
            addCriterion("obtain_period_end not in", values, "obtainPeriodEnd");
            return (Criteria) this;
        }

        public Criteria andObtainPeriodEndBetween(Timestamp value1, Timestamp value2) {
            addCriterion("obtain_period_end between", value1, value2, "obtainPeriodEnd");
            return (Criteria) this;
        }

        public Criteria andObtainPeriodEndNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("obtain_period_end not between", value1, value2, "obtainPeriodEnd");
            return (Criteria) this;
        }

        public Criteria andButtonIdIsNull() {
            addCriterion("button_id is null");
            return (Criteria) this;
        }

        public Criteria andButtonIdIsNotNull() {
            addCriterion("button_id is not null");
            return (Criteria) this;
        }

        public Criteria andButtonIdEqualTo(Integer value) {
            addCriterion("button_id =", value, "buttonId");
            return (Criteria) this;
        }

        public Criteria andButtonIdNotEqualTo(Integer value) {
            addCriterion("button_id <>", value, "buttonId");
            return (Criteria) this;
        }

        public Criteria andButtonIdGreaterThan(Integer value) {
            addCriterion("button_id >", value, "buttonId");
            return (Criteria) this;
        }

        public Criteria andButtonIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("button_id >=", value, "buttonId");
            return (Criteria) this;
        }

        public Criteria andButtonIdLessThan(Integer value) {
            addCriterion("button_id <", value, "buttonId");
            return (Criteria) this;
        }

        public Criteria andButtonIdLessThanOrEqualTo(Integer value) {
            addCriterion("button_id <=", value, "buttonId");
            return (Criteria) this;
        }

        public Criteria andButtonIdIn(List<Integer> values) {
            addCriterion("button_id in", values, "buttonId");
            return (Criteria) this;
        }

        public Criteria andButtonIdNotIn(List<Integer> values) {
            addCriterion("button_id not in", values, "buttonId");
            return (Criteria) this;
        }

        public Criteria andButtonIdBetween(Integer value1, Integer value2) {
            addCriterion("button_id between", value1, value2, "buttonId");
            return (Criteria) this;
        }

        public Criteria andButtonIdNotBetween(Integer value1, Integer value2) {
            addCriterion("button_id not between", value1, value2, "buttonId");
            return (Criteria) this;
        }

        public Criteria andButtonTypeIsNull() {
            addCriterion("button_type is null");
            return (Criteria) this;
        }

        public Criteria andButtonTypeIsNotNull() {
            addCriterion("button_type is not null");
            return (Criteria) this;
        }

        public Criteria andButtonTypeEqualTo(Integer value) {
            addCriterion("button_type =", value, "buttonType");
            return (Criteria) this;
        }

        public Criteria andButtonTypeNotEqualTo(Integer value) {
            addCriterion("button_type <>", value, "buttonType");
            return (Criteria) this;
        }

        public Criteria andButtonTypeGreaterThan(Integer value) {
            addCriterion("button_type >", value, "buttonType");
            return (Criteria) this;
        }

        public Criteria andButtonTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("button_type >=", value, "buttonType");
            return (Criteria) this;
        }

        public Criteria andButtonTypeLessThan(Integer value) {
            addCriterion("button_type <", value, "buttonType");
            return (Criteria) this;
        }

        public Criteria andButtonTypeLessThanOrEqualTo(Integer value) {
            addCriterion("button_type <=", value, "buttonType");
            return (Criteria) this;
        }

        public Criteria andButtonTypeIn(List<Integer> values) {
            addCriterion("button_type in", values, "buttonType");
            return (Criteria) this;
        }

        public Criteria andButtonTypeNotIn(List<Integer> values) {
            addCriterion("button_type not in", values, "buttonType");
            return (Criteria) this;
        }

        public Criteria andButtonTypeBetween(Integer value1, Integer value2) {
            addCriterion("button_type between", value1, value2, "buttonType");
            return (Criteria) this;
        }

        public Criteria andButtonTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("button_type not between", value1, value2, "buttonType");
            return (Criteria) this;
        }

        public Criteria andButtonTextIsNull() {
            addCriterion("button_text is null");
            return (Criteria) this;
        }

        public Criteria andButtonTextIsNotNull() {
            addCriterion("button_text is not null");
            return (Criteria) this;
        }

        public Criteria andButtonTextEqualTo(String value) {
            addCriterion("button_text =", value, "buttonText");
            return (Criteria) this;
        }

        public Criteria andButtonTextNotEqualTo(String value) {
            addCriterion("button_text <>", value, "buttonText");
            return (Criteria) this;
        }

        public Criteria andButtonTextGreaterThan(String value) {
            addCriterion("button_text >", value, "buttonText");
            return (Criteria) this;
        }

        public Criteria andButtonTextGreaterThanOrEqualTo(String value) {
            addCriterion("button_text >=", value, "buttonText");
            return (Criteria) this;
        }

        public Criteria andButtonTextLessThan(String value) {
            addCriterion("button_text <", value, "buttonText");
            return (Criteria) this;
        }

        public Criteria andButtonTextLessThanOrEqualTo(String value) {
            addCriterion("button_text <=", value, "buttonText");
            return (Criteria) this;
        }

        public Criteria andButtonTextLike(String value) {
            addCriterion("button_text like", value, "buttonText");
            return (Criteria) this;
        }

        public Criteria andButtonTextNotLike(String value) {
            addCriterion("button_text not like", value, "buttonText");
            return (Criteria) this;
        }

        public Criteria andButtonTextIn(List<String> values) {
            addCriterion("button_text in", values, "buttonText");
            return (Criteria) this;
        }

        public Criteria andButtonTextNotIn(List<String> values) {
            addCriterion("button_text not in", values, "buttonText");
            return (Criteria) this;
        }

        public Criteria andButtonTextBetween(String value1, String value2) {
            addCriterion("button_text between", value1, value2, "buttonText");
            return (Criteria) this;
        }

        public Criteria andButtonTextNotBetween(String value1, String value2) {
            addCriterion("button_text not between", value1, value2, "buttonText");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}