package com.bilibili.risk.po.risk;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LauMaterialToAuditCreativePo implements Serializable {
    /**
     * 自增主键ID
     */
    private Long id;

    /**
     * 创意id
     */
    private Integer creativeId;

    /**
     * 审核状态（1-待审核，2-审核通过，3-审核不通过）
     */
    private Integer auditStatus;

    /**
     * 软删除，0是有效，1是删除
     */
    private Integer isDeleted;

    /**
     * 版本
     */
    private Integer version;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    /**
     * 创意推审事件时间戳ms
     */
    private Long eventTime;

    private static final long serialVersionUID = 1L;
}