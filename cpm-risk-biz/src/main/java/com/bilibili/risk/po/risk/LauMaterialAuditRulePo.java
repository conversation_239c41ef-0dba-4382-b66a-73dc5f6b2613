package com.bilibili.risk.po.risk;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LauMaterialAuditRulePo implements Serializable {
    /**
     * 自增主键ID
     */
    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 拉取方式1逐个队列领取2时间正序领取
     */
    private Integer pullType;

    /**
     * 角色列表
     */
    @Deprecated
    private String rolesIds;

    /**
     * 备注
     */
    private String remark;

    /**
     * 扩展字段
     */
    private String extra;

    /**
     * 是否删除
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    /**
     * 排序字段
     */
    private Integer seq;

    private static final long serialVersionUID = 1L;
}