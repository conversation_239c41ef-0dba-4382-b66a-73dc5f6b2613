package com.bilibili.risk.po.ad;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public class LauUnderframeComponentPoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public LauUnderframeComponentPoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNull() {
            addCriterion("ctime is null");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNotNull() {
            addCriterion("ctime is not null");
            return (Criteria) this;
        }

        public Criteria andCtimeEqualTo(Timestamp value) {
            addCriterion("ctime =", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotEqualTo(Timestamp value) {
            addCriterion("ctime <>", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThan(Timestamp value) {
            addCriterion("ctime >", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("ctime >=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThan(Timestamp value) {
            addCriterion("ctime <", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("ctime <=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeIn(List<Timestamp> values) {
            addCriterion("ctime in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotIn(List<Timestamp> values) {
            addCriterion("ctime not in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime not between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNull() {
            addCriterion("mtime is null");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNotNull() {
            addCriterion("mtime is not null");
            return (Criteria) this;
        }

        public Criteria andMtimeEqualTo(Timestamp value) {
            addCriterion("mtime =", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotEqualTo(Timestamp value) {
            addCriterion("mtime <>", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThan(Timestamp value) {
            addCriterion("mtime >", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("mtime >=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThan(Timestamp value) {
            addCriterion("mtime <", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("mtime <=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeIn(List<Timestamp> values) {
            addCriterion("mtime in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotIn(List<Timestamp> values) {
            addCriterion("mtime not in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime not between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Integer value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Integer value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Integer value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Integer value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Integer value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Integer> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Integer> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andAccountIdIsNull() {
            addCriterion("account_id is null");
            return (Criteria) this;
        }

        public Criteria andAccountIdIsNotNull() {
            addCriterion("account_id is not null");
            return (Criteria) this;
        }

        public Criteria andAccountIdEqualTo(Integer value) {
            addCriterion("account_id =", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotEqualTo(Integer value) {
            addCriterion("account_id <>", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdGreaterThan(Integer value) {
            addCriterion("account_id >", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("account_id >=", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdLessThan(Integer value) {
            addCriterion("account_id <", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdLessThanOrEqualTo(Integer value) {
            addCriterion("account_id <=", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdIn(List<Integer> values) {
            addCriterion("account_id in", values, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotIn(List<Integer> values) {
            addCriterion("account_id not in", values, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdBetween(Integer value1, Integer value2) {
            addCriterion("account_id between", value1, value2, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotBetween(Integer value1, Integer value2) {
            addCriterion("account_id not between", value1, value2, "accountId");
            return (Criteria) this;
        }

        public Criteria andComponentNameIsNull() {
            addCriterion("component_name is null");
            return (Criteria) this;
        }

        public Criteria andComponentNameIsNotNull() {
            addCriterion("component_name is not null");
            return (Criteria) this;
        }

        public Criteria andComponentNameEqualTo(String value) {
            addCriterion("component_name =", value, "componentName");
            return (Criteria) this;
        }

        public Criteria andComponentNameNotEqualTo(String value) {
            addCriterion("component_name <>", value, "componentName");
            return (Criteria) this;
        }

        public Criteria andComponentNameGreaterThan(String value) {
            addCriterion("component_name >", value, "componentName");
            return (Criteria) this;
        }

        public Criteria andComponentNameGreaterThanOrEqualTo(String value) {
            addCriterion("component_name >=", value, "componentName");
            return (Criteria) this;
        }

        public Criteria andComponentNameLessThan(String value) {
            addCriterion("component_name <", value, "componentName");
            return (Criteria) this;
        }

        public Criteria andComponentNameLessThanOrEqualTo(String value) {
            addCriterion("component_name <=", value, "componentName");
            return (Criteria) this;
        }

        public Criteria andComponentNameLike(String value) {
            addCriterion("component_name like", value, "componentName");
            return (Criteria) this;
        }

        public Criteria andComponentNameNotLike(String value) {
            addCriterion("component_name not like", value, "componentName");
            return (Criteria) this;
        }

        public Criteria andComponentNameIn(List<String> values) {
            addCriterion("component_name in", values, "componentName");
            return (Criteria) this;
        }

        public Criteria andComponentNameNotIn(List<String> values) {
            addCriterion("component_name not in", values, "componentName");
            return (Criteria) this;
        }

        public Criteria andComponentNameBetween(String value1, String value2) {
            addCriterion("component_name between", value1, value2, "componentName");
            return (Criteria) this;
        }

        public Criteria andComponentNameNotBetween(String value1, String value2) {
            addCriterion("component_name not between", value1, value2, "componentName");
            return (Criteria) this;
        }

        public Criteria andComponentHashIsNull() {
            addCriterion("component_hash is null");
            return (Criteria) this;
        }

        public Criteria andComponentHashIsNotNull() {
            addCriterion("component_hash is not null");
            return (Criteria) this;
        }

        public Criteria andComponentHashEqualTo(String value) {
            addCriterion("component_hash =", value, "componentHash");
            return (Criteria) this;
        }

        public Criteria andComponentHashNotEqualTo(String value) {
            addCriterion("component_hash <>", value, "componentHash");
            return (Criteria) this;
        }

        public Criteria andComponentHashGreaterThan(String value) {
            addCriterion("component_hash >", value, "componentHash");
            return (Criteria) this;
        }

        public Criteria andComponentHashGreaterThanOrEqualTo(String value) {
            addCriterion("component_hash >=", value, "componentHash");
            return (Criteria) this;
        }

        public Criteria andComponentHashLessThan(String value) {
            addCriterion("component_hash <", value, "componentHash");
            return (Criteria) this;
        }

        public Criteria andComponentHashLessThanOrEqualTo(String value) {
            addCriterion("component_hash <=", value, "componentHash");
            return (Criteria) this;
        }

        public Criteria andComponentHashLike(String value) {
            addCriterion("component_hash like", value, "componentHash");
            return (Criteria) this;
        }

        public Criteria andComponentHashNotLike(String value) {
            addCriterion("component_hash not like", value, "componentHash");
            return (Criteria) this;
        }

        public Criteria andComponentHashIn(List<String> values) {
            addCriterion("component_hash in", values, "componentHash");
            return (Criteria) this;
        }

        public Criteria andComponentHashNotIn(List<String> values) {
            addCriterion("component_hash not in", values, "componentHash");
            return (Criteria) this;
        }

        public Criteria andComponentHashBetween(String value1, String value2) {
            addCriterion("component_hash between", value1, value2, "componentHash");
            return (Criteria) this;
        }

        public Criteria andComponentHashNotBetween(String value1, String value2) {
            addCriterion("component_hash not between", value1, value2, "componentHash");
            return (Criteria) this;
        }

        public Criteria andPromotionPurposeTypeIsNull() {
            addCriterion("promotion_purpose_type is null");
            return (Criteria) this;
        }

        public Criteria andPromotionPurposeTypeIsNotNull() {
            addCriterion("promotion_purpose_type is not null");
            return (Criteria) this;
        }

        public Criteria andPromotionPurposeTypeEqualTo(Integer value) {
            addCriterion("promotion_purpose_type =", value, "promotionPurposeType");
            return (Criteria) this;
        }

        public Criteria andPromotionPurposeTypeNotEqualTo(Integer value) {
            addCriterion("promotion_purpose_type <>", value, "promotionPurposeType");
            return (Criteria) this;
        }

        public Criteria andPromotionPurposeTypeGreaterThan(Integer value) {
            addCriterion("promotion_purpose_type >", value, "promotionPurposeType");
            return (Criteria) this;
        }

        public Criteria andPromotionPurposeTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("promotion_purpose_type >=", value, "promotionPurposeType");
            return (Criteria) this;
        }

        public Criteria andPromotionPurposeTypeLessThan(Integer value) {
            addCriterion("promotion_purpose_type <", value, "promotionPurposeType");
            return (Criteria) this;
        }

        public Criteria andPromotionPurposeTypeLessThanOrEqualTo(Integer value) {
            addCriterion("promotion_purpose_type <=", value, "promotionPurposeType");
            return (Criteria) this;
        }

        public Criteria andPromotionPurposeTypeIn(List<Integer> values) {
            addCriterion("promotion_purpose_type in", values, "promotionPurposeType");
            return (Criteria) this;
        }

        public Criteria andPromotionPurposeTypeNotIn(List<Integer> values) {
            addCriterion("promotion_purpose_type not in", values, "promotionPurposeType");
            return (Criteria) this;
        }

        public Criteria andPromotionPurposeTypeBetween(Integer value1, Integer value2) {
            addCriterion("promotion_purpose_type between", value1, value2, "promotionPurposeType");
            return (Criteria) this;
        }

        public Criteria andPromotionPurposeTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("promotion_purpose_type not between", value1, value2, "promotionPurposeType");
            return (Criteria) this;
        }

        public Criteria andTitleIsNull() {
            addCriterion("title is null");
            return (Criteria) this;
        }

        public Criteria andTitleIsNotNull() {
            addCriterion("title is not null");
            return (Criteria) this;
        }

        public Criteria andTitleEqualTo(String value) {
            addCriterion("title =", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotEqualTo(String value) {
            addCriterion("title <>", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleGreaterThan(String value) {
            addCriterion("title >", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleGreaterThanOrEqualTo(String value) {
            addCriterion("title >=", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLessThan(String value) {
            addCriterion("title <", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLessThanOrEqualTo(String value) {
            addCriterion("title <=", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLike(String value) {
            addCriterion("title like", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotLike(String value) {
            addCriterion("title not like", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleIn(List<String> values) {
            addCriterion("title in", values, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotIn(List<String> values) {
            addCriterion("title not in", values, "title");
            return (Criteria) this;
        }

        public Criteria andTitleBetween(String value1, String value2) {
            addCriterion("title between", value1, value2, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotBetween(String value1, String value2) {
            addCriterion("title not between", value1, value2, "title");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNull() {
            addCriterion("description is null");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNotNull() {
            addCriterion("description is not null");
            return (Criteria) this;
        }

        public Criteria andDescriptionEqualTo(String value) {
            addCriterion("description =", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotEqualTo(String value) {
            addCriterion("description <>", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThan(String value) {
            addCriterion("description >", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("description >=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThan(String value) {
            addCriterion("description <", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThanOrEqualTo(String value) {
            addCriterion("description <=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLike(String value) {
            addCriterion("description like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotLike(String value) {
            addCriterion("description not like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionIn(List<String> values) {
            addCriterion("description in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotIn(List<String> values) {
            addCriterion("description not in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionBetween(String value1, String value2) {
            addCriterion("description between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotBetween(String value1, String value2) {
            addCriterion("description not between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andImageUrlIsNull() {
            addCriterion("image_url is null");
            return (Criteria) this;
        }

        public Criteria andImageUrlIsNotNull() {
            addCriterion("image_url is not null");
            return (Criteria) this;
        }

        public Criteria andImageUrlEqualTo(String value) {
            addCriterion("image_url =", value, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlNotEqualTo(String value) {
            addCriterion("image_url <>", value, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlGreaterThan(String value) {
            addCriterion("image_url >", value, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlGreaterThanOrEqualTo(String value) {
            addCriterion("image_url >=", value, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlLessThan(String value) {
            addCriterion("image_url <", value, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlLessThanOrEqualTo(String value) {
            addCriterion("image_url <=", value, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlLike(String value) {
            addCriterion("image_url like", value, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlNotLike(String value) {
            addCriterion("image_url not like", value, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlIn(List<String> values) {
            addCriterion("image_url in", values, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlNotIn(List<String> values) {
            addCriterion("image_url not in", values, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlBetween(String value1, String value2) {
            addCriterion("image_url between", value1, value2, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlNotBetween(String value1, String value2) {
            addCriterion("image_url not between", value1, value2, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageMd5IsNull() {
            addCriterion("image_md5 is null");
            return (Criteria) this;
        }

        public Criteria andImageMd5IsNotNull() {
            addCriterion("image_md5 is not null");
            return (Criteria) this;
        }

        public Criteria andImageMd5EqualTo(String value) {
            addCriterion("image_md5 =", value, "imageMd5");
            return (Criteria) this;
        }

        public Criteria andImageMd5NotEqualTo(String value) {
            addCriterion("image_md5 <>", value, "imageMd5");
            return (Criteria) this;
        }

        public Criteria andImageMd5GreaterThan(String value) {
            addCriterion("image_md5 >", value, "imageMd5");
            return (Criteria) this;
        }

        public Criteria andImageMd5GreaterThanOrEqualTo(String value) {
            addCriterion("image_md5 >=", value, "imageMd5");
            return (Criteria) this;
        }

        public Criteria andImageMd5LessThan(String value) {
            addCriterion("image_md5 <", value, "imageMd5");
            return (Criteria) this;
        }

        public Criteria andImageMd5LessThanOrEqualTo(String value) {
            addCriterion("image_md5 <=", value, "imageMd5");
            return (Criteria) this;
        }

        public Criteria andImageMd5Like(String value) {
            addCriterion("image_md5 like", value, "imageMd5");
            return (Criteria) this;
        }

        public Criteria andImageMd5NotLike(String value) {
            addCriterion("image_md5 not like", value, "imageMd5");
            return (Criteria) this;
        }

        public Criteria andImageMd5In(List<String> values) {
            addCriterion("image_md5 in", values, "imageMd5");
            return (Criteria) this;
        }

        public Criteria andImageMd5NotIn(List<String> values) {
            addCriterion("image_md5 not in", values, "imageMd5");
            return (Criteria) this;
        }

        public Criteria andImageMd5Between(String value1, String value2) {
            addCriterion("image_md5 between", value1, value2, "imageMd5");
            return (Criteria) this;
        }

        public Criteria andImageMd5NotBetween(String value1, String value2) {
            addCriterion("image_md5 not between", value1, value2, "imageMd5");
            return (Criteria) this;
        }

        public Criteria andRawJumpUrlIsNull() {
            addCriterion("raw_jump_url is null");
            return (Criteria) this;
        }

        public Criteria andRawJumpUrlIsNotNull() {
            addCriterion("raw_jump_url is not null");
            return (Criteria) this;
        }

        public Criteria andRawJumpUrlEqualTo(String value) {
            addCriterion("raw_jump_url =", value, "rawJumpUrl");
            return (Criteria) this;
        }

        public Criteria andRawJumpUrlNotEqualTo(String value) {
            addCriterion("raw_jump_url <>", value, "rawJumpUrl");
            return (Criteria) this;
        }

        public Criteria andRawJumpUrlGreaterThan(String value) {
            addCriterion("raw_jump_url >", value, "rawJumpUrl");
            return (Criteria) this;
        }

        public Criteria andRawJumpUrlGreaterThanOrEqualTo(String value) {
            addCriterion("raw_jump_url >=", value, "rawJumpUrl");
            return (Criteria) this;
        }

        public Criteria andRawJumpUrlLessThan(String value) {
            addCriterion("raw_jump_url <", value, "rawJumpUrl");
            return (Criteria) this;
        }

        public Criteria andRawJumpUrlLessThanOrEqualTo(String value) {
            addCriterion("raw_jump_url <=", value, "rawJumpUrl");
            return (Criteria) this;
        }

        public Criteria andRawJumpUrlLike(String value) {
            addCriterion("raw_jump_url like", value, "rawJumpUrl");
            return (Criteria) this;
        }

        public Criteria andRawJumpUrlNotLike(String value) {
            addCriterion("raw_jump_url not like", value, "rawJumpUrl");
            return (Criteria) this;
        }

        public Criteria andRawJumpUrlIn(List<String> values) {
            addCriterion("raw_jump_url in", values, "rawJumpUrl");
            return (Criteria) this;
        }

        public Criteria andRawJumpUrlNotIn(List<String> values) {
            addCriterion("raw_jump_url not in", values, "rawJumpUrl");
            return (Criteria) this;
        }

        public Criteria andRawJumpUrlBetween(String value1, String value2) {
            addCriterion("raw_jump_url between", value1, value2, "rawJumpUrl");
            return (Criteria) this;
        }

        public Criteria andRawJumpUrlNotBetween(String value1, String value2) {
            addCriterion("raw_jump_url not between", value1, value2, "rawJumpUrl");
            return (Criteria) this;
        }

        public Criteria andJumpUrlIsNull() {
            addCriterion("jump_url is null");
            return (Criteria) this;
        }

        public Criteria andJumpUrlIsNotNull() {
            addCriterion("jump_url is not null");
            return (Criteria) this;
        }

        public Criteria andJumpUrlEqualTo(String value) {
            addCriterion("jump_url =", value, "jumpUrl");
            return (Criteria) this;
        }

        public Criteria andJumpUrlNotEqualTo(String value) {
            addCriterion("jump_url <>", value, "jumpUrl");
            return (Criteria) this;
        }

        public Criteria andJumpUrlGreaterThan(String value) {
            addCriterion("jump_url >", value, "jumpUrl");
            return (Criteria) this;
        }

        public Criteria andJumpUrlGreaterThanOrEqualTo(String value) {
            addCriterion("jump_url >=", value, "jumpUrl");
            return (Criteria) this;
        }

        public Criteria andJumpUrlLessThan(String value) {
            addCriterion("jump_url <", value, "jumpUrl");
            return (Criteria) this;
        }

        public Criteria andJumpUrlLessThanOrEqualTo(String value) {
            addCriterion("jump_url <=", value, "jumpUrl");
            return (Criteria) this;
        }

        public Criteria andJumpUrlLike(String value) {
            addCriterion("jump_url like", value, "jumpUrl");
            return (Criteria) this;
        }

        public Criteria andJumpUrlNotLike(String value) {
            addCriterion("jump_url not like", value, "jumpUrl");
            return (Criteria) this;
        }

        public Criteria andJumpUrlIn(List<String> values) {
            addCriterion("jump_url in", values, "jumpUrl");
            return (Criteria) this;
        }

        public Criteria andJumpUrlNotIn(List<String> values) {
            addCriterion("jump_url not in", values, "jumpUrl");
            return (Criteria) this;
        }

        public Criteria andJumpUrlBetween(String value1, String value2) {
            addCriterion("jump_url between", value1, value2, "jumpUrl");
            return (Criteria) this;
        }

        public Criteria andJumpUrlNotBetween(String value1, String value2) {
            addCriterion("jump_url not between", value1, value2, "jumpUrl");
            return (Criteria) this;
        }

        public Criteria andMgkPageIdIsNull() {
            addCriterion("mgk_page_id is null");
            return (Criteria) this;
        }

        public Criteria andMgkPageIdIsNotNull() {
            addCriterion("mgk_page_id is not null");
            return (Criteria) this;
        }

        public Criteria andMgkPageIdEqualTo(Long value) {
            addCriterion("mgk_page_id =", value, "mgkPageId");
            return (Criteria) this;
        }

        public Criteria andMgkPageIdNotEqualTo(Long value) {
            addCriterion("mgk_page_id <>", value, "mgkPageId");
            return (Criteria) this;
        }

        public Criteria andMgkPageIdGreaterThan(Long value) {
            addCriterion("mgk_page_id >", value, "mgkPageId");
            return (Criteria) this;
        }

        public Criteria andMgkPageIdGreaterThanOrEqualTo(Long value) {
            addCriterion("mgk_page_id >=", value, "mgkPageId");
            return (Criteria) this;
        }

        public Criteria andMgkPageIdLessThan(Long value) {
            addCriterion("mgk_page_id <", value, "mgkPageId");
            return (Criteria) this;
        }

        public Criteria andMgkPageIdLessThanOrEqualTo(Long value) {
            addCriterion("mgk_page_id <=", value, "mgkPageId");
            return (Criteria) this;
        }

        public Criteria andMgkPageIdIn(List<Long> values) {
            addCriterion("mgk_page_id in", values, "mgkPageId");
            return (Criteria) this;
        }

        public Criteria andMgkPageIdNotIn(List<Long> values) {
            addCriterion("mgk_page_id not in", values, "mgkPageId");
            return (Criteria) this;
        }

        public Criteria andMgkPageIdBetween(Long value1, Long value2) {
            addCriterion("mgk_page_id between", value1, value2, "mgkPageId");
            return (Criteria) this;
        }

        public Criteria andMgkPageIdNotBetween(Long value1, Long value2) {
            addCriterion("mgk_page_id not between", value1, value2, "mgkPageId");
            return (Criteria) this;
        }

        public Criteria andButtonIdIsNull() {
            addCriterion("button_id is null");
            return (Criteria) this;
        }

        public Criteria andButtonIdIsNotNull() {
            addCriterion("button_id is not null");
            return (Criteria) this;
        }

        public Criteria andButtonIdEqualTo(Integer value) {
            addCriterion("button_id =", value, "buttonId");
            return (Criteria) this;
        }

        public Criteria andButtonIdNotEqualTo(Integer value) {
            addCriterion("button_id <>", value, "buttonId");
            return (Criteria) this;
        }

        public Criteria andButtonIdGreaterThan(Integer value) {
            addCriterion("button_id >", value, "buttonId");
            return (Criteria) this;
        }

        public Criteria andButtonIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("button_id >=", value, "buttonId");
            return (Criteria) this;
        }

        public Criteria andButtonIdLessThan(Integer value) {
            addCriterion("button_id <", value, "buttonId");
            return (Criteria) this;
        }

        public Criteria andButtonIdLessThanOrEqualTo(Integer value) {
            addCriterion("button_id <=", value, "buttonId");
            return (Criteria) this;
        }

        public Criteria andButtonIdIn(List<Integer> values) {
            addCriterion("button_id in", values, "buttonId");
            return (Criteria) this;
        }

        public Criteria andButtonIdNotIn(List<Integer> values) {
            addCriterion("button_id not in", values, "buttonId");
            return (Criteria) this;
        }

        public Criteria andButtonIdBetween(Integer value1, Integer value2) {
            addCriterion("button_id between", value1, value2, "buttonId");
            return (Criteria) this;
        }

        public Criteria andButtonIdNotBetween(Integer value1, Integer value2) {
            addCriterion("button_id not between", value1, value2, "buttonId");
            return (Criteria) this;
        }

        public Criteria andButtonTypeIsNull() {
            addCriterion("button_type is null");
            return (Criteria) this;
        }

        public Criteria andButtonTypeIsNotNull() {
            addCriterion("button_type is not null");
            return (Criteria) this;
        }

        public Criteria andButtonTypeEqualTo(Integer value) {
            addCriterion("button_type =", value, "buttonType");
            return (Criteria) this;
        }

        public Criteria andButtonTypeNotEqualTo(Integer value) {
            addCriterion("button_type <>", value, "buttonType");
            return (Criteria) this;
        }

        public Criteria andButtonTypeGreaterThan(Integer value) {
            addCriterion("button_type >", value, "buttonType");
            return (Criteria) this;
        }

        public Criteria andButtonTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("button_type >=", value, "buttonType");
            return (Criteria) this;
        }

        public Criteria andButtonTypeLessThan(Integer value) {
            addCriterion("button_type <", value, "buttonType");
            return (Criteria) this;
        }

        public Criteria andButtonTypeLessThanOrEqualTo(Integer value) {
            addCriterion("button_type <=", value, "buttonType");
            return (Criteria) this;
        }

        public Criteria andButtonTypeIn(List<Integer> values) {
            addCriterion("button_type in", values, "buttonType");
            return (Criteria) this;
        }

        public Criteria andButtonTypeNotIn(List<Integer> values) {
            addCriterion("button_type not in", values, "buttonType");
            return (Criteria) this;
        }

        public Criteria andButtonTypeBetween(Integer value1, Integer value2) {
            addCriterion("button_type between", value1, value2, "buttonType");
            return (Criteria) this;
        }

        public Criteria andButtonTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("button_type not between", value1, value2, "buttonType");
            return (Criteria) this;
        }

        public Criteria andButtonTextIsNull() {
            addCriterion("button_text is null");
            return (Criteria) this;
        }

        public Criteria andButtonTextIsNotNull() {
            addCriterion("button_text is not null");
            return (Criteria) this;
        }

        public Criteria andButtonTextEqualTo(String value) {
            addCriterion("button_text =", value, "buttonText");
            return (Criteria) this;
        }

        public Criteria andButtonTextNotEqualTo(String value) {
            addCriterion("button_text <>", value, "buttonText");
            return (Criteria) this;
        }

        public Criteria andButtonTextGreaterThan(String value) {
            addCriterion("button_text >", value, "buttonText");
            return (Criteria) this;
        }

        public Criteria andButtonTextGreaterThanOrEqualTo(String value) {
            addCriterion("button_text >=", value, "buttonText");
            return (Criteria) this;
        }

        public Criteria andButtonTextLessThan(String value) {
            addCriterion("button_text <", value, "buttonText");
            return (Criteria) this;
        }

        public Criteria andButtonTextLessThanOrEqualTo(String value) {
            addCriterion("button_text <=", value, "buttonText");
            return (Criteria) this;
        }

        public Criteria andButtonTextLike(String value) {
            addCriterion("button_text like", value, "buttonText");
            return (Criteria) this;
        }

        public Criteria andButtonTextNotLike(String value) {
            addCriterion("button_text not like", value, "buttonText");
            return (Criteria) this;
        }

        public Criteria andButtonTextIn(List<String> values) {
            addCriterion("button_text in", values, "buttonText");
            return (Criteria) this;
        }

        public Criteria andButtonTextNotIn(List<String> values) {
            addCriterion("button_text not in", values, "buttonText");
            return (Criteria) this;
        }

        public Criteria andButtonTextBetween(String value1, String value2) {
            addCriterion("button_text between", value1, value2, "buttonText");
            return (Criteria) this;
        }

        public Criteria andButtonTextNotBetween(String value1, String value2) {
            addCriterion("button_text not between", value1, value2, "buttonText");
            return (Criteria) this;
        }

        public Criteria andGameBaseIdIsNull() {
            addCriterion("game_base_id is null");
            return (Criteria) this;
        }

        public Criteria andGameBaseIdIsNotNull() {
            addCriterion("game_base_id is not null");
            return (Criteria) this;
        }

        public Criteria andGameBaseIdEqualTo(Integer value) {
            addCriterion("game_base_id =", value, "gameBaseId");
            return (Criteria) this;
        }

        public Criteria andGameBaseIdNotEqualTo(Integer value) {
            addCriterion("game_base_id <>", value, "gameBaseId");
            return (Criteria) this;
        }

        public Criteria andGameBaseIdGreaterThan(Integer value) {
            addCriterion("game_base_id >", value, "gameBaseId");
            return (Criteria) this;
        }

        public Criteria andGameBaseIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("game_base_id >=", value, "gameBaseId");
            return (Criteria) this;
        }

        public Criteria andGameBaseIdLessThan(Integer value) {
            addCriterion("game_base_id <", value, "gameBaseId");
            return (Criteria) this;
        }

        public Criteria andGameBaseIdLessThanOrEqualTo(Integer value) {
            addCriterion("game_base_id <=", value, "gameBaseId");
            return (Criteria) this;
        }

        public Criteria andGameBaseIdIn(List<Integer> values) {
            addCriterion("game_base_id in", values, "gameBaseId");
            return (Criteria) this;
        }

        public Criteria andGameBaseIdNotIn(List<Integer> values) {
            addCriterion("game_base_id not in", values, "gameBaseId");
            return (Criteria) this;
        }

        public Criteria andGameBaseIdBetween(Integer value1, Integer value2) {
            addCriterion("game_base_id between", value1, value2, "gameBaseId");
            return (Criteria) this;
        }

        public Criteria andGameBaseIdNotBetween(Integer value1, Integer value2) {
            addCriterion("game_base_id not between", value1, value2, "gameBaseId");
            return (Criteria) this;
        }

        public Criteria andAppPackageIdIsNull() {
            addCriterion("app_package_id is null");
            return (Criteria) this;
        }

        public Criteria andAppPackageIdIsNotNull() {
            addCriterion("app_package_id is not null");
            return (Criteria) this;
        }

        public Criteria andAppPackageIdEqualTo(Integer value) {
            addCriterion("app_package_id =", value, "appPackageId");
            return (Criteria) this;
        }

        public Criteria andAppPackageIdNotEqualTo(Integer value) {
            addCriterion("app_package_id <>", value, "appPackageId");
            return (Criteria) this;
        }

        public Criteria andAppPackageIdGreaterThan(Integer value) {
            addCriterion("app_package_id >", value, "appPackageId");
            return (Criteria) this;
        }

        public Criteria andAppPackageIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("app_package_id >=", value, "appPackageId");
            return (Criteria) this;
        }

        public Criteria andAppPackageIdLessThan(Integer value) {
            addCriterion("app_package_id <", value, "appPackageId");
            return (Criteria) this;
        }

        public Criteria andAppPackageIdLessThanOrEqualTo(Integer value) {
            addCriterion("app_package_id <=", value, "appPackageId");
            return (Criteria) this;
        }

        public Criteria andAppPackageIdIn(List<Integer> values) {
            addCriterion("app_package_id in", values, "appPackageId");
            return (Criteria) this;
        }

        public Criteria andAppPackageIdNotIn(List<Integer> values) {
            addCriterion("app_package_id not in", values, "appPackageId");
            return (Criteria) this;
        }

        public Criteria andAppPackageIdBetween(Integer value1, Integer value2) {
            addCriterion("app_package_id between", value1, value2, "appPackageId");
            return (Criteria) this;
        }

        public Criteria andAppPackageIdNotBetween(Integer value1, Integer value2) {
            addCriterion("app_package_id not between", value1, value2, "appPackageId");
            return (Criteria) this;
        }

        public Criteria andCallUpUrlIsNull() {
            addCriterion("call_up_url is null");
            return (Criteria) this;
        }

        public Criteria andCallUpUrlIsNotNull() {
            addCriterion("call_up_url is not null");
            return (Criteria) this;
        }

        public Criteria andCallUpUrlEqualTo(String value) {
            addCriterion("call_up_url =", value, "callUpUrl");
            return (Criteria) this;
        }

        public Criteria andCallUpUrlNotEqualTo(String value) {
            addCriterion("call_up_url <>", value, "callUpUrl");
            return (Criteria) this;
        }

        public Criteria andCallUpUrlGreaterThan(String value) {
            addCriterion("call_up_url >", value, "callUpUrl");
            return (Criteria) this;
        }

        public Criteria andCallUpUrlGreaterThanOrEqualTo(String value) {
            addCriterion("call_up_url >=", value, "callUpUrl");
            return (Criteria) this;
        }

        public Criteria andCallUpUrlLessThan(String value) {
            addCriterion("call_up_url <", value, "callUpUrl");
            return (Criteria) this;
        }

        public Criteria andCallUpUrlLessThanOrEqualTo(String value) {
            addCriterion("call_up_url <=", value, "callUpUrl");
            return (Criteria) this;
        }

        public Criteria andCallUpUrlLike(String value) {
            addCriterion("call_up_url like", value, "callUpUrl");
            return (Criteria) this;
        }

        public Criteria andCallUpUrlNotLike(String value) {
            addCriterion("call_up_url not like", value, "callUpUrl");
            return (Criteria) this;
        }

        public Criteria andCallUpUrlIn(List<String> values) {
            addCriterion("call_up_url in", values, "callUpUrl");
            return (Criteria) this;
        }

        public Criteria andCallUpUrlNotIn(List<String> values) {
            addCriterion("call_up_url not in", values, "callUpUrl");
            return (Criteria) this;
        }

        public Criteria andCallUpUrlBetween(String value1, String value2) {
            addCriterion("call_up_url between", value1, value2, "callUpUrl");
            return (Criteria) this;
        }

        public Criteria andCallUpUrlNotBetween(String value1, String value2) {
            addCriterion("call_up_url not between", value1, value2, "callUpUrl");
            return (Criteria) this;
        }

        public Criteria andSubPkgIsNull() {
            addCriterion("sub_pkg is null");
            return (Criteria) this;
        }

        public Criteria andSubPkgIsNotNull() {
            addCriterion("sub_pkg is not null");
            return (Criteria) this;
        }

        public Criteria andSubPkgEqualTo(Integer value) {
            addCriterion("sub_pkg =", value, "subPkg");
            return (Criteria) this;
        }

        public Criteria andSubPkgNotEqualTo(Integer value) {
            addCriterion("sub_pkg <>", value, "subPkg");
            return (Criteria) this;
        }

        public Criteria andSubPkgGreaterThan(Integer value) {
            addCriterion("sub_pkg >", value, "subPkg");
            return (Criteria) this;
        }

        public Criteria andSubPkgGreaterThanOrEqualTo(Integer value) {
            addCriterion("sub_pkg >=", value, "subPkg");
            return (Criteria) this;
        }

        public Criteria andSubPkgLessThan(Integer value) {
            addCriterion("sub_pkg <", value, "subPkg");
            return (Criteria) this;
        }

        public Criteria andSubPkgLessThanOrEqualTo(Integer value) {
            addCriterion("sub_pkg <=", value, "subPkg");
            return (Criteria) this;
        }

        public Criteria andSubPkgIn(List<Integer> values) {
            addCriterion("sub_pkg in", values, "subPkg");
            return (Criteria) this;
        }

        public Criteria andSubPkgNotIn(List<Integer> values) {
            addCriterion("sub_pkg not in", values, "subPkg");
            return (Criteria) this;
        }

        public Criteria andSubPkgBetween(Integer value1, Integer value2) {
            addCriterion("sub_pkg between", value1, value2, "subPkg");
            return (Criteria) this;
        }

        public Criteria andSubPkgNotBetween(Integer value1, Integer value2) {
            addCriterion("sub_pkg not between", value1, value2, "subPkg");
            return (Criteria) this;
        }

        public Criteria andCustomizedImpUrlIsNull() {
            addCriterion("customized_imp_url is null");
            return (Criteria) this;
        }

        public Criteria andCustomizedImpUrlIsNotNull() {
            addCriterion("customized_imp_url is not null");
            return (Criteria) this;
        }

        public Criteria andCustomizedImpUrlEqualTo(String value) {
            addCriterion("customized_imp_url =", value, "customizedImpUrl");
            return (Criteria) this;
        }

        public Criteria andCustomizedImpUrlNotEqualTo(String value) {
            addCriterion("customized_imp_url <>", value, "customizedImpUrl");
            return (Criteria) this;
        }

        public Criteria andCustomizedImpUrlGreaterThan(String value) {
            addCriterion("customized_imp_url >", value, "customizedImpUrl");
            return (Criteria) this;
        }

        public Criteria andCustomizedImpUrlGreaterThanOrEqualTo(String value) {
            addCriterion("customized_imp_url >=", value, "customizedImpUrl");
            return (Criteria) this;
        }

        public Criteria andCustomizedImpUrlLessThan(String value) {
            addCriterion("customized_imp_url <", value, "customizedImpUrl");
            return (Criteria) this;
        }

        public Criteria andCustomizedImpUrlLessThanOrEqualTo(String value) {
            addCriterion("customized_imp_url <=", value, "customizedImpUrl");
            return (Criteria) this;
        }

        public Criteria andCustomizedImpUrlLike(String value) {
            addCriterion("customized_imp_url like", value, "customizedImpUrl");
            return (Criteria) this;
        }

        public Criteria andCustomizedImpUrlNotLike(String value) {
            addCriterion("customized_imp_url not like", value, "customizedImpUrl");
            return (Criteria) this;
        }

        public Criteria andCustomizedImpUrlIn(List<String> values) {
            addCriterion("customized_imp_url in", values, "customizedImpUrl");
            return (Criteria) this;
        }

        public Criteria andCustomizedImpUrlNotIn(List<String> values) {
            addCriterion("customized_imp_url not in", values, "customizedImpUrl");
            return (Criteria) this;
        }

        public Criteria andCustomizedImpUrlBetween(String value1, String value2) {
            addCriterion("customized_imp_url between", value1, value2, "customizedImpUrl");
            return (Criteria) this;
        }

        public Criteria andCustomizedImpUrlNotBetween(String value1, String value2) {
            addCriterion("customized_imp_url not between", value1, value2, "customizedImpUrl");
            return (Criteria) this;
        }

        public Criteria andCustomizedClickUrlIsNull() {
            addCriterion("customized_click_url is null");
            return (Criteria) this;
        }

        public Criteria andCustomizedClickUrlIsNotNull() {
            addCriterion("customized_click_url is not null");
            return (Criteria) this;
        }

        public Criteria andCustomizedClickUrlEqualTo(String value) {
            addCriterion("customized_click_url =", value, "customizedClickUrl");
            return (Criteria) this;
        }

        public Criteria andCustomizedClickUrlNotEqualTo(String value) {
            addCriterion("customized_click_url <>", value, "customizedClickUrl");
            return (Criteria) this;
        }

        public Criteria andCustomizedClickUrlGreaterThan(String value) {
            addCriterion("customized_click_url >", value, "customizedClickUrl");
            return (Criteria) this;
        }

        public Criteria andCustomizedClickUrlGreaterThanOrEqualTo(String value) {
            addCriterion("customized_click_url >=", value, "customizedClickUrl");
            return (Criteria) this;
        }

        public Criteria andCustomizedClickUrlLessThan(String value) {
            addCriterion("customized_click_url <", value, "customizedClickUrl");
            return (Criteria) this;
        }

        public Criteria andCustomizedClickUrlLessThanOrEqualTo(String value) {
            addCriterion("customized_click_url <=", value, "customizedClickUrl");
            return (Criteria) this;
        }

        public Criteria andCustomizedClickUrlLike(String value) {
            addCriterion("customized_click_url like", value, "customizedClickUrl");
            return (Criteria) this;
        }

        public Criteria andCustomizedClickUrlNotLike(String value) {
            addCriterion("customized_click_url not like", value, "customizedClickUrl");
            return (Criteria) this;
        }

        public Criteria andCustomizedClickUrlIn(List<String> values) {
            addCriterion("customized_click_url in", values, "customizedClickUrl");
            return (Criteria) this;
        }

        public Criteria andCustomizedClickUrlNotIn(List<String> values) {
            addCriterion("customized_click_url not in", values, "customizedClickUrl");
            return (Criteria) this;
        }

        public Criteria andCustomizedClickUrlBetween(String value1, String value2) {
            addCriterion("customized_click_url between", value1, value2, "customizedClickUrl");
            return (Criteria) this;
        }

        public Criteria andCustomizedClickUrlNotBetween(String value1, String value2) {
            addCriterion("customized_click_url not between", value1, value2, "customizedClickUrl");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}