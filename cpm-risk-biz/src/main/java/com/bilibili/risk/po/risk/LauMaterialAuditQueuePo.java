package com.bilibili.risk.po.risk;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LauMaterialAuditQueuePo implements Serializable {
    /**
     * 自增主键ID
     */
    private Long id;

    /**
     * 队列名称
     */
    private String name;

    /**
     * 业务类型:1三连,2必火
     */
    private Integer bizType;

    /**
     * 素材类型
     */
    private Integer materialType;

    /**
     * 拉取数量
     */
    private Long pullNum;

    /**
     * 任务超时时长，单位分钟
     */
    private Long taskTimeout;

    /**
     * 角色列表
     */
    private String roleIds;

    /**
     * 是否删除
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    /**
     * 排序字段
     */
    private Integer seq;

    /**
     * 队列类型，普通队列：0， 标签队列：1
     */
    private Integer queueType;

    /**
     * 标签ID
     */
    private Integer tagId;

    private static final long serialVersionUID = 1L;
}