package com.bilibili.risk.po.ad;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public class LauUnitExtraPoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public LauUnitExtraPoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNull() {
            addCriterion("ctime is null");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNotNull() {
            addCriterion("ctime is not null");
            return (Criteria) this;
        }

        public Criteria andCtimeEqualTo(Timestamp value) {
            addCriterion("ctime =", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotEqualTo(Timestamp value) {
            addCriterion("ctime <>", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThan(Timestamp value) {
            addCriterion("ctime >", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("ctime >=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThan(Timestamp value) {
            addCriterion("ctime <", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("ctime <=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeIn(List<Timestamp> values) {
            addCriterion("ctime in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotIn(List<Timestamp> values) {
            addCriterion("ctime not in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime not between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNull() {
            addCriterion("mtime is null");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNotNull() {
            addCriterion("mtime is not null");
            return (Criteria) this;
        }

        public Criteria andMtimeEqualTo(Timestamp value) {
            addCriterion("mtime =", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotEqualTo(Timestamp value) {
            addCriterion("mtime <>", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThan(Timestamp value) {
            addCriterion("mtime >", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("mtime >=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThan(Timestamp value) {
            addCriterion("mtime <", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("mtime <=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeIn(List<Timestamp> values) {
            addCriterion("mtime in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotIn(List<Timestamp> values) {
            addCriterion("mtime not in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime not between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andAccountIdIsNull() {
            addCriterion("account_id is null");
            return (Criteria) this;
        }

        public Criteria andAccountIdIsNotNull() {
            addCriterion("account_id is not null");
            return (Criteria) this;
        }

        public Criteria andAccountIdEqualTo(Integer value) {
            addCriterion("account_id =", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotEqualTo(Integer value) {
            addCriterion("account_id <>", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdGreaterThan(Integer value) {
            addCriterion("account_id >", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("account_id >=", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdLessThan(Integer value) {
            addCriterion("account_id <", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdLessThanOrEqualTo(Integer value) {
            addCriterion("account_id <=", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdIn(List<Integer> values) {
            addCriterion("account_id in", values, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotIn(List<Integer> values) {
            addCriterion("account_id not in", values, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdBetween(Integer value1, Integer value2) {
            addCriterion("account_id between", value1, value2, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotBetween(Integer value1, Integer value2) {
            addCriterion("account_id not between", value1, value2, "accountId");
            return (Criteria) this;
        }

        public Criteria andUnitIdIsNull() {
            addCriterion("unit_id is null");
            return (Criteria) this;
        }

        public Criteria andUnitIdIsNotNull() {
            addCriterion("unit_id is not null");
            return (Criteria) this;
        }

        public Criteria andUnitIdEqualTo(Integer value) {
            addCriterion("unit_id =", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdNotEqualTo(Integer value) {
            addCriterion("unit_id <>", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdGreaterThan(Integer value) {
            addCriterion("unit_id >", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("unit_id >=", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdLessThan(Integer value) {
            addCriterion("unit_id <", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdLessThanOrEqualTo(Integer value) {
            addCriterion("unit_id <=", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdIn(List<Integer> values) {
            addCriterion("unit_id in", values, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdNotIn(List<Integer> values) {
            addCriterion("unit_id not in", values, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdBetween(Integer value1, Integer value2) {
            addCriterion("unit_id between", value1, value2, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdNotBetween(Integer value1, Integer value2) {
            addCriterion("unit_id not between", value1, value2, "unitId");
            return (Criteria) this;
        }

        public Criteria andIsBiliNativeIsNull() {
            addCriterion("is_bili_native is null");
            return (Criteria) this;
        }

        public Criteria andIsBiliNativeIsNotNull() {
            addCriterion("is_bili_native is not null");
            return (Criteria) this;
        }

        public Criteria andIsBiliNativeEqualTo(Integer value) {
            addCriterion("is_bili_native =", value, "isBiliNative");
            return (Criteria) this;
        }

        public Criteria andIsBiliNativeNotEqualTo(Integer value) {
            addCriterion("is_bili_native <>", value, "isBiliNative");
            return (Criteria) this;
        }

        public Criteria andIsBiliNativeGreaterThan(Integer value) {
            addCriterion("is_bili_native >", value, "isBiliNative");
            return (Criteria) this;
        }

        public Criteria andIsBiliNativeGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_bili_native >=", value, "isBiliNative");
            return (Criteria) this;
        }

        public Criteria andIsBiliNativeLessThan(Integer value) {
            addCriterion("is_bili_native <", value, "isBiliNative");
            return (Criteria) this;
        }

        public Criteria andIsBiliNativeLessThanOrEqualTo(Integer value) {
            addCriterion("is_bili_native <=", value, "isBiliNative");
            return (Criteria) this;
        }

        public Criteria andIsBiliNativeIn(List<Integer> values) {
            addCriterion("is_bili_native in", values, "isBiliNative");
            return (Criteria) this;
        }

        public Criteria andIsBiliNativeNotIn(List<Integer> values) {
            addCriterion("is_bili_native not in", values, "isBiliNative");
            return (Criteria) this;
        }

        public Criteria andIsBiliNativeBetween(Integer value1, Integer value2) {
            addCriterion("is_bili_native between", value1, value2, "isBiliNative");
            return (Criteria) this;
        }

        public Criteria andIsBiliNativeNotBetween(Integer value1, Integer value2) {
            addCriterion("is_bili_native not between", value1, value2, "isBiliNative");
            return (Criteria) this;
        }

        public Criteria andTagsIsNull() {
            addCriterion("tags is null");
            return (Criteria) this;
        }

        public Criteria andTagsIsNotNull() {
            addCriterion("tags is not null");
            return (Criteria) this;
        }

        public Criteria andTagsEqualTo(String value) {
            addCriterion("tags =", value, "tags");
            return (Criteria) this;
        }

        public Criteria andTagsNotEqualTo(String value) {
            addCriterion("tags <>", value, "tags");
            return (Criteria) this;
        }

        public Criteria andTagsGreaterThan(String value) {
            addCriterion("tags >", value, "tags");
            return (Criteria) this;
        }

        public Criteria andTagsGreaterThanOrEqualTo(String value) {
            addCriterion("tags >=", value, "tags");
            return (Criteria) this;
        }

        public Criteria andTagsLessThan(String value) {
            addCriterion("tags <", value, "tags");
            return (Criteria) this;
        }

        public Criteria andTagsLessThanOrEqualTo(String value) {
            addCriterion("tags <=", value, "tags");
            return (Criteria) this;
        }

        public Criteria andTagsLike(String value) {
            addCriterion("tags like", value, "tags");
            return (Criteria) this;
        }

        public Criteria andTagsNotLike(String value) {
            addCriterion("tags not like", value, "tags");
            return (Criteria) this;
        }

        public Criteria andTagsIn(List<String> values) {
            addCriterion("tags in", values, "tags");
            return (Criteria) this;
        }

        public Criteria andTagsNotIn(List<String> values) {
            addCriterion("tags not in", values, "tags");
            return (Criteria) this;
        }

        public Criteria andTagsBetween(String value1, String value2) {
            addCriterion("tags between", value1, value2, "tags");
            return (Criteria) this;
        }

        public Criteria andTagsNotBetween(String value1, String value2) {
            addCriterion("tags not between", value1, value2, "tags");
            return (Criteria) this;
        }

        public Criteria andDeviceAppStoreIsNull() {
            addCriterion("device_app_store is null");
            return (Criteria) this;
        }

        public Criteria andDeviceAppStoreIsNotNull() {
            addCriterion("device_app_store is not null");
            return (Criteria) this;
        }

        public Criteria andDeviceAppStoreEqualTo(String value) {
            addCriterion("device_app_store =", value, "deviceAppStore");
            return (Criteria) this;
        }

        public Criteria andDeviceAppStoreNotEqualTo(String value) {
            addCriterion("device_app_store <>", value, "deviceAppStore");
            return (Criteria) this;
        }

        public Criteria andDeviceAppStoreGreaterThan(String value) {
            addCriterion("device_app_store >", value, "deviceAppStore");
            return (Criteria) this;
        }

        public Criteria andDeviceAppStoreGreaterThanOrEqualTo(String value) {
            addCriterion("device_app_store >=", value, "deviceAppStore");
            return (Criteria) this;
        }

        public Criteria andDeviceAppStoreLessThan(String value) {
            addCriterion("device_app_store <", value, "deviceAppStore");
            return (Criteria) this;
        }

        public Criteria andDeviceAppStoreLessThanOrEqualTo(String value) {
            addCriterion("device_app_store <=", value, "deviceAppStore");
            return (Criteria) this;
        }

        public Criteria andDeviceAppStoreLike(String value) {
            addCriterion("device_app_store like", value, "deviceAppStore");
            return (Criteria) this;
        }

        public Criteria andDeviceAppStoreNotLike(String value) {
            addCriterion("device_app_store not like", value, "deviceAppStore");
            return (Criteria) this;
        }

        public Criteria andDeviceAppStoreIn(List<String> values) {
            addCriterion("device_app_store in", values, "deviceAppStore");
            return (Criteria) this;
        }

        public Criteria andDeviceAppStoreNotIn(List<String> values) {
            addCriterion("device_app_store not in", values, "deviceAppStore");
            return (Criteria) this;
        }

        public Criteria andDeviceAppStoreBetween(String value1, String value2) {
            addCriterion("device_app_store between", value1, value2, "deviceAppStore");
            return (Criteria) this;
        }

        public Criteria andDeviceAppStoreNotBetween(String value1, String value2) {
            addCriterion("device_app_store not between", value1, value2, "deviceAppStore");
            return (Criteria) this;
        }

        public Criteria andSmartKeyWordIsNull() {
            addCriterion("smart_key_word is null");
            return (Criteria) this;
        }

        public Criteria andSmartKeyWordIsNotNull() {
            addCriterion("smart_key_word is not null");
            return (Criteria) this;
        }

        public Criteria andSmartKeyWordEqualTo(Integer value) {
            addCriterion("smart_key_word =", value, "smartKeyWord");
            return (Criteria) this;
        }

        public Criteria andSmartKeyWordNotEqualTo(Integer value) {
            addCriterion("smart_key_word <>", value, "smartKeyWord");
            return (Criteria) this;
        }

        public Criteria andSmartKeyWordGreaterThan(Integer value) {
            addCriterion("smart_key_word >", value, "smartKeyWord");
            return (Criteria) this;
        }

        public Criteria andSmartKeyWordGreaterThanOrEqualTo(Integer value) {
            addCriterion("smart_key_word >=", value, "smartKeyWord");
            return (Criteria) this;
        }

        public Criteria andSmartKeyWordLessThan(Integer value) {
            addCriterion("smart_key_word <", value, "smartKeyWord");
            return (Criteria) this;
        }

        public Criteria andSmartKeyWordLessThanOrEqualTo(Integer value) {
            addCriterion("smart_key_word <=", value, "smartKeyWord");
            return (Criteria) this;
        }

        public Criteria andSmartKeyWordIn(List<Integer> values) {
            addCriterion("smart_key_word in", values, "smartKeyWord");
            return (Criteria) this;
        }

        public Criteria andSmartKeyWordNotIn(List<Integer> values) {
            addCriterion("smart_key_word not in", values, "smartKeyWord");
            return (Criteria) this;
        }

        public Criteria andSmartKeyWordBetween(Integer value1, Integer value2) {
            addCriterion("smart_key_word between", value1, value2, "smartKeyWord");
            return (Criteria) this;
        }

        public Criteria andSmartKeyWordNotBetween(Integer value1, Integer value2) {
            addCriterion("smart_key_word not between", value1, value2, "smartKeyWord");
            return (Criteria) this;
        }

        public Criteria andAndroidAppPackageIdIsNull() {
            addCriterion("android_app_package_id is null");
            return (Criteria) this;
        }

        public Criteria andAndroidAppPackageIdIsNotNull() {
            addCriterion("android_app_package_id is not null");
            return (Criteria) this;
        }

        public Criteria andAndroidAppPackageIdEqualTo(Integer value) {
            addCriterion("android_app_package_id =", value, "androidAppPackageId");
            return (Criteria) this;
        }

        public Criteria andAndroidAppPackageIdNotEqualTo(Integer value) {
            addCriterion("android_app_package_id <>", value, "androidAppPackageId");
            return (Criteria) this;
        }

        public Criteria andAndroidAppPackageIdGreaterThan(Integer value) {
            addCriterion("android_app_package_id >", value, "androidAppPackageId");
            return (Criteria) this;
        }

        public Criteria andAndroidAppPackageIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("android_app_package_id >=", value, "androidAppPackageId");
            return (Criteria) this;
        }

        public Criteria andAndroidAppPackageIdLessThan(Integer value) {
            addCriterion("android_app_package_id <", value, "androidAppPackageId");
            return (Criteria) this;
        }

        public Criteria andAndroidAppPackageIdLessThanOrEqualTo(Integer value) {
            addCriterion("android_app_package_id <=", value, "androidAppPackageId");
            return (Criteria) this;
        }

        public Criteria andAndroidAppPackageIdIn(List<Integer> values) {
            addCriterion("android_app_package_id in", values, "androidAppPackageId");
            return (Criteria) this;
        }

        public Criteria andAndroidAppPackageIdNotIn(List<Integer> values) {
            addCriterion("android_app_package_id not in", values, "androidAppPackageId");
            return (Criteria) this;
        }

        public Criteria andAndroidAppPackageIdBetween(Integer value1, Integer value2) {
            addCriterion("android_app_package_id between", value1, value2, "androidAppPackageId");
            return (Criteria) this;
        }

        public Criteria andAndroidAppPackageIdNotBetween(Integer value1, Integer value2) {
            addCriterion("android_app_package_id not between", value1, value2, "androidAppPackageId");
            return (Criteria) this;
        }

        public Criteria andIosAppPackageIdIsNull() {
            addCriterion("ios_app_package_id is null");
            return (Criteria) this;
        }

        public Criteria andIosAppPackageIdIsNotNull() {
            addCriterion("ios_app_package_id is not null");
            return (Criteria) this;
        }

        public Criteria andIosAppPackageIdEqualTo(Integer value) {
            addCriterion("ios_app_package_id =", value, "iosAppPackageId");
            return (Criteria) this;
        }

        public Criteria andIosAppPackageIdNotEqualTo(Integer value) {
            addCriterion("ios_app_package_id <>", value, "iosAppPackageId");
            return (Criteria) this;
        }

        public Criteria andIosAppPackageIdGreaterThan(Integer value) {
            addCriterion("ios_app_package_id >", value, "iosAppPackageId");
            return (Criteria) this;
        }

        public Criteria andIosAppPackageIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("ios_app_package_id >=", value, "iosAppPackageId");
            return (Criteria) this;
        }

        public Criteria andIosAppPackageIdLessThan(Integer value) {
            addCriterion("ios_app_package_id <", value, "iosAppPackageId");
            return (Criteria) this;
        }

        public Criteria andIosAppPackageIdLessThanOrEqualTo(Integer value) {
            addCriterion("ios_app_package_id <=", value, "iosAppPackageId");
            return (Criteria) this;
        }

        public Criteria andIosAppPackageIdIn(List<Integer> values) {
            addCriterion("ios_app_package_id in", values, "iosAppPackageId");
            return (Criteria) this;
        }

        public Criteria andIosAppPackageIdNotIn(List<Integer> values) {
            addCriterion("ios_app_package_id not in", values, "iosAppPackageId");
            return (Criteria) this;
        }

        public Criteria andIosAppPackageIdBetween(Integer value1, Integer value2) {
            addCriterion("ios_app_package_id between", value1, value2, "iosAppPackageId");
            return (Criteria) this;
        }

        public Criteria andIosAppPackageIdNotBetween(Integer value1, Integer value2) {
            addCriterion("ios_app_package_id not between", value1, value2, "iosAppPackageId");
            return (Criteria) this;
        }

        public Criteria andParentUnitIdIsNull() {
            addCriterion("parent_unit_id is null");
            return (Criteria) this;
        }

        public Criteria andParentUnitIdIsNotNull() {
            addCriterion("parent_unit_id is not null");
            return (Criteria) this;
        }

        public Criteria andParentUnitIdEqualTo(Integer value) {
            addCriterion("parent_unit_id =", value, "parentUnitId");
            return (Criteria) this;
        }

        public Criteria andParentUnitIdNotEqualTo(Integer value) {
            addCriterion("parent_unit_id <>", value, "parentUnitId");
            return (Criteria) this;
        }

        public Criteria andParentUnitIdGreaterThan(Integer value) {
            addCriterion("parent_unit_id >", value, "parentUnitId");
            return (Criteria) this;
        }

        public Criteria andParentUnitIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("parent_unit_id >=", value, "parentUnitId");
            return (Criteria) this;
        }

        public Criteria andParentUnitIdLessThan(Integer value) {
            addCriterion("parent_unit_id <", value, "parentUnitId");
            return (Criteria) this;
        }

        public Criteria andParentUnitIdLessThanOrEqualTo(Integer value) {
            addCriterion("parent_unit_id <=", value, "parentUnitId");
            return (Criteria) this;
        }

        public Criteria andParentUnitIdIn(List<Integer> values) {
            addCriterion("parent_unit_id in", values, "parentUnitId");
            return (Criteria) this;
        }

        public Criteria andParentUnitIdNotIn(List<Integer> values) {
            addCriterion("parent_unit_id not in", values, "parentUnitId");
            return (Criteria) this;
        }

        public Criteria andParentUnitIdBetween(Integer value1, Integer value2) {
            addCriterion("parent_unit_id between", value1, value2, "parentUnitId");
            return (Criteria) this;
        }

        public Criteria andParentUnitIdNotBetween(Integer value1, Integer value2) {
            addCriterion("parent_unit_id not between", value1, value2, "parentUnitId");
            return (Criteria) this;
        }

        public Criteria andAllowAigcReplaceMaterialIsNull() {
            addCriterion("allow_aigc_replace_material is null");
            return (Criteria) this;
        }

        public Criteria andAllowAigcReplaceMaterialIsNotNull() {
            addCriterion("allow_aigc_replace_material is not null");
            return (Criteria) this;
        }

        public Criteria andAllowAigcReplaceMaterialEqualTo(Integer value) {
            addCriterion("allow_aigc_replace_material =", value, "allowAigcReplaceMaterial");
            return (Criteria) this;
        }

        public Criteria andAllowAigcReplaceMaterialNotEqualTo(Integer value) {
            addCriterion("allow_aigc_replace_material <>", value, "allowAigcReplaceMaterial");
            return (Criteria) this;
        }

        public Criteria andAllowAigcReplaceMaterialGreaterThan(Integer value) {
            addCriterion("allow_aigc_replace_material >", value, "allowAigcReplaceMaterial");
            return (Criteria) this;
        }

        public Criteria andAllowAigcReplaceMaterialGreaterThanOrEqualTo(Integer value) {
            addCriterion("allow_aigc_replace_material >=", value, "allowAigcReplaceMaterial");
            return (Criteria) this;
        }

        public Criteria andAllowAigcReplaceMaterialLessThan(Integer value) {
            addCriterion("allow_aigc_replace_material <", value, "allowAigcReplaceMaterial");
            return (Criteria) this;
        }

        public Criteria andAllowAigcReplaceMaterialLessThanOrEqualTo(Integer value) {
            addCriterion("allow_aigc_replace_material <=", value, "allowAigcReplaceMaterial");
            return (Criteria) this;
        }

        public Criteria andAllowAigcReplaceMaterialIn(List<Integer> values) {
            addCriterion("allow_aigc_replace_material in", values, "allowAigcReplaceMaterial");
            return (Criteria) this;
        }

        public Criteria andAllowAigcReplaceMaterialNotIn(List<Integer> values) {
            addCriterion("allow_aigc_replace_material not in", values, "allowAigcReplaceMaterial");
            return (Criteria) this;
        }

        public Criteria andAllowAigcReplaceMaterialBetween(Integer value1, Integer value2) {
            addCriterion("allow_aigc_replace_material between", value1, value2, "allowAigcReplaceMaterial");
            return (Criteria) this;
        }

        public Criteria andAllowAigcReplaceMaterialNotBetween(Integer value1, Integer value2) {
            addCriterion("allow_aigc_replace_material not between", value1, value2, "allowAigcReplaceMaterial");
            return (Criteria) this;
        }

        public Criteria andSearchFirstPriceCoefficientIsNull() {
            addCriterion("search_first_price_coefficient is null");
            return (Criteria) this;
        }

        public Criteria andSearchFirstPriceCoefficientIsNotNull() {
            addCriterion("search_first_price_coefficient is not null");
            return (Criteria) this;
        }

        public Criteria andSearchFirstPriceCoefficientEqualTo(Integer value) {
            addCriterion("search_first_price_coefficient =", value, "searchFirstPriceCoefficient");
            return (Criteria) this;
        }

        public Criteria andSearchFirstPriceCoefficientNotEqualTo(Integer value) {
            addCriterion("search_first_price_coefficient <>", value, "searchFirstPriceCoefficient");
            return (Criteria) this;
        }

        public Criteria andSearchFirstPriceCoefficientGreaterThan(Integer value) {
            addCriterion("search_first_price_coefficient >", value, "searchFirstPriceCoefficient");
            return (Criteria) this;
        }

        public Criteria andSearchFirstPriceCoefficientGreaterThanOrEqualTo(Integer value) {
            addCriterion("search_first_price_coefficient >=", value, "searchFirstPriceCoefficient");
            return (Criteria) this;
        }

        public Criteria andSearchFirstPriceCoefficientLessThan(Integer value) {
            addCriterion("search_first_price_coefficient <", value, "searchFirstPriceCoefficient");
            return (Criteria) this;
        }

        public Criteria andSearchFirstPriceCoefficientLessThanOrEqualTo(Integer value) {
            addCriterion("search_first_price_coefficient <=", value, "searchFirstPriceCoefficient");
            return (Criteria) this;
        }

        public Criteria andSearchFirstPriceCoefficientIn(List<Integer> values) {
            addCriterion("search_first_price_coefficient in", values, "searchFirstPriceCoefficient");
            return (Criteria) this;
        }

        public Criteria andSearchFirstPriceCoefficientNotIn(List<Integer> values) {
            addCriterion("search_first_price_coefficient not in", values, "searchFirstPriceCoefficient");
            return (Criteria) this;
        }

        public Criteria andSearchFirstPriceCoefficientBetween(Integer value1, Integer value2) {
            addCriterion("search_first_price_coefficient between", value1, value2, "searchFirstPriceCoefficient");
            return (Criteria) this;
        }

        public Criteria andSearchFirstPriceCoefficientNotBetween(Integer value1, Integer value2) {
            addCriterion("search_first_price_coefficient not between", value1, value2, "searchFirstPriceCoefficient");
            return (Criteria) this;
        }

        public Criteria andGeneralVersionIsNull() {
            addCriterion("general_version is null");
            return (Criteria) this;
        }

        public Criteria andGeneralVersionIsNotNull() {
            addCriterion("general_version is not null");
            return (Criteria) this;
        }

        public Criteria andGeneralVersionEqualTo(Integer value) {
            addCriterion("general_version =", value, "generalVersion");
            return (Criteria) this;
        }

        public Criteria andGeneralVersionNotEqualTo(Integer value) {
            addCriterion("general_version <>", value, "generalVersion");
            return (Criteria) this;
        }

        public Criteria andGeneralVersionGreaterThan(Integer value) {
            addCriterion("general_version >", value, "generalVersion");
            return (Criteria) this;
        }

        public Criteria andGeneralVersionGreaterThanOrEqualTo(Integer value) {
            addCriterion("general_version >=", value, "generalVersion");
            return (Criteria) this;
        }

        public Criteria andGeneralVersionLessThan(Integer value) {
            addCriterion("general_version <", value, "generalVersion");
            return (Criteria) this;
        }

        public Criteria andGeneralVersionLessThanOrEqualTo(Integer value) {
            addCriterion("general_version <=", value, "generalVersion");
            return (Criteria) this;
        }

        public Criteria andGeneralVersionIn(List<Integer> values) {
            addCriterion("general_version in", values, "generalVersion");
            return (Criteria) this;
        }

        public Criteria andGeneralVersionNotIn(List<Integer> values) {
            addCriterion("general_version not in", values, "generalVersion");
            return (Criteria) this;
        }

        public Criteria andGeneralVersionBetween(Integer value1, Integer value2) {
            addCriterion("general_version between", value1, value2, "generalVersion");
            return (Criteria) this;
        }

        public Criteria andGeneralVersionNotBetween(Integer value1, Integer value2) {
            addCriterion("general_version not between", value1, value2, "generalVersion");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}