package com.bilibili.risk.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import pleiades.venus.config.WatchedProperties;
import pleiades.venus.starter.paladin.PaladinPropertySourceFactory;

import java.util.Arrays;
import java.util.List;

@Slf4j
@Configuration
@Data
@WatchedProperties
@ConfigurationProperties(prefix = "biz")
@PropertySource(value = "classpath://biz.yaml",factory = PaladinPropertySourceFactory.class)
public class BizConfig {

    private Integer test;
    private Boolean backdoorEnable;

    private Integer tenantId;

    // 业务管理员角色id
    private Integer businessAdminRoleId;

    // 任务超时兜底数量
    private Long taskTimeoutFallbackMin;
    // 拉取任务兜底数量
    private Integer taskFallbackPullNum;

    // 审核权限校验开关
    private Integer auditTaskRoleCheckSwitch;

    // 配置兜底队列id, 必火三连公用一个
    private Long fallbackQueueId;
    // 等待队列id，必火三连公用一个
    private Long waitQueueId;

    // 绑定的最大队列数量
    private Integer ruleBindQueueMaxNum;

    // 素材任务 es 配置
    private String esDomain;
    private String esMaterialAuditTaskToken;
    private String esMaterialAuditTaskIndex;
    // es索引分片方式
    private Integer esMaterialAuditTaskIndexShardingType;

    // 操作日志 es 配置
    private String taskLogEsToken;
    private String taskLogEsIndex;
    // es索引分片方式
    private Integer taskLogEsIndexShardingType;

    // 进入兜底队列机器人
    private String wechatRobotEnterFallbackQueue;

    // 通用机器人
    private String robotCommon;

    private Integer adpGrpcSwitch;

    // 批次号发号器的token
    private String snowFlakeNoToken;

    private Integer timeoutWechatSendSwitch;
    // 预拉取开关，以防预拉取有问题
    private Integer prePullSwitch;
    private Integer pullRedisZsetSwitch;

    @Bean
    public Integer getTenantId() {
        return tenantId;
    }

    public List<Long> getSpecialQueueIds() {
        return Arrays.asList(fallbackQueueId, waitQueueId);
    }
}
