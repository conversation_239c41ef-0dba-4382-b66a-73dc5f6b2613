package com.bilibili.risk.config.shardingAlgorithm;

import org.apache.shardingsphere.api.sharding.complex.ComplexKeysShardingAlgorithm;
import org.apache.shardingsphere.api.sharding.complex.ComplexKeysShardingValue;
import org.springframework.beans.factory.annotation.Value;

import java.util.Collection;

public abstract class AbstractShardingAlgorithm<T extends Comparable<?>> implements ComplexKeysShardingAlgorithm<T> {

    @Value("${sharding.forbiddenBroadcast:false}")
    private Boolean forbiddenBroadcast;

    @Override
    public Collection<String> doSharding(Collection<String> availableTargetNames, ComplexKeysShardingValue<T> shardingValue) {
        if (shardingValue.getColumnNameAndShardingValuesMap().isEmpty()) {
            throw new RuntimeException("sharding value is empty");
        }
        Collection<String> tables = sharding(availableTargetNames, shardingValue);

        if(forbiddenBroadcast && tables.size() > 1) {
            throw new RuntimeException("禁止跨分片查询");
        }
        return tables;
    }

    protected abstract Collection<String> sharding(Collection<String> availableTargetNames, ComplexKeysShardingValue<T> shardingValue);
}
