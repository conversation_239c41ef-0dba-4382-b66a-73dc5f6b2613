package com.bilibili.risk.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import pleiades.venus.config.WatchedProperties;
import pleiades.venus.starter.paladin.PaladinPropertySourceFactory;

/**
 * 限流配置
 */
@Slf4j
@Configuration
@Data
@WatchedProperties
@ConfigurationProperties(prefix = "rate-limit")
@PropertySource(value = "classpath://rate-limit.yaml", factory = PaladinPropertySourceFactory.class)
public class RateLimitConfig {

    /**
     * 质检规则执行限流配置
     */
    private QualityRuleExecute qualityRuleExecute = new QualityRuleExecute();

    @Data
    public static class QualityRuleExecute {
        /**
         * 限流数量，默认10
         */
        private Integer limit = 10;
        
        /**
         * 限流时间窗口，单位秒，默认60秒
         */
        private Integer windowSeconds = 60;
        
        /**
         * 是否启用限流，默认启用
         */
        private Boolean enabled = true;
    }
}
