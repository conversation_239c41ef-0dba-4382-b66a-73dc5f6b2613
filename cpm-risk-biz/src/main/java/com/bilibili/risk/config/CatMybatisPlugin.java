package com.bilibili.risk.config;

import com.dianping.cat.Cat;

import com.dianping.cat.message.Transaction;

import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.*;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.session.Configuration;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.apache.ibatis.type.TypeHandlerRegistry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.DatabaseMetaData;
import java.sql.SQLException;
import java.text.DateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;

@Intercepts({
        @Signature(method = "query", type = Executor.class, args = {
                MappedStatement.class, Object.class, RowBounds.class,
                ResultHandler.class }),
        @Signature(method = "update", type = Executor.class, args = { MappedStatement.class, Object.class })
})
public class CatMybatisPlugin implements Interceptor {
    private static final Logger log = LoggerFactory.getLogger(CatMybatisPlugin.class);
    private static final Map<String, String> sqlURLCache = new ConcurrentHashMap(256);
    private static final String EMPTY_CONNECTION = "********************************************";

    public CatMybatisPlugin() {
    }

    public Object intercept(Invocation invocation) throws Throwable {
        Executor target = (Executor)invocation.getTarget();
        MappedStatement mappedStatement = (MappedStatement)invocation.getArgs()[0];
        String[] strArr = mappedStatement.getId().split("\\.");
        String methodName = strArr[strArr.length - 2] + "." + strArr[strArr.length - 1];
        Transaction t = Cat.newTransaction("SQL", methodName);
        Object parameter = null;
        if (invocation.getArgs().length > 1) {
            parameter = invocation.getArgs()[1];
        }

        BoundSql boundSql = mappedStatement.getBoundSql(parameter);
        Configuration configuration = mappedStatement.getConfiguration();
        String sql = this.showSql(configuration, boundSql);
        SqlCommandType sqlCommandType = mappedStatement.getSqlCommandType();
        Cat.logEvent("SQL.Method", sqlCommandType.name().toLowerCase(), "0", sql);
        String s = this.getDatabaseUrl(target);
        Cat.logEvent("SQL.Database", s);
        Object returnObj = null;

        try {
            returnObj = invocation.proceed();
            t.setStatus(Transaction.SUCCESS);
        } catch (Exception e) {
            t.setStatus(e);
            throw e;
        } finally {
            t.complete();
        }

        return returnObj;
    }

    private String getDatabaseUrl(Executor target) {
        org.apache.ibatis.transaction.Transaction transaction = target.getTransaction();
        if (transaction == null) {
            log.error("Could not find transaction on target [{}]", target);
            return null;
        } else {
            try {
                DatabaseMetaData metaData = transaction.getConnection().getMetaData();
                return metaData.getURL();
            } catch (SQLException var4) {
                log.error("Could not get database metadata on target [{}]", target);
                return null;
            }
        }
    }

    public String showSql(Configuration configuration, BoundSql boundSql) {
        Object parameterObject = boundSql.getParameterObject();
        List<ParameterMapping> parameterMappings = boundSql.getParameterMappings();
        String sql = boundSql.getSql().replaceAll("[\\s]+", " ");
//        if (parameterMappings.size() > 0 && parameterObject != null) {
//            TypeHandlerRegistry typeHandlerRegistry = configuration.getTypeHandlerRegistry();
//            if (typeHandlerRegistry.hasTypeHandler(parameterObject.getClass())) {
//                sql = sql.replaceFirst("\\?", Matcher.quoteReplacement(this.getParameterValue(parameterObject)));
//            } else {
//                MetaObject metaObject = configuration.newMetaObject(parameterObject);
//                Iterator var8 = parameterMappings.iterator();
//
//                while(var8.hasNext()) {
//                    ParameterMapping parameterMapping = (ParameterMapping)var8.next();
//                    String propertyName = parameterMapping.getProperty();
//                    Object obj;
//                    if (metaObject.hasGetter(propertyName)) {
//                        obj = metaObject.getValue(propertyName);
//                        sql = sql.replaceFirst("\\?", Matcher.quoteReplacement(this.getParameterValue(obj)));
//                    } else if (boundSql.hasAdditionalParameter(propertyName)) {
//                        obj = boundSql.getAdditionalParameter(propertyName);
//                        sql = sql.replaceFirst("\\?", Matcher.quoteReplacement(this.getParameterValue(obj)));
//                    }
//                }
//            }
//        }

        return sql;
    }

    private String getParameterValue(Object obj) {
        String value = null;
        if (obj instanceof String) {
            value = "'" + obj.toString() + "'";
        } else if (obj instanceof Date) {
            DateFormat formatter = DateFormat.getDateTimeInstance(2, 2, Locale.CHINA);
            value = "'" + formatter.format(new Date()) + "'";
        } else if (obj != null) {
            value = obj.toString();
        } else {
            value = "";
        }

        return value;
    }

    public Object plugin(Object target) {
        return target instanceof Executor ? Plugin.wrap(target, this) : target;
    }

    public void setProperties(Properties properties) {
    }
}
