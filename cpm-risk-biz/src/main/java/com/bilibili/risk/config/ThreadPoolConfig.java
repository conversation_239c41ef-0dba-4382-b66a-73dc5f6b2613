package com.bilibili.risk.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

@Configuration
@EnableAsync
@Slf4j
public class ThreadPoolConfig {

    /**
     * 发企业微信通知执行线程池
     */
    @Bean("wechatSendExecutor")
    public Executor wechatSendExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 核心线程数（默认线程数）
        executor.setCorePoolSize(2);
        // 最大线程数
        executor.setMaxPoolSize(5);
        // 缓冲队列大小
        executor.setQueueCapacity(200);
        // 线程空闲时间（秒）
        executor.setKeepAliveSeconds(60);
        // 线程名前缀
        executor.setThreadNamePrefix("wechat-send-executor-");
        // 拒绝策略（直接丢弃并记录日志）
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.DiscardPolicy() {
            @Override
            public void rejectedExecution(Runnable r, ThreadPoolExecutor e) {
                // 记录被拒绝的任务
                log.error("wechat send error[超过最大数，丢弃], r={},e={}", r.toString(), e.toString());
            }
        });
        // 初始化
        executor.initialize();
        return executor;
    }

}
