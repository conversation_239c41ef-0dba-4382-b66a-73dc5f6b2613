package com.bilibili.risk.config.shardingAlgorithm;

import com.bilibili.risk.constant.DatabaseConstant;
import com.bilibili.risk.enums.ShardingTableEnum;
import com.bilibili.risk.utils.MaterialTaskUtils;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.shardingsphere.api.sharding.complex.ComplexKeysShardingValue;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Set;

@Slf4j
@Component
public class TaskShardingAlgorithm extends AbstractShardingAlgorithm<String> {

    @Override
    public Collection<String> sharding(Collection<String> availableTargetNames, ComplexKeysShardingValue<String> shardingValue) {
        if (CollectionUtils.isNotEmpty(shardingValue.getColumnNameAndShardingValuesMap().get("task_id"))) {
            Set<String> tables = Sets.newConcurrentHashSet();
            for (String id : shardingValue.getColumnNameAndShardingValuesMap().get("task_id")) {
                tables.add(ShardingTableEnum.TABLE_LAU_MATERIAL_AUDIT_TASK.getTableName() + "_" + MaterialTaskUtils.calculateShardingKeyByTaskId(id,
                        ShardingTableEnum.TABLE_LAU_MATERIAL_AUDIT_TASK.getShardingAlgorithm()));
            }
            return tables;
        } else if (CollectionUtils.isNotEmpty(shardingValue.getColumnNameAndShardingValuesMap().get("creative_id"))) {
            Set<String> tables = Sets.newConcurrentHashSet();
            for (Object creativeId : shardingValue.getColumnNameAndShardingValuesMap().get("creative_id")) {
                Integer id = Integer.valueOf(creativeId.toString());
                tables.add(ShardingTableEnum.TABLE_LAU_MATERIAL_AUDIT_TASK.getTableName() + "_" + MaterialTaskUtils.calculateShardingKeyByCreativeId(id));
            }
            return tables;
        }

        throw new RuntimeException("sharding column is empty");
    }
}
