package com.bilibili.risk.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import pleiades.venus.config.WatchedProperties;

/**
 * @ClassName DynamicConfig
 * @<PERSON> <PERSON><PERSON>
 * @Date 2024/4/18 7:33 下午
 * @Version 1.0
 **/
@Configuration
public class DynamicConfig {

    @Bean
    @ConfigurationProperties("biz.dynamic")
    @WatchedProperties
    public DynamicProperties dynamicProperties() {
        return new DynamicProperties();
    }

}
