package com.bilibili.risk.config.shardingAlgorithm;

import com.bilibili.risk.enums.ShardingTableEnum;
import com.bilibili.risk.utils.MaterialTaskUtils;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.shardingsphere.api.sharding.complex.ComplexKeysShardingValue;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Set;

@Component
public class MaterialCreativeRelShardingAlgorithm extends AbstractShardingAlgorithm<String> {

    @Override
    protected Collection<String> sharding(Collection<String> availableTargetNames, ComplexKeysShardingValue<String> shardingValue) {

        if(CollectionUtils.isNotEmpty(shardingValue.getColumnNameAndShardingValuesMap().get("material_id"))) {
            Set<String> tables = Sets.newConcurrentHashSet();
            for (String materialId : shardingValue.getColumnNameAndShardingValuesMap().get("material_id")) {

                tables.add(ShardingTableEnum.TABLE_LAU_MATERIAL_CREATIVE_REL.getTableName() + "_" + MaterialTaskUtils.calculateShardingKeyByMaterialId(materialId,
                        ShardingTableEnum.TABLE_LAU_MATERIAL_CREATIVE_REL.getShardingAlgorithm()));
            }

            return tables;
        }
        throw new RuntimeException("sharding column is empty");
    }
}
