package com.bilibili.risk.config;

import com.bilibili.risk.constant.DatabaseConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;
import pleiades.component.mysql.datasource.BiliDataSource;

import javax.sql.DataSource;

@Slf4j
@Configuration
@PropertySource(value = "paladin://mysql.yaml")
@MapperScan(basePackages = {"com.bilibili.risk.dao.ad", "com.bilibili.id.mapper"}, sqlSessionTemplateRef = "adSqlSessionTemplate")
public class AdDatabaseConfig {

    @ConfigurationProperties(prefix = "mysql.ad") //这个在2.1已经有过说明了
    @Bean(name = "adDataSource", destroyMethod = "close") //定义beanName
    public BiliDataSource adDataSource() {

        return new BiliDataSource();
    }

    @Bean
    public SqlSessionFactory adSqlSessionFactory(@Qualifier(DatabaseConstant.AD_DATASOURCE) DataSource dataSource
            , @Qualifier(DatabaseConstant.MybatisPlugin) CatMybatisPlugin catMybatisPlugin) throws Exception {
        SqlSessionFactoryBean sessionFactory = new SqlSessionFactoryBean();
        sessionFactory.setPlugins(catMybatisPlugin);
        // 设置数据源
        sessionFactory.setDataSource(dataSource);
        // 设置 Mapper XML 文件路径
        sessionFactory.setMapperLocations(
                new PathMatchingResourcePatternResolver().getResources("classpath*:mapper/ad/*.xml")
        );
        return sessionFactory.getObject();
    }

    @Bean
    public SqlSessionTemplate adSqlSessionTemplate(@Qualifier("adSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }

    @Bean
    public PlatformTransactionManager adTransactionManager(@Qualifier(DatabaseConstant.AD_DATASOURCE) DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }
}
