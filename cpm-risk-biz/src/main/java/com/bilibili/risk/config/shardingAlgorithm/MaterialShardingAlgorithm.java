package com.bilibili.risk.config.shardingAlgorithm;

import com.bilibili.risk.constant.DatabaseConstant;
import com.bilibili.risk.enums.ShardingTableEnum;
import com.bilibili.risk.utils.MaterialTaskUtils;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.shardingsphere.api.sharding.complex.ComplexKeysShardingValue;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Set;

@Slf4j
@Component
public class MaterialShardingAlgorithm extends AbstractShardingAlgorithm<String> {


    @Override
    public Collection<String> sharding(Collection<String> availableTargetNames, ComplexKeysShardingValue<String> shardingValue) {
        if(CollectionUtils.isNotEmpty(shardingValue.getColumnNameAndShardingValuesMap().get("material_id"))) {
            Set<String> tables = Sets.newConcurrentHashSet();
            for (String materialId : shardingValue.getColumnNameAndShardingValuesMap().get("material_id")) {
                tables.add(ShardingTableEnum.TABLE_LAU_MATERIAL_AUDIT.getTableName() + "_" + MaterialTaskUtils.calculateShardingKeyByMaterialId(materialId,
                        ShardingTableEnum.TABLE_LAU_MATERIAL_AUDIT.getShardingAlgorithm()));
            }
            return tables;
        }  else if(CollectionUtils.isNotEmpty(shardingValue.getColumnNameAndShardingValuesMap().get("material_md5"))) {
            Set<String> tables = Sets.newConcurrentHashSet();
            for (String md5 : shardingValue.getColumnNameAndShardingValuesMap().get("material_md5")) {
                tables.add(ShardingTableEnum.TABLE_LAU_MATERIAL_AUDIT.getTableName() + "_" + MaterialTaskUtils.calculateShardingKeyByMd5(md5,
                        ShardingTableEnum.TABLE_LAU_MATERIAL_AUDIT.getShardingAlgorithm()));
            }
            return tables;
        }
        throw new RuntimeException("sharding column is empty");
    }
}
