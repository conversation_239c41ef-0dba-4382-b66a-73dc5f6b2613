package com.bilibili.risk.config;


import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.Codec;
import org.redisson.codec.SerializationCodec;
import org.redisson.config.ClusterServersConfig;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

import java.lang.reflect.Field;
import java.util.Objects;

@Slf4j
@PropertySource(value = "paladin://redis.yaml")
@Configuration
public class RedisConfig {
    public static final String ADP_CLUSTER = "adpCluster";

    public static final String ADP_CLUSTER_CONFIG = "adpClusterConfig";

    @Bean(ADP_CLUSTER_CONFIG)
    @ConfigurationProperties("redis.adp-cluster")
    public ClusterServersConfig adpClusterServersConfig() {
        return new ClusterServersConfig();
    }

    @Bean(ADP_CLUSTER)
    @SneakyThrows
    public RedissonClient adpRedissonClient(@Qualifier(ADP_CLUSTER_CONFIG) ClusterServersConfig clusterServersConfig) {
//        log.info("adpRedissonClient init..., adpClusterServersConfig={}", clusterServersConfig);
        // cpm-adp集群的序列化方式是 jdk的序列化
        return Redisson.create(genRedissonConfig(clusterServersConfig, new SerializationCodec()));
    }


    @SneakyThrows
    private static Config genRedissonConfig(ClusterServersConfig clusterServersConfig, Codec codec) {
        final Config config = new Config();
        try {
            Field field = config.getClass().getDeclaredField("clusterServersConfig");
            field.setAccessible(true);
            field.set(config, clusterServersConfig);
            if (!Objects.isNull(codec)) {
                config.setCodec(codec);
            }
            field.setAccessible(false);
        } catch (Exception e) {
        }
        return config;
    }
}
