package com.bilibili.risk.config;

import com.bilibili.risk.accessLog.db.MybatisInterceptor;
import com.bilibili.risk.accessLog.db.handler.SqlMetricsProcessor;
import com.bilibili.risk.accessLog.db.handler.SqlReporter;
import com.bilibili.risk.config.shardingAlgorithm.AbstractShardingAlgorithm;
import com.bilibili.risk.constant.DatabaseConstant;
import com.bilibili.risk.enums.ShardingTableEnum;
import com.google.common.collect.ImmutableBiMap;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.shardingsphere.api.config.sharding.ShardingRuleConfiguration;
import org.apache.shardingsphere.api.config.sharding.TableRuleConfiguration;
import org.apache.shardingsphere.api.config.sharding.strategy.ComplexShardingStrategyConfiguration;
import org.apache.shardingsphere.shardingjdbc.api.ShardingDataSourceFactory;
import org.apache.shardingsphere.underlying.common.config.properties.ConfigurationPropertyKey;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;
import pleiades.component.mysql.datasource.BiliDataSource;
import org.springframework.beans.factory.annotation.Qualifier;

import javax.sql.DataSource;
import java.sql.SQLException;
import java.util.Map;
import java.util.Properties;

@Slf4j
@Configuration
@PropertySource(value = "paladin://mysql.yaml")
@MapperScan(basePackages = {"com.bilibili.risk.dao.risk", "com.bilibili.id.mapper"}, sqlSessionTemplateRef = "riskSqlSessionTemplate")
public class RiskDatabaseConfig {

    @Autowired
    private SqlMetricsProcessor processor;

    @Autowired
    private SqlReporter reporter;

    @ConfigurationProperties(prefix = "mysql.risk") //这个在2.1已经有过说明了
    @Bean(name = "riskDataSource", destroyMethod = "close") //定义beanName
    public BiliDataSource riskDataSource() {

        BiliDataSource biliDataSource = new BiliDataSource();
        log.info("riskDataSource init..., biliDataSource={}", biliDataSource);
        return biliDataSource;
    }

    @Bean(DatabaseConstant.RISK_DATASOURCE_SHARDING)
    public DataSource getDataSource(@Qualifier("riskDataSource") DataSource dataSource, @Autowired Map<String, AbstractShardingAlgorithm> shardingAlgorithmMap) throws SQLException {
        ShardingRuleConfiguration shardingRuleConfig = new ShardingRuleConfiguration();
        TableRuleConfiguration taskConfiguration = new TableRuleConfiguration(ShardingTableEnum.TABLE_LAU_MATERIAL_AUDIT_TASK.getTableName()
                , "db.lau_material_audit_task_${0..127}");
        taskConfiguration.setTableShardingStrategyConfig(new ComplexShardingStrategyConfiguration("task_id,creative_id"
                , shardingAlgorithmMap.get(ShardingTableEnum.TABLE_LAU_MATERIAL_AUDIT_TASK.getShardingAlgorithm())));

        TableRuleConfiguration materialAuditConfiguration = new TableRuleConfiguration(ShardingTableEnum.TABLE_LAU_MATERIAL_AUDIT.getTableName()
                , "db.lau_material_audit_${0..31}");
        materialAuditConfiguration.setTableShardingStrategyConfig(new ComplexShardingStrategyConfiguration("material_id,material_md5"
                , shardingAlgorithmMap.get(ShardingTableEnum.TABLE_LAU_MATERIAL_AUDIT.getShardingAlgorithm())));

        TableRuleConfiguration materialCreativeConfiguration = new TableRuleConfiguration(ShardingTableEnum.TABLE_LAU_MATERIAL_CREATIVE_REL.getTableName()
                , "db.lau_material_creative_rel_${0..63}");
        materialCreativeConfiguration.setTableShardingStrategyConfig(new ComplexShardingStrategyConfiguration("material_id"
                , shardingAlgorithmMap.get(ShardingTableEnum.TABLE_LAU_MATERIAL_CREATIVE_REL.getShardingAlgorithm())));

        shardingRuleConfig.getTableRuleConfigs().addAll(Lists.newArrayList(taskConfiguration, materialAuditConfiguration, materialCreativeConfiguration));
        Properties properties = new Properties();
        properties.setProperty(ConfigurationPropertyKey.SQL_SHOW.getKey(), "true");
        properties.setProperty(ConfigurationPropertyKey.MAX_CONNECTIONS_COUNT.getKey(), "10");
        DataSource source = ShardingDataSourceFactory.createDataSource(ImmutableBiMap.of("db", dataSource), shardingRuleConfig, properties);
        log.info("riskDataSource init[创建sharding datasource]..., source={}", source);
        return source;
    }

    @Bean
    public SqlSessionFactory riskSqlSessionFactory(@Qualifier(DatabaseConstant.RISK_DATASOURCE_SHARDING) DataSource dataSource
            , @Qualifier(DatabaseConstant.MybatisPlugin) CatMybatisPlugin catMybatisPlugin,
              @Qualifier(DatabaseConstant.MybatisInterceptorPlugin) MybatisInterceptor mybatisInterceptor) throws Exception {
        SqlSessionFactoryBean sessionFactory = new SqlSessionFactoryBean();
        // 设置数据源
        sessionFactory.setDataSource(dataSource);
        // 再加入拦截器
        sessionFactory.setPlugins(catMybatisPlugin, mybatisInterceptor);
        // 设置 Mapper XML 文件路径
        sessionFactory.setMapperLocations(
                new PathMatchingResourcePatternResolver().getResources("classpath*:mapper/risk/*.xml")
        );
        return sessionFactory.getObject();
    }

    @Bean
    public JdbcTemplate riskJdbcTemplate(@Qualifier(DatabaseConstant.RISK_DATASOURCE_SHARDING) DataSource dataSource) {
        return new JdbcTemplate(dataSource);
    }

    @Bean
    public SqlSessionTemplate riskSqlSessionTemplate(@Qualifier("riskSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }

    @Bean
    public PlatformTransactionManager riskTransactionManager(@Qualifier(DatabaseConstant.RISK_DATASOURCE_SHARDING) DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean(DatabaseConstant.MybatisPlugin)
    public CatMybatisPlugin catExecutorMybatisPlugin() {
        return new CatMybatisPlugin();
    }

    @Bean(DatabaseConstant.MybatisInterceptorPlugin)
    public MybatisInterceptor mybatisInterceptorPlugin() {
        return new MybatisInterceptor(processor, reporter);
    }
}
