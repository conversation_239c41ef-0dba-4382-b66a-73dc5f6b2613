<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>sycpb.platform.cpm.risk</groupId>
        <artifactId>cpm-risk</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <artifactId>cpm-risk-biz</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>sycpb.platform.cpm.risk</groupId>
            <artifactId>cpm-risk-common</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- web服务必须依赖，包含基础的可观测性功能-->
        <dependency>
            <groupId>com.bilibili</groupId>
            <artifactId>warp-spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>commons-lang3</artifactId>
                    <groupId>org.apache.commons</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-api</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- 必须依赖，接入公司日志平台-->
        <dependency>
            <groupId>com.bilibili</groupId>
            <artifactId>warp-spring-boot-starter-logging</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>opentelemetry-semconv</artifactId>
                    <groupId>io.opentelemetry.semconv</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-api</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- 配置中心相关-->
        <dependency>
            <groupId>com.bilibili</groupId>
            <artifactId>warp-spring-boot-starter-paladin</artifactId>
        </dependency>
        <!-- gRPC相关 -->
        <dependency>
            <groupId>com.bilibili</groupId>
            <artifactId>warp-spring-boot-starter-grpc</artifactId>
        </dependency>
        <!-- databus相关 -->
        <dependency>
            <groupId>com.bilibili</groupId>
            <artifactId>warp-spring-boot-starter-databus</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>opentelemetry-api</artifactId>
                    <groupId>io.opentelemetry</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-lang3</artifactId>
                    <groupId>org.apache.commons</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- navigator限流相关，暂时只有单机限流 -->
        <dependency>
            <groupId>com.bilibili</groupId>
            <artifactId>warp-spring-boot-starter-limiter</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-api</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- navigator熔断相关 -->
        <dependency>
            <groupId>com.bilibili</groupId>
            <artifactId>warp-spring-boot-starter-breaker</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bilibili</groupId>
            <artifactId>warp-spring-boot-starter-opentelemetry</artifactId>
        </dependency>
        <dependency>
            <groupId>pleiades.component.datasource</groupId>
            <artifactId>mysql</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>protobuf-java</artifactId>
                    <groupId>com.google.protobuf</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>opentelemetry-api</artifactId>
                    <groupId>io.opentelemetry</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>opentelemetry-context</artifactId>
                    <groupId>io.opentelemetry</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-api</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>pleiades.venus</groupId>
            <artifactId>infoc</artifactId>
        </dependency>
        <dependency>
            <groupId>pleiades.venus</groupId>
            <artifactId>starter</artifactId>
        </dependency>
        <!-- Resilience4j 核心依赖 -->
        <dependency>
            <groupId>io.github.resilience4j</groupId>
            <artifactId>resilience4j-spring-boot2</artifactId>
            <version>1.7.1</version>
        </dependency>

        <!-- Resilience4j 断路器 -->
        <dependency>
            <groupId>io.github.resilience4j</groupId>
            <artifactId>resilience4j-circuitbreaker</artifactId>
            <version>1.7.1</version>
        </dependency>

        <!-- Resilience4j 限时器 -->
        <dependency>
            <groupId>io.github.resilience4j</groupId>
            <artifactId>resilience4j-timelimiter</artifactId>
            <version>1.7.1</version>
        </dependency>
        <!-- MyBatis Starter -->
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-api</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
            <version>2.7.18</version>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-api</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jdk8</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.module</groupId>
            <artifactId>jackson-module-parameter-names</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bilibili.rbac</groupId>
            <artifactId>rbac-filter</artifactId>
            <version>${rbac.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>spring-web</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-aop</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-beans</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-expression</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-core</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-webmvc</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-context</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-jdbc</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-tx</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <groupId>redis.clients</groupId>
                    <artifactId>jedis</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.data</groupId>
                    <artifactId>spring-data-redis</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.guava</groupId>
                    <artifactId>guava</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.code.gson</groupId>
                    <artifactId>gson</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.bilibili.rbac</groupId>
            <artifactId>rbac-api</artifactId>
            <version>${rbac.version}</version>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>4.12.0</version>
        </dependency>
        <dependency>
            <groupId>com.bilibili.adp</groupId>
            <artifactId>common</artifactId>
            <version>${adp.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-beans</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>redis.clients</groupId>
                    <artifactId>jedis</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.data</groupId>
                    <artifactId>spring-data-redis</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.guava</groupId>
                    <artifactId>guava</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.code.gson</groupId>
                    <artifactId>gson</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.mchange</groupId>
                    <artifactId>c3p0</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-lang3</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.mchange</groupId>
            <artifactId>c3p0</artifactId>
            <version>0.9.5.5</version> <!-- 最新稳定版本 -->
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.bilibili.config</groupId>-->
<!--            <artifactId>config-refresh</artifactId>-->
<!--            <version>3.0.4-RELEASE</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-elasticsearch</artifactId>
            <version>4.0.3.RELEASE</version>
            <exclusions>
                <exclusion>
                    <artifactId>spring-beans</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-context</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-core</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-tx</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jackson-core</artifactId>
                    <groupId>com.fasterxml.jackson.core</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jackson-annotations</artifactId>
                    <groupId>com.fasterxml.jackson.core</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>netty-codec-http</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-api</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
<!--        &lt;!&ndash; 在 pom.xml 中添加 CAT 客户端依赖 &ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>com.dianping.cat</groupId>-->
<!--            <artifactId>cat-client</artifactId>-->
<!--            <version>3.0.0</version> &lt;!&ndash; 使用适合你项目的版本 &ndash;&gt;-->
<!--        </dependency>-->
        <!-- S3 Transfer Manager 依赖 -->
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>s3-transfer-manager</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-api</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
<!--        &lt;!&ndash; aws-crt 依赖 &ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>software.amazon.awssdk.crt</groupId>-->
<!--            <artifactId>aws-crt</artifactId>-->
<!--            <version>0.29.25</version>-->
<!--        </dependency>-->
        <!-- grpc jar-->
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>infra_service.sequence_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_mgk.page-group_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_pandora.core_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_pandora.service_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_component_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_crm.account_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_crm.industry_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_archive_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_creative_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_unit_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_mng.risk_video_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_mng.creative_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>account_service.relation.v1_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>archive_service_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_audit_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>dynamic_service.feed_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bilibili.bcg</groupId>
            <artifactId>bjcom-cat-spring-boot-starter</artifactId>
            <version>2.4.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.8.9</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_risk.material_task_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_risk.material_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.shardingsphere</groupId>
            <artifactId>sharding-jdbc-core</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>com.google.guava</artifactId>
                    <groupId>guava</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-api</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>32.0.1-jre</version> <!-- 选择与 gRPC 兼容的版本 -->
        </dependency>
        <dependency>
            <groupId>com.bilibili.bjcom</groupId>
            <artifactId>bjcom-cat-spring</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty-all</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>gson</artifactId>
                    <groupId>com.google.code.gson</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.bilibili.bjcom</groupId>
            <artifactId>bjcom-cat-mybatis</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty-all</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.mybatis</groupId>
                    <artifactId>mybatis-spring</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-beans</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-api</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.bilibili</groupId>
            <artifactId>warp-databus</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-api</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>
</project>