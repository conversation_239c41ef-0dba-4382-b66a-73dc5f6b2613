syntax = "proto3";
package pgc.servant.season.season;
import "pgc/servant/media/model.proto";
import "google/protobuf/empty.proto";
import "extension/wdcli/wdcli.proto";

option go_package = "buf.bilibili.co/bapis/bapis-gen/pgc/servant.media;v1";
option java_package = "com.bapis.pgc.servant.media";
option java_multiple_files = true;
option (wdcli.appid) = "ogv.ogv.media-servant";

service Media {
  //查询media 详情（老接口，功能不再扩展，请接入GetMediaInfo新接口）
  rpc GetMediaDetail(pgc.servant.media.MediaDetailReq) returns (pgc.servant.media.MediaDetailReply);

  //多维度查询
  rpc MultiQueryMedia(pgc.servant.media.MultiQueryMediaReq) returns (pgc.servant.media.MultiQueryMediaReply);

  //媒资审核状态同步
  rpc NotifyMediaAuditResult(pgc.servant.media.MediaAuditResultReq) returns (google.protobuf.Empty);

  //新增媒资基础信息
  rpc AddMediaBase(pgc.servant.media.MediaBaseAddReq) returns (pgc.servant.media.MediaBaseAddReply);

  //更新媒资基础信息
  rpc UpdateMediaBase(pgc.servant.media.MediaBaseUpdateReq) returns (google.protobuf.Empty);

  //删除媒资基础信息
  rpc DeleteMediaBase(pgc.servant.media.MediaBaseDeleteReq) returns (google.protobuf.Empty);

  //根据mediaBizId查询媒资基础信息
  rpc GetMediaBaseByMediaBizId(pgc.servant.media.GetMediaBaseByMediaBizIdReq) returns (pgc.servant.media.GetMediaBaseByMediaBizIdReply);

  //根据mediaNo查询媒资基础信息
  rpc QueryMediaBaseByMediaNo(pgc.servant.media.QueryMediaBaseByMediaNoReq) returns (pgc.servant.media.QueryMediaBaseByMediaNoReply);

  //根据mediaNo批量查询媒资基础信息
  rpc BatchQueryMediaBaseByMediaNo(pgc.servant.media.BatchQueryMediaBaseByMediaNoReq) returns (pgc.servant.media.QueryMediaBaseByMediaNoReply);

  //批量保存媒资别名
  rpc BatchSaveMediaAlias(pgc.servant.media.BatchMediaAliasAddReq) returns (google.protobuf.Empty);

  //新增媒资别名
  rpc BatchAddMediaAlias(pgc.servant.media.BatchMediaAliasAddReq) returns (google.protobuf.Empty);

  //更新媒资别名
  rpc UpdateMediaAlias(pgc.servant.media.MediaAliasUpdateReq) returns (google.protobuf.Empty);

  //删除媒资别名
  rpc DeleteMediaAlias(pgc.servant.media.MediaAliasDeleteReq) returns (google.protobuf.Empty);

  //查询媒资别名
  rpc QueryMediaAlias(pgc.servant.media.MediaAliasQueryReq) returns (pgc.servant.media.MediaAliasQueryReply);

  //新增媒资主要属性
  rpc AddMediaMainAttr(pgc.servant.media.MediaAddMainAttrReq) returns (google.protobuf.Empty);

  //更新媒资主要属性
  rpc UpdateMediaMainAttr(pgc.servant.media.MediaMainAttrUpdateReq) returns (google.protobuf.Empty);

  //删除媒资主要属性
  rpc DeleteMediaMainAttr(pgc.servant.media.MediaMainAttrDeleteReq) returns (google.protobuf.Empty);

  //查询媒资主要属性
  rpc QueryMediaMainAttr(pgc.servant.media.MediaMainAttrQueryReq) returns (pgc.servant.media.MediaMainAttrQueryReply);

  //查询媒资附加属性
  rpc QueryMediaExtraAttr(pgc.servant.media.MediaExtraAttrQueryReq) returns (pgc.servant.media.MediaExtraAttrQueryReply);

  //新增媒资多媒体资源
  rpc AddMediaMultiResource(pgc.servant.media.MediaMultiResourceAddReq) returns (google.protobuf.Empty);

  //更新媒资多媒体资源
  rpc UpdateMediaMultiResource(pgc.servant.media.MediaMultiResourceUpdateReq) returns (google.protobuf.Empty);

  //删除媒资多媒体资源
  rpc DeleteMediaMultiResource(pgc.servant.media.MediaMultiResourceDeleteReq) returns (google.protobuf.Empty);

  //查询媒资多媒体资源
  rpc QueryMediaMultiResource(pgc.servant.media.MediaMultiResourceQueryReq) returns (pgc.servant.media.MediaMultiResourceQueryReply);

  //分页查询媒资多媒体资源
  rpc QueryMediaMultiResourceInPage(pgc.servant.media.MediaMultiResourceQueryInPageReq) returns (pgc.servant.media.MediaMultiResourceQueryInPageReply);

  //新增媒资生产信息
  rpc AddMediaProductInfo(pgc.servant.media.MediaProductInfoAddReq) returns (google.protobuf.Empty);

  //更新媒资生产信息
  rpc UpdateMediaProductInfo(pgc.servant.media.MediaProductInfoUpdateReq) returns (google.protobuf.Empty);

  //删除媒资生产信息
  rpc DeleteMediaProductInfo(pgc.servant.media.MediaProductInfoDeleteReq) returns (google.protobuf.Empty);

  //查询媒资生产信息
  rpc QueryMediaProductInfo(pgc.servant.media.MediaProductInfoQueryReq) returns (pgc.servant.media.MediaProductInfoQueryReply);

  //新增媒资发行信息
  rpc AddMediaReleaseInfo(pgc.servant.media.MediaReleaseInfoAddReq) returns (google.protobuf.Empty);

  //更新媒资发行信息
  rpc UpdateMediaReleaseInfo(pgc.servant.media.MediaReleaseInfoUpdateReq) returns (google.protobuf.Empty);

  //删除媒资发行信息
  rpc DeleteMediaReleaseInfo(pgc.servant.media.MediaReleaseInfoDeleteReq) returns (google.protobuf.Empty);

  //查询媒资发行信息
  rpc QueryMediaReleaseInfo(pgc.servant.media.MediaReleaseInfoQueryReq) returns (pgc.servant.media.MediaReleaseInfoQueryReply);

  //通用：根据key查询所有values
  rpc QueryEnumByKey(pgc.servant.media.QueryEnumByKeyReq) returns (pgc.servant.media.QueryEnumByKeyReply);

  //通用：根据key批量添加value
  rpc BatchAddEnumValues(pgc.servant.media.BatchAddEnumValuesReq) returns (google.protobuf.Empty);

  //新增媒资各平台关系
  rpc AddMediaPlatformRelationship(pgc.servant.media.AddMediaPlatformRelationshipReq) returns (google.protobuf.Empty);

  //更新媒资各平台关系
  rpc UpdateMediaPlatformRelationship(pgc.servant.media.UpdateMediaPlatformRelationshipReq) returns (google.protobuf.Empty);

  //删除媒资各平台关系
  rpc DeleteMediaPlatformRelationship(pgc.servant.media.DeleteMediaPlatformRelationshipReq) returns (google.protobuf.Empty);

  //根据mediaBizId查询关联关系
  rpc GetMediaPlatformRelationships(pgc.servant.media.GetMediaPlatformRelationshipsReq) returns (pgc.servant.media.GetMediaPlatformRelationshipsReply);

  //根据mediaNo查询站外媒资基础信息
  rpc QueryExternalMediaBaseInfo(pgc.servant.media.QueryExternalMediaBaseInfoReq) returns (pgc.servant.media.QueryExternalMediaBaseInfoReply);

  //临时兼容接口，旧写新
  rpc saveMediaBizInfoFromOld(pgc.servant.media.MediaBizInfoFromOldSaveReq) returns (google.protobuf.Empty);

  //根据mediaBizId查询媒资详情
  rpc getMediaBizInfoByMediaBizId(pgc.servant.media.MediaBizInfoGetReq) returns (pgc.servant.media.MediaBizInfoGetReply);

  //监听相关业务场景
  rpc MonitorMediaRelatedBizState(pgc.servant.media.MediaRelatedBizStateReq) returns (google.protobuf.Empty);

  //内部属性因子获取mediaBase
  rpc GetMediaBaseForInner(pgc.servant.media.GetMediaBaseFactor) returns (pgc.servant.media.MediaBase);

  //保存人物信息
  rpc SavePersonInfo(pgc.servant.media.PersonInfoSaveReq) returns (pgc.servant.media.PersonInfoSaveReply);

  //根据personNo查询人物详情
  rpc GetPersonInfo(pgc.servant.media.PersonInfoGetReq) returns (pgc.servant.media.PersonInfoGetReply);

  //获取人物相关作品集
  rpc GetPersonMedia(pgc.servant.media.PersonMediaGetReq) returns (pgc.servant.media.PersonMediaGetReply);

  //人物作品集操作（排序）
  rpc OrderPersonMedia(pgc.servant.media.PersonMediaOrderReq) returns (google.protobuf.Empty);

  //获取人物相关话题
  rpc QueryPersonTopic(pgc.servant.media.PersonTopicQueryReq) returns (pgc.servant.media.PersonTopicQueryReply);

  //更新人物话题
  rpc UpdateTopicAttr(pgc.servant.media.UpdateTopicAttrReq) returns (google.protobuf.Empty);

  //删除人物话题
  rpc DeleteTopicAttr(pgc.servant.media.DeleteTopicAttrReq) returns (google.protobuf.Empty);

  //根据id查询话题详情
  rpc GetTopicAttr(pgc.servant.media.GetTopicAttrReq) returns (pgc.servant.media.PersonTopicInfo);

  //多维度查询人物信息
  rpc MultiQueryPerson(pgc.servant.media.MultiQueryPersonReq) returns (pgc.servant.media.MultiQueryPersonReply);

  //批量保存人物多媒体资源
  rpc BatchSavePersonMultiResource(pgc.servant.media.BatchSavePersonMultiResourceReq) returns (google.protobuf.Empty);

  //新增人物多媒体资源
  rpc AddPersonMultiResource(pgc.servant.media.PersonMultiResourceAddReq) returns (google.protobuf.Empty);

  //更新人物多媒体资源
  rpc UpdatePersonMultiResource(pgc.servant.media.PersonMultiResourceUpdateReq) returns (google.protobuf.Empty);

  //删除人物多媒体资源
  rpc DeletePersonMultiResource(pgc.servant.media.PersonMultiResourceDeleteReq) returns (google.protobuf.Empty);

  //分页查询人物多媒体资源
  rpc QueryPersonMultiResourceInPage(pgc.servant.media.PersonMultiResourceQueryInPageReq) returns (pgc.servant.media.PersonMultiResourceQueryInPageReply);

  //新增媒资-影人关联关系
  rpc AddMediaPersonParticipation(pgc.servant.media.AddMediaPersonParticipationReq) returns (google.protobuf.Empty);

  //更新媒资-影人关联关系
  rpc UpdateMediaPersonParticipation(pgc.servant.media.UpdateMediaPersonParticipationReq) returns (google.protobuf.Empty);

  //删除媒资-影人关联关系
  rpc DeleteMediaPersonParticipation(pgc.servant.media.DeleteMediaPersonParticipationReq) returns (google.protobuf.Empty);

  //分页媒资-影人关联关系
  rpc QueryMediaPersonParticipationInPage(pgc.servant.media.QueryMediaPersonParticipationInPageReq) returns (pgc.servant.media.MediaPersonParticipationQueryInPageReply);

  //查询媒资-影人关联关系
  rpc GetMediaPersonParticipation(pgc.servant.media.MediaPersonParticipationReq) returns (pgc.servant.media.BiliMediaParticipation);

  //排序媒资 影人关联关系
  rpc OrderMediaPersonParticipation(pgc.servant.media.MediaPersonOrderReq) returns (google.protobuf.Empty);

  //临时兼容接口，旧写新
  rpc WriteOldToNew(pgc.servant.media.WriteOldToNewReq) returns (google.protobuf.Empty);

  //媒资-影人关系外部爬虫查询
  rpc QueryMediaParticipationByExternalPlatform(pgc.servant.media.QueryMediaParticipationByExternalPlatformReq) returns (pgc.servant.media.QueryMediaParticipationByExternalPlatformReply);

  //批量新增媒资-影人关系内外部数据
  rpc BatchAddExternalPlatformMediaParticipation(pgc.servant.media.BatchAddExternalPlatformMediaParticipationReq) returns (google.protobuf.Empty);

  //新增人物爬虫查询
  rpc QueryPersonCrawlerInfo(pgc.servant.media.QueryPersonCrawlerInfoReq) returns (pgc.servant.media.QueryPersonCrawlerInfoReply);

  //批量新增人物主要属性
  rpc BatchAddMediaMainAttr(pgc.servant.media.MediaBatchAddMainAttrReq) returns (google.protobuf.Empty);

  //批量新增媒资附加属性
  rpc BatchAddMediaExtraAttr(pgc.servant.media.MediaBatchAddExtraAttrReq) returns (google.protobuf.Empty);

  //根据外部平台编号查询媒资信息
  rpc GetExternalMediaInfo(pgc.servant.media.GetExternalMediaInfoReq) returns (pgc.servant.media.GetExternalMediaInfoReply);

  //根据外部平台编号查询媒资图片信息
  rpc GetExternalMediaMultiResourceInfo(pgc.servant.media.GetExternalMediaMultiResourceInfoReq) returns (pgc.servant.media.GetExternalMediaMultiResourceInfoReply);

  //批量保存站外媒资图片于站内
  rpc BatchSaveExternalMediaMultiResourceInfo(pgc.servant.media.BatchSaveExternalMediaMultiResourceInfoReq) returns (google.protobuf.Empty);

  //批量保存媒资发行信息
  rpc BatchSaveMediaReleaseInfo(pgc.servant.media.MediaReleaseInfoBatchAddReq) returns (google.protobuf.Empty);

  //分页查询媒资
  rpc QueryMediaBaseInPage(pgc.servant.media.QueryMediaBaseInPageReq) returns (pgc.servant.media.QueryMediaBaseInPageReply);

  //查询人物
  rpc QueryPersonBase(pgc.servant.media.QueryPersonBaseReq) returns (pgc.servant.media.QueryPersonBaseReply);

  //根据db编号自动生成媒资
  rpc AutoCreateMedia(pgc.servant.media.AutoCreateMediaReq) returns (google.protobuf.Empty);

  //分页查询媒资变更单
  rpc PageQueryMediaChangeOrder(pgc.servant.media.MediaChangeOrderPageQueryReq) returns (pgc.servant.media.MediaChangeOrderPageQueryReply);

  //批量审核媒资变更单
  rpc BatchAuditMediaChangeOrder(pgc.servant.media.MediaChangeOrderAuditReq) returns (google.protobuf.Empty);

}

//榜单
service Rank {

  //创建榜单信息
  rpc CreateRank(pgc.servant.media.RankCreateReq) returns (pgc.servant.media.RankCreateReply);

  //分页查询榜单信息
  rpc PageQueryRankInfo(pgc.servant.media.RankPageQueryReq) returns (pgc.servant.media.RankPageQueryReply);

  //按日期查询榜单信息,B端使用
  rpc QueryRankInfoDetailForAdmin(pgc.servant.media.RankAdminQueryReq) returns (pgc.servant.media.RankAdminQueryReply);

  //更新榜单信息
  rpc updateRank(pgc.servant.media.RankUpdateReq) returns (google.protobuf.Empty);

  //提交榜单
  rpc SubmitRank(pgc.servant.media.RankOperateReq) returns (google.protobuf.Empty);

  //审核榜单
  rpc AuditRank(pgc.servant.media.RankOperateReq) returns (google.protobuf.Empty);

  //上架待上架状态榜单
  rpc OnlineRank(pgc.servant.media.RankOperateReq) returns (google.protobuf.Empty);

  //取消榜单
  rpc CancelRank(pgc.servant.media.RankOperateReq) returns (google.protobuf.Empty);

  //下架榜单
  rpc OfflineRank(pgc.servant.media.RankOperateReq) returns (google.protobuf.Empty);

  //添加榜单媒资
  rpc AddRankMedia(pgc.servant.media.RankMediaAddReq) returns (google.protobuf.Empty);

  //编辑榜单媒资
  rpc UpdateRankMedia(pgc.servant.media.RankMediaUpdateReq) returns (google.protobuf.Empty);

  //删除榜单媒资
  rpc DeleteRankMedia(pgc.servant.media.RankMediaDeleteReq) returns (google.protobuf.Empty);

  //添加媒资稿件
  rpc AddRankMediaArchive(pgc.servant.media.RankMediaArchiveAddReq) returns (google.protobuf.Empty);

  //编辑媒资稿件
  rpc UpdateRankMediaArchive(pgc.servant.media.RankMediaArchiveUpdateReq) returns (google.protobuf.Empty);

  //删除媒资稿件
  rpc DeleteRankMediaArchive(pgc.servant.media.RankMediaArchiveDeleteReq) returns (google.protobuf.Empty);

  //查询推荐媒资
  rpc QueryRankRecommendMedia(pgc.servant.media.RankRecommendMediaQueryReq) returns (pgc.servant.media.RankRecommendMediaQueryReply);

  //查询推荐媒资
  rpc QueryRankRecommendMediaArchive(pgc.servant.media.RankRecommendMediaArchiveQueryReq) returns (pgc.servant.media.RankRecommendMediaArchiveQueryReply);

  //添加/更新媒资权重
  rpc AddRankMediaWeight(pgc.servant.media.RankMediaWeightAddReq) returns (google.protobuf.Empty);

  //删除媒资权重
  rpc DeleteRankMediaWeight(pgc.servant.media.RankMediaWeightDeleteReq) returns (google.protobuf.Empty);

  //分页查询榜单信息
  rpc PageQueryRankMediaWeight(pgc.servant.media.RankMediaWeightPageQueryReq) returns (pgc.servant.media.RankMediaWeightPageQueryReply);

  //删除媒资权重
  rpc AddRankPreviewMid(pgc.servant.media.RankPreviewMidAddReq) returns (google.protobuf.Empty);

  //刷新待提交榜单媒资
  rpc RenewWaitSubmitRankMedia(pgc.servant.media.RankMediaRenewReq) returns (pgc.servant.media.RenewWaitSubmitRankMediaReply);

  //添加媒资稿件黑名单
  rpc AddRankMediaBlackArchive(pgc.servant.media.RankBlackArchiveOperationReq) returns (google.protobuf.Empty);

  //删除媒资稿件黑名单
  rpc DeleteRankMediaBlackArchive(pgc.servant.media.RankBlackArchiveOperationReq) returns (google.protobuf.Empty);

}

service Maoyan {

  //根据条件查询猫眼电影数据
  rpc QueryMaoyanMovieData(pgc.servant.media.MaoyanQueryDataReq) returns (pgc.servant.media.QueryMaoyanMovieDataReply);

  //添加媒资稿件黑名单
  rpc AddMediaBlack(pgc.servant.media.MaoyanBlackMediaOperateReq) returns (google.protobuf.Empty);

  //删除媒资黑名单
  rpc DeleteMediaBlack(pgc.servant.media.MaoyanBlackMediaOperateReq) returns (google.protobuf.Empty);

  //添加稿件黑名单
  rpc AddArchiveBlack(pgc.servant.media.MaoyanBlackArchiveOperateReq) returns (google.protobuf.Empty);

  //删除稿件黑名单
  rpc DeleteBlackArchive(pgc.servant.media.MaoyanBlackArchiveOperateReq) returns (google.protobuf.Empty);

  //更新媒资统计信息
  rpc UpdateMediaStatisticsInfo(pgc.servant.media.MaoyanMediaUpdateReq) returns (google.protobuf.Empty);

  //发送猫眼电影推送事件
  rpc SendPushEvent(pgc.servant.media.MaoyanMediaPushReq) returns (google.protobuf.Empty);

  //屏蔽媒资推送动作
  rpc shieldedMedia(pgc.servant.media.MaoyanMediaShieldedReq) returns (google.protobuf.Empty);

  //查询稿件黑名单
  rpc QueryMaoyanBlackArchive(pgc.servant.media.QueryMaoyanBlackArchiveReq) returns (pgc.servant.media.QueryMaoyanBlackArchiveReply);
}