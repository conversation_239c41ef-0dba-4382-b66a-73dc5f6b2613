syntax = "proto3";
package pgc.servant.video;
import "pgc/servant/video/model.proto";
import "pgc/servant/video/common.proto";
import "google/protobuf/empty.proto";
import "extension/wdcli/wdcli.proto";


option go_package = "buf.bilibili.co/bapis/bapis-gen/pgc/servant.video;v1";
option java_package = "com.bapis.pgc.servant.video";
option java_multiple_files = true;
option (wdcli.appid) = "ogv.video.servant";


service Video {

  //根据cid获取关联视频
  rpc GetRelatedVideos(RelatedVideoRequest) returns (RelatedVideoReply);
  //根据视频物料id获取关联视频
  rpc GetRelatedVideosByIds(RelatedVideoIdsRequest) returns (RelatedVideoReply);
  //根据物料编号查询物料详情
  rpc GetVideoMaterialByMaterialNos(MaterialNosRequest) returns (VideoMaterialReply);
  //根据cids查询物料详情
  rpc GetVideoMaterialByCids(CidsRequest) returns (VideoMaterialReply);
  //根据不同查询条件集合查询物料详情
  rpc GetVideoMaterialByMediaIds(VideoMaterialListsRequest) returns (VideoMaterialByMediaIdsReply);
  //获取分类列表
  rpc GetVideoCategory(.google.protobuf.Empty) returns (VideoCategoryReply);
  //批量添加视频关系
  rpc BatchAddVideoRelationship(BatchAddVideoRelationshipRequest) returns (CommonReply);
  //创建视频物料
  rpc AddVideoMaterial(AddVideoMaterialRequest) returns (CommonReply);
  //创建视频物料(新外部接口)
  rpc AddVideoMaterialByExternalBusiness(AddVideoMaterialByExternalBusinessRequest) returns (CommonReply);
  //添加视频物料特征
  rpc AddVideoFeature(AddVideoFeatureRequest) returns (CommonReply);
  //批量添加视频物料特征
  rpc BatchAddVideoFeature(BatchAddVideoFeatureRequest) returns (CommonReply);
  //自定义批量添加视频物料特征
  rpc BatchCustomAddVideoFeature(BatchCustomAddVideoFeatureRequest) returns (CommonReply);
  //添加操作记录
  rpc AddVideoOperatedRecord(AddVideoOperatedRecordRequest) returns (CommonReply);
  //移除视频关联关系
  rpc RemoveVideoRelationship(RemoveVideoRelationshipRequest) returns (CommonReply);
  //根据视频物料ids查询物料详情
  rpc GetVideoMaterialByIds(VideoMaterialIdsRequest) returns (VideoMaterialReply);
  //添加高能看点
  rpc AddHighlight(AddHighlightRequest) returns (CommonReply);
  //批量添加高能看点
  rpc BatchAddHighlight(BatchAddHighlightRequest) returns (CommonReply);
  //自定义批量添加高能看点
  rpc BatchCustomAddHighlight(BatchCustomAddHighlightRequest) returns (BatchCustomAddHighlightReply);
  //获取视频特征列表
  rpc GetVideoFeatureList(VideoFeatureListRequest) returns (VideoFeatureListReply);
  //更新视频特征
  rpc UpdateVideoFeature(UpdateVideoFeatureRequest) returns (CommonReply);
  //删除视频特征
  rpc DeleteVideoFeature(DeleteVideoFeatureRequest) returns (CommonReply);
  //自定义删除视频特征
  rpc CustomDeleteVideoFeature(CustomDeleteVideoFeatureRequest) returns (CommonReply);
  //自定义更新视频特征
  rpc CustomUpdateVideoFeature(CustomUpdateVideoFeatureRequest) returns (CommonReply);
  //更新视频高能看点
  rpc UpdateVideoMaterial(UpdateVideoMaterialRequest) returns (CommonReply);
  //删除视频高能看点
  rpc DeleteVideoMaterial(DeleteVideoMaterialRequest) returns (CommonReply);
  //获取视频列表
  rpc GetVideoList(VideoMaterialListRequest) returns (VideoMaterialListReply);
  // 根据cid查询关联视频（新）
  rpc QueryRelatedVideos(VideoRelatedQueryReq) returns (VideoRelatedQueryReply);
  //获取投稿账号
  rpc QueryRelevantAccount(QueryRelevantAccountReq) returns (RelevantAccountReply);
  // 创建视频,包含视频投稿信息
  rpc CreateVideo(CreateVideoReq) returns (CommonReply);
  //视频换源
  rpc VideoSourceChange(VideoSourceChangeReq) returns(CommonReply);
  //确认更换视频
  rpc ConfirmVideoSourceChange(ConfirmVideoSourceChangeReq) returns(CommonReply);
  //取消更换视频
  rpc CancelVideoSourceChange(CancelVideoSourceChangeReq) returns(CommonReply);
  //根据视频物料编号查询视频详情
  rpc QueryVideoDetailByMaterialNo(MaterialNoRequest) returns (VideoDetail);
  //根据视频物料编号批量查询视频详情
  rpc QueryVideoDetailByMaterialNos(MaterialNosRequest) returns (VideoDetails);
  //根据视频物料cid批量查询视频详情
  rpc QueryVideoDetailByCids(CidsRequest) returns (VideoDetails);
  //获取枚举值接口
  rpc QueryEnumValue(EnumClassName) returns (EnumValues);
  //更新视频信息
  rpc UpdateVideo(UpdateVideoReq) returns (CommonReply);
  // 创建/收录视频
  rpc adminCreateVideo(AdminCreateVideoReq) returns(CommonReply);
  //批量创建/收录视频
  rpc adminBatchCreateVideo(AdminBatchCreateVideoReq) returns(CommonReply);
  //批量添加视频关系
  rpc BatchAddVideoRelationshipV2(BatchAddVideoRelationshipRequestV2) returns (CommonReply);
  //补充数据更新视频信息
  rpc AdminUpdateVideo(AdminUpdateVideoReq) returns (CommonReply);
  //查询智能物料素材库列表
  rpc QueryVideoConstructionList(QueryVideoConstructionReq) returns (VideoConstructionListReply);
  //根据统一物料id查询智能物料素材
  rpc QueryVideoConstructionListByExternalMaterialId(QueryVideoConstructionExternalReq) returns (VideoConstructionListExternalReply);
  //查询AI素材库列表
  rpc QueryAIVideoConstructionList(QueryAIVideoConstructionListReq) returns (AIVideoConstructionListReply);
  //变更AI素材状态
  rpc UpdateAIVideoConstructionStatus(UpdateAIVideoConstructionStatusReq) returns (CommonReply);
  //查询AI素材操作历史记录
  rpc QueryAIVideoConstructionHistory(QueryAIVideoConstructionHistoryReq) returns (AIVideoConstructionHistoryReply);
  //查询当前cid是否已经存在已生产的智能物料
  rpc QueryAIVideoConstructionIsProduced(QueryAIVideoConstructionIsProducedReq) returns (QueryAIVideoConstructionIsProducedReply);
  //查询视频相关基础信息
  rpc GetVideoMaterialInfo(GetVideoMaterialInfoReq) returns (GetVideoMaterialInfoReply);
  //查询视频黑名单列表
  rpc GetVideoFilterList(GetVideoFilterListReq) returns (GetVideoFilterListReply);
  //添加黑名单
  rpc AddVideoFilter(AddVideoFilterReq) returns (CommonReply);
  //删除黑名单
  rpc DeleteVideoFilter(DeleteVideoFilterReq) returns (CommonReply);
  //查询拆条视频高能点标签信息
  rpc QuerySplitVideoHighlightTag(QuerySplitVideoHighlightTagReq) returns (QuerySplitVideoHighlightTagReply);
  //视频绑定媒资
  rpc BindVideoMedia(BindVideoMediaReq) returns (BindVideoMediaReply);
  //查询视频字幕
  rpc GetVideoSubtitles(GetVideoSubtitlesRequest) returns (GetVideoSubtitlesReply);
}