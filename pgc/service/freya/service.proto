syntax = "proto3";

package pgc.service.freya;

option (wdcli.appid) = "pgc.service.freya";
option go_package = "buf.bilibili.co/bapis/bapis-gen/pgc/service.freya;api";
option java_package = "com.bapis.pgc.service.freya";
option java_multiple_files = true;

import "extension/wdcli/wdcli.proto";
import "google/protobuf/timestamp.proto";

// OType 业务类型
enum OType {
    // ogv
    OGV = 0;
}

// 房间模式(废弃)
enum RoomMode {
    // 普通房间
    N_ROOM = 0;
    // 随缘匹配房间
    R_MATCH = 1;
    // 官方放映室
    OFFICIAL = 2;
    // 谁是卧底放映室
    GAME_SPY = 3;
    // 预约房
    APPOINT = 4;
    // 大逃杀房间
    ESCAPE = 5;
    // 点映放映室
    LIVE = 6;
    // UGC首映
    UGC_PREMIERE = 7;
    // 连麦双人房
    DOUBLE = 8;
}

// 房间模式
enum SeasonRecommendType {
    // 算法推荐
    ALGORITHM = 0;
    // 运营池兜底
    OPERATION = 1;
}

// OType = OGV业务类型下的子业务类型
enum OGVSubType {
    DEFAULT = 0;
    ANIMATE = 1;
    MOVIE = 2;
    DOCUMENTARY = 3;
    CN_ANIMATE = 4;
    TV = 5;
    VARIETY = 7;
}


enum RoomQueryType {
    // 存在房间
    CONTAIN_ROOM = 0;
}

enum GreetingType {
    // 新用户发起招呼
    NEW_MEMBER = 0;
    // 房间已有用户发起招呼
    EXIST_MEMBER = 1;
}

// 长连接类型
enum ConnectionType {
    // broadcast
    BROADCAST = 0;
    // vega
    VEGA = 1;
    // vega 1.0 优化
    VEGA_OPTIMIZE = 2;
}

// 活动类型
enum ActivityType {
    // 保留
    EMPTY_ACTIVITY = 0;
    // 新春活动彩蛋
    NEW_YEAR_2021_EGG = 1;
    // 新春互动，停留时长
    NEW_YEAR_2021_STAY = 2;
    // 放映室挂件
    ROOM_PENDANT = 3;
    // 聊天大厅
    CHAT_HALL = 4;
    // 可点击图片
    CLICK_IMG = 5;
}

// 消息拉取来源类型
enum PullMsgFromType {
    // 移动端
    CLIENT = 0;
    // freya-job
    FREYA_JOB = 1;
}

enum FreyaCfgType {
    // 播放气泡
    ICON_BUBBLE = 0;
    // 一起看合成图片
    FREYA_MERGE_IMG = 1;
}

enum RoomConfType {
    // 未知配置
    UNKNOWN_CONF = 0;
    // 换片功能
    CHANGE_CONTENT = 1;
    // 换房功能
    CHANGE_ROOM = 2;
    // 成员栏功能
    MEMBER_LIST = 3;
    // 等人页面功能
    WAIT_PEOPLE = 4;
    // 结束前提示秒数
    END_TIP_SECONDS = 5;
    // 评论
    COMMENT = 6;
    // 一键三连（点赞、投币、收藏）
    FAVOR = 7;
    // 分享 (含播放器分享)
    SHARE = 8;
}

// 消息体类型
enum ChatMsgContentType {
    // 文本消息
    TEXT = 0;
    // 支持全文本可点(如破冰)
    TEXT_LINK = 1;
    // 图片链接消息
    IMG_LINK = 2;
    // 语音消息
    VOICE = 3;
}

// 消息类型
enum MsgSceneType {
    // 警告声明
    WARNING_STATEMENT = 0;
    // 破冰消息
    BREAK_ICE = 1;
    // 随缘匹配 - 关注提醒
    R_MATCH_FOLLOW = 2;
    // 随缘匹配 - 安排内容
    R_MATCH_CONTENT = 3;
    // 随缘匹配 - 选中房主
    R_MATCH_OWNER_SELECTED = 4;
    // 随缘匹配 - 被关注提醒
    R_MATCH_FOLLOWED = 5;
}

// 聊天消息状态
enum ChatMsgState {
    // 正常
    NORMAL = 0;
    // 删除
    DELETE = 1;
}

// 聊天消息查询类型
enum ChatMsgQueryType {
    // 非删除
    NOT_DELETED = 0;
    // 全部
    ALL = 1;
}

enum UpdateRange {
    // 全部
    UPDATEALL = 0;
    // 用户推荐的season
    RECSEASON = 1;
    // 用户性别
    SEX = 2;
    // 生日
    BIRTHDAY = 3;
    // 标签
    LABEL = 4;
    // 匹配性别
    MATCH_SEX = 5;
    // 授权信息
    AUTH_INFO = 6;
}

// User 用户相关信息
message User {
    // 用户mid
    int64 mid = 1;
    // 设备buvid
    string buvid = 2;
    // 用户性别 男|女|保密
    string sex = 3;
    // 是否是大会员
    int32 is_vip = 4;
    // 用户行为 (上报使用)
    int32 action_type = 5;
    // 透传客户端参数，当是web时，为空
    string mobi_app = 6;
    // 透传客户端参数，当是web时，为空
    string device = 7;
    // 透传参数(web端如果没有传参数，请设置值为pc)
    string platform = 8;
    // build号
    int32 build = 9;
    // 用户ip
    string ip = 10;
    // 用户的长连接类型
    ConnectionType connection_type = 11;
    // 区分用户一次进入一个房间到退出的生命周期标识id
    string enter_id = 12;
    // 对应客户端请求参数中的access_key,如果无法获取可传空
    string access_key = 13;
    // 客户端调用业务方的api地址，例如：/x/internal/gaia/check
    string api = 14;
    // 客户端请求的appkey
    string appkey = 15;
    // 网页上的referer
    string referer = 16;
    // 请求头中的user_agent
    string user_agent = 17;
    // web端 origin
    string origin = 18;
    // 经度
    string m_lon = 19;
    // 纬度
    string m_lat = 20;
    // 是否授权地址信息
    int32 locate = 21;
    // 账号等级
    int32 level = 22;
    // 气泡消息
    ChatBubble bubble = 23;
}

//用户信息
message UserInfoProto {
    // 用户id
    int64 mid = 1;
    // 用户头像url
    string face = 2;
    // 昵称
    string nickname = 3;
    // 等级
    int32 level = 4;
    // 签名
    string sign = 5;
    // 大会员信息
    VipProto vip = 6;
    // 身份认证信息
    OfficialProto official = 7;
    // 挂件信息
    PendantProto pendant = 8;
    // 设备buvid
    string buvid = 9;
    // 身份标语，有则优先展示，没有则默认判断
    string role = 10;
    // 聊天气泡
    ChatBubble chat_bubble = 11;
}

//大会员信息
message VipProto {
    int32 type = 1;
    int32 status = 2;
    int64 due_date = 3;
    int32 vip_pay_type = 4;
    int32 theme_type = 5;
    // 大会员角标，0：无角标，1：粉色大会员角标，2：绿色小会员角标
    int32 avatar_subscript = 6;
    // 昵称色值，可能为空，色值示例：#FFFB9E60
    string nickname_color = 7;
}

//认证信息
message OfficialProto {
    int32 role = 1;
    string title = 2;
    string desc = 3;
    int32 type = 4;
}

//挂件信息
message PendantProto {
    int32 pid = 1;
    string name = 2;
    string image = 3;
    int64 expire = 4;
    string image_enhance = 5;
}

// RoomRule
message RoomRule {
    // 是否公开
    int32 is_open = 1;
    // 性别匹配方式 0不区分 1异性 2同性
    int32 sex_type = 2;
}

message RoomConf {
    // 配置类型
    RoomConfType type = 1;
    // 0 功能可用可见 1 功能不展示 2 功能不可用弹框提示
    int32 value = 2;
    // 提示信息
    string message = 3;
}

// RoomInfo
message RoomInfo {
    // room_id
    int64 room_id = 1;
    // 业务id
    int64 oid = 2;
    // 业务子id
    int64 sub_id = 3;
    // 业务类型
    OType otype = 4;
    // 是否公开
    int32 is_open = 5;
    // 性别匹配方式 0不区分 1异性 2同性
    int32 sex_type = 6;
    // 房主mid
    int64 mid = 7;
    // 成员信息
    repeated RoomMember members = 8;
    // 房间状态
    RoomStatus status = 9;
    // 创建时间
    string ctime = 10;
    // 更新时间
    string mtime = 11;
    // 房间限制人数
    int32 limit_count = 12;
    // 子业务类型
    int64 sub_type = 13;
    // 房间类型
    RoomMode room_mode = 14;
    // 匹配结果
    MidMatchRes match_res = 15;
    // 区分用户一次进入一个房间到退出的标识id
    string enter_id = 16;
    // 是否打开等人一起看模式
    int32 wait_switch = 17;
    // 用户进入房间时的消息版本号
    int64 init_sequence_id = 18;
    // 开放时间
    string open_time = 19;
    // 房间配置属性
    map<string, RoomConf> room_conf = 20;
    // 房间所属信息 （roomMode=ESCAPE时大逃杀节点信息）
    string key = 21;
    // 成员人数，当人数过大时，member不会给所有数据
    int64 member_count = 22;
    // 房间标题
    string title = 23;
    // 公告描述等信息
    string desc = 24;
    // 公告版本
    int32 desc_ver = 25;
    // 房间活动相关信息
    RoomActivity activity = 26;
    // 组件房间component_room_id
    int64 com_room_id = 27;
    // 房间内最新的消息seqId
    int64 latest_sequence_id = 28;
}

// 房间活动相关信息
message RoomActivity {
    // 活动开始时间
    google.protobuf.Timestamp start = 1;
    // 活动结束时间
    google.protobuf.Timestamp end = 2;
}

// 房间维度房间卡信息
message RoomInfoCard {
    // room_id
    int64 room_id = 1;
    // 业务id
    int64 oid = 2;
    // 业务子id
    int64 sub_id = 3;
    // 业务类型
    OType otype = 4;
    // 房主mid
    int64 mid = 5;
    // 成员信息
    repeated RoomMemberCard members = 6;
    // 房间状态
    RoomStatus status = 7;
    // 房间类型
    RoomMode room_mode = 8;
}

// RoomMember
message RoomMember {
    // 用户id
    int64 mid = 1;
    // 设备buvid
    string buvid = 2;
    // 用户类型 0:普通用户 1:房主 2:嘉宾
    int64 type = 3;
    // 创建时间
    string ctime = 4;
    // 更新时间
    string mtime = 5;
    // 进入类型 0 匹配进入 1 邀请进入 2 房间合并
    int64 join_type = 6;
    // 用户的长连接类型
    ConnectionType connection_type = 7;
    // 客户端版本号
    int64 build = 8;
    // 客户端设备类型
    string device = 9;
    // 客户端包类型
    string mobi_app = 10;
    // 用户进入房间的生命周期标识id
    string enter_id = 11;
    // 平台信息
    string platform = 12;
    // 身份标语，有则优先展示，没有则默认判断
    string role = 13;
}

// RoomMember
message RoomMemberCard {
    // 用户id
    int64 mid = 1;
    // 设备buvid
    string buvid = 2;
    // 用户类型 0:普通用户 1:房主
    int64 type = 3;
    // 头像
    string face = 4;
    // 名字
    string name = 5;
}

// RoomStatus
message RoomStatus {
    // 播放状态：0暂停，1播放，2终止
    int32 status = 1;
    // 上次播放的时间
    int64 last_time = 2;
    // 起播进度
    int64 progress = 3;
    // 业务id
    int64 oid = 4;
    // 业务子id
    int64 sub_id = 5;
    // 业务类型
    OType otype = 6;
    // 房间过期时间(s)
    int64 expire_time = 7;
    // 是否允许自动起播
    int32 allow_auto_start = 8;
    // 是否起播过
    int32 had_started = 9;
    // 除房主外其他成员鉴权通过数
    int32 allow_play_num = 10;
    // room_id
    int64 room_id = 11;
    // 视频长度 单位：毫秒
    int64 duration = 12;
    // 业务标记位，每一位代表不同维度的bool  1-是否系统起播过
    int32 biz_flag = 13;
    // 额外匹配需要字段json,结构参看MatchRoomDto的具体子类(只含子类独有字段)
    string match_ext = 14;
}

// InfoReq
message InfoReq {
    // 用户信息
    User user = 1;
    // 房间id,如果没有则认为创建
    int64 room_id = 2;
    // 业务id
    int64 oid = 3;
    // 业务子id
    int64 sub_id = 4;
    // 业务类型
    OType otype = 5;
    // 房间规则
    RoomRule rule = 6;
    // 邀请人mid
    int64 invite_mid = 7;
    // 子业务类型
    int64 sub_type = 8;
    // 匹配来源: 0默认 1创建 2从广场页匹配并加入 3从搜索页匹配并加入 4邀请加入 5合并加入 6从详情页匹配加入 7从详情页创建 8 详情页随缘匹配 9广场页随缘匹配 10随缘匹配失败加入 11等人一起看加入
    int32 from_type = 9;
    // 是否需要风控判断
    bool need_risk = 10;
    // 加入校验的token
    string join_token = 11;
}

// NoticeReq
message NoticeReq {
    // 用户信息
    User user = 1;
    // 房间id
    int64 room_id = 2;
    // 公告描述等信息
    string desc = 3;
}

// RoomStatusReq
message RoomStatusReq {
    // 用户信息
    User user = 1;
    // 房间id
    int64 room_id = 2;
    // 业务类型
    OType otype = 3;
    // 房间状态
    RoomStatus status = 4;
    // 子业务类型
    int64 sub_type = 5;
    // 是否组件范畴
    int32 is_com = 6;
    // 是否系统请求
    int32 is_sys = 7;
}

// RoomIdReq
message RoomIdReq {
    // 用户信息
    User user = 1;
    // 房间id
    int64 room_id = 2;
    // 业务类型
    OType otype = 3;
    // 房主变更消息
    ChangeOwner change_owner = 4;
    // 是否组件范畴
    int32 is_com = 5;
}

// RoomIdReq
message RoomIdsReq {
    // 房间id
    repeated int64 room_ids = 1;
    // 业务类型
    OType otype = 2;
}

// 变更房主时 需要传入
message ChangeOwner {
    // 原先房主
    User old_room_owner = 1;
    // 房间有心跳成员
    repeated User online_user = 2;
}

// RoomIdReply
message RoomIdReply {
    // 房间id
    int64 room_id = 1;
    // 当入参oid是0时，为建议oid
    int64 oid = 2;
}

// KickOutReq
message KickOutReq {
    // 用户信息
    User user = 1;
    // 房间id
    int64 room_id = 2;
    // 业务类型
    OType otype = 3;
    // 被踢用户id
    int64 mid = 4;
}

// MergeRoomReq
message MergeRoomReq {
    // 房间id
    int64 room_id = 1;
    // 房主
    int64 mid = 2;
    // 合并目标的房间id
    int64 target_room_id = 3;
}

// SendChatMessageReq
message SendChatMessageReq {
    // 房间id
    int64 room_id = 1;
    // 发送者用户信息
    User user = 2;
    // 消息类型
    int32 content_type = 3;
    // 发送信息内容
    string content = 4;
    // 客户端请求标识
    int64 req_id = 5;
    // 增加是否系统标识，系统消息不过敏感词
    bool is_sys = 6;
    // 发送条件
    SendFilter filter = 7;
    // 是否需要风控
    bool need_risk = 8;
}

// SendFilter
message SendFilter {
    // 是否需要过滤
    bool need = 1;
    // 发送对象mid
    int64 mid = 2;
    // 接收事件消息的白名单用户
    repeated int64 allow = 3;
    // 不处理信息的黑名单用户 优先级低于白名单，当白名单有数据时，忽略黑名单
    repeated int64 block = 4;
}

// SendChatMessageReply
message SendChatMessageReply {
    // 消息id
    int64 msg_id = 1;
    // 房间消息
    RoomInfo room_info = 2;
}

// CardReq
message CardReq {
    // 业务id
    repeated int64 oids = 1;
    // 业务类型
    OType otype = 2;
}

// RoomCard season维度房间卡信息
message RoomCard {
    // 业务id
    int64 oid = 1;
    // 热度值
    int64 hot = 2;
    // 房间数
    int64 count = 3;
    // 最近加入的三个用户的mid
    repeated int64 mids = 4;
}

// RoomCardReply
message RoomCardReply {
    map<int64, RoomCard> result = 1;
}

// RoomMatchReq
message RoomMatchReq {
    // 用户信息
    User user = 1;
    // 业务id
    int64 oid = 2;
    // 业务子id
    int64 sub_id = 3;
    // 合并房间匹配时的当前用户所在roomId
    int64 room_id = 4;
    // 匹配来源: 0默认 1创建 2从广场页匹配并加入 3从搜索页匹配并加入 4邀请加入 5合并加入 6从详情页匹配加入 7从详情页创建 8 详情页随缘匹配 9广场页随缘匹配 10随缘匹配失败加入 11等人一起看加入
    int32 from_type = 5;
}

// RoomMergeReq
message RoomMergeReq {
    // 房间id
    int64 room_id = 1;
    // 房主
    int64 mid = 2;
    // 合并目标的房间id
    int64 target_room_id = 3;
}

// RoomDestroyReq
message RoomDestroyReq {
    // 房间id
    int64 room_id = 1;
    // 房间行为 (上报使用)
    int32 action_type = 2;
    // 房间关闭原因
    DestroyReason reason = 3;
}

// 房间关闭原因
enum DestroyReason {
    // 普通关闭
    REASON_NORMAL = 0;
    // 风控关闭
    REASON_RISK = 1;
}

// 请求最近观看用户
message RecentWatcherReq {
    // 请求条数 超过100条默认按100处理
    int32 limit_size = 1;
}

// mid返回集合
message MidsReply {
    // 最近加入的三个用户的mid
    repeated int64 mids = 1;
}

// 聊天消息
message ChatMessage {
    // 消息id
    int64 msg_id = 1;
    // 房间id
    int64 room_id = 2;
    // 发送者id
    int64 mid = 3;
    // 消息体类型
    ChatMsgContentType content_type = 4;
    // 消息体
    string content = 5;
    // 消息状态
    ChatMsgState state = 6;
    // 发送时间戳，单位: 秒
    int64 ts = 7;
    // 消息版本号
    int64 sequence_id = 8;
    // 上报json串，可能为空字符串
    string report = 9;
    // 聊天气泡
    ChatBubble chat_bubble = 10;
    // 成员角色
    string role = 11;
    // 通知信息类型
    int32 msg_type = 12;
    // 信息通知发送领域
    int32 msg_domain = 13;
}
message ChatBubble {
    // 背景色
    BgColor bg_color = 1;
    // 黑夜模式背景色
    BgColor dark_bg_color = 2;
}
enum ColorType {
    NONE = 0;
    // 渐变
    GRADIENT = 1;
    // 纯色
    PURE = 2;
}
message BgColor {
    ColorType type = 1;
    // 颜色 渐变色时数组标识从左到右
    repeated string color = 2;
}

// server端区分场景发送的消息 如 破冰消息/关注消息等
message SceneChatMessage {
    // 消息主体
    ChatMessage message = 1;
    // 场景
    MsgSceneType scene_type = 8;
}

// 批量聊天消息
message ChatMessagesReply {
    repeated SceneChatMessage messages = 1;
}

// 获取聊天消息
message ChatMessageReq {
    // 消息id
    int64 msg_id = 1;
}

// 获取上下文消息，返回条数：前left条+当前消息+后right条
message NearbyMessagesReq {
    // 消息id
    int64 msg_id = 1;
    // 前left条
    int32 left = 2;
    // 后right条
    int32 right = 3;
    // 查询类型
    ChatMsgQueryType query_type = 4;
}

// 上下文消息响应
message NearbyMessagesReply {
    // 消息列表
    repeated ChatMessage msg = 1;
}

// 分页查询各种房间状态的seasons
message RoomSeasonsReq {
    // 查询类型 目前仅支持 0:存在房间的season
    RoomQueryType queryType = 1;
    // 页码
    int32 pn = 2;
    // 页大小
    int32 ps = 3;
}

message RoomSeasonsReply {
    // season一起看房间简略信息集合 key:seasonId value:season房间简略信息
    map<int32, RoomSeasonInfo> room_info = 1;
    // 是否有下一页
    bool has_next = 2;
    // 总条数
    int32 total = 3;
}

message RoomSeasonInfo {
    // season_id
    int32 season_id = 1;
    // 房间数
    int64 count = 2;
}

// 查询广场页feed推荐season池
message FeedRecSeasonsReq {
    // 用户id
    int64 mid = 1;
    // 是否是大会员 0 不是 1 是
    int32 is_vip = 2;
    // 排序类型 0 热度值 1 点击量
    int32 rank_type = 3;
    // seasonType 或者 episodeType
    int32 otype = 4;
}

// 该返回已经废弃 见FeedRecHotSeasonsReply
message FeedRecSeasonsReply {
    repeated FeedRecProto feeds = 1 [deprecated = true];
}

message FeedRecHotSeasonsReply {
    // vip 池子
    repeated FeedRecProto vipHotSeasons = 1;
    // free 池子
    repeated FeedRecProto freeHotSeasons = 2;
}

message FeedRecProto {
    // 每个mid下的seasonIds、epIds推荐倒排链
    int64 id = 1;
    // id类型 season、episode
    string type = 2;
    // 热度值
    double hot_score = 3;
}

// 消息列表请求
message RoomMessagesReq {
    // 用户信息
    User user = 1;
    // 房间id
    int64 room_id = 2;
    // 消息id，只返回大于id的数据
    int64 msg_id = 3;
}

// admin消息列表请求
message AdminRoomMessagesReq {
    // 用户信息, 如果mid大于0，则只返回该用户(100条内)
    User user = 1;
    // 房间id，最多50个
    repeated int64 room_id = 2;
    // 每个房间消息数量
    int32 mgs_size = 3;
}

// 消息列表返回
message MessagesReply {
    // 消息列表
    repeated ChatMessage msg = 1;
}

// admin消息列表返回
message AdminMessagesReply {
    // 消息列表
    map<int64, MessagesReply> data = 1;
}

// 查询一起看的一些数据version
message FreyaDataVersionReq {
    // 请求version类型
    string version_type = 1;
}

// 一起看version返回
message FreyaDataVersionReply {
    // 当前version
    string cur_version = 1;
}

// 用户进房间打招呼
message GreetingReq {
    // 房间id
    int64 room_id = 1;
    // 打招呼类型
    GreetingType greetingType = 2;
    // 用户信息
    User user = 3;
    // 客户端请求标识
    int64 req_id = 4;
    // mid 打招呼的目标id
    int64 mid = 5;
    // 打招呼的目标用户的name
    string greeting_name = 6;
    // 打招呼的触发动作
    int32 action_type = 7;
}

// 用户进房间打招呼
message TipMessageReq {
    // 房间id
    int64 room_id = 1;
    // 用户信息
    User user = 2;
}

// 批量获取消息
message TipMessagesReq {
    // 房间id
    int64 room_id = 1;
    // 用户信息
    User user = 2;
    // 场景类型 1-破冰 2-关注提示等
    repeated int32 scenes = 3;
}

message UserInfo {
    // 用户id
    int64 mid = 1;
    // 性别
    string sex = 2;
    // 生日
    string birthday = 3;
    // 生日时间戳
    int64 birth_time = 4;
    // 年龄
    int32 age = 5;
    // 星座
    string constellation = 6;
    // 星系
    string galaxy = 7;
    // 用户标签
    repeated UserLabelShow labels = 8;
    // 用户想要匹配的性别 0不限 1男 2女
    int32 match_sex = 9;
    // ip
    string ip = 10;
    // 经度
    string m_lon = 11;
    // 纬度
    string m_lat = 12;
    // 是否授权地址信息
    int32 locate = 13;
    // 是否禁止授权个人信息
    int32 ban_base_info = 14;
}

// 修改部分用户信息Req
message UpdateUserLabelPartlyReq {
    // 用户信息
    UserInfo user_info = 1;
    // update 范围
    repeated UpdateRange range = 3;
}

// 一起看用户侧 额外信息
message UserExtReq {
    // 用户id
    int64 mid = 1;
    // 推荐的seasonIds
    repeated int64 season_ids = 2;
    // update 范围
    repeated UpdateRange range = 3;
    // 业务类型: 0 season_id 1 ep_id
    int32 otype = 4;
}

// 一起看用户侧 额外信息
message UserExtInfoReply {
    repeated UserRec user_rec = 1;
}

// 用户推荐信息
message UserRec {
    int64 mid = 1;
    // 业务id
    int64 oid = 2;
    // 业务类型: 0 season_id 1 ep_id
    int32 otype = 3;
    // 排序字段 需要手动塞值
    int32 ord = 4;
}

message UserLabel {
    // 用户标签对应 id
    int64 id = 1;
    // 用户标签名称
    string name = 2;
    // 用户标签类型
    string type = 3;
    // 标签的排序
    int32 ord = 4;
    // 背景色值
    string bg_color = 5;
    // 背景夜间色值
    string bg_night_color = 6;
    // 文字色值
    string text_color = 7;
    // 文字夜间色值
    string text_night_color = 8;
}

// 用户标签-展示用
message UserLabelShow {
    // 用户标签对应 id
    int64 id = 1;
    // 用户标签内容
    string name = 2;
    // 用户标签类型
    string type = 3;
    // 背景色值
    string bg_color = 4;
    // 背景夜间色值
    string bg_night_color = 5;
    // 文字色值
    string text_color = 6;
    // 文字夜间色值
    string text_night_color = 7;
}

// mid - 匹配结果
message MidMatchRes {
    repeated MatchRes match_res = 1;
}

// 匹配结果
message MatchRes {
    // 匹配度
    int32 score = 1;
    // 匹配话题
    repeated string topics = 2;
    // 匹配标签
    repeated UserLabelShow labels = 3;
    // mid
    int64 mid = 4;
}

message LabelConfigReply {
    repeated UserLabel labels = 1;
}

message LabelConfigReq {
    // 排序规则
    int32 order_type = 1;
}

message CreateForMatchReq {
    // 用户信息
    repeated User users = 1;
    // 业务id
    int64 oid = 2;
    // 业务子id
    int64 sub_id = 3;
    // 业务类型
    OType otype = 4;
    // 房间类型
    RoomMode roomMode = 5;
    // 随缘匹配类型的房间拓展信息
    RMatchRoomInfo rmatch_info = 6;
}

// 随缘匹配类型的房间拓展信息
message RMatchRoomInfo {
    // 用户相似度分数
    int64 score = 1;
    // 随缘匹配花费的时间 key:mid, value duration耗时，单位秒
    map<int64, int64> duration = 2;
    // 推荐的内容的来源
    SeasonRecommendType rec_type = 3;
    // 匹配id
    map<int64, string> match_ids = 4;
}

message UpdateStatusReply {
    // 用户mid
    int64 mid = 1;
    // 更新状态 0 更新过  1 没有更新过
    int32 status = 2;
}

message ReportMsgReq {
    // 房间信息
    RoomInfo room_info = 1;
    // 用户信息
    User user = 2;
    // 邀请人mid
    int64 invite_mid = 3;
    // event 上报使用
    string event = 4;
    // 用户房间状态
    UserRoomStatus user_room_status = 5;
    // 最后一次心跳时间 单位 秒
    int64 last_live_time = 6;
}

message UserRoomStatus {
    // 房间ID
    int64 room_id = 1;
    // mid
    int64 mid = 2;
    // 加入时间
    int64 enter_time = 3;
    // 离开时间
    int64 leave_time = 4;
    // 是否是房主
    bool is_room_owner = 5;
    // 播放状态
    int32 play_status = 6;
    // seasonId
    int64 season_id = 7;
    // episodeId
    int64 episode_id = 8;
    // enterId 用户进入房间的生命周期标识id
    string enter_id = 9;
}

// 业务id请求
message BizIdReq {
    int64 oid = 1;
    int64 sub_id = 2;
    int64 mid = 3;
}

// WaitingRoomReply
message WaitingRoomReply {
    // 成员信息
    repeated WaitingRoom rooms = 1;
    // 创建连接
    string create_link = 2;
    // oid对应title
    string o_title = 3;
}

// 等待中的房间信息
message WaitingRoom {
    // oid
    int64 room_id = 1;
    // oid
    int64 oid = 2;
    // sub_id
    int64 sub_id = 3;
    // oid 对应title
    string o_title = 4;
    // sub_id 对应title
    string sub_title = 5;
    // sub背景
    string sub_cover = 6;
    // 是否起播过
    int32 had_started = 7;
    // 等待描述：等你看/正在放映
    string waiting_desc = 8;
    // 观看成员
    repeated RecentWatcher recent_watchers = 9;
    // 房主昵称
    string owner_name = 10;
    // 房主性别
    int32 owner_sex = 11;
    // 房主头像
    string owner_face = 12;
    // 房主性别图标
    string owner_sex_icon = 13;
    // desc2
    string desc2 = 14;
    // icon2
    string icon2 = 15;
    // 播放进度百分比
    int32 progress_percent = 16;
}

// 最近观看者
message RecentWatcher {
    // 头像
    string face = 1;
    // 名字
    string name = 2;
    // mid
    int64 mid = 3;
}

//消息列表v2请求
message PullMessagesReq {
    // 用户信息
    User user = 1;
    // 房间id
    int64 room_id = 2;
    // 拉取的消息版本号起始id
    int64 start_seq_id = 3;
    // 拉取的消息版本号结束id，如果不传，默认返回最多100条信息
    int64 end_seq_id = 4;
    // 消息拉取来源
    PullMsgFromType from_type = 5;
}

//消息列表v2返回
message PullMessagesReply {
    //消息列表
    repeated ChatMessage msg = 1;
    //最新的消息版本号
    int64 latest_seq_id = 2;
}

message FirstTipsReq {
    // 用户信息
    User user = 1;
    // 房间id
    int64 room_id = 2;
    // 拉取的消息版本号起始id
    int64 start_seq_id = 3;
}

message Empty {

}
// 一起看白名单请求入参
message FreyaOidListReq {
    // 当下游方为了更新缓存时，请一定设置为true，保证回源数据库
    bool no_cache = 1;
}

// 一起看白名单id集合
message FreyaOidListReply {
    repeated int64 ids = 1;
}

message FreyaContainsReq {
    int64 oid = 1;
}

message FreyaContainsReply {
    bool exist = 1;
}

// 一起看系统发送消息
message FreyaSystemSendReq {
    // 一起看房间id
    int64 room_id = 1;
    // 一起看事件名称
    string event_name = 2;
    // 事件通知消息
    string msg = 3;
    // 消息id
    int64 msg_id = 4;
    // 信息通知发送领域  0 默认，1 房间用户， 2 系统通知
    int32 domain = 5;
    // 通知信息类型 0 默认，1 房间用户， 2 系统通知
    int32 msg_type = 6;
    // 消息发送服务端时间 时间戳 单位秒
    int64 ts = 7;
    // 可带占位符匹配的消息体 ep "还没有其他小伙伴，[去邀请>]<https://big.bilibili.com/mobile/giftIndex?mid=123>"
    string content = 8;
    // 消息体类型 0 json格式的文本消息 1 支持全文本可点(破冰)
    int32 content_type = 9;
    // 业务特化参数
    Business business = 10;
    // 系统消息上报信息json串，可以为空字符串
    string report = 11;
    // 发送条件
    SendFilter filter = 12;
    // bizType
    int32 biz_type = 13;
}

message Business {
    // 一起看 关注者 id
    int64 mid = 10;
    // 一起看 被关注者 id
    int64 fid = 11;
    // 触发通知类型 TriggerType 0 默认, 1 关注类动作（关注,取关）2 点映放映室白名单用户进入 3成员变更类型
    int32 trigger_type = 12;
}

// 新年活动请求参数
message ActivityReq {
    // 用户信息
    User user = 1;
    // 房间id
    int64 room_id = 2;
    // 消息id
    int64 msg_id = 3;
    // 活动类型
    ActivityType act_type = 4;
    // 客户端请求标识
    int64 req_id = 5;
    // oid
    int64 oid = 6;
    // sub_id
    int64 sub_id = 7;
    // joinTime
    string join_time = 8;
}

// 活动请求参数
message ActivityStatus {
    // 0 不存在/非法 1 有效可以进行后续动作
    int32 valid = 1;
}

message ActivityInfoReply {
    repeated ActBaseInfo act_base_infos = 1;
}

message ActBaseInfo {
    // 活动落地页
    string land_url = 1;
    // 图片地址
    string img_url = 2;
    // 活动类型
    ActivityType act_type = 3;
    // 活动落地页2 (挂件活动 web端用)
    string land_url_2 = 4;
    // 图片地址2 (挂件活动 web端用)
    string img_url_2 = 5;
}

// 批量请求房间信息
message RoomCardsReq {
    // 房间id
    repeated int64 room_ids = 1;
}

// 批量房间信息reply
message RoomCardsReply {
    // 房间信息
    map<int64, RoomInfoCard> infos = 1;
}

message FreyaCfgReq {
    repeated FreyaCfgType config_types = 1;
    int64 oid = 2;
    int64 sub_id = 3;
}

message FreyaCfgReply {
    map<int32, FreyaConfig> configs = 1;
}

message FreyaConfig {
    // 描述文案
    string desc = 1;
    // 配置类型 8 播放气泡
    int32 type = 2;
    // 下发次数 已废弃
    int32 issued_cnt = 3 [deprecated = true];
    // 是否长久显示气泡 已废弃
    bool is_always_show = 4 [deprecated = true];
    // 半屏生效次数
    int32 screen_number = 5;
    // 全屏生效次数
    int32 full_screen_number = 6;
    // web生效次数
    int32 web_number = 7;
}

message FreyaExperimentReq {
    repeated int64 oids = 1;
    repeated FreyaCfgType config_types = 2;
}

message FreyaExperimentReply {
    map<int64, FreyaExperInfo> exper_infos = 1;
}

// RoomInfosReply
message RoomInfosReply {
    map<int64, RoomInfo> room_infos = 1;
}

message FreyaExperInfo {
    // 是否可以匹配 0 否  1 是
    int32 can_match = 1;
    // img link
    string link = 2;
    // match link
    string link2 = 3;
}

// GameUpdateReq
message UpdateMatchExtReq {
    // 房间id
    int64 room_id = 1;
    // 房间类型
    RoomMode mode = 2;
    // json字符串，结构参看MatchRoomDto的具体子类(只含子类独有字段)
    string body = 3;
}

// 用户标记
message UserFlag {
    // 是否新用户 0-否 1-是
    int32 is_new = 1;
}

// 返回info json
message InfoJsonReply {
    string info_json = 1;
}

// 返回info json
message InfoJsonReq {
    // 用户信息
    User user = 1;
    // 房间id
    int64 room_id = 2;
    // 业务类型
    OType otype = 3;
    // 请求oid 房主时以此为准
    int64 req_oid = 4;
    // 请求sub_id 房主时以此为准
    int64 req_sub_id = 5;
}

// 通用活动req
message CommonActInfoReq {
    // 请求活动类型
    repeated ActivityType act_types = 1;
    // oid
    int64 oid = 2;
    // subId
    int64 sub_id = 3;
    // bizType详见BizTypeEnum
    int32 biz_type = 4;
    // roomId 大厅id/放映室id
    int64 room_id = 5;
}

// 通用活动配置
message CommonActInfoReply {
    repeated CommonActInfo act_infos = 1;
}

// 猜你喜欢特化卡req
message GuessCardReq {
    // 猜你喜欢的业务id 含seasonId、epId
    repeated GuessBizId guessIds = 1;
}

// 猜你喜欢业务id
message GuessBizId {
    // oid
    int64 oid = 1;
    // sub_id
    int64 sub_id = 2;
    // 1-season 2-ep
    int32 recall_type = 3;
}

// 猜你喜欢特化卡reply
message GuessCardReply {
    // oid对应结果map
    map<int64, GuessCard> oid_cards_map = 1;
    // sub_id对应结果卡map
    map<int64, GuessCard> sub_id_cards_map = 2;
}

// 一起看猜你喜欢卡
message GuessCard {
    // 角标url
    string badge = 1;
    // icon集合
    repeated string icons = 2;
    // 自定义描述 desc
    string desc = 3;
    // 跳转链接
    string link = 4;
}

// 通用活动信息
message CommonActInfo {
    // id
    int64 id = 1;
    // 活动类型
    int32 act_type = 2;
    // 活动信息
    oneof act_info {
        ChatHall hall = 100;
        ClickImg click_img_info = 101;
        Pendant pendant = 102;
    }
}

// 聊天大厅
message ChatHall {
    // 长连接路径
    string target_path = 1;
    // deprecated已放入ext 业务key
    string biz_key = 2;
    // 主tab显示
    string tab = 3;
    // icon
    string icon = 4;
    // 副tab显示
    string tab_sub = 5;
    // 提示文字 展示形式eg:气泡
    string tip_text = 6;
    // 提示图标
    string tip_icon = 7;
    // deprecated 已放入ext 展示mode 0-无 1-小红点 2-hot动效
    int32 show_mode = 8;
    // 聊天大厅扩展字段 避免每次调整一起看view都要跟随发版
    string ext = 9;
}

// 可点击图片
message ClickImg {
    // 图片地址
    string img_link = 1;
    // 图片链接
    string link = 2;
    // app 跳转方式 1 全屏 2 半屏
    int32 app_skip = 3;
    // web 跳转方式  1 新标签页 2 iframe
    int32 web_skip = 4;
}

// 放映室/聊天大厅 挂件
message Pendant {
    // 挂件图片地址
    string img_url = 1;
    // 活动跳转地址
    string url = 2;
    // web端图片地址
    string web_img_url = 3;
    // web端跳转地址
    string web_url = 4;
}

message AccompanyInfo {
    // 陪伴次数
    int32 count = 1;
    // 陪伴时间（毫秒）
    int64 duration = 2;
    // 互动表情次数
    int32 freya_emote = 3;
}

message AccompanyInfoReq {
    // 用户信息
    User user = 1;
    // 房间id
    int64 room_id = 2;
    // 别人的mid
    int64 the_other = 3;
}

message AccompanyInfoReply {
    // 本次陪伴信息
    AccompanyInfo current = 1;
    // 累计陪伴信息
    AccompanyInfo accumulate = 2;
    // 服务端渲染的用于分享的图片
    string share_pic_url = 3;
}

// 放映室管控消息
message FreyaControlMessage {
    //消息id
    int64 message_id = 1;
    //用户id
    int64 mid = 2;
}
// 管控消息请求
message ControlMsgReq {
    repeated FreyaControlMessage message = 1;
}

// 返回管控失败的消息
message ControlMsgReply {
    //消息id
    repeated int64 message_id = 1;
}

// 获得渲染好的分享卡请求
message PointLiveShareCardReq {
    // 用户uid
    int64 mid = 1;
    // 房间id
    int64 room_id = 2;
}

// 返回分享卡的地址
message PointLiveShareCardReply {
    // 服务端渲染好的完整分享卡
    string share_card = 1;
}

// 超前点映用户内容鉴权req
message LivePremiereAuthReq {
    // 房间id
    int64 room_id = 1;
    // epId
    int64 ep_id = 2;
    // mid
    int64 mid = 3;
}
// 超前点映用户内容鉴权
message LivePremiereAuthReply {
    // 是否是超前点映
    bool is_live_pre = 1;
    // 是否有权限
    bool has_right = 2;
}

// 语音消息签名请求
message FreyaVoiceUrlSignReq {
    // 语音消息的msgId
    int64 msg_id = 1;
    // 用户ip
    string user_ip = 2;
    // 用户平台
    string platform = 3;
}

// 语音消息签名返回
message FreyaVoiceUrlSignReply {
    // 带签名的url
    string voice_url = 1;
    // 签名过期时间,为秒级时间戳
    int64 expire = 2;
}


// 纯享模式历史消息拉取请求
message PointLivePureModeMsgReq {
    int64 room_id = 1;
    // sequenceId,决定房间内的消息顺序
    int64 seq_id = 2;
    // backward为true，表明朝着seqId减小的方法拉取更旧的消息；backward为false，表明朝着seqId增大的方法拉取更新的消息，
    bool backward = 3;
    // 客户端希望返回的消息数量，消息数量上限实际由服务端决定
    int32 ps = 4;
    // 用户信息
    User user = 5;
}

// 纯享模式历史消息拉取返回内容
message PointLivePureModeMsgReply {
    // has_next为true表示没有可以继续拉取消息，has_next为false表示没有更多的消息可以拉取了
    bool has_next = 1;
    // has_next为true时下一个可拉取的seqId
    int64 next_seq_id = 2;
    // 嘉宾历史消息
    repeated ChatMessage msg = 3;
    // key：msgId value:用户类型 1-放映员 2-嘉宾
    map<int64, int32> msg_id_uer_type = 4;
}
// Freya service
service Freya {
    // 加入/创建房间
    rpc Join (InfoReq) returns (RoomInfo);
    // 离开房间
    rpc Leave (RoomIdReq) returns (RoomIdReply);
    // 房间信息
    rpc Info (RoomIdReq) returns (RoomInfo);
    // 房间状态信息
    rpc Status (RoomIdReq) returns (RoomStatus);
    // 修改房间信息, 目前只支持修改rule，需要都传递
    rpc ModifyInfo (InfoReq) returns (RoomIdReply);
    // 修改房间状态
    rpc ModifyStatus (RoomStatusReq) returns (RoomIdReply);
    // 踢人
    rpc KickOut (KickOutReq) returns (RoomInfo);
    // 房间合并提示
    rpc RoomMergeTip (MergeRoomReq) returns (RoomInfo);
    // 发送房间im聊天信息
    rpc SendChatMessage (SendChatMessageReq) returns (SendChatMessageReply);
    // 房间卡片信息(season维度)
    rpc Card (CardReq) returns (RoomCardReply);
    // 房间匹配
    rpc RoomMatch (RoomMatchReq) returns (RoomIdReply);
    // 房间合并
    rpc Merge (InfoReq) returns (RoomInfo);
    // 管理员房间销毁
    rpc AdminDestroy (RoomDestroyReq) returns (RoomIdReply);
    // 管理员踢人
    rpc AdminKickOut (KickOutReq) returns (RoomInfo);
    // 查询最近用户
    rpc RecentWatcher (RecentWatcherReq) returns (MidsReply);
    // 查询im聊天信息
    rpc ChatMessageById (ChatMessageReq) returns (ChatMessage);
    // 查询上下文消息
    rpc NearbyMessages (NearbyMessagesReq) returns (NearbyMessagesReply);
    // 匹配并加入房间
    rpc RoomMatchAndJoin (InfoReq) returns (RoomInfo);
    // 分页查询各种房间状态的seasons
    rpc RoomSeasons (RoomSeasonsReq) returns (RoomSeasonsReply);
    // 查询广场页feed推荐season池 (废弃接口,请不要使用) 见FeedRecHotSeasons
    rpc FeedRecSeasons (FeedRecSeasonsReq) returns (FeedRecSeasonsReply) {
      option deprecated = true;
    };
    // 查询房间内，最近100条
    rpc RoomMessages (RoomMessagesReq) returns (MessagesReply);
    // 消息列表拉取v2
    rpc PullMessages (PullMessagesReq) returns (PullMessagesReply);
    // admin查询房间内，最近100条
    rpc AdminRoomMessages (AdminRoomMessagesReq) returns (AdminMessagesReply);
    // 查询一起看的一些数据版本
    rpc FreyaDataVersion (FreyaDataVersionReq) returns (FreyaDataVersionReply);
    // 用户进房间打招呼
    rpc Greeting (GreetingReq) returns (ChatMessage);
    // 进入房间的提示消息
    rpc TipMessage (TipMessageReq) returns (ChatMessage);
    // 单个请求用户标签信息
    rpc GetUserInfo (User) returns (UserInfo);
    // 修改用户标签
    rpc UpdateUserLabel (UserInfo) returns (UserInfo);
    // 修改部分用户信息
    rpc UpdateUserLabelPartly (UpdateUserLabelPartlyReq) returns (UserInfo);
    // 获取已配置的用户标签
    rpc LabelConfig (LabelConfigReq) returns (LabelConfigReply);
    // 算法匹配进行的创建
    rpc CreateForMatch (CreateForMatchReq) returns (RoomInfo);
    // 获取用户更新状态
    rpc UpdateStatus (User) returns (UpdateStatusReply);
    // 根据用户mid查询用户所在房间信息
    rpc AdminRoomInfo (User) returns (RoomInfo);
    // 批量获取提示消息
    rpc TipMessages (TipMessagesReq) returns (ChatMessagesReply);
    // 数据埋点上报
    rpc ReportMsg (ReportMsgReq) returns (Empty);
    // 请求 oid & epId 等待中的房间信息
    rpc WaitingRooms (BizIdReq) returns (WaitingRoomReply);
    // 激活房间（原有2min无操作会被判断为死房间）
    rpc ActiveRoom (RoomIdReq) returns (RoomInfo);
    // 查询广场页feed推荐season池
    rpc FeedRecHotSeasons (FeedRecSeasonsReq) returns (FeedRecHotSeasonsReply);
    // 查询一起看白名单
    rpc FreyaOidList (FreyaOidListReq) returns (FreyaOidListReply);
    // 判断该oid 是否存在
    rpc FreyaOidContains (FreyaContainsReq) returns (FreyaContainsReply);
    // 一起看 长连接系统广播消息通知
    rpc FreyaSystemSend (FreyaSystemSendReq) returns (Empty);
    // 一起看 长连接系统发送动作
    rpc FreyaSystemEvent (FreyaSystemSendReq) returns (Empty);
    // 一起看 系统触发房主变更, 只是房主变更，不会leave
    rpc SystemChangeOwner (RoomIdReq) returns (RoomIdReply);
    // 活动，触发发送活动卡片
    rpc ActivitySend (ActivityReq) returns (Empty);
    // 活动，领取状态校验
    rpc ActivityCheck (ActivityReq) returns (ActivityStatus);
    // 修改一起看用户侧扩展信息
    rpc UpdateUserDomain (UserExtReq) returns (Empty);
    // 获取一起看用户侧扩展信息
    rpc UserExtInfo (UserExtReq) returns (UserExtInfoReply);
    // 获取一起看活动相关信息
    rpc FreyaActInfo (ActivityReq) returns (ActivityInfoReply);
    // 获取一起看在线放映室
    rpc OnlineRoom (BizIdReq) returns (WaitingRoomReply);
    // 批量获取房间信息
    rpc RoomCards (RoomCardsReq) returns (RoomCardsReply);
    // 获取一起看配置 已废弃
    rpc FreyaConfig (FreyaCfgReq) returns (FreyaCfgReply) {
      option deprecated = true;
    };
    // 获取一起看实验相关信息
    rpc FreyaExperiment (FreyaExperimentReq) returns (FreyaExperimentReply);
    // 批量查询房间信息（无用户buvid校验等）
    rpc Infos (RoomIdsReq) returns (RoomInfosReply);
    // 更新匹配额外信息
    rpc UpdateMatchExt (UpdateMatchExtReq) returns (Empty);
    // 修改房间公告
    rpc ModifyNotice (NoticeReq) returns (Empty);
    // 获取roomInfo Json, 使用eg：详情页
    rpc InfoJson (InfoJsonReq) returns (InfoJsonReply);
    // 一起看通用活动信息
    rpc FreyaCommonActInfo (CommonActInfoReq) returns (CommonActInfoReply);
    // 首次进房提示
    rpc FirstTips (FirstTipsReq) returns (PullMessagesReply);
    // 获取一起看配置
    rpc FreyaConfigV2 (FreyaCfgReq) returns (FreyaCfgReply);
    // 猜你喜欢一起看特化卡
    rpc FreyaGuessCard (GuessCardReq) returns (GuessCardReply);
    // 获取双人房陪伴信息
    rpc FreyaAccompanyInfo(AccompanyInfoReq) returns (AccompanyInfoReply);
    // 删除放映室或点映的指定消息
    rpc RemoveMessage (ControlMsgReq) returns (ControlMsgReply);
    // 将放映室或点映的指定消息设为自见
    rpc SelfObservedMessage (ControlMsgReq) returns (ControlMsgReply);
    // 获得渲染好的分享卡
    rpc GetPointLiveShareCard (PointLiveShareCardReq) returns (PointLiveShareCardReply);
    // 点映房间超前点映态校验
    rpc LivePremiereAuth (LivePremiereAuthReq) returns (LivePremiereAuthReply);
    // 语音消息签名
    rpc FreyaVoiceUrlSign (FreyaVoiceUrlSignReq) returns (FreyaVoiceUrlSignReply);
    // 点映纯享模式拉取消息接口
    rpc PointLivePureModePullMsg (PointLivePureModeMsgReq) returns (PointLivePureModeMsgReply);
}