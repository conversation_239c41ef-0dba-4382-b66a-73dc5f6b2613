syntax = "proto3";

package pgc.service.freya.card;

option go_package = "buf.bilibili.co/bapis/bapis-gen/pgc/service.freya.card;api";
option java_package = "com.bapis.pgc.service.freya.card";
option java_multiple_files = true;

message FreyaCardInfo {
  // season_id
  int32 season_id = 1;
  // 标题
  string title = 2;
  // 封面
  string cover = 3;
  // 角标
  Badge badge_info = 4;
  // 副标题
  string subtitle = 5;
  // 评分信息
  RatingInfo rating = 6;
  // 年份类型地区文案 2019|国创|中国大陆
  string styles = 7;
  // index_show
  string index_show = 8;
  // 详情地址
  string url = 9;
  // 房间信息
  RoomInfo room_info = 10;
  // 正片分集
  repeated FreyaEpInfo eps = 11;
  // season付费状态
  int32 season_status = 12;
  // season类型  1：番剧，2：电影，3：纪录片，4：国漫，5：电视剧
  int32 season_type = 13;
  // season类型显示名  1：番剧，2：电影，3：纪录片，4：国漫，5：电视剧
  string season_type_name = 14;
  // 演员信息
  string actors = 15;
  // 计数统计
  StatsProto stat = 16;
  // 总集数
  int32 total_count = 17;
  // 是否完结 1完结 0未完结
  int32 is_finish = 18;
  // 是否开播 1开播 0未开播
  int32 is_started = 19;
  // 剧集模式 1单集 2多集
  int32 mode = 20;
  // 更新时间信息
  string renewal_time = 21;
  // 时长
  int32 duration = 22;
  // 含有怎样的非法ep 位1 含有非法付费类型ep 位2 含竖屏 位3 含弹幕关闭 位4 含评论关闭 位5 其他等
  int32 limit_attr = 23;
  // media标识位 见:https://info.bilibili.co/pages/viewpage.action?pageId=12862004
  int32 media_attr = 24;
  // 风格
  repeated MediaStyleProto media_styles = 25;
  // 年份 2020
  string year = 26;
  // 地区  中国/日本
  string area = 27;
  //season版本:正片tv 剧场版movie 其他other
  string season_version = 28;
}

// 分集信息
message FreyaEpInfo {
  // 短标题
  string index_title = 1;
  // 长标题
  string long_title = 2;
  // ep封面
  string cover = 3;
  // ep跳转地址
  string url = 4;
  // 角标
  Badge badge_info = 5;
  // epId
  int32 ep_id = 6;
  // ep付费状态
  int32 status = 7;
  // aid
  int64 aid = 8;
  // cid
  int64 cid = 9;
  // 视频尺寸对象
  DimensionProto dimension = 10;
  // ep 位运算特征值 https://info.bilibili.co/pages/viewpage.action?pageId=45331159
  int32 attr = 11;
  // 是否首播 0-否 1-是
  bool is_premiere = 12;
}

// 视频长宽信息
message DimensionProto {
  // 视频宽
  int32 width = 1;
  // 视频高
  int32 height = 2;
  // 视频是否允许旋转
  int32 rotate = 3;
}

message Badge {
  // 角标文案
  string text = 1;
  // 角标色值
  string bg_color = 2;
  // 角标色值-夜间模式
  string bg_color_night = 3;
}

// RatingProto 评分信息
message RatingInfo {
  // 评分数
  float score = 1;
  // 评分人数
  int32 count = 2;
}

// StatsProto 计数
message StatsProto {
  // 播放数
  int64 view = 1;
  // 追番数
  int64 follow = 2;
  // 系列追番数
  int64 series_follow = 3;
  //系列播放量
  int64 series_view = 4;
  // 追番文案
  string follow_view = 5;
}

// 房间信息
message RoomInfo {
  // 热度值
  int64 hot = 1;
  // 房间数
  int64 count = 2;
  //最近观看者
  repeated RecentWatcher recent_watchers = 3;
}

// 最近观看者
message RecentWatcher {
  // 头像
  string face = 1;
  // 名字
  string name = 2;
  // mid
  int64 mid = 3;
}


// MediaStyleProto .
message MediaStyleProto {
  // style id
  int32 style_id = 1;
  // type id
  int32 type_id = 2;
  // 风格名称
  string style_name = 3;
  // 是否隐藏
  int32 is_hidden = 4;
  // 排序字段
  int32 ord = 5;
  // 新styleId
  int32 new_id = 6;
}

// UserReq .
message UserReq {
  // 用户id
  int64 mid = 1;
  // 透传客户端参数，当是web时，为空
  string mobi_app = 2;
  // 透传客户端参数，当是web时，为空
  string device = 3;
  // 透传参数(web端如果没有传参数，请设置值为pc/web)
  string platform = 4;
  // build号
  int32 build = 5;
  // 设备号
  string buvid = 6;
  // 是否是大会员 0 不是 1 是
  int32 is_vip = 7;
}