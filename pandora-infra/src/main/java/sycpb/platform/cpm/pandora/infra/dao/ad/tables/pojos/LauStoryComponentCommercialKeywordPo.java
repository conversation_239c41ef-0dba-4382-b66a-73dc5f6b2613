/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad.tables.pojos;


import java.io.Serializable;
import java.sql.Timestamp;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LauStoryComponentCommercialKeywordPo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long      id;
    private Timestamp ctime;
    private Timestamp mtime;
    private Long      componentId;
    private String    keyword;

    public LauStoryComponentCommercialKeywordPo() {}

    public LauStoryComponentCommercialKeywordPo(LauStoryComponentCommercialKeywordPo value) {
        this.id = value.id;
        this.ctime = value.ctime;
        this.mtime = value.mtime;
        this.componentId = value.componentId;
        this.keyword = value.keyword;
    }

    public LauStoryComponentCommercialKeywordPo(
        Long      id,
        Timestamp ctime,
        Timestamp mtime,
        Long      componentId,
        String    keyword
    ) {
        this.id = id;
        this.ctime = ctime;
        this.mtime = mtime;
        this.componentId = componentId;
        this.keyword = keyword;
    }

    /**
     * Getter for <code>lau_story_component_commercial_keyword.id</code>. id
     */
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>lau_story_component_commercial_keyword.id</code>. id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * Getter for <code>lau_story_component_commercial_keyword.ctime</code>.
     * 创建时间
     */
    public Timestamp getCtime() {
        return this.ctime;
    }

    /**
     * Setter for <code>lau_story_component_commercial_keyword.ctime</code>.
     * 创建时间
     */
    public void setCtime(Timestamp ctime) {
        this.ctime = ctime;
    }

    /**
     * Getter for <code>lau_story_component_commercial_keyword.mtime</code>.
     * 更新时间
     */
    public Timestamp getMtime() {
        return this.mtime;
    }

    /**
     * Setter for <code>lau_story_component_commercial_keyword.mtime</code>.
     * 更新时间
     */
    public void setMtime(Timestamp mtime) {
        this.mtime = mtime;
    }

    /**
     * Getter for
     * <code>lau_story_component_commercial_keyword.component_id</code>.
     * story组件id
     */
    public Long getComponentId() {
        return this.componentId;
    }

    /**
     * Setter for
     * <code>lau_story_component_commercial_keyword.component_id</code>.
     * story组件id
     */
    public void setComponentId(Long componentId) {
        this.componentId = componentId;
    }

    /**
     * Getter for <code>lau_story_component_commercial_keyword.keyword</code>.
     * 卖点关键词
     */
    public String getKeyword() {
        return this.keyword;
    }

    /**
     * Setter for <code>lau_story_component_commercial_keyword.keyword</code>.
     * 卖点关键词
     */
    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("LauStoryComponentCommercialKeywordPo (");

        sb.append(id);
        sb.append(", ").append(ctime);
        sb.append(", ").append(mtime);
        sb.append(", ").append(componentId);
        sb.append(", ").append(keyword);

        sb.append(")");
        return sb.toString();
    }
}
