/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad.tables.records;


import java.sql.Timestamp;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record8;
import org.jooq.Row8;
import org.jooq.impl.UpdatableRecordImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad.tables.TLauCreativeMonitoring;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.pojos.LauCreativeMonitoringPo;


/**
 * 效果创意-监控链接表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LauCreativeMonitoringRecord extends UpdatableRecordImpl<LauCreativeMonitoringRecord> implements Record8<Integer, Integer, Long, String, Integer, Integer, Timestamp, Timestamp> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>lau_creative_monitoring.id</code>. id
     */
    public void setId(Integer value) {
        set(0, value);
    }

    /**
     * Getter for <code>lau_creative_monitoring.id</code>. id
     */
    public Integer getId() {
        return (Integer) get(0);
    }

    /**
     * Setter for <code>lau_creative_monitoring.unit_id</code>. 单元ID
     */
    public void setUnitId(Integer value) {
        set(1, value);
    }

    /**
     * Getter for <code>lau_creative_monitoring.unit_id</code>. 单元ID
     */
    public Integer getUnitId() {
        return (Integer) get(1);
    }

    /**
     * Setter for <code>lau_creative_monitoring.creative_id</code>. 创意ID
     */
    public void setCreativeId(Long value) {
        set(2, value);
    }

    /**
     * Getter for <code>lau_creative_monitoring.creative_id</code>. 创意ID
     */
    public Long getCreativeId() {
        return (Long) get(2);
    }

    /**
     * Setter for <code>lau_creative_monitoring.url</code>. 监控URL
     */
    public void setUrl(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>lau_creative_monitoring.url</code>. 监控URL
     */
    public String getUrl() {
        return (String) get(3);
    }

    /**
     * Setter for <code>lau_creative_monitoring.type</code>. 1-展示监控 2-点击监控
     * 3-播放0s监控 4-播放3s监控 5-播放5s监控 6-游戏点击
     */
    public void setType(Integer value) {
        set(4, value);
    }

    /**
     * Getter for <code>lau_creative_monitoring.type</code>. 1-展示监控 2-点击监控
     * 3-播放0s监控 4-播放3s监控 5-播放5s监控 6-游戏点击
     */
    public Integer getType() {
        return (Integer) get(4);
    }

    /**
     * Setter for <code>lau_creative_monitoring.is_deleted</code>. 是否删除0否 1是
     */
    public void setIsDeleted(Integer value) {
        set(5, value);
    }

    /**
     * Getter for <code>lau_creative_monitoring.is_deleted</code>. 是否删除0否 1是
     */
    public Integer getIsDeleted() {
        return (Integer) get(5);
    }

    /**
     * Setter for <code>lau_creative_monitoring.ctime</code>. 创建时间
     */
    public void setCtime(Timestamp value) {
        set(6, value);
    }

    /**
     * Getter for <code>lau_creative_monitoring.ctime</code>. 创建时间
     */
    public Timestamp getCtime() {
        return (Timestamp) get(6);
    }

    /**
     * Setter for <code>lau_creative_monitoring.mtime</code>. 更新时间
     */
    public void setMtime(Timestamp value) {
        set(7, value);
    }

    /**
     * Getter for <code>lau_creative_monitoring.mtime</code>. 更新时间
     */
    public Timestamp getMtime() {
        return (Timestamp) get(7);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Integer> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record8 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row8<Integer, Integer, Long, String, Integer, Integer, Timestamp, Timestamp> fieldsRow() {
        return (Row8) super.fieldsRow();
    }

    @Override
    public Row8<Integer, Integer, Long, String, Integer, Integer, Timestamp, Timestamp> valuesRow() {
        return (Row8) super.valuesRow();
    }

    @Override
    public Field<Integer> field1() {
        return TLauCreativeMonitoring.LAU_CREATIVE_MONITORING.ID;
    }

    @Override
    public Field<Integer> field2() {
        return TLauCreativeMonitoring.LAU_CREATIVE_MONITORING.UNIT_ID;
    }

    @Override
    public Field<Long> field3() {
        return TLauCreativeMonitoring.LAU_CREATIVE_MONITORING.CREATIVE_ID;
    }

    @Override
    public Field<String> field4() {
        return TLauCreativeMonitoring.LAU_CREATIVE_MONITORING.URL;
    }

    @Override
    public Field<Integer> field5() {
        return TLauCreativeMonitoring.LAU_CREATIVE_MONITORING.TYPE;
    }

    @Override
    public Field<Integer> field6() {
        return TLauCreativeMonitoring.LAU_CREATIVE_MONITORING.IS_DELETED;
    }

    @Override
    public Field<Timestamp> field7() {
        return TLauCreativeMonitoring.LAU_CREATIVE_MONITORING.CTIME;
    }

    @Override
    public Field<Timestamp> field8() {
        return TLauCreativeMonitoring.LAU_CREATIVE_MONITORING.MTIME;
    }

    @Override
    public Integer component1() {
        return getId();
    }

    @Override
    public Integer component2() {
        return getUnitId();
    }

    @Override
    public Long component3() {
        return getCreativeId();
    }

    @Override
    public String component4() {
        return getUrl();
    }

    @Override
    public Integer component5() {
        return getType();
    }

    @Override
    public Integer component6() {
        return getIsDeleted();
    }

    @Override
    public Timestamp component7() {
        return getCtime();
    }

    @Override
    public Timestamp component8() {
        return getMtime();
    }

    @Override
    public Integer value1() {
        return getId();
    }

    @Override
    public Integer value2() {
        return getUnitId();
    }

    @Override
    public Long value3() {
        return getCreativeId();
    }

    @Override
    public String value4() {
        return getUrl();
    }

    @Override
    public Integer value5() {
        return getType();
    }

    @Override
    public Integer value6() {
        return getIsDeleted();
    }

    @Override
    public Timestamp value7() {
        return getCtime();
    }

    @Override
    public Timestamp value8() {
        return getMtime();
    }

    @Override
    public LauCreativeMonitoringRecord value1(Integer value) {
        setId(value);
        return this;
    }

    @Override
    public LauCreativeMonitoringRecord value2(Integer value) {
        setUnitId(value);
        return this;
    }

    @Override
    public LauCreativeMonitoringRecord value3(Long value) {
        setCreativeId(value);
        return this;
    }

    @Override
    public LauCreativeMonitoringRecord value4(String value) {
        setUrl(value);
        return this;
    }

    @Override
    public LauCreativeMonitoringRecord value5(Integer value) {
        setType(value);
        return this;
    }

    @Override
    public LauCreativeMonitoringRecord value6(Integer value) {
        setIsDeleted(value);
        return this;
    }

    @Override
    public LauCreativeMonitoringRecord value7(Timestamp value) {
        setCtime(value);
        return this;
    }

    @Override
    public LauCreativeMonitoringRecord value8(Timestamp value) {
        setMtime(value);
        return this;
    }

    @Override
    public LauCreativeMonitoringRecord values(Integer value1, Integer value2, Long value3, String value4, Integer value5, Integer value6, Timestamp value7, Timestamp value8) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached LauCreativeMonitoringRecord
     */
    public LauCreativeMonitoringRecord() {
        super(TLauCreativeMonitoring.LAU_CREATIVE_MONITORING);
    }

    /**
     * Create a detached, initialised LauCreativeMonitoringRecord
     */
    public LauCreativeMonitoringRecord(Integer id, Integer unitId, Long creativeId, String url, Integer type, Integer isDeleted, Timestamp ctime, Timestamp mtime) {
        super(TLauCreativeMonitoring.LAU_CREATIVE_MONITORING);

        setId(id);
        setUnitId(unitId);
        setCreativeId(creativeId);
        setUrl(url);
        setType(type);
        setIsDeleted(isDeleted);
        setCtime(ctime);
        setMtime(mtime);
    }

    /**
     * Create a detached, initialised LauCreativeMonitoringRecord
     */
    public LauCreativeMonitoringRecord(LauCreativeMonitoringPo value) {
        super(TLauCreativeMonitoring.LAU_CREATIVE_MONITORING);

        if (value != null) {
            setId(value.getId());
            setUnitId(value.getUnitId());
            setCreativeId(value.getCreativeId());
            setUrl(value.getUrl());
            setType(value.getType());
            setIsDeleted(value.getIsDeleted());
            setCtime(value.getCtime());
            setMtime(value.getMtime());
        }
    }
}
