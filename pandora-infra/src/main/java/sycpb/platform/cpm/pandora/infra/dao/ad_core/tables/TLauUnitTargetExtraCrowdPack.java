/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables;


import java.sql.Timestamp;

import org.jooq.Field;
import org.jooq.Identity;
import org.jooq.Name;
import org.jooq.Row6;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.Internal;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad_core.DefaultSchema;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.records.LauUnitTargetExtraCrowdPackRecord;


/**
 * 单元-扩展种子人群包表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class TLauUnitTargetExtraCrowdPack extends TableImpl<LauUnitTargetExtraCrowdPackRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>lau_unit_target_extra_crowd_pack</code>
     */
    public static final TLauUnitTargetExtraCrowdPack LAU_UNIT_TARGET_EXTRA_CROWD_PACK = new TLauUnitTargetExtraCrowdPack();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<LauUnitTargetExtraCrowdPackRecord> getRecordType() {
        return LauUnitTargetExtraCrowdPackRecord.class;
    }

    /**
     * The column <code>lau_unit_target_extra_crowd_pack.id</code>.
     */
    public final TableField<LauUnitTargetExtraCrowdPackRecord, Integer> ID = createField(DSL.name("id"), SQLDataType.INTEGER.nullable(false).identity(true), this, "");

    /**
     * The column <code>lau_unit_target_extra_crowd_pack.unit_id</code>. 单元ID
     */
    public final TableField<LauUnitTargetExtraCrowdPackRecord, Integer> UNIT_ID = createField(DSL.name("unit_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "单元ID");

    /**
     * The column <code>lau_unit_target_extra_crowd_pack.crowd_pack_id</code>.
     * 人群包ID(来自DMP)
     */
    public final TableField<LauUnitTargetExtraCrowdPackRecord, Integer> CROWD_PACK_ID = createField(DSL.name("crowd_pack_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "人群包ID(来自DMP)");

    /**
     * The column <code>lau_unit_target_extra_crowd_pack.ctime</code>. 创建时间
     */
    public final TableField<LauUnitTargetExtraCrowdPackRecord, Timestamp> CTIME = createField(DSL.name("ctime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "创建时间");

    /**
     * The column <code>lau_unit_target_extra_crowd_pack.mtime</code>. 修改时间
     */
    public final TableField<LauUnitTargetExtraCrowdPackRecord, Timestamp> MTIME = createField(DSL.name("mtime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "修改时间");

    /**
     * The column <code>lau_unit_target_extra_crowd_pack.is_deleted</code>. 软删除
     * 0-有效，1-删除
     */
    public final TableField<LauUnitTargetExtraCrowdPackRecord, Integer> IS_DELETED = createField(DSL.name("is_deleted"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "软删除 0-有效，1-删除");

    private TLauUnitTargetExtraCrowdPack(Name alias, Table<LauUnitTargetExtraCrowdPackRecord> aliased) {
        this(alias, aliased, null);
    }

    private TLauUnitTargetExtraCrowdPack(Name alias, Table<LauUnitTargetExtraCrowdPackRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment("单元-扩展种子人群包表"), TableOptions.table());
    }

    /**
     * Create an aliased <code>lau_unit_target_extra_crowd_pack</code> table
     * reference
     */
    public TLauUnitTargetExtraCrowdPack(String alias) {
        this(DSL.name(alias), LAU_UNIT_TARGET_EXTRA_CROWD_PACK);
    }

    /**
     * Create an aliased <code>lau_unit_target_extra_crowd_pack</code> table
     * reference
     */
    public TLauUnitTargetExtraCrowdPack(Name alias) {
        this(alias, LAU_UNIT_TARGET_EXTRA_CROWD_PACK);
    }

    /**
     * Create a <code>lau_unit_target_extra_crowd_pack</code> table reference
     */
    public TLauUnitTargetExtraCrowdPack() {
        this(DSL.name("lau_unit_target_extra_crowd_pack"), null);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : DefaultSchema.DEFAULT_SCHEMA;
    }

    @Override
    public Identity<LauUnitTargetExtraCrowdPackRecord, Integer> getIdentity() {
        return (Identity<LauUnitTargetExtraCrowdPackRecord, Integer>) super.getIdentity();
    }

    @Override
    public UniqueKey<LauUnitTargetExtraCrowdPackRecord> getPrimaryKey() {
        return Internal.createUniqueKey(TLauUnitTargetExtraCrowdPack.LAU_UNIT_TARGET_EXTRA_CROWD_PACK, DSL.name("KEY_lau_unit_target_extra_crowd_pack_PRIMARY"), new TableField[] { TLauUnitTargetExtraCrowdPack.LAU_UNIT_TARGET_EXTRA_CROWD_PACK.ID }, true);
    }

    @Override
    public TLauUnitTargetExtraCrowdPack as(String alias) {
        return new TLauUnitTargetExtraCrowdPack(DSL.name(alias), this);
    }

    @Override
    public TLauUnitTargetExtraCrowdPack as(Name alias) {
        return new TLauUnitTargetExtraCrowdPack(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public TLauUnitTargetExtraCrowdPack rename(String name) {
        return new TLauUnitTargetExtraCrowdPack(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public TLauUnitTargetExtraCrowdPack rename(Name name) {
        return new TLauUnitTargetExtraCrowdPack(name, null);
    }

    // -------------------------------------------------------------------------
    // Row6 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row6<Integer, Integer, Integer, Timestamp, Timestamp, Integer> fieldsRow() {
        return (Row6) super.fieldsRow();
    }
}
