/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.daos;


import java.sql.Timestamp;
import java.util.List;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauCampaign;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauCampaignPo;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.records.LauCampaignRecord;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
@Repository
public class LauCampaignDao extends DAOImpl<LauCampaignRecord, LauCampaignPo, Integer> {

    /**
     * Create a new LauCampaignDao without any configuration
     */
    public LauCampaignDao() {
        super(TLauCampaign.LAU_CAMPAIGN, LauCampaignPo.class);
    }

    /**
     * Create a new LauCampaignDao with an attached configuration
     */
    @Autowired
    public LauCampaignDao(Configuration configuration) {
        super(TLauCampaign.LAU_CAMPAIGN, LauCampaignPo.class, configuration);
    }

    @Override
    public Integer getId(LauCampaignPo object) {
        return object.getCampaignId();
    }

    /**
     * Fetch records that have <code>campaign_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCampaignPo> fetchRangeOfCampaignId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCampaign.LAU_CAMPAIGN.CAMPAIGN_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>campaign_id IN (values)</code>
     */
    public List<LauCampaignPo> fetchByCampaignId(Integer... values) {
        return fetch(TLauCampaign.LAU_CAMPAIGN.CAMPAIGN_ID, values);
    }

    /**
     * Fetch a unique record that has <code>campaign_id = value</code>
     */
    public LauCampaignPo fetchOneByCampaignId(Integer value) {
        return fetchOne(TLauCampaign.LAU_CAMPAIGN.CAMPAIGN_ID, value);
    }

    /**
     * Fetch records that have <code>account_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCampaignPo> fetchRangeOfAccountId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCampaign.LAU_CAMPAIGN.ACCOUNT_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>account_id IN (values)</code>
     */
    public List<LauCampaignPo> fetchByAccountId(Integer... values) {
        return fetch(TLauCampaign.LAU_CAMPAIGN.ACCOUNT_ID, values);
    }

    /**
     * Fetch records that have <code>campaign_name BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCampaignPo> fetchRangeOfCampaignName(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauCampaign.LAU_CAMPAIGN.CAMPAIGN_NAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>campaign_name IN (values)</code>
     */
    public List<LauCampaignPo> fetchByCampaignName(String... values) {
        return fetch(TLauCampaign.LAU_CAMPAIGN.CAMPAIGN_NAME, values);
    }

    /**
     * Fetch records that have <code>cost_type BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCampaignPo> fetchRangeOfCostType(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCampaign.LAU_CAMPAIGN.COST_TYPE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>cost_type IN (values)</code>
     */
    public List<LauCampaignPo> fetchByCostType(Integer... values) {
        return fetch(TLauCampaign.LAU_CAMPAIGN.COST_TYPE, values);
    }

    /**
     * Fetch records that have <code>budget_type BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCampaignPo> fetchRangeOfBudgetType(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCampaign.LAU_CAMPAIGN.BUDGET_TYPE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>budget_type IN (values)</code>
     */
    public List<LauCampaignPo> fetchByBudgetType(Integer... values) {
        return fetch(TLauCampaign.LAU_CAMPAIGN.BUDGET_TYPE, values);
    }

    /**
     * Fetch records that have <code>budget BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCampaignPo> fetchRangeOfBudget(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(TLauCampaign.LAU_CAMPAIGN.BUDGET, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>budget IN (values)</code>
     */
    public List<LauCampaignPo> fetchByBudget(Long... values) {
        return fetch(TLauCampaign.LAU_CAMPAIGN.BUDGET, values);
    }

    /**
     * Fetch records that have <code>speed_mode BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCampaignPo> fetchRangeOfSpeedMode(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCampaign.LAU_CAMPAIGN.SPEED_MODE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>speed_mode IN (values)</code>
     */
    public List<LauCampaignPo> fetchBySpeedMode(Integer... values) {
        return fetch(TLauCampaign.LAU_CAMPAIGN.SPEED_MODE, values);
    }

    /**
     * Fetch records that have <code>status BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCampaignPo> fetchRangeOfStatus(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCampaign.LAU_CAMPAIGN.STATUS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>status IN (values)</code>
     */
    public List<LauCampaignPo> fetchByStatus(Integer... values) {
        return fetch(TLauCampaign.LAU_CAMPAIGN.STATUS, values);
    }

    /**
     * Fetch records that have <code>is_deleted BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCampaignPo> fetchRangeOfIsDeleted(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCampaign.LAU_CAMPAIGN.IS_DELETED, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_deleted IN (values)</code>
     */
    public List<LauCampaignPo> fetchByIsDeleted(Integer... values) {
        return fetch(TLauCampaign.LAU_CAMPAIGN.IS_DELETED, values);
    }

    /**
     * Fetch records that have <code>ctime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCampaignPo> fetchRangeOfCtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TLauCampaign.LAU_CAMPAIGN.CTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>ctime IN (values)</code>
     */
    public List<LauCampaignPo> fetchByCtime(Timestamp... values) {
        return fetch(TLauCampaign.LAU_CAMPAIGN.CTIME, values);
    }

    /**
     * Fetch records that have <code>mtime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCampaignPo> fetchRangeOfMtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TLauCampaign.LAU_CAMPAIGN.MTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>mtime IN (values)</code>
     */
    public List<LauCampaignPo> fetchByMtime(Timestamp... values) {
        return fetch(TLauCampaign.LAU_CAMPAIGN.MTIME, values);
    }

    /**
     * Fetch records that have <code>sales_type BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCampaignPo> fetchRangeOfSalesType(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCampaign.LAU_CAMPAIGN.SALES_TYPE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>sales_type IN (values)</code>
     */
    public List<LauCampaignPo> fetchBySalesType(Integer... values) {
        return fetch(TLauCampaign.LAU_CAMPAIGN.SALES_TYPE, values);
    }

    /**
     * Fetch records that have <code>order_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCampaignPo> fetchRangeOfOrderId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCampaign.LAU_CAMPAIGN.ORDER_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>order_id IN (values)</code>
     */
    public List<LauCampaignPo> fetchByOrderId(Integer... values) {
        return fetch(TLauCampaign.LAU_CAMPAIGN.ORDER_ID, values);
    }

    /**
     * Fetch records that have <code>campaign_status BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCampaignPo> fetchRangeOfCampaignStatus(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCampaign.LAU_CAMPAIGN.CAMPAIGN_STATUS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>campaign_status IN (values)</code>
     */
    public List<LauCampaignPo> fetchByCampaignStatus(Integer... values) {
        return fetch(TLauCampaign.LAU_CAMPAIGN.CAMPAIGN_STATUS, values);
    }

    /**
     * Fetch records that have <code>campaign_status_mtime BETWEEN
     * lowerInclusive AND upperInclusive</code>
     */
    public List<LauCampaignPo> fetchRangeOfCampaignStatusMtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TLauCampaign.LAU_CAMPAIGN.CAMPAIGN_STATUS_MTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>campaign_status_mtime IN (values)</code>
     */
    public List<LauCampaignPo> fetchByCampaignStatusMtime(Timestamp... values) {
        return fetch(TLauCampaign.LAU_CAMPAIGN.CAMPAIGN_STATUS_MTIME, values);
    }

    /**
     * Fetch records that have <code>promotion_purpose_type BETWEEN
     * lowerInclusive AND upperInclusive</code>
     */
    public List<LauCampaignPo> fetchRangeOfPromotionPurposeType(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCampaign.LAU_CAMPAIGN.PROMOTION_PURPOSE_TYPE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>promotion_purpose_type IN (values)</code>
     */
    public List<LauCampaignPo> fetchByPromotionPurposeType(Integer... values) {
        return fetch(TLauCampaign.LAU_CAMPAIGN.PROMOTION_PURPOSE_TYPE, values);
    }

    /**
     * Fetch records that have <code>origin_tag BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCampaignPo> fetchRangeOfOriginTag(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCampaign.LAU_CAMPAIGN.ORIGIN_TAG, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>origin_tag IN (values)</code>
     */
    public List<LauCampaignPo> fetchByOriginTag(Integer... values) {
        return fetch(TLauCampaign.LAU_CAMPAIGN.ORIGIN_TAG, values);
    }

    /**
     * Fetch records that have <code>lau_account_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCampaignPo> fetchRangeOfLauAccountId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCampaign.LAU_CAMPAIGN.LAU_ACCOUNT_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>lau_account_id IN (values)</code>
     */
    public List<LauCampaignPo> fetchByLauAccountId(Integer... values) {
        return fetch(TLauCampaign.LAU_CAMPAIGN.LAU_ACCOUNT_ID, values);
    }

    /**
     * Fetch records that have <code>need_wake_app BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCampaignPo> fetchRangeOfNeedWakeApp(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCampaign.LAU_CAMPAIGN.NEED_WAKE_APP, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>need_wake_app IN (values)</code>
     */
    public List<LauCampaignPo> fetchByNeedWakeApp(Integer... values) {
        return fetch(TLauCampaign.LAU_CAMPAIGN.NEED_WAKE_APP, values);
    }

    /**
     * Fetch records that have <code>adp_version BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCampaignPo> fetchRangeOfAdpVersion(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCampaign.LAU_CAMPAIGN.ADP_VERSION, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>adp_version IN (values)</code>
     */
    public List<LauCampaignPo> fetchByAdpVersion(Integer... values) {
        return fetch(TLauCampaign.LAU_CAMPAIGN.ADP_VERSION, values);
    }

    /**
     * Fetch records that have <code>is_new_fly BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCampaignPo> fetchRangeOfIsNewFly(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCampaign.LAU_CAMPAIGN.IS_NEW_FLY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_new_fly IN (values)</code>
     */
    public List<LauCampaignPo> fetchByIsNewFly(Integer... values) {
        return fetch(TLauCampaign.LAU_CAMPAIGN.IS_NEW_FLY, values);
    }

    /**
     * Fetch records that have <code>budget_limit_type BETWEEN lowerInclusive
     * AND upperInclusive</code>
     */
    public List<LauCampaignPo> fetchRangeOfBudgetLimitType(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCampaign.LAU_CAMPAIGN.BUDGET_LIMIT_TYPE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>budget_limit_type IN (values)</code>
     */
    public List<LauCampaignPo> fetchByBudgetLimitType(Integer... values) {
        return fetch(TLauCampaign.LAU_CAMPAIGN.BUDGET_LIMIT_TYPE, values);
    }

    /**
     * Fetch records that have <code>flag BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCampaignPo> fetchRangeOfFlag(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCampaign.LAU_CAMPAIGN.FLAG, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>flag IN (values)</code>
     */
    public List<LauCampaignPo> fetchByFlag(Integer... values) {
        return fetch(TLauCampaign.LAU_CAMPAIGN.FLAG, values);
    }

    /**
     * Fetch records that have <code>deduction_sign BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCampaignPo> fetchRangeOfDeductionSign(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCampaign.LAU_CAMPAIGN.DEDUCTION_SIGN, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>deduction_sign IN (values)</code>
     */
    public List<LauCampaignPo> fetchByDeductionSign(Integer... values) {
        return fetch(TLauCampaign.LAU_CAMPAIGN.DEDUCTION_SIGN, values);
    }

    /**
     * Fetch records that have <code>is_managed BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCampaignPo> fetchRangeOfIsManaged(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCampaign.LAU_CAMPAIGN.IS_MANAGED, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_managed IN (values)</code>
     */
    public List<LauCampaignPo> fetchByIsManaged(Integer... values) {
        return fetch(TLauCampaign.LAU_CAMPAIGN.IS_MANAGED, values);
    }

    /**
     * Fetch records that have <code>managed_begin_time BETWEEN lowerInclusive
     * AND upperInclusive</code>
     */
    public List<LauCampaignPo> fetchRangeOfManagedBeginTime(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauCampaign.LAU_CAMPAIGN.MANAGED_BEGIN_TIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>managed_begin_time IN (values)</code>
     */
    public List<LauCampaignPo> fetchByManagedBeginTime(String... values) {
        return fetch(TLauCampaign.LAU_CAMPAIGN.MANAGED_BEGIN_TIME, values);
    }

    /**
     * Fetch records that have <code>managed_end_time BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCampaignPo> fetchRangeOfManagedEndTime(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauCampaign.LAU_CAMPAIGN.MANAGED_END_TIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>managed_end_time IN (values)</code>
     */
    public List<LauCampaignPo> fetchByManagedEndTime(String... values) {
        return fetch(TLauCampaign.LAU_CAMPAIGN.MANAGED_END_TIME, values);
    }

    /**
     * Fetch records that have <code>is_gd_plus BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCampaignPo> fetchRangeOfIsGdPlus(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCampaign.LAU_CAMPAIGN.IS_GD_PLUS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_gd_plus IN (values)</code>
     */
    public List<LauCampaignPo> fetchByIsGdPlus(Integer... values) {
        return fetch(TLauCampaign.LAU_CAMPAIGN.IS_GD_PLUS, values);
    }

    /**
     * Fetch records that have <code>action_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCampaignPo> fetchRangeOfActionId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCampaign.LAU_CAMPAIGN.ACTION_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>action_id IN (values)</code>
     */
    public List<LauCampaignPo> fetchByActionId(Integer... values) {
        return fetch(TLauCampaign.LAU_CAMPAIGN.ACTION_ID, values);
    }

    /**
     * Fetch records that have <code>crm_order_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCampaignPo> fetchRangeOfCrmOrderId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCampaign.LAU_CAMPAIGN.CRM_ORDER_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>crm_order_id IN (values)</code>
     */
    public List<LauCampaignPo> fetchByCrmOrderId(Integer... values) {
        return fetch(TLauCampaign.LAU_CAMPAIGN.CRM_ORDER_ID, values);
    }

    /**
     * Fetch records that have <code>is_middle_ad BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCampaignPo> fetchRangeOfIsMiddleAd(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCampaign.LAU_CAMPAIGN.IS_MIDDLE_AD, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_middle_ad IN (values)</code>
     */
    public List<LauCampaignPo> fetchByIsMiddleAd(Integer... values) {
        return fetch(TLauCampaign.LAU_CAMPAIGN.IS_MIDDLE_AD, values);
    }

    /**
     * Fetch records that have <code>ad_type BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCampaignPo> fetchRangeOfAdType(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCampaign.LAU_CAMPAIGN.AD_TYPE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>ad_type IN (values)</code>
     */
    public List<LauCampaignPo> fetchByAdType(Integer... values) {
        return fetch(TLauCampaign.LAU_CAMPAIGN.AD_TYPE, values);
    }

    /**
     * Fetch records that have <code>support_auto BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCampaignPo> fetchRangeOfSupportAuto(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCampaign.LAU_CAMPAIGN.SUPPORT_AUTO, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>support_auto IN (values)</code>
     */
    public List<LauCampaignPo> fetchBySupportAuto(Integer... values) {
        return fetch(TLauCampaign.LAU_CAMPAIGN.SUPPORT_AUTO, values);
    }
}
