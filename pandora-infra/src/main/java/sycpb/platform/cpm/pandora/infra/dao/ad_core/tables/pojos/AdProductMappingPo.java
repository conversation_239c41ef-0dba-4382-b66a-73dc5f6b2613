/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos;


import java.io.Serializable;
import java.sql.Timestamp;


/**
 * 投放产品基本信息
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class AdProductMappingPo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long      id;
    private Long      adProductId;
    private Integer   mappingId;
    private Integer   type;
    private Integer   isDeleted;
    private Timestamp ctime;
    private Timestamp mtime;
    private Integer   belongType;

    public AdProductMappingPo() {}

    public AdProductMappingPo(AdProductMappingPo value) {
        this.id = value.id;
        this.adProductId = value.adProductId;
        this.mappingId = value.mappingId;
        this.type = value.type;
        this.isDeleted = value.isDeleted;
        this.ctime = value.ctime;
        this.mtime = value.mtime;
        this.belongType = value.belongType;
    }

    public AdProductMappingPo(
        Long      id,
        Long      adProductId,
        Integer   mappingId,
        Integer   type,
        Integer   isDeleted,
        Timestamp ctime,
        Timestamp mtime,
        Integer   belongType
    ) {
        this.id = id;
        this.adProductId = adProductId;
        this.mappingId = mappingId;
        this.type = type;
        this.isDeleted = isDeleted;
        this.ctime = ctime;
        this.mtime = mtime;
        this.belongType = belongType;
    }

    /**
     * Getter for <code>ad_product_mapping.id</code>. ID
     */
    public Long getId() {
        return this.id;
    }

    /**
     * Setter for <code>ad_product_mapping.id</code>. ID
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * Getter for <code>ad_product_mapping.ad_product_id</code>. 产品ID
     */
    public Long getAdProductId() {
        return this.adProductId;
    }

    /**
     * Setter for <code>ad_product_mapping.ad_product_id</code>. 产品ID
     */
    public void setAdProductId(Long adProductId) {
        this.adProductId = adProductId;
    }

    /**
     * Getter for <code>ad_product_mapping.mapping_id</code>. mappingId 账户id,
     * 单元id
     */
    public Integer getMappingId() {
        return this.mappingId;
    }

    /**
     * Setter for <code>ad_product_mapping.mapping_id</code>. mappingId 账户id,
     * 单元id
     */
    public void setMappingId(Integer mappingId) {
        this.mappingId = mappingId;
    }

    /**
     * Getter for <code>ad_product_mapping.type</code>. 关联类型 1-账户 2-单元
     */
    public Integer getType() {
        return this.type;
    }

    /**
     * Setter for <code>ad_product_mapping.type</code>. 关联类型 1-账户 2-单元
     */
    public void setType(Integer type) {
        this.type = type;
    }

    /**
     * Getter for <code>ad_product_mapping.is_deleted</code>. 软删除，0是有效，1是删除
     */
    public Integer getIsDeleted() {
        return this.isDeleted;
    }

    /**
     * Setter for <code>ad_product_mapping.is_deleted</code>. 软删除，0是有效，1是删除
     */
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    /**
     * Getter for <code>ad_product_mapping.ctime</code>. 添加时间
     */
    public Timestamp getCtime() {
        return this.ctime;
    }

    /**
     * Setter for <code>ad_product_mapping.ctime</code>. 添加时间
     */
    public void setCtime(Timestamp ctime) {
        this.ctime = ctime;
    }

    /**
     * Getter for <code>ad_product_mapping.mtime</code>. 变更时间
     */
    public Timestamp getMtime() {
        return this.mtime;
    }

    /**
     * Setter for <code>ad_product_mapping.mtime</code>. 变更时间
     */
    public void setMtime(Timestamp mtime) {
        this.mtime = mtime;
    }

    /**
     * Getter for <code>ad_product_mapping.belong_type</code>. 数据来源 1-用户录入
     * 2-人工标注
     */
    public Integer getBelongType() {
        return this.belongType;
    }

    /**
     * Setter for <code>ad_product_mapping.belong_type</code>. 数据来源 1-用户录入
     * 2-人工标注
     */
    public void setBelongType(Integer belongType) {
        this.belongType = belongType;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("AdProductMappingPo (");

        sb.append(id);
        sb.append(", ").append(adProductId);
        sb.append(", ").append(mappingId);
        sb.append(", ").append(type);
        sb.append(", ").append(isDeleted);
        sb.append(", ").append(ctime);
        sb.append(", ").append(mtime);
        sb.append(", ").append(belongType);

        sb.append(")");
        return sb.toString();
    }
}
