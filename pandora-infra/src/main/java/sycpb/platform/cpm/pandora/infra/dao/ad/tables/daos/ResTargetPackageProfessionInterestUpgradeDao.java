/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad.tables.daos;


import java.sql.Timestamp;
import java.util.List;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import sycpb.platform.cpm.pandora.infra.dao.ad.tables.TResTargetPackageProfessionInterestUpgrade;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.pojos.ResTargetPackageProfessionInterestUpgradePo;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.records.ResTargetPackageProfessionInterestUpgradeRecord;


/**
 * 新版定向包行业兴趣人群定向表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
@Repository
public class ResTargetPackageProfessionInterestUpgradeDao extends DAOImpl<ResTargetPackageProfessionInterestUpgradeRecord, ResTargetPackageProfessionInterestUpgradePo, Integer> {

    /**
     * Create a new ResTargetPackageProfessionInterestUpgradeDao without any
     * configuration
     */
    public ResTargetPackageProfessionInterestUpgradeDao() {
        super(TResTargetPackageProfessionInterestUpgrade.RES_TARGET_PACKAGE_PROFESSION_INTEREST_UPGRADE, ResTargetPackageProfessionInterestUpgradePo.class);
    }

    /**
     * Create a new ResTargetPackageProfessionInterestUpgradeDao with an
     * attached configuration
     */
    @Autowired
    public ResTargetPackageProfessionInterestUpgradeDao(Configuration configuration) {
        super(TResTargetPackageProfessionInterestUpgrade.RES_TARGET_PACKAGE_PROFESSION_INTEREST_UPGRADE, ResTargetPackageProfessionInterestUpgradePo.class, configuration);
    }

    @Override
    public Integer getId(ResTargetPackageProfessionInterestUpgradePo object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<ResTargetPackageProfessionInterestUpgradePo> fetchRangeOfId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TResTargetPackageProfessionInterestUpgrade.RES_TARGET_PACKAGE_PROFESSION_INTEREST_UPGRADE.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<ResTargetPackageProfessionInterestUpgradePo> fetchById(Integer... values) {
        return fetch(TResTargetPackageProfessionInterestUpgrade.RES_TARGET_PACKAGE_PROFESSION_INTEREST_UPGRADE.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public ResTargetPackageProfessionInterestUpgradePo fetchOneById(Integer value) {
        return fetchOne(TResTargetPackageProfessionInterestUpgrade.RES_TARGET_PACKAGE_PROFESSION_INTEREST_UPGRADE.ID, value);
    }

    /**
     * Fetch records that have <code>target_package_id BETWEEN lowerInclusive
     * AND upperInclusive</code>
     */
    public List<ResTargetPackageProfessionInterestUpgradePo> fetchRangeOfTargetPackageId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TResTargetPackageProfessionInterestUpgrade.RES_TARGET_PACKAGE_PROFESSION_INTEREST_UPGRADE.TARGET_PACKAGE_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>target_package_id IN (values)</code>
     */
    public List<ResTargetPackageProfessionInterestUpgradePo> fetchByTargetPackageId(Integer... values) {
        return fetch(TResTargetPackageProfessionInterestUpgrade.RES_TARGET_PACKAGE_PROFESSION_INTEREST_UPGRADE.TARGET_PACKAGE_ID, values);
    }

    /**
     * Fetch records that have <code>crowd_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<ResTargetPackageProfessionInterestUpgradePo> fetchRangeOfCrowdId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TResTargetPackageProfessionInterestUpgrade.RES_TARGET_PACKAGE_PROFESSION_INTEREST_UPGRADE.CROWD_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>crowd_id IN (values)</code>
     */
    public List<ResTargetPackageProfessionInterestUpgradePo> fetchByCrowdId(Integer... values) {
        return fetch(TResTargetPackageProfessionInterestUpgrade.RES_TARGET_PACKAGE_PROFESSION_INTEREST_UPGRADE.CROWD_ID, values);
    }

    /**
     * Fetch records that have <code>ctime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<ResTargetPackageProfessionInterestUpgradePo> fetchRangeOfCtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TResTargetPackageProfessionInterestUpgrade.RES_TARGET_PACKAGE_PROFESSION_INTEREST_UPGRADE.CTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>ctime IN (values)</code>
     */
    public List<ResTargetPackageProfessionInterestUpgradePo> fetchByCtime(Timestamp... values) {
        return fetch(TResTargetPackageProfessionInterestUpgrade.RES_TARGET_PACKAGE_PROFESSION_INTEREST_UPGRADE.CTIME, values);
    }

    /**
     * Fetch records that have <code>mtime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<ResTargetPackageProfessionInterestUpgradePo> fetchRangeOfMtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TResTargetPackageProfessionInterestUpgrade.RES_TARGET_PACKAGE_PROFESSION_INTEREST_UPGRADE.MTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>mtime IN (values)</code>
     */
    public List<ResTargetPackageProfessionInterestUpgradePo> fetchByMtime(Timestamp... values) {
        return fetch(TResTargetPackageProfessionInterestUpgrade.RES_TARGET_PACKAGE_PROFESSION_INTEREST_UPGRADE.MTIME, values);
    }

    /**
     * Fetch records that have <code>is_deleted BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<ResTargetPackageProfessionInterestUpgradePo> fetchRangeOfIsDeleted(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TResTargetPackageProfessionInterestUpgrade.RES_TARGET_PACKAGE_PROFESSION_INTEREST_UPGRADE.IS_DELETED, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_deleted IN (values)</code>
     */
    public List<ResTargetPackageProfessionInterestUpgradePo> fetchByIsDeleted(Integer... values) {
        return fetch(TResTargetPackageProfessionInterestUpgrade.RES_TARGET_PACKAGE_PROFESSION_INTEREST_UPGRADE.IS_DELETED, values);
    }
}
