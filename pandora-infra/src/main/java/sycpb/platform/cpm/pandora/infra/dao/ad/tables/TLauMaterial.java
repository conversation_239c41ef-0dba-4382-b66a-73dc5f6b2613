/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad.tables;


import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import org.jooq.Field;
import org.jooq.Identity;
import org.jooq.Name;
import org.jooq.Row6;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.Internal;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad.DefaultSchema;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.records.LauMaterialRecord;


/**
 * 投放物料表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class TLauMaterial extends TableImpl<LauMaterialRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>lau_material</code>
     */
    public static final TLauMaterial LAU_MATERIAL = new TLauMaterial();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<LauMaterialRecord> getRecordType() {
        return LauMaterialRecord.class;
    }

    /**
     * The column <code>lau_material.id</code>. 自增id
     */
    public final TableField<LauMaterialRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false).identity(true), this, "自增id");

    /**
     * The column <code>lau_material.ctime</code>. 添加时间
     */
    public final TableField<LauMaterialRecord, Timestamp> CTIME = createField(DSL.name("ctime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "添加时间");

    /**
     * The column <code>lau_material.mtime</code>. 修改时间
     */
    public final TableField<LauMaterialRecord, Timestamp> MTIME = createField(DSL.name("mtime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "修改时间");

    /**
     * The column <code>lau_material.material_md5</code>. 物料内容的MD5值, 用来判断物料是否相同
     */
    public final TableField<LauMaterialRecord, String> MATERIAL_MD5 = createField(DSL.name("material_md5"), SQLDataType.VARCHAR(32).nullable(false).defaultValue(DSL.inline("''", SQLDataType.VARCHAR)), this, "物料内容的MD5值, 用来判断物料是否相同");

    /**
     * The column <code>lau_material.material_type</code>. 物料类型: 0 - 未知, 1 - 图片,
     * 3 - GIF, 4, 标题, 5 - 三图, 6 - 视频和封面, 7 - 稿件
     */
    public final TableField<LauMaterialRecord, Integer> MATERIAL_TYPE = createField(DSL.name("material_type"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "物料类型: 0 - 未知, 1 - 图片, 3 - GIF, 4, 标题, 5 - 三图, 6 - 视频和封面, 7 - 稿件");

    /**
     * The column <code>lau_material.material_content</code>. 物料内容
     */
    public final TableField<LauMaterialRecord, String> MATERIAL_CONTENT = createField(DSL.name("material_content"), SQLDataType.VARCHAR(256).nullable(false).defaultValue(DSL.inline("''", SQLDataType.VARCHAR)), this, "物料内容");

    private TLauMaterial(Name alias, Table<LauMaterialRecord> aliased) {
        this(alias, aliased, null);
    }

    private TLauMaterial(Name alias, Table<LauMaterialRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment("投放物料表"), TableOptions.table());
    }

    /**
     * Create an aliased <code>lau_material</code> table reference
     */
    public TLauMaterial(String alias) {
        this(DSL.name(alias), LAU_MATERIAL);
    }

    /**
     * Create an aliased <code>lau_material</code> table reference
     */
    public TLauMaterial(Name alias) {
        this(alias, LAU_MATERIAL);
    }

    /**
     * Create a <code>lau_material</code> table reference
     */
    public TLauMaterial() {
        this(DSL.name("lau_material"), null);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : DefaultSchema.DEFAULT_SCHEMA;
    }

    @Override
    public Identity<LauMaterialRecord, Long> getIdentity() {
        return (Identity<LauMaterialRecord, Long>) super.getIdentity();
    }

    @Override
    public UniqueKey<LauMaterialRecord> getPrimaryKey() {
        return Internal.createUniqueKey(TLauMaterial.LAU_MATERIAL, DSL.name("KEY_lau_material_PRIMARY"), new TableField[] { TLauMaterial.LAU_MATERIAL.ID }, true);
    }

    @Override
    public List<UniqueKey<LauMaterialRecord>> getUniqueKeys() {
        return Arrays.asList(
            Internal.createUniqueKey(TLauMaterial.LAU_MATERIAL, DSL.name("KEY_lau_material_uk_type_md5"), new TableField[] { TLauMaterial.LAU_MATERIAL.MATERIAL_TYPE, TLauMaterial.LAU_MATERIAL.MATERIAL_MD5 }, true)
        );
    }

    @Override
    public TLauMaterial as(String alias) {
        return new TLauMaterial(DSL.name(alias), this);
    }

    @Override
    public TLauMaterial as(Name alias) {
        return new TLauMaterial(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public TLauMaterial rename(String name) {
        return new TLauMaterial(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public TLauMaterial rename(Name name) {
        return new TLauMaterial(name, null);
    }

    // -------------------------------------------------------------------------
    // Row6 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row6<Long, Timestamp, Timestamp, String, Integer, String> fieldsRow() {
        return (Row6) super.fieldsRow();
    }
}
