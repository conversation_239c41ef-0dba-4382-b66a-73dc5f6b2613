/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad.tables;


import java.sql.Date;
import java.sql.Timestamp;

import org.jooq.Field;
import org.jooq.Identity;
import org.jooq.Name;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.Internal;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad.DefaultSchema;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.records.AccAccountRecord;


/**
 * s_support_pickup
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class TAccAccount extends TableImpl<AccAccountRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>acc_account</code>
     */
    public static final TAccAccount ACC_ACCOUNT = new TAccAccount();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<AccAccountRecord> getRecordType() {
        return AccAccountRecord.class;
    }

    /**
     * The column <code>acc_account.account_id</code>. 账号ID
     */
    public final TableField<AccAccountRecord, Integer> ACCOUNT_ID = createField(DSL.name("account_id"), SQLDataType.INTEGER.nullable(false).identity(true), this, "账号ID");

    /**
     * The column <code>acc_account.username</code>. 用户名
     */
    public final TableField<AccAccountRecord, String> USERNAME = createField(DSL.name("username"), SQLDataType.VARCHAR(64).nullable(false).defaultValue(DSL.inline("''", SQLDataType.VARCHAR)), this, "用户名");

    /**
     * The column <code>acc_account.mobile</code>. 手机号码
     */
    public final TableField<AccAccountRecord, String> MOBILE = createField(DSL.name("mobile"), SQLDataType.VARCHAR(64).defaultValue(DSL.inline("NULL", SQLDataType.VARCHAR)), this, "手机号码");

    /**
     * The column <code>acc_account.password_strength</code>. 密码强度-已废弃
     */
    public final TableField<AccAccountRecord, Integer> PASSWORD_STRENGTH = createField(DSL.name("password_strength"), SQLDataType.INTEGER.nullable(false), this, "密码强度-已废弃");

    /**
     * The column <code>acc_account.salt</code>. 盐-已废弃
     */
    public final TableField<AccAccountRecord, String> SALT = createField(DSL.name("salt"), SQLDataType.VARCHAR(128).nullable(false).defaultValue(DSL.inline("''", SQLDataType.VARCHAR)), this, "盐-已废弃");

    /**
     * The column <code>acc_account.salt_password</code>. 加盐密码-已废弃
     */
    public final TableField<AccAccountRecord, String> SALT_PASSWORD = createField(DSL.name("salt_password"), SQLDataType.VARCHAR(64).nullable(false).defaultValue(DSL.inline("''", SQLDataType.VARCHAR)), this, "加盐密码-已废弃");

    /**
     * The column <code>acc_account.status</code>. 状态:   -1-未开通 0-启用 1-冻结 2-余额不足
     */
    public final TableField<AccAccountRecord, Integer> STATUS = createField(DSL.name("status"), SQLDataType.INTEGER.nullable(false), this, "状态:   -1-未开通 0-启用 1-冻结 2-余额不足");

    /**
     * The column <code>acc_account.crm_customer_id</code>. crm customer主键-已废弃
     */
    public final TableField<AccAccountRecord, Integer> CRM_CUSTOMER_ID = createField(DSL.name("crm_customer_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "crm customer主键-已废弃");

    /**
     * The column <code>acc_account.ctime</code>. 添加时间
     */
    public final TableField<AccAccountRecord, Timestamp> CTIME = createField(DSL.name("ctime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "添加时间");

    /**
     * The column <code>acc_account.mtime</code>. 更新时间
     */
    public final TableField<AccAccountRecord, Timestamp> MTIME = createField(DSL.name("mtime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "更新时间");

    /**
     * The column <code>acc_account.is_deleted</code>. 软删除，0是有效，1是删除
     */
    public final TableField<AccAccountRecord, Integer> IS_DELETED = createField(DSL.name("is_deleted"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "软删除，0是有效，1是删除");

    /**
     * The column <code>acc_account.order_type</code>. 账户订单类型(0预付款 1后付款
     * -1未开通)-已废弃
     */
    public final TableField<AccAccountRecord, Integer> ORDER_TYPE = createField(DSL.name("order_type"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("-1", SQLDataType.INTEGER)), this, "账户订单类型(0预付款 1后付款 -1未开通)-已废弃");

    /**
     * The column <code>acc_account.mid</code>. 主站账号id
     */
    public final TableField<AccAccountRecord, Long> MID = createField(DSL.name("mid"), SQLDataType.BIGINT.nullable(false).defaultValue(DSL.inline("0", SQLDataType.BIGINT)), this, "主站账号id");

    /**
     * The column <code>acc_account.account_type</code>. 0客户（空绑定账号） 1主站账号
     */
    public final TableField<AccAccountRecord, Integer> ACCOUNT_TYPE = createField(DSL.name("account_type"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "0客户（空绑定账号） 1主站账号");

    /**
     * The column <code>acc_account.active_time</code>. 激活时间（默认为空）
     */
    public final TableField<AccAccountRecord, Timestamp> ACTIVE_TIME = createField(DSL.name("active_time"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.inline("'0000-00-00 00:00:00'", SQLDataType.TIMESTAMP)), this, "激活时间（默认为空）");

    /**
     * The column <code>acc_account.name</code>. 真实姓名（企业用户为公司名）
     */
    public final TableField<AccAccountRecord, String> NAME = createField(DSL.name("name"), SQLDataType.VARCHAR(32).nullable(false).defaultValue(DSL.inline("''", SQLDataType.VARCHAR)), this, "真实姓名（企业用户为公司名）");

    /**
     * The column <code>acc_account.icp_record_number</code>. icp备案号
     */
    public final TableField<AccAccountRecord, String> ICP_RECORD_NUMBER = createField(DSL.name("icp_record_number"), SQLDataType.VARCHAR(256).defaultValue(DSL.inline("NULL", SQLDataType.VARCHAR)), this, "icp备案号");

    /**
     * The column <code>acc_account.icp_info_image</code>. icp截图url(废弃)
     */
    public final TableField<AccAccountRecord, String> ICP_INFO_IMAGE = createField(DSL.name("icp_info_image"), SQLDataType.VARCHAR(255).nullable(false).defaultValue(DSL.inline("''", SQLDataType.VARCHAR)), this, "icp截图url(废弃)");

    /**
     * The column <code>acc_account.brand_domain</code>. 推广域名
     */
    public final TableField<AccAccountRecord, String> BRAND_DOMAIN = createField(DSL.name("brand_domain"), SQLDataType.VARCHAR(512).defaultValue(DSL.inline("NULL", SQLDataType.VARCHAR)), this, "推广域名");

    /**
     * The column <code>acc_account.user_type</code>. 用户属性 0个人用户 1机构用户 2个人起飞用户
     */
    public final TableField<AccAccountRecord, Integer> USER_TYPE = createField(DSL.name("user_type"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "用户属性 0个人用户 1机构用户 2个人起飞用户");

    /**
     * The column <code>acc_account.ad_status</code>. 是否允许投放效果广告，广告系统状态（-1未激活
     * 0允许 1禁止）
     */
    public final TableField<AccAccountRecord, Integer> AD_STATUS = createField(DSL.name("ad_status"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "是否允许投放效果广告，广告系统状态（-1未激活 0允许 1禁止）");

    /**
     * The column <code>acc_account.version</code>. 版本号(mvcc)
     */
    public final TableField<AccAccountRecord, Integer> VERSION = createField(DSL.name("version"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "版本号(mvcc)");

    /**
     * The column <code>acc_account.category_first_id</code>. 行业一级分类id
     */
    public final TableField<AccAccountRecord, Integer> CATEGORY_FIRST_ID = createField(DSL.name("category_first_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "行业一级分类id");

    /**
     * The column <code>acc_account.category_second_id</code>. 行业二级分类id
     */
    public final TableField<AccAccountRecord, Integer> CATEGORY_SECOND_ID = createField(DSL.name("category_second_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "行业二级分类id");

    /**
     * The column <code>acc_account.remark</code>. 备注
     */
    public final TableField<AccAccountRecord, String> REMARK = createField(DSL.name("remark"), SQLDataType.VARCHAR(512).nullable(false).defaultValue(DSL.inline("''", SQLDataType.VARCHAR)), this, "备注");

    /**
     * The column <code>acc_account.is_agent</code>. 是否是代理商 0否 1是
     */
    public final TableField<AccAccountRecord, Integer> IS_AGENT = createField(DSL.name("is_agent"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "是否是代理商 0否 1是");

    /**
     * The column <code>acc_account.agent_type</code>. 代理商类型：0-无 1-品牌代理商 
     * 2-MCN代理商  3-效果代理商
     */
    public final TableField<AccAccountRecord, Integer> AGENT_TYPE = createField(DSL.name("agent_type"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "代理商类型：0-无 1-品牌代理商  2-MCN代理商  3-效果代理商");

    /**
     * The column <code>acc_account.business_role_id</code>. 业务角色ID
     */
    public final TableField<AccAccountRecord, Integer> BUSINESS_ROLE_ID = createField(DSL.name("business_role_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "业务角色ID");

    /**
     * The column <code>acc_account.company_name</code>. 公司名称
     */
    public final TableField<AccAccountRecord, String> COMPANY_NAME = createField(DSL.name("company_name"), SQLDataType.VARCHAR(64).nullable(false).defaultValue(DSL.inline("''", SQLDataType.VARCHAR)), this, "公司名称");

    /**
     * The column <code>acc_account.area_id</code>. 区域id
     */
    public final TableField<AccAccountRecord, Integer> AREA_ID = createField(DSL.name("area_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "区域id");

    /**
     * The column <code>acc_account.dependency_agent_id</code>. 所属代理商id
     */
    public final TableField<AccAccountRecord, Integer> DEPENDENCY_AGENT_ID = createField(DSL.name("dependency_agent_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "所属代理商id");

    /**
     * The column <code>acc_account.website_name</code>. 网站名称
     */
    public final TableField<AccAccountRecord, String> WEBSITE_NAME = createField(DSL.name("website_name"), SQLDataType.VARCHAR(256).defaultValue(DSL.inline("NULL", SQLDataType.VARCHAR)), this, "网站名称");

    /**
     * The column <code>acc_account.weibo</code>. 微博账号
     */
    public final TableField<AccAccountRecord, String> WEIBO = createField(DSL.name("weibo"), SQLDataType.VARCHAR(256).defaultValue(DSL.inline("''", SQLDataType.VARCHAR)), this, "微博账号");

    /**
     * The column <code>acc_account.internal_linkman</code>. 内部联系人（内网账号）
     */
    public final TableField<AccAccountRecord, String> INTERNAL_LINKMAN = createField(DSL.name("internal_linkman"), SQLDataType.VARCHAR(64).nullable(false).defaultValue(DSL.inline("''", SQLDataType.VARCHAR)), this, "内部联系人（内网账号）");

    /**
     * The column <code>acc_account.linkman_address</code>. 联系人地址
     */
    public final TableField<AccAccountRecord, String> LINKMAN_ADDRESS = createField(DSL.name("linkman_address"), SQLDataType.VARCHAR(256).defaultValue(DSL.inline("NULL", SQLDataType.VARCHAR)), this, "联系人地址");

    /**
     * The column <code>acc_account.bank</code>. 开户行
     */
    public final TableField<AccAccountRecord, String> BANK = createField(DSL.name("bank"), SQLDataType.VARCHAR(256).defaultValue(DSL.inline("NULL", SQLDataType.VARCHAR)), this, "开户行");

    /**
     * The column <code>acc_account.qualification_id</code>. 主体资质分类id
     */
    public final TableField<AccAccountRecord, Integer> QUALIFICATION_ID = createField(DSL.name("qualification_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "主体资质分类id");

    /**
     * The column <code>acc_account.business_licence_code</code>. 营业执照编码
     */
    public final TableField<AccAccountRecord, String> BUSINESS_LICENCE_CODE = createField(DSL.name("business_licence_code"), SQLDataType.VARCHAR(256).defaultValue(DSL.inline("NULL", SQLDataType.VARCHAR)), this, "营业执照编码");

    /**
     * The column <code>acc_account.business_licence_expire_date</code>.
     * 营业执照到期时间
     */
    public final TableField<AccAccountRecord, Date> BUSINESS_LICENCE_EXPIRE_DATE = createField(DSL.name("business_licence_expire_date"), SQLDataType.DATE.nullable(false).defaultValue(DSL.inline("'0000-00-00'", SQLDataType.DATE)), this, "营业执照到期时间");

    /**
     * The column <code>acc_account.is_business_licence_indefinite</code>.
     * 营业执照是否长期有效 1是 0 否
     */
    public final TableField<AccAccountRecord, Integer> IS_BUSINESS_LICENCE_INDEFINITE = createField(DSL.name("is_business_licence_indefinite"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("1", SQLDataType.INTEGER)), this, "营业执照是否长期有效 1是 0 否");

    /**
     * The column <code>acc_account.legal_person_name</code>. 法人姓名
     */
    public final TableField<AccAccountRecord, String> LEGAL_PERSON_NAME = createField(DSL.name("legal_person_name"), SQLDataType.VARCHAR(256).defaultValue(DSL.inline("NULL", SQLDataType.VARCHAR)), this, "法人姓名");

    /**
     * The column <code>acc_account.legal_person_idcard_expire_date</code>.
     * 企业法人身份证过期时间
     */
    public final TableField<AccAccountRecord, Date> LEGAL_PERSON_IDCARD_EXPIRE_DATE = createField(DSL.name("legal_person_idcard_expire_date"), SQLDataType.DATE.nullable(false).defaultValue(DSL.inline("'0000-00-00'", SQLDataType.DATE)), this, "企业法人身份证过期时间");

    /**
     * The column <code>acc_account.is_legal_person_idcard_indefinite</code>.
     * 法人身份证是否长期有效 1是 0 否
     */
    public final TableField<AccAccountRecord, Integer> IS_LEGAL_PERSON_IDCARD_INDEFINITE = createField(DSL.name("is_legal_person_idcard_indefinite"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("1", SQLDataType.INTEGER)), this, "法人身份证是否长期有效 1是 0 否");

    /**
     * The column <code>acc_account.audit_remark</code>. 审核备注

     */
    public final TableField<AccAccountRecord, String> AUDIT_REMARK = createField(DSL.name("audit_remark"), SQLDataType.VARCHAR(512).nullable(false).defaultValue(DSL.inline("''", SQLDataType.VARCHAR)), this, "审核备注\n");

    /**
     * The column <code>acc_account.account_status</code>. 0-启用 1-冻结 3编辑中 4待审核
     * 5驳回
     * 
     * 
     */
    public final TableField<AccAccountRecord, Integer> ACCOUNT_STATUS = createField(DSL.name("account_status"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "0-启用 1-冻结 3编辑中 4待审核 5驳回\n\n");

    /**
     * The column <code>acc_account.linkman_email</code>. 联系人邮箱
     */
    public final TableField<AccAccountRecord, String> LINKMAN_EMAIL = createField(DSL.name("linkman_email"), SQLDataType.VARCHAR(256).defaultValue(DSL.inline("NULL", SQLDataType.VARCHAR)), this, "联系人邮箱");

    /**
     * The column <code>acc_account.gd_status</code>. GD广告系统状态（0允许 1禁止）
     */
    public final TableField<AccAccountRecord, Integer> GD_STATUS = createField(DSL.name("gd_status"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "GD广告系统状态（0允许 1禁止）");

    /**
     * The column <code>acc_account.agent_auth_expire_date</code>. 代理商授权有效期

     */
    public final TableField<AccAccountRecord, Date> AGENT_AUTH_EXPIRE_DATE = createField(DSL.name("agent_auth_expire_date"), SQLDataType.DATE.nullable(false).defaultValue(DSL.inline("'0000-00-00'", SQLDataType.DATE)), this, "代理商授权有效期\n");

    /**
     * The column <code>acc_account.is_inner</code>. 是否是内部账号 0否 1是
     */
    public final TableField<AccAccountRecord, Integer> IS_INNER = createField(DSL.name("is_inner"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "是否是内部账号 0否 1是");

    /**
     * The column <code>acc_account.department_id</code>. 部门 id 
     */
    public final TableField<AccAccountRecord, Integer> DEPARTMENT_ID = createField(DSL.name("department_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("1", SQLDataType.INTEGER)), this, "部门 id ");

    /**
     * The column <code>acc_account.is_support_seller</code>. 是否支持商家中心投放 0-否 1-是
     */
    public final TableField<AccAccountRecord, Integer> IS_SUPPORT_SELLER = createField(DSL.name("is_support_seller"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "是否支持商家中心投放 0-否 1-是");

    /**
     * The column <code>acc_account.is_support_game</code>. 是否支持游戏中心投放 0-否 1-是
     */
    public final TableField<AccAccountRecord, Integer> IS_SUPPORT_GAME = createField(DSL.name("is_support_game"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "是否支持游戏中心投放 0-否 1-是");

    /**
     * The column <code>acc_account.is_support_dpa</code>. 是否支持DPA投放 0-否 1-是
     */
    public final TableField<AccAccountRecord, Integer> IS_SUPPORT_DPA = createField(DSL.name("is_support_dpa"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "是否支持DPA投放 0-否 1-是");

    /**
     * The column <code>acc_account.is_support_content</code>. 是否支持内容推广 0-否 1-是
     */
    public final TableField<AccAccountRecord, Integer> IS_SUPPORT_CONTENT = createField(DSL.name("is_support_content"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "是否支持内容推广 0-否 1-是");

    /**
     * The column <code>acc_account.product_line</code>. 产品线名
     */
    public final TableField<AccAccountRecord, String> PRODUCT_LINE = createField(DSL.name("product_line"), SQLDataType.VARCHAR(128).nullable(false).defaultValue(DSL.inline("''", SQLDataType.VARCHAR)), this, "产品线名");

    /**
     * The column <code>acc_account.phone_number</code>. 联系人手机号
     */
    public final TableField<AccAccountRecord, String> PHONE_NUMBER = createField(DSL.name("phone_number"), SQLDataType.VARCHAR(128).defaultValue(DSL.inline("NULL", SQLDataType.VARCHAR)), this, "联系人手机号");

    /**
     * The column <code>acc_account.idcard_type</code>. 证件类型 0-未知 1-身份证（大陆地区）
     * 2-护照（港澳台及海外）
     */
    public final TableField<AccAccountRecord, Integer> IDCARD_TYPE = createField(DSL.name("idcard_type"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "证件类型 0-未知 1-身份证（大陆地区） 2-护照（港澳台及海外）");

    /**
     * The column <code>acc_account.idcard_number</code>. 证件号码
     */
    public final TableField<AccAccountRecord, String> IDCARD_NUMBER = createField(DSL.name("idcard_number"), SQLDataType.VARCHAR(128).defaultValue(DSL.inline("NULL", SQLDataType.VARCHAR)), this, "证件号码");

    /**
     * The column <code>acc_account.idcard_expire_date</code>. 证件到期时间
     */
    public final TableField<AccAccountRecord, Timestamp> IDCARD_EXPIRE_DATE = createField(DSL.name("idcard_expire_date"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.inline("'0000-00-00 00:00:00'", SQLDataType.TIMESTAMP)), this, "证件到期时间");

    /**
     * The column <code>acc_account.personal_address</code>. 联系人地址（个人账户）
     */
    public final TableField<AccAccountRecord, String> PERSONAL_ADDRESS = createField(DSL.name("personal_address"), SQLDataType.VARCHAR(512).defaultValue(DSL.inline("NULL", SQLDataType.VARCHAR)), this, "联系人地址（个人账户）");

    /**
     * The column <code>acc_account.is_idcard_indefinite</code>. 证件有效期是否长期有效 1是
     * 0 否
     */
    public final TableField<AccAccountRecord, Integer> IS_IDCARD_INDEFINITE = createField(DSL.name("is_idcard_indefinite"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "证件有效期是否长期有效 1是 0 否");

    /**
     * The column <code>acc_account.personal_name</code>. 个人姓名
     */
    public final TableField<AccAccountRecord, String> PERSONAL_NAME = createField(DSL.name("personal_name"), SQLDataType.VARCHAR(128).defaultValue(DSL.inline("NULL", SQLDataType.VARCHAR)), this, "个人姓名");

    /**
     * The column <code>acc_account.group_id</code>.
     * 集团id（对应acc_company_group的ID）
     */
    public final TableField<AccAccountRecord, Integer> GROUP_ID = createField(DSL.name("group_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "集团id（对应acc_company_group的ID）");

    /**
     * The column <code>acc_account.product_line_id</code>. 产品线id
     */
    public final TableField<AccAccountRecord, Integer> PRODUCT_LINE_ID = createField(DSL.name("product_line_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "产品线id");

    /**
     * The column <code>acc_account.product_id</code>. 产品id
     */
    public final TableField<AccAccountRecord, Integer> PRODUCT_ID = createField(DSL.name("product_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "产品id");

    /**
     * The column <code>acc_account.is_support_pickup</code>. 是否支持UP主商单投放 0-否
     * 1-是
     */
    public final TableField<AccAccountRecord, Integer> IS_SUPPORT_PICKUP = createField(DSL.name("is_support_pickup"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "是否支持UP主商单投放 0-否 1-是");

    /**
     * The column <code>acc_account.is_support_mas</code>. 是否支持互选广告投放 0-否 1-是
     */
    public final TableField<AccAccountRecord, Integer> IS_SUPPORT_MAS = createField(DSL.name("is_support_mas"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "是否支持互选广告投放 0-否 1-是");

    /**
     * The column <code>acc_account.auto_update_label</code>. 是否自动更新帐号标签 0-否 1-是
     */
    public final TableField<AccAccountRecord, Integer> AUTO_UPDATE_LABEL = createField(DSL.name("auto_update_label"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("1", SQLDataType.INTEGER)), this, "是否自动更新帐号标签 0-否 1-是");

    /**
     * The column <code>acc_account.is_support_fly</code>. 是否支持商业起飞投放 0-否 1-是
     */
    public final TableField<AccAccountRecord, Integer> IS_SUPPORT_FLY = createField(DSL.name("is_support_fly"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "是否支持商业起飞投放 0-否 1-是");

    /**
     * The column <code>acc_account.allow_cash_pay</code>. 是否允许现金支付（个人起飞专用）
     * 0-不允许 1-允许
     */
    public final TableField<AccAccountRecord, Integer> ALLOW_CASH_PAY = createField(DSL.name("allow_cash_pay"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "是否允许现金支付（个人起飞专用） 0-不允许 1-允许");

    /**
     * The column <code>acc_account.allow_incentive_bonus_pay</code>.
     * 是否允许激励金支付（个人起飞专用） 0-不允许 1-允许
     */
    public final TableField<AccAccountRecord, Integer> ALLOW_INCENTIVE_BONUS_PAY = createField(DSL.name("allow_incentive_bonus_pay"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "是否允许激励金支付（个人起飞专用） 0-不允许 1-允许");

    /**
     * The column <code>acc_account.customer_id</code>. 所属客户id
     */
    public final TableField<AccAccountRecord, Integer> CUSTOMER_ID = createField(DSL.name("customer_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "所属客户id");

    /**
     * The column <code>acc_account.creator</code>. 创建人
     */
    public final TableField<AccAccountRecord, String> CREATOR = createField(DSL.name("creator"), SQLDataType.VARCHAR(64).nullable(false).defaultValue(DSL.inline("''", SQLDataType.VARCHAR)), this, "创建人");

    /**
     * The column <code>acc_account.payment_period</code>. 账期
     */
    public final TableField<AccAccountRecord, Integer> PAYMENT_PERIOD = createField(DSL.name("payment_period"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "账期");

    /**
     * The column <code>acc_account.is_support_local_ad</code>. 是否支持本地广告产品 0-否
     * 1-是
     */
    public final TableField<AccAccountRecord, Integer> IS_SUPPORT_LOCAL_AD = createField(DSL.name("is_support_local_ad"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "是否支持本地广告产品 0-否 1-是");

    /**
     * The column <code>acc_account.first_industry_tag_id</code>. 一级行业标签
     */
    public final TableField<AccAccountRecord, Integer> FIRST_INDUSTRY_TAG_ID = createField(DSL.name("first_industry_tag_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "一级行业标签");

    /**
     * The column <code>acc_account.second_industry_tag_id</code>. 二级行业标签
     */
    public final TableField<AccAccountRecord, Integer> SECOND_INDUSTRY_TAG_ID = createField(DSL.name("second_industry_tag_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "二级行业标签");

    /**
     * The column <code>acc_account.third_industry_tag_id</code>. 三级行业标签
     */
    public final TableField<AccAccountRecord, Integer> THIRD_INDUSTRY_TAG_ID = createField(DSL.name("third_industry_tag_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "三级行业标签");

    /**
     * The column <code>acc_account.commerce_category_first_id</code>.
     * 商业行业一级分类id(新版)
     */
    public final TableField<AccAccountRecord, Integer> COMMERCE_CATEGORY_FIRST_ID = createField(DSL.name("commerce_category_first_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "商业行业一级分类id(新版)");

    /**
     * The column <code>acc_account.commerce_category_second_id</code>.
     * 商业行业二级分类id（新版）
     */
    public final TableField<AccAccountRecord, Integer> COMMERCE_CATEGORY_SECOND_ID = createField(DSL.name("commerce_category_second_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "商业行业二级分类id（新版）");

    /**
     * The column <code>acc_account.allow_signing_bonus_pay</code>.
     * 是否允许签约金支付（个人起飞专用） 0-不允许 1-允许
     */
    public final TableField<AccAccountRecord, Integer> ALLOW_SIGNING_BONUS_PAY = createField(DSL.name("allow_signing_bonus_pay"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "是否允许签约金支付（个人起飞专用） 0-不允许 1-允许");

    /**
     * The column <code>acc_account.allow_fly_coin_pay</code>. 是否支持起飞币支付 0-否 1-是
     */
    public final TableField<AccAccountRecord, Integer> ALLOW_FLY_COIN_PAY = createField(DSL.name("allow_fly_coin_pay"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "是否支持起飞币支付 0-否 1-是");

    /**
     * The column <code>acc_account.allow_flow_ticket_pay</code>. 是否支持流量券支付 0-否
     * 1-是
     */
    public final TableField<AccAccountRecord, Integer> ALLOW_FLOW_TICKET_PAY = createField(DSL.name("allow_flow_ticket_pay"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "是否支持流量券支付 0-否 1-是");

    /**
     * The column <code>acc_account.finance_type</code>. 财务类型：0-无 1-现金账户 2-虚拟金账户
     */
    public final TableField<AccAccountRecord, Integer> FINANCE_TYPE = createField(DSL.name("finance_type"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "财务类型：0-无 1-现金账户 2-虚拟金账户");

    /**
     * The column <code>acc_account.has_mgk_form</code>. 是否含有落地页表单 0-不包含 1-包含
     */
    public final TableField<AccAccountRecord, Integer> HAS_MGK_FORM = createField(DSL.name("has_mgk_form"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "是否含有落地页表单 0-不包含 1-包含");

    /**
     * The column <code>acc_account.mgk_form_privacy_policy</code>. 建站隐私政策是否同意
     * 0-未设置 1-同意 2 不同意
     */
    public final TableField<AccAccountRecord, Integer> MGK_FORM_PRIVACY_POLICY = createField(DSL.name("mgk_form_privacy_policy"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "建站隐私政策是否同意 0-未设置 1-同意 2 不同意");

    /**
     * The column <code>acc_account.show_to_customer</code>. 0:对客户可见 1:对客户不可见
     */
    public final TableField<AccAccountRecord, Integer> SHOW_TO_CUSTOMER = createField(DSL.name("show_to_customer"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "0:对客户可见 1:对客户不可见");

    /**
     * The column <code>acc_account.is_support_clue_pass</code>. 是否支持线索通 0-否 1-是
     */
    public final TableField<AccAccountRecord, Integer> IS_SUPPORT_CLUE_PASS = createField(DSL.name("is_support_clue_pass"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "是否支持线索通 0-否 1-是");

    /**
     * The column <code>acc_account.promotion_type</code>. 花火开户推广类型 0-无 1-网站
     * 2-网上店铺 3-线下实体店
     */
    public final TableField<AccAccountRecord, Integer> PROMOTION_TYPE = createField(DSL.name("promotion_type"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "花火开户推广类型 0-无 1-网站 2-网上店铺 3-线下实体店");

    /**
     * The column <code>acc_account.united_first_industry_id</code>. 统一一级行业标签
     */
    public final TableField<AccAccountRecord, Integer> UNITED_FIRST_INDUSTRY_ID = createField(DSL.name("united_first_industry_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "统一一级行业标签");

    /**
     * The column <code>acc_account.united_second_industry_id</code>. 统一一级行业标签
     */
    public final TableField<AccAccountRecord, Integer> UNITED_SECOND_INDUSTRY_ID = createField(DSL.name("united_second_industry_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "统一一级行业标签");

    /**
     * The column <code>acc_account.united_third_industry_id</code>. 统一一级行业标签
     */
    public final TableField<AccAccountRecord, Integer> UNITED_THIRD_INDUSTRY_ID = createField(DSL.name("united_third_industry_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "统一一级行业标签");

    /**
     * The column <code>acc_account.old_agent_id</code>. 历史代理商id
     */
    public final TableField<AccAccountRecord, Integer> OLD_AGENT_ID = createField(DSL.name("old_agent_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "历史代理商id");

    private TAccAccount(Name alias, Table<AccAccountRecord> aliased) {
        this(alias, aliased, null);
    }

    private TAccAccount(Name alias, Table<AccAccountRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment("s_support_pickup"), TableOptions.table());
    }

    /**
     * Create an aliased <code>acc_account</code> table reference
     */
    public TAccAccount(String alias) {
        this(DSL.name(alias), ACC_ACCOUNT);
    }

    /**
     * Create an aliased <code>acc_account</code> table reference
     */
    public TAccAccount(Name alias) {
        this(alias, ACC_ACCOUNT);
    }

    /**
     * Create a <code>acc_account</code> table reference
     */
    public TAccAccount() {
        this(DSL.name("acc_account"), null);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : DefaultSchema.DEFAULT_SCHEMA;
    }

    @Override
    public Identity<AccAccountRecord, Integer> getIdentity() {
        return (Identity<AccAccountRecord, Integer>) super.getIdentity();
    }

    @Override
    public UniqueKey<AccAccountRecord> getPrimaryKey() {
        return Internal.createUniqueKey(TAccAccount.ACC_ACCOUNT, DSL.name("KEY_acc_account_PRIMARY"), new TableField[] { TAccAccount.ACC_ACCOUNT.ACCOUNT_ID }, true);
    }

    @Override
    public TAccAccount as(String alias) {
        return new TAccAccount(DSL.name(alias), this);
    }

    @Override
    public TAccAccount as(Name alias) {
        return new TAccAccount(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public TAccAccount rename(String name) {
        return new TAccAccount(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public TAccAccount rename(Name name) {
        return new TAccAccount(name, null);
    }
}
