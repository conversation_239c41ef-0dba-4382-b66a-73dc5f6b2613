/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.daos;


import java.sql.Timestamp;
import java.util.List;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauUnitMonitoring;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauUnitMonitoringPo;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.records.LauUnitMonitoringRecord;


/**
 * 单元监控链接表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
@Repository
public class LauUnitMonitoringDao extends DAOImpl<LauUnitMonitoringRecord, LauUnitMonitoringPo, Long> {

    /**
     * Create a new LauUnitMonitoringDao without any configuration
     */
    public LauUnitMonitoringDao() {
        super(TLauUnitMonitoring.LAU_UNIT_MONITORING, LauUnitMonitoringPo.class);
    }

    /**
     * Create a new LauUnitMonitoringDao with an attached configuration
     */
    @Autowired
    public LauUnitMonitoringDao(Configuration configuration) {
        super(TLauUnitMonitoring.LAU_UNIT_MONITORING, LauUnitMonitoringPo.class, configuration);
    }

    @Override
    public Long getId(LauUnitMonitoringPo object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnitMonitoringPo> fetchRangeOfId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(TLauUnitMonitoring.LAU_UNIT_MONITORING.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<LauUnitMonitoringPo> fetchById(Long... values) {
        return fetch(TLauUnitMonitoring.LAU_UNIT_MONITORING.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public LauUnitMonitoringPo fetchOneById(Long value) {
        return fetchOne(TLauUnitMonitoring.LAU_UNIT_MONITORING.ID, value);
    }

    /**
     * Fetch records that have <code>ctime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnitMonitoringPo> fetchRangeOfCtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TLauUnitMonitoring.LAU_UNIT_MONITORING.CTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>ctime IN (values)</code>
     */
    public List<LauUnitMonitoringPo> fetchByCtime(Timestamp... values) {
        return fetch(TLauUnitMonitoring.LAU_UNIT_MONITORING.CTIME, values);
    }

    /**
     * Fetch records that have <code>mtime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnitMonitoringPo> fetchRangeOfMtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TLauUnitMonitoring.LAU_UNIT_MONITORING.MTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>mtime IN (values)</code>
     */
    public List<LauUnitMonitoringPo> fetchByMtime(Timestamp... values) {
        return fetch(TLauUnitMonitoring.LAU_UNIT_MONITORING.MTIME, values);
    }

    /**
     * Fetch records that have <code>unit_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnitMonitoringPo> fetchRangeOfUnitId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauUnitMonitoring.LAU_UNIT_MONITORING.UNIT_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>unit_id IN (values)</code>
     */
    public List<LauUnitMonitoringPo> fetchByUnitId(Integer... values) {
        return fetch(TLauUnitMonitoring.LAU_UNIT_MONITORING.UNIT_ID, values);
    }

    /**
     * Fetch records that have <code>url BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnitMonitoringPo> fetchRangeOfUrl(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauUnitMonitoring.LAU_UNIT_MONITORING.URL, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>url IN (values)</code>
     */
    public List<LauUnitMonitoringPo> fetchByUrl(String... values) {
        return fetch(TLauUnitMonitoring.LAU_UNIT_MONITORING.URL, values);
    }

    /**
     * Fetch records that have <code>type BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauUnitMonitoringPo> fetchRangeOfType(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauUnitMonitoring.LAU_UNIT_MONITORING.TYPE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>type IN (values)</code>
     */
    public List<LauUnitMonitoringPo> fetchByType(Integer... values) {
        return fetch(TLauUnitMonitoring.LAU_UNIT_MONITORING.TYPE, values);
    }
}
