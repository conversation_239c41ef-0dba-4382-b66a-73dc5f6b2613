/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad.tables;


import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import org.jooq.Field;
import org.jooq.Identity;
import org.jooq.Name;
import org.jooq.Row6;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.Internal;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad.DefaultSchema;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.records.ResTargetMinigameRecord;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class TResTargetMinigame extends TableImpl<ResTargetMinigameRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>res_target_minigame</code>
     */
    public static final TResTargetMinigame RES_TARGET_MINIGAME = new TResTargetMinigame();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ResTargetMinigameRecord> getRecordType() {
        return ResTargetMinigameRecord.class;
    }

    /**
     * The column <code>res_target_minigame.id</code>. 主键
     */
    public final TableField<ResTargetMinigameRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false).identity(true), this, "主键");

    /**
     * The column <code>res_target_minigame.unit_id</code>. 单元id
     */
    public final TableField<ResTargetMinigameRecord, Long> UNIT_ID = createField(DSL.name("unit_id"), SQLDataType.BIGINT.nullable(false), this, "单元id");

    /**
     * The column <code>res_target_minigame.include_game_ids</code>. 定投游戏id，逗号分隔
     */
    public final TableField<ResTargetMinigameRecord, String> INCLUDE_GAME_IDS = createField(DSL.name("include_game_ids"), SQLDataType.VARCHAR(3000).nullable(false), this, "定投游戏id，逗号分隔");

    /**
     * The column <code>res_target_minigame.exclude_game_ids</code>. 排除游戏id，逗号分隔
     */
    public final TableField<ResTargetMinigameRecord, String> EXCLUDE_GAME_IDS = createField(DSL.name("exclude_game_ids"), SQLDataType.VARCHAR(3000).nullable(false), this, "排除游戏id，逗号分隔");

    /**
     * The column <code>res_target_minigame.ctime</code>. 创建时间
     */
    public final TableField<ResTargetMinigameRecord, Timestamp> CTIME = createField(DSL.name("ctime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "创建时间");

    /**
     * The column <code>res_target_minigame.mtime</code>. 修改时间
     */
    public final TableField<ResTargetMinigameRecord, Timestamp> MTIME = createField(DSL.name("mtime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "修改时间");

    private TResTargetMinigame(Name alias, Table<ResTargetMinigameRecord> aliased) {
        this(alias, aliased, null);
    }

    private TResTargetMinigame(Name alias, Table<ResTargetMinigameRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>res_target_minigame</code> table reference
     */
    public TResTargetMinigame(String alias) {
        this(DSL.name(alias), RES_TARGET_MINIGAME);
    }

    /**
     * Create an aliased <code>res_target_minigame</code> table reference
     */
    public TResTargetMinigame(Name alias) {
        this(alias, RES_TARGET_MINIGAME);
    }

    /**
     * Create a <code>res_target_minigame</code> table reference
     */
    public TResTargetMinigame() {
        this(DSL.name("res_target_minigame"), null);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : DefaultSchema.DEFAULT_SCHEMA;
    }

    @Override
    public Identity<ResTargetMinigameRecord, Long> getIdentity() {
        return (Identity<ResTargetMinigameRecord, Long>) super.getIdentity();
    }

    @Override
    public UniqueKey<ResTargetMinigameRecord> getPrimaryKey() {
        return Internal.createUniqueKey(TResTargetMinigame.RES_TARGET_MINIGAME, DSL.name("KEY_res_target_minigame_PRIMARY"), new TableField[] { TResTargetMinigame.RES_TARGET_MINIGAME.ID }, true);
    }

    @Override
    public List<UniqueKey<ResTargetMinigameRecord>> getUniqueKeys() {
        return Arrays.asList(
            Internal.createUniqueKey(TResTargetMinigame.RES_TARGET_MINIGAME, DSL.name("KEY_res_target_minigame_IDX_UNIT_ID"), new TableField[] { TResTargetMinigame.RES_TARGET_MINIGAME.UNIT_ID }, true)
        );
    }

    @Override
    public TResTargetMinigame as(String alias) {
        return new TResTargetMinigame(DSL.name(alias), this);
    }

    @Override
    public TResTargetMinigame as(Name alias) {
        return new TResTargetMinigame(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public TResTargetMinigame rename(String name) {
        return new TResTargetMinigame(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public TResTargetMinigame rename(Name name) {
        return new TResTargetMinigame(name, null);
    }

    // -------------------------------------------------------------------------
    // Row6 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row6<Long, Long, String, String, Timestamp, Timestamp> fieldsRow() {
        return (Row6) super.fieldsRow();
    }
}
