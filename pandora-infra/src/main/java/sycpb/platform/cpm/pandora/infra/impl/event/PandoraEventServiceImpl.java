package sycpb.platform.cpm.pandora.infra.impl.event;

import com.bilibili.warp.databus.DatabusTemplate;
import com.bilibili.warp.databus.Message;
import com.bilibili.warp.spring.boot.autoconfigure.databus.DatabusProperties;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Base64Utils;
import sycpb.platform.cpm.pandora.infra.api.event.PandoraEventService;

import java.util.Objects;
import java.util.UUID;

@Slf4j
@Service
public class PandoraEventServiceImpl implements PandoraEventService {
    private static final String PANDORA_EVENT_KEY = "pandora-event";

    private final ObjectMapper om;
    private final DatabusTemplate databusTemplate;
    private final String topic;
    private final String group;

    public PandoraEventServiceImpl(ObjectMapper om, DatabusProperties databusProperties, DatabusTemplate databusTemplate) {
        this.om = om;
        this.databusTemplate = databusTemplate;
        final var databusProperty = databusProperties.getProperties().get(PANDORA_EVENT_KEY);
        topic = databusProperty.getTopic();
        group = databusProperty.getPub().getGroup();
    }

    @Override
    @SneakyThrows
    public <T> void pubLegacy(Integer key, T obj) {
        final String encoded;
        if (Objects.isNull(obj)) {
            encoded = "";
        } else {
            encoded = Base64Utils.encodeToString(om.writeValueAsBytes(obj));
        }
        log.info("PandoraEvent=[key={}, raw_value={}]", key, obj);
        log.info("PandoraEvent=[key={}, value={}]", key, encoded);
        final var message = Message.Builder
                .of(UUID.randomUUID().toString(), new PandoraEventMsg(key, encoded))
                .withHeader("version", "legacy")
                .build();
        databusTemplate.pub(topic, group, message);
    }

    @Override
    @SneakyThrows
    public <T> void pub(Integer key, T obj) {
        log.info("PandoraEvent=[key={}, value={}]", key, obj);
        final var message = Message.Builder
                .of(UUID.randomUUID().toString(), obj)
                .withHeader("version", "0")
                .withHeader("key", String.valueOf(key))
                .build();
        databusTemplate.pub(topic, group, message);
    }
}
