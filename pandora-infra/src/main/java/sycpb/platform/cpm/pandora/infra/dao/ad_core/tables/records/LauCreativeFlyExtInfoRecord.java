/*
 * This file is generated by j<PERSON><PERSON><PERSON>.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.records;


import java.sql.Timestamp;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record17;
import org.jooq.Row17;
import org.jooq.impl.UpdatableRecordImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauCreativeFlyExtInfo;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauCreativeFlyExtInfoPo;


/**
 * 起飞创意额外信息表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LauCreativeFlyExtInfoRecord extends UpdatableRecordImpl<LauCreativeFlyExtInfoRecord> implements Record17<Long, Integer, Integer, Integer, Integer, <PERSON>, Integer, Integer, String, Integer, <PERSON>tamp, <PERSON>tamp, Integer, String, Integer, String, Integer> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>lau_creative_fly_ext_info.id</code>. 自增ID
     */
    public void setId(Long value) {
        set(0, value);
    }

    /**
     * Getter for <code>lau_creative_fly_ext_info.id</code>. 自增ID
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>lau_creative_fly_ext_info.account_id</code>. 账号ID
     */
    public void setAccountId(Integer value) {
        set(1, value);
    }

    /**
     * Getter for <code>lau_creative_fly_ext_info.account_id</code>. 账号ID
     */
    public Integer getAccountId() {
        return (Integer) get(1);
    }

    /**
     * Setter for <code>lau_creative_fly_ext_info.unit_id</code>. 单元ID
     */
    public void setUnitId(Integer value) {
        set(2, value);
    }

    /**
     * Getter for <code>lau_creative_fly_ext_info.unit_id</code>. 单元ID
     */
    public Integer getUnitId() {
        return (Integer) get(2);
    }

    /**
     * Setter for <code>lau_creative_fly_ext_info.creative_id</code>. 创意ID
     */
    public void setCreativeId(Integer value) {
        set(3, value);
    }

    /**
     * Getter for <code>lau_creative_fly_ext_info.creative_id</code>. 创意ID
     */
    public Integer getCreativeId() {
        return (Integer) get(3);
    }

    /**
     * Setter for <code>lau_creative_fly_ext_info.scenes_type</code>. 起飞场景类型
     * 1-优选场景 3-指定场景 2-已废弃
     */
    public void setScenesType(Integer value) {
        set(4, value);
    }

    /**
     * Getter for <code>lau_creative_fly_ext_info.scenes_type</code>. 起飞场景类型
     * 1-优选场景 3-指定场景 2-已废弃
     */
    public Integer getScenesType() {
        return (Integer) get(4);
    }

    /**
     * Setter for <code>lau_creative_fly_ext_info.specific_scenes</code>.
     * 起飞指定广告位 1-信息流 2-播放页（逗号分隔）
     */
    public void setSpecificScenes(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>lau_creative_fly_ext_info.specific_scenes</code>.
     * 起飞指定广告位 1-信息流 2-播放页（逗号分隔）
     */
    public String getSpecificScenes() {
        return (String) get(5);
    }

    /**
     * Setter for <code>lau_creative_fly_ext_info.creative_style</code>. 起飞创意形式
     * 1-静态图文 2-gif图文(主表style_ability字段的映射)
     */
    public void setCreativeStyle(Integer value) {
        set(6, value);
    }

    /**
     * Getter for <code>lau_creative_fly_ext_info.creative_style</code>. 起飞创意形式
     * 1-静态图文 2-gif图文(主表style_ability字段的映射)
     */
    public Integer getCreativeStyle() {
        return (Integer) get(6);
    }

    /**
     * Setter for <code>lau_creative_fly_ext_info.banner4_cover_type</code>.
     * 版本合并4.0封面类型 0-未知 1-16:10 2-16:9 3-4:3, 10-起飞合并
     */
    public void setBanner4CoverType(Integer value) {
        set(7, value);
    }

    /**
     * Getter for <code>lau_creative_fly_ext_info.banner4_cover_type</code>.
     * 版本合并4.0封面类型 0-未知 1-16:10 2-16:9 3-4:3, 10-起飞合并
     */
    public Integer getBanner4CoverType() {
        return (Integer) get(7);
    }

    /**
     * Setter for <code>lau_creative_fly_ext_info.story_url</code>. 小卡投stroy链接
     */
    public void setStoryUrl(String value) {
        set(8, value);
    }

    /**
     * Getter for <code>lau_creative_fly_ext_info.story_url</code>. 小卡投stroy链接
     */
    public String getStoryUrl() {
        return (String) get(8);
    }

    /**
     * Setter for <code>lau_creative_fly_ext_info.is_deleted</code>.
     * 软删除，0是有效，1是删除
     */
    public void setIsDeleted(Integer value) {
        set(9, value);
    }

    /**
     * Getter for <code>lau_creative_fly_ext_info.is_deleted</code>.
     * 软删除，0是有效，1是删除
     */
    public Integer getIsDeleted() {
        return (Integer) get(9);
    }

    /**
     * Setter for <code>lau_creative_fly_ext_info.ctime</code>. 添加时间
     */
    public void setCtime(Timestamp value) {
        set(10, value);
    }

    /**
     * Getter for <code>lau_creative_fly_ext_info.ctime</code>. 添加时间
     */
    public Timestamp getCtime() {
        return (Timestamp) get(10);
    }

    /**
     * Setter for <code>lau_creative_fly_ext_info.mtime</code>. 更新时间
     */
    public void setMtime(Timestamp value) {
        set(11, value);
    }

    /**
     * Getter for <code>lau_creative_fly_ext_info.mtime</code>. 更新时间
     */
    public Timestamp getMtime() {
        return (Timestamp) get(11);
    }

    /**
     * Setter for <code>lau_creative_fly_ext_info.is_yellow_car</code>. 启用带货组件类型
     * 1:小黄车；2:评论前置；3:组件优选; 4: 优先一跳唤起
     */
    public void setIsYellowCar(Integer value) {
        set(12, value);
    }

    /**
     * Getter for <code>lau_creative_fly_ext_info.is_yellow_car</code>. 启用带货组件类型
     * 1:小黄车；2:评论前置；3:组件优选; 4: 优先一跳唤起
     */
    public Integer getIsYellowCar() {
        return (Integer) get(12);
    }

    /**
     * Setter for <code>lau_creative_fly_ext_info.yellow_car_title</code>. 小黄车标题
     */
    public void setYellowCarTitle(String value) {
        set(13, value);
    }

    /**
     * Getter for <code>lau_creative_fly_ext_info.yellow_car_title</code>. 小黄车标题
     */
    public String getYellowCarTitle() {
        return (String) get(13);
    }

    /**
     * Setter for <code>lau_creative_fly_ext_info.yellow_car_icon</code>. 1-购物
     */
    public void setYellowCarIcon(Integer value) {
        set(14, value);
    }

    /**
     * Getter for <code>lau_creative_fly_ext_info.yellow_car_icon</code>. 1-购物
     */
    public Integer getYellowCarIcon() {
        return (Integer) get(14);
    }

    /**
     * Setter for <code>lau_creative_fly_ext_info.tv_url</code>. TV连接
     */
    public void setTvUrl(String value) {
        set(15, value);
    }

    /**
     * Getter for <code>lau_creative_fly_ext_info.tv_url</code>. TV连接
     */
    public String getTvUrl() {
        return (String) get(15);
    }

    /**
     * Setter for <code>lau_creative_fly_ext_info.prefer_direct_call_up</code>.
     * 优先一跳唤起
     */
    public void setPreferDirectCallUp(Integer value) {
        set(16, value);
    }

    /**
     * Getter for <code>lau_creative_fly_ext_info.prefer_direct_call_up</code>.
     * 优先一跳唤起
     */
    public Integer getPreferDirectCallUp() {
        return (Integer) get(16);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record17 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row17<Long, Integer, Integer, Integer, Integer, String, Integer, Integer, String, Integer, Timestamp, Timestamp, Integer, String, Integer, String, Integer> fieldsRow() {
        return (Row17) super.fieldsRow();
    }

    @Override
    public Row17<Long, Integer, Integer, Integer, Integer, String, Integer, Integer, String, Integer, Timestamp, Timestamp, Integer, String, Integer, String, Integer> valuesRow() {
        return (Row17) super.valuesRow();
    }

    @Override
    public Field<Long> field1() {
        return TLauCreativeFlyExtInfo.LAU_CREATIVE_FLY_EXT_INFO.ID;
    }

    @Override
    public Field<Integer> field2() {
        return TLauCreativeFlyExtInfo.LAU_CREATIVE_FLY_EXT_INFO.ACCOUNT_ID;
    }

    @Override
    public Field<Integer> field3() {
        return TLauCreativeFlyExtInfo.LAU_CREATIVE_FLY_EXT_INFO.UNIT_ID;
    }

    @Override
    public Field<Integer> field4() {
        return TLauCreativeFlyExtInfo.LAU_CREATIVE_FLY_EXT_INFO.CREATIVE_ID;
    }

    @Override
    public Field<Integer> field5() {
        return TLauCreativeFlyExtInfo.LAU_CREATIVE_FLY_EXT_INFO.SCENES_TYPE;
    }

    @Override
    public Field<String> field6() {
        return TLauCreativeFlyExtInfo.LAU_CREATIVE_FLY_EXT_INFO.SPECIFIC_SCENES;
    }

    @Override
    public Field<Integer> field7() {
        return TLauCreativeFlyExtInfo.LAU_CREATIVE_FLY_EXT_INFO.CREATIVE_STYLE;
    }

    @Override
    public Field<Integer> field8() {
        return TLauCreativeFlyExtInfo.LAU_CREATIVE_FLY_EXT_INFO.BANNER4_COVER_TYPE;
    }

    @Override
    public Field<String> field9() {
        return TLauCreativeFlyExtInfo.LAU_CREATIVE_FLY_EXT_INFO.STORY_URL;
    }

    @Override
    public Field<Integer> field10() {
        return TLauCreativeFlyExtInfo.LAU_CREATIVE_FLY_EXT_INFO.IS_DELETED;
    }

    @Override
    public Field<Timestamp> field11() {
        return TLauCreativeFlyExtInfo.LAU_CREATIVE_FLY_EXT_INFO.CTIME;
    }

    @Override
    public Field<Timestamp> field12() {
        return TLauCreativeFlyExtInfo.LAU_CREATIVE_FLY_EXT_INFO.MTIME;
    }

    @Override
    public Field<Integer> field13() {
        return TLauCreativeFlyExtInfo.LAU_CREATIVE_FLY_EXT_INFO.IS_YELLOW_CAR;
    }

    @Override
    public Field<String> field14() {
        return TLauCreativeFlyExtInfo.LAU_CREATIVE_FLY_EXT_INFO.YELLOW_CAR_TITLE;
    }

    @Override
    public Field<Integer> field15() {
        return TLauCreativeFlyExtInfo.LAU_CREATIVE_FLY_EXT_INFO.YELLOW_CAR_ICON;
    }

    @Override
    public Field<String> field16() {
        return TLauCreativeFlyExtInfo.LAU_CREATIVE_FLY_EXT_INFO.TV_URL;
    }

    @Override
    public Field<Integer> field17() {
        return TLauCreativeFlyExtInfo.LAU_CREATIVE_FLY_EXT_INFO.PREFER_DIRECT_CALL_UP;
    }

    @Override
    public Long component1() {
        return getId();
    }

    @Override
    public Integer component2() {
        return getAccountId();
    }

    @Override
    public Integer component3() {
        return getUnitId();
    }

    @Override
    public Integer component4() {
        return getCreativeId();
    }

    @Override
    public Integer component5() {
        return getScenesType();
    }

    @Override
    public String component6() {
        return getSpecificScenes();
    }

    @Override
    public Integer component7() {
        return getCreativeStyle();
    }

    @Override
    public Integer component8() {
        return getBanner4CoverType();
    }

    @Override
    public String component9() {
        return getStoryUrl();
    }

    @Override
    public Integer component10() {
        return getIsDeleted();
    }

    @Override
    public Timestamp component11() {
        return getCtime();
    }

    @Override
    public Timestamp component12() {
        return getMtime();
    }

    @Override
    public Integer component13() {
        return getIsYellowCar();
    }

    @Override
    public String component14() {
        return getYellowCarTitle();
    }

    @Override
    public Integer component15() {
        return getYellowCarIcon();
    }

    @Override
    public String component16() {
        return getTvUrl();
    }

    @Override
    public Integer component17() {
        return getPreferDirectCallUp();
    }

    @Override
    public Long value1() {
        return getId();
    }

    @Override
    public Integer value2() {
        return getAccountId();
    }

    @Override
    public Integer value3() {
        return getUnitId();
    }

    @Override
    public Integer value4() {
        return getCreativeId();
    }

    @Override
    public Integer value5() {
        return getScenesType();
    }

    @Override
    public String value6() {
        return getSpecificScenes();
    }

    @Override
    public Integer value7() {
        return getCreativeStyle();
    }

    @Override
    public Integer value8() {
        return getBanner4CoverType();
    }

    @Override
    public String value9() {
        return getStoryUrl();
    }

    @Override
    public Integer value10() {
        return getIsDeleted();
    }

    @Override
    public Timestamp value11() {
        return getCtime();
    }

    @Override
    public Timestamp value12() {
        return getMtime();
    }

    @Override
    public Integer value13() {
        return getIsYellowCar();
    }

    @Override
    public String value14() {
        return getYellowCarTitle();
    }

    @Override
    public Integer value15() {
        return getYellowCarIcon();
    }

    @Override
    public String value16() {
        return getTvUrl();
    }

    @Override
    public Integer value17() {
        return getPreferDirectCallUp();
    }

    @Override
    public LauCreativeFlyExtInfoRecord value1(Long value) {
        setId(value);
        return this;
    }

    @Override
    public LauCreativeFlyExtInfoRecord value2(Integer value) {
        setAccountId(value);
        return this;
    }

    @Override
    public LauCreativeFlyExtInfoRecord value3(Integer value) {
        setUnitId(value);
        return this;
    }

    @Override
    public LauCreativeFlyExtInfoRecord value4(Integer value) {
        setCreativeId(value);
        return this;
    }

    @Override
    public LauCreativeFlyExtInfoRecord value5(Integer value) {
        setScenesType(value);
        return this;
    }

    @Override
    public LauCreativeFlyExtInfoRecord value6(String value) {
        setSpecificScenes(value);
        return this;
    }

    @Override
    public LauCreativeFlyExtInfoRecord value7(Integer value) {
        setCreativeStyle(value);
        return this;
    }

    @Override
    public LauCreativeFlyExtInfoRecord value8(Integer value) {
        setBanner4CoverType(value);
        return this;
    }

    @Override
    public LauCreativeFlyExtInfoRecord value9(String value) {
        setStoryUrl(value);
        return this;
    }

    @Override
    public LauCreativeFlyExtInfoRecord value10(Integer value) {
        setIsDeleted(value);
        return this;
    }

    @Override
    public LauCreativeFlyExtInfoRecord value11(Timestamp value) {
        setCtime(value);
        return this;
    }

    @Override
    public LauCreativeFlyExtInfoRecord value12(Timestamp value) {
        setMtime(value);
        return this;
    }

    @Override
    public LauCreativeFlyExtInfoRecord value13(Integer value) {
        setIsYellowCar(value);
        return this;
    }

    @Override
    public LauCreativeFlyExtInfoRecord value14(String value) {
        setYellowCarTitle(value);
        return this;
    }

    @Override
    public LauCreativeFlyExtInfoRecord value15(Integer value) {
        setYellowCarIcon(value);
        return this;
    }

    @Override
    public LauCreativeFlyExtInfoRecord value16(String value) {
        setTvUrl(value);
        return this;
    }

    @Override
    public LauCreativeFlyExtInfoRecord value17(Integer value) {
        setPreferDirectCallUp(value);
        return this;
    }

    @Override
    public LauCreativeFlyExtInfoRecord values(Long value1, Integer value2, Integer value3, Integer value4, Integer value5, String value6, Integer value7, Integer value8, String value9, Integer value10, Timestamp value11, Timestamp value12, Integer value13, String value14, Integer value15, String value16, Integer value17) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        value13(value13);
        value14(value14);
        value15(value15);
        value16(value16);
        value17(value17);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached LauCreativeFlyExtInfoRecord
     */
    public LauCreativeFlyExtInfoRecord() {
        super(TLauCreativeFlyExtInfo.LAU_CREATIVE_FLY_EXT_INFO);
    }

    /**
     * Create a detached, initialised LauCreativeFlyExtInfoRecord
     */
    public LauCreativeFlyExtInfoRecord(Long id, Integer accountId, Integer unitId, Integer creativeId, Integer scenesType, String specificScenes, Integer creativeStyle, Integer banner4CoverType, String storyUrl, Integer isDeleted, Timestamp ctime, Timestamp mtime, Integer isYellowCar, String yellowCarTitle, Integer yellowCarIcon, String tvUrl, Integer preferDirectCallUp) {
        super(TLauCreativeFlyExtInfo.LAU_CREATIVE_FLY_EXT_INFO);

        setId(id);
        setAccountId(accountId);
        setUnitId(unitId);
        setCreativeId(creativeId);
        setScenesType(scenesType);
        setSpecificScenes(specificScenes);
        setCreativeStyle(creativeStyle);
        setBanner4CoverType(banner4CoverType);
        setStoryUrl(storyUrl);
        setIsDeleted(isDeleted);
        setCtime(ctime);
        setMtime(mtime);
        setIsYellowCar(isYellowCar);
        setYellowCarTitle(yellowCarTitle);
        setYellowCarIcon(yellowCarIcon);
        setTvUrl(tvUrl);
        setPreferDirectCallUp(preferDirectCallUp);
    }

    /**
     * Create a detached, initialised LauCreativeFlyExtInfoRecord
     */
    public LauCreativeFlyExtInfoRecord(LauCreativeFlyExtInfoPo value) {
        super(TLauCreativeFlyExtInfo.LAU_CREATIVE_FLY_EXT_INFO);

        if (value != null) {
            setId(value.getId());
            setAccountId(value.getAccountId());
            setUnitId(value.getUnitId());
            setCreativeId(value.getCreativeId());
            setScenesType(value.getScenesType());
            setSpecificScenes(value.getSpecificScenes());
            setCreativeStyle(value.getCreativeStyle());
            setBanner4CoverType(value.getBanner4CoverType());
            setStoryUrl(value.getStoryUrl());
            setIsDeleted(value.getIsDeleted());
            setCtime(value.getCtime());
            setMtime(value.getMtime());
            setIsYellowCar(value.getIsYellowCar());
            setYellowCarTitle(value.getYellowCarTitle());
            setYellowCarIcon(value.getYellowCarIcon());
            setTvUrl(value.getTvUrl());
            setPreferDirectCallUp(value.getPreferDirectCallUp());
        }
    }
}
