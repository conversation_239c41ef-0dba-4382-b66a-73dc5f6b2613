/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.records;


import java.sql.Timestamp;

import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauCampaign;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauCampaignPo;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LauCampaignRecord extends UpdatableRecordImpl<LauCampaignRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>lau_campaign.campaign_id</code>. 推广计划ID
     */
    public void setCampaignId(Integer value) {
        set(0, value);
    }

    /**
     * Getter for <code>lau_campaign.campaign_id</code>. 推广计划ID
     */
    public Integer getCampaignId() {
        return (Integer) get(0);
    }

    /**
     * Setter for <code>lau_campaign.account_id</code>. 账号ID
     */
    public void setAccountId(Integer value) {
        set(1, value);
    }

    /**
     * Getter for <code>lau_campaign.account_id</code>. 账号ID
     */
    public Integer getAccountId() {
        return (Integer) get(1);
    }

    /**
     * Setter for <code>lau_campaign.campaign_name</code>. 推广计划名称
     */
    public void setCampaignName(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>lau_campaign.campaign_name</code>. 推广计划名称
     */
    public String getCampaignName() {
        return (String) get(2);
    }

    /**
     * Setter for <code>lau_campaign.cost_type</code>. 扣费类型：1-cpm、2-cpc
     */
    public void setCostType(Integer value) {
        set(3, value);
    }

    /**
     * Getter for <code>lau_campaign.cost_type</code>. 扣费类型：1-cpm、2-cpc
     */
    public Integer getCostType() {
        return (Integer) get(3);
    }

    /**
     * Setter for <code>lau_campaign.budget_type</code>. 预算类型：1-日预算、2-总预算
     */
    public void setBudgetType(Integer value) {
        set(4, value);
    }

    /**
     * Getter for <code>lau_campaign.budget_type</code>. 预算类型：1-日预算、2-总预算
     */
    public Integer getBudgetType() {
        return (Integer) get(4);
    }

    /**
     * Setter for <code>lau_campaign.budget</code>. 推广预算
     */
    public void setBudget(Long value) {
        set(5, value);
    }

    /**
     * Getter for <code>lau_campaign.budget</code>. 推广预算
     */
    public Long getBudget() {
        return (Long) get(5);
    }

    /**
     * Setter for <code>lau_campaign.speed_mode</code>. 投放模式（1-匀速投放，2-加速投放）
     */
    public void setSpeedMode(Integer value) {
        set(6, value);
    }

    /**
     * Getter for <code>lau_campaign.speed_mode</code>. 投放模式（1-匀速投放，2-加速投放）
     */
    public Integer getSpeedMode() {
        return (Integer) get(6);
    }

    /**
     * Setter for <code>lau_campaign.status</code>. 状态（1-有效，2-暂停, 3-删除）
     */
    public void setStatus(Integer value) {
        set(7, value);
    }

    /**
     * Getter for <code>lau_campaign.status</code>. 状态（1-有效，2-暂停, 3-删除）
     */
    public Integer getStatus() {
        return (Integer) get(7);
    }

    /**
     * Setter for <code>lau_campaign.is_deleted</code>. 软删除，0是有效，1是删除
     */
    public void setIsDeleted(Integer value) {
        set(8, value);
    }

    /**
     * Getter for <code>lau_campaign.is_deleted</code>. 软删除，0是有效，1是删除
     */
    public Integer getIsDeleted() {
        return (Integer) get(8);
    }

    /**
     * Setter for <code>lau_campaign.ctime</code>. 添加时间
     */
    public void setCtime(Timestamp value) {
        set(9, value);
    }

    /**
     * Getter for <code>lau_campaign.ctime</code>. 添加时间
     */
    public Timestamp getCtime() {
        return (Timestamp) get(9);
    }

    /**
     * Setter for <code>lau_campaign.mtime</code>. 更新时间
     */
    public void setMtime(Timestamp value) {
        set(10, value);
    }

    /**
     * Getter for <code>lau_campaign.mtime</code>. 更新时间
     */
    public Timestamp getMtime() {
        return (Timestamp) get(10);
    }

    /**
     * Setter for <code>lau_campaign.sales_type</code>. 售卖类型 11-CPM, 12-CPC,
     * 21-GD
     */
    public void setSalesType(Integer value) {
        set(11, value);
    }

    /**
     * Getter for <code>lau_campaign.sales_type</code>. 售卖类型 11-CPM, 12-CPC,
     * 21-GD
     */
    public Integer getSalesType() {
        return (Integer) get(11);
    }

    /**
     * Setter for <code>lau_campaign.order_id</code>. fc_order.id 订单ID,
     * CPM广告订单ID为0
     */
    public void setOrderId(Integer value) {
        set(12, value);
    }

    /**
     * Getter for <code>lau_campaign.order_id</code>. fc_order.id 订单ID,
     * CPM广告订单ID为0
     */
    public Integer getOrderId() {
        return (Integer) get(12);
    }

    /**
     * Setter for <code>lau_campaign.campaign_status</code>.
     * 计划状态:1-有效,2-已暂停,3-已结束,4-已删除,5-预算超限
     */
    public void setCampaignStatus(Integer value) {
        set(13, value);
    }

    /**
     * Getter for <code>lau_campaign.campaign_status</code>.
     * 计划状态:1-有效,2-已暂停,3-已结束,4-已删除,5-预算超限
     */
    public Integer getCampaignStatus() {
        return (Integer) get(13);
    }

    /**
     * Setter for <code>lau_campaign.campaign_status_mtime</code>. 单元状态更新时间
     */
    public void setCampaignStatusMtime(Timestamp value) {
        set(14, value);
    }

    /**
     * Getter for <code>lau_campaign.campaign_status_mtime</code>. 单元状态更新时间
     */
    public Timestamp getCampaignStatusMtime() {
        return (Timestamp) get(14);
    }

    /**
     * Setter for <code>lau_campaign.promotion_purpose_type</code>. 推广目的类型（2-落地页
     * 4-应用下载 5-会员购 6-上架游戏 7-投稿内容 8-直播间 9-电商
     */
    public void setPromotionPurposeType(Integer value) {
        set(15, value);
    }

    /**
     * Getter for <code>lau_campaign.promotion_purpose_type</code>. 推广目的类型（2-落地页
     * 4-应用下载 5-会员购 6-上架游戏 7-投稿内容 8-直播间 9-电商
     */
    public Integer getPromotionPurposeType() {
        return (Integer) get(15);
    }

    /**
     * Setter for <code>lau_campaign.origin_tag</code>. 来源标签 0 默认 1adx 2dpa 3ssa
     */
    public void setOriginTag(Integer value) {
        set(16, value);
    }

    /**
     * Getter for <code>lau_campaign.origin_tag</code>. 来源标签 0 默认 1adx 2dpa 3ssa
     */
    public Integer getOriginTag() {
        return (Integer) get(16);
    }

    /**
     * Setter for <code>lau_campaign.lau_account_id</code>. 投放端账号信息
     */
    public void setLauAccountId(Integer value) {
        set(17, value);
    }

    /**
     * Getter for <code>lau_campaign.lau_account_id</code>. 投放端账号信息
     */
    public Integer getLauAccountId() {
        return (Integer) get(17);
    }

    /**
     * Setter for <code>lau_campaign.need_wake_app</code>. 唤起外部APP：0-无须唤起 1-需要唤起
     */
    public void setNeedWakeApp(Integer value) {
        set(18, value);
    }

    /**
     * Getter for <code>lau_campaign.need_wake_app</code>. 唤起外部APP：0-无须唤起 1-需要唤起
     */
    public Integer getNeedWakeApp() {
        return (Integer) get(18);
    }

    /**
     * Setter for <code>lau_campaign.adp_version</code>. 广告平台版本号 0-老版 1-新版
     */
    public void setAdpVersion(Integer value) {
        set(19, value);
    }

    /**
     * Getter for <code>lau_campaign.adp_version</code>. 广告平台版本号 0-老版 1-新版
     */
    public Integer getAdpVersion() {
        return (Integer) get(19);
    }

    /**
     * Setter for <code>lau_campaign.is_new_fly</code>. 新起飞：0-否 1-是
     */
    public void setIsNewFly(Integer value) {
        set(20, value);
    }

    /**
     * Getter for <code>lau_campaign.is_new_fly</code>. 新起飞：0-否 1-是
     */
    public Integer getIsNewFly() {
        return (Integer) get(20);
    }

    /**
     * Setter for <code>lau_campaign.budget_limit_type</code>.
     * 预算限制类型，1：指定预算，2：不限预算
     */
    public void setBudgetLimitType(Integer value) {
        set(21, value);
    }

    /**
     * Getter for <code>lau_campaign.budget_limit_type</code>.
     * 预算限制类型，1：指定预算，2：不限预算
     */
    public Integer getBudgetLimitType() {
        return (Integer) get(21);
    }

    /**
     * Setter for <code>lau_campaign.flag</code>. 多种类型、枚举等通用字段。 0~100 来源
     */
    public void setFlag(Integer value) {
        set(22, value);
    }

    /**
     * Getter for <code>lau_campaign.flag</code>. 多种类型、枚举等通用字段。 0~100 来源
     */
    public Integer getFlag() {
        return (Integer) get(22);
    }

    /**
     * Setter for <code>lau_campaign.deduction_sign</code>. 扣费标识位（0-默认 21-起飞签约托管
     * 22-起飞签约订单 23-起飞-现金-托管 24-起飞-激励金-托管 25-起飞-起飞币-托管）
     */
    public void setDeductionSign(Integer value) {
        set(23, value);
    }

    /**
     * Getter for <code>lau_campaign.deduction_sign</code>. 扣费标识位（0-默认 21-起飞签约托管
     * 22-起飞签约订单 23-起飞-现金-托管 24-起飞-激励金-托管 25-起飞-起飞币-托管）
     */
    public Integer getDeductionSign() {
        return (Integer) get(23);
    }

    /**
     * Setter for <code>lau_campaign.is_managed</code>. 是否是托管计划（0-非托管 1-托管）
     */
    public void setIsManaged(Integer value) {
        set(24, value);
    }

    /**
     * Getter for <code>lau_campaign.is_managed</code>. 是否是托管计划（0-非托管 1-托管）
     */
    public Integer getIsManaged() {
        return (Integer) get(24);
    }

    /**
     * Setter for <code>lau_campaign.managed_begin_time</code>. 托管计划投放开始日期
     */
    public void setManagedBeginTime(String value) {
        set(25, value);
    }

    /**
     * Getter for <code>lau_campaign.managed_begin_time</code>. 托管计划投放开始日期
     */
    public String getManagedBeginTime() {
        return (String) get(25);
    }

    /**
     * Setter for <code>lau_campaign.managed_end_time</code>. 托管计划投放结束日期
     */
    public void setManagedEndTime(String value) {
        set(26, value);
    }

    /**
     * Getter for <code>lau_campaign.managed_end_time</code>. 托管计划投放结束日期
     */
    public String getManagedEndTime() {
        return (String) get(26);
    }

    /**
     * Setter for <code>lau_campaign.is_gd_plus</code>. 是否gd+：0- 否 1-是
     */
    public void setIsGdPlus(Integer value) {
        set(27, value);
    }

    /**
     * Getter for <code>lau_campaign.is_gd_plus</code>. 是否gd+：0- 否 1-是
     */
    public Integer getIsGdPlus() {
        return (Integer) get(27);
    }

    /**
     * Setter for <code>lau_campaign.action_id</code>. 活动id
     */
    public void setActionId(Integer value) {
        set(28, value);
    }

    /**
     * Getter for <code>lau_campaign.action_id</code>. 活动id
     */
    public Integer getActionId() {
        return (Integer) get(28);
    }

    /**
     * Setter for <code>lau_campaign.crm_order_id</code>. 绑定的crm订单号（起飞业务）
     */
    public void setCrmOrderId(Integer value) {
        set(29, value);
    }

    /**
     * Getter for <code>lau_campaign.crm_order_id</code>. 绑定的crm订单号（起飞业务）
     */
    public Integer getCrmOrderId() {
        return (Integer) get(29);
    }

    /**
     * Setter for <code>lau_campaign.is_middle_ad</code>. 是新中台广告：0-否 1-是
     */
    public void setIsMiddleAd(Integer value) {
        set(30, value);
    }

    /**
     * Getter for <code>lau_campaign.is_middle_ad</code>. 是新中台广告：0-否 1-是
     */
    public Integer getIsMiddleAd() {
        return (Integer) get(30);
    }

    /**
     * Setter for <code>lau_campaign.ad_type</code>. 广告类型：0-所有广告 1-搜索广告
     */
    public void setAdType(Integer value) {
        set(31, value);
    }

    /**
     * Getter for <code>lau_campaign.ad_type</code>. 广告类型：0-所有广告 1-搜索广告
     */
    public Integer getAdType() {
        return (Integer) get(31);
    }

    /**
     * Setter for <code>lau_campaign.support_auto</code>. 是否支持自动投放: 0-否, 1-是
     */
    public void setSupportAuto(Integer value) {
        set(32, value);
    }

    /**
     * Getter for <code>lau_campaign.support_auto</code>. 是否支持自动投放: 0-否, 1-是
     */
    public Integer getSupportAuto() {
        return (Integer) get(32);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Integer> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached LauCampaignRecord
     */
    public LauCampaignRecord() {
        super(TLauCampaign.LAU_CAMPAIGN);
    }

    /**
     * Create a detached, initialised LauCampaignRecord
     */
    public LauCampaignRecord(Integer campaignId, Integer accountId, String campaignName, Integer costType, Integer budgetType, Long budget, Integer speedMode, Integer status, Integer isDeleted, Timestamp ctime, Timestamp mtime, Integer salesType, Integer orderId, Integer campaignStatus, Timestamp campaignStatusMtime, Integer promotionPurposeType, Integer originTag, Integer lauAccountId, Integer needWakeApp, Integer adpVersion, Integer isNewFly, Integer budgetLimitType, Integer flag, Integer deductionSign, Integer isManaged, String managedBeginTime, String managedEndTime, Integer isGdPlus, Integer actionId, Integer crmOrderId, Integer isMiddleAd, Integer adType, Integer supportAuto) {
        super(TLauCampaign.LAU_CAMPAIGN);

        setCampaignId(campaignId);
        setAccountId(accountId);
        setCampaignName(campaignName);
        setCostType(costType);
        setBudgetType(budgetType);
        setBudget(budget);
        setSpeedMode(speedMode);
        setStatus(status);
        setIsDeleted(isDeleted);
        setCtime(ctime);
        setMtime(mtime);
        setSalesType(salesType);
        setOrderId(orderId);
        setCampaignStatus(campaignStatus);
        setCampaignStatusMtime(campaignStatusMtime);
        setPromotionPurposeType(promotionPurposeType);
        setOriginTag(originTag);
        setLauAccountId(lauAccountId);
        setNeedWakeApp(needWakeApp);
        setAdpVersion(adpVersion);
        setIsNewFly(isNewFly);
        setBudgetLimitType(budgetLimitType);
        setFlag(flag);
        setDeductionSign(deductionSign);
        setIsManaged(isManaged);
        setManagedBeginTime(managedBeginTime);
        setManagedEndTime(managedEndTime);
        setIsGdPlus(isGdPlus);
        setActionId(actionId);
        setCrmOrderId(crmOrderId);
        setIsMiddleAd(isMiddleAd);
        setAdType(adType);
        setSupportAuto(supportAuto);
    }

    /**
     * Create a detached, initialised LauCampaignRecord
     */
    public LauCampaignRecord(LauCampaignPo value) {
        super(TLauCampaign.LAU_CAMPAIGN);

        if (value != null) {
            setCampaignId(value.getCampaignId());
            setAccountId(value.getAccountId());
            setCampaignName(value.getCampaignName());
            setCostType(value.getCostType());
            setBudgetType(value.getBudgetType());
            setBudget(value.getBudget());
            setSpeedMode(value.getSpeedMode());
            setStatus(value.getStatus());
            setIsDeleted(value.getIsDeleted());
            setCtime(value.getCtime());
            setMtime(value.getMtime());
            setSalesType(value.getSalesType());
            setOrderId(value.getOrderId());
            setCampaignStatus(value.getCampaignStatus());
            setCampaignStatusMtime(value.getCampaignStatusMtime());
            setPromotionPurposeType(value.getPromotionPurposeType());
            setOriginTag(value.getOriginTag());
            setLauAccountId(value.getLauAccountId());
            setNeedWakeApp(value.getNeedWakeApp());
            setAdpVersion(value.getAdpVersion());
            setIsNewFly(value.getIsNewFly());
            setBudgetLimitType(value.getBudgetLimitType());
            setFlag(value.getFlag());
            setDeductionSign(value.getDeductionSign());
            setIsManaged(value.getIsManaged());
            setManagedBeginTime(value.getManagedBeginTime());
            setManagedEndTime(value.getManagedEndTime());
            setIsGdPlus(value.getIsGdPlus());
            setActionId(value.getActionId());
            setCrmOrderId(value.getCrmOrderId());
            setIsMiddleAd(value.getIsMiddleAd());
            setAdType(value.getAdType());
            setSupportAuto(value.getSupportAuto());
        }
    }
}
