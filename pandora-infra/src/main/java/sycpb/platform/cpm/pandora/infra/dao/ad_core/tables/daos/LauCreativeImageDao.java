/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.daos;


import java.sql.Timestamp;
import java.util.List;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauCreativeImage;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauCreativeImagePo;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.records.LauCreativeImageRecord;


/**
 * 创意图片关联表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
@Repository
public class LauCreativeImageDao extends DAOImpl<LauCreativeImageRecord, LauCreativeImagePo, Integer> {

    /**
     * Create a new LauCreativeImageDao without any configuration
     */
    public LauCreativeImageDao() {
        super(TLauCreativeImage.LAU_CREATIVE_IMAGE, LauCreativeImagePo.class);
    }

    /**
     * Create a new LauCreativeImageDao with an attached configuration
     */
    @Autowired
    public LauCreativeImageDao(Configuration configuration) {
        super(TLauCreativeImage.LAU_CREATIVE_IMAGE, LauCreativeImagePo.class, configuration);
    }

    @Override
    public Integer getId(LauCreativeImagePo object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeImagePo> fetchRangeOfId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCreativeImage.LAU_CREATIVE_IMAGE.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<LauCreativeImagePo> fetchById(Integer... values) {
        return fetch(TLauCreativeImage.LAU_CREATIVE_IMAGE.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public LauCreativeImagePo fetchOneById(Integer value) {
        return fetchOne(TLauCreativeImage.LAU_CREATIVE_IMAGE.ID, value);
    }

    /**
     * Fetch records that have <code>unit_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeImagePo> fetchRangeOfUnitId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCreativeImage.LAU_CREATIVE_IMAGE.UNIT_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>unit_id IN (values)</code>
     */
    public List<LauCreativeImagePo> fetchByUnitId(Integer... values) {
        return fetch(TLauCreativeImage.LAU_CREATIVE_IMAGE.UNIT_ID, values);
    }

    /**
     * Fetch records that have <code>creative_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeImagePo> fetchRangeOfCreativeId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCreativeImage.LAU_CREATIVE_IMAGE.CREATIVE_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>creative_id IN (values)</code>
     */
    public List<LauCreativeImagePo> fetchByCreativeId(Integer... values) {
        return fetch(TLauCreativeImage.LAU_CREATIVE_IMAGE.CREATIVE_ID, values);
    }

    /**
     * Fetch records that have <code>image_url BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeImagePo> fetchRangeOfImageUrl(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauCreativeImage.LAU_CREATIVE_IMAGE.IMAGE_URL, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>image_url IN (values)</code>
     */
    public List<LauCreativeImagePo> fetchByImageUrl(String... values) {
        return fetch(TLauCreativeImage.LAU_CREATIVE_IMAGE.IMAGE_URL, values);
    }

    /**
     * Fetch records that have <code>image_md5 BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeImagePo> fetchRangeOfImageMd5(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLauCreativeImage.LAU_CREATIVE_IMAGE.IMAGE_MD5, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>image_md5 IN (values)</code>
     */
    public List<LauCreativeImagePo> fetchByImageMd5(String... values) {
        return fetch(TLauCreativeImage.LAU_CREATIVE_IMAGE.IMAGE_MD5, values);
    }

    /**
     * Fetch records that have <code>type BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeImagePo> fetchRangeOfType(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCreativeImage.LAU_CREATIVE_IMAGE.TYPE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>type IN (values)</code>
     */
    public List<LauCreativeImagePo> fetchByType(Integer... values) {
        return fetch(TLauCreativeImage.LAU_CREATIVE_IMAGE.TYPE, values);
    }

    /**
     * Fetch records that have <code>is_deleted BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeImagePo> fetchRangeOfIsDeleted(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCreativeImage.LAU_CREATIVE_IMAGE.IS_DELETED, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_deleted IN (values)</code>
     */
    public List<LauCreativeImagePo> fetchByIsDeleted(Integer... values) {
        return fetch(TLauCreativeImage.LAU_CREATIVE_IMAGE.IS_DELETED, values);
    }

    /**
     * Fetch records that have <code>ctime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeImagePo> fetchRangeOfCtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TLauCreativeImage.LAU_CREATIVE_IMAGE.CTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>ctime IN (values)</code>
     */
    public List<LauCreativeImagePo> fetchByCtime(Timestamp... values) {
        return fetch(TLauCreativeImage.LAU_CREATIVE_IMAGE.CTIME, values);
    }

    /**
     * Fetch records that have <code>mtime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeImagePo> fetchRangeOfMtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TLauCreativeImage.LAU_CREATIVE_IMAGE.MTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>mtime IN (values)</code>
     */
    public List<LauCreativeImagePo> fetchByMtime(Timestamp... values) {
        return fetch(TLauCreativeImage.LAU_CREATIVE_IMAGE.MTIME, values);
    }

    /**
     * Fetch records that have <code>mgk_template_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeImagePo> fetchRangeOfMgkTemplateId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCreativeImage.LAU_CREATIVE_IMAGE.MGK_TEMPLATE_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>mgk_template_id IN (values)</code>
     */
    public List<LauCreativeImagePo> fetchByMgkTemplateId(Integer... values) {
        return fetch(TLauCreativeImage.LAU_CREATIVE_IMAGE.MGK_TEMPLATE_ID, values);
    }

    /**
     * Fetch records that have <code>mgk_media_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeImagePo> fetchRangeOfMgkMediaId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(TLauCreativeImage.LAU_CREATIVE_IMAGE.MGK_MEDIA_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>mgk_media_id IN (values)</code>
     */
    public List<LauCreativeImagePo> fetchByMgkMediaId(Long... values) {
        return fetch(TLauCreativeImage.LAU_CREATIVE_IMAGE.MGK_MEDIA_ID, values);
    }

    /**
     * Fetch records that have <code>material_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeImagePo> fetchRangeOfMaterialId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(TLauCreativeImage.LAU_CREATIVE_IMAGE.MATERIAL_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>material_id IN (values)</code>
     */
    public List<LauCreativeImagePo> fetchByMaterialId(Long... values) {
        return fetch(TLauCreativeImage.LAU_CREATIVE_IMAGE.MATERIAL_ID, values);
    }

    /**
     * Fetch records that have <code>is_use_smart_cut BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeImagePo> fetchRangeOfIsUseSmartCut(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCreativeImage.LAU_CREATIVE_IMAGE.IS_USE_SMART_CUT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_use_smart_cut IN (values)</code>
     */
    public List<LauCreativeImagePo> fetchByIsUseSmartCut(Integer... values) {
        return fetch(TLauCreativeImage.LAU_CREATIVE_IMAGE.IS_USE_SMART_CUT, values);
    }

    /**
     * Fetch records that have <code>is_aigc_replace BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LauCreativeImagePo> fetchRangeOfIsAigcReplace(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLauCreativeImage.LAU_CREATIVE_IMAGE.IS_AIGC_REPLACE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_aigc_replace IN (values)</code>
     */
    public List<LauCreativeImagePo> fetchByIsAigcReplace(Integer... values) {
        return fetch(TLauCreativeImage.LAU_CREATIVE_IMAGE.IS_AIGC_REPLACE, values);
    }
}
