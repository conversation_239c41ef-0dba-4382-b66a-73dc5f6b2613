package sycpb.platform.cpm.pandora.infra.utils;

import com.google.common.collect.Lists;
import org.springframework.util.CollectionUtils;
import sycpb.platform.cpm.pandora.infra.utils.bos.DiffResult;

import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.BiFunction;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

public class DiffUtils {

    public static<T, K, UK> DiffResult<T, K> diff(List<T> existingRecs, List<T> newRecs, Function<T, UK> getUK, Function<T, K> getKey, BiConsumer<T, K> setKey) {
        return diff(existingRecs, newRecs, getUK, getKey, setKey, null, null);
    }

    public static<T, K, UK> DiffResult<T, K> diff(List<T> existingRecs, List<T> newRecs, Function<T, UK> getUK, Function<T, K> getKey, BiConsumer<T, K> setKey, BiFunction<T, T, Boolean> equalsHandler) {
        return diff(existingRecs, newRecs, getUK, getKey, setKey, null, equalsHandler);
    }

    public static<T, K, UK> DiffResult<T, K> diff(List<T> existingRecs, List<T> newRecs, Function<T, UK> getUK, Function<T, K> getKey, BiConsumer<T, K> setKey, BiConsumer<T, T> postHandler, BiFunction<T, T, Boolean> equalsHandler) {
        final DiffResult<T, K> result = new DiffResult<>();
        final List<K> offlineKeys = new ArrayList<>();
        final List<T> changedRecords = new ArrayList<>();
        final List<T> newRecords = new ArrayList<>();
        result.setOfflineRecordKeys(offlineKeys);
        result.setNewRecords(newRecords);
        result.setChangedRecords(changedRecords);
        final Set<UK> newUKSet = newRecs.stream()
                .map(getUK)
                .collect(Collectors.toSet());
        for (T existingRec : existingRecs) {
            if (!newUKSet.contains(getUK.apply(existingRec))) {
                offlineKeys.add(getKey.apply(existingRec));
            }
        }
        final Map<UK, T> existingRecMap = existingRecs.stream()
                .collect(Collectors.toMap(getUK, Function.identity(), (x, y) -> y));
        for (T newRec : newRecs) {
            final UK uk = getUK.apply(newRec);
            final T existingRecord = existingRecMap.get(uk);
            if (Objects.isNull(existingRecord)) {
                newRecords.add(newRec);
            } else {
                setKey.accept(newRec, getKey.apply(existingRecord));
                if (Objects.nonNull(postHandler)) {
                    postHandler.accept(newRec, existingRecord);
                }
                if (Objects.isNull(equalsHandler)
                        || !equalsHandler.apply(newRec, existingRecord)) {
                    changedRecords.add(newRec);
                }
            }
        }
        return result;
    }

    public static<T, K> void exec(DiffResult<T, K> diff, Consumer<Collection<T>> insertFunc, Consumer<Collection<T>> updateFunc, Consumer<Collection<K>> deleteFunc) {
        if (Objects.isNull(diff)) return;

        final var newRecords = diff.getNewRecords();
        if (Objects.nonNull(insertFunc) && !CollectionUtils.isEmpty(newRecords)) {
            List<List<T>> partition = Lists.partition(new ArrayList<>(newRecords), 20);
            partition.forEach(insertFunc);
        }
        final var changedRecords = diff.getChangedRecords();
        if (Objects.nonNull(updateFunc) && !CollectionUtils.isEmpty(changedRecords)) {
            updateFunc.accept(changedRecords);
        }
        final var offlineRecordKeys = diff.getOfflineRecordKeys();
        if (Objects.nonNull(deleteFunc) && !CollectionUtils.isEmpty(offlineRecordKeys)) {
            deleteFunc.accept(offlineRecordKeys);
        }
    }

    public static<T, K> void execBatch(DiffResult<T, K> diff, Consumer<Collection<T>> batchInsertFunc, Consumer<Collection<T>> batchUpdateFunc, Consumer<Collection<K>> batchDeleteFunc, int batchSize) {
        if (Objects.isNull(diff)) return;

        final var newRecords = diff.getNewRecords();
        if (Objects.nonNull(batchInsertFunc) && !CollectionUtils.isEmpty(newRecords)) {
            List<List<T>> partition = Lists.partition(new ArrayList<>(newRecords), batchSize);
            partition.forEach(batchInsertFunc);
        }
        final var changedRecords = diff.getChangedRecords();
        if (Objects.nonNull(batchUpdateFunc) && !CollectionUtils.isEmpty(changedRecords)) {
            List<List<T>> partition = Lists.partition(new ArrayList<>(changedRecords), batchSize);
            partition.forEach(batchUpdateFunc);
        }
        final var offlineRecordKeys = diff.getOfflineRecordKeys();
        if (Objects.nonNull(batchDeleteFunc) && !CollectionUtils.isEmpty(offlineRecordKeys)) {
            List<List<K>> partition = Lists.partition(new ArrayList<>(offlineRecordKeys), batchSize);
            partition.forEach(batchDeleteFunc);
        }
    }
}
