/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.records;


import java.sql.Timestamp;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record11;
import org.jooq.Row11;
import org.jooq.impl.UpdatableRecordImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauUnitShopGoods;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauUnitShopGoodsPo;


/**
 * 单元-商品映射表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LauUnitShopGoodsRecord extends UpdatableRecordImpl<LauUnitShopGoodsRecord> implements Record11<Integer, Integer, Long, Integer, Integer, Integer, Integer, Integer, Timestamp, Timestamp, Integer> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>lau_unit_shop_goods.id</code>.
     */
    public void setId(Integer value) {
        set(0, value);
    }

    /**
     * Getter for <code>lau_unit_shop_goods.id</code>.
     */
    public Integer getId() {
        return (Integer) get(0);
    }

    /**
     * Setter for <code>lau_unit_shop_goods.account_id</code>. 账号ID
     */
    public void setAccountId(Integer value) {
        set(1, value);
    }

    /**
     * Getter for <code>lau_unit_shop_goods.account_id</code>. 账号ID
     */
    public Integer getAccountId() {
        return (Integer) get(1);
    }

    /**
     * Setter for <code>lau_unit_shop_goods.mid</code>. 主站mid
     */
    public void setMid(Long value) {
        set(2, value);
    }

    /**
     * Getter for <code>lau_unit_shop_goods.mid</code>. 主站mid
     */
    public Long getMid() {
        return (Long) get(2);
    }

    /**
     * Setter for <code>lau_unit_shop_goods.unit_id</code>. 单元ID
     */
    public void setUnitId(Integer value) {
        set(3, value);
    }

    /**
     * Getter for <code>lau_unit_shop_goods.unit_id</code>. 单元ID
     */
    public Integer getUnitId() {
        return (Integer) get(3);
    }

    /**
     * Setter for <code>lau_unit_shop_goods.shop_id</code>. 店铺ID
     */
    public void setShopId(Integer value) {
        set(4, value);
    }

    /**
     * Getter for <code>lau_unit_shop_goods.shop_id</code>. 店铺ID
     */
    public Integer getShopId() {
        return (Integer) get(4);
    }

    /**
     * Setter for <code>lau_unit_shop_goods.goods_id</code>. 商品ID
     */
    public void setGoodsId(Integer value) {
        set(5, value);
    }

    /**
     * Getter for <code>lau_unit_shop_goods.goods_id</code>. 商品ID
     */
    public Integer getGoodsId() {
        return (Integer) get(5);
    }

    /**
     * Setter for <code>lau_unit_shop_goods.product_id</code>. DPA商品ID
     */
    public void setProductId(Integer value) {
        set(6, value);
    }

    /**
     * Getter for <code>lau_unit_shop_goods.product_id</code>. DPA商品ID
     */
    public Integer getProductId() {
        return (Integer) get(6);
    }

    /**
     * Setter for <code>lau_unit_shop_goods.product_type</code>. 产品类型：1-商品 2-票务
     * 3-cms落地页 4-小程序
     */
    public void setProductType(Integer value) {
        set(7, value);
    }

    /**
     * Getter for <code>lau_unit_shop_goods.product_type</code>. 产品类型：1-商品 2-票务
     * 3-cms落地页 4-小程序
     */
    public Integer getProductType() {
        return (Integer) get(7);
    }

    /**
     * Setter for <code>lau_unit_shop_goods.ctime</code>. 创建时间
     */
    public void setCtime(Timestamp value) {
        set(8, value);
    }

    /**
     * Getter for <code>lau_unit_shop_goods.ctime</code>. 创建时间
     */
    public Timestamp getCtime() {
        return (Timestamp) get(8);
    }

    /**
     * Setter for <code>lau_unit_shop_goods.mtime</code>. 修改时间
     */
    public void setMtime(Timestamp value) {
        set(9, value);
    }

    /**
     * Getter for <code>lau_unit_shop_goods.mtime</code>. 修改时间
     */
    public Timestamp getMtime() {
        return (Timestamp) get(9);
    }

    /**
     * Setter for <code>lau_unit_shop_goods.is_deleted</code>. 软删除 0-有效，1-删除
     */
    public void setIsDeleted(Integer value) {
        set(10, value);
    }

    /**
     * Getter for <code>lau_unit_shop_goods.is_deleted</code>. 软删除 0-有效，1-删除
     */
    public Integer getIsDeleted() {
        return (Integer) get(10);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Integer> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record11 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row11<Integer, Integer, Long, Integer, Integer, Integer, Integer, Integer, Timestamp, Timestamp, Integer> fieldsRow() {
        return (Row11) super.fieldsRow();
    }

    @Override
    public Row11<Integer, Integer, Long, Integer, Integer, Integer, Integer, Integer, Timestamp, Timestamp, Integer> valuesRow() {
        return (Row11) super.valuesRow();
    }

    @Override
    public Field<Integer> field1() {
        return TLauUnitShopGoods.LAU_UNIT_SHOP_GOODS.ID;
    }

    @Override
    public Field<Integer> field2() {
        return TLauUnitShopGoods.LAU_UNIT_SHOP_GOODS.ACCOUNT_ID;
    }

    @Override
    public Field<Long> field3() {
        return TLauUnitShopGoods.LAU_UNIT_SHOP_GOODS.MID;
    }

    @Override
    public Field<Integer> field4() {
        return TLauUnitShopGoods.LAU_UNIT_SHOP_GOODS.UNIT_ID;
    }

    @Override
    public Field<Integer> field5() {
        return TLauUnitShopGoods.LAU_UNIT_SHOP_GOODS.SHOP_ID;
    }

    @Override
    public Field<Integer> field6() {
        return TLauUnitShopGoods.LAU_UNIT_SHOP_GOODS.GOODS_ID;
    }

    @Override
    public Field<Integer> field7() {
        return TLauUnitShopGoods.LAU_UNIT_SHOP_GOODS.PRODUCT_ID;
    }

    @Override
    public Field<Integer> field8() {
        return TLauUnitShopGoods.LAU_UNIT_SHOP_GOODS.PRODUCT_TYPE;
    }

    @Override
    public Field<Timestamp> field9() {
        return TLauUnitShopGoods.LAU_UNIT_SHOP_GOODS.CTIME;
    }

    @Override
    public Field<Timestamp> field10() {
        return TLauUnitShopGoods.LAU_UNIT_SHOP_GOODS.MTIME;
    }

    @Override
    public Field<Integer> field11() {
        return TLauUnitShopGoods.LAU_UNIT_SHOP_GOODS.IS_DELETED;
    }

    @Override
    public Integer component1() {
        return getId();
    }

    @Override
    public Integer component2() {
        return getAccountId();
    }

    @Override
    public Long component3() {
        return getMid();
    }

    @Override
    public Integer component4() {
        return getUnitId();
    }

    @Override
    public Integer component5() {
        return getShopId();
    }

    @Override
    public Integer component6() {
        return getGoodsId();
    }

    @Override
    public Integer component7() {
        return getProductId();
    }

    @Override
    public Integer component8() {
        return getProductType();
    }

    @Override
    public Timestamp component9() {
        return getCtime();
    }

    @Override
    public Timestamp component10() {
        return getMtime();
    }

    @Override
    public Integer component11() {
        return getIsDeleted();
    }

    @Override
    public Integer value1() {
        return getId();
    }

    @Override
    public Integer value2() {
        return getAccountId();
    }

    @Override
    public Long value3() {
        return getMid();
    }

    @Override
    public Integer value4() {
        return getUnitId();
    }

    @Override
    public Integer value5() {
        return getShopId();
    }

    @Override
    public Integer value6() {
        return getGoodsId();
    }

    @Override
    public Integer value7() {
        return getProductId();
    }

    @Override
    public Integer value8() {
        return getProductType();
    }

    @Override
    public Timestamp value9() {
        return getCtime();
    }

    @Override
    public Timestamp value10() {
        return getMtime();
    }

    @Override
    public Integer value11() {
        return getIsDeleted();
    }

    @Override
    public LauUnitShopGoodsRecord value1(Integer value) {
        setId(value);
        return this;
    }

    @Override
    public LauUnitShopGoodsRecord value2(Integer value) {
        setAccountId(value);
        return this;
    }

    @Override
    public LauUnitShopGoodsRecord value3(Long value) {
        setMid(value);
        return this;
    }

    @Override
    public LauUnitShopGoodsRecord value4(Integer value) {
        setUnitId(value);
        return this;
    }

    @Override
    public LauUnitShopGoodsRecord value5(Integer value) {
        setShopId(value);
        return this;
    }

    @Override
    public LauUnitShopGoodsRecord value6(Integer value) {
        setGoodsId(value);
        return this;
    }

    @Override
    public LauUnitShopGoodsRecord value7(Integer value) {
        setProductId(value);
        return this;
    }

    @Override
    public LauUnitShopGoodsRecord value8(Integer value) {
        setProductType(value);
        return this;
    }

    @Override
    public LauUnitShopGoodsRecord value9(Timestamp value) {
        setCtime(value);
        return this;
    }

    @Override
    public LauUnitShopGoodsRecord value10(Timestamp value) {
        setMtime(value);
        return this;
    }

    @Override
    public LauUnitShopGoodsRecord value11(Integer value) {
        setIsDeleted(value);
        return this;
    }

    @Override
    public LauUnitShopGoodsRecord values(Integer value1, Integer value2, Long value3, Integer value4, Integer value5, Integer value6, Integer value7, Integer value8, Timestamp value9, Timestamp value10, Integer value11) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached LauUnitShopGoodsRecord
     */
    public LauUnitShopGoodsRecord() {
        super(TLauUnitShopGoods.LAU_UNIT_SHOP_GOODS);
    }

    /**
     * Create a detached, initialised LauUnitShopGoodsRecord
     */
    public LauUnitShopGoodsRecord(Integer id, Integer accountId, Long mid, Integer unitId, Integer shopId, Integer goodsId, Integer productId, Integer productType, Timestamp ctime, Timestamp mtime, Integer isDeleted) {
        super(TLauUnitShopGoods.LAU_UNIT_SHOP_GOODS);

        setId(id);
        setAccountId(accountId);
        setMid(mid);
        setUnitId(unitId);
        setShopId(shopId);
        setGoodsId(goodsId);
        setProductId(productId);
        setProductType(productType);
        setCtime(ctime);
        setMtime(mtime);
        setIsDeleted(isDeleted);
    }

    /**
     * Create a detached, initialised LauUnitShopGoodsRecord
     */
    public LauUnitShopGoodsRecord(LauUnitShopGoodsPo value) {
        super(TLauUnitShopGoods.LAU_UNIT_SHOP_GOODS);

        if (value != null) {
            setId(value.getId());
            setAccountId(value.getAccountId());
            setMid(value.getMid());
            setUnitId(value.getUnitId());
            setShopId(value.getShopId());
            setGoodsId(value.getGoodsId());
            setProductId(value.getProductId());
            setProductType(value.getProductType());
            setCtime(value.getCtime());
            setMtime(value.getMtime());
            setIsDeleted(value.getIsDeleted());
        }
    }
}
