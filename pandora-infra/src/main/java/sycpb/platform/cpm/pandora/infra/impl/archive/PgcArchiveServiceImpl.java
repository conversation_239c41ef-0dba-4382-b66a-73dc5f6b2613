package sycpb.platform.cpm.pandora.infra.impl.archive;

import java.util.*;

import com.bapis.ott.service.*;
import com.bapis.pgc.service.season.episode.EpisodeGrpc;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import pleiades.venus.starter.rpc.client.RPCClient;
import sycpb.platform.cpm.pandora.infra.api.archive.*;
import sycpb.platform.cpm.pandora.infra.enums.ArchiveState;
import com.bapis.pgc.service.season.episode.EpisodeInfoProto;
import com.bapis.pgc.service.season.episode.AidInfosReply;
import com.bapis.pgc.service.season.episode.EpAidReq;
import sycpb.platform.cpm.pandora.infra.enums.ErrorCodeEnum;
import sycpb.platform.cpm.pandora.infra.err.PandoraAssert;

import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class PgcArchiveServiceImpl implements IPgcArchiveService {

    @RPCClient("season.service")
    private EpisodeGrpc.EpisodeBlockingStub episodeBlockingStub;

    @RPCClient("ott.service")
    private OTTServiceGrpc.OTTServiceBlockingStub ottServiceBlockingStub;

    @Autowired
    private IBiliArchiveService biliArchiveService;


    private static final int MAX_PGC_ARCS_PASSED_REQUEST_PAGE_SIZE = 50;

    @Override
    public Map<Long, PgcArchiveBo> fetchMap(Collection<Long> x) {
        Map<Long, BiliArchiveBo> biliArchiveMap = biliArchiveService.fetchMap(x);
        if (CollectionUtils.isEmpty(biliArchiveMap.values())) {
            return Collections.emptyMap();
        }

        return doGetPgcArchivesByBiliArcBoMap(biliArchiveMap);
    }

    private Map<Long, PgcArchiveBo> doGetPgcArchivesByBiliArcBoMap(Map<Long, BiliArchiveBo> biliArchiveBoMap) {
        List<Long> pgcAids = biliArchiveBoMap
                .values()
                .stream()
                .filter(BiliArchiveBo::getIsPgc)
                .filter(archive -> ArchiveState.VALID_STATE_CODE_SET.contains(archive.getState()))
                .map(BiliArchiveBo::getAvid)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(pgcAids)) {
            return Collections.emptyMap();
        }
        log.info("get episode info by aids :{}", pgcAids);
        List<List<Long>> aidPartition = Lists.partition(pgcAids, MAX_PGC_ARCS_PASSED_REQUEST_PAGE_SIZE);
        return aidPartition
                .stream()
                .flatMap(list -> doGetEpisodeInfosByAids(list).entrySet().stream())
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> BiliArchiveMapper.MAPPER.toPgcArchiveBo(biliArchiveBoMap.get(entry.getKey()), entry.getValue())));

    }

    public Map<Long, EpisodeInfoProto> doGetEpisodeInfosByAids(List<Long> pgcAids) {
        if (CollectionUtils.isEmpty(pgcAids)) {
            return Collections.emptyMap();
        }
        PandoraAssert.isTrue(pgcAids.size() <= 100, "pgc archive aids should loe 100",
                ErrorCodeEnum.DomainType.RESOURCE, ErrorCodeEnum.ErrorType.BIZ_ERR, ErrorCodeEnum.SubCode.PARAM_INVALID);
        AidInfosReply aidInfosReply = AidInfosReply.getDefaultInstance();

        try {
            aidInfosReply = this.episodeBlockingStub.listByAids2(EpAidReq.newBuilder().addAllAid2S(pgcAids).build());
        } catch (Exception var4) {
            log.error("get episode info by aids error, aids :{}, exception :{}", pgcAids, var4.getMessage());
        }

        return aidInfosReply.getInfosMap();
    }

    /**
     *
     * @param ids
     * @param mediaTp 1 PGC,Id 对应 seasonId;2 UGC ,Id 对应 archiveId; 3 PGC Video, id对应episodeId; 4 UGC Video,id对应archive 的 cId
     * @return
     */
    public Map<Long, Boolean> isOttByMediaTp(Collection<Long> ids, MediaTp mediaTp) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyMap();
        }

        log.info("isOttFrom, Ids :{}", ids);
        Map<Long, Boolean> validOttMap;
        try {
            List<Media> items = ids
                    .stream()
                    .map(id -> Media.newBuilder().setId(id).setType(mediaTp).build())
                    .collect(Collectors.toList());
            MediaStatusBatchReq req = MediaStatusBatchReq.newBuilder().addAllItem(items).build();
            MediaStatusBatchReply reply = ottServiceBlockingStub.mediaStatusBatch(req);
            validOttMap = reply.getResultMap()
                    .values()
                    .stream()
                    .collect(Collectors.toMap(mediaStatus -> mediaStatus.getId(), mediaStatus -> (mediaStatus.getStatus() == MediaStatusTp.ImportPass)));
        } catch (Exception e) {
            log.error("mediaStatusBatch error", e);
            validOttMap = new HashMap<>();
        }
        return validOttMap;
    }
}
