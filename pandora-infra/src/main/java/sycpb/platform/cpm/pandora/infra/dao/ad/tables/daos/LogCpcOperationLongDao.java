/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad.tables.daos;


import java.sql.Timestamp;
import java.util.List;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import sycpb.platform.cpm.pandora.infra.dao.ad.tables.TLogCpcOperationLong;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.pojos.LogCpcOperationLongPo;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.records.LogCpcOperationLongRecord;


/**
 * cpc操作日志表(objId long)
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
@Repository
public class LogCpcOperationLongDao extends DAOImpl<LogCpcOperationLongRecord, LogCpcOperationLongPo, Long> {

    /**
     * Create a new LogCpcOperationLongDao without any configuration
     */
    public LogCpcOperationLongDao() {
        super(TLogCpcOperationLong.LOG_CPC_OPERATION_LONG, LogCpcOperationLongPo.class);
    }

    /**
     * Create a new LogCpcOperationLongDao with an attached configuration
     */
    @Autowired
    public LogCpcOperationLongDao(Configuration configuration) {
        super(TLogCpcOperationLong.LOG_CPC_OPERATION_LONG, LogCpcOperationLongPo.class, configuration);
    }

    @Override
    public Long getId(LogCpcOperationLongPo object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LogCpcOperationLongPo> fetchRangeOfId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(TLogCpcOperationLong.LOG_CPC_OPERATION_LONG.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<LogCpcOperationLongPo> fetchById(Long... values) {
        return fetch(TLogCpcOperationLong.LOG_CPC_OPERATION_LONG.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public LogCpcOperationLongPo fetchOneById(Long value) {
        return fetchOne(TLogCpcOperationLong.LOG_CPC_OPERATION_LONG.ID, value);
    }

    /**
     * Fetch records that have <code>obj_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LogCpcOperationLongPo> fetchRangeOfObjId(Long lowerInclusive, Long upperInclusive) {
        return fetchRange(TLogCpcOperationLong.LOG_CPC_OPERATION_LONG.OBJ_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>obj_id IN (values)</code>
     */
    public List<LogCpcOperationLongPo> fetchByObjId(Long... values) {
        return fetch(TLogCpcOperationLong.LOG_CPC_OPERATION_LONG.OBJ_ID, values);
    }

    /**
     * Fetch records that have <code>table_name BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LogCpcOperationLongPo> fetchRangeOfTableName(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLogCpcOperationLong.LOG_CPC_OPERATION_LONG.TABLE_NAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>table_name IN (values)</code>
     */
    public List<LogCpcOperationLongPo> fetchByTableName(String... values) {
        return fetch(TLogCpcOperationLong.LOG_CPC_OPERATION_LONG.TABLE_NAME, values);
    }

    /**
     * Fetch records that have <code>type BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LogCpcOperationLongPo> fetchRangeOfType(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLogCpcOperationLong.LOG_CPC_OPERATION_LONG.TYPE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>type IN (values)</code>
     */
    public List<LogCpcOperationLongPo> fetchByType(Integer... values) {
        return fetch(TLogCpcOperationLong.LOG_CPC_OPERATION_LONG.TYPE, values);
    }

    /**
     * Fetch records that have <code>operator_username BETWEEN lowerInclusive
     * AND upperInclusive</code>
     */
    public List<LogCpcOperationLongPo> fetchRangeOfOperatorUsername(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLogCpcOperationLong.LOG_CPC_OPERATION_LONG.OPERATOR_USERNAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>operator_username IN (values)</code>
     */
    public List<LogCpcOperationLongPo> fetchByOperatorUsername(String... values) {
        return fetch(TLogCpcOperationLong.LOG_CPC_OPERATION_LONG.OPERATOR_USERNAME, values);
    }

    /**
     * Fetch records that have <code>operator_type BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LogCpcOperationLongPo> fetchRangeOfOperatorType(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLogCpcOperationLong.LOG_CPC_OPERATION_LONG.OPERATOR_TYPE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>operator_type IN (values)</code>
     */
    public List<LogCpcOperationLongPo> fetchByOperatorType(Integer... values) {
        return fetch(TLogCpcOperationLong.LOG_CPC_OPERATION_LONG.OPERATOR_TYPE, values);
    }

    /**
     * Fetch records that have <code>value BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LogCpcOperationLongPo> fetchRangeOfValue(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLogCpcOperationLong.LOG_CPC_OPERATION_LONG.VALUE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>value IN (values)</code>
     */
    public List<LogCpcOperationLongPo> fetchByValue(String... values) {
        return fetch(TLogCpcOperationLong.LOG_CPC_OPERATION_LONG.VALUE, values);
    }

    /**
     * Fetch records that have <code>ctime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LogCpcOperationLongPo> fetchRangeOfCtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TLogCpcOperationLong.LOG_CPC_OPERATION_LONG.CTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>ctime IN (values)</code>
     */
    public List<LogCpcOperationLongPo> fetchByCtime(Timestamp... values) {
        return fetch(TLogCpcOperationLong.LOG_CPC_OPERATION_LONG.CTIME, values);
    }

    /**
     * Fetch records that have <code>mtime BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LogCpcOperationLongPo> fetchRangeOfMtime(Timestamp lowerInclusive, Timestamp upperInclusive) {
        return fetchRange(TLogCpcOperationLong.LOG_CPC_OPERATION_LONG.MTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>mtime IN (values)</code>
     */
    public List<LogCpcOperationLongPo> fetchByMtime(Timestamp... values) {
        return fetch(TLogCpcOperationLong.LOG_CPC_OPERATION_LONG.MTIME, values);
    }

    /**
     * Fetch records that have <code>is_deleted BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LogCpcOperationLongPo> fetchRangeOfIsDeleted(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLogCpcOperationLong.LOG_CPC_OPERATION_LONG.IS_DELETED, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_deleted IN (values)</code>
     */
    public List<LogCpcOperationLongPo> fetchByIsDeleted(Integer... values) {
        return fetch(TLogCpcOperationLong.LOG_CPC_OPERATION_LONG.IS_DELETED, values);
    }

    /**
     * Fetch records that have <code>bilibili_username BETWEEN lowerInclusive
     * AND upperInclusive</code>
     */
    public List<LogCpcOperationLongPo> fetchRangeOfBilibiliUsername(String lowerInclusive, String upperInclusive) {
        return fetchRange(TLogCpcOperationLong.LOG_CPC_OPERATION_LONG.BILIBILI_USERNAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>bilibili_username IN (values)</code>
     */
    public List<LogCpcOperationLongPo> fetchByBilibiliUsername(String... values) {
        return fetch(TLogCpcOperationLong.LOG_CPC_OPERATION_LONG.BILIBILI_USERNAME, values);
    }

    /**
     * Fetch records that have <code>account_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LogCpcOperationLongPo> fetchRangeOfAccountId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLogCpcOperationLong.LOG_CPC_OPERATION_LONG.ACCOUNT_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>account_id IN (values)</code>
     */
    public List<LogCpcOperationLongPo> fetchByAccountId(Integer... values) {
        return fetch(TLogCpcOperationLong.LOG_CPC_OPERATION_LONG.ACCOUNT_ID, values);
    }

    /**
     * Fetch records that have <code>system_type BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LogCpcOperationLongPo> fetchRangeOfSystemType(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLogCpcOperationLong.LOG_CPC_OPERATION_LONG.SYSTEM_TYPE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>system_type IN (values)</code>
     */
    public List<LogCpcOperationLongPo> fetchBySystemType(Integer... values) {
        return fetch(TLogCpcOperationLong.LOG_CPC_OPERATION_LONG.SYSTEM_TYPE, values);
    }

    /**
     * Fetch records that have <code>sales_type BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<LogCpcOperationLongPo> fetchRangeOfSalesType(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(TLogCpcOperationLong.LOG_CPC_OPERATION_LONG.SALES_TYPE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>sales_type IN (values)</code>
     */
    public List<LogCpcOperationLongPo> fetchBySalesType(Integer... values) {
        return fetch(TLogCpcOperationLong.LOG_CPC_OPERATION_LONG.SALES_TYPE, values);
    }
}
