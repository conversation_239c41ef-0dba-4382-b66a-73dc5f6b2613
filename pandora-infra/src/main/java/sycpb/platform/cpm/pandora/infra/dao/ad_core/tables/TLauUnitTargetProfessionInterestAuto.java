/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables;


import java.sql.Timestamp;

import org.jooq.Field;
import org.jooq.Identity;
import org.jooq.Name;
import org.jooq.Row8;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.Internal;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad_core.DefaultSchema;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.records.LauUnitTargetProfessionInterestAutoRecord;


/**
 * 单元-行业优选离线表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class TLauUnitTargetProfessionInterestAuto extends TableImpl<LauUnitTargetProfessionInterestAutoRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of
     * <code>lau_unit_target_profession_interest_auto</code>
     */
    public static final TLauUnitTargetProfessionInterestAuto LAU_UNIT_TARGET_PROFESSION_INTEREST_AUTO = new TLauUnitTargetProfessionInterestAuto();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<LauUnitTargetProfessionInterestAutoRecord> getRecordType() {
        return LauUnitTargetProfessionInterestAutoRecord.class;
    }

    /**
     * The column <code>lau_unit_target_profession_interest_auto.id</code>.
     */
    public final TableField<LauUnitTargetProfessionInterestAutoRecord, Integer> ID = createField(DSL.name("id"), SQLDataType.INTEGER.nullable(false).identity(true), this, "");

    /**
     * The column
     * <code>lau_unit_target_profession_interest_auto.account_id</code>. 账户id
     */
    public final TableField<LauUnitTargetProfessionInterestAutoRecord, Integer> ACCOUNT_ID = createField(DSL.name("account_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "账户id");

    /**
     * The column <code>lau_unit_target_profession_interest_auto.unit_id</code>.
     * 单元ID
     */
    public final TableField<LauUnitTargetProfessionInterestAutoRecord, Integer> UNIT_ID = createField(DSL.name("unit_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "单元ID");

    /**
     * The column
     * <code>lau_unit_target_profession_interest_auto.crowd_id</code>. 人群包ID
     */
    public final TableField<LauUnitTargetProfessionInterestAutoRecord, Integer> CROWD_ID = createField(DSL.name("crowd_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "人群包ID");

    /**
     * The column <code>lau_unit_target_profession_interest_auto.ctime</code>.
     * 创建时间
     */
    public final TableField<LauUnitTargetProfessionInterestAutoRecord, Timestamp> CTIME = createField(DSL.name("ctime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "创建时间");

    /**
     * The column <code>lau_unit_target_profession_interest_auto.mtime</code>.
     * 修改时间
     */
    public final TableField<LauUnitTargetProfessionInterestAutoRecord, Timestamp> MTIME = createField(DSL.name("mtime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "修改时间");

    /**
     * The column
     * <code>lau_unit_target_profession_interest_auto.is_deleted</code>. 软删除
     * 0-有效，1-删除
     */
    public final TableField<LauUnitTargetProfessionInterestAutoRecord, Integer> IS_DELETED = createField(DSL.name("is_deleted"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "软删除 0-有效，1-删除");

    /**
     * The column
     * <code>lau_unit_target_profession_interest_auto.is_comand</code>. 是否处理过
     * 0-未处理，1-处理过
     */
    public final TableField<LauUnitTargetProfessionInterestAutoRecord, Integer> IS_COMAND = createField(DSL.name("is_comand"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "是否处理过 0-未处理，1-处理过");

    private TLauUnitTargetProfessionInterestAuto(Name alias, Table<LauUnitTargetProfessionInterestAutoRecord> aliased) {
        this(alias, aliased, null);
    }

    private TLauUnitTargetProfessionInterestAuto(Name alias, Table<LauUnitTargetProfessionInterestAutoRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment("单元-行业优选离线表"), TableOptions.table());
    }

    /**
     * Create an aliased <code>lau_unit_target_profession_interest_auto</code>
     * table reference
     */
    public TLauUnitTargetProfessionInterestAuto(String alias) {
        this(DSL.name(alias), LAU_UNIT_TARGET_PROFESSION_INTEREST_AUTO);
    }

    /**
     * Create an aliased <code>lau_unit_target_profession_interest_auto</code>
     * table reference
     */
    public TLauUnitTargetProfessionInterestAuto(Name alias) {
        this(alias, LAU_UNIT_TARGET_PROFESSION_INTEREST_AUTO);
    }

    /**
     * Create a <code>lau_unit_target_profession_interest_auto</code> table
     * reference
     */
    public TLauUnitTargetProfessionInterestAuto() {
        this(DSL.name("lau_unit_target_profession_interest_auto"), null);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : DefaultSchema.DEFAULT_SCHEMA;
    }

    @Override
    public Identity<LauUnitTargetProfessionInterestAutoRecord, Integer> getIdentity() {
        return (Identity<LauUnitTargetProfessionInterestAutoRecord, Integer>) super.getIdentity();
    }

    @Override
    public UniqueKey<LauUnitTargetProfessionInterestAutoRecord> getPrimaryKey() {
        return Internal.createUniqueKey(TLauUnitTargetProfessionInterestAuto.LAU_UNIT_TARGET_PROFESSION_INTEREST_AUTO, DSL.name("KEY_lau_unit_target_profession_interest_auto_PRIMARY"), new TableField[] { TLauUnitTargetProfessionInterestAuto.LAU_UNIT_TARGET_PROFESSION_INTEREST_AUTO.ID }, true);
    }

    @Override
    public TLauUnitTargetProfessionInterestAuto as(String alias) {
        return new TLauUnitTargetProfessionInterestAuto(DSL.name(alias), this);
    }

    @Override
    public TLauUnitTargetProfessionInterestAuto as(Name alias) {
        return new TLauUnitTargetProfessionInterestAuto(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public TLauUnitTargetProfessionInterestAuto rename(String name) {
        return new TLauUnitTargetProfessionInterestAuto(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public TLauUnitTargetProfessionInterestAuto rename(Name name) {
        return new TLauUnitTargetProfessionInterestAuto(name, null);
    }

    // -------------------------------------------------------------------------
    // Row8 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row8<Integer, Integer, Integer, Integer, Timestamp, Timestamp, Integer, Integer> fieldsRow() {
        return (Row8) super.fieldsRow();
    }
}
