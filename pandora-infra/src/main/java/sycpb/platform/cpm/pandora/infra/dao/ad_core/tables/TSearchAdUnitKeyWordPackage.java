/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables;


import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import org.jooq.Field;
import org.jooq.Identity;
import org.jooq.Name;
import org.jooq.Row8;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.Internal;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad_core.DefaultSchema;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.records.SearchAdUnitKeyWordPackageRecord;


/**
 * 单元关键词表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class TSearchAdUnitKeyWordPackage extends TableImpl<SearchAdUnitKeyWordPackageRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>search_ad_unit_key_word_package</code>
     */
    public static final TSearchAdUnitKeyWordPackage SEARCH_AD_UNIT_KEY_WORD_PACKAGE = new TSearchAdUnitKeyWordPackage();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<SearchAdUnitKeyWordPackageRecord> getRecordType() {
        return SearchAdUnitKeyWordPackageRecord.class;
    }

    /**
     * The column <code>search_ad_unit_key_word_package.id</code>. 自增ID
     */
    public final TableField<SearchAdUnitKeyWordPackageRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false).identity(true), this, "自增ID");

    /**
     * The column <code>search_ad_unit_key_word_package.package_md5</code>.
     * 词包md5
     */
    public final TableField<SearchAdUnitKeyWordPackageRecord, String> PACKAGE_MD5 = createField(DSL.name("package_md5"), SQLDataType.VARCHAR(40).nullable(false).defaultValue(DSL.inline("''", SQLDataType.VARCHAR)), this, "词包md5");

    /**
     * The column <code>search_ad_unit_key_word_package.key_word</code>. 关键词
     */
    public final TableField<SearchAdUnitKeyWordPackageRecord, String> KEY_WORD = createField(DSL.name("key_word"), SQLDataType.VARCHAR(256).nullable(false).defaultValue(DSL.inline("''", SQLDataType.VARCHAR)), this, "关键词");

    /**
     * The column <code>search_ad_unit_key_word_package.ctime</code>. 添加时间
     */
    public final TableField<SearchAdUnitKeyWordPackageRecord, Timestamp> CTIME = createField(DSL.name("ctime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "添加时间");

    /**
     * The column <code>search_ad_unit_key_word_package.mtime</code>. 更新时间
     */
    public final TableField<SearchAdUnitKeyWordPackageRecord, Timestamp> MTIME = createField(DSL.name("mtime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "更新时间");

    /**
     * The column <code>search_ad_unit_key_word_package.keyword_md5</code>.
     * 关键词md5
     */
    public final TableField<SearchAdUnitKeyWordPackageRecord, String> KEYWORD_MD5 = createField(DSL.name("keyword_md5"), SQLDataType.VARCHAR(40).nullable(false).defaultValue(DSL.inline("''", SQLDataType.VARCHAR)), this, "关键词md5");

    /**
     * The column <code>search_ad_unit_key_word_package.source</code>. 0-商业召回
     * 1-主站sug
     */
    public final TableField<SearchAdUnitKeyWordPackageRecord, Integer> SOURCE = createField(DSL.name("source"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "0-商业召回 1-主站sug");

    /**
     * The column <code>search_ad_unit_key_word_package.search_word</code>. 搜索词
     */
    public final TableField<SearchAdUnitKeyWordPackageRecord, String> SEARCH_WORD = createField(DSL.name("search_word"), SQLDataType.VARCHAR(256).nullable(false).defaultValue(DSL.inline("''", SQLDataType.VARCHAR)), this, "搜索词");

    private TSearchAdUnitKeyWordPackage(Name alias, Table<SearchAdUnitKeyWordPackageRecord> aliased) {
        this(alias, aliased, null);
    }

    private TSearchAdUnitKeyWordPackage(Name alias, Table<SearchAdUnitKeyWordPackageRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment("单元关键词表"), TableOptions.table());
    }

    /**
     * Create an aliased <code>search_ad_unit_key_word_package</code> table
     * reference
     */
    public TSearchAdUnitKeyWordPackage(String alias) {
        this(DSL.name(alias), SEARCH_AD_UNIT_KEY_WORD_PACKAGE);
    }

    /**
     * Create an aliased <code>search_ad_unit_key_word_package</code> table
     * reference
     */
    public TSearchAdUnitKeyWordPackage(Name alias) {
        this(alias, SEARCH_AD_UNIT_KEY_WORD_PACKAGE);
    }

    /**
     * Create a <code>search_ad_unit_key_word_package</code> table reference
     */
    public TSearchAdUnitKeyWordPackage() {
        this(DSL.name("search_ad_unit_key_word_package"), null);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : DefaultSchema.DEFAULT_SCHEMA;
    }

    @Override
    public Identity<SearchAdUnitKeyWordPackageRecord, Long> getIdentity() {
        return (Identity<SearchAdUnitKeyWordPackageRecord, Long>) super.getIdentity();
    }

    @Override
    public UniqueKey<SearchAdUnitKeyWordPackageRecord> getPrimaryKey() {
        return Internal.createUniqueKey(TSearchAdUnitKeyWordPackage.SEARCH_AD_UNIT_KEY_WORD_PACKAGE, DSL.name("KEY_search_ad_unit_key_word_package_PRIMARY"), new TableField[] { TSearchAdUnitKeyWordPackage.SEARCH_AD_UNIT_KEY_WORD_PACKAGE.ID }, true);
    }

    @Override
    public List<UniqueKey<SearchAdUnitKeyWordPackageRecord>> getUniqueKeys() {
        return Arrays.asList(
            Internal.createUniqueKey(TSearchAdUnitKeyWordPackage.SEARCH_AD_UNIT_KEY_WORD_PACKAGE, DSL.name("KEY_search_ad_unit_key_word_package_uk_package_md5_keyword_md5"), new TableField[] { TSearchAdUnitKeyWordPackage.SEARCH_AD_UNIT_KEY_WORD_PACKAGE.PACKAGE_MD5, TSearchAdUnitKeyWordPackage.SEARCH_AD_UNIT_KEY_WORD_PACKAGE.KEY_WORD }, true)
        );
    }

    @Override
    public TSearchAdUnitKeyWordPackage as(String alias) {
        return new TSearchAdUnitKeyWordPackage(DSL.name(alias), this);
    }

    @Override
    public TSearchAdUnitKeyWordPackage as(Name alias) {
        return new TSearchAdUnitKeyWordPackage(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public TSearchAdUnitKeyWordPackage rename(String name) {
        return new TSearchAdUnitKeyWordPackage(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public TSearchAdUnitKeyWordPackage rename(Name name) {
        return new TSearchAdUnitKeyWordPackage(name, null);
    }

    // -------------------------------------------------------------------------
    // Row8 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row8<Long, String, String, Timestamp, Timestamp, String, Integer, String> fieldsRow() {
        return (Row8) super.fieldsRow();
    }
}
