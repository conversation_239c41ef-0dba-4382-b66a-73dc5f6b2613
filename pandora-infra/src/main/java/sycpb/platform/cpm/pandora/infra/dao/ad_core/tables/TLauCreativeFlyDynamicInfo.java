/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables;


import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import org.jooq.Field;
import org.jooq.Identity;
import org.jooq.Name;
import org.jooq.Row15;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.Internal;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;

import sycpb.platform.cpm.pandora.infra.dao.ad_core.DefaultSchema;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.records.LauCreativeFlyDynamicInfoRecord;


/**
 * 起飞创意的动态信息（引擎用）
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class TLauCreativeFlyDynamicInfo extends TableImpl<LauCreativeFlyDynamicInfoRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>lau_creative_fly_dynamic_info</code>
     */
    public static final TLauCreativeFlyDynamicInfo LAU_CREATIVE_FLY_DYNAMIC_INFO = new TLauCreativeFlyDynamicInfo();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<LauCreativeFlyDynamicInfoRecord> getRecordType() {
        return LauCreativeFlyDynamicInfoRecord.class;
    }

    /**
     * The column <code>lau_creative_fly_dynamic_info.id</code>. 自增ID
     */
    public final TableField<LauCreativeFlyDynamicInfoRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false).identity(true), this, "自增ID");

    /**
     * The column <code>lau_creative_fly_dynamic_info.account_id</code>. 账号ID
     */
    public final TableField<LauCreativeFlyDynamicInfoRecord, Integer> ACCOUNT_ID = createField(DSL.name("account_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "账号ID");

    /**
     * The column <code>lau_creative_fly_dynamic_info.unit_id</code>. 单元ID
     */
    public final TableField<LauCreativeFlyDynamicInfoRecord, Integer> UNIT_ID = createField(DSL.name("unit_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "单元ID");

    /**
     * The column <code>lau_creative_fly_dynamic_info.creative_id</code>. 创意ID
     */
    public final TableField<LauCreativeFlyDynamicInfoRecord, Integer> CREATIVE_ID = createField(DSL.name("creative_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "创意ID");

    /**
     * The column <code>lau_creative_fly_dynamic_info.dynamic_id</code>. 动态id
     */
    public final TableField<LauCreativeFlyDynamicInfoRecord, Long> DYNAMIC_ID = createField(DSL.name("dynamic_id"), SQLDataType.BIGINT.nullable(false).defaultValue(DSL.inline("0", SQLDataType.BIGINT)), this, "动态id");

    /**
     * The column <code>lau_creative_fly_dynamic_info.like_count</code>.
     * 点赞数（字符串）
     */
    public final TableField<LauCreativeFlyDynamicInfoRecord, String> LIKE_COUNT = createField(DSL.name("like_count"), SQLDataType.VARCHAR(16).nullable(false).defaultValue(DSL.inline("''", SQLDataType.VARCHAR)), this, "点赞数（字符串）");

    /**
     * The column <code>lau_creative_fly_dynamic_info.nickname</code>. 昵称
     */
    public final TableField<LauCreativeFlyDynamicInfoRecord, String> NICKNAME = createField(DSL.name("nickname"), SQLDataType.VARCHAR(32).nullable(false).defaultValue(DSL.inline("''", SQLDataType.VARCHAR)), this, "昵称");

    /**
     * The column <code>lau_creative_fly_dynamic_info.is_deleted</code>.
     * 软删除，0是有效，1是删除
     */
    public final TableField<LauCreativeFlyDynamicInfoRecord, Integer> IS_DELETED = createField(DSL.name("is_deleted"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "软删除，0是有效，1是删除");

    /**
     * The column <code>lau_creative_fly_dynamic_info.ctime</code>. 添加时间
     */
    public final TableField<LauCreativeFlyDynamicInfoRecord, Timestamp> CTIME = createField(DSL.name("ctime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "添加时间");

    /**
     * The column <code>lau_creative_fly_dynamic_info.mtime</code>. 更新时间
     */
    public final TableField<LauCreativeFlyDynamicInfoRecord, Timestamp> MTIME = createField(DSL.name("mtime"), SQLDataType.TIMESTAMP(0).nullable(false).defaultValue(DSL.field("current_timestamp()", SQLDataType.TIMESTAMP)), this, "更新时间");

    /**
     * The column <code>lau_creative_fly_dynamic_info.sid</code>. 动态关联的直播预约id
     */
    public final TableField<LauCreativeFlyDynamicInfoRecord, Long> SID = createField(DSL.name("sid"), SQLDataType.BIGINT.nullable(false).defaultValue(DSL.inline("0", SQLDataType.BIGINT)), this, "动态关联的直播预约id");

    /**
     * The column <code>lau_creative_fly_dynamic_info.dynamic_up_mid</code>.
     * 动态up主mid
     */
    public final TableField<LauCreativeFlyDynamicInfoRecord, Long> DYNAMIC_UP_MID = createField(DSL.name("dynamic_up_mid"), SQLDataType.BIGINT.nullable(false).defaultValue(DSL.inline("0", SQLDataType.BIGINT)), this, "动态up主mid");

    /**
     * The column <code>lau_creative_fly_dynamic_info.source</code>. 动态来源: 0-未知
     */
    public final TableField<LauCreativeFlyDynamicInfoRecord, Integer> SOURCE = createField(DSL.name("source"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "动态来源: 0-未知");

    /**
     * The column <code>lau_creative_fly_dynamic_info.second_show_mid</code>.
     * 动态联合投稿展示的第二个mid
     */
    public final TableField<LauCreativeFlyDynamicInfoRecord, Long> SECOND_SHOW_MID = createField(DSL.name("second_show_mid"), SQLDataType.BIGINT.nullable(false).defaultValue(DSL.inline("0", SQLDataType.BIGINT)), this, "动态联合投稿展示的第二个mid");

    /**
     * The column <code>lau_creative_fly_dynamic_info.shadow_info</code>. 联合投稿信息
     */
    public final TableField<LauCreativeFlyDynamicInfoRecord, String> SHADOW_INFO = createField(DSL.name("shadow_info"), SQLDataType.VARCHAR(1024).nullable(false).defaultValue(DSL.inline("''", SQLDataType.VARCHAR)), this, "联合投稿信息");

    private TLauCreativeFlyDynamicInfo(Name alias, Table<LauCreativeFlyDynamicInfoRecord> aliased) {
        this(alias, aliased, null);
    }

    private TLauCreativeFlyDynamicInfo(Name alias, Table<LauCreativeFlyDynamicInfoRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment("起飞创意的动态信息（引擎用）"), TableOptions.table());
    }

    /**
     * Create an aliased <code>lau_creative_fly_dynamic_info</code> table
     * reference
     */
    public TLauCreativeFlyDynamicInfo(String alias) {
        this(DSL.name(alias), LAU_CREATIVE_FLY_DYNAMIC_INFO);
    }

    /**
     * Create an aliased <code>lau_creative_fly_dynamic_info</code> table
     * reference
     */
    public TLauCreativeFlyDynamicInfo(Name alias) {
        this(alias, LAU_CREATIVE_FLY_DYNAMIC_INFO);
    }

    /**
     * Create a <code>lau_creative_fly_dynamic_info</code> table reference
     */
    public TLauCreativeFlyDynamicInfo() {
        this(DSL.name("lau_creative_fly_dynamic_info"), null);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : DefaultSchema.DEFAULT_SCHEMA;
    }

    @Override
    public Identity<LauCreativeFlyDynamicInfoRecord, Long> getIdentity() {
        return (Identity<LauCreativeFlyDynamicInfoRecord, Long>) super.getIdentity();
    }

    @Override
    public UniqueKey<LauCreativeFlyDynamicInfoRecord> getPrimaryKey() {
        return Internal.createUniqueKey(TLauCreativeFlyDynamicInfo.LAU_CREATIVE_FLY_DYNAMIC_INFO, DSL.name("KEY_lau_creative_fly_dynamic_info_PRIMARY"), new TableField[] { TLauCreativeFlyDynamicInfo.LAU_CREATIVE_FLY_DYNAMIC_INFO.ID }, true);
    }

    @Override
    public List<UniqueKey<LauCreativeFlyDynamicInfoRecord>> getUniqueKeys() {
        return Arrays.asList(
            Internal.createUniqueKey(TLauCreativeFlyDynamicInfo.LAU_CREATIVE_FLY_DYNAMIC_INFO, DSL.name("KEY_lau_creative_fly_dynamic_info_creative_id_idx"), new TableField[] { TLauCreativeFlyDynamicInfo.LAU_CREATIVE_FLY_DYNAMIC_INFO.CREATIVE_ID }, true)
        );
    }

    @Override
    public TLauCreativeFlyDynamicInfo as(String alias) {
        return new TLauCreativeFlyDynamicInfo(DSL.name(alias), this);
    }

    @Override
    public TLauCreativeFlyDynamicInfo as(Name alias) {
        return new TLauCreativeFlyDynamicInfo(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public TLauCreativeFlyDynamicInfo rename(String name) {
        return new TLauCreativeFlyDynamicInfo(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public TLauCreativeFlyDynamicInfo rename(Name name) {
        return new TLauCreativeFlyDynamicInfo(name, null);
    }

    // -------------------------------------------------------------------------
    // Row15 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row15<Long, Integer, Integer, Integer, Long, String, String, Integer, Timestamp, Timestamp, Long, Long, Integer, Long, String> fieldsRow() {
        return (Row15) super.fieldsRow();
    }
}
