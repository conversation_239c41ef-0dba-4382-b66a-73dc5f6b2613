/*
 * This file is generated by jOOQ.
 */
package sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos;


import java.io.Serializable;
import java.sql.Timestamp;


/**
 * 单元-已转化用户过滤定向表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LauUnitTargetInstalledUserFilterPo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer   id;
    private Integer   unitId;
    private Integer   filterType;
    private String    targetContent;
    private Integer   isDeleted;
    private Timestamp ctime;
    private Timestamp mtime;

    public LauUnitTargetInstalledUserFilterPo() {}

    public LauUnitTargetInstalledUserFilterPo(LauUnitTargetInstalledUserFilterPo value) {
        this.id = value.id;
        this.unitId = value.unitId;
        this.filterType = value.filterType;
        this.targetContent = value.targetContent;
        this.isDeleted = value.isDeleted;
        this.ctime = value.ctime;
        this.mtime = value.mtime;
    }

    public LauUnitTargetInstalledUserFilterPo(
        Integer   id,
        Integer   unitId,
        Integer   filterType,
        String    targetContent,
        Integer   isDeleted,
        Timestamp ctime,
        Timestamp mtime
    ) {
        this.id = id;
        this.unitId = unitId;
        this.filterType = filterType;
        this.targetContent = targetContent;
        this.isDeleted = isDeleted;
        this.ctime = ctime;
        this.mtime = mtime;
    }

    /**
     * Getter for <code>lau_unit_target_installed_user_filter.id</code>. 自增id
     */
    public Integer getId() {
        return this.id;
    }

    /**
     * Setter for <code>lau_unit_target_installed_user_filter.id</code>. 自增id
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * Getter for <code>lau_unit_target_installed_user_filter.unit_id</code>.
     * 单元id
     */
    public Integer getUnitId() {
        return this.unitId;
    }

    /**
     * Setter for <code>lau_unit_target_installed_user_filter.unit_id</code>.
     * 单元id
     */
    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    /**
     * Getter for
     * <code>lau_unit_target_installed_user_filter.filter_type</code>.
     * 已安装用户过滤类型: 0：不限，1：过滤已安装，2：定向其他(原来的定向) 3: 定向已安装
     */
    public Integer getFilterType() {
        return this.filterType;
    }

    /**
     * Setter for
     * <code>lau_unit_target_installed_user_filter.filter_type</code>.
     * 已安装用户过滤类型: 0：不限，1：过滤已安装，2：定向其他(原来的定向) 3: 定向已安装
     */
    public void setFilterType(Integer filterType) {
        this.filterType = filterType;
    }

    /**
     * Getter for
     * <code>lau_unit_target_installed_user_filter.target_content</code>. 定向 app
     * 类型: 1-京东 2-淘宝 3-拼多多 4-微信
     */
    public String getTargetContent() {
        return this.targetContent;
    }

    /**
     * Setter for
     * <code>lau_unit_target_installed_user_filter.target_content</code>. 定向 app
     * 类型: 1-京东 2-淘宝 3-拼多多 4-微信
     */
    public void setTargetContent(String targetContent) {
        this.targetContent = targetContent;
    }

    /**
     * Getter for <code>lau_unit_target_installed_user_filter.is_deleted</code>.
     * 软删除 0 否 1是
     */
    public Integer getIsDeleted() {
        return this.isDeleted;
    }

    /**
     * Setter for <code>lau_unit_target_installed_user_filter.is_deleted</code>.
     * 软删除 0 否 1是
     */
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    /**
     * Getter for <code>lau_unit_target_installed_user_filter.ctime</code>. 创建时间
     */
    public Timestamp getCtime() {
        return this.ctime;
    }

    /**
     * Setter for <code>lau_unit_target_installed_user_filter.ctime</code>. 创建时间
     */
    public void setCtime(Timestamp ctime) {
        this.ctime = ctime;
    }

    /**
     * Getter for <code>lau_unit_target_installed_user_filter.mtime</code>. 更新时间
     */
    public Timestamp getMtime() {
        return this.mtime;
    }

    /**
     * Setter for <code>lau_unit_target_installed_user_filter.mtime</code>. 更新时间
     */
    public void setMtime(Timestamp mtime) {
        this.mtime = mtime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("LauUnitTargetInstalledUserFilterPo (");

        sb.append(id);
        sb.append(", ").append(unitId);
        sb.append(", ").append(filterType);
        sb.append(", ").append(targetContent);
        sb.append(", ").append(isDeleted);
        sb.append(", ").append(ctime);
        sb.append(", ").append(mtime);

        sb.append(")");
        return sb.toString();
    }
}
