package sycpb.platform.cpm.pandora.infra.utils;

import io.vavr.control.Try;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.BiFunction;
import java.util.function.Consumer;
import java.util.function.Function;
import javax.annotation.Nullable;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @desc
 * @date 2022/6/7
 */
public class IteratorHelper {

    public static <ID, E> Iterator<List<E>> buildIterator(
            BiFunction<ID, Integer, List<E>> valueGetter, Function<E, ID> idGetter, Integer pageSize) {

        return buildIterator(valueGetter, idGetter, pageSize, null);
    }


    /**
     * @param valueGetter
     * @param idGetter
     * @param pageSize
     * @param postHandler 对迭代后得到数据的后置处理操作
     * @param <ID>
     * @param <E>
     * @return
     */
    public static <ID, E> Iterator<List<E>> buildIterator(
            BiFunction<ID, Integer, List<E>> valueGetter, Function<E, ID> idGetter, Integer pageSize,
            @Nullable Consumer<List<E>> postHandler) {

        return new Iterator<List<E>>() {

            private final AtomicBoolean hasNext = new AtomicBoolean(true);

            private final AtomicReference<ID> currentIndex = new AtomicReference<>();


            @Override
            public boolean hasNext() {
                return hasNext.get();
            }

            @Override
            public List<E> next() {

                if (!hasNext.get()) {
                    throw new NoSuchElementException();
                }

                List<E> allAccount = valueGetter.apply(currentIndex.get(), pageSize);

                if (CollectionUtils.isEmpty(allAccount) || allAccount.size() < pageSize) {
                    hasNext.set(false);
                } else {
                    // last index getter,
                    currentIndex.set(idGetter.apply(allAccount.get(allAccount.size() - 1)));
                }

                if (CollectionUtils.isEmpty(allAccount)) {
                    return new ArrayList<>();
                }

                if (postHandler != null) {
                    postHandler.accept(allAccount);
                }

                return allAccount;
            }
        };
    }


    public static <ID, T> Iterator<T> buildIterator4Cursor(
            BiFunction<ID, Integer, T> valueGetter, Function<T, ID> nextCursorGetter, Integer pageSize,
            @Nullable Consumer<T> postHandler) {

        return new Iterator<T>() {

            private final AtomicBoolean hasNext = new AtomicBoolean(true);

            private final AtomicReference<ID> currentIndex = new AtomicReference<>();


            @Override
            public boolean hasNext() {
                return hasNext.get();
            }

            @Override
            public T next() {

                if (!hasNext.get()) {
                    throw new NoSuchElementException();
                }

                T page = Try.of(this::callWithRetry)
                        .onFailure(t -> {
                            hasNext.set(false);
                        }).get();

                ID nextCursor = nextCursorGetter.apply(page);

                if (nextCursor == null) {
                    hasNext.set(false);
                } else {
                    currentIndex.set(nextCursor);
                }

//                if (CollectionUtils.isEmpty(page) || page.size() < pageSize) {
//                    hasNext.set(false);
//                } else {
//                    // last index getter,
//                    currentIndex.set(idGetter.apply(page.get(page.size() - 1)));
//                }

                if (postHandler != null) {
                    postHandler.accept(page);
                }

                return page;
            }

            private T callWithRetry() {

                int maxFailCount = 3;

                int failCount = 0;

                int sleep = 100;

                while (true) {
                    try {
                        return valueGetter.apply(currentIndex.get(), pageSize);
                    } catch (Exception e) {
                        failCount++;
                        if (failCount >= maxFailCount) {
                            throw e;
                        }
                        try {
                            TimeUnit.MILLISECONDS.sleep(sleep);
                        } catch (InterruptedException ex) {
                            Thread.currentThread().interrupt();
                        }
                    }
                }


            }
        };


    }
}
