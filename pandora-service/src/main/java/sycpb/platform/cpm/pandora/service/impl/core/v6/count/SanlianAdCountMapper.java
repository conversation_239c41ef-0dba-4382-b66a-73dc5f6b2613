package sycpb.platform.cpm.pandora.service.impl.core.v6.count;

import com.bapis.ad.adp.ad_count.AdCountReply;
import com.bapis.ad.adp.ad_count.AdLimitReply;
import com.bapis.ad.adp.ad_count.AutoLimitByCampaignCheckReply;
import com.bapis.ad.adp.ad_count.AutoLimitCheckReply;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import sycpb.platform.cpm.pandora.service.impl.core.v6.count.bos.SanlianAdCountBo;
import sycpb.platform.cpm.pandora.service.impl.core.v6.count.bos.SanlianAdLimitBo;
import sycpb.platform.cpm.pandora.service.impl.core.v6.count.bos.SanlianAutoLimitBo;

/**
 * @ClassName SanlianAdCountMapper
 * <AUTHOR>
 * @Date 2024/11/21 9:20 下午
 * @Version 1.0
 **/
@Mapper
public interface SanlianAdCountMapper {

    SanlianAdCountMapper MAPPER = Mappers.getMapper(SanlianAdCountMapper.class);

    SanlianAdCountBo fromRpcBo(AdCountReply x);
    SanlianAdLimitBo fromRpcBo(AdLimitReply x);

    SanlianAutoLimitBo fromCampaignRpcBo(AutoLimitByCampaignCheckReply x);

    SanlianAutoLimitBo fromRpcBo(AutoLimitCheckReply x);

}
