package sycpb.platform.cpm.pandora.service.impl.resource.product_category;

import org.jooq.DSLContext;
import org.springframework.stereotype.Service;
import sycpb.platform.cpm.pandora.infra.config.mysql.MySqlConfig;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.pojos.AdProductBaseInfoPo;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.pojos.AdProductLibrarySharePo;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.pojos.AdProductTotalInfoPo;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.records.AdProductBaseInfoRecord;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.records.AdProductLibraryShareRecord;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.records.AdProductTotalInfoRecord;
import sycpb.platform.cpm.pandora.infra.utils.NumberUtils;
import sycpb.platform.cpm.pandora.service.api.resource.product_category.IResAdProductBaseInfoService;
import sycpb.platform.cpm.pandora.service.api.resource.product_category.bos.AdProductBaseInfoBo;
import sycpb.platform.cpm.pandora.service.api.resource.product_category.bos.AdProductLibraryShareBo;
import sycpb.platform.cpm.pandora.service.impl.core.v6.creative.enums.IsDeleted;

import javax.annotation.Resource;

import java.util.Objects;

import static sycpb.platform.cpm.pandora.infra.dao.ad.tables.TAdProductBaseInfo.AD_PRODUCT_BASE_INFO;
import static sycpb.platform.cpm.pandora.infra.dao.ad.tables.TAdProductLibraryShare.AD_PRODUCT_LIBRARY_SHARE;
import static sycpb.platform.cpm.pandora.infra.dao.ad.tables.TAdProductTotalInfo.AD_PRODUCT_TOTAL_INFO;

/**
 * @ClassName ResProductCategoryServiceImpl
 * <AUTHOR>
 * @Date 2024/6/9 2:21 下午
 * @Version 1.0
 **/
@Service
public class ResAdProductBaseInfoServiceImpl implements IResAdProductBaseInfoService {

    @Resource(name = MySqlConfig.AD_DSL_CONTEXT)
    private DSLContext ad;

    @Override
    public AdProductBaseInfoBo getById(Long id) {
        AdProductBaseInfoRecord record = ad.selectFrom(AD_PRODUCT_BASE_INFO)
                .where(AD_PRODUCT_BASE_INFO.ID.eq(id))
                .and(AD_PRODUCT_BASE_INFO.IS_DELETED.eq(IsDeleted.VALID.getCode()))
                .fetchOne();
        if (Objects.isNull(record)) {
            return null;
        }

        return ResAdProductBaseInfoServiceImplMapper.MAPPER.fromPo(record.into(AdProductBaseInfoPo.class));
    }

    @Override
    public AdProductBaseInfoBo getByIdV2(Long id) {
        AdProductTotalInfoRecord record = ad.selectFrom(AD_PRODUCT_TOTAL_INFO)
                .where(AD_PRODUCT_TOTAL_INFO.AD_PRODUCT_ID.eq(id))
                .and(AD_PRODUCT_TOTAL_INFO.IS_DELETED.eq(IsDeleted.VALID.getCode()))
                .fetchOne();
        if (Objects.isNull(record)) {
            return null;
        }
        return ResAdProductBaseInfoServiceImplMapper.MAPPER.fromPo(record.into(AdProductTotalInfoPo.class));

    }

    @Override
    public AdProductLibraryShareBo getByLibraryIdAndAccountId(Long libraryId, Integer accountId) {
        if (!NumberUtils.isPositive(libraryId)
                || !NumberUtils.isPositive(accountId)) {
            return null;
        }

        AdProductLibraryShareRecord shareRecord = ad.selectFrom(AD_PRODUCT_LIBRARY_SHARE)
                .where(AD_PRODUCT_LIBRARY_SHARE.ACCOUNT_ID.eq(accountId))
                .and(AD_PRODUCT_LIBRARY_SHARE.LIBRARY_ID.eq(libraryId))
                .and(AD_PRODUCT_LIBRARY_SHARE.IS_DELETED.eq(IsDeleted.VALID.getCode()))
                .fetchOne();

        if (Objects.isNull(shareRecord)) {
            return null;
        }

        return ResAdProductBaseInfoServiceImplMapper.MAPPER.fromPo(shareRecord.into(AdProductLibrarySharePo.class));
    }
}
