package sycpb.platform.cpm.pandora.service.impl.resource.rule;

import com.bapis.ad.location.adp.v6.*;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import pleiades.venus.starter.rpc.client.RPCClient;
import sycpb.platform.cpm.pandora.service.api.resource.rule.*;
import sycpb.platform.cpm.pandora.service.api.resource.rule.bos.input.BusMarkParamBo;
import sycpb.platform.cpm.pandora.service.api.resource.rule.bos.input.CreativeContentParamBo;
import sycpb.platform.cpm.pandora.service.api.resource.rule.bos.input.NativeParamBo;
import sycpb.platform.cpm.pandora.service.api.resource.rule.bos.input.UnitLimitByCostParamBo;
import sycpb.platform.cpm.pandora.service.api.resource.rule.bos.output.*;
import sycpb.platform.cpm.pandora.service.api.resource.unit.bos.UnitLimitInfoBo;
import sycpb.platform.cpm.pandora.service.proxy.CpmLocationProxy;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

@Slf4j
@Service
@RequiredArgsConstructor
public class RuleServiceImpl implements IRuleService {

    @Resource
    private CpmLocationProxy cpmLocationProxy;

    @Getter
    private volatile List<RuleBo<CreativeContentParamBo, CreativeContentBo>> creativeContentRules;
    @Getter
    private volatile List<RuleBo<BusMarkParamBo, BusMarkBo>> busMarkRules;
    @Getter
    private volatile List<RuleBo<NativeParamBo, NativeBo>> supportBiliNativeRules;
    @Getter
    private volatile List<StdRuleBo<SlotGroupTemplateMappingBo>> slotGroupTemplateMappingRules;
    @Getter
    private volatile List<StdRuleBo<UnitBidBo>> unitBidRules;
    @Getter
    private volatile List<StdRuleBo<UnitContentBo>> unitContentRules;
    @Getter
    private volatile List<StdRuleBo<CpaTargetBo>> assistCpaTargetRules;
    @Getter
    private volatile List<StdRuleBo<CpaTargetBo>> deepCpaTargetRules;
    @Getter
    private volatile List<StdRuleBo<CpaTargetBo>> cpaTargetRules;
    @Getter
    private volatile List<StdRuleBo<PromotionContentTypeBo>> promotionContentTypeRules;
    @Getter
    private volatile List<StdRuleBo<CampaignContentBo>> campaignContentRules;
    @Getter
    private volatile List<StdRuleBo<PromotionPurposeTypeBo>> promotionPurposeTypeRules;
    @Getter
    private volatile List<StdRuleBo<CpaTargetExtBo>> cpaTargetExtRules;
    @Getter
    private volatile List<StdRuleBo<UnitLimitInfoBo>> unitLimitByLabelRules;
    @Getter
    private volatile List<RuleBo<UnitLimitByCostParamBo, UnitLimitInfoBo>> unitLimitByCostRules;

    @PostConstruct
    private void init() {
        final var executorService = Executors.newFixedThreadPool(10);
        Executors.newSingleThreadExecutor().execute(() -> {
            run(executorService);
        });
    }

    @SneakyThrows
    private void run(ExecutorService executorService) {
        final List<Runnable> refreshers = List.of(
                this::reloadCreativeContent,
                this::reloadBusMark,
                this::reloadSupportBiliNative,
                this::reloadSlotGroupTemplateMappingRules,
                this::reloadUnitBidRules,
                this::reloadUnitContentRules,
                this::reloadAssistCpaTargetRules,
                this::reloadDeepCpaTargetRules,
                this::reloadCpaTargetRules,
                this::reloadPromotionContentTypeRules,
                this::reloadCampaignContentRules,
                this::reloadPromotionPurposeTypeRules,
                this::reloadCpaTargetExtRules,
                this::reloadUnitLimitByLabelRules,
                this::reloadUnitLimitByCostRules
        );
        while (true) {
            try {
                for (Runnable refresher : refreshers) {
                    executorService.execute(() -> {
                        try {
                            refresher.run();
                        } catch (Throwable t) {
                            log.error("refresh thread failed", t);
                        }
                    });
                }
                TimeUnit.SECONDS.sleep(60);
            } catch (Throwable t) {
                log.error("refresh main failed", t);
            }
        }
    }

    private void reloadCreativeContent() {
        final var resp = cpmLocationProxy.creativeContentConfig();
        final var rules = CreativeRuleMapper.MAPPER.toCreativeContentRules(resp.getEntityList());
        if (!CollectionUtils.isEmpty(rules)) {
            creativeContentRules = rules;
        }
    }

    private void reloadBusMark() {
        final var resp = cpmLocationProxy.busMarkConfig();
        busMarkRules = CreativeRuleMapper.MAPPER.toBusMarkRules(resp.getEntityList());
    }

    private void reloadSupportBiliNative() {
        final var resp = cpmLocationProxy.supportNativeArcConfig();
        supportBiliNativeRules = CreativeRuleMapper.MAPPER.toSupportBiliNativeRules(resp.getEntityList());
    }

    private void reloadSlotGroupTemplateMappingRules() {
        final var resp = cpmLocationProxy.slotGroupMappingConfig();
        slotGroupTemplateMappingRules = CreativeRuleMapper.MAPPER.toSlotGroupTemplateMappingBos(resp.getEntityList());
        System.out.println();
    }

    private void reloadUnitBidRules() {
        final var resp = cpmLocationProxy.unitTwoStageMinBidConfig();
        unitBidRules = UnitConfigMapper.MAPPER.fromUnitBidRos(resp.getEntityList());
    }

    private void reloadUnitContentRules() {
        final var resp = cpmLocationProxy.unitContentConfig();
        unitContentRules = UnitConfigMapper.MAPPER.fromUnitContentRos(resp.getEntityList());
    }

    private void reloadAssistCpaTargetRules() {
        final var resp = cpmLocationProxy.unitAssistCpaTargetConfig();
        assistCpaTargetRules = UnitConfigMapper.MAPPER.fromAssistCpaTargetRos(resp.getEntityList());
    }

    private void reloadDeepCpaTargetRules() {
        final var resp = cpmLocationProxy.unitDeepCpaTargetConfig();
        deepCpaTargetRules = UnitConfigMapper.MAPPER.fromDeepCpaTargetRos(resp.getEntityList());
    }

    private void reloadCpaTargetRules() {
        final var resp = cpmLocationProxy.unitCpaTargetConfig();
        cpaTargetRules = UnitConfigMapper.MAPPER.fromCpaTargetRos(resp.getEntityList());
    }

    private void reloadPromotionContentTypeRules() {
        final var resp = cpmLocationProxy.unitPpcConfig();
        promotionContentTypeRules = UnitConfigMapper.MAPPER.fromPpcRos(resp.getEntityList());
    }

    private void reloadCampaignContentRules() {
        final var resp = cpmLocationProxy.campaignContentConfig();
        campaignContentRules = CampaignRuleMapper.MAPPER.toCampaignContentBos(resp.getEntityList());
    }

    private void reloadPromotionPurposeTypeRules() {
        final var resp = cpmLocationProxy.campaignPptConfig();
        promotionPurposeTypeRules = CampaignRuleMapper.MAPPER.toPromotionPurposeTypeBos(resp.getEntityList());
    }

    private void reloadCpaTargetExtRules() {
        final var resp = cpmLocationProxy.unitCpaTargetAttrConfig();
        cpaTargetExtRules = UnitConfigMapper.MAPPER.fromCpaTargetExtRos(resp.getEntityList());
    }

    private void reloadUnitLimitByLabelRules() {
        final var resp = cpmLocationProxy.unitLimitByLabelConfig();
        unitLimitByLabelRules = UnitConfigMapper.MAPPER.fromUnitLimitConfigByLabelRos(resp.getEntityList());
    }

    private void reloadUnitLimitByCostRules() {
        final var resp = cpmLocationProxy.unitLimitByCostConfig();
        unitLimitByCostRules = UnitConfigMapper.MAPPER.fromUnitLimitConfigByCostRos(resp.getEntityList());
    }
}
