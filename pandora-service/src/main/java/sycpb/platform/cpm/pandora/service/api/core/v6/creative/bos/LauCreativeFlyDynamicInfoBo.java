package sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import sycpb.platform.cpm.pandora.infra.compare.CompareMeta;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class LauCreativeFlyDynamicInfoBo {
    @CompareMeta(copyFromCurrentVersion = true, comparable = false)
    private Long      id;
    private Integer   accountId;
    private Integer   unitId;
    @CompareMeta(logGroupId = true, comparable = false, uk = true)
    private Integer   creativeId;
    @CompareMeta(logDescription = "动态id", shadow = true)
    private Long      dynamicId;
    @CompareMeta(shadow = true)
    private Long      dynamicUpMid;
    private Integer   source;

    private Integer   dynamicType;
}
