package sycpb.platform.cpm.pandora.service.api.core.batch.bos;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class QualificationOperationBo {
    private List<Integer> qualificationIds;
    private Long qualificationPackageId;
}
