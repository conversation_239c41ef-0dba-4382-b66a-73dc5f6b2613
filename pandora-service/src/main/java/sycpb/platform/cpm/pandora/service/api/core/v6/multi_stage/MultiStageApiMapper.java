package sycpb.platform.cpm.pandora.service.api.core.v6.multi_stage;

import com.bapis.ad.pandora.core.v6.CopyUnitAndCreativesReq;
import com.bapis.ad.pandora.core.v6.CopyUnitAndCreativesResp;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.factory.Mappers;
import sycpb.platform.cpm.pandora.infra.common.EnumMapper;
import sycpb.platform.cpm.pandora.infra.common.comma_split.CommaSplitMapper;
import sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos.UnitCreativeIdsBo;
import sycpb.platform.cpm.pandora.service.api.core.v6.multi_stage.bos.CopyUnitAndCreativesBo;

@Mapper(collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        uses = {EnumMapper.class, CommaSplitMapper.class})
public interface MultiStageApiMapper {
    MultiStageApiMapper MAPPER = Mappers.getMapper(MultiStageApiMapper.class);

    CopyUnitAndCreativesBo fromRo(CopyUnitAndCreativesReq x);

    @Mapping(target = "creativeId", source = "creativeIds")
    CopyUnitAndCreativesResp toRo(UnitCreativeIdsBo x);
}
