package sycpb.platform.cpm.pandora.service.impl.core.batch.handlers.unit;

import com.bapis.ad.pandora.resource.BudgetType;
import org.jooq.DSLContext;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import sycpb.platform.cpm.pandora.infra.common.functions.JooqFunctions;
import sycpb.platform.cpm.pandora.infra.config.mysql.MySqlConfig;
import sycpb.platform.cpm.pandora.infra.dao.ad.tables.daos.LauUnitNextdayBudgetDao;
import sycpb.platform.cpm.pandora.service.api.core.batch.bos.BudgetOperationBo;
import sycpb.platform.cpm.pandora.service.api.core.operation_log.IOperationLogService;
import sycpb.platform.cpm.pandora.service.impl.core.batch.BatchOperationExtService;
import sycpb.platform.cpm.pandora.service.impl.core.batch.bos.LauUnitNextdayBudgetBo;
import sycpb.platform.cpm.pandora.service.impl.core.batch.handlers.campaign.CampaignNextDayBudgetHandler;
import sycpb.platform.cpm.pandora.service.impl.core.batch.handlers.misc.CompareFunctions;
import sycpb.platform.cpm.pandora.service.impl.core.v6.unit.UnitImplMapper;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;

import static sycpb.platform.cpm.pandora.infra.dao.ad.tables.TLauUnitNextdayBudget.LAU_UNIT_NEXTDAY_BUDGET;

@Service
public class UnitNextDayBudgetHandler extends UnitBudgetTemplateHandler {
    @Resource(name = MySqlConfig.AD_DSL_CONTEXT)
    private DSLContext ad;
    @Resource
    private LauUnitNextdayBudgetDao lauUnitNextdayBudgetDao;

    public UnitNextDayBudgetHandler(BatchOperationExtService batchOperationExtService,
                                    IOperationLogService operationLogService,
                                    @Qualifier(MySqlConfig.AD_CORE_DSL_CONTEXT) DSLContext adCore) {
        super(operationLogService, batchOperationExtService, adCore);
    }

    @Override
    public void handleRequest(UnitContext ctx) {
        super.handleRequest(ctx);
        updateBudgetContext(ctx);
        ctx.setNextDayBudgetEffectTime(CampaignNextDayBudgetHandler.resolveBudgetEffectTime(ctx.getBatchOperationBo().getOperationRegisterTime()));
    }

    @Override
    public void handleTarget(UnitContext ctx) {
        super.handleTarget(ctx);
        ctx.setLauUnitNextdayBudgetBo(ctx.getNextDatBudgetMap().get(ctx.getTargetId()));
    }

    @Override
    public List<Function<UnitContext, String>> fetchValidators() {
        final var list = new ArrayList<>(super.fetchValidators());

        // 添加次日预算时间冲突校验
        list.add(c -> {
            // 获取当前次日预算配置
            final var currentBudget = c.getLauUnitNextdayBudgetBo();
            if (Objects.nonNull(currentBudget)) {
                // 检查时间是否相同
                final var newEffectiveTime = c.getNextDayBudgetEffectTime();
                if (!Objects.equals(currentBudget.getBudgetEffectiveTime(), newEffectiveTime)) {
                    return "次日预算处理中，请稍后重试";
                }
            }
            return null;
        });

        return list;
    }

    @Override
    public CompareBo compare(UnitContext ctx) {
        final var curBudgetBo = Optional.ofNullable(ctx.getLauUnitNextdayBudgetBo())
                .map(x -> BudgetOperationBo.newInstance(x.getBudget(), BudgetType.BUDGET_DAILY_VALUE, x.getIsRepeat()))
                .orElse(null);
        return CompareFunctions.compareNextDayBudget(curBudgetBo, ctx.getOperationBo().getBudget(), ctx.getOperatorBo(), ctx.getTargetId(), "lau_unit");
    }

    @Override
    public void batchUpdateTarget(UnitContext ctx) {
        if (CollectionUtils.isEmpty(ctx.getUpdatingTargetIds())) return;

        final List<LauUnitNextdayBudgetBo> newVersions = new ArrayList<>();
        final List<LauUnitNextdayBudgetBo> curVersions = new ArrayList<>();
        for (Integer updatingTargetId : ctx.getUpdatingTargetIds()) {
            final var newBo = new LauUnitNextdayBudgetBo();
            newVersions.add(newBo);
            newBo.setUnitId(updatingTargetId);
            newBo.setBudget(ctx.getOperationBo().getBudget().getBudgetValue());
            newBo.setIsRepeat(ctx.getOperationBo().getBudget().getIsRepeat());
            newBo.setBudgetEffectiveTime(ctx.getNextDayBudgetEffectTime());
            final var curBo = ctx.getNextDatBudgetMap().get(updatingTargetId);
            if (Objects.nonNull(curBo)) {
                curVersions.add(curBo);
            }
        }
        JooqFunctions.save(curVersions, newVersions, JooqFunctions.JooqSaveContext.minimum(LauUnitNextdayBudgetBo.class, UnitImplMapper.MAPPER::toPo, lauUnitNextdayBudgetDao));
        // 删除可能存在的冗余数据
        if (!CollectionUtils.isEmpty(ctx.getDeprecatingUnitNextDayBudgetIdList())) {
            ad.update(LAU_UNIT_NEXTDAY_BUDGET)
                    .set(LAU_UNIT_NEXTDAY_BUDGET.IS_DELETED, 1)
                    .where(LAU_UNIT_NEXTDAY_BUDGET.ID.in(ctx.getDeprecatingUnitNextDayBudgetIdList()))
                    .execute();
        }
    }

    @Override
    public void batchUpdate(UnitContext ctx) {
        ((UnitNextDayBudgetHandler) AopContext.currentProxy()).batchUpdateWithTx(ctx);
    }

    @Transactional(MySqlConfig.AD_TX_MGR)
    public void batchUpdateWithTx(UnitContext ctx) {
        super.batchUpdate(ctx);
    }

    private void updateBudgetContext(UnitContext ctx) {
        if (CollectionUtils.isEmpty(ctx.getAllTargetIds())) {
            ctx.setNextDatBudgetMap(Map.of());
            ctx.setDeprecatingUnitNextDayBudgetIdList(List.of());
            return;
        }

        final var bos = ad.select(LAU_UNIT_NEXTDAY_BUDGET.ID,
                        LAU_UNIT_NEXTDAY_BUDGET.UNIT_ID,
                        LAU_UNIT_NEXTDAY_BUDGET.BUDGET,
                        LAU_UNIT_NEXTDAY_BUDGET.IS_REPEAT,
                        LAU_UNIT_NEXTDAY_BUDGET.BUDGET_EFFECTIVE_TIME
                ).from(LAU_UNIT_NEXTDAY_BUDGET)
                .where(LAU_UNIT_NEXTDAY_BUDGET.UNIT_ID.in(ctx.getAllTargetIds()))
                .and(LAU_UNIT_NEXTDAY_BUDGET.IS_DELETED.eq(0))
                .fetch()
                .into(LauUnitNextdayBudgetBo.class);
        final List<Integer> list = new ArrayList<>();
        final Map<Integer, LauUnitNextdayBudgetBo> map = new HashMap<>();
        for (final var bo : bos) {
            final var existingBo = map.get(bo.getUnitId());
            if (Objects.isNull(existingBo)) {
                map.put(bo.getUnitId(), bo);
            } else {
                if (existingBo.getId() > bo.getId()) {
                    list.add(bo.getId());
                } else {
                    list.add(existingBo.getId());
                    map.put(bo.getUnitId(), bo);
                }
            }
        }
        ctx.setDeprecatingUnitNextDayBudgetIdList(list);
        ctx.setNextDatBudgetMap(map);
    }
}
