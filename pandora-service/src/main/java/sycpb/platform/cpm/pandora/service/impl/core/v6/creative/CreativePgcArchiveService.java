package sycpb.platform.cpm.pandora.service.impl.core.v6.creative;

import com.bapis.ad.pandora.resource.PromotionContentType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jooq.DSLContext;
import org.springframework.stereotype.Service;
import sycpb.platform.cpm.pandora.infra.common.functions.JooqFunctions;
import sycpb.platform.cpm.pandora.infra.config.mysql.MySqlConfig;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.daos.LauCreativePgcArchiveDao;
import sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.pojos.LauCreativePgcArchivePo;
import sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos.CreativeBo;
import sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos.LauCreativePgcArchiveBo;
import sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos.UnitCreativeBo;
import sycpb.platform.cpm.pandora.service.impl.core.v6.creative.bos.SaveUnitCreativeContextBo;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

import static sycpb.platform.cpm.pandora.infra.dao.ad_core.tables.TLauCreativePgcArchive.LAU_CREATIVE_PGC_ARCHIVE;

@Service
@Slf4j
@RequiredArgsConstructor
public class CreativePgcArchiveService {
    private final LauCreativePgcArchiveDao lauCreativePgcArchiveDao;

    @Resource(name = MySqlConfig.AD_CORE_DSL_CONTEXT)
    private DSLContext adCore;


    public List<LauCreativePgcArchiveBo> fetch(Integer unitId) {
        return UnitCreativeImplMapper.MAPPER.fromLauCreativePgcArchivePos(lauCreativePgcArchiveDao.fetchByUnitId((unitId)));
    }
    public void save(SaveUnitCreativeContextBo ctx) {
        if (ctx.getLauUnitBo().getPromotionPurposeType() != PromotionContentType.PPC_OGV_PROMOTION_VALUE) {
            return;
        }
        final var currentVersions = extract(ctx.getCurrentVersion());
        final var newVersions =  extract(ctx.getNewVersion());
        final var ctx0 = JooqFunctions.JooqSaveContext.minimum(LauCreativePgcArchiveBo.class, UnitCreativeImplMapper.MAPPER::toPo, lauCreativePgcArchiveDao)
                .setOpLog(ctx.genCreativeCompareConsumer())
                .setDSLContext(adCore)
                .setTableImpl(LAU_CREATIVE_PGC_ARCHIVE)
                .setDatabasePrimaryKeyField(LAU_CREATIVE_PGC_ARCHIVE.ID)
                .setDatabasePrimaryKeyMapper(LauCreativePgcArchivePo::getId);
        JooqFunctions.save(currentVersions, newVersions, ctx0);


    }

    public List<LauCreativePgcArchiveBo> extract(UnitCreativeBo unitCreativeBo) {
        return UnitCreativeImplMapper.extractObjectFromUnitCreativeBo(unitCreativeBo, CreativeBo::getLauCreativePgcArchiveBo)
                .stream().filter(bo ->  bo.getAid() != null && bo.getAid() > 0).collect(Collectors.toList());
    }
}
