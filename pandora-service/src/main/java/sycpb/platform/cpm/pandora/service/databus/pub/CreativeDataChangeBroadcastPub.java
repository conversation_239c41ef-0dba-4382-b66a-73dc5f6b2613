package sycpb.platform.cpm.pandora.service.databus.pub;

import com.alibaba.fastjson.JSON;
import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.databus.DatabusTemplate;
import com.bilibili.warp.databus.Message;
import com.bilibili.warp.databus.PubResult;
import com.bilibili.warp.spring.boot.autoconfigure.databus.DatabusProperties;
import com.drew.lang.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import sycpb.platform.cpm.pandora.infra.utils.TimeUtils;
import sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos.broadcast.UnitCreativeIdBroadCastBo;

import javax.annotation.Resource;
import java.time.LocalDate;

/**
 * 非子创意推审
 *
 * <AUTHOR>
 * @Description
 * @date 4/10/24
 **/
@Slf4j
@Service
public class CreativeDataChangeBroadcastPub {

    @Resource
    private DatabusTemplate databusTemplate;

    public static final String CREATIVE_DATA_CHANGE_BROADCAST = "creative-data-change-broadcast";
    private final String topic;
    private final String group;

    public CreativeDataChangeBroadcastPub(DatabusProperties databusProperties) {
        DatabusProperty databusProperty = databusProperties.getProperties().get(CREATIVE_DATA_CHANGE_BROADCAST);
        this.topic = databusProperty.getTopic();
        this.group = databusProperty.getPub().getGroup();
    }

    public void pub(Integer key, UnitCreativeIdBroadCastBo bo) {
        if (bo == null) {
            return;
        }
        String msgStr = JSON.toJSONString(bo);
        log.info("creative data broadcast pub msg, msg={}", msgStr);
        Assert.notNull(bo, "auditCreativeMessage参数不能为空");

        bo.setEvent_time(System.currentTimeMillis());
        // messageKey和value自定义，value会被配置的serializer序列化
        Message message = Message.Builder.of(key + "", bo)
                .build();
        // topic 和 group 按照申请的填写即可. 可以自行注入相关配置，用配置的方式来发送消息
        PubResult result = databusTemplate.pub(topic, group, message);
        if (result.isSuccess()) {
            log.info("creative data broadcast pub msg success, msg={}", JSON.toJSONString(bo));
        } else {
            Throwable throwable = result.getThrowable();
            log.error("creative data broadcast pub msg error ", throwable);
            throw new RuntimeException(throwable.getMessage());
        }

    }

}
