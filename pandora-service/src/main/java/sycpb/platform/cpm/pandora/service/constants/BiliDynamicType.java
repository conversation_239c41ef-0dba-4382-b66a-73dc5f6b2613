/*
 * Copyright (c) 2015-2023 BiliBili Inc.
 */

package sycpb.platform.cpm.pandora.service.constants;

import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

public class BiliDynamicType {
    public static final int IMAGE_TEXT = 2;
    public static final int TEXT = 4;
    public static final int ARCHIVE = 8;

    public static Set<Integer> IMG_TEXT_DYNAMIC_TYPES = Arrays.asList(IMAGE_TEXT, TEXT).stream().collect(Collectors.toSet());
}
