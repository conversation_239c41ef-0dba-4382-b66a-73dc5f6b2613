package sycpb.platform.cpm.pandora.service.api.resource.material;

import com.bapis.ad.pandora.resource.ImageInfo;
import com.bapis.ad.pandora.resource.RawImage;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.factory.Mappers;
import sycpb.platform.cpm.pandora.infra.api.bfs.RawImageBo;

@Mapper(collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ResMaterialApiMapper {
    ResMaterialApiMapper MAPPER = Mappers.getMapper(ResMaterialApiMapper.class);

    ImageInfo toRo(ResImageBo x);

    @Mapping(target = "mimeType", ignore = true)
    @Mapping(target = "rawBytes", expression = "java(x.getRawBytes().toByteArray())")
    RawImageBo fromRo(RawImage x);
}
