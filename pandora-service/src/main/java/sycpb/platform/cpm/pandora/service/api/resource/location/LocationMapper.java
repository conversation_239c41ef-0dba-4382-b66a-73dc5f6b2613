package sycpb.platform.cpm.pandora.service.api.resource.location;

import com.bapis.ad.location.adp.v6.TemplateGroupBindConfigInfo;
import com.bapis.ad.pandora.resource.*;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.factory.Mappers;
import sycpb.platform.cpm.pandora.infra.common.EnumMapper;
import sycpb.platform.cpm.pandora.service.api.resource.location.bos.*;

import java.util.List;
import java.util.Set;

@Mapper(collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        uses = {EnumMapper.class})
public interface LocationMapper {
    LocationMapper MAPPER = Mappers.getMapper(LocationMapper.class);

    List<TemplateGroupTemplateMappingBo> toTemplateGroupTemplateMappingBos(List<TemplateGroupBindConfigInfo> x);
    @Mapping(target = "templateIds", source = "templateId")
    TemplateGroupTemplateMappingBo fromRo(TemplateGroupBindConfigInfo x);

    @Mapping(target = "templateGroupImages", source = "templateGroupImageSet")
    @Mapping(target = "templateGroupGifs", source = "templateGroupGifSet")
    @Mapping(target = "busMarks", source = "busMarkSet")
    TemplateGroupBo fromRo(TemplateGroupInfo x);
    List<TemplateGroupBo> toTemplateGroupBos(List<TemplateGroupInfo> x);

    TemplateGroupBo copyTemplateGroupBo(TemplateGroupBo x);

    @Mapping(target = "accountLabelIds", source = "accountLabelIdSet")
    @Mapping(target = "buttons", source = "buttonInfo")
    @Mapping(target = "requireAutoPlay", expression = "java(LocationMapper.requireAutoPlay(x.getCardType()))")
    @Mapping(target = "notPugvCard", expression = "java(LocationMapper.notPugvCard(x.getCardType()))")
    TemplateBo fromRo(TemplateInfo x);
    List<TemplateBo> toTemplateBos(List<TemplateInfo> x);

    @Mapping(target = "sceneIds", source = "sceneIdSet")
    @Mapping(target = "channelIds", source = "channelIdSet")
    @Mapping(target = "accountLabelIds", source = "accountLabelIdSet")
    @Mapping(target = "slotIds", source = "slotIdSet")
    SlotGroupBo fromRo(SlotGroupInfo x);
    List<SlotGroupBo> toSlotGroupBos(List<SlotGroupInfo> x);

    List<ChannelBo> toChannelBos(List<ChannelInfo> x);

    List<SceneBo> toSceneBos(List<SceneInfo> x);

    List<ButtonBo> toButtonBos(List<ButtonInfo> x);

    static boolean requireAutoPlay(Integer cardType) {
        return Set.of(74,27,88,53,75,65,73,115).contains(cardType);
    }

    static boolean notPugvCard(Integer cardType) {
        return ! Set.of(1, 6).contains(cardType);
    }
}
