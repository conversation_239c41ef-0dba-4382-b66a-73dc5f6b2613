package sycpb.platform.cpm.pandora.service.impl.core.v6.creative.enums;

import java.util.Optional;

/**
 * <AUTHOR>
 * @Description
 * @date 4/16/24
 **/
public enum CreativeStyleEnum {

    UNKNOWN(0, "创意形态未知"),
    IMAGE(1, "静态图文"),
    GIF(2, "GIF图文"),
    AVID(3, "静态视频"),
    VIDEO(4, "Inline视频"),
    EXCITE_VIDEO(5, "激励视频"),
    TEXT(6, "文字链"),
    BVID(7, "bilibili稿件"),
    SELECTIVE(8, "选择式稿件");

    private final Integer code;
    private final String name;

    private CreativeStyleEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static Optional<CreativeStyleEnum> getByCodeOptional(Integer code) {
        CreativeStyleEnum[] var1 = values();
        int var2 = var1.length;

        for (int var3 = 0; var3 < var2; ++var3) {
            CreativeStyleEnum creativeStyleEnum = var1[var3];
            if (creativeStyleEnum.code.equals(code)) {
                return Optional.of(creativeStyleEnum);
            }
        }

        return Optional.empty();
    }

    public String getValue() {
        return this.name;
    }

    public Integer getKey() {
        return this.code;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }
}
