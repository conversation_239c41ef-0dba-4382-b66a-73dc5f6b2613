package sycpb.platform.cpm.pandora.service.impl.resource.recall;

import com.bapis.ad.pandora.resource.AdType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import sycpb.platform.cpm.pandora.infra.utils.NumberUtils;
import sycpb.platform.cpm.pandora.service.api.resource.location.ILocationService;
import sycpb.platform.cpm.pandora.service.api.resource.recall.IRecallService;
import sycpb.platform.cpm.pandora.service.api.resource.recall.RecallResourceParamBo;
import sycpb.platform.cpm.pandora.service.api.resource.rule.IRuleService;
import sycpb.platform.cpm.pandora.service.api.resource.rule.StdRuleBo;
import sycpb.platform.cpm.pandora.service.api.resource.rule.bos.output.SlotGroupTemplateMappingBo;
import sycpb.platform.cpm.pandora.service.constants.Channel;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class RecallServiceImpl implements IRecallService {
    @Resource
    private IRuleService ruleService;
    @Resource
    private ILocationService locationService;

    @Override
    public List<SlotGroupTemplateMappingConfigBo> recallSlotGroupTemplateMapping(RecallResourceParamBo recallResourceParamBo) {
        final List<SlotGroupTemplateMappingConfigBo> configBos = new ArrayList<>();
        final var templateMap = locationService.getTemplateMap();
        final var slotGroupMap = locationService.getSlotGroupMap();
        final var templateTemplateGroupMap = locationService.getTemplateTemplateGroupMap();
        for (final var slotGroupTemplateMappingRule : ruleService.getSlotGroupTemplateMappingRules()) {
            final var configBo = new SlotGroupTemplateMappingConfigBo();
            final var inputBo = slotGroupTemplateMappingRule.getInput();
            final var outputBo = slotGroupTemplateMappingRule.getOutput();
            if (CollectionUtils.isEmpty(outputBo.getTemplateIds())) {
                debugLogForRecall(recallResourceParamBo, slotGroupTemplateMappingRule, "模板id为空");
                continue;
            }

            final var templateId = outputBo.getTemplateIds().get(0);
            configBo.setTemplateId(templateId);
            final var templateBo = templateMap.get(templateId);
            if (Objects.isNull(templateBo)) {
                debugLogForRecall(recallResourceParamBo, slotGroupTemplateMappingRule, "模板信息为空");
                continue;
            }

            configBo.setTemplateBo(templateBo);
            final var templateGroupId = templateTemplateGroupMap.get(templateId);
            if (Objects.isNull(templateGroupId)) {
                debugLogForRecall(recallResourceParamBo, slotGroupTemplateMappingRule, "模板组id为空");
                continue;
            }

            configBo.setTemplateGroupId(templateGroupId);
            final var slotGroupId = outputBo.getSlotGroupId();
            if (!NumberUtils.isPositive(slotGroupId)) {
                debugLogForRecall(recallResourceParamBo, slotGroupTemplateMappingRule, "广告位组id为空");
                continue;
            }

            configBo.setSlotGroupId(outputBo.getSlotGroupId());
            final var slotGroupBo = slotGroupMap.get(slotGroupId);
            if (Objects.isNull(slotGroupBo)) {
                debugLogForRecall(recallResourceParamBo, slotGroupTemplateMappingRule, "广告位组信息为空");
                continue;
            }

            configBo.setSlotGroupBo(slotGroupBo);
            // 计划推广目的召回, 不支持配置空值
            if (CollectionUtils.isEmpty(inputBo.getPromotionPurposeTypes())
                    || Objects.nonNull(recallResourceParamBo.getPromotionPurposeType()) && !inputBo.getPromotionPurposeTypes().contains(recallResourceParamBo.getPromotionPurposeType())
            ) {
                debugLogForRecall(recallResourceParamBo, slotGroupTemplateMappingRule, "计划推广目的过滤");
                continue;
            }

            // 单元推广内容召回, 不支持配置空值
            if (CollectionUtils.isEmpty(inputBo.getPromotionContentTypes())
                    || Objects.nonNull(recallResourceParamBo.getPromotionContentType()) && !inputBo.getPromotionContentTypes().contains(recallResourceParamBo.getPromotionContentType())
            ) {
                debugLogForRecall(recallResourceParamBo, slotGroupTemplateMappingRule, "单元推广内容过滤");
                continue;
            }

            // 基础目标召回, 配置为空代表不支持基础目标投放
            if (Objects.nonNull(recallResourceParamBo.getBaseTarget())
                    && (CollectionUtils.isEmpty(inputBo.getBaseTargetIds()) || !inputBo.getBaseTargetIds().contains(recallResourceParamBo.getBaseTarget()))
            ) {
                debugLogForRecall(recallResourceParamBo, slotGroupTemplateMappingRule, "基础目标过滤");
                continue;
            }

            // 浅层转化目标召回, 配置为空代表不支持浅层转化目标
            if (Objects.nonNull(recallResourceParamBo.getCpaTarget())
                    && (CollectionUtils.isEmpty(inputBo.getCpaTargetIds()) || !inputBo.getCpaTargetIds().contains(recallResourceParamBo.getCpaTarget()))
            ) {
                debugLogForRecall(recallResourceParamBo, slotGroupTemplateMappingRule, "浅层转化目标过滤");
                continue;
            }

            // 账号标签召回, 配置为空代表不限, 包含模板自身校验, 绑定关系校验
            if (accountLabelInCompatible(inputBo.getAccountLabelIds(), recallResourceParamBo.getAccountLabelIds())
                    || accountLabelInCompatible(templateBo.getAccountLabelIds(), recallResourceParamBo.getAccountLabelIds())
            ) {
                debugLogForRecall(recallResourceParamBo, slotGroupTemplateMappingRule, "账号标签过滤");
                continue;
            }

            // 暗投召回
            if (!recallResourceParamBo.isAllowHidden() && NumberUtils.isPositive(templateBo.getIsHidden())) {
                debugLogForRecall(recallResourceParamBo, slotGroupTemplateMappingRule, "暗投过滤");
                continue;
            }

            // 搜索召回
            if (Objects.nonNull(recallResourceParamBo.getAdType())
                    && !NumberUtils.isPositive(slotGroupBo.getIsSupportSearchAd())
                    && Objects.equals(recallResourceParamBo.getAdType(), AdType.ADTYPE_SEARCH_VALUE)
            ) {
                debugLogForRecall(recallResourceParamBo, slotGroupTemplateMappingRule, "搜索过滤");
                continue;
            }

            // 流量类型召回
            if (Objects.nonNull(recallResourceParamBo.getChannelId())
                    && !Objects.equals(recallResourceParamBo.getChannelId(), Channel.ALL)
                    && !slotGroupBo.getChannelIds().contains(recallResourceParamBo.getChannelId())
            ) {
                debugLogForRecall(recallResourceParamBo, slotGroupTemplateMappingRule, "流量类型过滤");
                continue;
            }

            // 场景召回
            if (Objects.nonNull(recallResourceParamBo.getSceneIds())
                    && (!NumberUtils.isPositive(slotGroupBo.getIsSupportPreferScene())
                    && CollectionUtils.isEmpty(recallResourceParamBo.getSceneIds())
                    || !CollectionUtils.isEmpty(recallResourceParamBo.getSceneIds())
                    && slotGroupBo.getSceneIds().stream().noneMatch(x -> recallResourceParamBo.getSceneIds().contains(x)))
            ) {
                debugLogForRecall(recallResourceParamBo, slotGroupTemplateMappingRule, "场景过滤");
                continue;
            }

            debugLogForRecall(recallResourceParamBo, slotGroupTemplateMappingRule, "召回成功");
            configBos.add(configBo);
            configBo.setPromotionPurposeTypes(inputBo.getPromotionPurposeTypes());
            configBo.setPromotionContentTypes(inputBo.getPromotionContentTypes());
            configBo.setBaseTargetIds(inputBo.getBaseTargetIds());
            configBo.setCpaTargetIds(inputBo.getCpaTargetIds());
        }
        return configBos;
    }

    private static boolean accountLabelInCompatible(List<Integer> requirement, List<Integer> actual) {
        // 要求为空代表不限
        if (CollectionUtils.isEmpty(requirement)) return false;

        return !CollectionUtils.containsAny(requirement, actual);
    }

    private static void debugLogForRecall(RecallResourceParamBo recallResourceParamBo, StdRuleBo<SlotGroupTemplateMappingBo> ruleBo, String reason) {
        if (recallResourceParamBo.isDebug()) {
            log.info("param={}, rule={}, ruled_out_reason={}", recallResourceParamBo, ruleBo, reason);
        }
    }
}
