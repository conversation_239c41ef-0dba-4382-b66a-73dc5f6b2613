package sycpb.platform.cpm.pandora.service.impl.core.v6.creative.bos;

import com.bapis.ad.pandora.resource.PromotionContentType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import sycpb.platform.cpm.pandora.infra.api.archive.BiliArchiveBo;
import sycpb.platform.cpm.pandora.infra.compare.bos.ChangeLogBo;
import sycpb.platform.cpm.pandora.infra.utils.NumberUtils;
import sycpb.platform.cpm.pandora.service.api.core.operation_log.OperationLogContextBo;
import sycpb.platform.cpm.pandora.service.api.core.operation_log.OperatorBo;
import sycpb.platform.cpm.pandora.service.api.core.v6.campaign.bos.LauCampaignBo;
import sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos.LauDynamicBo;
import sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos.LauUnitCreativeBo;
import sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos.UnitCreativeBo;
import sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos.broadcast.UnitCreativeIdBroadCastBo;
import sycpb.platform.cpm.pandora.service.api.core.v6.unit.bos.*;
import sycpb.platform.cpm.pandora.service.api.resource.account.bos.AccountBo;
import sycpb.platform.cpm.pandora.service.api.resource.creative.bos.CreativeContentGoodsBo;
import sycpb.platform.cpm.pandora.service.api.resource.material.ResImageBo;
import sycpb.platform.cpm.pandora.service.databus.pub.bo.NativeMsgItem;
import sycpb.platform.cpm.pandora.service.impl.core.v6.creative.CreativeExtraService;
import sycpb.platform.cpm.pandora.service.impl.core.v6.misc.bos.OpusCardBo;
import sycpb.platform.cpm.pandora.service.impl.core.v6.misc.bos.PugvArchiveBo;
import sycpb.platform.cpm.pandora.service.impl.core.v6.misc.hidden.DynHiddenTemplateService;
import sycpb.platform.cpm.pandora.service.impl.core.v6.misc.landing_page.LandingPageService;

import java.util.*;
import java.util.function.BiConsumer;

@Getter
@Setter
@NoArgsConstructor
public class SaveUnitCreativeContextBo {
    private Set<Integer> accountLabelIds;
    private AccountBo accountBo;
    private LauCampaignBo campaignBo;
    private LauUnitBo lauUnitBo;
    private LauUnitExtraBo lauUnitExtraBo;
    private LauUnitGameBo lauUnitGameBo;
    private LauUnitLiveReserveBo lauUnitLiveReserveBo;
    private LauUnitGoodsBo lauUnitGoodsBo;
    private LauUnitBiliMiniGameBo lauUnitBiliMiniGameBo;
    private UnitCreativeBo newVersion;
    // 当前 unit_id下数据库中已有的creative
    private UnitCreativeBo currentVersion;
    private OperatorBo operator;

    private Map<Integer, Integer> operationTypeMap = new HashMap<>();
    private Map<Integer, List<ChangeLogBo>> changeLogsMap = new HashMap<>();
    private List<ChangeLogBo> unitChangeLogBos = new ArrayList<>();
    private LandingPageService.Context landingPageContext;
    private CreativeExtraService.Context creativeExtraContext;
    private DynHiddenTemplateService.Context dynHiddenTemplateContext;

    private Integer accountId;
    private Integer campaignId;
    private Integer unitId;
    private boolean isProgrammatic;
    private boolean isV2Programmatic;
    private boolean needDeleteOldExploreCreative;
    private Map<Long, BiliArchiveBo> biliArchiveMap;
    private Map<Long, ResImageBo> resImageMap;
    // 创意影子需要审核 Map；只有有影子白名单，并且投放中编辑 or 曾经有影子，再次触审核时才会有 true
    private Map<Integer, Boolean> needShadowAuditMap = new HashMap<>();
    private Map<Integer, Integer> onlyCpsLinkReplaceUpdateMap = new HashMap<>();
    private Map<Long, OpusCardBo> opusCardMap;
    private Map<Long, PugvArchiveBo> pugvArchiveMap;
    private Map<Long, CreativeContentGoodsBo> creativeContentGoodsBoMap = new HashMap<>();

    // 自动投放 母创意map
    private Map<Integer, LauUnitCreativeBo> parentUnitCreativeBoMap = new HashMap<>();
    // aaa项目 特批跳过所有的账户权限校验
    private boolean needSkipAccountAuthorize;
    // Deprecated
    // private boolean pcHiddenFlag;
    private boolean ottHiddenFlag;
    private boolean storyHiddenFlag;

    //个人起飞
    private boolean personalUp;



    /**
     * 原生主体类型，并不是用来判断是否是原生的
     *
     * @see sycpb.platform.cpm.pandora.service.constants.NativeBodyType
     */
    private Integer nativeBodyType;
    /**
     * 原生创意审核列表.
     */
    private List<NativeMsgItem> nativeCreativeAuditList = new ArrayList<>();

    /**
     * 原生稿件审核列表.
     */
    private List<NativeMsgItem> nativeArchiveAuditList = new ArrayList<>();

    /**
     * 动态表新增，防止创意级别动态信息冗余过多
     */
    private Map<Long, LauDynamicBo> dynamicBoMap = new HashMap<>();

    /**
     * 创建创编后的异步广播结构体
     */
    private UnitCreativeIdBroadCastBo unitCreativeIdBroadCastBo = new UnitCreativeIdBroadCastBo();


    public SaveUnitCreativeContextBo(OperatorBo operator) {
        this.operator = operator;
    }

    public SaveUnitCreativeContextBo(UnitCreativeBo newVersion, OperatorBo operator) {
        this.newVersion = newVersion;
        this.operator = operator;
        accountId = operator.getOperatorId();
        unitId = newVersion.getUnit().getLauUnitBo().getUnitId();
        isProgrammatic = NumberUtils.isPositive(newVersion.getUnit().getLauUnitBo().getIsProgrammatic());
    }

    public BiConsumer<String, List<ChangeLogBo>> genCreativeCompareConsumer() {
        return (k, v) -> {
            Assert.hasText(k, "compare runtime: failed to generate log group id");
            final var ik = Integer.parseInt(k);
            var bos = changeLogsMap.get(ik);
            if (CollectionUtils.isEmpty(bos)) {
                bos = new ArrayList<>();
                changeLogsMap.put(ik, bos);
            }
            bos.addAll(v);
        };
    }

    public BiConsumer<String, List<ChangeLogBo>> genUnitCompareConsumer() {
        return (k, v) -> unitChangeLogBos.addAll(v);
    }

    public Integer getGameBaseId() {
        return Optional.ofNullable(lauUnitGameBo).map(LauUnitGameBo::getGameBaseId).orElse(0);
    }

    // 干净的日志提取
    public List<OperationLogContextBo> buildOperationLogContextBos(SaveUnitCreativeContextBo ctx) {
        final var opLogCtxBos = new ArrayList<OperationLogContextBo>();
        for (Map.Entry<Integer, List<ChangeLogBo>> entry : ctx.getChangeLogsMap().entrySet()) {
            final var logCtx = OperationLogContextBo.newContext(ctx.getOperator(), "lau_unit_creative");
            opLogCtxBos.add(logCtx);
            logCtx.updateContext(entry.getKey(), ctx.getOperationTypeMap().get(entry.getKey()));
            logCtx.setChanges(entry.getValue());
        }

        return opLogCtxBos;
    }

    public boolean isDPACreative() {
        return lauUnitBo != null && lauUnitBo.getPromotionPurposeType().equals(PromotionContentType.PPC_DYNAMIC_GOODS_VALUE);
    }
}
