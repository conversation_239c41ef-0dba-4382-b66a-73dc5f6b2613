package sycpb.platform.cpm.pandora.service.api.core.v6.creative.bos;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import sycpb.platform.cpm.pandora.infra.compare.CompareMeta;

import java.sql.Timestamp;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class LauNativeArchiveBo {

    @CompareMeta(copyFromCurrentVersion = true, uk = true, comparable = false)
    private Long id;

    @CompareMeta(comparable = false)
    private Long avid;

    /**
     * biz type:1三连2必火
     */
    private Integer bizType;

    /**
     * @see sycpb.platform.cpm.pandora.service.constants.NativeBodyType
     */
    private Integer type;

    @CompareMeta(logDescription = "拒审原因")
    private String reason;

    @CompareMeta(logDescription = "审核状态")
    private Integer auditStatus;
    private Integer   shallowAuditStatus;

    private Integer isRecheck;

    private Integer version;

    @CompareMeta(logDescription = "title")
    private String title;

    private Long upMid;

    @CompareMeta(logDescription = "cover")
    private String cover;

    private String upNickname;

    private String tagIds;

    private Timestamp submitTime;
}
