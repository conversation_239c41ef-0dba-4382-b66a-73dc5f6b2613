<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>sycpb.platform</groupId>
        <artifactId>cpm-pandora</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <artifactId>pandora-service</artifactId>

    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>sycpb.platform</groupId>
            <artifactId>pandora-infra</artifactId>
            <version>${parent.version}</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.drewnoakes</groupId>
            <artifactId>metadata-extractor</artifactId>
            <version>2.19.0</version>
        </dependency>
        <dependency>
            <groupId>com.bilibili.business.content</groupId>
            <artifactId>business-content-api</artifactId>
            <version>1.0.1</version>
        </dependency>
        <dependency>
            <groupId>pleiades.venus</groupId>
            <artifactId>infoc</artifactId>
            <version>${pleiades.version}</version>
        </dependency>
        <dependency>
            <groupId>pleiades.venus</groupId>
            <artifactId>starter</artifactId>
            <version>${pleiades.version}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>${easyexcel.version}</version>
        </dependency>
        <dependency>
            <groupId>sycpb.platform</groupId>
            <artifactId>boggart-logback</artifactId>
            <version>${boggart.version}</version>
        </dependency>
        <dependency>
            <groupId>sycpb.platform</groupId>
            <artifactId>boggart-datasource-proxy</artifactId>
            <version>${boggart.version}</version>
        </dependency>
        <dependency>
            <groupId>sycpb.platform</groupId>
            <artifactId>boggart-grpc</artifactId>
            <version>${boggart.version}</version>
        </dependency>

    </dependencies>

</project>