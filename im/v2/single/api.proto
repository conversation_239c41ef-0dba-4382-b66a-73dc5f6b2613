syntax = "proto3";

package im.v2.single;

import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "extension/wdcli/wdcli.proto";

option (wdcli.appid) = "community.msg.single";
option go_package = "buf.bilibili.co/bapis/bapis-gen/im/v2.single;api";
option java_package = "com.bapis.im.v2.single";
option java_multiple_files = true;

message UnreadReq{
  uint64 uid = 1[(gogoproto.moretags) = 'validate:"gt=0,required"'];
  repeated UnreadType unread_type = 2;
}

message SessUnreadInfo{
  // l1数目（手动私信、自动回复、通知卡）
  uint32 l1_unread = 1;
  // l2数目（营销批量私信）
  uint32 l2_unread = 2;
  // 弱提示通知卡数目
  uint32 weak_notify_unread = 3;
  // 弱提示自动回复数目
  uint32 weak_auto_reply_unread = 4;
}

message UnreadRsp{
  message Info{
    // deprecated 通知卡未读
    bool notify_unread = 1;
    // 未读数明细 key是talkerUid
    map<uint64, SessUnreadInfo> count = 2;
  }
  // key是UnreadType
  map<int32, Info> info = 1;
}

message InboxReq{
  uint64 uid = 1[(gogoproto.moretags) = 'validate:"gt=0,required"'];
  // key是talker_uid
  map<uint64, InboxItem> items = 2[(gogoproto.moretags) = 'validate:"min=1,max=30,required"'];
}

message InboxItem{
  // 起始位置
  uint64 begin = 1;
  uint32 size = 2[(gogoproto.moretags) = 'validate:"gt=0,lte=100,required"'];
  Order order = 3;
}

message InboxRsp{
  // key是talker_uid
  map<uint64, Inbox> inbox = 1;
}

message Inbox{
  repeated Msg msg_list = 1;
}

message Msg {
  // 消息唯一key
  uint64 msg_key = 1;
  // 消息序列号
  uint64 seq_no = 2;
}

message MsgListReq{
  uint64 uid = 1[(gogoproto.moretags) = 'validate:"gt=0,required"'];
  // key是talker_uid
  map<uint64, InboxItem> items = 2[(gogoproto.moretags) = 'validate:"min=1,max=30,required"'];
}

message MsgItems{
  repeated MsgItem msg_items = 1;
}

message MsgListRsp{
  // key是talker_uid
  map<uint64, MsgItems> msg_list = 1;
}

message MsgDetailsReq{
  repeated uint64 msg_keys = 1[(gogoproto.moretags) = 'validate:"min=1,max=30"'];
}

message MsgDetailsRsp{
  // key是msg_key
  map <uint64, MsgItem> details = 1;
}

message MsgItem {
  //发送方uid
  uint64 sender_uid = 1;
  //接收方id
  uint64 receiver_uid = 2;
  //消息类型
  MsgType type = 3;
  //消息内容
  string content = 4;
  //服务端的序列号
  uint64 seq_no = 5;
  //消息发送时间（服务端时间）
  uint64 timestamp = 6;
  //消息唯一标示
  uint64 msg_key = 7;
  //消息状态
  MsgStatus status = 8;
  //通知码
  string notify_code = 9;
  //消息来源
  MsgSource source = 10;
  //扩展字段
  MsgExt ext = 11[(gogoproto.jsontag) = "ext"];
}


message MsgExt {
  // 批量私信的sceneId
  string biz_scene_id = 1;
}

message AllAckReq{
  uint64 uid = 1[(gogoproto.moretags) = 'validate:"gt=0,required"'];
  AllAckScope scope = 2;
}

message AllAckRsp{}


message Empty{}

message SessionItem {
  uint64 uid = 1;
  uint64 talker_uid = 2;
  uint64 session_ts = 3;
  // 置顶时间戳
  uint64 top_ts = 4;
  // 免打扰开启
  bool dnd = 5;
  // 已读消息的seqno
  uint64 ack_seqno = 6;
  // 状态 0正常 1删除 2不明 3不明 4垃圾箱
  SessionStatus status = 7;
  // uid是否关注talker_uid
  bool follow = 8;
  // 陌生人
  bool stranger = 9;
  // 花火
  bool huahuo = 10;
  // 未读详情
  SessUnreadInfo unread = 11;
  // 会话被删除的seqno
  uint64 del_seqno = 12;
  // 是否有新通知卡未读
  bool notify_unread = 13;
  // 从垃圾箱移出过,代表信任
  bool trust = 14;
  // 推送设置。是否接收非通知卡批量私信
  bool receive_ad_biz_msg = 15;
  // 是否被拦截（垃圾箱）
  bool is_intercept = 16;
}


message SessionsUnTopReq{
  uint64 uid = 1 [(gogoproto.moretags) = 'validate:"gt=0,required"'];
  uint64 begin = 2; // 返回比begin小的
  uint32 size = 3[(gogoproto.moretags) = 'validate:"gt=0,lte=30,required"'];
  SessionType type = 4;
}

message SessionsTopReq{
  uint64 uid = 1 [(gogoproto.moretags) = 'validate:"gt=0,required"'];
  SessionType type = 2;
}

message NewSessionsUnTopReq{
  uint64 uid = 1 [(gogoproto.moretags) = 'validate:"gt=0,required"'];
  uint64 begin = 2; // 返回比begin大的
  uint32 size = 3[(gogoproto.moretags) = 'validate:"gt=0,lte=30,required"'];
  SessionType type = 4;
}

message NewSessionsTopReq{
  uint64 uid = 1 [(gogoproto.moretags) = 'validate:"gt=0,required"'];
  uint64 begin = 2; // 返回比begin大的
  SessionType type = 3;
}

message SessionsRsp{
  repeated SessionItem sessions = 1;
  bool has_more = 2;
}

message SessionDetailsReq{
  uint64 uid = 1 [(gogoproto.moretags) = 'validate:"gt=0,required"'];
  repeated uint64 talker_uid = 2[(gogoproto.moretags) = 'validate:"gt=0,min=1,max=30"'];
}

message SessionDetailsRsp{
  map<uint64, SessionItem> sessions = 1;
}

enum MsgType {
  SMT_UNKNOWN = 0;
  // 文本
  SMT_TEXT = 1;
  // 图片
  SMT_PIC = 2;
  // 音频
  SMT_AUDIO = 3 [deprecated = true];
  // 分享
  SMT_SHARE = 4 [deprecated = true];
  // 用户撤回
  SMT_DRAWBACK = 5;
  // 自定义表情
  SMT_CUSTOM_FACE = 6;
  // 分享V2
  SMT_SHARE_V2 = 7;
  // 系统撤销
  SMT_SYS_CANCEL = 8;
  // 小程序
  SMT_MINI_PROGRAM = 9;
  // 通知卡片
  SMT_NOTIFY_MSG = 10;
  // 视频卡片
  SMT_VIDEO_CARD = 11;
  // 专栏卡片
  SMT_ARTICLE_CARD = 12;
  // 异形卡
  SMT_PICTURE_CARD = 13;
  // 通用分享卡
  SMT_COMMON_SHARE_CARD = 14;
  // 来自分享的文本
  SMT_TEXT_SHARE = 15;
  // 推荐卡
  SMT_RECOMMEND_CARD = 16;
  // 插入到消息列表的提示
  SMT_SYSTEM_NOTE = 18;
  // gpt消息
  SMT_GPT_MSG = 19;
  // 消息变更
  SMT_MODIFY = 51;
  // ai消息
  SMT_AI = 52;
  // 富文本消息
  SMT_RICH_TEXT = 53;
}

enum MsgStatus{
  // 正常
  Normal = 0;
  // 用户撤回
  UserWithdraw = 1;
  //系统撤回
  SysWithdraw = 2;
  // 假发送（自见）
  FakeSend = 3;
  // 假发送消息被撤回时更新为这个状态
  FakeSendWithdraw = 4;
  // 待审核（自见）
  Verify = 5;
  // 待审核消息被撤回时更新为这个状态
  VerifyWithdraw = 6;
  // 审核驳回（自见）
  VerifyReject = 7;
  // 审核驳回消息被撤回时更新为这个状态
  WithDrawVerifyReject = 8;
  // AI审核驳回（双方可见，展示一张分裂样式图片）
  AIVerifyReject = 50;
}

enum MsgSource{
  UnKnow = 0;
  // 粉板
  Ios = 1;
  // 粉板
  Android = 2;
  // 未知
  H5 = 3;
  // 未知
  PC = 4;
  // 批量私信后台
  BizStage = 5;
  // 接口批量私信
  Biz = 6;
  // web页或pc客户端
  Web = 7;
  //关注自动回复
  FollowAutoReply = 8;
  //收到私信自动回复
  ReceiveMsgAutoReply = 9;
  //收到私信&命中关键词，自动回复
  KeyWordAutoReply = 10;
  //大航海自动回复
  VoyageAutoReply = 11;
  // 附赠留言
  AttachMsg = 12;
  //未知
  GroupSystemMsg = 13;
  //花火自动化私信
  FireworkAuto = 14;
  //花火非自动化私信
  Firework = 15;
  //机审后台
  AegisBackstage = 16;
  // 互关自动打招呼
  FollowAutoGreet = 17;
  // 插入到消息列表的提示文案，比如防骚扰提示
  SystemNote = 18;
  // ai、gpt
  Ai_Gpt = 19;
  // Ai自动打招呼消息
  AiHello = 20;
  // 感谢互动
  ThanksMsgFeed = 21;
}


enum UnreadType{
  //0
  Invalid = 0;
  //1:关注的人未读数
  Follow = 1;
  //2:未关注的人未读数
  UnFollow = 2;
  //3:垃圾箱未读数
  Dustbin = 3;
  //4:陌生人未读数
  Stranger = 4;
  //5:花火
  Huahuo = 5;
}


enum AllAckScope {
  S_Ack_All = 0;
  S_Ack_Dustbin = 1;
  S_Ack_Unfollowed = 2;
  S_Ack_Stranger = 3;
}


enum Order{
  Asc = 0;
  Desc = 1;
}

enum SessionStatus{
  // 正常
  SS_Normal = 0;
  // 删除
  SS_Delete = 1;
  // 关系链拉黑
  SS_Black = 2;
  // 不明
  SS_SessionStatus3 = 3;
  // 垃圾箱
  SS_Dustbin = 4;
}

enum SessionType{
  S_UNCLEAR = 0;
  // 关注
  S_Follow = 1;
  // 未关注
  S_UnFollow = 2;
  // 陌生人
  S_Stranger = 3;
  // 垃圾箱
  S_Dustbin = 4;
  // 花火
  S_Huahuo = 5;
  // 正常(不含垃圾箱) && 筛掉陌生人
  S_ExceptStranger = 6;
  // 正常(不含垃圾箱)
  S_NORMAL = 7;
}

message TopReq{
  uint64 uid = 1 [(gogoproto.moretags) = 'validate:"gt=0,required"'];
  uint64 talker_uid = 2[(gogoproto.moretags) = 'validate:"gt=0,required"'];
}

message TopRsp{
  SessionItem session = 1;
}

message UnTopReq{
  uint64 uid = 1 [(gogoproto.moretags) = 'validate:"gt=0,required"'];
  uint64 talker_uid = 2[(gogoproto.moretags) = 'validate:"gt=0,required"'];
}

message UnTopRsp{
  SessionItem session = 1;
}

message RemoveSessionReq{
  uint64 uid = 1 [(gogoproto.moretags) = 'validate:"gt=0,required"'];
  uint64 talker_uid = 2[(gogoproto.moretags) = 'validate:"gt=0,required"'];
}

message RemoveSessionRsp{}

message DelMsgReq{
  repeated DelMsgItem msgs = 1[(gogoproto.moretags) = 'validate:"gt=0,min=1,max=30"'];
}

message DelMsgRsp{
  repeated DelMsgItem fail = 1;
}

message MsgDelInfoReq{
  repeated DelMsgItem msgs = 1[(gogoproto.moretags) = 'validate:"min=1,max=30"'];
}

message DelMsgItem{
  uint64 uid = 1[(gogoproto.moretags) = 'validate:"gt=0,required"'];
  uint64 talker_uid = 2[(gogoproto.moretags) = 'validate:"gt=0,required"'];
  uint64 msg_key = 3[(gogoproto.moretags) = 'validate:"gt=0,required"'];
  uint64 seq_no = 4[(gogoproto.moretags) = 'validate:"gt=0,required"'];
}

message MsgDelInfoRsp{
  repeated MsgDelInfo msgs = 1;
}

message MsgDelInfo{
  DelMsgItem msg = 1;
  uint64 del_micro_ts = 2; // 毫秒删除时间戳，0代表没删除
}

message FetchMsgKeysReq {
  repeated SeqnoItem params = 1[(gogoproto.moretags) = 'validate:"min=1,max=50"'];
}

message FetchMsgKeysRsp{
  repeated SeqnoAndMsgKey msgs = 1;
}

message SeqnoAndMsgKey{
  SeqnoItem msg_index = 1; // 对应一个查询seqno
  uint64 msg_key = 2;
}

message SeqnoItem {
  uint64 uid = 1[(gogoproto.moretags) = 'validate:"gt=0,required"'];
  uint64 talker_id = 2[(gogoproto.moretags) = 'validate:"gt=0,required"'];
  uint64 seqno = 3[(gogoproto.moretags) = 'validate:"gt=0,required"'];
}

message BatchRemoveSessionRsp{}

message BatchRemoveSessionReq{
  uint64 uid = 1[(gogoproto.moretags) = 'validate:"gt=0,required"'];
  SessionType type = 2;
}

message AckOneSessionReq{
  uint64 uid = 1[(gogoproto.moretags) = 'validate:"gt=0,required"'];
  uint64 talker_uid = 2[(gogoproto.moretags) = 'validate:"gt=0,required"'];
}

message AckOneSessionRsp{}

message ReleaseOneDustbinRsp{}

message ReleaseOneDustbinReq{
  uint64 uid = 1[(gogoproto.moretags) = 'validate:"gt=0,required"'];
  uint64 talker_uid = 2[(gogoproto.moretags) = 'validate:"gt=0,required"'];
}

message ReleaseAllDustbinReq{
  uint64 uid = 1[(gogoproto.moretags) = 'validate:"gt=0,required"'];
}

message LatestRelatedMembersReq {
  int64 uid = 1[(gogoproto.moretags) = 'validate:"required"'];
}

message LatestRelatedMembersRsp {
  repeated LatestRelatedMember members = 1;
}

message LatestRelatedMember {
  // member uid
  uint64 uid = 1;
  // 最新聊天时间
  int64 ts = 2;
}

message ReleaseAllDustbinRsp{}

message AiUidListRsp{
  map<uint64, uint64> ai_uid_map = 1; // key: up uid, value: ai uid
}

message SendMsgReq {
  uint64 sender_uid = 1[(gogoproto.moretags) = 'validate:"gt=0,required"'];//发送方uid
  uint64 receiver_uid = 2[(gogoproto.moretags) = 'validate:"gt=0,required"'];//接收方id
  MsgType msg_type = 3[(gogoproto.moretags) = 'validate:"required"'];//消息类型
  string content = 4[(gogoproto.moretags) = 'validate:"gt=0,required"'];//消息内容
  MsgSource msg_source = 5[(gogoproto.moretags) = 'validate:"required"'];//消息来源
  string canal_token = 6; // 通用豁免token
  WbiSignVerify wbi_sign_verify = 7;// apigw wbi验签结果
  string refer = 8;
  uint64 devcrc32 = 9;
  Device device = 10;
  NetWork net_work = 11;
  Fawkes fawkes = 12;
  Auth auth = 13;
  AutoReply auto_reply = 14;
  bool new_face_version = 15;
}

enum WbiSignVerify{
  SUC = 0; //成功
  FAIL = 1; //失败 篡改或者风险设备或者过期（8小时，不支持按业务配置）
  MISS_SIGN = 2; //无密钥
}

message AutoReply {
  uint64 id = 1;
  string text = 2;
  uint32 type = 3;
}

message Auth{
  string auth_key = 1;
  string auth_type = 2;
  string auth_value = 3;
}

message Fawkes{
  string app_key = 1;
  string env = 2;
}

message NetWork{
  // 网络类型
  uint64 type = 1;
  // 免流类型
  uint64 tf = 2;
  // 运营商
  string operator = 3;
  // 客户端IP
  string remote_ip = 4;
  // 客户端端口
  string remote_port = 5;
  // webcdn ip
  string webcdn_ip = 6;
}

message Device {
  // cookie: sid
  string sid = 1;
  string  buvid = 2;
  string  user_agent = 3;
  string  buvid3 = 4;
  // app: 包类型 同RawMobilApp
  string  mobil_app = 5;
  uint64  build = 6;
  // app: 系统类型
  string  platform = 7;
  // app: 市场渠道
  string channel = 8;
  // app: 手机型号
  string model = 9;
  // app: 系统版本
  string os_ver = 10;
  // 本地设备指纹
  string fp_local = 11;
  // 远程设备指纹
  string fp_remote = 12;
  // 版本号(version_name)
  string version_name = 13;
  // app: 手机品牌
  string brand = 14;
  // app: 运行设备
  string device = 15;
}

message SendMsgRsp {
  uint64 msg_key = 1;
  uint64 seq_no = 2;
  string msg_content = 3;
  //表情资源信息
  repeated Emotion emotions = 4;
  //提示,比如转账警告
  Prompt prompt = 5;
};

message Prompt {
  string toast = 1;
  uint32 rule_id = 2;
  repeated HighText high_text = 3;
}

message HighText {
  string title = 1;
  string url = 2;
  uint32 index = 3;
  ActionType action = 4;
}

enum ActionType {
  INVALID = 0; //无效
  RECALL = 1; //撤回
}

message Emotion {
  string text = 1;
  string url = 2;
  EmoteSize  size = 3;
  string gif_url = 4;
};

enum EmoteSize {
  UnSpecial = 0;
  Small = 1;
  Big = 2;
}

message SessionToastReq {
  uint64 uid = 1[(gogoproto.moretags) = 'validate:"required"'];
}

message SessionToastRsp {
  // 警告提示
  WarningToast warning_toast = 1;
  // 禁言提示
  ImBannedToast im_banned_toast = 2;
}

message WarningToast {
  // 警告提示
  bool warning_toast = 1;
}

message ImBannedToast {
  // 禁言提示
  bool im_banned_toast = 1;
  // 禁言截止时间
  int64 im_banned_end_time = 2;
  // 是否永久禁言
  bool is_permanent = 3;
}

message ClearSessionToastReq {
  ToastType toast_type = 1[(gogoproto.moretags) = 'validate:"required"'];
  uint64 uid = 2[(gogoproto.moretags) = 'validate:"required"'];
}

enum ToastType {
  // 无
  ToastTypeDefault = 0;
  // 禁言
  ToastTypeBanned = 1;
  // 举报
  ToastTypeReport = 2;
}

message ShareListReq{
  uint64 uid = 1[(gogoproto.moretags) = 'validate:"gt=0,required"'];
}

message ShareListRsp{
  repeated uint64 uid = 1;
}

message LoadAllShowPushWhiteReq{
}

message LoadAllShowPushWhiteRsp{
  repeated uint64 uid_white_list = 1;
}

message SetPushSSReq{
  uint64 uid = 1[(gogoproto.moretags) = 'validate:"gt=0,required"'];
  uint64 talker_uid = 2[(gogoproto.moretags) = 'validate:"gt=0,required"'];
  bool is_receive = 3;
}

message SetPushSSRsp{}

message SetMsgDndReq{
  uint64 uid = 1[(gogoproto.moretags) = 'validate:"gt=0,required"'];
  uint64 talker_uid = 2[(gogoproto.moretags) = 'validate:"gt=0,required"'];
  bool is_dnd = 3;
}
message SetMsgDndRsp{}

service Single {
  //单聊未读数
  rpc Unread (UnreadReq) returns  (UnreadRsp);
  //单聊消息列表 必区间
  rpc Inbox (InboxReq) returns  (InboxRsp);
  //单聊消息详情
  rpc MsgDetails (MsgDetailsReq) returns  (MsgDetailsRsp);
  //单聊一键已读
  rpc AllAck (AllAckReq) returns  (AllAckRsp);
  //单聊置顶会话(全部)
  rpc SessionsTop (SessionsTopReq) returns  (SessionsRsp);
  //单聊会话未置顶列表
  rpc SessionsUnTop (SessionsUnTopReq) returns  (SessionsRsp);
  //轮询单聊新未置顶会话
  rpc NewSessionsUnTop (NewSessionsUnTopReq) returns  (SessionsRsp);
  //批量查单聊会话详情
  rpc SessionDetails (SessionDetailsReq) returns  (SessionDetailsRsp);
  //置顶会话
  rpc Top (TopReq) returns  (TopRsp);
  //取消置顶会话
  rpc UnTop (UnTopReq) returns  (UnTopRsp);
  //删除单条会话
  rpc RemoveSession (RemoveSessionReq) returns  (RemoveSessionRsp);
  //删除一个类型的会话
  rpc BatchRemoveSession (BatchRemoveSessionReq) returns  (BatchRemoveSessionRsp);
  //批量删除私信
  rpc DelMsg (DelMsgReq) returns  (DelMsgRsp);
  //查询私信删除记录
  rpc MsgDelInfo(MsgDelInfoReq) returns  (MsgDelInfoRsp);
  //查seqno对应的msgkey
  rpc FetchMsgKeysBySeqno (FetchMsgKeysReq) returns  (FetchMsgKeysRsp);
  //已读一个会话
  rpc AckOneSession (AckOneSessionReq) returns  (AckOneSessionRsp);
  //从垃圾箱移出
  rpc ReleaseOneDustbin (ReleaseOneDustbinReq) returns  (ReleaseOneDustbinRsp);
  //垃圾箱会话全部恢复正常
  rpc ReleaseAllDustbin (ReleaseAllDustbinReq) returns  (ReleaseAllDustbinRsp);
  // AI 账号列表 map, key 为 up uid, value 为 ai uid
  rpc AiUidList(Empty) returns (AiUidListRsp);
  // c端用户发私信
  rpc SendMsg(SendMsgReq) returns (SendMsgRsp);
  // 会话页提示查询
  rpc SessionToast(SessionToastReq) returns (SessionToastRsp);
  // 警告提示清理
  rpc ClearSessionToast(ClearSessionToastReq) returns (Empty);
  // 私信分享的推荐uid列表
  rpc ShareList(ShareListReq) returns (ShareListRsp);
  // 私信列表
  rpc MsgList(MsgListReq) returns (MsgListRsp);
  // 是否展示推送设置所有白名单
  rpc LoadAllShowPushWhite(LoadAllShowPushWhiteReq) returns (LoadAllShowPushWhiteRsp);
  // 操作推送设置
  rpc SetPushSS(SetPushSSReq) returns (SetPushSSRsp);
  // 操作消息免打扰
  rpc SetMsgDnd(SetMsgDndReq) returns (SetMsgDndRsp);
}