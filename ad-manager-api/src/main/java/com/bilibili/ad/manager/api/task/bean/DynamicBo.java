package com.bilibili.ad.manager.api.task.bean;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class DynamicBo {

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class DynamicOwnerBo {
        private Long mid;
        private String name;
        private String face;
    }

    private DynamicOwnerBo owner;
    private Long id;
    private Integer type;
    private Long rid;
    private String content;
    private String url;
    private List<String> imageUrls;
}
