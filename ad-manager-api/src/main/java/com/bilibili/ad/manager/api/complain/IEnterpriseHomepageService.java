package com.bilibili.ad.manager.api.complain;

import com.bilibili.ad.manager.api.complain.dto.*;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.exception.ServiceException;

import java.util.List;

public interface IEnterpriseHomepageService {

    List<HomePageCarouselDto> getHomePageCarouselList() throws ServiceException;

    PageResult<HomePageAnnouncementDto> getHomePageAnnouncementList(Integer page, Integer size) throws ServiceException;

    PageResult<HomePageGuidanceDto> getHomePageGuidanceList(Integer page, Integer size) throws ServiceException;

    PageResult<HomePageCaseDto> getHomePageCaseList(Integer page, Integer size) throws ServiceException;

    PageResult<HomePageNoticeDto> getHomePageNoticeList(Integer page, Integer size) throws ServiceException;

    Long addHomePageCarousel(HomePageCarouselDto dto) throws ServiceException;

    Long addHomePageAnnouncement(HomePageAnnouncementDto dto) throws ServiceException;

    Long addHomePageGuidance(HomePageGuidanceDto dto) throws ServiceException;

    Long addHomePageCase(HomePageCaseDto dto) throws ServiceException;

    Long addHomePageNotice(HomePageNoticeDto dto) throws ServiceException;

    void updateHomePageCarousel(HomePageCarouselDto dto) throws ServiceException;

    void updateHomePageAnnouncement(HomePageAnnouncementDto dto) throws ServiceException;

    void updateHomePageGuidance(HomePageGuidanceDto dto) throws ServiceException;

    void updateHomePageCase(HomePageCaseDto dto) throws ServiceException;

    void updateHomePageNotice(HomePageNoticeDto dto) throws ServiceException;

    void deleteHomePageCarousel(Long id) throws ServiceException;

    void deleteHomePageAnnouncement(Long id) throws ServiceException;

    void deleteHomePageGuidance(Long id) throws ServiceException;

    void deleteHomePageCase(Long id) throws ServiceException;

    void setHomePageNoticePushStatus(Long id, Integer canPush) throws ServiceException;

}
