package com.bilibili.ad.manager.api.flypaycard.dto;

import cn.hutool.core.date.DateUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FlyPayCardSystemRechargeOptDto implements Serializable {
    private static final long serialVersionUID = 4547976821009587759L;
    @ApiModelProperty("附件")
    private List<String> url;

    @ApiModelProperty("mid")
    private List<RechargeInfo> rechargeMappings;

    @ApiModelProperty("业务方id")
    private String businessId;

    @ApiModelProperty("自定义备注")
    private CustomInfo customInfo;

    @ApiModelProperty("发放原因（C端可见）")
    private String remark;

    @ApiModelProperty("有效截止时间yyyy-MM-dd")
    private String expire;

    @ApiModelProperty("是否有过期时间，默认为true")
    private Boolean hasExpire;

    @ApiModelProperty("token防止前端页面重复点击")
    private String token;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RechargeInfo {

        @ApiModelProperty("充值金额")
        private BigDecimal rechargeAmount;

        @ApiModelProperty("充值账户mid列表")
        private List<Long> mids;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CustomInfo {

        @ApiModelProperty("操作人username")
        private String operator;

        @ApiModelProperty("附件url")
        private List<String> attachments;
    }

    public boolean validBusinessId() {
        return businessId != null && businessId.length() > 0;
    }

    public boolean validUrl() {
        return url != null && !url.isEmpty();
    }

    public boolean validToken() {
        return token != null && !token.isEmpty();
    }

    public boolean validRemark() {
        return remark != null && remark.length() > 0;
    }

    public boolean validExpireDay() {
        if (hasExpire) {
            return expire != null && expire.length() > 0 && DateUtil.parse(expire, "yyyy-MM-dd").compareTo(DateUtil.date()) > 0;
        }
        return true;
    }

    public boolean validOperator() {
        return customInfo != null && customInfo.getOperator() != null && customInfo.getOperator().length() > 0;

    }

    public boolean validRechargeMappings() {
        if (!rechargeMappings.isEmpty()) {
            for (RechargeInfo t : rechargeMappings) {
                if (t.getMids().isEmpty() || t.getMids().stream().distinct().count() != t.getMids().size() || t.getRechargeAmount().longValue() <= 0)
                    return false;
            }
            return true;
        }
        return false;

    }

    public boolean verify() {
        return validBusinessId() && validUrl() && validRemark() && validExpireDay() && validToken() && validOperator() && validRechargeMappings();
    }
}
