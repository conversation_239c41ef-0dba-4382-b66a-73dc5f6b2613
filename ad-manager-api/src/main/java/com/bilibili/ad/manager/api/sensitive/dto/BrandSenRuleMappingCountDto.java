package com.bilibili.ad.manager.api.sensitive.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * @ClassName BrandSenRuleMappingBo
 * <AUTHOR>
 * @Date 2023/10/24 7:08 下午
 * @Version 1.0
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BrandSenRuleMappingCountDto {
    private Long ruleId;
    private Long midCount;
    private Timestamp maxMtime;
}
