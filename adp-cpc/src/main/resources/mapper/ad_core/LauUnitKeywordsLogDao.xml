<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.adp.cpc.dao.ad_core.mybatis.LauUnitKeywordsLogDao">
  <resultMap id="BaseResultMap" type="com.bilibili.adp.cpc.po.ad.LauUnitKeywordsLogPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="unit_id" jdbcType="INTEGER" property="unitId" />
    <result column="biz_type" jdbcType="INTEGER" property="bizType" />
    <result column="operate_username" jdbcType="VARCHAR" property="operateUsername" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.bilibili.adp.cpc.po.ad.LauUnitKeywordsLogPo">
    <result column="value" jdbcType="LONGVARCHAR" property="value" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, unit_id, biz_type, operate_username, ctime, mtime
  </sql>
  <sql id="Blob_Column_List">
    `value`
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.bilibili.adp.cpc.po.ad.LauUnitKeywordsLogPoExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from lau_unit_keywords_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByExample" parameterType="com.bilibili.adp.cpc.po.ad.LauUnitKeywordsLogPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from lau_unit_keywords_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from lau_unit_keywords_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from lau_unit_keywords_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.adp.cpc.po.ad.LauUnitKeywordsLogPoExample">
    delete from lau_unit_keywords_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.bilibili.adp.cpc.po.ad.LauUnitKeywordsLogPo" useGeneratedKeys="true">
    insert into lau_unit_keywords_log (unit_id, biz_type, operate_username, 
      ctime, mtime, `value`
      )
    values (#{unitId,jdbcType=INTEGER}, #{bizType,jdbcType=INTEGER}, #{operateUsername,jdbcType=VARCHAR}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{value,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.bilibili.adp.cpc.po.ad.LauUnitKeywordsLogPo" useGeneratedKeys="true">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_unit_keywords_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="unitId != null">
        unit_id,
      </if>
      <if test="bizType != null">
        biz_type,
      </if>
      <if test="operateUsername != null">
        operate_username,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="value != null">
        `value`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="unitId != null">
        #{unitId,jdbcType=INTEGER},
      </if>
      <if test="bizType != null">
        #{bizType,jdbcType=INTEGER},
      </if>
      <if test="operateUsername != null">
        #{operateUsername,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="value != null">
        #{value,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.adp.cpc.po.ad.LauUnitKeywordsLogPoExample" resultType="java.lang.Long">
    select count(*) from lau_unit_keywords_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update lau_unit_keywords_log
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.unitId != null">
        unit_id = #{record.unitId,jdbcType=INTEGER},
      </if>
      <if test="record.bizType != null">
        biz_type = #{record.bizType,jdbcType=INTEGER},
      </if>
      <if test="record.operateUsername != null">
        operate_username = #{record.operateUsername,jdbcType=VARCHAR},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.value != null">
        `value` = #{record.value,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update lau_unit_keywords_log
    set id = #{record.id,jdbcType=BIGINT},
      unit_id = #{record.unitId,jdbcType=INTEGER},
      biz_type = #{record.bizType,jdbcType=INTEGER},
      operate_username = #{record.operateUsername,jdbcType=VARCHAR},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      `value` = #{record.value,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update lau_unit_keywords_log
    set id = #{record.id,jdbcType=BIGINT},
      unit_id = #{record.unitId,jdbcType=INTEGER},
      biz_type = #{record.bizType,jdbcType=INTEGER},
      operate_username = #{record.operateUsername,jdbcType=VARCHAR},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.adp.cpc.po.ad.LauUnitKeywordsLogPo">
    update lau_unit_keywords_log
    <set>
      <if test="unitId != null">
        unit_id = #{unitId,jdbcType=INTEGER},
      </if>
      <if test="bizType != null">
        biz_type = #{bizType,jdbcType=INTEGER},
      </if>
      <if test="operateUsername != null">
        operate_username = #{operateUsername,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="value != null">
        `value` = #{value,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.bilibili.adp.cpc.po.ad.LauUnitKeywordsLogPo">
    update lau_unit_keywords_log
    set unit_id = #{unitId,jdbcType=INTEGER},
      biz_type = #{bizType,jdbcType=INTEGER},
      operate_username = #{operateUsername,jdbcType=VARCHAR},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      `value` = #{value,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.adp.cpc.po.ad.LauUnitKeywordsLogPo">
    update lau_unit_keywords_log
    set unit_id = #{unitId,jdbcType=INTEGER},
      biz_type = #{bizType,jdbcType=INTEGER},
      operate_username = #{operateUsername,jdbcType=VARCHAR},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>