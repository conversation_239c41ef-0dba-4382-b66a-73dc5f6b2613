package com.bilibili.adp.v6.report.bo;

import com.alibaba.fastjson.annotation.JSONField;
import com.bilibili.adp.cpc.utils.StringToIntListDeserializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 复制的代码，从web模块下沉到service模块
 *
 * @see com.bilibili.adp.advertiser.portal.webapi.effect_ad.SearchWordAnalysisReportController#export(com.bilibili.adp.advertiser.portal.common.Context, List, List, List, Integer, Integer, Integer, Long, Long, Integer, String, Integer, String, HttpServletResponse)
 */
@Data
@Builder
@AllArgsConstructor
public class ExportSearchWordAnalysisBo {

    @Range(min = 1, message = "账户id不能为空")
    private int accountId;

    @JSONField(name = "campaign_id", deserializeUsing = StringToIntListDeserializer.class)
    private List<Integer> campaignIds;

    @JSONField(name = "unit_id", deserializeUsing = StringToIntListDeserializer.class)
    private List<Integer> unitIds;

    @JSONField(name = "creative_id", deserializeUsing = StringToIntListDeserializer.class)
    private List<Integer> creativeIds;

    @JSONField(name = "time_type")
    private Integer timeType;

    @JSONField(name = "page")
    private Integer page;

    @JSONField(name = "size")
    private Integer size;

    @JSONField(name = "from_time")
    @NotNull(message = "开始时间不能为空")
    private Long fromTime;

    @JSONField(name = "to_time")
    @NotNull(message = "结束时间不能为空")
    private Long toTime;

    @JSONField(name = "group_by")
    private Integer groupBy;

    @JSONField(name = "sort_field")
    private String sortField;

    @JSONField(name = "key_word_group_by")
    private Integer keyWordGroupBy;

    @JSONField(name = "sort_type")
    private String sortType;

    @JSONField(name = "location_type")
    private Integer locationType;



    public ExportSearchWordAnalysisBo() {
        this.sortField = "pv";
        this.sortType = "1";
    }
}
