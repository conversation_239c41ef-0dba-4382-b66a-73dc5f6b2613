package com.bilibili.adp.v6.resource.dynamic.converters;

import com.bapis.account.service.Info;
import com.bapis.ad.commercialorder.adauth.GetPageAdAuthRs;
import com.bapis.dynamic.common.PictureInfo;
import com.bapis.dynamic.service.feed.DynInfo;
import com.bilibili.adp.v6.resource.common.CommercialOrderAuthCommonServiceConverter;
import com.bilibili.adp.v6.resource.dynamic.bos.CommercialOrderDynamicAuthBo;
import com.bilibili.adp.v6.resource.dynamic.bos.DynamicInfoBo;
import com.bilibili.adp.v6.resource.dynamic.bos.CommercialOrderDynamicStatusBo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(uses = CommercialOrderAuthCommonServiceConverter.class)
public interface CommercialOrderDynamicConverter {
    CommercialOrderDynamicConverter MAPPER = Mappers.getMapper(CommercialOrderDynamicConverter.class);

    @Mapping(target = "jumpUri", source = "dynInfo.extension.jumpUri")
    @Mapping(target = "ctime", expression = "java(com.bilibili.adp.common.util.Utils.isPositive(dynInfo.getSimpleInfo().getCtime())?dynInfo.getSimpleInfo().getCtime()*1000:null)")
    @Mapping(target = "mid", source = "info.mid")
    @Mapping(target = "name", source = "info.name")
    @Mapping(target = "face", source = "info.face")
    @Mapping(target = "dynamicId", source = "dynInfo.simpleInfo.dynId")
    @Mapping(target = "type", source = "dynInfo.simpleInfo.type")
    @Mapping(target = "rid", source = "dynInfo.simpleInfo.rid")
    @Mapping(target = "cover", source = "dynInfo.card.cover")
    @Mapping(target = "title", source = "dynInfo.card.title")
    @Mapping(target = "content", source = "dynInfo.card.content")
    @Mapping(target = "pictures", source = "dynInfo.card.pictures")
    DynamicInfoBo toBo(Info info, DynInfo dynInfo);

    default String toUrl(PictureInfo pictureInfo){
        return pictureInfo.getImgSrc();
    }


    @Mapping(target = "dynamicInfo", source = "dynamicBo")
    CommercialOrderDynamicStatusBo toBo(int authTimes, DynamicInfoBo dynamicBo);

    @Mapping(target = "authSource", source = "item.type")
    @Mapping(target = "authId", source = "item.applyId")
    @Mapping(target = "sourceAccountId", source = "item.accountId")
    @Mapping(target = "timeInfo", source = "item")
    @Mapping(target = "authStatus", source = "item.stateExt")
    @Mapping(target = "dynamicInfo", source = "dynamicBo")
    CommercialOrderDynamicAuthBo toBo(GetPageAdAuthRs.GetPageAdAuthRsItem item, DynamicInfoBo dynamicBo);
}
