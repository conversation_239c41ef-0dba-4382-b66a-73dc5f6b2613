package com.bilibili.adp.v6.report;

import com.bapis.ad.pluto.meta.AdMetaServiceGrpc;
import com.bapis.ad.pluto.service.v1.AdStatsServiceGrpc;
import org.springframework.stereotype.Component;
import pleiades.venus.starter.rpc.client.RPCClient;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.util.Objects;
import java.util.Properties;

@Component
public class PlutoProxy {

    private static String name;

    static {
        getAppName();
    }

    @RPCClient(value = "sycpb.platform.cpm-pluto")
    private AdMetaServiceGrpc.AdMetaServiceBlockingStub adMetaServiceBlockingStub;

    @RPCClient(value = "sycpb.platform.cpm-pluto")
    private AdStatsServiceGrpc.AdStatsServiceBlockingStub adStatsServiceBlockingStub;

    @RPCClient(value = "sycpb.platform.cpm-pluto-edge")
    private AdMetaServiceGrpc.AdMetaServiceBlockingStub adMetaEdgeServiceBlockingStub;

    @RPCClient(value = "sycpb.platform.cpm-pluto-edge")
    private AdStatsServiceGrpc.AdStatsServiceBlockingStub adStatsEdgeServiceBlockingStub;

    public AdMetaServiceGrpc.AdMetaServiceBlockingStub metaBlockingStub() {
        if (Objects.equals(name, "cpm-adp")) {
            return adMetaServiceBlockingStub;
        } else {
            return adMetaEdgeServiceBlockingStub;
        }
    }

    public AdStatsServiceGrpc.AdStatsServiceBlockingStub statBlockingStub() {
        if (Objects.equals(name, "cpm-adp")) {
            return adStatsServiceBlockingStub;
        } else {
            return adStatsEdgeServiceBlockingStub;
        }
    }

    public static void getAppName() {
        String fileName = "META-INF/app.properties";
        Properties prop = new Properties();
        try (InputStream input = PlutoProxy.class.getClassLoader().getResourceAsStream(fileName)) {
            if (input == null) {
                throw new FileNotFoundException("未找到配置文件: " + fileName);
            }
            prop.load(input);
            name = prop.getProperty("app.name");
        } catch (FileNotFoundException ex) {
            throw new RuntimeException("配置文件未找到: " + fileName, ex);
        } catch (IOException ex) {
            throw new RuntimeException("读取配置时出错", ex);
        }
    }
}