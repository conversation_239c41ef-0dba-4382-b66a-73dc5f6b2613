package com.bilibili.adp.v6.report;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.bilibili.adp.common.enums.SalesType;
import com.bilibili.adp.common.enums.StatQueryType;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.StringDateParser;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.services.campaign.api.ICpcCampaignService;
import com.bilibili.adp.cpc.biz.services.unit.api.ICpcUnitService;
import com.bilibili.adp.cpc.biz.services.unit.dto.QueryCpcUnitDto;
import com.bilibili.adp.cpc.core.LaunchPersonAnalysisService;
import com.bilibili.adp.cpc.core.bos.AllStatTargetParamBo;
import com.bilibili.adp.cpc.core.bos.PersonAnalysisStatBo;
import com.bilibili.adp.cpc.core.bos.PersonAnalysisTargetStatBo;
import com.bilibili.adp.cpc.dto.CpcLightUnitDto;
import com.bilibili.adp.cpc.enums.StatTargetMappings;
import com.bilibili.adp.cpc.biz.services.campaign.dto.CpcCampaignDto;
import com.bilibili.adp.cpc.biz.services.campaign.dto.QueryCpcCampaignDto;
import com.bilibili.adp.legacy.dto.ExportAllCrowdAnalysisDto;
import com.bilibili.adp.legacy.dto.ExportAllTargetStatDto;
import com.bilibili.adp.cpc.utils.ValidateUtil;
import com.bilibili.adp.v6.report.bo.ExportAnalysisExcelBo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.faces.event.ExceptionQueuedEvent;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import javax.validation.Validator;
import java.io.OutputStream;
import java.sql.Timestamp;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 复制方法，方法从web模块下沉到service模块
 *
 * @see com.bilibili.adp.advertiser.portal.webapi.flyPro.v2.helper.ManagedCampaignHelper
 */
@Slf4j
@Service
public class CrowdAnalysisExportService {
    @Autowired
    private ICpcCampaignService cpcCampaignService;
    @Autowired
    private ICpcUnitService cpcUnitService;
    @Autowired
    private LaunchPersonAnalysisService launchPersonAnalysisService;

    @Autowired
    private Validator validator;


    public void exportExcelAnalysis(ExportAnalysisExcelBo excelReqBo, OutputStream outputStream) throws Exception {

        ValidateUtil.validate(validator, excelReqBo);

        final String LOG_PREFIX = "导出人群分析";
        log.info(MessageFormat.format("{0}: 调用开始", LOG_PREFIX));
        AllStatTargetParamBo statTargetParam = AllStatTargetParamBo.builder()
                .accountIds(Lists.newArrayList(excelReqBo.getAccountId()))
                .fromDate(new Timestamp(excelReqBo.getFromTime()))
                .toDate(Utils.getEndOfDay(new Timestamp(excelReqBo.getToTime())))
                .campaignIds(excelReqBo.getCampaignIdList())
                .unitIds(excelReqBo.getUnitIdList())
                .creativeIds(excelReqBo.getCreativeIdList())
                .salesTypes(SalesType.PLATFORM_SALES_TYPES)
                .type(StatQueryType.CREATIVE.getCode())
                .build();

        log.info(MessageFormat.format("{0}: 调用报表准备结束", LOG_PREFIX));
        try {
            List<PersonAnalysisTargetStatBo> statTargetBoList = launchPersonAnalysisService.getAllPercentStatTargetsFromES(statTargetParam, false);
            log.info(MessageFormat.format("{0}: 调用报表结束 -> {1}", LOG_PREFIX, statTargetBoList.size()));
            String dateRange = StringDateParser.getDateString(statTargetParam.getFromDate())
                    + "~" + StringDateParser.getDateString(statTargetParam.getToDate());

            ExportAllCrowdAnalysisDto exportAnalysisStatVo = buildAdTargetStatVo(statTargetBoList, dateRange);

            log.info(MessageFormat.format("{0}: 组装报表结束", LOG_PREFIX));

            ExcelWriter writer = null;


            try {
                writer = EasyExcel.write(outputStream, ExportAllTargetStatDto.class)
                        .build();
                final WriteSheet ageSheet = EasyExcel.writerSheet("年龄")
                        .build();
                writer.write(exportAnalysisStatVo.getAge(), ageSheet);
                final WriteSheet genderSheet = EasyExcel.writerSheet("性别")
                        .build();
                writer.write(exportAnalysisStatVo.getGender(), genderSheet);
                final WriteSheet deviceSheet = EasyExcel.writerSheet("设备")
                        .build();
                writer.write(exportAnalysisStatVo.getDevice(), deviceSheet);
                final WriteSheet provinceSheet = EasyExcel.writerSheet("省级地域")
                        .build();
                writer.write(exportAnalysisStatVo.getProvince(), provinceSheet);
                final WriteSheet citySheet = EasyExcel.writerSheet("市级地域")
                        .build();
                writer.write(exportAnalysisStatVo.getCity(), citySheet);
            } finally {
                if (Objects.nonNull(writer)) writer.finish();
            }
        } catch (Exception ex) {
            log.error("人群分析报表下载发生异常", ex);
            String errorMsg = "生成报表错误";
            if (ex instanceof IllegalArgumentException) {
                if (StringUtils.isNotBlank(ex.getMessage())) {
                    errorMsg = ex.getMessage();
                }
            }
            throw new ServiceException(500, errorMsg);
        } finally {
            log.info(MessageFormat.format("{0}: 调用结束", LOG_PREFIX));
        }
    }

    private ExportAllCrowdAnalysisDto buildAdTargetStatVo(List<PersonAnalysisTargetStatBo> statTargetBoList, String dateRang) {
        if (CollectionUtils.isEmpty(statTargetBoList)) {
            return ExportAllCrowdAnalysisDto.empty();
        }

        List<Integer> campaginIds = statTargetBoList.stream().map(PersonAnalysisTargetStatBo::getCampaignId).distinct().collect(Collectors.toList());
        List<Integer> untiIds = statTargetBoList.stream().map(PersonAnalysisTargetStatBo::getUnitId).distinct().collect(Collectors.toList());

        List<CpcCampaignDto> cpcCampaignDtos = cpcCampaignService.queryCpcCampaign(QueryCpcCampaignDto.builder().campaignIds(campaginIds).build());
        Map<Integer, String> campaginNameMap = cpcCampaignDtos.stream().collect(Collectors.toMap(CpcCampaignDto::getCampaignId, CpcCampaignDto::getCampaignName));

        List<CpcLightUnitDto> cpcLightUnitDtos = cpcUnitService.queryLightUnits(QueryCpcUnitDto.builder().unitIds(untiIds).build());
        Map<Integer, String> unitNameMap = cpcLightUnitDtos.stream().collect(Collectors.toMap(CpcLightUnitDto::getUnitId, CpcLightUnitDto::getUnitName));

        ExportAllCrowdAnalysisDto crowdAnalysisVo = new ExportAllCrowdAnalysisDto();
        Map<String, List<PersonAnalysisTargetStatBo>> statTargetMap = statTargetBoList.stream()
                .collect(Collectors.groupingBy(PersonAnalysisTargetStatBo::getTargetType, Collectors.toList()));

        // 年龄
        List<PersonAnalysisTargetStatBo> ageTargetBoList = statTargetMap.getOrDefault(StatTargetMappings.AGE.getTargetType(), Collections.emptyList());
        crowdAnalysisVo.setAge(getAdTargetStatVos(ageTargetBoList, campaginNameMap, unitNameMap, dateRang));

        // 性别
        List<PersonAnalysisTargetStatBo> genderTargetBoList = statTargetMap.getOrDefault(StatTargetMappings.GENDER.getTargetType(), Collections.emptyList());
        crowdAnalysisVo.setGender(getAdTargetStatVos(genderTargetBoList, campaginNameMap, unitNameMap, dateRang));

        // 设备
        List<PersonAnalysisTargetStatBo> deviceTargetBoList = statTargetMap.getOrDefault(StatTargetMappings.DEVICE.getTargetType(), Collections.emptyList());
        crowdAnalysisVo.setDevice(getAdTargetStatVos(deviceTargetBoList, campaginNameMap, unitNameMap, dateRang));

        // 省份
        List<PersonAnalysisTargetStatBo> provinceTargetBoList = statTargetMap.getOrDefault(StatTargetMappings.PROVINCE.getTargetType(), Collections.emptyList());
        crowdAnalysisVo.setProvince(getAdTargetStatVos(provinceTargetBoList, campaginNameMap, unitNameMap, dateRang));

        // 城市
        List<PersonAnalysisTargetStatBo> cityTargetBoList = statTargetMap.getOrDefault(StatTargetMappings.CITY.getTargetType(), Collections.emptyList());
        crowdAnalysisVo.setCity(getAdTargetStatVos(cityTargetBoList, campaginNameMap, unitNameMap, dateRang));
        return crowdAnalysisVo;
    }

    private List<ExportAllTargetStatDto> getAdTargetStatVos(List<PersonAnalysisTargetStatBo> targetBoList,
                                                            Map<Integer, String> campaginNameMap,
                                                            Map<Integer, String> unitNameMap, String dateRang) {
        List<ExportAllTargetStatDto> targetVoList = new ArrayList<>(targetBoList.size());
        for (PersonAnalysisTargetStatBo bo : targetBoList) {
            ExportAllTargetStatDto statVo = new ExportAllTargetStatDto();
            statVo.setTargetName(bo.getTargetValueDesc());
            statVo.setCampaignId(bo.getCampaignId());
            statVo.setCampaignName(campaginNameMap.get(bo.getCampaignId()));
            statVo.setUnitId(bo.getUnitId());
            statVo.setUnitName(unitNameMap.get(bo.getUnitId()));
            statVo.setCreativeId(bo.getCreativeId());
            statVo.setDateRang(dateRang);

            PersonAnalysisStatBo pureBo = bo.getStatBo();
            statVo.setShowCount(pureBo.getShowCount());
            statVo.setClickCount(pureBo.getClickCount());
            statVo.setCost(pureBo.getCost());
            statVo.setOrderPlaceCount(pureBo.getOrderPlaceCount());
            statVo.setOrderPlaceAmount(pureBo.getOrderPlaceAmount());
            statVo.setUserCostCount(pureBo.getUserCostCount());
            statVo.setUserCostAmount(pureBo.getUserCostAmount());
            statVo.setGameSubscribeApiCount(pureBo.getGameSubscribeApiCount());
            statVo.setFanIncreaseCount(pureBo.getFanIncreaseCount());
            statVo.setIosAppActiveCount(pureBo.getIosAppActiveCount());
            statVo.setAndroidAppActiveCount(pureBo.getAndroidAppActiveCount());
            statVo.setAppActiveCount(pureBo.getAppActiveCount());
            statVo.setUserRegisterCount(pureBo.getUserRegisterCount());
            statVo.setFormSubmitCount(pureBo.getFormSubmitCount());
            statVo.setAndroidDownloadSuccessCount(pureBo.getAndroidDownloadSuccessCount());
            statVo.setAndroidInstallSuccessCount(pureBo.getAndroidInstallSuccessCount());
            statVo.setClueValidCount(pureBo.getClueValidCount());
            statVo.setRetentionCount(pureBo.getRetentionCount());
            statVo.setAppCallUpCount(pureBo.getAppCallUpCount());
            statVo.setLpCallUpCount(pureBo.getLpCallUpCount());
            statVo.setLpCallUpSuccessCount(pureBo.getLpCallUpSuccessCount());
            statVo.setLpCallUpSuccessStayCount(pureBo.getLpCallUpSuccessStayCount());
            statVo.setFormPaidCount(pureBo.getFormPaidCount());
            statVo.setAndroidGameCenterActivationCount(pureBo.getAndroidGameCenterActivationCount());
            statVo.setAccountSubscribeCount(pureBo.getAccountSubscribeCount());
            statVo.setPlayCount(pureBo.getPlayCount());
            statVo.setUnderBoxLinkCount(pureBo.getUnderBoxLinkCount());
            statVo.setDynamicDetailPageBrowseCount(pureBo.getDynamicDetailPageBrowseCount());
            statVo.setCommentUrlClickCount(pureBo.getCommentUrlClickCount());
            statVo.setCommentCallUpSuccessCount(pureBo.getCommentCallUpSuccessCount());

            targetVoList.add(statVo);
        }

        return targetVoList;
    }
}
