package com.bilibili.adp.v6.enums.mid;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum RequestTypeEnum {
    AUTHORIZING(1, "授权", "authorizing"),
    RENEWAL(2, "续期", "renewal"),
    UPDATE(3, "编辑", "update"),;

    private final Integer code;
    private final String desc;
    private final String value;

    public static RequestTypeEnum getByCode(Integer code) {
        for (RequestTypeEnum requestTypeEnum : RequestTypeEnum.values()) {
            if (requestTypeEnum.getCode().equals(code)) {
                return requestTypeEnum;
            }
        }
        throw new IllegalArgumentException("非有效请求类型");
    }
}
