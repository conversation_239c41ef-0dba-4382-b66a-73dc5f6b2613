package com.bilibili.adp.v6.count;

import com.bilibili.adp.v6.count.bos.*;

/**
 * @ClassName SanlianAdCountService
 * <AUTHOR>
 * @Date 2024/11/18 8:46 下午
 * @Version 1.0
 **/

public interface ISanlianAdCountService {

    SanlianAdCountBo getSanlianCount(SanlianAdCountQueryBo queryBo);

    SanlianAdLimitBo getSanlianLimit(SanlianAdLimitQueryBo queryBo);

    SanlianAutoLimitBo getSanlianAutoLimitByCampaign(SanlianAutoLimitByCampaignQueryBo queryBo);

    SanlianAutoLimitBo getSanlianAutoLimit(SanlianAutoLimitQueryBo queryBo);

    Integer getAutoCampaignUnitDefaultLimit();

    Integer getAutoCampaignUnitWhiteLabelLimit();

}
