package com.bilibili.adp.v6.account;

import com.bapis.ad.account.crm.acc.*;
import com.bapis.ad.account.product.AccountProductServiceGrpc;
import com.bapis.ad.account.product.ProductDto;
import com.bapis.ad.account.product.QueryAccountProductReq;
import com.bapis.ad.account.product.QueryAccountProductResp;
import com.bapis.ad.crm.account.AccountReadServiceGrpc;
import com.bapis.ad.crm.account.ListAccountIdsByTypeReq;
import com.bapis.ad.crm.account.ListAccountIdsByTypeResp;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.api.agent.dto.AgentDto;
import com.bilibili.crm.platform.api.customer.dto.CustomerBaseDto;
import com.bilibili.crm.platform.api.customer.dto.CustomerInfoDto;
import com.bilibili.crm.platform.soa.ISoaAgentService;
import com.bilibili.crm.platform.soa.ISoaCustomerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import pleiades.venus.starter.rpc.client.RPCClient;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AccountV6Service {

    @Resource
    private ISoaAgentService iSoaAgentService;
    @Resource
    private ISoaCustomerService soaCustomerService;

    @RPCClient("sycpb.cpm.ad-account")
    private AccountProductServiceGrpc.AccountProductServiceBlockingStub accountProductServiceBlockingStub;

    @RPCClient("sycpb.cpm.ad-account")
    private CrmAccountServiceGrpc.CrmAccountServiceBlockingStub crmAccountServiceBlockingStub;

    @RPCClient(value = "sycpb.cpm.crm-portal")
    private AccountReadServiceGrpc.AccountReadServiceBlockingStub accountReadServiceBlockingStub;

    public List<Integer> listAccountIdsByType(Integer accountId, int type) {
        try {
            ListAccountIdsByTypeReq req = ListAccountIdsByTypeReq.newBuilder()
                    .setAccountId(accountId)
                    .setType(ListAccountIdsByTypeReq.Type.forNumber(type))
                    .build();
            ListAccountIdsByTypeResp resp = accountReadServiceBlockingStub.listAccountIdsByType(req);
            return resp.getAccountIdsList().isEmpty() ? Collections.singletonList(accountId) : resp.getAccountIdsList();
        } catch (Exception e) {
            log.error("根据账户id获取关联账号id 列表失败，参数:{}", accountId, e);
            return Collections.singletonList(accountId);
        }
    }

    public List<AccountBase> listAccounts(List<Integer> accountIds) {
        if (CollectionUtils.isEmpty(accountIds)) {
            return Collections.emptyList();
        }

        AccountBasesReply accountBasesReply = crmAccountServiceBlockingStub.getAccountBaseByIds(AccountIdsReq.newBuilder().addAllAccountIds(accountIds).build());
        return accountBasesReply.getDataList();
    }

    public Map<Integer, AccountBase> getAccountBaseMap(List<Integer> accountIds) {
        if (CollectionUtils.isEmpty(accountIds)) {
            return Collections.emptyMap();
        }

        return this.listAccounts(accountIds).stream().collect(Collectors.toMap(AccountBase::getAccountId, t -> t, (k1, k2) -> k1));
    }

    public AccountBase getAccount(Integer accountId) {
        AccountBaseReply accountBase = crmAccountServiceBlockingStub.getAccountBase(
                AccountIdReq.newBuilder().setAccountId(accountId).build());
        return accountBase.getData();
    }

    public String getAccountName(Integer accountId) {
        AccountBaseReply accountBase = crmAccountServiceBlockingStub.getAccountBase(
                AccountIdReq.newBuilder().setAccountId(accountId).build());
        if (accountBase == null) {
            return "";
        }
        return accountBase.getData().getUserName();
    }

    public String getAgentName(Integer agentId) {
        if (!Utils.isPositive(agentId)) {
            return "";
        }
        AgentDto agent = iSoaAgentService.getAgentById(agentId);
        return agent.getName();
    }

    public String getCustomerName(Integer customerId) {
        if (!Utils.isPositive(customerId)) {
            return "";
        }
        CustomerInfoDto customerDto = soaCustomerService.getCustomerDto(customerId);
        return customerDto.getUsername();
    }

    public Map<Integer, CustomerBaseDto> getCustomersMap(List<Integer> customerIds) {
        if (CollectionUtils.isEmpty(customerIds)) {
            return Collections.emptyMap();
        }

        List<CustomerBaseDto> customerBaseDtos = soaCustomerService.getCustomerBaseDtosByIds(customerIds);
        if (CollectionUtils.isEmpty(customerBaseDtos)) {
            return Collections.emptyMap();
        }
        return customerBaseDtos.stream().collect(Collectors.toMap(CustomerBaseDto::getId, t -> t, (k1, k2) -> k1));
    }

    public Map<Integer, ProductDto> getProductsMap(List<Integer> productIds) {
        if (CollectionUtils.isEmpty(productIds)) {
            return Collections.emptyMap();
        }

        QueryAccountProductResp queryAccountProductResp = accountProductServiceBlockingStub.queryAccountProductByIds(
                QueryAccountProductReq.newBuilder().addAllProductIds(productIds).build());
        if (CollectionUtils.isEmpty(queryAccountProductResp.getDataList())) {
            return Collections.emptyMap();
        }
        return queryAccountProductResp.getDataList().stream().collect(Collectors.toMap(t -> t.getId(), t -> t, (k1, k2) -> k1));
    }

    public String getProductName(Integer productId) {
        if (!Utils.isPositive(productId)) {
            return "";
        }
        QueryAccountProductResp queryAccountProductResp = accountProductServiceBlockingStub.queryAccountProductByIds(
                QueryAccountProductReq.newBuilder().addProductIds(productId).build());
        if (!CollectionUtils.isEmpty(queryAccountProductResp.getDataList())) {
            return queryAccountProductResp.getDataList().get(0).getName();
        }
        return "";
    }

}
