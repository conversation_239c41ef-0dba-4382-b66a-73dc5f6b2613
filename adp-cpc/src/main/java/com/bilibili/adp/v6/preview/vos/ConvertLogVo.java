package com.bilibili.adp.v6.preview.vos;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class ConvertLogVo {
    private Long creativeId;

    private Long mid;

    private Long eventTime;

    private String eventType;

    private String eventName;

    private String monitoringUrl;

    private String convType;

    private String convName;

    private String convValue;

    private String IP;

    private String callbackUrl;
}
