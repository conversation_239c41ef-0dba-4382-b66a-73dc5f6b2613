package com.bilibili.adp.v6.enums.mid;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum RenewalStatusEnum {
    INIT(0, "尚未发起续期"),
    WAIT_ACCEPT(1, "续期待确认"),
    DISMISS(2, "续期被拒绝"),
    CONFIRM(3, "续期成功");
    private final Integer code;
    private final String message;

    public static RenewalStatusEnum getByCode(Integer value) {
        for (RenewalStatusEnum renewalStatusEnum : values()) {
            if (renewalStatusEnum.getCode().equals(value)) {
                return renewalStatusEnum;
            }
        }
        return null;
    }
}
