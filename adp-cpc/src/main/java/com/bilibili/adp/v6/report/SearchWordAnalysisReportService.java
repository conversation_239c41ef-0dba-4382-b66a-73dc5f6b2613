package com.bilibili.adp.v6.report;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.bapis.ad.jupiter.search.words.SearchWordAnalysisReportReply;
import com.bapis.ad.jupiter.search.words.SearchWordsAnalysisReportServiceGrpc;
import com.bapis.ad.jupiter.search.words.StatReq;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.services.campaign.api.ICpcCampaignService;
import com.bilibili.adp.cpc.biz.services.campaign.dto.CpcCampaignDto;
import com.bilibili.adp.cpc.biz.services.campaign.dto.QueryCpcCampaignDto;
import com.bilibili.adp.cpc.biz.services.creative.api.ICpcCreativeService;
import com.bilibili.adp.cpc.biz.services.creative.dto.CpcCreativeDto;
import com.bilibili.adp.cpc.biz.services.search.overt.bo.SearchKeyReportBo;
import com.bilibili.adp.cpc.biz.services.search.overt.bo.SearchKeyReportChartBo;
import com.bilibili.adp.cpc.biz.services.search.overt.bo.SearchKeyReportVo;
import com.bilibili.adp.cpc.biz.services.unit.api.ICpcUnitService;
import com.bilibili.adp.cpc.biz.services.unit.dto.QueryCpcUnitDto;
import com.bilibili.adp.cpc.dto.CpcLightUnitDto;
import com.bilibili.adp.cpc.enums.ad.CampaignAdType;
import com.bilibili.adp.cpc.enums.ad.OcpcTargetEnum;
import com.bilibili.adp.cpc.excel.FreezeHeaderSheetWriteHandler;
import com.bilibili.adp.cpc.utils.LocalDateUtils;
import com.bilibili.adp.cpc.utils.TimeUtils;
import com.bilibili.adp.cpc.utils.ValidateUtil;
import com.bilibili.adp.launch.api.creative.dto.QueryCpcCreativeDto;
import com.bilibili.adp.v6.report.bo.ExportSearchWordAnalysisBo;
import com.bilibili.adp.v6.report.bo.QuerySearchWordAnalysisBo;
import com.bilibili.adp.v6.report.converters.ProgramedCreativeAnalysisServiceConverter;
import com.bilibili.crm.platform.common.IsValid;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import pleiades.venus.starter.rpc.client.RPCClient;

import javax.annotation.Resource;
import javax.validation.Validator;
import java.io.OutputStream;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 复制的代码，从web模块下沉到service模块
 *
 * @see com.bilibili.adp.advertiser.portal.webapi.effect_ad.SearchWordAnalysisReportController
 */
@Slf4j
@Service
public class SearchWordAnalysisReportService {

    public static final String HOUR_SUFFIX = ":00:00";
    @Resource
    private ICpcUnitService iCpcUnitService;
    @Resource
    private ICpcCampaignService iCpcCampaignService;
    @Resource
    private ICpcCreativeService iCpcCreativeService;

    @Autowired
    private Validator validator;

    @RPCClient(value = "sycpb.platform.cpm-jupiter")
    private SearchWordsAnalysisReportServiceGrpc.SearchWordsAnalysisReportServiceBlockingStub serviceWordBlockingStub;

    public SearchKeyReportChartBo chart(QuerySearchWordAnalysisBo query) {

        ValidateUtil.validate(validator, query);

        int accountId = query.getAccountId();
        List<Integer> campaignIds = query.getCampaignIds();
        List<Integer> unitIds = query.getUnitIds();
        List<Integer> creativeIds = query.getCreativeIds();
        int timeType;
        Long fromTime = query.getFromTime();
        Long toTime = query.getToTime();

        if (toTime - fromTime > TimeUtils.DAY_TIME_MILLS) {
            timeType = StatReq.TimeType.DAY_VALUE;
        } else {
            timeType = StatReq.TimeType.HOUR_VALUE;
        }

        StatReq.Builder reqBuilder = StatReq.newBuilder()
                .setAccountId(accountId)
                .setBeginTime(fromTime)
                .setEndTime(toTime)
                .setTimeType(StatReq.TimeType.forNumber(timeType));

        if (!CollectionUtils.isEmpty(campaignIds)) {
            reqBuilder.addAllCampaignIds(campaignIds);
        }

        if (!CollectionUtils.isEmpty(unitIds)) {
            reqBuilder.addAllUnitIds(unitIds);
        }

        if (!CollectionUtils.isEmpty(creativeIds)) {
            reqBuilder.addAllCreativeIds(creativeIds);
        }

        SearchWordAnalysisReportReply reportReply = serviceWordBlockingStub.chart(reqBuilder.build());
        List<SearchKeyReportBo> reportBoList = ProgramedCreativeAnalysisServiceConverter.MAPPER.grpcsToBos(
                reportReply.getDataList());


        List<String> xaxis = buildXaxis(fromTime, toTime);
        reportBoList = chartVosFill(fromTime, toTime, reportBoList, xaxis);
        reportBoList = buildHourDate(reportBoList, fromTime, toTime, timeType);
        return SearchKeyReportChartBo.builder()
                .keyReportList(reportBoList)
                .xaxis(xaxis)
                .build();
    }

    private List<SearchKeyReportBo> buildHourDate(List<SearchKeyReportBo> bos, Long from, Long to, Integer timeType) {
        if (to - from > TimeUtils.DAY_TIME_MILLS) {
            return bos;
        }
        for (SearchKeyReportBo searchKeyReportBo : bos) {
            long hourDate = searchKeyReportBo.getDate().getTime() + Integer.parseInt(
                    searchKeyReportBo.getLogHour()) * 3600 * 1000L;
            if (null != timeType && com.bapis.ad.report.StatReq.TimeType.HOUR.getNumber() == timeType) {
                searchKeyReportBo.setDate(new Timestamp(hourDate));
            }
        }
        return bos;
    }

    public void export(ExportSearchWordAnalysisBo query, OutputStream outputStream) {

        ValidateUtil.validate(validator, query);

        int accountId = query.getAccountId();
        List<Integer> campaignIds = query.getCampaignIds();
        List<Integer> unitIds = query.getUnitIds();
        List<Integer> creativeIds = query.getCreativeIds();
        Integer timeType = query.getTimeType();
        Integer page = query.getPage();
        Integer size = query.getSize();
        Long fromTime = query.getFromTime();
        Long toTime = query.getToTime();
        Integer groupBy = query.getGroupBy();
        String sortField = query.getSortField();
        Integer keyWordGroupBy = query.getKeyWordGroupBy();
        String sortType = query.getSortType();

        StatReq.Builder reqBuilder = StatReq.newBuilder()
                .setAccountId(accountId)
                .setBeginTime(fromTime)
                .setEndTime(toTime)
                .setGroupBy(GroupByEnum.getById(groupBy).getName())
                .setPage(page)
                .setPageSize(size)
                .setKeyWordGroupBy(keyWordGroupBy)
                .setSortField(sortField)
                .setSortType(sortType)
                .setLocationType(query.getLocationType())
                .setTimeType(StatReq.TimeType.forNumber(timeType));

        if (!CollectionUtils.isEmpty(campaignIds)) {
            reqBuilder.addAllCampaignIds(campaignIds);
        }

        if (!CollectionUtils.isEmpty(unitIds)) {
            reqBuilder.addAllUnitIds(unitIds);
        }

        if (!CollectionUtils.isEmpty(creativeIds)) {
            reqBuilder.addAllCreativeIds(creativeIds);
        }

        SearchWordAnalysisReportReply reportReply = serviceWordBlockingStub.list(reqBuilder.build());
        List<SearchKeyReportBo> reportBos = ProgramedCreativeAnalysisServiceConverter.MAPPER.grpcsToBos(
                reportReply.getDataList());

        List<SearchKeyReportVo> vos = ProgramedCreativeAnalysisServiceConverter.MAPPER.searchKeyBoToVo(
                changeGroupByVal(enhanceField(reportBos, query.getFromTime(), query.getToTime(), query.getTimeType()),
                        groupBy, keyWordGroupBy, timeType));

        ExcelWriter writer = null;
        try {
            writer = EasyExcel.write(outputStream).build();
            final WriteSheet sheet1 = EasyExcel.writerSheet("sheet1")
                    .head(SearchKeyReportVo.class)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .registerWriteHandler(new FreezeHeaderSheetWriteHandler())
                    .build();
            writer.write(vos, sheet1);
        } catch (Exception e) {
            log.error("exportSearchWordAnalysisReport error: ", e);
            throw new RuntimeException(e);
        } finally {
            if (writer != null) {
                writer.finish();
            }
        }
    }

    private List<SearchKeyReportBo> changeGroupByVal(List<SearchKeyReportBo> bos, Integer groupBy,
            Integer keyWordGroupBy, Integer timeType) {
        GroupByEnum groupByEnum = GroupByEnum.getById(groupBy);
        for (SearchKeyReportBo bo : bos) {
            switch (groupByEnum) {
                case CAMPAIGN:
                    bo.setUnitId(null);
                    bo.setUnitName(null);
                    bo.setCreativeName(null);
                    bo.setCreativeId(null);
                    break;
                case UNIT:
                    bo.setCreativeName(null);
                    bo.setCreativeId(null);
                    break;
                default:
                    break;
            }
            if (IsValid.TRUE.getCode().equals(keyWordGroupBy)) {
                bo.setSearchKey(null);
            }

            if (timeType != StatReq.TimeType.HOUR.getNumber()) {
                bo.setLogHour(null);
            }
        }
        return bos;
    }

    private List<SearchKeyReportBo> enhanceField(List<SearchKeyReportBo> bos, Long fromTime, Long toTime,
            Integer timeType) {
        String allTimeStr = null;
        if (null != timeType && com.bapis.ad.report.StatReq.TimeType.ALL.getNumber() == timeType) {
            String fromTimeStr = LocalDateUtils.formatTime(fromTime, LocalDateUtils.DATE_PATTERN);
            String toTimeStr = LocalDateUtils.formatTime(toTime, LocalDateUtils.DATE_PATTERN);
            allTimeStr = fromTimeStr + "~" + toTimeStr;
        }

        List<Integer> campaignIds = bos.stream().map(SearchKeyReportBo::getCampaignId).filter(Objects::nonNull)
                .distinct().collect(Collectors.toList());
        List<Integer> unitIds = bos.stream().map(SearchKeyReportBo::getUnitId).filter(Objects::nonNull).distinct()
                .collect(Collectors.toList());
        List<Integer> creativeIds = bos.stream().map(SearchKeyReportBo::getCreativeId).filter(Objects::nonNull)
                .distinct().collect(Collectors.toList());

        Map<Integer, CpcCampaignDto> campaignDtoMap = new HashMap<>();
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(campaignIds)) {
            List<CpcCampaignDto> cpcCampaignDtos = iCpcCampaignService.queryCpcCampaign(
                    QueryCpcCampaignDto.builder().campaignIds(campaignIds).build());
            campaignDtoMap = cpcCampaignDtos.stream()
                    .collect(Collectors.toMap(CpcCampaignDto::getCampaignId, Function.identity()));
        }

        Map<Integer, CpcLightUnitDto> unitDtoMap = new HashMap<>();
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(unitIds)) {
            List<CpcLightUnitDto> cpcLightUnitDtos = iCpcUnitService.queryLightUnits(
                    QueryCpcUnitDto.builder().unitIds(unitIds).build());
            unitDtoMap = cpcLightUnitDtos.stream()
                    .collect(Collectors.toMap(CpcLightUnitDto::getUnitId, Function.identity()));
        }

        Map<Integer, CpcCreativeDto> creativeDtoMap = new HashMap<>();
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(creativeIds)) {
            List<CpcCreativeDto> cpcCreativeDtos = iCpcCreativeService.querySimpleCreativeS(
                    QueryCpcCreativeDto.builder().creativeIds(creativeIds).build());
            creativeDtoMap = cpcCreativeDtos.stream()
                    .collect(Collectors.toMap(CpcCreativeDto::getCreativeId, Function.identity()));
        }

        for (SearchKeyReportBo reportBo : bos) {

            reportBo.setCampaignName(
                    campaignDtoMap.getOrDefault(reportBo.getCampaignId(), CpcCampaignDto.builder().build())
                            .getCampaignName());
            reportBo.setUnitName(
                    unitDtoMap.getOrDefault(reportBo.getUnitId(), CpcLightUnitDto.builder().build()).getUnitName());
            reportBo.setCreativeName(
                    creativeDtoMap.getOrDefault(reportBo.getCreativeId(), CpcCreativeDto.builder().build())
                            .getCreativeName());
//                reportBo.setDateStr(Utils.getTimestamp2String(new Timestamp(reportBo.getDate().getTime()), "yyyy-MM-dd HH:mm:ss"));
            reportBo.setDateStr(Utils.getTimestamp2String(new Timestamp(reportBo.getDate().getTime()), "yyyy-MM-dd"));
            reportBo.setLogHour(reportBo.getLogHour() + HOUR_SUFFIX);

            if (StringUtils.isNotBlank(allTimeStr)) {
                reportBo.setDateStr(allTimeStr);
            }
            reportBo.setOcpcTargetName(!Utils.isPositive(reportBo.getOcpcTarget()) ? "" : OcpcTargetEnum.getByCode(
                    reportBo.getOcpcTarget()).getDesc());
            reportBo.setOcpxTargetTwoName(
                    !Utils.isPositive(reportBo.getOcpxTargetTwo()) ? "" : OcpcTargetEnum.getByCode(
                            reportBo.getOcpxTargetTwo()).getDesc());
            reportBo.setIsSearchKuaitouName(
                    null == reportBo.getIsSearchKuaitou() ? "未知" : IsValid.getByCode(reportBo.getIsSearchKuaitou())
                            .getDesc());
            reportBo.setAdSearchTypeName(CampaignAdType.getByCodeWithDefaultDesc(reportBo.getAdSearchType()));
        }

        return bos;
    }

    private List<SearchKeyReportBo> chartVosFill(Long fromTime, Long toTime, List<SearchKeyReportBo> vos,
            List<String> xaxis) {

        if (toTime - fromTime <= TimeUtils.DAY_TIME_MILLS) {
            List<String> hours = vos.stream().map(SearchKeyReportBo::getLogHour).collect(Collectors.toList());

            for (int i = 0; i < 24; i++) {
                if (!hours.contains(String.format("%02d", i))) {
                    SearchKeyReportBo searchKeyReportBo = SearchKeyReportBo.emptyInstance(
                            LocalDateUtils.formatTime(fromTime, "yyyy-MM-dd"), "yyyy-MM-dd");
                    searchKeyReportBo.setLogHour(String.format("%02d", i));
                    vos.add(searchKeyReportBo);
                }
            }
            vos = vos.stream().sorted(Comparator.comparing(r -> Integer.parseInt(r.getLogHour())))
                    .collect(Collectors.toList());
            return vos;
        }

        List<String> dateStrs = vos.stream().map(r -> LocalDateUtils.formatTime(r.getDate().getTime(), "yyyy-MM-dd"))
                .collect(Collectors.toList());
        for (int i = 0; i < xaxis.size(); i++) {
            if (!dateStrs.contains(xaxis.get(i))) {
                vos.add(i, SearchKeyReportBo.emptyInstance(xaxis.get(i), "yyyy-MM-dd"));
            }
        }
        vos = vos.stream().sorted(Comparator.comparing(SearchKeyReportBo::getDate)).collect(Collectors.toList());
        return vos;
    }

    private List<String> buildXaxis(Long fromTime, Long toTime) {
        if (toTime - fromTime < TimeUtils.DAY_TIME_MILLS) {
            List<String> hourAxis = new ArrayList<>();
            for (int i = 0; i < 24; i++) {
                hourAxis.add(LocalDateUtils.formatTime(fromTime + i * 3600 * 1000L, "yyyy-MM-dd HH:mm:ss"));
            }
            return hourAxis;
        }
        return TimeUtils.genDateDayList(fromTime, toTime, "yyyy-MM-dd");
    }

    private enum GroupByEnum {
        /**
         *
         */
        CAMPAIGN(1, "campaign"),

        /**
         *
         */
        UNIT(2, "unit"),

        /**
         *
         */
        CREATIVE(3, "creative"),

        /**
         * 全部
         */
        ALL(0, "all"),

        ;

        private Integer id;

        /**
         * 名称
         */
        private String name;

        GroupByEnum(Integer id, String name) {
            this.id = id;
            this.name = name;
        }

        static GroupByEnum getByName(String name) {
            for (GroupByEnum value : GroupByEnum.values()) {
                if (value.getName().equals(name)) {
                    return value;
                }
            }
            throw new RuntimeException("unknown name" + name);
        }

        static GroupByEnum getById(Integer id) {
            for (GroupByEnum value : GroupByEnum.values()) {
                if (value.getId().equals(id)) {
                    return value;
                }
            }
            throw new RuntimeException("unknown id" + id);
        }

        public String getName() {
            return name;
        }

        public Integer getId() {
            return id;
        }
    }
}
