package com.bilibili.adp.legacy.bo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CpsReplaceUrlInfoBo {
    // 是否替换链接
    private Integer isReplaceUrl;

    // 替链落地页类型:1落地页2微信小程序
    private Integer replaceUrlType;

    // 转化链链接(兜底链接)类型，1-三方落地页 5-高能建站落地页
    private Integer conversionUrlType;

    // 转化链链接(兜底链接)高能建站落地页page_id, 针对type = 5 回显用
    private String conversionUrlPageId;

    // 转化链连接(兜底链接)用户填写的原始落地页/落地页地址
    private String conversionUrl;

    // 微信小程序id(是否开启一跳)
    private Integer miniGameId;

    // app换端跳转链接(双端公用)
    private String appJumpUrl;
}
