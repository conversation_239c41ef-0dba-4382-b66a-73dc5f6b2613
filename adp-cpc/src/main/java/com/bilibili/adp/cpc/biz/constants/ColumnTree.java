package com.bilibili.adp.cpc.biz.constants;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ColumnTree {

    private int code;
    private String key;
    private String name;
    private boolean fixed;

    private List<ColumnTree> children;


    public static ColumnTree from(ReportColumn column, List<ColumnTree> children) {
        return ColumnTree.builder()
                .code(column.getCode())
                .key(column.getKey())
                .name(column.getName())
                .fixed(column.isRequired())
                .children(children)
                .build();
    }


    public static List<ColumnTree> fromList(List<ReportColumn> columns) {
        return columns.stream().map(x -> ColumnTree.builder()
                .code(x.getCode())
                .key(x.getKey())
                .name(x.getName())
                .fixed(x.isRequired())
                .build()).collect(Collectors.toList());

    }

    public static ColumnTree fromMap(ReportColumn parent, Map<ReportColumn, List<ReportColumn>> map) {
        List<ColumnTree> children = new ArrayList<>();
        map.forEach((key, value) -> {
            ColumnTree tree = ColumnTree.from(key, ColumnTree.fromList(value));
            children.add(tree);
        });
        return ColumnTree.from(parent, children);
    }
}
