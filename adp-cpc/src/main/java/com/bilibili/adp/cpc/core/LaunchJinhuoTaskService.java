package com.bilibili.adp.cpc.core;

import com.bilibili.adp.cpc.biz.bos.account.AccountJinhuoTaskInfoBo;
import com.bilibili.adp.cpc.biz.bos.account.JinghuoAbutmentTaskInfoBo;
import com.bilibili.adp.cpc.biz.converter.account.BusinessMapper;
import com.bilibili.adp.cpc.biz.services.account.LaunchAccountV1Service;
import com.bilibili.adp.cpc.dao.querydsl.pos.AccAccountJinghuoAbutmentTaskMappingPo;
import com.bilibili.adp.cpc.dao.querydsl.pos.LauJinghuoAbutmentTaskInfoPo;
import com.bilibili.adp.cpc.enums.AccountJinhuoTaskStatusEnum;
import com.bilibili.adp.cpc.repo.AccountJinhuoTaskMappingRepo;
import com.bilibili.adp.cpc.repo.LauJinghuoAbutmentTaskInfoRepo;
import com.bilibili.crm.platform.api.customer.dto.CustomerInfoDto;
import com.bilibili.crm.platform.soa.ISoaCustomerService;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


@Slf4j
@Service
public class LaunchJinhuoTaskService {

    @Autowired
    private LaunchAccountV1Service launchAccountV1Service;

    @Resource
    private AccountJinhuoTaskMappingRepo accountJinhuoTaskMappingRepo;

    @Resource
    private LauJinghuoAbutmentTaskInfoRepo lauJinghuoAbutmentTaskInfoRepo;

    @Resource
    private ISoaCustomerService soaCustomerService;


    public void saveTaskMapping(int accountID, Long taskID, String secret) {

        AccAccountJinghuoAbutmentTaskMappingPo existPo = accountJinhuoTaskMappingRepo.queryVaildBytaskId(taskID);
        Assert.isTrue(Objects.isNull(existPo) || existPo.getTaskId() == 0L || existPo.getAccountId() == accountID, "任务已被绑定");

        LauJinghuoAbutmentTaskInfoPo taskInfo = lauJinghuoAbutmentTaskInfoRepo.query(taskID);


        Assert.isTrue(!Objects.isNull(taskInfo) && taskInfo.getTaskId() > 0, "任务不存在");
        Assert.isTrue(secret.equals(taskInfo.getTaskSecret()), "任务密钥错误");
        // Assert.isTrue(checkLicenceCode(accountID, taskInfo.getSocialCreditCode()), "绑定失败，任务下单主体不一致");

        AccAccountJinghuoAbutmentTaskMappingPo po = new AccAccountJinghuoAbutmentTaskMappingPo();
        po.setAccountId(accountID);
        po.setTaskId(taskID);
        po.setIsDeleted(0);
        List<AccAccountJinghuoAbutmentTaskMappingPo> list = accountJinhuoTaskMappingRepo.query(accountID);
        if (CollectionUtils.isEmpty(list)) {
            accountJinhuoTaskMappingRepo.insert(po);
            return;
        }
        accountJinhuoTaskMappingRepo.update(list.get(0).getId(), taskID);
    }


    public AccountJinhuoTaskInfoBo getAccountJinhuoTaskIDetail(int accountID) {
        AccountJinhuoTaskInfoBo bo = AccountJinhuoTaskInfoBo.builder().build();
        AccAccountJinghuoAbutmentTaskMappingPo po = accountJinhuoTaskMappingRepo.queryVaild(accountID);
        if (Objects.isNull(po)) {
            return bo;
        }

        LauJinghuoAbutmentTaskInfoPo taskInfoPo = lauJinghuoAbutmentTaskInfoRepo.query(po.getTaskId());
        bo.setTaskID(po.getTaskId());
        bo.setTaskName(taskInfoPo.getTaskName());
        bo.setStartTime(taskInfoPo.getStartTime().getTime());
        bo.setEndTime(taskInfoPo.getEndTime().getTime());
        bo.setBindTime(taskInfoPo.getMtime().getTime());


        long nowTime = System.currentTimeMillis();

        if (nowTime < bo.getStartTime()) {
            bo.setTaskStatus(AccountJinhuoTaskStatusEnum.Not_Started.getCode());
        } else if (nowTime > bo.getEndTime()) {
            bo.setTaskStatus(AccountJinhuoTaskStatusEnum.Ended.getCode());
        } else {
            bo.setTaskStatus(AccountJinhuoTaskStatusEnum.Proceed.getCode());

        }
        return bo;
    }


    public void saveJinhuoTask(JinghuoAbutmentTaskInfoBo bo) {
        LauJinghuoAbutmentTaskInfoPo po = lauJinghuoAbutmentTaskInfoRepo.query(bo.getTaskId());
        if (Objects.isNull(po)) {
            lauJinghuoAbutmentTaskInfoRepo.insert(BusinessMapper.MAPPER.toPo(bo));
        } else {
            bo.setId(po.getId());
            lauJinghuoAbutmentTaskInfoRepo.update(BusinessMapper.MAPPER.toPo(bo));
        }
    }


    private boolean checkLicenceCode(int accountID, String socialCreditCode) {

        if (Strings.isNullOrEmpty(socialCreditCode)) {
            return true;
        }

        Integer customerId = launchAccountV1Service.getCustomerId(accountID);

        if (customerId == null) {
            return false;
        }

        CustomerInfoDto dto = soaCustomerService.getCustomerDto(customerId);
        if (Objects.isNull(dto)) {
            return false;
        }
        return socialCreditCode.equals(dto.getBusinessLicenceCode());
    }

}
