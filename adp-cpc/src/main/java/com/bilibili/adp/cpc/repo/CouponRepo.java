package com.bilibili.adp.cpc.repo;

import com.bilibili.adp.common.util.ObjectUtils;
import com.bilibili.adp.common.util.Page;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.core.constants.IsDeleted;
import com.bilibili.adp.cpc.dao.ad.AccCouponUseQueueDao;
import com.bilibili.adp.cpc.dao.ad.AccCouponUseQueueItemDao;
import com.bilibili.adp.cpc.dao.ad.ExtAccCouponUseQueueDao;
import com.bilibili.adp.cpc.enums.CouponQueueItemStatusEnum;
import com.bilibili.adp.cpc.po.ad.*;
import com.bilibili.adp.cpc.utils.metrics.CustomMetrics;
import com.bilibili.report.platform.api.dto.MinAndMaxId;
import io.opentelemetry.api.common.AttributeKey;
import io.opentelemetry.api.common.Attributes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static com.bilibili.adp.cpc.utils.metrics.MetricsKeyConstant.*;

/**
 * <AUTHOR>
 * @date 2023/9/15 15:04
 */
@Component
@Slf4j
public class CouponRepo {

    @Resource
    private AccCouponUseQueueDao accCouponUseQueueDao;
    @Resource
    private AccCouponUseQueueItemDao accCouponUseQueueItemDao;
    @Resource
    private ExtAccCouponUseQueueDao extAccCouponUseQueueItemDao;
    @Resource
    private CustomMetrics customMetrics;

    /**
     * 根据账户id获取生效的队列
     *
     * @param accountId
     * @return
     */
    public AccCouponUseQueuePo fetchEnabledCouponQueueByAccountId(Integer accountId) {

        AccCouponUseQueuePoExample example = new AccCouponUseQueuePoExample();
        example.or().andAccountIdEqualTo(accountId).andIsDeletedEqualTo(IsDeleted.VALID);
        List<AccCouponUseQueuePo> accCouponUseQueuePos = accCouponUseQueueDao.selectByExample(example);
        if (CollectionUtils.isEmpty(accCouponUseQueuePos)) {
            return null;
        }
        return accCouponUseQueuePos.get(0);
    }

    /**
     * 创建新队列
     *
     * @param accountId
     * @return
     */
    public Long createNewCouponQueue(Integer accountId) {

        AccCouponUseQueuePo record = new AccCouponUseQueuePo();
        record.setAccountId(accountId);
        accCouponUseQueueDao.insertSelective(record);
        return record.getId();
    }

    /**
     * 添加优惠券到队列
     *
     * @param accountId
     * @param couponId 子券 id
     * @param queueId
     * @param status
     * @param nextSort
     * @param parentCouponId 父券 id
     * @return
     */
    public Integer addCouponToQueue(Integer accountId, String biliName, Long couponId, Long queueId, Integer status,
                                    Integer nextSort, Long parentCouponId) {

        AccCouponUseQueueItemPo record = AccCouponUseQueueItemPo.builder()
                .accountId(accountId)
                .couponId(couponId)
                .parentCouponId(parentCouponId)
                .queueId(queueId)
                .bizStatus(status)
                .bizSort(nextSort)
                .bilibiliUsername(biliName)
                .build();

        // 使用时间
        if (CouponQueueItemStatusEnum.USING.getCode().equals(status)) {
            record.setUseTime(Utils.getNow());
        }

        int id = accCouponUseQueueItemDao.insertSelective(record);

        Attributes attributesTotal = Attributes.of(AttributeKey.stringKey(count_type), count_type_api_coupon,
                AttributeKey.stringKey(count_type_APIName), count_type_api_coupon,
                AttributeKey.stringKey(a_operator), count_type_op_coupon_add_queue
        );
        customMetrics.count(1, attributesTotal);
        return id;
    }

    /**
     * 获取队列有序的启用的元素列表
     *
     * @param queueId
     * @return
     */
    public List<AccCouponUseQueueItemPo> querySortedEnabledItemsByQueueId(Long queueId) {

        AccCouponUseQueueItemPoExample example = new AccCouponUseQueueItemPoExample();
        example.or()
                .andQueueIdEqualTo(queueId)
                .andIsDeletedEqualTo(IsDeleted.VALID)
                // 排队中和使用中的
                .andBizStatusIn(CouponQueueItemStatusEnum.ENABLE_STATUS_LIST);
        List<AccCouponUseQueueItemPo> itemPoList = accCouponUseQueueItemDao.selectByExample(example);
        return itemPoList;
    }

    public List<AccCouponUseQueueItemPo> queryQueueingItemsByParentId(Long parentId) {
        if (!Utils.isPositive(parentId)) {
            return Collections.EMPTY_LIST;
        }

        AccCouponUseQueueItemPoExample example = new AccCouponUseQueueItemPoExample();
        example.or()
                .andParentCouponIdEqualTo(parentId)
                .andIsDeletedEqualTo(IsDeleted.VALID)
                // 排队中和使用中的
                .andBizStatusEqualTo(CouponQueueItemStatusEnum.QUEUEING.getCode());
        List<AccCouponUseQueueItemPo> itemPoList = accCouponUseQueueItemDao.selectByExample(example);
        return itemPoList;
    }

    /**
     * 查询排队中的队列元素
     *
     * @return
     */
    public List<AccCouponUseQueueItemPo> querySortedQueueingItemsByQueueId(Long queueId, Page page) {

        AccCouponUseQueueItemPoExample example = new AccCouponUseQueueItemPoExample();
        AccCouponUseQueueItemPoExample.Criteria criteria = example.or()
                .andQueueIdEqualTo(queueId)
                .andIsDeletedEqualTo(IsDeleted.VALID)
                // 排队中和使用中的
                .andBizStatusIn(Arrays.asList(CouponQueueItemStatusEnum.QUEUEING.getCode()));
        ObjectUtils.setPage(() -> page, example::setLimit, example::setOffset);

        List<AccCouponUseQueueItemPo> itemPoList = accCouponUseQueueItemDao.selectByExample(example);
        return itemPoList;
    }

    public Integer countByQueueId(Long queueId) {

        AccCouponUseQueueItemPoExample example = new AccCouponUseQueueItemPoExample();
        example.or()
                .andQueueIdEqualTo(queueId)
                .andIsDeletedEqualTo(IsDeleted.VALID)
                // 排队中和使用中的
                .andBizStatusIn(Arrays.asList(CouponQueueItemStatusEnum.QUEUEING.getCode()));
        long count = accCouponUseQueueItemDao.countByExample(example);
        return Math.toIntExact(count);
    }

    /**
     * 删除队列(元素删除的时候如果是最后一个会触发队列删除)
     *
     * @param queueId
     */
    public Integer deleteQueueById(Long queueId) {
        AccCouponUseQueuePo queuePo = AccCouponUseQueuePo.builder()
                .id(queueId)
                .isDeleted(IsDeleted.DELETED)
                .build();
        return accCouponUseQueueDao.updateByPrimaryKeySelective(queuePo);
    }

    /**
     * 将元素从队列删除
     *
     * @param remark
     * @param itemId
     */
    public Integer removeItemFromQueue(Long itemId, String remark) {

        AccCouponUseQueueItemPo queueItemPo = AccCouponUseQueueItemPo.builder()
                .id(itemId)
                .isDeleted(IsDeleted.DELETED)
                .bizStatus(CouponQueueItemStatusEnum.TERMINAL.getCode())
                .removeTime(Utils.getNow())
                .remark(remark)
                .build();
        return accCouponUseQueueItemDao.updateByPrimaryKeySelective(queueItemPo);
    }

    /**
     * 修改队列元素状态
     *
     * @param id
     * @param bizStatus
     */
    public void updateQueueItemStatus(Long id, Integer bizStatus) {

        AccCouponUseQueueItemPo itemPo = AccCouponUseQueueItemPo.builder()
                .id(id)
                .bizStatus(bizStatus)
                .build();
        accCouponUseQueueItemDao.updateByPrimaryKeySelective(itemPo);
    }

    public List<AccCouponUseQueuePo> queryQueueList(Long fromId, Long toId) {
        AccCouponUseQueuePoExample example = new AccCouponUseQueuePoExample();
        example.or().andIdGreaterThanOrEqualTo(fromId).andIdLessThan(toId).andIsDeletedEqualTo(IsDeleted.VALID);
        return accCouponUseQueueDao.selectByExample(example);
    }

    public Long queryQueueMaxId() {
        AccCouponUseQueuePoExample example = new AccCouponUseQueuePoExample();
        MinAndMaxId minAndMaxId = extAccCouponUseQueueItemDao.queryMinAndMaxIdByExample(example);
        if (minAndMaxId != null) {
            return minAndMaxId.getMaxId();
        }
        return 0L;
    }

}
