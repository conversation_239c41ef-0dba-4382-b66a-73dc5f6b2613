package com.bilibili.adp.cpc.dao.querydsl.pos;

import javax.annotation.Generated;

/**
 * LauMaterialPo is a Querydsl bean type
 */
@Generated("com.querydsl.codegen.BeanSerializer")
public class LauMaterialPo {

    private java.sql.Timestamp ctime;

    private Long id;

    private String materialContent;

    private String materialMd5;

    private Integer materialType;

    private java.sql.Timestamp mtime;

    public java.sql.Timestamp getCtime() {
        return ctime;
    }

    public void setCtime(java.sql.Timestamp ctime) {
        this.ctime = ctime;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getMaterialContent() {
        return materialContent;
    }

    public void setMaterialContent(String materialContent) {
        this.materialContent = materialContent;
    }

    public String getMaterialMd5() {
        return materialMd5;
    }

    public void setMaterialMd5(String materialMd5) {
        this.materialMd5 = materialMd5;
    }

    public Integer getMaterialType() {
        return materialType;
    }

    public void setMaterialType(Integer materialType) {
        this.materialType = materialType;
    }

    public java.sql.Timestamp getMtime() {
        return mtime;
    }

    public void setMtime(java.sql.Timestamp mtime) {
        this.mtime = mtime;
    }

    @Override
    public String toString() {
         return "ctime = " + ctime + ", id = " + id + ", materialContent = " + materialContent + ", materialMd5 = " + materialMd5 + ", materialType = " + materialType + ", mtime = " + mtime;
    }

}

