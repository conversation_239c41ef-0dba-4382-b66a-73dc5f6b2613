package com.bilibili.adp.cpc.biz.services.live.api;

import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.cpc.biz.services.live.bos.LiveGameDetailBo;
import com.bilibili.adp.cpc.biz.services.live.bos.LiveGameInfoBo;
import com.bilibili.adp.cpc.biz.services.live.bos.LiveGameLaunchInfoBo;
import com.bilibili.adp.cpc.biz.services.live.bos.QueryLiveGameCardBo;

public interface ILiveGameCardService {

    /*
     * 查询直播游戏卡列表
     */
    PageResult<LiveGameInfoBo> queryLiveGameCardList(QueryLiveGameCardBo queryLiveGameCardBo, int page, int size);

    /*
     * 查询直播游戏卡详情
     * 此接口因为请求量太大已经迁移到建站了
     */
    @Deprecated
    LiveGameDetailBo queryLiveGameCardDetail(Long pageId, Integer launchPlatform);

    /*
     * 新建直播游戏卡信息
     */
    void addLiveGameCard(LiveGameInfoBo gameInfoBo);

    /*
     * 更新直播游戏卡信息
     */
    void updateLiveGameCard(LiveGameInfoBo gameInfoBo);

    /*
     * 更新直播游戏卡的投放时间
     */
    void delLiveGameCard(Long id);

    /*
     * 更新直播游戏卡的投放时间
     */
    void updateLiveGameCardLaunchInfo(LiveGameLaunchInfoBo launchInfoBo);
}
