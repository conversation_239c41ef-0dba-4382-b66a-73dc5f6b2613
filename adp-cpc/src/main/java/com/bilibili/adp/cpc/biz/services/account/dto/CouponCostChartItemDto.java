package com.bilibili.adp.cpc.biz.services.account.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CouponCostChartItemDto {

    // 日期
    private String logDate;

    // 可消耗激励
    private BigDecimal sumCouponValue;

    // 已消耗激励
    private BigDecimal sumCouponValueCost;

}
