package com.bilibili.adp.cpc.dao.querydsl;

import static com.querydsl.core.types.PathMetadataFactory.*;
import com.bilibili.adp.cpc.dao.querydsl.pos.CommerceCenterCategoryPo;


import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;

import com.querydsl.sql.ColumnMetadata;
import java.sql.Types;




/**
 * QCommerceCenterCategory is a Querydsl query type for CommerceCenterCategoryPo
 */
@Generated("com.querydsl.sql.codegen.MetaDataSerializer")
public class QCommerceCenterCategory extends com.querydsl.sql.RelationalPathBase<CommerceCenterCategoryPo> {

    private static final long serialVersionUID = 824030098;

    public static final QCommerceCenterCategory commerceCenterCategory = new QCommerceCenterCategory("commerce_center_category");

    public final DateTimePath<java.sql.Timestamp> ctime = createDateTime("ctime", java.sql.Timestamp.class);

    public final NumberPath<Integer> id = createNumber("id", Integer.class);

    public final NumberPath<Integer> isDeleted = createNumber("isDeleted", Integer.class);

    public final NumberPath<Integer> level = createNumber("level", Integer.class);

    public final DateTimePath<java.sql.Timestamp> mtime = createDateTime("mtime", java.sql.Timestamp.class);

    public final StringPath name = createString("name");

    public final NumberPath<Integer> pId = createNumber("pId", Integer.class);

    public final StringPath prompt = createString("prompt");

    public final StringPath remark = createString("remark");

    public final NumberPath<Integer> status = createNumber("status", Integer.class);

    public final com.querydsl.sql.PrimaryKey<CommerceCenterCategoryPo> primary = createPrimaryKey(id);

    public QCommerceCenterCategory(String variable) {
        super(CommerceCenterCategoryPo.class, forVariable(variable), "null", "commerce_center_category");
        addMetadata();
    }

    public QCommerceCenterCategory(String variable, String schema, String table) {
        super(CommerceCenterCategoryPo.class, forVariable(variable), schema, table);
        addMetadata();
    }

    public QCommerceCenterCategory(String variable, String schema) {
        super(CommerceCenterCategoryPo.class, forVariable(variable), schema, "commerce_center_category");
        addMetadata();
    }

    public QCommerceCenterCategory(Path<? extends CommerceCenterCategoryPo> path) {
        super(path.getType(), path.getMetadata(), "null", "commerce_center_category");
        addMetadata();
    }

    public QCommerceCenterCategory(PathMetadata metadata) {
        super(CommerceCenterCategoryPo.class, metadata, "null", "commerce_center_category");
        addMetadata();
    }

    public void addMetadata() {
        addMetadata(ctime, ColumnMetadata.named("ctime").withIndex(9).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(id, ColumnMetadata.named("id").withIndex(1).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(isDeleted, ColumnMetadata.named("is_deleted").withIndex(8).ofType(Types.TINYINT).withSize(3).notNull());
        addMetadata(level, ColumnMetadata.named("level").withIndex(3).ofType(Types.TINYINT).withSize(3).notNull());
        addMetadata(mtime, ColumnMetadata.named("mtime").withIndex(10).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(name, ColumnMetadata.named("name").withIndex(2).ofType(Types.VARCHAR).withSize(25).notNull());
        addMetadata(pId, ColumnMetadata.named("p_id").withIndex(4).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(prompt, ColumnMetadata.named("prompt").withIndex(5).ofType(Types.VARCHAR).withSize(64).notNull());
        addMetadata(remark, ColumnMetadata.named("remark").withIndex(6).ofType(Types.VARCHAR).withSize(64).notNull());
        addMetadata(status, ColumnMetadata.named("status").withIndex(7).ofType(Types.TINYINT).withSize(3).notNull());
    }

}

