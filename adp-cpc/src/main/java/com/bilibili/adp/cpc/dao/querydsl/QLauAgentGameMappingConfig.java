package com.bilibili.adp.cpc.dao.querydsl;

import static com.querydsl.core.types.PathMetadataFactory.*;
import com.bilibili.adp.cpc.dao.querydsl.pos.LauAgentGameMappingConfigPo;


import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;

import com.querydsl.sql.ColumnMetadata;
import java.sql.Types;




/**
 * QLauAgentGameMappingConfig is a Querydsl query type for LauAgentGameMappingConfigPo
 */
@Generated("com.querydsl.sql.codegen.MetaDataSerializer")
public class QLauAgentGameMappingConfig extends com.querydsl.sql.RelationalPathBase<LauAgentGameMappingConfigPo> {

    private static final long serialVersionUID = 2103086355;

    public static final QLauAgentGameMappingConfig lauAgentGameMappingConfig = new QLauAgentGameMappingConfig("lau_agent_game_mapping_config");

    public final NumberPath<Integer> agentId = createNumber("agentId", Integer.class);

    public final DateTimePath<java.sql.Timestamp> ctime = createDateTime("ctime", java.sql.Timestamp.class);

    public final NumberPath<Long> gameBaseId = createNumber("gameBaseId", Long.class);

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final NumberPath<Integer> isDeleted = createNumber("isDeleted", Integer.class);

    public final DateTimePath<java.sql.Timestamp> mtime = createDateTime("mtime", java.sql.Timestamp.class);

    public final com.querydsl.sql.PrimaryKey<LauAgentGameMappingConfigPo> primary = createPrimaryKey(id);

    public QLauAgentGameMappingConfig(String variable) {
        super(LauAgentGameMappingConfigPo.class, forVariable(variable), "null", "lau_agent_game_mapping_config");
        addMetadata();
    }

    public QLauAgentGameMappingConfig(String variable, String schema, String table) {
        super(LauAgentGameMappingConfigPo.class, forVariable(variable), schema, table);
        addMetadata();
    }

    public QLauAgentGameMappingConfig(String variable, String schema) {
        super(LauAgentGameMappingConfigPo.class, forVariable(variable), schema, "lau_agent_game_mapping_config");
        addMetadata();
    }

    public QLauAgentGameMappingConfig(Path<? extends LauAgentGameMappingConfigPo> path) {
        super(path.getType(), path.getMetadata(), "null", "lau_agent_game_mapping_config");
        addMetadata();
    }

    public QLauAgentGameMappingConfig(PathMetadata metadata) {
        super(LauAgentGameMappingConfigPo.class, metadata, "null", "lau_agent_game_mapping_config");
        addMetadata();
    }

    public void addMetadata() {
        addMetadata(agentId, ColumnMetadata.named("agent_id").withIndex(2).ofType(Types.INTEGER).withSize(10));
        addMetadata(ctime, ColumnMetadata.named("ctime").withIndex(4).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(gameBaseId, ColumnMetadata.named("game_base_id").withIndex(3).ofType(Types.BIGINT).withSize(19).notNull());
        addMetadata(id, ColumnMetadata.named("id").withIndex(1).ofType(Types.BIGINT).withSize(20).notNull());
        addMetadata(isDeleted, ColumnMetadata.named("is_deleted").withIndex(6).ofType(Types.TINYINT).withSize(3).notNull());
        addMetadata(mtime, ColumnMetadata.named("mtime").withIndex(5).ofType(Types.TIMESTAMP).withSize(19).notNull());
    }

}

