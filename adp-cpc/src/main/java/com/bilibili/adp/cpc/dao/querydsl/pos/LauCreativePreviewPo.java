package com.bilibili.adp.cpc.dao.querydsl.pos;

import javax.annotation.Generated;

/**
 * LauCreativePreviewPo is a Querydsl bean type
 */
@Generated("com.querydsl.codegen.BeanSerializer")
public class LauCreativePreviewPo {

    private Integer accountId;

    private Integer creativeId;

    private java.sql.Timestamp ctime;

    private Integer id;

    private Integer isDeleted;

    private java.sql.Timestamp mtime;

    private java.sql.Timestamp previewTime;

    public Integer getAccountId() {
        return accountId;
    }

    public void setAccountId(Integer accountId) {
        this.accountId = accountId;
    }

    public Integer getCreativeId() {
        return creativeId;
    }

    public void setCreativeId(Integer creativeId) {
        this.creativeId = creativeId;
    }

    public java.sql.Timestamp getCtime() {
        return ctime;
    }

    public void setCtime(java.sql.Timestamp ctime) {
        this.ctime = ctime;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public java.sql.Timestamp getMtime() {
        return mtime;
    }

    public void setMtime(java.sql.Timestamp mtime) {
        this.mtime = mtime;
    }

    public java.sql.Timestamp getPreviewTime() {
        return previewTime;
    }

    public void setPreviewTime(java.sql.Timestamp previewTime) {
        this.previewTime = previewTime;
    }

    @Override
    public String toString() {
         return "accountId = " + accountId + ", creativeId = " + creativeId + ", ctime = " + ctime + ", id = " + id + ", isDeleted = " + isDeleted + ", mtime = " + mtime + ", previewTime = " + previewTime;
    }

}

