package com.bilibili.adp.cpc.biz.services.ocpx.compensate.managed.impl;

import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.services.ocpx.compensate.managed.IOcpxManagedAutoPayStatusService;
import com.bilibili.adp.launch.api.launch.dto.ocpx.OcpxManagedAutoPayStatusDto;
import com.bilibili.adp.launch.biz.dao.OcpxManagedAutoCompensationStatusDao;
import com.bilibili.adp.launch.biz.pojo.OcpxManagedAutoCompensationStatusPo;
import com.bilibili.adp.launch.biz.pojo.OcpxManagedAutoCompensationStatusPoExample;
import com.bilibili.adp.util.common.DistributedLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class OcpxManagedAutoPayStatusServiceImpl implements IOcpxManagedAutoPayStatusService {
    private static final Logger LOGGER = LoggerFactory.getLogger(OcpxManagedAutoPayStatusServiceImpl.class);

    @Autowired
    private OcpxManagedAutoCompensationStatusDao ocpxManagedAutoCompensationStatusDao;
    @Autowired
    private OcpxManagedAutoPayStatusServiceDelegate autoPayStatusDelegate;
    @Autowired
    private DistributedLock distributedLock;

    @Override
    public List<OcpxManagedAutoPayStatusDto> findAutoPayStatusByCampaignIdList(List<Integer> campaignIds) {
        List<OcpxManagedAutoPayStatusDto> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(campaignIds)) {
            return result;
        }

        OcpxManagedAutoCompensationStatusPoExample example = new OcpxManagedAutoCompensationStatusPoExample();
        example.or().andCampaignIdIn(campaignIds).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<OcpxManagedAutoCompensationStatusPo> autoPayStatusPoList = ocpxManagedAutoCompensationStatusDao.selectByExample(example);

        for (OcpxManagedAutoCompensationStatusPo ocpxAutoCompensationStatusPo : autoPayStatusPoList) {
            result.add(po2Dto(ocpxAutoCompensationStatusPo));
        }

        return result;
    }

    @Override
    public List<OcpxManagedAutoPayStatusDto> findAutoPayStatusByCampaignIdListAndPeriod(List<Integer> campaignIds, Integer period) {
        List<OcpxManagedAutoPayStatusDto> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(campaignIds)) {
            return result;
        }

        OcpxManagedAutoCompensationStatusPoExample example = new OcpxManagedAutoCompensationStatusPoExample();
        example.or().andCampaignIdIn(campaignIds).andPeriodEqualTo(period).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<OcpxManagedAutoCompensationStatusPo> autoPayStatusPoList = ocpxManagedAutoCompensationStatusDao.selectByExample(example);

        for (OcpxManagedAutoCompensationStatusPo ocpxAutoCompensationStatusPo : autoPayStatusPoList) {
            result.add(po2Dto(ocpxAutoCompensationStatusPo));
        }

        return result;
    }

    @Override
    public void saveAutoPayStatus(List<OcpxManagedAutoPayStatusDto> ocpxAutoPayStatusDtoList) {
        if (CollectionUtils.isEmpty(ocpxAutoPayStatusDtoList)) {
            return;
        }
        autoPayStatusDelegate.saveAutoPayStatus(ocpxAutoPayStatusDtoList);
    }

    @Override
    public List<OcpxManagedAutoPayStatusDto> getAutoPayStatusByPayDate(Date payDate) {
        List<OcpxManagedAutoPayStatusDto> result = new ArrayList<>();

        OcpxManagedAutoCompensationStatusPoExample example = new OcpxManagedAutoCompensationStatusPoExample();
        example.or()
                .andPayDateLessThanOrEqualTo(Utils.getEndOfDay(new Timestamp(payDate.getTime())))
                .andPayDateGreaterThanOrEqualTo(Utils.getBeginOfDay(new Timestamp(payDate.getTime())))
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<OcpxManagedAutoCompensationStatusPo> autoPayStatusPoList = ocpxManagedAutoCompensationStatusDao.selectByExample(example);
        for (OcpxManagedAutoCompensationStatusPo ocpxAutoCompensationStatusPo : autoPayStatusPoList) {
            result.add(po2Dto(ocpxAutoCompensationStatusPo));
        }

        return result;
    }

    private OcpxManagedAutoPayStatusDto po2Dto(OcpxManagedAutoCompensationStatusPo po) {
        OcpxManagedAutoPayStatusDto dto = new OcpxManagedAutoPayStatusDto();
        BeanUtils.copyProperties(po, dto);
        return dto;
    }
}
