package com.bilibili.adp.cpc.biz.services.unit.strategy;

import com.bilibili.adp.cpc.biz.services.unit.dto.UpdateCpcUnitDto;
import com.bilibili.adp.launch.api.unit.dto.BatchUpdateNoDpaUnitDto;
import com.bilibili.adp.cpc.biz.services.unit.dto.NewCpcUnitDto;

/**
 * 单元校验策略
 * <AUTHOR>
 * @date 2022-01-21 12:00 下午
 */
public interface IUnitVerifyStrategy {
    /**
     * 返回策略对应的推广目的
     * @return Integer 推广目的
     */
    Integer getPromotionPurposeType();
    /**
     *  校验新建单元
     *
     * <AUTHOR>
     * @date  2022-01-20 6:14 下午
     * @param unit 新建单元dto
     */
    void verifyNewUnit(NewCpcUnitDto unit);

    /**
     *
     *  校验更新单元
     * <AUTHOR>
     * @date  2022-02-02 3:32 下午
     * @param unit 更新单元 dto
     */
    void verifyUpdateUnit(UpdateCpcUnitDto unit);

    /**
     *  校验批量更新单元
     *
     * <AUTHOR>
     * @date  2022-02-10 8:32 下午
     * @param unit 批量更新单元
     */
    void verifyBatchUpdateUnit(BatchUpdateNoDpaUnitDto unit);


}
