package com.bilibili.adp.cpc.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 */
@Configuration
public class AsyThreadPoolConfig {


    @Bean("creativeExtDescriptionPool")
    public ExecutorService CreativeExtDescriptionPool() {
        return threadPoolAbort(2, "creativeExtDescriptionPool");
    }

    @Bean("gameCreativePubPool")
    public ExecutorService GameCreativePubPool() {
        return threadPoolAbortDequeSize(2, "gameCreativePubPool", 256);
    }

    private ThreadPoolExecutor threadPoolAbort(int poolSize, String threadNamePrefix) {
        return new ThreadPoolExecutor(poolSize, poolSize, 0L, TimeUnit.MILLISECONDS,
                new LinkedBlockingDeque<>(128), new NamedThreadFactor(threadNamePrefix));
    }

    private ThreadPoolExecutor threadPoolAbortDequeSize(int poolSize, String threadNamePrefix, int dequeSize) {
        return new ThreadPoolExecutor(poolSize, poolSize, 0L, TimeUnit.MILLISECONDS,
                new LinkedBlockingDeque<>(dequeSize), new NamedThreadFactor(threadNamePrefix));
    }


    private class NamedThreadFactor implements ThreadFactory {


        private final AtomicInteger threadNumber = new AtomicInteger(1);
        private final String namePrefix;

        NamedThreadFactor(String namePrefix) {
            this.namePrefix = namePrefix + "-";
        }


        @Override
        public Thread newThread(Runnable r) {
            Thread thread = new Thread(r, namePrefix + threadNumber.getAndIncrement());
            if (thread.isDaemon()) {
                thread.setDaemon(true);
            }
            if (thread.getPriority() != Thread.NORM_PRIORITY) {
                thread.setPriority(Thread.NORM_PRIORITY);
            }
            return thread;
        }
    }
}
