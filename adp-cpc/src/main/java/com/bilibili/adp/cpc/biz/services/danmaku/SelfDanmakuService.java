package com.bilibili.adp.cpc.biz.services.danmaku;

import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.util.SnowflakeIdWorker;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.converter.danmaku.SelfDanmakuConverter;
import com.bilibili.adp.cpc.biz.services.danmaku.api.ISelfDanmakuMaterialService;
import com.bilibili.adp.cpc.biz.services.danmaku.bos.SelfDanmakuBo;
import com.bilibili.adp.cpc.biz.services.danmaku.bos.SelfDanmakuGroupSaveBo;
import com.bilibili.adp.cpc.biz.services.danmaku.bos.SelfDanmakuMaterialSaveBo;
import com.bilibili.adp.cpc.biz.services.danmaku.bos.SelfDanmakuSaveEntityBo;
import com.bilibili.adp.cpc.biz.services.danmaku.bos.query.QuerySelfDanmakuBo;
import com.bilibili.adp.cpc.core.constants.IsDeleted;
import com.bilibili.adp.cpc.dao.querydsl.pos.LauSelfDanmakuPo;
import com.bilibili.adp.cpc.enums.danmaku.SelfDanmakuAuditStatusEnum;
import com.bilibili.adp.cpc.enums.danmaku.SelfDanmakuStatusEnum;
import com.bilibili.adp.cpc.enums.danmaku.SelfDanmakuTypeEnum;
import com.bilibili.bjcom.querydsl.BaseQueryFactory;
import com.bilibili.bjcom.querydsl.clause.BaseQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.AD_BQF;
import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.AD_TM;
import static com.bilibili.adp.cpc.dao.querydsl.QLauSelfDanmaku.lauSelfDanmaku;

/**
 * @ClassName SelfDanmakuService
 * <AUTHOR>
 * @Date 2023/12/27 2:04 下午
 * @Version 1.0
 **/
@Service
@Slf4j
public class SelfDanmakuService {

    @Resource(name = AD_BQF)
    private BaseQueryFactory adBqf;

    @Resource
    private SnowflakeIdWorker snowflakeIdWorker;

    @Resource
    private ISelfDanmakuMaterialService selfDanmakuMaterialService;


    public List<SelfDanmakuBo> queryByGroupId(Long groupId) {
        if (!Utils.isPositive(groupId)) {
            return Collections.emptyList();
        }

        QuerySelfDanmakuBo queryBo = QuerySelfDanmakuBo.builder()
                .groupId(groupId)
                .danmakuStatusList(SelfDanmakuStatusEnum.NON_DELETED_SELF_DANMAKU_STATUS_LIST)
                .build();
        return queryList(queryBo);
    }

    public Map<Long, List<SelfDanmakuBo>> queryMapByGroupIdList(List<Long> groupIdList) {
        if (CollectionUtils.isEmpty(groupIdList)) {
            return Collections.emptyMap();
        }

        QuerySelfDanmakuBo queryBo = QuerySelfDanmakuBo.builder()
                .groupIdList(groupIdList)
                .danmakuStatusList(SelfDanmakuStatusEnum.NON_DELETED_SELF_DANMAKU_STATUS_LIST)
                .build();

        List<SelfDanmakuBo> selfDanmakuBoList = queryList(queryBo);
        return selfDanmakuBoList.stream()
                .collect(Collectors.groupingBy(SelfDanmakuBo::getGroupId));
    }

    public long countByGroupId(Long groupId) {
        Assert.isTrue(Utils.isPositive(groupId), "查询弹幕组id不可为空");
        QuerySelfDanmakuBo queryBo = QuerySelfDanmakuBo.builder()
                .groupId(groupId)
                .danmakuStatusList(SelfDanmakuStatusEnum.NON_DELETED_SELF_DANMAKU_STATUS_LIST)
                .build();
        return generateBaseQuery(queryBo).fetchCount();
    }

    public List<SelfDanmakuBo> queryList(QuerySelfDanmakuBo queryBo) {
        validateQueryBo(queryBo);
        return generateBaseQuery(queryBo).fetch(SelfDanmakuBo.class);
    }

    public PageResult<SelfDanmakuBo> queryForPage(QuerySelfDanmakuBo queryBo) {
        validateQueryBo(queryBo);
        long count = generateBaseQuery(queryBo).fetchCount();
        List<SelfDanmakuBo> recordList = generateBaseQuery(queryBo).fetch(SelfDanmakuBo.class);
        return PageResult.<SelfDanmakuBo>builder()
                .total((int) count)
                .records(recordList)
                .build();
    }

    private BaseQuery<LauSelfDanmakuPo> generateBaseQuery(QuerySelfDanmakuBo queryBo) {
         return adBqf.selectFrom(lauSelfDanmaku)
                .whereIfNotEmpty(queryBo.getDanmakuIdList(), lauSelfDanmaku.danmakuId::in)
                .whereIfNotEmpty(queryBo.getDanmakuStatusList(), lauSelfDanmaku.danmakuStatus::in)
                .whereIfNotEmpty(queryBo.getGroupIdList(), lauSelfDanmaku.groupId::in)

                .whereIfNotNull(queryBo.getAccountId(), lauSelfDanmaku.accountId::eq)
                .whereIfNotNull(queryBo.getGroupId(), lauSelfDanmaku.groupId::eq)
                .whereIfNotNull(queryBo.getDanmakuStatus(), lauSelfDanmaku.danmakuStatus::eq)
                .whereIfNotNull(queryBo.getDanmakuType(), lauSelfDanmaku.danmakuType::eq)
                .whereIfNotNull(queryBo.getStartMtime(), lauSelfDanmaku.mtime::goe)
                .whereIfNotNull(queryBo.getEndMtime(), lauSelfDanmaku.mtime::loe)

                .whereIfHasText(queryBo.getDanmakuMd5(), lauSelfDanmaku.danmakuMd5::eq)

                .where(lauSelfDanmaku.isDeleted.eq(IsDeleted.VALID))

                 .orderByIfHasText(queryBo.getOrderBy())

                .limitIfNotNull(queryBo.getPageSize())
                .offsetIfNotNull(Objects.isNull(queryBo.getPage()) || Objects.isNull(queryBo.getPageSize()) ?
                        null : (long) (queryBo.getPage() - 1) * queryBo.getPageSize());
    }

    private void validateQueryBo(QuerySelfDanmakuBo queryBo) {
        Assert.notNull(queryBo, "查询商业弹幕条件不可为空");
        boolean hasAccountId = Utils.isPositive(queryBo.getAccountId());
        boolean hasDanmakuId = !CollectionUtils.isEmpty(queryBo.getDanmakuIdList());
        boolean hasGroupId = Objects.nonNull(queryBo.getGroupId())
                || !CollectionUtils.isEmpty(queryBo.getGroupIdList());
        Assert.isTrue(hasAccountId || hasDanmakuId || hasGroupId, "查询商业自提弹幕条件不足");
    }

    @Transactional(value = AD_TM, rollbackFor = Exception.class)
    public void saveSelfDanmakuList(SelfDanmakuGroupSaveBo saveBo,
                                    List<SelfDanmakuBo> existBoList,
                                    boolean needSaveMaterial) {
        List<SelfDanmakuSaveEntityBo> entityBoList = saveBo.getEntityBoList();
        Set<Long> existDanmakuIdSet =
                existBoList.stream().map(SelfDanmakuBo::getDanmakuId).collect(Collectors.toSet());
        List<SelfDanmakuSaveEntityBo> updateEntityBoList = entityBoList.stream()
                .filter(entityBo -> Utils.isPositive(entityBo.getDanmakuId()))
                .collect(Collectors.toList());
        List<SelfDanmakuSaveEntityBo> insertEntityBoList = entityBoList.stream()
                .filter(entityBo -> !Utils.isPositive(entityBo.getDanmakuId()))
                .collect(Collectors.toList());

        Set<Long> updateDanmakuIds = updateEntityBoList.stream()
                .map(SelfDanmakuSaveEntityBo::getDanmakuId)
                .collect(Collectors.toSet());
        List<Long> deleteDanmakuIds = existDanmakuIdSet.stream()
                .filter(danmakuId -> !updateDanmakuIds.contains(danmakuId))
                .collect(Collectors.toList());
        Assert.isTrue(existDanmakuIdSet.containsAll(updateDanmakuIds), "您不能编辑不属于当前弹幕组的弹幕");

        // 新建
        insertEntityBoList.forEach(this::insertBySaveEntityBo);

        // 更新
        updateEntityBoList.forEach(this::updateBySaveEntityBo);

        // 软删
        deleteBatch(saveBo.getGroupId(), deleteDanmakuIds);

        // 同步素材库
        if (needSaveMaterial) {
            List<SelfDanmakuMaterialSaveBo> materialSaveBoList =
                    SelfDanmakuConverter.MAPPER.convertSave2MaterialSaveBoList(entityBoList);
            materialSaveBoList.forEach(materialSaveBo -> {
                Long materialId = snowflakeIdWorker.nextId();
                String danmakuTypeDesc =
                        SelfDanmakuTypeEnum.getByCode(materialSaveBo.getDanmakuType())
                                .getDesc();
                String materialName = saveBo.getGroupName() + "-" + danmakuTypeDesc + "-" + materialId;
                materialSaveBo.setDanmakuName(materialName);
                materialSaveBo.setDanmakuMaterialId(materialId);
            });
            selfDanmakuMaterialService.batchInsertMaterialListFromGroup(materialSaveBoList, saveBo.getAccountId());
        }
    }

    public void auditPassByGroupIdAndDanmakuIdList(Long groupId, List<Long> danmakuIdList) {
        Assert.isTrue(Utils.isPositive(groupId), "过审弹幕组id不可为空");
        Assert.notEmpty(danmakuIdList, "过审弹幕id不可为空");
        adBqf.update(lauSelfDanmaku)
                .where(lauSelfDanmaku.groupId.eq(groupId))
                .where(lauSelfDanmaku.danmakuId.in(danmakuIdList))
                .set(lauSelfDanmaku.danmakuStatus, SelfDanmakuStatusEnum.AUDIT_PASS.getCode())
                .set(lauSelfDanmaku.danmakuAuditStatus, SelfDanmakuAuditStatusEnum.AUDIT_PASS.getCode())
                .execute();
    }

    public void auditRejectByGroupIdAndDanmakuIdList(Long groupId, List<Long> danmakuIdList) {
        Assert.isTrue(Utils.isPositive(groupId), "过审弹幕组id不可为空");
        Assert.notEmpty(danmakuIdList, "过审弹幕id不可为空");
        adBqf.update(lauSelfDanmaku)
                .where(lauSelfDanmaku.groupId.eq(groupId))
                .where(lauSelfDanmaku.danmakuId.in(danmakuIdList))
                .set(lauSelfDanmaku.danmakuStatus, SelfDanmakuStatusEnum.AUDIT_REJECT.getCode())
                .set(lauSelfDanmaku.danmakuAuditStatus, SelfDanmakuAuditStatusEnum.AUDIT_REJECT.getCode())
                .execute();
    }

    private void insertBySaveEntityBo(SelfDanmakuSaveEntityBo saveEntityBo) {
        Assert.isTrue(Utils.isPositive(saveEntityBo.getGroupId()), "新建弹幕组id不可为空");
        //唯一id 雪花算法
        saveEntityBo.setDanmakuId(snowflakeIdWorker.nextId());
        adBqf.insert(lauSelfDanmaku)
                .insertBean(saveEntityBo);
    }

    private void updateBySaveEntityBo(SelfDanmakuSaveEntityBo saveEntityBo) {
        Assert.isTrue(Utils.isPositive(saveEntityBo.getGroupId()), "更新弹幕组id不可为空");
        Assert.isTrue(Utils.isPositive(saveEntityBo.getDanmakuId()), "更新弹幕id不可为空");

        adBqf.update(lauSelfDanmaku)
                .where(lauSelfDanmaku.danmakuId.eq(saveEntityBo.getDanmakuId()))
                .where(lauSelfDanmaku.groupId.eq(saveEntityBo.getGroupId()))
                .where(lauSelfDanmaku.danmakuStatus.in(SelfDanmakuStatusEnum.NON_DELETED_SELF_DANMAKU_STATUS_LIST))
                .where(lauSelfDanmaku.isDeleted.eq(IsDeleted.VALID))
                .populate(saveEntityBo)
                .execute();
    }

    private void deleteBatch(Long groupId, List<Long> danmakuIdList) {
        Assert.isTrue(Utils.isPositive(groupId), "删除弹幕组下弹幕时弹幕组id不可为空");

        if (CollectionUtils.isEmpty(danmakuIdList)) {
            return;
        }

        adBqf.update(lauSelfDanmaku)
                .whereIfNotNull(groupId, lauSelfDanmaku.groupId::eq)
                .whereIfNotEmpty(danmakuIdList, lauSelfDanmaku.danmakuId::in)
                .set(lauSelfDanmaku.danmakuStatus, SelfDanmakuStatusEnum.DELETED.getCode())
                .execute();
    }

    public void deleteByGroupIdList(List<Long> groupIdList) {
        if (CollectionUtils.isEmpty(groupIdList)) {
            return;
        }

        adBqf.update(lauSelfDanmaku)
                .where(lauSelfDanmaku.groupId.in(groupIdList))
                .set(lauSelfDanmaku.danmakuStatus, SelfDanmakuStatusEnum.DELETED.getCode())
                .execute();
    }
}
