package com.bilibili.adp.cpc.dao.querydsl.pos;

import javax.annotation.Generated;

/**
 * ResTargetPackageCrowdPackageUpgradePo is a Querydsl bean type
 */
@Generated("com.querydsl.codegen.BeanSerializer")
public class ResTargetPackageCrowdPackageUpgradePo {

    private Integer crowdPackId;

    private java.sql.Timestamp ctime;

    private Integer groupId;

    private Long id;

    private Integer isDeleted;

    private java.sql.Timestamp mtime;

    private Integer targetPackageId;

    private Integer type;

    public Integer getCrowdPackId() {
        return crowdPackId;
    }

    public void setCrowdPackId(Integer crowdPackId) {
        this.crowdPackId = crowdPackId;
    }

    public java.sql.Timestamp getCtime() {
        return ctime;
    }

    public void setCtime(java.sql.Timestamp ctime) {
        this.ctime = ctime;
    }

    public Integer getGroupId() {
        return groupId;
    }

    public void setGroupId(Integer groupId) {
        this.groupId = groupId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public java.sql.Timestamp getMtime() {
        return mtime;
    }

    public void setMtime(java.sql.Timestamp mtime) {
        this.mtime = mtime;
    }

    public Integer getTargetPackageId() {
        return targetPackageId;
    }

    public void setTargetPackageId(Integer targetPackageId) {
        this.targetPackageId = targetPackageId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    @Override
    public String toString() {
         return "crowdPackId = " + crowdPackId + ", ctime = " + ctime + ", groupId = " + groupId + ", id = " + id + ", isDeleted = " + isDeleted + ", mtime = " + mtime + ", targetPackageId = " + targetPackageId + ", type = " + type;
    }

}

