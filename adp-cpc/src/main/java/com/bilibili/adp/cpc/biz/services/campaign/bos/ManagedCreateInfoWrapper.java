package com.bilibili.adp.cpc.biz.services.campaign.bos;

import com.bilibili.adp.cpc.biz.services.unit.dto.CpcUnitDto;
import com.bilibili.adp.launch.api.creative.dto.ImageVideoDto;
import com.bilibili.adp.launch.api.flyPro.dto.enums.ScenesEnum;
import lombok.Builder;
import lombok.Data;


/**
 * copy class
 * @see com.bilibili.adp.launch.biz.bean.ManagedCampaignJobBo
 * <AUTHOR>
 * @date 2021/8/25
 */
@Builder
@Data
public class ManagedCreateInfoWrapper {

    private ManagedCampaignJobBo managedCampaign;
    private CpcUnitDto unit;
    private ManagedCampaignUnderBoxDto underBox;
    private ScenesEnum scenesEnum;
    private Integer flyBannerCoverType;
    private ManagedVideoCreativeCoverTitleDto jobTitleDto;
    private ImageVideoDto imageVideodto;
    private String title;
    private String videoTitle;
    private Integer creativeIndex;
}
