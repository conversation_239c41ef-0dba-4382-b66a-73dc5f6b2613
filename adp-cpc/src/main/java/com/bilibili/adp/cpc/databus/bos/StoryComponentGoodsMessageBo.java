package com.bilibili.adp.cpc.databus.bos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class StoryComponentGoodsMessageBo {
    private Integer accountId;
    // 商品导入的来源: 0:未知 1:三连 2:悬赏服务商页面 3:运营后台
    private Integer bizFrom;
    // 商品来源
    private Integer sourceType;
    // 商品的item_id
    private Long itemId;
    private String itemName;
    // 跳转类型: 0-普通商品链接 1-app schema_url跳转 2-小程序跳转
    private Integer urlType;
    // 跳转 H5 URL
    private String jumpUrl;
    // 跳转 schema URL
    private String schemaUrl;
    // 跳转 小程序三元组
    private String miniAppOriginId;
    private String miniAppName;
    private String miniAppPath;
    // 是否启用 0:未知 1:上架 2:下架
    private Integer shelvesStatus;

    private Integer isDeleted;
    private Date ctime;
    private Date mtime;
}
