package com.bilibili.adp.cpc.splash_screen.creative.service;

import com.bapis.cheese.service.season.business.v1.AnalyzeAdURLItem;
import com.bapis.cheese.service.season.business.v1.AnalyzeAdURLsRequest;
import com.bapis.cheese.service.season.business.v1.BusinessServiceGrpc;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauCreativeExtraPo;
import com.bilibili.adp.cpc.utils.CommonFuncs;
import com.bilibili.adp.cpc.utils.RecDiffResult;
import com.bilibili.bjcom.querydsl.BaseQueryFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import pleiades.venus.starter.rpc.client.RPCClient;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.bilibili.adp.cpc.dao.ad_core.querydsl.QLauCreativeExtra.lauCreativeExtra;
import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.AD_CORE_BQF;
import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.AD_TM;

/**
 * 为了资质包id 包了这顿饺子 写了这个类
 */
@Slf4j
@Service
public class SplashScreenCreativeExtraService {

    @Resource(name = AD_CORE_BQF)
    private BaseQueryFactory adCoreBqf;

    @RPCClient(value = "cheese.season.service")
    private BusinessServiceGrpc.BusinessServiceBlockingStub businessServiceStub;

    public LauCreativeExtraPo get(Integer creativeId) {
        return adCoreBqf.selectFrom(lauCreativeExtra)
                .where(lauCreativeExtra.creativeId.eq(creativeId))
                .where(lauCreativeExtra.isDeleted.eq(0))
                .fetchFirst();
    }

    public List<LauCreativeExtraPo> list(Collection<Integer> creativeIds) {
        return adCoreBqf.selectFrom(lauCreativeExtra)
                .where(lauCreativeExtra.creativeId.in(creativeIds))
                .where(lauCreativeExtra.isDeleted.eq(0))
                .fetch();
    }

    @Transactional(transactionManager = AD_TM, rollbackFor = Exception.class)
    public void save(Integer creativeId, LauCreativeExtraPo po) {
        final LauCreativeExtraPo existingPo = get(creativeId);
        final List<LauCreativeExtraPo> existingPos = Stream.of(existingPo).filter(Objects::nonNull).collect(Collectors.toList());
        final List<LauCreativeExtraPo> pos = Stream.of(po).filter(Objects::nonNull).collect(Collectors.toList());
        final RecDiffResult<LauCreativeExtraPo, Long> result = CommonFuncs.recDiff(existingPos, pos, this::uk, LauCreativeExtraPo::getId, CommonFuncs.getDefaultBiFunction(LauCreativeExtraPo::getId, LauCreativeExtraPo::setId));
        CommonFuncs.handleRecDiff(result, adCoreBqf, lauCreativeExtra, lauCreativeExtra.id::in);
    }

    private Integer uk(LauCreativeExtraPo lauCreativeExtraPo) {
        return lauCreativeExtraPo.getCreativeId();
    }


    public Map<String, LauCreativeExtraPo> getSeasonInfo(List<String> urls) {
        List<String> seasonUrls = urls.stream().filter(x -> x.contains("bilibili") && x.contains("cheese")).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(seasonUrls)) {
            return new HashMap<>();
        }
        AnalyzeAdURLsRequest.Builder builder = AnalyzeAdURLsRequest.newBuilder().addAllUrl(urls);
        Map<String, LauCreativeExtraPo> map = new HashMap<>();

        try {
            Map<String, AnalyzeAdURLItem> infosMap = businessServiceStub.analyzeAdURLs(builder.build()).getInfosMap();
            if (!CollectionUtils.isEmpty(infosMap)) {
                infosMap.keySet().forEach(x -> {
                    AnalyzeAdURLItem analyzeAdURLItem = infosMap.get(x);
                    LauCreativeExtraPo po = new LauCreativeExtraPo();
                    po.setSeasonId((analyzeAdURLItem.getSeasonId()));
                    po.setSeasonAvid((analyzeAdURLItem.getAid()));
                    map.put(x, po);
                });
            }
        } catch (Exception e) {
            log.error("getSeasonInfo exception e", e);
        }
        return map;
    }
}
