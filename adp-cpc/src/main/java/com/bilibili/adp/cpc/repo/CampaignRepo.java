package com.bilibili.adp.cpc.repo;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.core.constants.IsDeleted;
import com.bilibili.adp.launch.biz.ad_core.mybatis.LauCampaignDao;
import com.bilibili.adp.launch.biz.pojo.LauCampaignPo;
import com.bilibili.adp.launch.biz.pojo.LauCampaignPoExample;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/2 00:15
 */
@Repository
@RequiredArgsConstructor
public class CampaignRepo {

    private final LauCampaignDao lauCampaignDao;

    public LauCampaignPo fetchCampaign(Integer campaignId) {
        if (!Utils.isPositive(campaignId)) {
            return null;
        }

        LauCampaignPo lauCampaignPo = lauCampaignDao.selectByPrimaryKey(campaignId);
        return lauCampaignPo;
    }

    public List<LauCampaignPo> queryCampaignList(List<Integer> campaignIds) {
        if (CollectionUtils.isEmpty(campaignIds)) {
            return Collections.EMPTY_LIST;
        }

        LauCampaignPoExample example = new LauCampaignPoExample();
        example.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID).andCampaignIdIn(campaignIds);
        return lauCampaignDao.selectByExample(example);
    }

}
