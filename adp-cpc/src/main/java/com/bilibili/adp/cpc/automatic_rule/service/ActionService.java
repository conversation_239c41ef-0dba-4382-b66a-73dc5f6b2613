package com.bilibili.adp.cpc.automatic_rule.service;

import com.bilibili.adp.cpc.automatic_rule.bos.ActionBo;
import com.bilibili.adp.cpc.automatic_rule.constants.AutomaticRuleConstant;
import com.bilibili.adp.cpc.automatic_rule.converter.ActionConverter;
import com.bilibili.adp.cpc.automatic_rule.enums.action.ActionType;
import com.bilibili.adp.cpc.automatic_rule.enums.action.Subject;
import com.bilibili.adp.cpc.automatic_rule.enums.action.ValueType;
import com.bilibili.adp.cpc.dao.querydsl.pos.LauAutomaticRuleActionPo;
import com.bilibili.adp.cpc.utils.CommonFuncs;
import com.bilibili.adp.cpc.utils.RecDiffResult;
import com.bilibili.bjcom.querydsl.BaseQueryFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.AD_BQF;
import static com.bilibili.adp.cpc.dao.querydsl.QLauAutomaticRuleAction.lauAutomaticRuleAction;

@Slf4j
@Service
@RequiredArgsConstructor
public class ActionService {
    @Resource(name = AD_BQF)
    private BaseQueryFactory adBqf;

    public static List<String> actions(List<LauAutomaticRuleActionPo> actions) {
        return actions
                .stream()
                .map(ActionService::action)
                .collect(Collectors.toList());
    }
    public static String action(LauAutomaticRuleActionPo action) {
        Subject subject = Subject.getByCode(action.getSubject());
        if (subject == Subject.PAUSE_UNIT) {
            return subject.getDesc();
        }
        String actionTypeDesc = ActionType.getByCode(action.getActionType()).getDesc();
        String subjectDesc = subject.getDesc();
        String value = action.getValue();
        String valueTypeMark = ValueType.getByCode(action.getValueType()).getMark();
        return  actionTypeDesc + subjectDesc + value + valueTypeMark;
    }

    public static String action(LauAutomaticRuleActionPo action, BigDecimal originValue, BigDecimal adjustValue) {
        Subject subject = Subject.getByCode(action.getSubject());
        if (subject == Subject.PAUSE_UNIT) {
            return subject.getDesc();
        }
        String actionTypeDesc = ActionType.getByCode(action.getActionType()).getDesc();
        String subjectDesc = subject.getDesc();
        return  actionTypeDesc + subjectDesc + originValue + " -> " + adjustValue;
    }

    public LauAutomaticRuleActionPo getAction(Long actionId) {
        return adBqf
                .selectFrom(lauAutomaticRuleAction)
                .where(lauAutomaticRuleAction.actionId.eq(actionId))
                .where(lauAutomaticRuleAction.isDeleted.eq(0))
                .fetchFirst();
    }
    public List<LauAutomaticRuleActionPo> listActionsByRuleId(Long ruleId) {
        return adBqf
                .selectFrom(lauAutomaticRuleAction)
                .where(lauAutomaticRuleAction.ruleId.eq(ruleId))
                .where(lauAutomaticRuleAction.isDeleted.eq(0))
                .fetch();
    }
    public List<LauAutomaticRuleActionPo> listActionsByRuleIds(Collection<Long> ruleIds) {
        return adBqf
                .selectFrom(lauAutomaticRuleAction)
                .where(lauAutomaticRuleAction.ruleId.in(ruleIds))
                .where(lauAutomaticRuleAction.isDeleted.eq(0))
                .fetch();
    }

    public void saveActions(Integer accountId, Long ruleId, List<ActionBo> actions) {
        final List<LauAutomaticRuleActionPo> existedPos = listActionsByRuleIds(Collections.singletonList(ruleId));
        final List<LauAutomaticRuleActionPo> newPos = actions
                .stream()
                .map(action -> ActionConverter.MAPPER.bo2Po(accountId, ruleId, action))
                .collect(Collectors.toList());
        RecDiffResult<LauAutomaticRuleActionPo, Long> result = CommonFuncs.recDiff(existedPos, newPos, this::uk, LauAutomaticRuleActionPo::getActionId, LauAutomaticRuleActionPo::setActionId);
        CommonFuncs.handleRecDiff(result, adBqf, lauAutomaticRuleAction, lauAutomaticRuleAction.actionId::in);
    }

    private String uk(LauAutomaticRuleActionPo po) {
        return po.getAccountId() + "-" + po.getRuleId() + "-" + po.getActionId();
    }

    public void deleteActions(Integer accountId, Long ruleId) {
        adBqf
                .update(lauAutomaticRuleAction)
                .set(lauAutomaticRuleAction.isDeleted, AutomaticRuleConstant.RULE_IS_DELETED)
                .where(lauAutomaticRuleAction.accountId.eq(accountId))
                .where(lauAutomaticRuleAction.ruleId.eq(ruleId))
                .execute();
    }
}
