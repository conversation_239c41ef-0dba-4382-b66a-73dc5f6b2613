package com.bilibili.adp.cpc.biz.services.account.dto;

import com.bilibili.adp.cpc.enums.AdpCouponTypeEnum;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CouponDeatailDto {

    private Long couponId;

    /**
     * 优惠券名称
     */
    private String couponName;

    private String couponDesc;

    /**
     * 优惠券面值
     */
    private BigDecimal couponValue;
    private BigDecimal couponLockValue;

    /**
     * 状态
     * @see com.bilibili.adp.cpc.enums.CouponStatusEnum
     */
    private Integer status;
    private String statusDesc;

    /**
     * 开始时间
     */
    private String beginTime;
    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 已使用赠送金额(元)
     */
    private BigDecimal usedCouponValue;
    /**
     * 剩余赠送金额(元)
     */
    private BigDecimal notUsedCouponValue;

    /**
     * 已使用冻结金额(元)
     */
    private BigDecimal usedCouponLockValue;
    /**
     * 剩余冻结金额(元)
     */
    private BigDecimal notUsedCouponLockValue;

    /**
     * 操作人
     */
    private String operateUserName;

    /**
     * 操作时间
     */
    private String operateTime;

    /**
     * 券类型
     * @see AdpCouponTypeEnum
     */
    private Integer couponType;

    @ApiModelProperty("已使用冻结金额比例")
    private BigDecimal usedCouponValuePercent;
    @ApiModelProperty("已使用赠送金额比例")
    private BigDecimal usedCouponLockValuePercent;
    @ApiModelProperty("已使用金额比例")
    private BigDecimal usedCouponPercent;

    private Integer projectId;
    private String projectName;

    private Integer crmCouponType;
    private Long shardCouponId;
    private Long childCouponId;

    // 当前共享券子券消耗
    private BigDecimal childCouponConsumeValue;

}
