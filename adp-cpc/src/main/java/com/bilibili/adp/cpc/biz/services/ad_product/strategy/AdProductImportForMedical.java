package com.bilibili.adp.cpc.biz.services.ad_product.strategy;

import com.bilibili.adp.account.dto.AccountAllInfoDto;
import com.bilibili.adp.account.dto.AccountDto;
import com.bilibili.adp.account.service.IQueryAccountService;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.services.account.AdpCpcAccountService;
import com.bilibili.adp.cpc.biz.services.account.config.AccountConfig;
import com.bilibili.adp.cpc.biz.services.ad_product.AdProductHelper;
import com.bilibili.adp.cpc.biz.services.ad_product.dto.ImportAdProductMedicalDto;
import com.bilibili.adp.cpc.biz.services.ad_product.dto.ImportAdProductResultDto;
import com.bilibili.adp.cpc.biz.services.log.DbTable;
import com.bilibili.adp.cpc.biz.services.log.ILogOperateNewService;
import com.bilibili.adp.cpc.dao.querydsl.pos.AccAccountPo;
import com.bilibili.adp.cpc.enums.ad_product.AdProductCategoryLevelEnum;
import com.bilibili.adp.cpc.po.ad.AdProductCategoryPo;
import com.bilibili.adp.cpc.po.ad.AdProductSkuPo;
import com.bilibili.adp.cpc.po.ad.AdProductSpuPo;
import com.bilibili.adp.cpc.repo.AdProductRepo;
import com.bilibili.adp.resource.api.account.label.IAccountLabelService;
import com.bilibili.adp.resource.biz.pojo.ResTargetItemPo;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.bilibili.adp.cpc.biz.services.ad_product.AdProductUtils.convertAreaName2IdArrStr;
import static com.bilibili.adp.cpc.biz.services.ad_product.AdProductUtils.convertId2ArrStr;

/**
 * 投放产品-医疗产品导入实现类
 */
@Slf4j
@Component
public class AdProductImportForMedical extends AdProductImportBase<ImportAdProductMedicalDto> {

    @Autowired
    private IQueryAccountService queryAccountService;

    @Autowired
    private ILogOperateNewService logOperateService;

    @Autowired
    private AdProductHelper adProductHelper;

    @Autowired
    private IAccountLabelService accountLabelService;

    @Autowired
    private AccountConfig accountConfig;

    @Value("${sdpa.medical.united.first.industry.id:205}")
    private Integer sdpaMedicalUnitedFirstIndustryId;

    @Value("#{'${sdpa.medical.united.second.industry.set:206,279,288}'.split(',')}")
    private Set<Integer> sdpaMedicalUnitSecondIndustrySet;
    @Autowired
    private AdpCpcAccountService adpCpcAccountService;

    public AdProductImportForMedical(AdProductRepo adProductRepo, RedissonClient redissonClient) {
        super(adProductRepo, redissonClient);
    }

    @Override
    protected void validateImportData(List<ImportAdProductMedicalDto> importDtos) {
        // 基础验证逻辑
    }

    @Override
    protected void importDataBizProc(List<ImportAdProductMedicalDto> importDtos) {
        // 获取账户信息
        Integer accountId = importDtos.stream().map(ImportAdProductMedicalDto::getAccountId)
                .findFirst().orElse(0);
        AccountAllInfoDto accountAllInfoDto = queryAccountService.getAccountAllInfoFromCache(accountId);
        AccountDto accountDto = accountAllInfoDto.getAccountDto();

        AccAccountPo accAccountPo = adpCpcAccountService.get(accountId);

        // 检查行业和标签权限
        // 医疗库权限: 需要满足行业要求或者在标签白名单中
        List<Integer> accountLabelIds = accountLabelService.getLabelIdsByAccountId(accountId);
        boolean hasLabel = accountLabelIds.contains(accountConfig.getIsSupportSdpaMedicalLabelId());
        boolean hasIndustry = Objects.equals(accAccountPo.getUnitedFirstIndustryId(), sdpaMedicalUnitedFirstIndustryId)
                && sdpaMedicalUnitSecondIndustrySet.contains(accAccountPo.getUnitedSecondIndustryId());

        Assert.isTrue(hasIndustry || hasLabel, "账号:" + accountDto.getAccountId() + "行业不符合或不在白名单内");

        // 获取行业分类信息
        List<AdProductCategoryPo> firstCategoryPos =
                adProductRepo.getAllProductCategoryListByLevel(AdProductCategoryLevelEnum.ONE.getCode());
        Map<String, AdProductCategoryPo> firstCategoryNameMap =
                firstCategoryPos.stream().collect(Collectors.toMap(AdProductCategoryPo::getName, Function.identity(), adProductHelper::compareCategoryRule));

        List<AdProductCategoryPo> secondCategoryPos =
                adProductRepo.getAllProductCategoryListByLevel(AdProductCategoryLevelEnum.TWO.getCode());
        Map<String, List<AdProductCategoryPo>> secondCategoryNameListMap = secondCategoryPos.stream().collect(Collectors.groupingBy(AdProductCategoryPo::getName, Collectors.toList()));

        List<AdProductCategoryPo> thirdCategoryPos =
                adProductRepo.getAllProductCategoryListByLevel(AdProductCategoryLevelEnum.THREE.getCode());
        Map<String, List<AdProductCategoryPo>> thirdCategoryNameListMap = thirdCategoryPos.stream().collect(Collectors.groupingBy(AdProductCategoryPo::getName, Collectors.toList()));

        // 获取SPU信息
        List<String> spuNameList = importDtos.stream().map(ImportAdProductMedicalDto::getSpuName).distinct().collect(Collectors.toList());
        List<AdProductSpuPo> spuPos = adProductRepo.getSpuListByNames(spuNameList);
        Map<String, AdProductSpuPo> spuNameMap = spuPos.stream().collect(Collectors.toMap(AdProductSpuPo::getName, Function.identity(), adProductHelper::compareSpuPo));


        // 获取SPU信息
        List<String> skuNameList = importDtos.stream().map(ImportAdProductMedicalDto::getSkuName).distinct().collect(Collectors.toList());
        List<AdProductSkuPo> skuPos = adProductRepo.getSkuListByNames(skuNameList);
        Map<String, AdProductSkuPo> skuNameMap = skuPos.stream().collect(Collectors.toMap(AdProductSkuPo::getName, Function.identity(), adProductHelper::compareSkuPo));

        // 获取地区信息
        List<ResTargetItemPo> areaItemPos = adProductRepo.getAllAreaList();
        Map<String, Integer> areaNameMap = new HashMap<>();
        areaItemPos.forEach(po -> areaNameMap.put(po.getName(), po.getId()));

        // 处理每个导入项
        for (ImportAdProductMedicalDto medicalDto : importDtos) {
            int cur = medicalDto.getLineNum() + 3;

            // 处理商品价格
            double price = 0;
            if (StringUtils.hasText(medicalDto.getAdOriginalPriceName())) {
                price = Double.parseDouble(medicalDto.getAdOriginalPriceName());
                // 价格范围验证：0.1-9999999，保留两位小数
                Assert.isTrue(price >= 0.1 && price <= 9999999, "第" + cur + "行商品价格不在有效范围内(0.1-9999999)");
                medicalDto.setAdOriginalPrice(String.valueOf(Math.round(price * 100)));
            } else {
                Assert.isTrue(false, "第" + cur + "行商品价格不能为空");
            }

            // 商品名称验证：最长20个字符
            Assert.isTrue(StringUtils.hasText(medicalDto.getName()), "第" + cur + "行商品名称不能为空");
            Assert.isTrue(medicalDto.getName().length() <= 20, "第" + cur + "行商品名称不能超过20个字符");

            // 商品描述验证：最多500字
            if (StringUtils.hasText(medicalDto.getProductBrief())) {
                Assert.isTrue(medicalDto.getProductBrief().length() <= 500, "第" + cur + "行商品描述不能超过500个字符");
            }

            // 处理地区信息
            medicalDto.setArea(convertAreaName2IdArrStr(areaNameMap, medicalDto.getAreaName()));

            // 处理品牌信息
            Integer accProductId = accountDto.getProductId();
            medicalDto.setBrandId(convertId2ArrStr(accProductId));

            // 行业分类验证
            AdProductCategoryPo firstCategoryPo = firstCategoryNameMap.getOrDefault(medicalDto.getFirstCategoryName(),
                    AdProductCategoryPo.builder().code(0L).build());
            medicalDto.setFirstCategoryCode(firstCategoryPo.getCode());
            Assert.isTrue(Utils.isPositive(medicalDto.getFirstCategoryCode()), "第" + cur + "行一级行业不存在");

            List<AdProductCategoryPo> secondAdProductCategoryPoList = secondCategoryNameListMap.getOrDefault(medicalDto.getSecondCategoryName(), new ArrayList<>());
            List<AdProductCategoryPo> filterSecondAdProductCategoryPoList = secondAdProductCategoryPoList.stream().filter(adProductCategoryPo -> Objects.equals(medicalDto.getFirstCategoryCode(), adProductCategoryPo.getPCode())).collect(Collectors.toList());
            Assert.isTrue(filterSecondAdProductCategoryPoList.size() == 1, "第" + cur + "行二级行业不存在");
            medicalDto.setSecondCategoryCode(filterSecondAdProductCategoryPoList.get(0).getCode());

            List<AdProductCategoryPo> thirdAdProductCategoryPoList = thirdCategoryNameListMap.getOrDefault(medicalDto.getThirdCategoryName(), new ArrayList<>());
            List<AdProductCategoryPo> filterThirdAdProductCategoryPoList = thirdAdProductCategoryPoList.stream().filter(adProductCategoryPo -> Objects.equals(medicalDto.getSecondCategoryCode(), adProductCategoryPo.getPCode())).collect(Collectors.toList());
            Assert.isTrue(filterThirdAdProductCategoryPoList.size() == 1, "第" + cur + "行三级行业不存在");
            medicalDto.setThirdCategoryCode(filterThirdAdProductCategoryPoList.get(0).getCode());

            // SPU验证
            AdProductSpuPo spuPo = spuNameMap.getOrDefault(medicalDto.getSpuName(),
                    AdProductSpuPo.builder().code(0L).thirdCategoryCode(0L).build());
            medicalDto.setSpuCode(spuPo.getCode());
            Assert.isTrue(Utils.isPositive(medicalDto.getSpuCode()), "第" + cur + "行spu不存在");


            AdProductSkuPo skuPo = skuNameMap.getOrDefault(medicalDto.getSkuName(),
                    AdProductSkuPo.builder().code(0L).spuCode(0L).build());
            medicalDto.setSkuCode(skuPo.getCode());
            Assert.isTrue(Utils.isPositive(medicalDto.getSkuCode()), "第" + cur + "行sku不存在");

            // 行业层级关系验证已在上面的过滤逻辑中完成


        }
    }

    @Override
    protected List<ImportAdProductResultDto> convertImportDtos2ResultDtos(List<ImportAdProductMedicalDto> importDtos) {
        return importDtos.stream().map(t -> ImportAdProductResultDto.builder()
                .name(t.getName())
                .lineNum(t.getLineNum())
                .success(t.getSuccess())
                .msg(t.getMsg())
                .build()).collect(Collectors.toList());
    }

    @Override
    protected Map<String, Long> batchInsertProduct(List<ImportAdProductMedicalDto> importDtos, Operator operator) {

        List<Long> adProductIdList = batchGenProductId(importDtos.size());
        for (int i = 0; i < importDtos.size(); i++) {
            importDtos.get(i).setAdProductId(adProductIdList.get(i));
        }
        Map<String, Long> productNameIdMap = adProductRepo.insertImportMedicalV2(importDtos);
        List<Integer> idIntList = productNameIdMap.values().stream().map(Long::intValue).collect(Collectors.toList());
        logOperateService.addBatchInsertLog(DbTable.AD_PRODUCT_TOTAL_INFO, operator, importDtos, idIntList, true);
        return productNameIdMap;
    }
}
