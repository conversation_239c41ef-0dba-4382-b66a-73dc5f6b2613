package com.bilibili.adp.cpc.automatic_rule.bos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExecutionRecordBo {
    private Integer accountId;
    private Long ruleId;
    private String ruleName;
    private Integer executeLogic;
    private List<String> triggerConditions;
    private List<String> conditions;
    private Long actionId;
    private String action;
    private Integer subject;
    private Integer objectType;
    private Integer objectId;
    private LocalDateTime executeTime;
    private Integer executeResult;
    private String executeResultDetail;
}
