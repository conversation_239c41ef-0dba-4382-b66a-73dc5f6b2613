package com.bilibili.adp.cpc.dao.querydsl.pos;

import javax.annotation.Generated;

/**
 * LauGameInfoPo is a Querydsl bean type
 */
@Generated("com.querydsl.codegen.BeanSerializer")
public class LauGameInfoPo {

    private Long avid;

    private Integer bilibiliIndex;

    private Integer bookingNum;

    private Integer commentNum;

    private java.sql.Timestamp ctime;

    private Integer dmpAppId;

    private Integer downloadCount;

    private Integer duration;

    private Integer followerNum;

    private Integer gameBaseId;

    private String gameName;

    private Double grade;

    private String iconUrl;

    private Integer id;

    private Integer isDeleted;

    private java.sql.Timestamp mtime;

    private Integer subPkgStatus;

    private String summary;

    private Integer supportSubPkg;

    private String tags;

    public Long getAvid() {
        return avid;
    }

    public void setAvid(Long avid) {
        this.avid = avid;
    }

    public Integer getBilibiliIndex() {
        return bilibiliIndex;
    }

    public void setBilibiliIndex(Integer bilibiliIndex) {
        this.bilibiliIndex = bilibiliIndex;
    }

    public Integer getBookingNum() {
        return bookingNum;
    }

    public void setBookingNum(Integer bookingNum) {
        this.bookingNum = bookingNum;
    }

    public Integer getCommentNum() {
        return commentNum;
    }

    public void setCommentNum(Integer commentNum) {
        this.commentNum = commentNum;
    }

    public java.sql.Timestamp getCtime() {
        return ctime;
    }

    public void setCtime(java.sql.Timestamp ctime) {
        this.ctime = ctime;
    }

    public Integer getDmpAppId() {
        return dmpAppId;
    }

    public void setDmpAppId(Integer dmpAppId) {
        this.dmpAppId = dmpAppId;
    }

    public Integer getDownloadCount() {
        return downloadCount;
    }

    public void setDownloadCount(Integer downloadCount) {
        this.downloadCount = downloadCount;
    }

    public Integer getDuration() {
        return duration;
    }

    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    public Integer getFollowerNum() {
        return followerNum;
    }

    public void setFollowerNum(Integer followerNum) {
        this.followerNum = followerNum;
    }

    public Integer getGameBaseId() {
        return gameBaseId;
    }

    public void setGameBaseId(Integer gameBaseId) {
        this.gameBaseId = gameBaseId;
    }

    public String getGameName() {
        return gameName;
    }

    public void setGameName(String gameName) {
        this.gameName = gameName;
    }

    public Double getGrade() {
        return grade;
    }

    public void setGrade(Double grade) {
        this.grade = grade;
    }

    public String getIconUrl() {
        return iconUrl;
    }

    public void setIconUrl(String iconUrl) {
        this.iconUrl = iconUrl;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public java.sql.Timestamp getMtime() {
        return mtime;
    }

    public void setMtime(java.sql.Timestamp mtime) {
        this.mtime = mtime;
    }

    public Integer getSubPkgStatus() {
        return subPkgStatus;
    }

    public void setSubPkgStatus(Integer subPkgStatus) {
        this.subPkgStatus = subPkgStatus;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public Integer getSupportSubPkg() {
        return supportSubPkg;
    }

    public void setSupportSubPkg(Integer supportSubPkg) {
        this.supportSubPkg = supportSubPkg;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    @Override
    public String toString() {
         return "avid = " + avid + ", bilibiliIndex = " + bilibiliIndex + ", bookingNum = " + bookingNum + ", commentNum = " + commentNum + ", ctime = " + ctime + ", dmpAppId = " + dmpAppId + ", downloadCount = " + downloadCount + ", duration = " + duration + ", followerNum = " + followerNum + ", gameBaseId = " + gameBaseId + ", gameName = " + gameName + ", grade = " + grade + ", iconUrl = " + iconUrl + ", id = " + id + ", isDeleted = " + isDeleted + ", mtime = " + mtime + ", subPkgStatus = " + subPkgStatus + ", summary = " + summary + ", supportSubPkg = " + supportSubPkg + ", tags = " + tags;
    }

}

