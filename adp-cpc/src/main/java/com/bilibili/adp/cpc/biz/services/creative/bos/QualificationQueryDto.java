package com.bilibili.adp.cpc.biz.services.creative.bos;

import com.bilibili.adp.common.util.Page;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class QualificationQueryDto {

    /**
     * 是否新版
     */
    private Integer isMiddleNewVersion;

    private Integer curAccountId;

    private List<Integer> accountIds;

    private Integer qualificationId;

    private List<Integer> qualificationIds;

    private String likeQualificationName;

    private List<Integer> types;

    private java.sql.Timestamp ctimeFrom;
    private java.sql.Timestamp ctimeTo;

    /**
     * 资质有效时间段
     */
    private java.sql.Timestamp quaValidFrom;
    private java.sql.Timestamp quaValidTo;

    private List<Integer> searchAccountIds;

    /**
     * 共享范围筛选
     */
    private List<Integer> shareScopes;

    private Page page;
}
