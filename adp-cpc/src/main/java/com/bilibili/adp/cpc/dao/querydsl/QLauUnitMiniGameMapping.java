package com.bilibili.adp.cpc.dao.querydsl;

import static com.querydsl.core.types.PathMetadataFactory.*;
import com.bilibili.adp.cpc.dao.querydsl.pos.LauUnitMiniGameMappingPo;


import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;

import com.querydsl.sql.ColumnMetadata;
import java.sql.Types;




/**
 * QLauUnitMiniGameMapping is a Querydsl query type for LauUnitMiniGameMappingPo
 */
@Generated("com.querydsl.sql.codegen.MetaDataSerializer")
public class QLauUnitMiniGameMapping extends com.querydsl.sql.RelationalPathBase<LauUnitMiniGameMappingPo> {

    private static final long serialVersionUID = *********;

    public static final QLauUnitMiniGameMapping lauUnitMiniGameMapping = new QLauUnitMiniGameMapping("lau_unit_mini_game_mapping");

    public final NumberPath<Integer> accountId = createNumber("accountId", Integer.class);

    public final DateTimePath<java.sql.Timestamp> ctime = createDateTime("ctime", java.sql.Timestamp.class);

    public final StringPath gameUrl = createString("gameUrl");

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final NumberPath<Integer> isDeleted = createNumber("isDeleted", Integer.class);

    public final NumberPath<Integer> miniGameId = createNumber("miniGameId", Integer.class);

    public final DateTimePath<java.sql.Timestamp> mtime = createDateTime("mtime", java.sql.Timestamp.class);

    public final NumberPath<Integer> unitId = createNumber("unitId", Integer.class);

    public final com.querydsl.sql.PrimaryKey<LauUnitMiniGameMappingPo> primary = createPrimaryKey(id);

    public QLauUnitMiniGameMapping(String variable) {
        super(LauUnitMiniGameMappingPo.class, forVariable(variable), "null", "lau_unit_mini_game_mapping");
        addMetadata();
    }

    public QLauUnitMiniGameMapping(String variable, String schema, String table) {
        super(LauUnitMiniGameMappingPo.class, forVariable(variable), schema, table);
        addMetadata();
    }

    public QLauUnitMiniGameMapping(String variable, String schema) {
        super(LauUnitMiniGameMappingPo.class, forVariable(variable), schema, "lau_unit_mini_game_mapping");
        addMetadata();
    }

    public QLauUnitMiniGameMapping(Path<? extends LauUnitMiniGameMappingPo> path) {
        super(path.getType(), path.getMetadata(), "null", "lau_unit_mini_game_mapping");
        addMetadata();
    }

    public QLauUnitMiniGameMapping(PathMetadata metadata) {
        super(LauUnitMiniGameMappingPo.class, metadata, "null", "lau_unit_mini_game_mapping");
        addMetadata();
    }

    public void addMetadata() {
        addMetadata(accountId, ColumnMetadata.named("account_id").withIndex(5).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(ctime, ColumnMetadata.named("ctime").withIndex(7).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(gameUrl, ColumnMetadata.named("game_url").withIndex(3).ofType(Types.VARCHAR).withSize(2048).notNull());
        addMetadata(id, ColumnMetadata.named("id").withIndex(1).ofType(Types.BIGINT).withSize(19).notNull());
        addMetadata(isDeleted, ColumnMetadata.named("is_deleted").withIndex(6).ofType(Types.TINYINT).withSize(3).notNull());
        addMetadata(miniGameId, ColumnMetadata.named("mini_game_id").withIndex(4).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(mtime, ColumnMetadata.named("mtime").withIndex(8).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(unitId, ColumnMetadata.named("unit_id").withIndex(2).ofType(Types.INTEGER).withSize(10).notNull());
    }

}

