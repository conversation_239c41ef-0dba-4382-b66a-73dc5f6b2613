package com.bilibili.adp.cpc.biz.services.creative.bos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LauCreativeButtonCopyBo {

    private Integer buttonCopyId;

    private Integer buttonType;

    private Integer creativeId;

    private String customizedUrl;

    private String extendUrl;

    private Integer id;

    private Integer isDeleted;

    private String jumpUrl;

    private Integer unitId;
}

