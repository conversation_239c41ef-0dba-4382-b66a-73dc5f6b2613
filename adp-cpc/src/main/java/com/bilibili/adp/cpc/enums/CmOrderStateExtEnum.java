package com.bilibili.adp.cpc.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.modelmapper.internal.util.Assert;

import java.util.Arrays;
import java.util.Objects;

/**
 * @ClassName CmOrderStateExtEnum
 * <AUTHOR>
 * @Date 2022/2/10 9:09 下午
 * @Version 1.0
 **/
@AllArgsConstructor
@Getter
public enum CmOrderStateExtEnum {
    waitConfirmUpAndMcn(0,"授权待MCN-UP主确认"),
    waitConfirmUp(1,"授权待UP主确认"),
    waitConfirmMcn(2,"授权待MCN确认"),
    confirm(3,"授权已接受"),
    confirmAndActive(4,"授权已生效"),
    confirmAndTimeOut(5,"授权已过期"),
    rejectByMcn(6,"MCN已拒绝授权"),
    rejectByUp(7,"UP主已拒绝授权"),
    cancel(8,"授权已撤回"),
    invalid(9,"授权已失效"),

    error(-1,"状态异常")
    ;
    public final Integer code;
    public final String desc;

    CmOrderStateExtEnum(int code, String desc) {
        this.code = code;
        this.desc=desc;
    }

    public static CmOrderStateExtEnum getByCode(Integer code) {
        Assert.isTrue(Objects.nonNull(code), "CmOrderStateExtEnum code null");

        for (CmOrderStateExtEnum bean : values()) {
            if (bean.getCode().equals(code)) {
                return bean;
            }
        }
        throw new IllegalArgumentException("CmOrderStateExtEnum code does not exist");
    }
}
