package com.bilibili.adp.cpc.biz.converter.audit;

import com.bapis.ad.audit.AuditInfo;
import com.bapis.ad.audit.AuditStatus;
import com.bapis.ad.audit.MiscElemAuditInfo;
import com.bapis.ad.audit.QueryNativeArchivesReq;
import com.bapis.ad.audit.SingleQueryCreativeBodyListRes;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.enums.SystemType;
import com.bilibili.adp.cpc.biz.services.archive.bos.CreativeBodyAuditBo;
import com.bilibili.adp.cpc.dao.querydsl.pos.LauCreativeBodyAuditPo;
import com.bilibili.adp.cpc.dto.LauNativeArchiveQueryDto;
import com.bilibili.adp.launch.api.creative.dto.CpcCreativeAuditDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.factory.Mappers;

import java.sql.Timestamp;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Mapper(nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface AuditConverter {
    AuditConverter MAPPER = Mappers.getMapper(AuditConverter.class);
    /**
     * grpc operator to effect ad operator
     * @param operator grpc operator
     * @return effect ad  operator
     */
    @Mapping(target = "operatorType", expression = "java(com.bilibili.adp.common.enums.OperatorType.getByCode(operator.getOperatorTypeValue()))")
    @Mapping(target = "systemType", expression = "java(com.bilibili.adp.common.enums.SystemType.getByCode(operator.getSystemTypeValue()))")
    Operator grpcOperator2Operator(com.bapis.ad.audit.Operator operator);

    @Mapping(target = "status", expression = "java(com.bilibili.adp.cpc.core.constants.ProgrammaticMiscElemAuditStatus.fromAuditStatus(auditStatus))")
    MiscElemAuditInfo auditDto2MiscElemAuditInfo(CpcCreativeAuditDto creativeAuditDto, int auditStatus, int accountId);

    @Mapping(target = "operatorType", source = "operator.operatorType.code")
    @Mapping(target = "systemType", expression = "java(com.bilibili.adp.cpc.biz.converter.audit.AuditConverter.systemType(com.bilibili.adp.common.enums.SystemType.CPM.getCode()))")
    com.bapis.ad.audit.Operator operator2GrpcOperator(Operator operator);

    @Mapping(target = "rejectReason", source = "reason")
    AuditInfo creativeAuditDto2Info(CpcCreativeAuditDto creativeAuditDto);

    @Mapping(target = "submitTimeFrom", expression = "java(com.bilibili.adp.cpc.biz.converter.audit.AuditConverter.toTimestamp(archivesReq.getSubmitTimeFrom()))")
    @Mapping(target = "submitTimeTo", expression = "java(com.bilibili.adp.cpc.biz.converter.audit.AuditConverter.toTimestamp(archivesReq.getSubmitTimeTo()))")
    @Mapping(target = "mtimeFrom", expression = "java(com.bilibili.adp.cpc.biz.converter.audit.AuditConverter.toTimestamp(archivesReq.getMtimeFrom()))")
    @Mapping(target = "mtimeTo", expression = "java(com.bilibili.adp.cpc.biz.converter.audit.AuditConverter.toTimestamp(archivesReq.getMtimeTo()))")
    @Mapping(target = "auditStatus", expression = "java(archivesReq.getAuditStatusValueList())")
    @Mapping(target = "type", source = "nativeBodyType")
    LauNativeArchiveQueryDto toQueryDto(QueryNativeArchivesReq archivesReq);

    static com.bapis.ad.audit.OperatorType operatorType(Integer num) {
        return com.bapis.ad.audit.OperatorType.forNumber(num);
    }

    static com.bapis.ad.audit.SystemType systemType(Integer num) {
        return com.bapis.ad.audit.SystemType.forNumber(num);
    }



    public static Timestamp toTimestamp(Long timeLong) {
        if (Objects.isNull(timeLong) || timeLong == 0L){
            return null;
        }
        return new Timestamp(timeLong);
    }
    List<SingleQueryCreativeBodyListRes> creativeBodyAuditBos2Grpc(List<CreativeBodyAuditBo> creativeBodyAuditBos);

    List<CreativeBodyAuditBo> creativeBodyAuditPos2Bos(List<LauCreativeBodyAuditPo> creativeBodyAuditPos);
    @Mapping(target = "ctime", expression = "java(creativeBodyAuditPo.getCtime().getTime())")
    @Mapping(target = "mtime", expression = "java(creativeBodyAuditPo.getMtime().getTime())")
    CreativeBodyAuditBo creativeBodyAuditPos2Bo(LauCreativeBodyAuditPo creativeBodyAuditPo);

    LauCreativeBodyAuditPo creativeBodyAuditBo2Po(CreativeBodyAuditBo bo);
}
