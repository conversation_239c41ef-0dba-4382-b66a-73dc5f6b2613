package com.bilibili.adp.cpc.dao.querydsl.pos;

import javax.annotation.Generated;

/**
 * AdvertiserAvidMappingPo is a Querydsl bean type
 */
@Generated("com.querydsl.codegen.BeanSerializer")
public class AdvertiserAvidMappingPo {

    private Integer accountId;

    private Long avId;

    private java.sql.Timestamp ctime;

    private Integer id;

    private Integer isDeleted;

    private java.sql.Timestamp mtime;

    private String operator;

    public Integer getAccountId() {
        return accountId;
    }

    public void setAccountId(Integer accountId) {
        this.accountId = accountId;
    }

    public Long getAvId() {
        return avId;
    }

    public void setAvId(Long avId) {
        this.avId = avId;
    }

    public java.sql.Timestamp getCtime() {
        return ctime;
    }

    public void setCtime(java.sql.Timestamp ctime) {
        this.ctime = ctime;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public java.sql.Timestamp getMtime() {
        return mtime;
    }

    public void setMtime(java.sql.Timestamp mtime) {
        this.mtime = mtime;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    @Override
    public String toString() {
         return "accountId = " + accountId + ", avId = " + avId + ", ctime = " + ctime + ", id = " + id + ", isDeleted = " + isDeleted + ", mtime = " + mtime + ", operator = " + operator;
    }

}

