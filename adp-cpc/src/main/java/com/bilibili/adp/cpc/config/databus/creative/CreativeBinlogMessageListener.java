package com.bilibili.adp.cpc.config.databus.creative;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bilibili.adp.account.dto.AccountBaseDto;
import com.bilibili.adp.account.service.IQueryAccountService;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.bos.WarpDatabusProperty;
import com.bilibili.adp.cpc.biz.constants.Constants;
import com.bilibili.adp.cpc.biz.services.creative.AdpCpcCreativePubInfoService;
import com.bilibili.adp.cpc.biz.services.creative.CreativeBinlogDealService;
import com.bilibili.adp.cpc.biz.services.creative.api.ICpcCreativeService;
import com.bilibili.adp.cpc.biz.services.creative.bos.CreativeBinlogBo;
import com.bilibili.adp.cpc.biz.services.creative.business.content.BusinessContentService;
import com.bilibili.adp.cpc.core.LaunchCreativeExtraService;
import com.bilibili.adp.cpc.core.constants.IsDeleted;
import com.bilibili.adp.cpc.core.constants.IsManaged;
import com.bilibili.adp.cpc.core.constants.LaunchStatus;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitCreativePo;
import com.bilibili.adp.cpc.enums.AdpVersion;
import com.bilibili.adp.cpc.enums.AuditStatus;
import com.bilibili.adp.cpc.enums.UserType;
import com.bilibili.adp.cpc.enums.ad.CreativeCountEnum;
import com.bilibili.adp.launch.api.common.enums.FlagEnums;
import com.bilibili.adp.util.common.AdpCatUtils;
import com.bilibili.warp.databus.AckableMessage;
import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.databus.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <AUTHOR>
 * @Date 2023/6/16 11:38 上午
 * @Version 1.0
 **/
@Component
@Slf4j
public class CreativeBinlogMessageListener implements MessageListener {

    public static final String CREATIVE_BINLOG = "creative-binlog";
    private final String topic;
    private final String group;

    @Value("${databus.creative.binlog.enabled:true}")
    private Boolean enabled;

    @Resource
    private CreativeBinlogDealService creativeBinlogDealService;
    @Resource
    private BusinessContentService businessContentService;
    @Autowired
    private ICpcCreativeService cpcCreativeService;
    @Autowired
    private LaunchCreativeExtraService launchCreativeExtraService;
    @Autowired
    private IQueryAccountService accountService;

    @Value("${creative.binlog.need.test.accountId:10010}")
    private Integer creativeBinlogNeedTestAccountId;

    private static final String TEST_MOCK_TOPIC = "testTopic";
    private static final String TEST_MOCK_GROUP = "testGroup";

    @Resource
    private AdpCpcCreativePubInfoService cpcCreativePubInfoService;

    private final static ThreadLocal<GameCreativeInfoContext> context = new ThreadLocal<>();

    @Autowired
    private GameCreativeInfoContextService gameCreativeInfoContextService;

    private static final String ACTION = "action";
    private static final String INSERT = "insert";
    private static final String UPDATE = "update";
    private static final String DELETE = "delete";
    private static final String NEW = "new";
    private static final String OLD = "old";

    public CreativeBinlogMessageListener(WarpDatabusProperty warpDatabusProperty) {
        // 注入DatabusProperties，此处的ALIAS常量即配置文件中填写的别名，是topic的上一级配置键
        // pub场景同理，在使用DatabusTemplate的地方注入DatabusProperties来获取
        Map<String, DatabusProperty> properties = warpDatabusProperty.getProperties();
        DatabusProperty property = properties.get(CREATIVE_BINLOG);
        log.info("CreativeBinlogMessageListener, property={}", JSONObject.toJSONString(property));
        this.topic = Objects.isNull(property) ? TEST_MOCK_TOPIC : property.getTopic();
        this.group = Objects.isNull(property) ? TEST_MOCK_GROUP : property.getSub().getGroup();
    }

    private void handleMsg (String value) {
        log.info("handleCreativeBinlogMsg, value={}", value);
        JSONObject jsonObject = JSONObject.parseObject(value);
        String action = jsonObject.getString(ACTION);
        if (StringUtils.isEmpty(action)) {
            return;
        }
        // mapi高优
        handleAdCount(jsonObject, action);
        handleActionMsg(jsonObject, action);
        handleBusinessContent(jsonObject, action);
        handleCreative2Game(jsonObject, action);
    }

    private void handleCreative2Game(JSONObject jsonObject, String action) {
        if (INSERT.equals(action)) {
            return;
        }
        JSONObject newObject = jsonObject.getJSONObject(NEW);
        LauUnitCreativePo newPo = deserializeCreativePo(newObject);
        if (Objects.isNull(newPo)) {
            return;
        }

        if (Objects.equals(IsManaged.EXPLORE, newPo.getIsManaged())
                && !Utils.isPositive(newPo.getParentCreativeId())) {
            return;
        }

        if(com.bilibili.adp.common.enums.AuditStatus.INIT.getCode() == newPo.getAuditStatus()) {
            // 待审核的不推送了
            return ;
        }
        // 筛选
        List<LauUnitCreativePo> creativePos = cpcCreativePubInfoService.filterCreativePo(Collections.singletonList(newPo), new ArrayList<>(cpcCreativePubInfoService.getGamePPT()));
        if (!CollectionUtils.isEmpty(creativePos)) {
            cpcCreativePubInfoService.creativePub(creativePos, null, null);
        }
    }

    private void handleAdCount(JSONObject jsonObject, String action) {
        JSONObject oldObject = jsonObject.getJSONObject(OLD);
        CreativeBinlogBo oldBo = deserializeCreativeBinlogDto(oldObject);
        JSONObject newObject = jsonObject.getJSONObject(NEW);
        CreativeBinlogBo newBo = deserializeCreativeBinlogDto(newObject);
        LocalDate today = LocalDate.now();
        if (Objects.isNull(newBo)
                || !Utils.isPositive(newBo.getAccountId())
                || StringUtils.isEmpty(newBo.getCtime())) {
            return;
        }

        if (Objects.equals(action, INSERT)) {
            dealIncrCreativeNum(newBo, today, 1L);
        } else if (Objects.equals(action, UPDATE) && Objects.nonNull(oldBo)) {
            // 检查状态是否发生改变 没改变的话不处理
            boolean newBoIsDeleted = Objects.equals(newBo.getStatus(), LaunchStatus.DELETED)
                    || Objects.equals(newBo.getIsDeleted(), IsDeleted.DELETED);
            boolean oldBoIsDeleted = Objects.equals(oldBo.getStatus(), LaunchStatus.DELETED)
                    || Objects.equals(oldBo.getIsDeleted(), IsDeleted.DELETED);

            if (newBoIsDeleted == oldBoIsDeleted) {
                return;
            }

            // 删除创意的情况
            if (newBoIsDeleted) {
                dealIncrCreativeNum(newBo, today, -1L);
            }

            // 从已删除创意变成未删除 恢复创意的情况
            if (oldBoIsDeleted) {
                dealIncrCreativeNum(newBo, today, 1L);
            }
        } else if (Objects.equals(action, DELETE)) {
            boolean newBoIsDeleted = Objects.equals(newBo.getStatus(), LaunchStatus.DELETED)
                    || Objects.equals(newBo.getIsDeleted(), IsDeleted.DELETED);
            if (newBoIsDeleted) {
                return;
            }
            dealIncrCreativeNum(newBo, today, -1L);
        }
    }

    private void dealIncrCreativeNum(CreativeBinlogBo bo, LocalDate today, Long num) {
        if (Objects.isNull(bo) || Objects.isNull(today)) {
            return;
        }

        boolean isSanlian = getNeedSanlianHandle(bo);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        boolean isTodayCreate = LocalDateTime.parse(bo.getCtime(), formatter).toLocalDate().isEqual(today);

        if (isSanlian) {
            cpcCreativeService.incrCreativeNumByAccountId(bo.getAccountId(), num, CreativeCountEnum.SANLIAN.getCode());
            if (isTodayCreate) {
                cpcCreativeService.incrTodayCreativeNumByAccountId(bo.getAccountId(), today.toString(), num, CreativeCountEnum.SANLIAN.getCode());
            }
        }
    }

    private boolean getNeedSanlianHandle(CreativeBinlogBo bo) {
        if (Objects.isNull(bo)) {
            return false;
        }

        Integer accountId = bo.getAccountId();

        AccountBaseDto accountBaseDto = accountService.getAccountBaseDtoById(accountId);
        // 个人起飞 过滤
        if (Objects.isNull(accountBaseDto) || Objects.equals(UserType.PERSONAL_FLY.getCode(), accountBaseDto.getUserType())) {
            return false;
        }

        // 非个人起飞老三连 都需要
        if (AdpVersion.isMiddle(bo.getAdpVersion())) {
            return true;
        }

        // 非个人起飞新三连 只需要排除自动投放
        if (AdpVersion.isCpcFlyMerge(bo.getAdpVersion())) {
            Integer autoCreativeParentId = launchCreativeExtraService.getAutoCreativeParentIdByCreativeId(bo.getCreativeId());
            return !Utils.isPositive(autoCreativeParentId);
        }

        return false;
    }


    private void handleBusinessContent(JSONObject jsonObject, String action) {
        JSONObject oldObject = jsonObject.getJSONObject(OLD);
        CreativeBinlogBo oldBo = deserializeCreativeBinlogDto(oldObject);
        JSONObject newObject = jsonObject.getJSONObject(NEW);
        CreativeBinlogBo newBo = deserializeCreativeBinlogDto(newObject);

        if (Objects.nonNull(newBo)
                && Objects.equals(IsManaged.EXPLORE, newBo.getIsManaged())
                && !Utils.isPositive(newBo.getParentCreativeId())) {
            return;
        }

        if ((null == oldBo && AuditStatus.ACCEPTED.getCode().equals(newBo.getAuditStatus()))
                || (null != oldBo && !AuditStatus.ACCEPTED.getCode().equals(oldBo.getAuditStatus()) && AuditStatus.ACCEPTED.getCode().equals(newBo.getAuditStatus()))) {
            //新三连创意走Pandora
            if (!AdpVersion.CPC_FLY_MERGE.getKey().equals(newBo.getAdpVersion())) {
                LauUnitCreativePo po = new LauUnitCreativePo();
                BeanUtils.copyProperties(newBo, po);
                businessContentService.pub(po);
            }
        }
    }

    private void handleActionMsg(JSONObject jsonObject, String action) {
        if (!INSERT.equals(action)) {
            return;
        }
        JSONObject actionObject = jsonObject.getJSONObject(NEW);
        CreativeBinlogBo creativeBinlogBo = deserializeCreativeBinlogDto(actionObject);
        if (Objects.isNull(creativeBinlogBo)) {
            log.error("handleActionMsg creativeBinlogBo does not exist, jsonObject:{}", jsonObject);
            return;
        }

        if (Utils.isPositive(creativeBinlogNeedTestAccountId)
                && !Objects.equals(creativeBinlogBo.getAccountId(), creativeBinlogNeedTestAccountId)) {
            return;
        }

        if (Objects.equals(IsManaged.EXPLORE, creativeBinlogBo.getIsManaged())
                && !Utils.isPositive(creativeBinlogBo.getParentCreativeId())) {
            return;
        }

        // 目前仅处理新增创意
        creativeBinlogDealService.dealCreativeBinlog(creativeBinlogBo);
    }

    private CreativeBinlogBo deserializeCreativeBinlogDto(JSONObject insertObject) {
        if (Objects.isNull(insertObject)) {
            return null;
        }
        CreativeBinlogBo creativeBinlogBo = null;
        try {
            creativeBinlogBo = insertObject.toJavaObject(CreativeBinlogBo.class);
        } catch (Exception e) {
            log.error("deserializeCreativeBinlogDto error, insertObject:{}", insertObject, e);
        }
        return creativeBinlogBo;
    }

    private LauUnitCreativePo deserializeCreativePo(JSONObject insertObject) {
        if (Objects.isNull(insertObject)) {
            return null;
        }
        LauUnitCreativePo creativePo = null;
        try {
            creativePo = insertObject.toJavaObject(LauUnitCreativePo.class);
        } catch (Exception e) {
            log.error("deserializeCreativeBinlogDto error, insertObject:{}", insertObject, e);
        }
        return creativePo;
    }

    @Override
    public String topic() {
        return this.topic;
    }

    @Override
    public String group() {
        return this.group;
    }

    @Override
    public void onMessage(AckableMessage ackableMessage) {
        log.info("wrap onMessage, creativeBinlog ackableMessage={}", JSON.toJSONString(ackableMessage));
        if (!Boolean.TRUE.equals(enabled)) {
            return;
        }

        try {
            AdpCatUtils.newTransaction(Constants.CAT_TYPE_DATABUS, CREATIVE_BINLOG + ":sub", transaction -> {
                String value = new String(ackableMessage.payload());
                handleMsg(value);
            });
        } catch (Exception e) {
            log.error("CreativeBinlogSubTask error", e);
        }

    }

    @Override
    public boolean autoCommit() {
        return true;
    }
}
