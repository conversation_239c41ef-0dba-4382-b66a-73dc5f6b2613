package com.bilibili.adp.cpc.biz.services.creative;

import com.bilibili.adp.cpc.biz.services.creative.config.AccLabelConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.List;

@Slf4j
@Service
public class ShowMonitorUrlCheckService {

    @Autowired
    private AccLabelConfig accLabelConfig;

    public void check(List<Integer> accountLabelIds, String customizedImpUrl){
        final boolean isShowMonitorUrlCheck = accountLabelIds.contains(accLabelConfig.getShowMonitorUrlCheckLabelId());
        if(isShowMonitorUrlCheck){
            Assert.isTrue(StringUtils.isNotBlank(customizedImpUrl),"点击监控链接不能为空");
        }
    }
}
