package com.bilibili.adp.cpc.biz.services.profile;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.constants.Constants;
import com.bilibili.adp.cpc.biz.services.AdpCpcLauUserBehaviorService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.text.MessageFormat;

@Service
@RequiredArgsConstructor
public class AdpCpcProfileService {
    private final AdpCpcLauUserBehaviorService adpCpcLauUserBehaviorService;
    private final ObjectMapper objectMapper;

    public MaterialReplaceProfileBo getMaterialReplaceProfile(Integer accountId, Integer type) {
        Assert.isTrue(Utils.isPositive(accountId), "账号 id 不能为空");
        Assert.isTrue(type == Constants.MATERIAL_REPLACE_ATTRIBUTES || type == Constants.DYNAMIC_MATERIAL_REPLACE_ATTRIBUTES, "type 参数不合法");

        final String rawConfig = adpCpcLauUserBehaviorService.getGeneralConfig(accountId, type, 1);
        if (!StringUtils.hasText(rawConfig)) {
            return MaterialReplaceProfileBo.DEFAULT;
        }

        try {
            return objectMapper.readValue(rawConfig, MaterialReplaceProfileBo.class);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(MessageFormat.format("账号 id 对应的配置数据解析失败, account_id={0}", accountId));
        }
    }

    @SneakyThrows
    public void saveMaterialReplaceProfile(Integer accountId, MaterialReplaceProfileBo materialReplaceProfileBo) {
        Assert.isTrue(Utils.isPositive(accountId), "账号 id 不能为空");
        Assert.notNull(materialReplaceProfileBo, "配置数据不能为空");
        int type = materialReplaceProfileBo.getType();
        Assert.isTrue(type == Constants.MATERIAL_REPLACE_ATTRIBUTES || type == Constants.DYNAMIC_MATERIAL_REPLACE_ATTRIBUTES, "type 参数不合法");

        final String rawConfig = objectMapper.writeValueAsString(materialReplaceProfileBo);
        adpCpcLauUserBehaviorService.saveUserGeneralConfig(accountId, type, 1, rawConfig);
    }
}
