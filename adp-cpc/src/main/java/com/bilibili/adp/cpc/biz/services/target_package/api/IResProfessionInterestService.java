package com.bilibili.adp.cpc.biz.services.target_package.api;

import com.bilibili.adp.cpc.po.ad.ResProfessionInterestCrowdsPo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/2/23
 * @description 行业兴趣人群（新）持久层接口类
 */
public interface IResProfessionInterestService {

    List<ResProfessionInterestCrowdsPo> allValidProfessionInterest();

    Integer batchInsertProfessionInterestPo(List<ResProfessionInterestCrowdsPo> pos);
}
