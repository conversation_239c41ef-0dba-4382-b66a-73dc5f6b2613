package com.bilibili.adp.cpc.dao.querydsl.pos;

import javax.annotation.Generated;

/**
 * PopupNotificationConfigPo is a Querydsl bean type
 */
@Generated("com.querydsl.codegen.BeanSerializer")
public class PopupNotificationConfigPo {

    private Integer button1;

    private Integer button2;

    private String buttonLearnMoreUrl;

    private String buttonTryNowUrl;

    private String content;

    private java.sql.Timestamp ctime;

    private java.sql.Timestamp effectiveEndTime;

    private java.sql.Timestamp effectiveStartTime;

    private Integer id;

    private String imageRedirectUrl;

    private String imageUrl;

    private Integer isDeleted;

    private Integer isPublicToAllUsers;

    private Integer isVisible;

    private String location;

    private java.sql.Timestamp mtime;

    private Integer templateId;

    private Integer templateType;

    private String title;

    public Integer getButton1() {
        return button1;
    }

    public void setButton1(Integer button1) {
        this.button1 = button1;
    }

    public Integer getButton2() {
        return button2;
    }

    public void setButton2(Integer button2) {
        this.button2 = button2;
    }

    public String getButtonLearnMoreUrl() {
        return buttonLearnMoreUrl;
    }

    public void setButtonLearnMoreUrl(String buttonLearnMoreUrl) {
        this.buttonLearnMoreUrl = buttonLearnMoreUrl;
    }

    public String getButtonTryNowUrl() {
        return buttonTryNowUrl;
    }

    public void setButtonTryNowUrl(String buttonTryNowUrl) {
        this.buttonTryNowUrl = buttonTryNowUrl;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public java.sql.Timestamp getCtime() {
        return ctime;
    }

    public void setCtime(java.sql.Timestamp ctime) {
        this.ctime = ctime;
    }

    public java.sql.Timestamp getEffectiveEndTime() {
        return effectiveEndTime;
    }

    public void setEffectiveEndTime(java.sql.Timestamp effectiveEndTime) {
        this.effectiveEndTime = effectiveEndTime;
    }

    public java.sql.Timestamp getEffectiveStartTime() {
        return effectiveStartTime;
    }

    public void setEffectiveStartTime(java.sql.Timestamp effectiveStartTime) {
        this.effectiveStartTime = effectiveStartTime;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getImageRedirectUrl() {
        return imageRedirectUrl;
    }

    public void setImageRedirectUrl(String imageRedirectUrl) {
        this.imageRedirectUrl = imageRedirectUrl;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Integer getIsPublicToAllUsers() {
        return isPublicToAllUsers;
    }

    public void setIsPublicToAllUsers(Integer isPublicToAllUsers) {
        this.isPublicToAllUsers = isPublicToAllUsers;
    }

    public Integer getIsVisible() {
        return isVisible;
    }

    public void setIsVisible(Integer isVisible) {
        this.isVisible = isVisible;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public java.sql.Timestamp getMtime() {
        return mtime;
    }

    public void setMtime(java.sql.Timestamp mtime) {
        this.mtime = mtime;
    }

    public Integer getTemplateId() {
        return templateId;
    }

    public void setTemplateId(Integer templateId) {
        this.templateId = templateId;
    }

    public Integer getTemplateType() {
        return templateType;
    }

    public void setTemplateType(Integer templateType) {
        this.templateType = templateType;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    @Override
    public String toString() {
         return "button1 = " + button1 + ", button2 = " + button2 + ", buttonLearnMoreUrl = " + buttonLearnMoreUrl + ", buttonTryNowUrl = " + buttonTryNowUrl + ", content = " + content + ", ctime = " + ctime + ", effectiveEndTime = " + effectiveEndTime + ", effectiveStartTime = " + effectiveStartTime + ", id = " + id + ", imageRedirectUrl = " + imageRedirectUrl + ", imageUrl = " + imageUrl + ", isDeleted = " + isDeleted + ", isPublicToAllUsers = " + isPublicToAllUsers + ", isVisible = " + isVisible + ", location = " + location + ", mtime = " + mtime + ", templateId = " + templateId + ", templateType = " + templateType + ", title = " + title;
    }

}

