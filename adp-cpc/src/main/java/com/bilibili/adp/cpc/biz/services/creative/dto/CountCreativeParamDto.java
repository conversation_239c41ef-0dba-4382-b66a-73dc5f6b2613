package com.bilibili.adp.cpc.biz.services.creative.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName CountCreativeParamDto
 * <AUTHOR>
 * @Date 2024/6/5 5:15 下午
 * @Version 1.0
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CountCreativeParamDto {

    private Integer accountId;
    private Integer creativeStatus;
    private List<Integer> unitIds;
    private List<Integer> statusList;
    private List<Integer> auditStatusList;
    private List<Integer> adpVersionList;

}
