package com.bilibili.adp.cpc.biz.services.creative;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.exception.ServiceRuntimeException;
import com.bilibili.adp.cpc.dao.ad_core.mybatis.LauCreativeTemplateDao;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitPo;
import com.bilibili.adp.cpc.enums.PromotionPurposeType;
import com.bilibili.adp.cpc.po.ad.LauCreativeTemplatePo;
import com.bilibili.adp.cpc.po.ad.LauCreativeTemplatePoExample;
import com.bilibili.adp.cpc.repo.OuterLauUnitRepo;
import com.bilibili.mas.common.utils.MasCatUtils;
import com.bilibili.sycpb.acc.api.dict.common.YesNoEnum;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 创意模板刷数
 * 需求：https://www.tapd.bilibili.co/67874887/prong/stories/view/1167874887002937689
 *
 * <AUTHOR>
 * @date 2023/2/22 19:53
 */
@Slf4j
@Component
public class CreativeTemplateRefresh {

    @Autowired
    private LauCreativeTemplateDao lauCreativeTemplateDao;
    @Autowired
    private OuterLauUnitRepo outerLauUnitRepo;

    /**
     * 刷新单元下的创意模板 ids(软删)
     * 注: 生产刷24w数据
     * @param optType 操作类型 1: 检查数据 2: 刷数据
     */
    public String refreshCreativeTemplateByUnitIds(Integer optType, Long startId, List<Integer> unitIds) {
        log.info("refreshCreativeTemplateByUnitIds, optType:{}, startId:{}, unitId size:{}", optType, startId, unitIds.size());
        if (CollectionUtils.isEmpty(unitIds)) {
            return "单元id不能为空";
        }
        Assert.notNull(optType, "optType 不能为空");

        RefreshCountInfo refreshCountInfo = MasCatUtils.newTransactionAndReturn("refreshCreativeTemplateByUnitIds",
                "refreshCreativeTemplateByUnitIds", transaction -> {
                    // 分批处理
                    List<List<Integer>> unitIdsPartitions = Lists.partition(unitIds, 1000);
                    RefreshCountInfo totalRefreshCountInfo = RefreshCountInfo.create();
                    for (List<Integer> unitIdsPartition : unitIdsPartitions) {
                        RefreshCountInfo tmpCountInfo = doRefreshCreativeTemplateByUnitIds(optType, startId, unitIdsPartition);
                        totalRefreshCountInfo.setRealRefreshCount(totalRefreshCountInfo.getRealRefreshCount() + tmpCountInfo.getRealRefreshCount());
                        totalRefreshCountInfo.setNeedRefreshCount(totalRefreshCountInfo.getNeedRefreshCount() + tmpCountInfo.getNeedRefreshCount());
                    }
                    return totalRefreshCountInfo;
                }
        );
        log.info("refreshCreativeTemplateByUnitIds1, needCount:{}, realRefreshCount",
                refreshCountInfo.getNeedRefreshCount(), refreshCountInfo.getRealRefreshCount());
        return JSON.toJSONString(refreshCountInfo);
    }

    private RefreshCountInfo doRefreshCreativeTemplateByUnitIds(Integer optType, Long startId, List<Integer> unitIds) {
        StopWatch sw = new StopWatch();
        sw.start("task1");

        Long lastMaxId = 0L;
        if (startId != null) {
            lastMaxId = startId;
        }

        RefreshCountInfo refreshCountInfo = RefreshCountInfo.create();
        Integer realTotalDeleteCount = 0;
        Integer needTotalDeleteCount = 0;
        while (true) {
            // 1.获取单元下模板id为296,298 的创意模板列表
            LauCreativeTemplatePoExample example = new LauCreativeTemplatePoExample();
            LauCreativeTemplatePoExample.Criteria criteria = example.createCriteria().andIsDeletedEqualTo(YesNoEnum.NO.getCode()).andUnitIdIn(unitIds).andTemplateIdIn(Arrays.asList(296, 298))
                    .andIdGreaterThan(lastMaxId);
            example.setOrderByClause("id");
            example.setLimit(1000);
            List<LauCreativeTemplatePo> poList = lauCreativeTemplateDao.selectByExample(example);
            log.info("doRefreshCreativeTemplateByUnitIds 获取一批需要刷的数据 size:{}, startId:{}", poList.size(), lastMaxId);
            if (CollectionUtils.isEmpty(poList)) {
                log.info("doRefreshCreativeTemplateByUnitIds 没有数据需要刷了====");
                break;
            }
            lastMaxId = poList.get(poList.size() - 1).getId();
            List<Long> ids = poList.stream().map(t -> t.getId()).collect(Collectors.toList());
            List<Integer> tmpUnitIds = poList.stream().map(t -> t.getUnitId()).distinct().collect(Collectors.toList());

            // 2. 检查是否是安卓游戏目标下的单元
            List<LauUnitPo> lauUnitPos = outerLauUnitRepo.queryAdp6UnitListByRange(tmpUnitIds);
            List<Integer> notGamePromotionUnitIds = lauUnitPos.stream().filter(lauUnitPo -> !PromotionPurposeType.GAME.getCode().equals(lauUnitPo.getPromotionPurposeType()))
                    .map(lauUnitPo -> lauUnitPo.getUnitId()).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(notGamePromotionUnitIds)) {
                String notGamePromoUnitIdStr = notGamePromotionUnitIds.stream().map(unitId -> unitId + "").collect(Collectors.joining(","));
                log.error("doRefreshCreativeTemplateByUnitIds, 存在单元非游戏推广目的,unitIds:{}", notGamePromoUnitIdStr);
                throw new ServiceRuntimeException("存在单元非游戏推广目的,unitIds:" + notGamePromoUnitIdStr);
            }
            needTotalDeleteCount += poList.size();

            // 3. 软删这些创意模板
            if (optType.equals(2)) {
                LauCreativeTemplatePo record = new LauCreativeTemplatePo();
                record.setIsDeleted(YesNoEnum.YES.getCode());
                LauCreativeTemplatePoExample exampleUpdate = new LauCreativeTemplatePoExample();
                exampleUpdate.createCriteria().andIsDeletedEqualTo(YesNoEnum.NO.getCode()).andIdIn(ids).andUnitIdIn(unitIds).andTemplateIdIn(Arrays.asList(296, 298));
                int deletedCount = lauCreativeTemplateDao.updateByExampleSelective(record, exampleUpdate);
                log.info("doRefreshCreativeTemplateByUnitIds 需要刷size:{},实际刷size:{},startId:{}", poList.size(), deletedCount, lastMaxId);
                realTotalDeleteCount += deletedCount;
            }
        }
        sw.stop();

        String format = String.format("doRefreshCreativeTemplateByUnitIds 需要刷%d,实际刷%d,cost:%s",
                needTotalDeleteCount, realTotalDeleteCount, sw.getLastTaskTimeMillis());
        log.info(format);
        refreshCountInfo.setRealRefreshCount(realTotalDeleteCount);
        refreshCountInfo.setNeedRefreshCount(needTotalDeleteCount);
        return refreshCountInfo;
    }


}

@Data
class RefreshCountInfo {
    private Integer needRefreshCount;
    private Integer realRefreshCount;

    public static RefreshCountInfo create() {
        RefreshCountInfo countInfo = new RefreshCountInfo();
        countInfo.setNeedRefreshCount(0);
        countInfo.setRealRefreshCount(0);
        return countInfo;
    }
}
