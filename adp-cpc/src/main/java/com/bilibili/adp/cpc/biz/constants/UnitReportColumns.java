package com.bilibili.adp.cpc.biz.constants;

import java.util.Arrays;
import java.util.List;

import static com.bilibili.adp.cpc.biz.constants.ReportColumn.*;

public class UnitReportColumns {
    // 固定的列
    public static final List<ReportColumn> REQUIRED_LIST = Arrays.asList(
            UNIT_ID,
            UNIT_NAME
    );

    public static final List<ReportColumn> DEFAULT_LIST = Arrays.asList(
            CAMPAIGN_ID,
            CAMPAIGN_NAME,
            SLOT_GROUP,
            OS_NAME,
            UNIT_STATUS,
            LAUNCH_RANGE,
            OCPX_STATUS,
            OCPX_TARGET_TWO_STATUS,
            OCPX_TARGET_ONE_BID,
            OCPX_TARGET_TWO_BID,
            OCPX_AUTO_PAY_FINAL_STATUS,
            SALES_MODE_NAME,
            BID_PRICE,
            UNIT_BUDGET,
            COST,
            PV,
            CLICK,
            CTR,
            CPC,
            CPM
    );

    public static final List<ReportColumn> VARIABLE_LIST = Arrays.asList(
            UNIT_ATTRIBUTE,
            BASIC_DATA,
            SALE_CLUE,
            APP_AND_GAME,
            E_COMMERCE_AND_STORE,
            CONTENT_AND_INTERACTION
    );

    public static final List<ReportColumn> MIDDLE_VARIABLE_LIST = Arrays.asList(
            UNIT_ATTRIBUTE,
            BASIC_DATA,
            SALE_CLUE,
            APP_AND_GAME,
            E_COMMERCE_AND_STORE,
            MIDDLE_CONTENT_AND_INTERACTION,
            LIVE,
            TBK_FEED_INDICATOR_EXCHANGE,
            UNION_OPEN_EXCHANGE_MEDIA_EFFECT_DATA
    );
}
