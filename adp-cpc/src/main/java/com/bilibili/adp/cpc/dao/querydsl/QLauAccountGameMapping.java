package com.bilibili.adp.cpc.dao.querydsl;

import static com.querydsl.core.types.PathMetadataFactory.*;
import com.bilibili.adp.cpc.dao.querydsl.pos.LauAccountGameMappingPo;


import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;

import com.querydsl.sql.ColumnMetadata;
import java.sql.Types;




/**
 * QLauAccountGameMapping is a Querydsl query type for LauAccountGameMappingPo
 */
@Generated("com.querydsl.sql.codegen.MetaDataSerializer")
public class QLauAccountGameMapping extends com.querydsl.sql.RelationalPathBase<LauAccountGameMappingPo> {

    private static final long serialVersionUID = **********;

    public static final QLauAccountGameMapping lauAccountGameMapping = new QLauAccountGameMapping("lau_account_game_mapping");

    public final NumberPath<Integer> accountId = createNumber("accountId", Integer.class);

    public final NumberPath<Integer> agentId = createNumber("agentId", Integer.class);

    public final DateTimePath<java.sql.Timestamp> authEffectiveTime = createDateTime("authEffectiveTime", java.sql.Timestamp.class);

    public final DateTimePath<java.sql.Timestamp> authExpireTime = createDateTime("authExpireTime", java.sql.Timestamp.class);

    public final NumberPath<Long> authMid = createNumber("authMid", Long.class);

    public final NumberPath<Integer> authStatus = createNumber("authStatus", Integer.class);

    public final NumberPath<Integer> authTimeType = createNumber("authTimeType", Integer.class);

    public final DateTimePath<java.sql.Timestamp> ctime = createDateTime("ctime", java.sql.Timestamp.class);

    public final NumberPath<Long> gameBaseId = createNumber("gameBaseId", Long.class);

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final NumberPath<Integer> isDeleted = createNumber("isDeleted", Integer.class);

    public final DateTimePath<java.sql.Timestamp> mtime = createDateTime("mtime", java.sql.Timestamp.class);

    public final NumberPath<Integer> renewalStatus = createNumber("renewalStatus", Integer.class);

    public final NumberPath<Integer> sharingScope = createNumber("sharingScope", Integer.class);

    public final com.querydsl.sql.PrimaryKey<LauAccountGameMappingPo> primary = createPrimaryKey(id);

    public QLauAccountGameMapping(String variable) {
        super(LauAccountGameMappingPo.class, forVariable(variable), "null", "lau_account_game_mapping");
        addMetadata();
    }

    public QLauAccountGameMapping(String variable, String schema, String table) {
        super(LauAccountGameMappingPo.class, forVariable(variable), schema, table);
        addMetadata();
    }

    public QLauAccountGameMapping(String variable, String schema) {
        super(LauAccountGameMappingPo.class, forVariable(variable), schema, "lau_account_game_mapping");
        addMetadata();
    }

    public QLauAccountGameMapping(Path<? extends LauAccountGameMappingPo> path) {
        super(path.getType(), path.getMetadata(), "null", "lau_account_game_mapping");
        addMetadata();
    }

    public QLauAccountGameMapping(PathMetadata metadata) {
        super(LauAccountGameMappingPo.class, metadata, "null", "lau_account_game_mapping");
        addMetadata();
    }

    public void addMetadata() {
        addMetadata(accountId, ColumnMetadata.named("account_id").withIndex(2).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(agentId, ColumnMetadata.named("agent_id").withIndex(3).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(authEffectiveTime, ColumnMetadata.named("auth_effective_time").withIndex(11).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(authExpireTime, ColumnMetadata.named("auth_expire_time").withIndex(12).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(authMid, ColumnMetadata.named("auth_mid").withIndex(13).ofType(Types.BIGINT).withSize(19).notNull());
        addMetadata(authStatus, ColumnMetadata.named("auth_status").withIndex(8).ofType(Types.TINYINT).withSize(3).notNull());
        addMetadata(authTimeType, ColumnMetadata.named("auth_time_type").withIndex(10).ofType(Types.TINYINT).withSize(3).notNull());
        addMetadata(ctime, ColumnMetadata.named("ctime").withIndex(5).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(gameBaseId, ColumnMetadata.named("game_base_id").withIndex(4).ofType(Types.BIGINT).withSize(19).notNull());
        addMetadata(id, ColumnMetadata.named("id").withIndex(1).ofType(Types.BIGINT).withSize(20).notNull());
        addMetadata(isDeleted, ColumnMetadata.named("is_deleted").withIndex(7).ofType(Types.TINYINT).withSize(3).notNull());
        addMetadata(mtime, ColumnMetadata.named("mtime").withIndex(6).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(renewalStatus, ColumnMetadata.named("renewal_status").withIndex(9).ofType(Types.TINYINT).withSize(3).notNull());
        addMetadata(sharingScope, ColumnMetadata.named("sharing_scope").withIndex(14).ofType(Types.TINYINT).withSize(3).notNull());
    }

}

