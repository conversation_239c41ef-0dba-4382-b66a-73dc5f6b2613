package com.bilibili.adp.cpc.biz.services.archive;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.services.archive.bos.BusFlyArcAtmosphereBo;
import com.bilibili.adp.cpc.core.constants.IsDeleted;
import com.bilibili.bjcom.querydsl.BaseQueryFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.AD_BQF;
import static com.bilibili.adp.cpc.dao.querydsl.QLauBusinessFlyArcAtmosphereRecord.lauBusinessFlyArcAtmosphereRecord;

/**
 * 商业起飞稿件 商业气氛 用于主站评论治理策略
 *
 * @ClassName BusinessFlyArcAtmosphereService
 * <AUTHOR>
 * @Date 2023/8/18 6:35 下午
 * @Version 1.0
 **/
@Service
@Slf4j
public class MainArcAtmosphereService implements ApplicationContextAware {

    @Resource(name = AD_BQF)
    private BaseQueryFactory adBqf;

    private ApplicationContext applicationContext;

    private ExecutorService pool;

    //核心线程数
    @Value("${adp.arc.business.fly.atmosphere.core.pool.size:6}")
    private int corePoolSize;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    @PostConstruct
    private void init() {
        pool = Executors.newFixedThreadPool(corePoolSize);
    }

    public void handleMainArcAtmosphere(List<Long> avids, Integer accountId, Integer businessDomain) {
        if (CollectionUtils.isEmpty(avids)) {
            return;
        }

        if (!Utils.isPositive(accountId)) {
            return;
        }

        if (Objects.isNull(businessDomain)) {
            return;
        }

        log.info("handleArcAtmosphere, avids={},accountId={}, businessDomain={}", avids, accountId, businessDomain);
        MainArcAtmosphereTask task = applicationContext.getBean(MainArcAtmosphereTask.class);
        task.setAvids(avids);
        task.setAccountId(accountId);
        task.setBusinessDomain(businessDomain);
        pool.execute(task);
    }

    public BusFlyArcAtmosphereBo queryBusFlyArcAtmosphereInfo(Long avid) {
        List<BusFlyArcAtmosphereBo> infoBo = adBqf.selectFrom(lauBusinessFlyArcAtmosphereRecord)
                .where(lauBusinessFlyArcAtmosphereRecord.aid.eq(avid))
                .where(lauBusinessFlyArcAtmosphereRecord.isDeleted.eq(IsDeleted.VALID))
                .fetch(BusFlyArcAtmosphereBo.class);
        if (CollectionUtils.isEmpty(infoBo)) {
            return null;
        }
        return infoBo.get(0);
    }

}
