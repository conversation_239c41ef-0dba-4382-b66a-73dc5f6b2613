package com.bilibili.adp.cpc.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 * @date 2024/2/22 18:27
 */
@Configuration
public class BusinessToolThreadPoolConfig {
    @Bean
    public ThreadPoolTaskExecutor businessToolThreadPoolExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(20);
        executor.setKeepAliveSeconds(300);
        executor.setMaxPoolSize(20);
        executor.setQueueCapacity(30);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setThreadFactory(r -> new Thread(r, "business_tool_thread_archive_comment_component" + r.hashCode()));
        return executor;
    }
}
