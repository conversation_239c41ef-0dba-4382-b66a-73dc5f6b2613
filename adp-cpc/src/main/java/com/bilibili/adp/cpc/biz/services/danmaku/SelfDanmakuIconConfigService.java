package com.bilibili.adp.cpc.biz.services.danmaku;

import com.bilibili.adp.cpc.biz.services.danmaku.bos.SelfDanmakuIconConfigBo;
import com.bilibili.bjcom.querydsl.BaseQueryFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.AD_BQF;
import static com.bilibili.adp.cpc.dao.querydsl.QLauSelfDanmakuIconConfig.lauSelfDanmakuIconConfig;

/**
 * @ClassName SelfDanmakuIconConfigBo
 * <AUTHOR>
 * @Date 2023/12/27 11:58 上午
 * @Version 1.0
 **/
@Service
@Slf4j
public class SelfDanmakuIconConfigService {

    @Resource(name = AD_BQF)
    private BaseQueryFactory adBqf;

    // 预计100条数据以内
    public List<SelfDanmakuIconConfigBo> queryBoList() {
        return adBqf.selectFrom(lauSelfDanmakuIconConfig)
                .fetch(SelfDanmakuIconConfigBo.class);
    }
}
