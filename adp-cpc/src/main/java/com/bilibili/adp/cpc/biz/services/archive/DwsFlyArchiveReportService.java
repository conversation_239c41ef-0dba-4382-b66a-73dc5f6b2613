package com.bilibili.adp.cpc.biz.services.archive;

import com.bilibili.bjcom.querydsl.BaseQueryFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.sql.Date;
import java.time.LocalDate;
import java.util.Collections;
import java.util.List;

import static com.bilibili.adp.cpc.config.AppConfig.CPC_CLICKHOUSE_BQF;
import static com.bilibili.adp.cpc.dao.querydsl_clickhouse.QDwsFly.dwsFly;

/**
 * @ClassName DwsFlyArchiveReportService
 * <AUTHOR>
 * @Date 2023/7/18 2:08 下午
 * @Version 1.0
 **/
@Service
@Slf4j
public class DwsFlyArchiveReportService {

    private final BaseQueryFactory clickhouseBaseQueryFactory;

    public DwsFlyArchiveReportService(@Qualifier(CPC_CLICKHOUSE_BQF) BaseQueryFactory clickhouseBaseQueryFactory) {
        this.clickhouseBaseQueryFactory = clickhouseBaseQueryFactory;
    }

    public List<Long> getHasPvFlyAvidList(List<Long> avidList, Long checkRange) {
        if (CollectionUtils.isEmpty(avidList)) {
            return Collections.emptyList();
        }
        LocalDate now = LocalDate.now();
        Date startCheckDate = Date.valueOf(now.minusDays(checkRange + 1));
        String startCheckDateStr = startCheckDate.toString();
        return clickhouseBaseQueryFactory.from(dwsFly)
                .select(dwsFly.avid)
                .distinct()
                .where(dwsFly.avid.in(avidList))
                .where(dwsFly.logDate.goe(startCheckDateStr))
                .where(dwsFly.pv.gt(0L))
                .fetch();

    }

}
