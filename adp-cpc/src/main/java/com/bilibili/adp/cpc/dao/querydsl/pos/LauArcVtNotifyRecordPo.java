package com.bilibili.adp.cpc.dao.querydsl.pos;

import javax.annotation.Generated;

/**
 * LauArcVtNotifyRecordPo is a Querydsl bean type
 */
@Generated("com.querydsl.codegen.BeanSerializer")
public class LauArcVtNotifyRecordPo {

    private Long aid;

    private java.sql.Timestamp ctime;

    private Long id;

    private Integer isDeleted;

    private java.sql.Timestamp mtime;

    private Integer notifyState;

    private java.sql.Timestamp notifyTime;

    private java.sql.Timestamp recentActiveTime;

    public Long getAid() {
        return aid;
    }

    public void setAid(Long aid) {
        this.aid = aid;
    }

    public java.sql.Timestamp getCtime() {
        return ctime;
    }

    public void setCtime(java.sql.Timestamp ctime) {
        this.ctime = ctime;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public java.sql.Timestamp getMtime() {
        return mtime;
    }

    public void setMtime(java.sql.Timestamp mtime) {
        this.mtime = mtime;
    }

    public Integer getNotifyState() {
        return notifyState;
    }

    public void setNotifyState(Integer notifyState) {
        this.notifyState = notifyState;
    }

    public java.sql.Timestamp getNotifyTime() {
        return notifyTime;
    }

    public void setNotifyTime(java.sql.Timestamp notifyTime) {
        this.notifyTime = notifyTime;
    }

    public java.sql.Timestamp getRecentActiveTime() {
        return recentActiveTime;
    }

    public void setRecentActiveTime(java.sql.Timestamp recentActiveTime) {
        this.recentActiveTime = recentActiveTime;
    }

    @Override
    public String toString() {
         return "aid = " + aid + ", ctime = " + ctime + ", id = " + id + ", isDeleted = " + isDeleted + ", mtime = " + mtime + ", notifyState = " + notifyState + ", notifyTime = " + notifyTime + ", recentActiveTime = " + recentActiveTime;
    }

}

