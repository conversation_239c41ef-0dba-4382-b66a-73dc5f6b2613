package com.bilibili.adp.cpc.dao.querydsl;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.bilibili.adp.cpc.dao.querydsl.pos.LauStartUpCrowdsPo;


import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;

import com.querydsl.sql.ColumnMetadata;
import java.sql.Types;




/**
 * QLauStartUpCrowds is a Querydsl query type for LauStartUpCrowdsPo
 */
@Generated("com.querydsl.sql.codegen.MetaDataSerializer")
public class QLauStartUpCrowds extends com.querydsl.sql.RelationalPathBase<LauStartUpCrowdsPo> {

    private static final long serialVersionUID = 2096983687;

    public static final QLauStartUpCrowds lauStartUpCrowds = new QLauStartUpCrowds("lau_start_up_crowds");

    public final NumberPath<Integer> bizStatus = createNumber("bizStatus", Integer.class);

    public final NumberPath<Integer> crowdId = createNumber("crowdId", Integer.class);

    public final DateTimePath<java.sql.Timestamp> ctime = createDateTime("ctime", java.sql.Timestamp.class);

    public final NumberPath<Integer> id = createNumber("id", Integer.class);

    public final NumberPath<Integer> levelOneId = createNumber("levelOneId", Integer.class);

    public final StringPath levelOneName = createString("levelOneName");

    public final NumberPath<Integer> levelThreeId = createNumber("levelThreeId", Integer.class);

    public final StringPath levelThreeName = createString("levelThreeName");

    public final NumberPath<Integer> levelTwoId = createNumber("levelTwoId", Integer.class);

    public final StringPath levelTwoName = createString("levelTwoName");

    public final DateTimePath<java.sql.Timestamp> mtime = createDateTime("mtime", java.sql.Timestamp.class);

    public final com.querydsl.sql.PrimaryKey<LauStartUpCrowdsPo> primary = createPrimaryKey(id);

    public QLauStartUpCrowds(String variable) {
        super(LauStartUpCrowdsPo.class, forVariable(variable), "null", "lau_start_up_crowds");
        addMetadata();
    }

    public QLauStartUpCrowds(String variable, String schema, String table) {
        super(LauStartUpCrowdsPo.class, forVariable(variable), schema, table);
        addMetadata();
    }

    public QLauStartUpCrowds(String variable, String schema) {
        super(LauStartUpCrowdsPo.class, forVariable(variable), schema, "lau_start_up_crowds");
        addMetadata();
    }

    public QLauStartUpCrowds(Path<? extends LauStartUpCrowdsPo> path) {
        super(path.getType(), path.getMetadata(), "null", "lau_start_up_crowds");
        addMetadata();
    }

    public QLauStartUpCrowds(PathMetadata metadata) {
        super(LauStartUpCrowdsPo.class, metadata, "null", "lau_start_up_crowds");
        addMetadata();
    }

    public void addMetadata() {
        addMetadata(bizStatus, ColumnMetadata.named("biz_status").withIndex(4).ofType(Types.TINYINT).withSize(3).notNull());
        addMetadata(crowdId, ColumnMetadata.named("crowd_id").withIndex(5).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(ctime, ColumnMetadata.named("ctime").withIndex(2).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(id, ColumnMetadata.named("id").withIndex(1).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(levelOneId, ColumnMetadata.named("level_one_id").withIndex(6).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(levelOneName, ColumnMetadata.named("level_one_name").withIndex(7).ofType(Types.VARCHAR).withSize(32).notNull());
        addMetadata(levelThreeId, ColumnMetadata.named("level_three_id").withIndex(10).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(levelThreeName, ColumnMetadata.named("level_three_name").withIndex(11).ofType(Types.VARCHAR).withSize(32).notNull());
        addMetadata(levelTwoId, ColumnMetadata.named("level_two_id").withIndex(8).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(levelTwoName, ColumnMetadata.named("level_two_name").withIndex(9).ofType(Types.VARCHAR).withSize(32).notNull());
        addMetadata(mtime, ColumnMetadata.named("mtime").withIndex(3).ofType(Types.TIMESTAMP).withSize(19).notNull());
    }

}

