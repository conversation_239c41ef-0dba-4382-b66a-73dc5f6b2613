package com.bilibili.adp.cpc.databus.sub;

import com.alibaba.fastjson.JSONObject;
import com.bapis.ad.mgk.*;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.cpc.biz.bos.WarpDatabusProperty;
import com.bilibili.adp.cpc.biz.services.ad_product.IAdProductService;
import com.bilibili.adp.cpc.biz.services.rta.RtaStrategyService;
import com.bilibili.adp.cpc.dao.querydsl.pos.RtaStrategyMappingPo;
import com.bilibili.adp.cpc.databus.Constant;
import com.bilibili.adp.cpc.enums.YesOrNoEnum;
import com.bilibili.adp.cpc.po.ad.LauMiniGamePo;
import com.bilibili.adp.cpc.repo.AdProductRepo;
import com.bilibili.warp.databus.AckableMessage;
import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.databus.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import pleiades.venus.starter.rpc.client.RPCClient;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;

@Component
@Slf4j
public class MiniGameAndRtaBinlogSub implements MessageListener {


    @Autowired
    private AdProductRepo adProductRepo;

    @Autowired
    private IAdProductService adProductService;

    @Resource
    private RtaStrategyService rtaStrategyService;

    @RPCClient(value = "sycpb.cpm.mgk-portal")
    private LandingPageServiceGrpc.LandingPageServiceBlockingStub landingPageServiceBlockingStub;

    private final String topic;

    private final String group;

    public MiniGameAndRtaBinlogSub(WarpDatabusProperty warpDatabusProperty) {
        // 注入DatabusProperties，此处的ALIAS常量即配置文件中填写的别名，是topic的上一级配置键
        // pub场景同理，在使用DatabusTemplate的地方注入DatabusProperties来获取
        Map<String, DatabusProperty> properties = warpDatabusProperty.getProperties();
        DatabusProperty property = properties.get("lau_mini_game");
        log.info("lau_mini_game, property={}", JSONObject.toJSONString(property));
        this.topic = property.getTopic();
        this.group = property.getSub().getGroup();
    }

    @Override
    public String topic() {
        return this.topic;
    }

    @Override
    public String group() {
        return this.group;
    }

    @Override
    public void onMessage(AckableMessage message) {
        try {
            String value = new String(message.payload());
            JSONObject msg = JSONObject.parseObject(value);

            String table = msg.getString("table");

            switch (table) {
                case "lau_mini_game":
                    handleLauMiniGameBinlog(msg);
                    break;
                case "rta_strategy_mapping":
                    handleRtaStrategyMappingBinlog(msg);
                    break;
            }
            message.ack();
        } catch (Exception e) {
            String msg = new String(message.payload());
            log.info("AdProductMappingBinlogSub onMessage error message : {}", msg, e);
        }
    }

    private void handleRtaStrategyMappingBinlog(JSONObject msg) throws ServiceException {
        String action = msg.getString(Constant.ACTION);

        JSONObject newObject = msg.getJSONObject(Constant.NEW);

        if (!Objects.equals(action, Constant.INSERT) && !Objects.equals(action, Constant.UPDATE)) {
            return;
        }

        RtaStrategyMappingPo rtaStrategyMappingPo = deserializeRtaStrategyMappingBinlogDto(newObject);
        if (Objects.isNull(rtaStrategyMappingPo)) {
            return;
        }
        rtaStrategyService.saveTencentMiniGameStrategy(rtaStrategyMappingPo.getObjId(), rtaStrategyMappingPo.getStrategyName(), false, rtaStrategyMappingPo.getMtime().getTime());
    }

    private void handleLauMiniGameBinlog(JSONObject msg) throws Exception {
        String action = msg.getString(Constant.ACTION);

        JSONObject newObject = msg.getJSONObject(Constant.NEW);
        LauMiniGamePo newLauMiniGamePo = deserializeBinlogDto(newObject);

        if (Objects.isNull(newLauMiniGamePo)) {
            return;
        }

        if (Objects.equals(action, Constant.INSERT)) {
            adProductService.createMiniGameProduct(newLauMiniGamePo);
            rtaStrategyService.saveTencentMiniGameStrategy(newLauMiniGamePo.getAccountId(), newLauMiniGamePo.getOriginId());
        }

        if (Objects.equals(action, Constant.UPDATE)) {
            //查询小游戏对应的落地页
            MgkPagesWithMacroParamReply mgkPagesWithMacroParamReply = landingPageServiceBlockingStub.queryLimitLandingPageWithExtraParam(QueryPagesExtraReq.newBuilder()
                    .setLauMiniGameId(newLauMiniGamePo.getId())
                    .addAccountId(newLauMiniGamePo.getAccountId())
                    .setNeedWithLauMiniGame(YesOrNoEnum.YES.getCode())
                    .build());
            if (CollectionUtils.isEmpty(mgkPagesWithMacroParamReply.getMgkPageReplyList())) {
                return;
            }
            //调用异步刷新缓存
            for (MgkPageWithMacroParamReply mgkPageWithMacroParamReply : mgkPagesWithMacroParamReply.getMgkPageReplyList()) {
                long pageId = mgkPageWithMacroParamReply.getPageId();
                try {
                    PublishReply publishReply = landingPageServiceBlockingStub.asyncRefreshPageCache(PublishReq.newBuilder().setPageId(pageId).build());
                    log.info("异步刷新缓存：pageId {} , publishReply {}", pageId, publishReply);
                } catch (Exception e) {
                    log.error("异步刷新缓存失败：pageId {} ", pageId);
                    log.error("error", e);
                }
            }
        }
    }


    private LauMiniGamePo deserializeBinlogDto(JSONObject jsonObject) {
        if (Objects.isNull(jsonObject)) {
            return null;
        }
        return jsonObject.toJavaObject(LauMiniGamePo.class);
    }


    private RtaStrategyMappingPo deserializeRtaStrategyMappingBinlogDto(JSONObject jsonObject) {
        if (Objects.isNull(jsonObject)) {
            return null;
        }
        return jsonObject.toJavaObject(RtaStrategyMappingPo.class);
    }


}
