package com.bilibili.adp.cpc.enums;

import com.bilibili.mgk.platform.common.utils.BusinessException;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

/**
 * @ClassName DealStatusEnum
 * @<PERSON> x<PERSON>gyan
 * @Date 2022/1/24 5:24 下午
 * @Version 1.0
 **/
@Getter
@AllArgsConstructor
public enum DealStatusEnum {
    UNTREATED(0, "未处理"),
    DONE(1, "已处理"),
    ;
    public static DealStatusEnum getByCode(Integer code) {
        for (DealStatusEnum bean : values()) {
            if (code.equals(bean.getCode())) {
                return bean;
            }
        }
        throw new BusinessException("unknown code DealStatusEnum " + code);
    }

    private final Integer code;
    private final String name;
}
