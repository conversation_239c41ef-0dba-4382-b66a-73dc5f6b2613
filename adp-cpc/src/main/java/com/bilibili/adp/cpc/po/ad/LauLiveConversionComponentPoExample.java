package com.bilibili.adp.cpc.po.ad;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public class LauLiveConversionComponentPoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public LauLiveConversionComponentPoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andRoomIdIsNull() {
            addCriterion("room_id is null");
            return (Criteria) this;
        }

        public Criteria andRoomIdIsNotNull() {
            addCriterion("room_id is not null");
            return (Criteria) this;
        }

        public Criteria andRoomIdEqualTo(Long value) {
            addCriterion("room_id =", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdNotEqualTo(Long value) {
            addCriterion("room_id <>", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdGreaterThan(Long value) {
            addCriterion("room_id >", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdGreaterThanOrEqualTo(Long value) {
            addCriterion("room_id >=", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdLessThan(Long value) {
            addCriterion("room_id <", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdLessThanOrEqualTo(Long value) {
            addCriterion("room_id <=", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdIn(List<Long> values) {
            addCriterion("room_id in", values, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdNotIn(List<Long> values) {
            addCriterion("room_id not in", values, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdBetween(Long value1, Long value2) {
            addCriterion("room_id between", value1, value2, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdNotBetween(Long value1, Long value2) {
            addCriterion("room_id not between", value1, value2, "roomId");
            return (Criteria) this;
        }

        public Criteria andUidIsNull() {
            addCriterion("uid is null");
            return (Criteria) this;
        }

        public Criteria andUidIsNotNull() {
            addCriterion("uid is not null");
            return (Criteria) this;
        }

        public Criteria andUidEqualTo(Long value) {
            addCriterion("uid =", value, "uid");
            return (Criteria) this;
        }

        public Criteria andUidNotEqualTo(Long value) {
            addCriterion("uid <>", value, "uid");
            return (Criteria) this;
        }

        public Criteria andUidGreaterThan(Long value) {
            addCriterion("uid >", value, "uid");
            return (Criteria) this;
        }

        public Criteria andUidGreaterThanOrEqualTo(Long value) {
            addCriterion("uid >=", value, "uid");
            return (Criteria) this;
        }

        public Criteria andUidLessThan(Long value) {
            addCriterion("uid <", value, "uid");
            return (Criteria) this;
        }

        public Criteria andUidLessThanOrEqualTo(Long value) {
            addCriterion("uid <=", value, "uid");
            return (Criteria) this;
        }

        public Criteria andUidIn(List<Long> values) {
            addCriterion("uid in", values, "uid");
            return (Criteria) this;
        }

        public Criteria andUidNotIn(List<Long> values) {
            addCriterion("uid not in", values, "uid");
            return (Criteria) this;
        }

        public Criteria andUidBetween(Long value1, Long value2) {
            addCriterion("uid between", value1, value2, "uid");
            return (Criteria) this;
        }

        public Criteria andUidNotBetween(Long value1, Long value2) {
            addCriterion("uid not between", value1, value2, "uid");
            return (Criteria) this;
        }

        public Criteria andAccountIdIsNull() {
            addCriterion("account_id is null");
            return (Criteria) this;
        }

        public Criteria andAccountIdIsNotNull() {
            addCriterion("account_id is not null");
            return (Criteria) this;
        }

        public Criteria andAccountIdEqualTo(Integer value) {
            addCriterion("account_id =", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotEqualTo(Integer value) {
            addCriterion("account_id <>", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdGreaterThan(Integer value) {
            addCriterion("account_id >", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("account_id >=", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdLessThan(Integer value) {
            addCriterion("account_id <", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdLessThanOrEqualTo(Integer value) {
            addCriterion("account_id <=", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdIn(List<Integer> values) {
            addCriterion("account_id in", values, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotIn(List<Integer> values) {
            addCriterion("account_id not in", values, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdBetween(Integer value1, Integer value2) {
            addCriterion("account_id between", value1, value2, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotBetween(Integer value1, Integer value2) {
            addCriterion("account_id not between", value1, value2, "accountId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdIsNull() {
            addCriterion("customer_id is null");
            return (Criteria) this;
        }

        public Criteria andCustomerIdIsNotNull() {
            addCriterion("customer_id is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerIdEqualTo(Integer value) {
            addCriterion("customer_id =", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotEqualTo(Integer value) {
            addCriterion("customer_id <>", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdGreaterThan(Integer value) {
            addCriterion("customer_id >", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("customer_id >=", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdLessThan(Integer value) {
            addCriterion("customer_id <", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdLessThanOrEqualTo(Integer value) {
            addCriterion("customer_id <=", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdIn(List<Integer> values) {
            addCriterion("customer_id in", values, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotIn(List<Integer> values) {
            addCriterion("customer_id not in", values, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdBetween(Integer value1, Integer value2) {
            addCriterion("customer_id between", value1, value2, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotBetween(Integer value1, Integer value2) {
            addCriterion("customer_id not between", value1, value2, "customerId");
            return (Criteria) this;
        }

        public Criteria andAgentIdIsNull() {
            addCriterion("agent_id is null");
            return (Criteria) this;
        }

        public Criteria andAgentIdIsNotNull() {
            addCriterion("agent_id is not null");
            return (Criteria) this;
        }

        public Criteria andAgentIdEqualTo(Integer value) {
            addCriterion("agent_id =", value, "agentId");
            return (Criteria) this;
        }

        public Criteria andAgentIdNotEqualTo(Integer value) {
            addCriterion("agent_id <>", value, "agentId");
            return (Criteria) this;
        }

        public Criteria andAgentIdGreaterThan(Integer value) {
            addCriterion("agent_id >", value, "agentId");
            return (Criteria) this;
        }

        public Criteria andAgentIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("agent_id >=", value, "agentId");
            return (Criteria) this;
        }

        public Criteria andAgentIdLessThan(Integer value) {
            addCriterion("agent_id <", value, "agentId");
            return (Criteria) this;
        }

        public Criteria andAgentIdLessThanOrEqualTo(Integer value) {
            addCriterion("agent_id <=", value, "agentId");
            return (Criteria) this;
        }

        public Criteria andAgentIdIn(List<Integer> values) {
            addCriterion("agent_id in", values, "agentId");
            return (Criteria) this;
        }

        public Criteria andAgentIdNotIn(List<Integer> values) {
            addCriterion("agent_id not in", values, "agentId");
            return (Criteria) this;
        }

        public Criteria andAgentIdBetween(Integer value1, Integer value2) {
            addCriterion("agent_id between", value1, value2, "agentId");
            return (Criteria) this;
        }

        public Criteria andAgentIdNotBetween(Integer value1, Integer value2) {
            addCriterion("agent_id not between", value1, value2, "agentId");
            return (Criteria) this;
        }

        public Criteria andCampaignIdIsNull() {
            addCriterion("campaign_id is null");
            return (Criteria) this;
        }

        public Criteria andCampaignIdIsNotNull() {
            addCriterion("campaign_id is not null");
            return (Criteria) this;
        }

        public Criteria andCampaignIdEqualTo(Integer value) {
            addCriterion("campaign_id =", value, "campaignId");
            return (Criteria) this;
        }

        public Criteria andCampaignIdNotEqualTo(Integer value) {
            addCriterion("campaign_id <>", value, "campaignId");
            return (Criteria) this;
        }

        public Criteria andCampaignIdGreaterThan(Integer value) {
            addCriterion("campaign_id >", value, "campaignId");
            return (Criteria) this;
        }

        public Criteria andCampaignIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("campaign_id >=", value, "campaignId");
            return (Criteria) this;
        }

        public Criteria andCampaignIdLessThan(Integer value) {
            addCriterion("campaign_id <", value, "campaignId");
            return (Criteria) this;
        }

        public Criteria andCampaignIdLessThanOrEqualTo(Integer value) {
            addCriterion("campaign_id <=", value, "campaignId");
            return (Criteria) this;
        }

        public Criteria andCampaignIdIn(List<Integer> values) {
            addCriterion("campaign_id in", values, "campaignId");
            return (Criteria) this;
        }

        public Criteria andCampaignIdNotIn(List<Integer> values) {
            addCriterion("campaign_id not in", values, "campaignId");
            return (Criteria) this;
        }

        public Criteria andCampaignIdBetween(Integer value1, Integer value2) {
            addCriterion("campaign_id between", value1, value2, "campaignId");
            return (Criteria) this;
        }

        public Criteria andCampaignIdNotBetween(Integer value1, Integer value2) {
            addCriterion("campaign_id not between", value1, value2, "campaignId");
            return (Criteria) this;
        }

        public Criteria andUnitIdIsNull() {
            addCriterion("unit_id is null");
            return (Criteria) this;
        }

        public Criteria andUnitIdIsNotNull() {
            addCriterion("unit_id is not null");
            return (Criteria) this;
        }

        public Criteria andUnitIdEqualTo(Integer value) {
            addCriterion("unit_id =", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdNotEqualTo(Integer value) {
            addCriterion("unit_id <>", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdGreaterThan(Integer value) {
            addCriterion("unit_id >", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("unit_id >=", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdLessThan(Integer value) {
            addCriterion("unit_id <", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdLessThanOrEqualTo(Integer value) {
            addCriterion("unit_id <=", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdIn(List<Integer> values) {
            addCriterion("unit_id in", values, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdNotIn(List<Integer> values) {
            addCriterion("unit_id not in", values, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdBetween(Integer value1, Integer value2) {
            addCriterion("unit_id between", value1, value2, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdNotBetween(Integer value1, Integer value2) {
            addCriterion("unit_id not between", value1, value2, "unitId");
            return (Criteria) this;
        }

        public Criteria andCreativeIdIsNull() {
            addCriterion("creative_id is null");
            return (Criteria) this;
        }

        public Criteria andCreativeIdIsNotNull() {
            addCriterion("creative_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreativeIdEqualTo(Integer value) {
            addCriterion("creative_id =", value, "creativeId");
            return (Criteria) this;
        }

        public Criteria andCreativeIdNotEqualTo(Integer value) {
            addCriterion("creative_id <>", value, "creativeId");
            return (Criteria) this;
        }

        public Criteria andCreativeIdGreaterThan(Integer value) {
            addCriterion("creative_id >", value, "creativeId");
            return (Criteria) this;
        }

        public Criteria andCreativeIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("creative_id >=", value, "creativeId");
            return (Criteria) this;
        }

        public Criteria andCreativeIdLessThan(Integer value) {
            addCriterion("creative_id <", value, "creativeId");
            return (Criteria) this;
        }

        public Criteria andCreativeIdLessThanOrEqualTo(Integer value) {
            addCriterion("creative_id <=", value, "creativeId");
            return (Criteria) this;
        }

        public Criteria andCreativeIdIn(List<Integer> values) {
            addCriterion("creative_id in", values, "creativeId");
            return (Criteria) this;
        }

        public Criteria andCreativeIdNotIn(List<Integer> values) {
            addCriterion("creative_id not in", values, "creativeId");
            return (Criteria) this;
        }

        public Criteria andCreativeIdBetween(Integer value1, Integer value2) {
            addCriterion("creative_id between", value1, value2, "creativeId");
            return (Criteria) this;
        }

        public Criteria andCreativeIdNotBetween(Integer value1, Integer value2) {
            addCriterion("creative_id not between", value1, value2, "creativeId");
            return (Criteria) this;
        }

        public Criteria andCardTitleIsNull() {
            addCriterion("card_title is null");
            return (Criteria) this;
        }

        public Criteria andCardTitleIsNotNull() {
            addCriterion("card_title is not null");
            return (Criteria) this;
        }

        public Criteria andCardTitleEqualTo(String value) {
            addCriterion("card_title =", value, "cardTitle");
            return (Criteria) this;
        }

        public Criteria andCardTitleNotEqualTo(String value) {
            addCriterion("card_title <>", value, "cardTitle");
            return (Criteria) this;
        }

        public Criteria andCardTitleGreaterThan(String value) {
            addCriterion("card_title >", value, "cardTitle");
            return (Criteria) this;
        }

        public Criteria andCardTitleGreaterThanOrEqualTo(String value) {
            addCriterion("card_title >=", value, "cardTitle");
            return (Criteria) this;
        }

        public Criteria andCardTitleLessThan(String value) {
            addCriterion("card_title <", value, "cardTitle");
            return (Criteria) this;
        }

        public Criteria andCardTitleLessThanOrEqualTo(String value) {
            addCriterion("card_title <=", value, "cardTitle");
            return (Criteria) this;
        }

        public Criteria andCardTitleLike(String value) {
            addCriterion("card_title like", value, "cardTitle");
            return (Criteria) this;
        }

        public Criteria andCardTitleNotLike(String value) {
            addCriterion("card_title not like", value, "cardTitle");
            return (Criteria) this;
        }

        public Criteria andCardTitleIn(List<String> values) {
            addCriterion("card_title in", values, "cardTitle");
            return (Criteria) this;
        }

        public Criteria andCardTitleNotIn(List<String> values) {
            addCriterion("card_title not in", values, "cardTitle");
            return (Criteria) this;
        }

        public Criteria andCardTitleBetween(String value1, String value2) {
            addCriterion("card_title between", value1, value2, "cardTitle");
            return (Criteria) this;
        }

        public Criteria andCardTitleNotBetween(String value1, String value2) {
            addCriterion("card_title not between", value1, value2, "cardTitle");
            return (Criteria) this;
        }

        public Criteria andButtonTextIsNull() {
            addCriterion("button_text is null");
            return (Criteria) this;
        }

        public Criteria andButtonTextIsNotNull() {
            addCriterion("button_text is not null");
            return (Criteria) this;
        }

        public Criteria andButtonTextEqualTo(String value) {
            addCriterion("button_text =", value, "buttonText");
            return (Criteria) this;
        }

        public Criteria andButtonTextNotEqualTo(String value) {
            addCriterion("button_text <>", value, "buttonText");
            return (Criteria) this;
        }

        public Criteria andButtonTextGreaterThan(String value) {
            addCriterion("button_text >", value, "buttonText");
            return (Criteria) this;
        }

        public Criteria andButtonTextGreaterThanOrEqualTo(String value) {
            addCriterion("button_text >=", value, "buttonText");
            return (Criteria) this;
        }

        public Criteria andButtonTextLessThan(String value) {
            addCriterion("button_text <", value, "buttonText");
            return (Criteria) this;
        }

        public Criteria andButtonTextLessThanOrEqualTo(String value) {
            addCriterion("button_text <=", value, "buttonText");
            return (Criteria) this;
        }

        public Criteria andButtonTextLike(String value) {
            addCriterion("button_text like", value, "buttonText");
            return (Criteria) this;
        }

        public Criteria andButtonTextNotLike(String value) {
            addCriterion("button_text not like", value, "buttonText");
            return (Criteria) this;
        }

        public Criteria andButtonTextIn(List<String> values) {
            addCriterion("button_text in", values, "buttonText");
            return (Criteria) this;
        }

        public Criteria andButtonTextNotIn(List<String> values) {
            addCriterion("button_text not in", values, "buttonText");
            return (Criteria) this;
        }

        public Criteria andButtonTextBetween(String value1, String value2) {
            addCriterion("button_text between", value1, value2, "buttonText");
            return (Criteria) this;
        }

        public Criteria andButtonTextNotBetween(String value1, String value2) {
            addCriterion("button_text not between", value1, value2, "buttonText");
            return (Criteria) this;
        }

        public Criteria andCardSamllImageUrlIsNull() {
            addCriterion("card_samll_image_url is null");
            return (Criteria) this;
        }

        public Criteria andCardSamllImageUrlIsNotNull() {
            addCriterion("card_samll_image_url is not null");
            return (Criteria) this;
        }

        public Criteria andCardSamllImageUrlEqualTo(String value) {
            addCriterion("card_samll_image_url =", value, "cardSamllImageUrl");
            return (Criteria) this;
        }

        public Criteria andCardSamllImageUrlNotEqualTo(String value) {
            addCriterion("card_samll_image_url <>", value, "cardSamllImageUrl");
            return (Criteria) this;
        }

        public Criteria andCardSamllImageUrlGreaterThan(String value) {
            addCriterion("card_samll_image_url >", value, "cardSamllImageUrl");
            return (Criteria) this;
        }

        public Criteria andCardSamllImageUrlGreaterThanOrEqualTo(String value) {
            addCriterion("card_samll_image_url >=", value, "cardSamllImageUrl");
            return (Criteria) this;
        }

        public Criteria andCardSamllImageUrlLessThan(String value) {
            addCriterion("card_samll_image_url <", value, "cardSamllImageUrl");
            return (Criteria) this;
        }

        public Criteria andCardSamllImageUrlLessThanOrEqualTo(String value) {
            addCriterion("card_samll_image_url <=", value, "cardSamllImageUrl");
            return (Criteria) this;
        }

        public Criteria andCardSamllImageUrlLike(String value) {
            addCriterion("card_samll_image_url like", value, "cardSamllImageUrl");
            return (Criteria) this;
        }

        public Criteria andCardSamllImageUrlNotLike(String value) {
            addCriterion("card_samll_image_url not like", value, "cardSamllImageUrl");
            return (Criteria) this;
        }

        public Criteria andCardSamllImageUrlIn(List<String> values) {
            addCriterion("card_samll_image_url in", values, "cardSamllImageUrl");
            return (Criteria) this;
        }

        public Criteria andCardSamllImageUrlNotIn(List<String> values) {
            addCriterion("card_samll_image_url not in", values, "cardSamllImageUrl");
            return (Criteria) this;
        }

        public Criteria andCardSamllImageUrlBetween(String value1, String value2) {
            addCriterion("card_samll_image_url between", value1, value2, "cardSamllImageUrl");
            return (Criteria) this;
        }

        public Criteria andCardSamllImageUrlNotBetween(String value1, String value2) {
            addCriterion("card_samll_image_url not between", value1, value2, "cardSamllImageUrl");
            return (Criteria) this;
        }

        public Criteria andComponentTypeIsNull() {
            addCriterion("component_type is null");
            return (Criteria) this;
        }

        public Criteria andComponentTypeIsNotNull() {
            addCriterion("component_type is not null");
            return (Criteria) this;
        }

        public Criteria andComponentTypeEqualTo(Integer value) {
            addCriterion("component_type =", value, "componentType");
            return (Criteria) this;
        }

        public Criteria andComponentTypeNotEqualTo(Integer value) {
            addCriterion("component_type <>", value, "componentType");
            return (Criteria) this;
        }

        public Criteria andComponentTypeGreaterThan(Integer value) {
            addCriterion("component_type >", value, "componentType");
            return (Criteria) this;
        }

        public Criteria andComponentTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("component_type >=", value, "componentType");
            return (Criteria) this;
        }

        public Criteria andComponentTypeLessThan(Integer value) {
            addCriterion("component_type <", value, "componentType");
            return (Criteria) this;
        }

        public Criteria andComponentTypeLessThanOrEqualTo(Integer value) {
            addCriterion("component_type <=", value, "componentType");
            return (Criteria) this;
        }

        public Criteria andComponentTypeIn(List<Integer> values) {
            addCriterion("component_type in", values, "componentType");
            return (Criteria) this;
        }

        public Criteria andComponentTypeNotIn(List<Integer> values) {
            addCriterion("component_type not in", values, "componentType");
            return (Criteria) this;
        }

        public Criteria andComponentTypeBetween(Integer value1, Integer value2) {
            addCriterion("component_type between", value1, value2, "componentType");
            return (Criteria) this;
        }

        public Criteria andComponentTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("component_type not between", value1, value2, "componentType");
            return (Criteria) this;
        }

        public Criteria andMgkPageIdIsNull() {
            addCriterion("mgk_page_id is null");
            return (Criteria) this;
        }

        public Criteria andMgkPageIdIsNotNull() {
            addCriterion("mgk_page_id is not null");
            return (Criteria) this;
        }

        public Criteria andMgkPageIdEqualTo(Long value) {
            addCriterion("mgk_page_id =", value, "mgkPageId");
            return (Criteria) this;
        }

        public Criteria andMgkPageIdNotEqualTo(Long value) {
            addCriterion("mgk_page_id <>", value, "mgkPageId");
            return (Criteria) this;
        }

        public Criteria andMgkPageIdGreaterThan(Long value) {
            addCriterion("mgk_page_id >", value, "mgkPageId");
            return (Criteria) this;
        }

        public Criteria andMgkPageIdGreaterThanOrEqualTo(Long value) {
            addCriterion("mgk_page_id >=", value, "mgkPageId");
            return (Criteria) this;
        }

        public Criteria andMgkPageIdLessThan(Long value) {
            addCriterion("mgk_page_id <", value, "mgkPageId");
            return (Criteria) this;
        }

        public Criteria andMgkPageIdLessThanOrEqualTo(Long value) {
            addCriterion("mgk_page_id <=", value, "mgkPageId");
            return (Criteria) this;
        }

        public Criteria andMgkPageIdIn(List<Long> values) {
            addCriterion("mgk_page_id in", values, "mgkPageId");
            return (Criteria) this;
        }

        public Criteria andMgkPageIdNotIn(List<Long> values) {
            addCriterion("mgk_page_id not in", values, "mgkPageId");
            return (Criteria) this;
        }

        public Criteria andMgkPageIdBetween(Long value1, Long value2) {
            addCriterion("mgk_page_id between", value1, value2, "mgkPageId");
            return (Criteria) this;
        }

        public Criteria andMgkPageIdNotBetween(Long value1, Long value2) {
            addCriterion("mgk_page_id not between", value1, value2, "mgkPageId");
            return (Criteria) this;
        }

        public Criteria andConversionUrlIsNull() {
            addCriterion("conversion_url is null");
            return (Criteria) this;
        }

        public Criteria andConversionUrlIsNotNull() {
            addCriterion("conversion_url is not null");
            return (Criteria) this;
        }

        public Criteria andConversionUrlEqualTo(String value) {
            addCriterion("conversion_url =", value, "conversionUrl");
            return (Criteria) this;
        }

        public Criteria andConversionUrlNotEqualTo(String value) {
            addCriterion("conversion_url <>", value, "conversionUrl");
            return (Criteria) this;
        }

        public Criteria andConversionUrlGreaterThan(String value) {
            addCriterion("conversion_url >", value, "conversionUrl");
            return (Criteria) this;
        }

        public Criteria andConversionUrlGreaterThanOrEqualTo(String value) {
            addCriterion("conversion_url >=", value, "conversionUrl");
            return (Criteria) this;
        }

        public Criteria andConversionUrlLessThan(String value) {
            addCriterion("conversion_url <", value, "conversionUrl");
            return (Criteria) this;
        }

        public Criteria andConversionUrlLessThanOrEqualTo(String value) {
            addCriterion("conversion_url <=", value, "conversionUrl");
            return (Criteria) this;
        }

        public Criteria andConversionUrlLike(String value) {
            addCriterion("conversion_url like", value, "conversionUrl");
            return (Criteria) this;
        }

        public Criteria andConversionUrlNotLike(String value) {
            addCriterion("conversion_url not like", value, "conversionUrl");
            return (Criteria) this;
        }

        public Criteria andConversionUrlIn(List<String> values) {
            addCriterion("conversion_url in", values, "conversionUrl");
            return (Criteria) this;
        }

        public Criteria andConversionUrlNotIn(List<String> values) {
            addCriterion("conversion_url not in", values, "conversionUrl");
            return (Criteria) this;
        }

        public Criteria andConversionUrlBetween(String value1, String value2) {
            addCriterion("conversion_url between", value1, value2, "conversionUrl");
            return (Criteria) this;
        }

        public Criteria andConversionUrlNotBetween(String value1, String value2) {
            addCriterion("conversion_url not between", value1, value2, "conversionUrl");
            return (Criteria) this;
        }

        public Criteria andSceneIsNull() {
            addCriterion("scene is null");
            return (Criteria) this;
        }

        public Criteria andSceneIsNotNull() {
            addCriterion("scene is not null");
            return (Criteria) this;
        }

        public Criteria andSceneEqualTo(String value) {
            addCriterion("scene =", value, "scene");
            return (Criteria) this;
        }

        public Criteria andSceneNotEqualTo(String value) {
            addCriterion("scene <>", value, "scene");
            return (Criteria) this;
        }

        public Criteria andSceneGreaterThan(String value) {
            addCriterion("scene >", value, "scene");
            return (Criteria) this;
        }

        public Criteria andSceneGreaterThanOrEqualTo(String value) {
            addCriterion("scene >=", value, "scene");
            return (Criteria) this;
        }

        public Criteria andSceneLessThan(String value) {
            addCriterion("scene <", value, "scene");
            return (Criteria) this;
        }

        public Criteria andSceneLessThanOrEqualTo(String value) {
            addCriterion("scene <=", value, "scene");
            return (Criteria) this;
        }

        public Criteria andSceneLike(String value) {
            addCriterion("scene like", value, "scene");
            return (Criteria) this;
        }

        public Criteria andSceneNotLike(String value) {
            addCriterion("scene not like", value, "scene");
            return (Criteria) this;
        }

        public Criteria andSceneIn(List<String> values) {
            addCriterion("scene in", values, "scene");
            return (Criteria) this;
        }

        public Criteria andSceneNotIn(List<String> values) {
            addCriterion("scene not in", values, "scene");
            return (Criteria) this;
        }

        public Criteria andSceneBetween(String value1, String value2) {
            addCriterion("scene between", value1, value2, "scene");
            return (Criteria) this;
        }

        public Criteria andSceneNotBetween(String value1, String value2) {
            addCriterion("scene not between", value1, value2, "scene");
            return (Criteria) this;
        }

        public Criteria andFormSubmitIsNull() {
            addCriterion("form_submit is null");
            return (Criteria) this;
        }

        public Criteria andFormSubmitIsNotNull() {
            addCriterion("form_submit is not null");
            return (Criteria) this;
        }

        public Criteria andFormSubmitEqualTo(Integer value) {
            addCriterion("form_submit =", value, "formSubmit");
            return (Criteria) this;
        }

        public Criteria andFormSubmitNotEqualTo(Integer value) {
            addCriterion("form_submit <>", value, "formSubmit");
            return (Criteria) this;
        }

        public Criteria andFormSubmitGreaterThan(Integer value) {
            addCriterion("form_submit >", value, "formSubmit");
            return (Criteria) this;
        }

        public Criteria andFormSubmitGreaterThanOrEqualTo(Integer value) {
            addCriterion("form_submit >=", value, "formSubmit");
            return (Criteria) this;
        }

        public Criteria andFormSubmitLessThan(Integer value) {
            addCriterion("form_submit <", value, "formSubmit");
            return (Criteria) this;
        }

        public Criteria andFormSubmitLessThanOrEqualTo(Integer value) {
            addCriterion("form_submit <=", value, "formSubmit");
            return (Criteria) this;
        }

        public Criteria andFormSubmitIn(List<Integer> values) {
            addCriterion("form_submit in", values, "formSubmit");
            return (Criteria) this;
        }

        public Criteria andFormSubmitNotIn(List<Integer> values) {
            addCriterion("form_submit not in", values, "formSubmit");
            return (Criteria) this;
        }

        public Criteria andFormSubmitBetween(Integer value1, Integer value2) {
            addCriterion("form_submit between", value1, value2, "formSubmit");
            return (Criteria) this;
        }

        public Criteria andFormSubmitNotBetween(Integer value1, Integer value2) {
            addCriterion("form_submit not between", value1, value2, "formSubmit");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNull() {
            addCriterion("ctime is null");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNotNull() {
            addCriterion("ctime is not null");
            return (Criteria) this;
        }

        public Criteria andCtimeEqualTo(Timestamp value) {
            addCriterion("ctime =", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotEqualTo(Timestamp value) {
            addCriterion("ctime <>", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThan(Timestamp value) {
            addCriterion("ctime >", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("ctime >=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThan(Timestamp value) {
            addCriterion("ctime <", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("ctime <=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeIn(List<Timestamp> values) {
            addCriterion("ctime in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotIn(List<Timestamp> values) {
            addCriterion("ctime not in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime not between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNull() {
            addCriterion("mtime is null");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNotNull() {
            addCriterion("mtime is not null");
            return (Criteria) this;
        }

        public Criteria andMtimeEqualTo(Timestamp value) {
            addCriterion("mtime =", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotEqualTo(Timestamp value) {
            addCriterion("mtime <>", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThan(Timestamp value) {
            addCriterion("mtime >", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("mtime >=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThan(Timestamp value) {
            addCriterion("mtime <", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("mtime <=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeIn(List<Timestamp> values) {
            addCriterion("mtime in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotIn(List<Timestamp> values) {
            addCriterion("mtime not in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime not between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andToolIdIsNull() {
            addCriterion("tool_id is null");
            return (Criteria) this;
        }

        public Criteria andToolIdIsNotNull() {
            addCriterion("tool_id is not null");
            return (Criteria) this;
        }

        public Criteria andToolIdEqualTo(String value) {
            addCriterion("tool_id =", value, "toolId");
            return (Criteria) this;
        }

        public Criteria andToolIdNotEqualTo(String value) {
            addCriterion("tool_id <>", value, "toolId");
            return (Criteria) this;
        }

        public Criteria andToolIdGreaterThan(String value) {
            addCriterion("tool_id >", value, "toolId");
            return (Criteria) this;
        }

        public Criteria andToolIdGreaterThanOrEqualTo(String value) {
            addCriterion("tool_id >=", value, "toolId");
            return (Criteria) this;
        }

        public Criteria andToolIdLessThan(String value) {
            addCriterion("tool_id <", value, "toolId");
            return (Criteria) this;
        }

        public Criteria andToolIdLessThanOrEqualTo(String value) {
            addCriterion("tool_id <=", value, "toolId");
            return (Criteria) this;
        }

        public Criteria andToolIdLike(String value) {
            addCriterion("tool_id like", value, "toolId");
            return (Criteria) this;
        }

        public Criteria andToolIdNotLike(String value) {
            addCriterion("tool_id not like", value, "toolId");
            return (Criteria) this;
        }

        public Criteria andToolIdIn(List<String> values) {
            addCriterion("tool_id in", values, "toolId");
            return (Criteria) this;
        }

        public Criteria andToolIdNotIn(List<String> values) {
            addCriterion("tool_id not in", values, "toolId");
            return (Criteria) this;
        }

        public Criteria andToolIdBetween(String value1, String value2) {
            addCriterion("tool_id between", value1, value2, "toolId");
            return (Criteria) this;
        }

        public Criteria andToolIdNotBetween(String value1, String value2) {
            addCriterion("tool_id not between", value1, value2, "toolId");
            return (Criteria) this;
        }

        public Criteria andSubTitleIsNull() {
            addCriterion("sub_title is null");
            return (Criteria) this;
        }

        public Criteria andSubTitleIsNotNull() {
            addCriterion("sub_title is not null");
            return (Criteria) this;
        }

        public Criteria andSubTitleEqualTo(String value) {
            addCriterion("sub_title =", value, "subTitle");
            return (Criteria) this;
        }

        public Criteria andSubTitleNotEqualTo(String value) {
            addCriterion("sub_title <>", value, "subTitle");
            return (Criteria) this;
        }

        public Criteria andSubTitleGreaterThan(String value) {
            addCriterion("sub_title >", value, "subTitle");
            return (Criteria) this;
        }

        public Criteria andSubTitleGreaterThanOrEqualTo(String value) {
            addCriterion("sub_title >=", value, "subTitle");
            return (Criteria) this;
        }

        public Criteria andSubTitleLessThan(String value) {
            addCriterion("sub_title <", value, "subTitle");
            return (Criteria) this;
        }

        public Criteria andSubTitleLessThanOrEqualTo(String value) {
            addCriterion("sub_title <=", value, "subTitle");
            return (Criteria) this;
        }

        public Criteria andSubTitleLike(String value) {
            addCriterion("sub_title like", value, "subTitle");
            return (Criteria) this;
        }

        public Criteria andSubTitleNotLike(String value) {
            addCriterion("sub_title not like", value, "subTitle");
            return (Criteria) this;
        }

        public Criteria andSubTitleIn(List<String> values) {
            addCriterion("sub_title in", values, "subTitle");
            return (Criteria) this;
        }

        public Criteria andSubTitleNotIn(List<String> values) {
            addCriterion("sub_title not in", values, "subTitle");
            return (Criteria) this;
        }

        public Criteria andSubTitleBetween(String value1, String value2) {
            addCriterion("sub_title between", value1, value2, "subTitle");
            return (Criteria) this;
        }

        public Criteria andSubTitleNotBetween(String value1, String value2) {
            addCriterion("sub_title not between", value1, value2, "subTitle");
            return (Criteria) this;
        }

        public Criteria andBookingOuterTypeIsNull() {
            addCriterion("booking_outer_type is null");
            return (Criteria) this;
        }

        public Criteria andBookingOuterTypeIsNotNull() {
            addCriterion("booking_outer_type is not null");
            return (Criteria) this;
        }

        public Criteria andBookingOuterTypeEqualTo(Integer value) {
            addCriterion("booking_outer_type =", value, "bookingOuterType");
            return (Criteria) this;
        }

        public Criteria andBookingOuterTypeNotEqualTo(Integer value) {
            addCriterion("booking_outer_type <>", value, "bookingOuterType");
            return (Criteria) this;
        }

        public Criteria andBookingOuterTypeGreaterThan(Integer value) {
            addCriterion("booking_outer_type >", value, "bookingOuterType");
            return (Criteria) this;
        }

        public Criteria andBookingOuterTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("booking_outer_type >=", value, "bookingOuterType");
            return (Criteria) this;
        }

        public Criteria andBookingOuterTypeLessThan(Integer value) {
            addCriterion("booking_outer_type <", value, "bookingOuterType");
            return (Criteria) this;
        }

        public Criteria andBookingOuterTypeLessThanOrEqualTo(Integer value) {
            addCriterion("booking_outer_type <=", value, "bookingOuterType");
            return (Criteria) this;
        }

        public Criteria andBookingOuterTypeIn(List<Integer> values) {
            addCriterion("booking_outer_type in", values, "bookingOuterType");
            return (Criteria) this;
        }

        public Criteria andBookingOuterTypeNotIn(List<Integer> values) {
            addCriterion("booking_outer_type not in", values, "bookingOuterType");
            return (Criteria) this;
        }

        public Criteria andBookingOuterTypeBetween(Integer value1, Integer value2) {
            addCriterion("booking_outer_type between", value1, value2, "bookingOuterType");
            return (Criteria) this;
        }

        public Criteria andBookingOuterTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("booking_outer_type not between", value1, value2, "bookingOuterType");
            return (Criteria) this;
        }

        public Criteria andSubTitleTypeIsNull() {
            addCriterion("sub_title_type is null");
            return (Criteria) this;
        }

        public Criteria andSubTitleTypeIsNotNull() {
            addCriterion("sub_title_type is not null");
            return (Criteria) this;
        }

        public Criteria andSubTitleTypeEqualTo(Integer value) {
            addCriterion("sub_title_type =", value, "subTitleType");
            return (Criteria) this;
        }

        public Criteria andSubTitleTypeNotEqualTo(Integer value) {
            addCriterion("sub_title_type <>", value, "subTitleType");
            return (Criteria) this;
        }

        public Criteria andSubTitleTypeGreaterThan(Integer value) {
            addCriterion("sub_title_type >", value, "subTitleType");
            return (Criteria) this;
        }

        public Criteria andSubTitleTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("sub_title_type >=", value, "subTitleType");
            return (Criteria) this;
        }

        public Criteria andSubTitleTypeLessThan(Integer value) {
            addCriterion("sub_title_type <", value, "subTitleType");
            return (Criteria) this;
        }

        public Criteria andSubTitleTypeLessThanOrEqualTo(Integer value) {
            addCriterion("sub_title_type <=", value, "subTitleType");
            return (Criteria) this;
        }

        public Criteria andSubTitleTypeIn(List<Integer> values) {
            addCriterion("sub_title_type in", values, "subTitleType");
            return (Criteria) this;
        }

        public Criteria andSubTitleTypeNotIn(List<Integer> values) {
            addCriterion("sub_title_type not in", values, "subTitleType");
            return (Criteria) this;
        }

        public Criteria andSubTitleTypeBetween(Integer value1, Integer value2) {
            addCriterion("sub_title_type between", value1, value2, "subTitleType");
            return (Criteria) this;
        }

        public Criteria andSubTitleTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("sub_title_type not between", value1, value2, "subTitleType");
            return (Criteria) this;
        }

        public Criteria andMiniGameIdIsNull() {
            addCriterion("mini_game_id is null");
            return (Criteria) this;
        }

        public Criteria andMiniGameIdIsNotNull() {
            addCriterion("mini_game_id is not null");
            return (Criteria) this;
        }

        public Criteria andMiniGameIdEqualTo(Integer value) {
            addCriterion("mini_game_id =", value, "miniGameId");
            return (Criteria) this;
        }

        public Criteria andMiniGameIdNotEqualTo(Integer value) {
            addCriterion("mini_game_id <>", value, "miniGameId");
            return (Criteria) this;
        }

        public Criteria andMiniGameIdGreaterThan(Integer value) {
            addCriterion("mini_game_id >", value, "miniGameId");
            return (Criteria) this;
        }

        public Criteria andMiniGameIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("mini_game_id >=", value, "miniGameId");
            return (Criteria) this;
        }

        public Criteria andMiniGameIdLessThan(Integer value) {
            addCriterion("mini_game_id <", value, "miniGameId");
            return (Criteria) this;
        }

        public Criteria andMiniGameIdLessThanOrEqualTo(Integer value) {
            addCriterion("mini_game_id <=", value, "miniGameId");
            return (Criteria) this;
        }

        public Criteria andMiniGameIdIn(List<Integer> values) {
            addCriterion("mini_game_id in", values, "miniGameId");
            return (Criteria) this;
        }

        public Criteria andMiniGameIdNotIn(List<Integer> values) {
            addCriterion("mini_game_id not in", values, "miniGameId");
            return (Criteria) this;
        }

        public Criteria andMiniGameIdBetween(Integer value1, Integer value2) {
            addCriterion("mini_game_id between", value1, value2, "miniGameId");
            return (Criteria) this;
        }

        public Criteria andMiniGameIdNotBetween(Integer value1, Integer value2) {
            addCriterion("mini_game_id not between", value1, value2, "miniGameId");
            return (Criteria) this;
        }

        public Criteria andBusinessToolTypeIsNull() {
            addCriterion("business_tool_type is null");
            return (Criteria) this;
        }

        public Criteria andBusinessToolTypeIsNotNull() {
            addCriterion("business_tool_type is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessToolTypeEqualTo(Integer value) {
            addCriterion("business_tool_type =", value, "businessToolType");
            return (Criteria) this;
        }

        public Criteria andBusinessToolTypeNotEqualTo(Integer value) {
            addCriterion("business_tool_type <>", value, "businessToolType");
            return (Criteria) this;
        }

        public Criteria andBusinessToolTypeGreaterThan(Integer value) {
            addCriterion("business_tool_type >", value, "businessToolType");
            return (Criteria) this;
        }

        public Criteria andBusinessToolTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("business_tool_type >=", value, "businessToolType");
            return (Criteria) this;
        }

        public Criteria andBusinessToolTypeLessThan(Integer value) {
            addCriterion("business_tool_type <", value, "businessToolType");
            return (Criteria) this;
        }

        public Criteria andBusinessToolTypeLessThanOrEqualTo(Integer value) {
            addCriterion("business_tool_type <=", value, "businessToolType");
            return (Criteria) this;
        }

        public Criteria andBusinessToolTypeIn(List<Integer> values) {
            addCriterion("business_tool_type in", values, "businessToolType");
            return (Criteria) this;
        }

        public Criteria andBusinessToolTypeNotIn(List<Integer> values) {
            addCriterion("business_tool_type not in", values, "businessToolType");
            return (Criteria) this;
        }

        public Criteria andBusinessToolTypeBetween(Integer value1, Integer value2) {
            addCriterion("business_tool_type between", value1, value2, "businessToolType");
            return (Criteria) this;
        }

        public Criteria andBusinessToolTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("business_tool_type not between", value1, value2, "businessToolType");
            return (Criteria) this;
        }

        public Criteria andIosConversionUrlIsNull() {
            addCriterion("ios_conversion_url is null");
            return (Criteria) this;
        }

        public Criteria andIosConversionUrlIsNotNull() {
            addCriterion("ios_conversion_url is not null");
            return (Criteria) this;
        }

        public Criteria andIosConversionUrlEqualTo(String value) {
            addCriterion("ios_conversion_url =", value, "iosConversionUrl");
            return (Criteria) this;
        }

        public Criteria andIosConversionUrlNotEqualTo(String value) {
            addCriterion("ios_conversion_url <>", value, "iosConversionUrl");
            return (Criteria) this;
        }

        public Criteria andIosConversionUrlGreaterThan(String value) {
            addCriterion("ios_conversion_url >", value, "iosConversionUrl");
            return (Criteria) this;
        }

        public Criteria andIosConversionUrlGreaterThanOrEqualTo(String value) {
            addCriterion("ios_conversion_url >=", value, "iosConversionUrl");
            return (Criteria) this;
        }

        public Criteria andIosConversionUrlLessThan(String value) {
            addCriterion("ios_conversion_url <", value, "iosConversionUrl");
            return (Criteria) this;
        }

        public Criteria andIosConversionUrlLessThanOrEqualTo(String value) {
            addCriterion("ios_conversion_url <=", value, "iosConversionUrl");
            return (Criteria) this;
        }

        public Criteria andIosConversionUrlLike(String value) {
            addCriterion("ios_conversion_url like", value, "iosConversionUrl");
            return (Criteria) this;
        }

        public Criteria andIosConversionUrlNotLike(String value) {
            addCriterion("ios_conversion_url not like", value, "iosConversionUrl");
            return (Criteria) this;
        }

        public Criteria andIosConversionUrlIn(List<String> values) {
            addCriterion("ios_conversion_url in", values, "iosConversionUrl");
            return (Criteria) this;
        }

        public Criteria andIosConversionUrlNotIn(List<String> values) {
            addCriterion("ios_conversion_url not in", values, "iosConversionUrl");
            return (Criteria) this;
        }

        public Criteria andIosConversionUrlBetween(String value1, String value2) {
            addCriterion("ios_conversion_url between", value1, value2, "iosConversionUrl");
            return (Criteria) this;
        }

        public Criteria andIosConversionUrlNotBetween(String value1, String value2) {
            addCriterion("ios_conversion_url not between", value1, value2, "iosConversionUrl");
            return (Criteria) this;
        }

        public Criteria andAndroidConversionUrlIsNull() {
            addCriterion("android_conversion_url is null");
            return (Criteria) this;
        }

        public Criteria andAndroidConversionUrlIsNotNull() {
            addCriterion("android_conversion_url is not null");
            return (Criteria) this;
        }

        public Criteria andAndroidConversionUrlEqualTo(String value) {
            addCriterion("android_conversion_url =", value, "androidConversionUrl");
            return (Criteria) this;
        }

        public Criteria andAndroidConversionUrlNotEqualTo(String value) {
            addCriterion("android_conversion_url <>", value, "androidConversionUrl");
            return (Criteria) this;
        }

        public Criteria andAndroidConversionUrlGreaterThan(String value) {
            addCriterion("android_conversion_url >", value, "androidConversionUrl");
            return (Criteria) this;
        }

        public Criteria andAndroidConversionUrlGreaterThanOrEqualTo(String value) {
            addCriterion("android_conversion_url >=", value, "androidConversionUrl");
            return (Criteria) this;
        }

        public Criteria andAndroidConversionUrlLessThan(String value) {
            addCriterion("android_conversion_url <", value, "androidConversionUrl");
            return (Criteria) this;
        }

        public Criteria andAndroidConversionUrlLessThanOrEqualTo(String value) {
            addCriterion("android_conversion_url <=", value, "androidConversionUrl");
            return (Criteria) this;
        }

        public Criteria andAndroidConversionUrlLike(String value) {
            addCriterion("android_conversion_url like", value, "androidConversionUrl");
            return (Criteria) this;
        }

        public Criteria andAndroidConversionUrlNotLike(String value) {
            addCriterion("android_conversion_url not like", value, "androidConversionUrl");
            return (Criteria) this;
        }

        public Criteria andAndroidConversionUrlIn(List<String> values) {
            addCriterion("android_conversion_url in", values, "androidConversionUrl");
            return (Criteria) this;
        }

        public Criteria andAndroidConversionUrlNotIn(List<String> values) {
            addCriterion("android_conversion_url not in", values, "androidConversionUrl");
            return (Criteria) this;
        }

        public Criteria andAndroidConversionUrlBetween(String value1, String value2) {
            addCriterion("android_conversion_url between", value1, value2, "androidConversionUrl");
            return (Criteria) this;
        }

        public Criteria andAndroidConversionUrlNotBetween(String value1, String value2) {
            addCriterion("android_conversion_url not between", value1, value2, "androidConversionUrl");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}