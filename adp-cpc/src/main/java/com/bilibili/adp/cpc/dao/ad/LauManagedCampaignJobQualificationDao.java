package com.bilibili.adp.cpc.dao.ad;

import com.bilibili.adp.cpc.po.ad.LauManagedCampaignJobQualificationPo;
import com.bilibili.adp.cpc.po.ad.LauManagedCampaignJobQualificationPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface LauManagedCampaignJobQualificationDao {
    long countByExample(LauManagedCampaignJobQualificationPoExample example);

    int deleteByExample(LauManagedCampaignJobQualificationPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(LauManagedCampaignJobQualificationPo record);

    int insertBatch(List<LauManagedCampaignJobQualificationPo> records);

    int insertUpdateBatch(List<LauManagedCampaignJobQualificationPo> records);

    int insert(LauManagedCampaignJobQualificationPo record);

    int insertUpdateSelective(LauManagedCampaignJobQualificationPo record);

    int insertSelective(LauManagedCampaignJobQualificationPo record);

    List<LauManagedCampaignJobQualificationPo> selectByExample(LauManagedCampaignJobQualificationPoExample example);

    LauManagedCampaignJobQualificationPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") LauManagedCampaignJobQualificationPo record, @Param("example") LauManagedCampaignJobQualificationPoExample example);

    int updateByExample(@Param("record") LauManagedCampaignJobQualificationPo record, @Param("example") LauManagedCampaignJobQualificationPoExample example);

    int updateByPrimaryKeySelective(LauManagedCampaignJobQualificationPo record);

    int updateByPrimaryKey(LauManagedCampaignJobQualificationPo record);
}