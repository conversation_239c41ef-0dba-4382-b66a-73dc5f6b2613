package com.bilibili.adp.cpc.core.bos;

import com.bilibili.adp.cpc.vo.fly.MiddleFlyInvitationInfoVo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 程序化创意单元及下的创意列表
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UnitCreativeBo {
    // 单元id
    private Integer unitId;
    // 流量类型
    private Integer channelId;
    // 是否优选
    private Boolean isPreferScene;
    // 选择的场景
    private List<Integer> sceneIds;
    // 单元上绑定的稿件id
    private Long videoId;
    // 品牌标
    private Integer brandInfoId;
    // 品牌空间
    private Long spaceMid;
    // 监控链接
    private UnitMonitorBo monitoring;
    // 创意分类
    private UnitBusinessCategoryBo businessCategory;
    // 旧广告标
    private Integer cmMarkId;
    // 标签
    private List<String> tags;
    // 是否程序化创意
    private Boolean isProgrammatic;
    // 创意列表
    private List<CreativeBo> creatives;
    // 邀约组件信息
    private List<MiddleFlyInvitationInfoVo> middleFlyInvitationInfoVos;
    private Integer isSmartMaterial;
}
