package com.bilibili.adp.cpc.biz.services.danmaku.api;

import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.cpc.biz.services.danmaku.bos.SelfDanmakuMaterialBo;
import com.bilibili.adp.cpc.biz.services.danmaku.bos.SelfDanmakuMaterialSaveBo;
import com.bilibili.adp.cpc.biz.services.danmaku.bos.SelfDanmakuPicBo;
import com.bilibili.adp.cpc.biz.services.danmaku.bos.query.QuerySelfDanmakuMaterialBo;
import com.bilibili.adp.launch.api.creative.dto.BfsFile;

import java.util.List;

/**
 * @ClassName ISelfDanmakuMaterialService
 * <AUTHOR>
 * @Date 2024/1/2 2:04 下午
 * @Version 1.0
 **/
public interface ISelfDanmakuMaterialService {

    List<SelfDanmakuMaterialBo> queryForList(QuerySelfDanmakuMaterialBo queryBo);

    PageResult<SelfDanmakuMaterialBo> queryForPage(QuerySelfDanmakuMaterialBo queryBo);

    void batchInsertMaterialListFromGroup(List<SelfDanmakuMaterialSaveBo> materialInsertBoList, int accountId);

    void batchInsertDanmakuMaterial(List<SelfDanmakuMaterialSaveBo> insertBoList, int accountId);

    Long updateDanmakuMaterial(SelfDanmakuMaterialSaveBo updateBo, int accountId);

    void deleteByAccountIdAndMaterialIds(int accountId, List<Long> danmakuMaterialIdList);

    SelfDanmakuPicBo uploadPic(BfsFile bfsFile);
}
