package com.bilibili.adp.cpc.biz.services.creative;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

@Retention(RetentionPolicy.RUNTIME)
public @interface CpcExcelResources {
    /**
     * 属性的标题名称
     * @return
     */
    String title();
    /**
     * 在excel的顺序
     * @return
     */
    int order() default 9999;

    /**
     * 当该列为空时跳过它
     */
    boolean skipIfEmpty() default true;

    /**
     * 动态显示时，是否一直显示
     *
     * @return
     */
    boolean alwaysShow() default false;
}
