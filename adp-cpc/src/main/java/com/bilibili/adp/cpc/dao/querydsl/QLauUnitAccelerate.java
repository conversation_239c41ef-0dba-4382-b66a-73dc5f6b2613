package com.bilibili.adp.cpc.dao.querydsl;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.bilibili.adp.cpc.dao.querydsl.pos.LauUnitAcceleratePo;


import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;

import com.querydsl.sql.ColumnMetadata;
import java.sql.Types;




/**
 * QLauUnitAccelerate is a Querydsl query type for LauUnitAcceleratePo
 */
@Generated("com.querydsl.sql.codegen.MetaDataSerializer")
public class QLauUnitAccelerate extends com.querydsl.sql.RelationalPathBase<LauUnitAcceleratePo> {

    private static final long serialVersionUID = 321302331;

    public static final QLauUnitAccelerate lauUnitAccelerate = new QLauUnitAccelerate("lau_unit_accelerate");

    public final NumberPath<Long> accelerateBudget = createNumber("accelerateBudget", Long.class);

    public final DateTimePath<java.sql.Timestamp> accelerateEndTime = createDateTime("accelerateEndTime", java.sql.Timestamp.class);

    public final DateTimePath<java.sql.Timestamp> accelerateStartTime = createDateTime("accelerateStartTime", java.sql.Timestamp.class);

    public final NumberPath<Integer> accelerateStatus = createNumber("accelerateStatus", Integer.class);

    public final NumberPath<Integer> accelerateType = createNumber("accelerateType", Integer.class);

    public final DateTimePath<java.sql.Timestamp> ctime = createDateTime("ctime", java.sql.Timestamp.class);

    public final NumberPath<Integer> id = createNumber("id", Integer.class);

    public final NumberPath<Integer> isDeleted = createNumber("isDeleted", Integer.class);

    public final DateTimePath<java.sql.Timestamp> mtime = createDateTime("mtime", java.sql.Timestamp.class);

    public final NumberPath<Integer> unitId = createNumber("unitId", Integer.class);

    public final com.querydsl.sql.PrimaryKey<LauUnitAcceleratePo> primary = createPrimaryKey(id);

    public QLauUnitAccelerate(String variable) {
        super(LauUnitAcceleratePo.class, forVariable(variable), "null", "lau_unit_accelerate");
        addMetadata();
    }

    public QLauUnitAccelerate(String variable, String schema, String table) {
        super(LauUnitAcceleratePo.class, forVariable(variable), schema, table);
        addMetadata();
    }

    public QLauUnitAccelerate(String variable, String schema) {
        super(LauUnitAcceleratePo.class, forVariable(variable), schema, "lau_unit_accelerate");
        addMetadata();
    }

    public QLauUnitAccelerate(Path<? extends LauUnitAcceleratePo> path) {
        super(path.getType(), path.getMetadata(), "null", "lau_unit_accelerate");
        addMetadata();
    }

    public QLauUnitAccelerate(PathMetadata metadata) {
        super(LauUnitAcceleratePo.class, metadata, "null", "lau_unit_accelerate");
        addMetadata();
    }

    public void addMetadata() {
        addMetadata(accelerateBudget, ColumnMetadata.named("accelerate_budget").withIndex(3).ofType(Types.BIGINT).withSize(19).notNull());
        addMetadata(accelerateEndTime, ColumnMetadata.named("accelerate_end_time").withIndex(7).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(accelerateStartTime, ColumnMetadata.named("accelerate_start_time").withIndex(6).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(accelerateStatus, ColumnMetadata.named("accelerate_status").withIndex(5).ofType(Types.TINYINT).withSize(3).notNull());
        addMetadata(accelerateType, ColumnMetadata.named("accelerate_type").withIndex(4).ofType(Types.TINYINT).withSize(3).notNull());
        addMetadata(ctime, ColumnMetadata.named("ctime").withIndex(8).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(id, ColumnMetadata.named("id").withIndex(1).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(isDeleted, ColumnMetadata.named("is_deleted").withIndex(10).ofType(Types.TINYINT).withSize(3).notNull());
        addMetadata(mtime, ColumnMetadata.named("mtime").withIndex(9).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(unitId, ColumnMetadata.named("unit_id").withIndex(2).ofType(Types.INTEGER).withSize(10).notNull());
    }

}

