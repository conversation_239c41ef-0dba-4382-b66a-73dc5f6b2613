package com.bilibili.adp.cpc.biz.services.ad_product.dto;

import com.bilibili.adp.common.annotation.DatabaseColumnName;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class SdpaProductECommerceExtraDto {

    @DatabaseColumnName("spu码")
    private Long spuCode;

    private String spuName;

    @DatabaseColumnName("商品来源")
    private Integer platformName;

    @DatabaseColumnName("商品卖点")
    private List<String> remark;

    @DatabaseColumnName("店铺名称")
    private String storeName;

    @DatabaseColumnName("受众群体")
    private Integer age;

    @DatabaseColumnName("商品视频链接")
    private String videoLink;

    @DatabaseColumnName("原价")
    private String originalPrice;

    @DatabaseColumnName("折扣价")
    private Long discount;

    @DatabaseColumnName("IOS应用调起ulink链接")
    private String iosUpUlinkUrl;

}
