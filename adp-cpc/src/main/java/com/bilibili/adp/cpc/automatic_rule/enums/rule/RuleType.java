package com.bilibili.adp.cpc.automatic_rule.enums.rule;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum RuleType {
    DELIVERY_AUTOMATIC_OPTIMIZATION(1, "投放自动优化"),
    TIME_SHARING_BUDGET_BID_ADJUSTMENT(2, "分时预算出价调整"),
    ;
    private final int code;
    private final String desc;

    public static RuleType getByCode(int code) {
        return Arrays.stream(values())
                .filter(ruleType -> ruleType.getCode() == code)
                .findFirst()
                .orElseThrow(IllegalArgumentException::new);

    }
}
