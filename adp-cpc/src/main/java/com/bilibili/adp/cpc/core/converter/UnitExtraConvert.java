package com.bilibili.adp.cpc.core.converter;

import com.bilibili.adp.cpc.biz.services.unit.dto.CpcUnitExtraDto;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitExtraPo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper
public interface UnitExtraConvert {
    UnitExtraConvert MAPPER = Mappers.getMapper(UnitExtraConvert.class);

    // 不为2，说明都是旧版（1）
    // 因为有些unit_extra的值是0，但是还没写入db
    // 新版本的generalVersion肯定是2
    @Mapping(target="generalVersion", expression = "java(po.getGeneralVersion().equals(2)?2:1)")
    CpcUnitExtraDto toDto(LauUnitExtraPo po);
}
