package com.bilibili.adp.cpc.dao.ad_core.querydsl;

import static com.querydsl.core.types.PathMetadataFactory.*;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauCreativeComponentPo;


import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;

import com.querydsl.sql.ColumnMetadata;
import java.sql.Types;




/**
 * QLauCreativeComponent is a Querydsl query type for LauCreativeComponentPo
 */
@Generated("com.querydsl.sql.codegen.MetaDataSerializer")
public class QLauCreativeComponent extends com.querydsl.sql.RelationalPathBase<LauCreativeComponentPo> {

    private static final long serialVersionUID = *********;

    public static final QLauCreativeComponent lauCreativeComponent = new QLauCreativeComponent("lau_creative_component");

    public final NumberPath<Integer> accountId = createNumber("accountId", Integer.class);

    public final NumberPath<Integer> campaignId = createNumber("campaignId", Integer.class);

    public final NumberPath<Long> componentId = createNumber("componentId", Long.class);

    public final NumberPath<Integer> componentType = createNumber("componentType", Integer.class);

    public final NumberPath<Integer> creativeId = createNumber("creativeId", Integer.class);

    public final DateTimePath<java.sql.Timestamp> ctime = createDateTime("ctime", java.sql.Timestamp.class);

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final NumberPath<Integer> isDeleted = createNumber("isDeleted", Integer.class);

    public final DateTimePath<java.sql.Timestamp> mtime = createDateTime("mtime", java.sql.Timestamp.class);

    public final NumberPath<Integer> unitId = createNumber("unitId", Integer.class);

    public final com.querydsl.sql.PrimaryKey<LauCreativeComponentPo> primary = createPrimaryKey(id);

    public QLauCreativeComponent(String variable) {
        super(LauCreativeComponentPo.class, forVariable(variable), "null", "lau_creative_component");
        addMetadata();
    }

    public QLauCreativeComponent(String variable, String schema, String table) {
        super(LauCreativeComponentPo.class, forVariable(variable), schema, table);
        addMetadata();
    }

    public QLauCreativeComponent(String variable, String schema) {
        super(LauCreativeComponentPo.class, forVariable(variable), schema, "lau_creative_component");
        addMetadata();
    }

    public QLauCreativeComponent(Path<? extends LauCreativeComponentPo> path) {
        super(path.getType(), path.getMetadata(), "null", "lau_creative_component");
        addMetadata();
    }

    public QLauCreativeComponent(PathMetadata metadata) {
        super(LauCreativeComponentPo.class, metadata, "null", "lau_creative_component");
        addMetadata();
    }

    public void addMetadata() {
        addMetadata(accountId, ColumnMetadata.named("account_id").withIndex(5).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(campaignId, ColumnMetadata.named("campaign_id").withIndex(6).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(componentId, ColumnMetadata.named("component_id").withIndex(10).ofType(Types.BIGINT).withSize(19).notNull());
        addMetadata(componentType, ColumnMetadata.named("component_type").withIndex(9).ofType(Types.TINYINT).withSize(3).notNull());
        addMetadata(creativeId, ColumnMetadata.named("creative_id").withIndex(8).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(ctime, ColumnMetadata.named("ctime").withIndex(2).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(id, ColumnMetadata.named("id").withIndex(1).ofType(Types.BIGINT).withSize(19).notNull());
        addMetadata(isDeleted, ColumnMetadata.named("is_deleted").withIndex(4).ofType(Types.TINYINT).withSize(3).notNull());
        addMetadata(mtime, ColumnMetadata.named("mtime").withIndex(3).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(unitId, ColumnMetadata.named("unit_id").withIndex(7).ofType(Types.INTEGER).withSize(10).notNull());
    }

}

