package com.bilibili.adp.cpc.dao.ad;

import com.bilibili.adp.cpc.po.ad.LauCreativeInvitationPo;
import com.bilibili.adp.cpc.po.ad.LauCreativeInvitationPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface LauCreativeInvitationDao {
    long countByExample(LauCreativeInvitationPoExample example);

    int deleteByExample(LauCreativeInvitationPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(LauCreativeInvitationPo record);

    int insertBatch(List<LauCreativeInvitationPo> records);

    int insertUpdateBatch(List<LauCreativeInvitationPo> records);

    int insert(LauCreativeInvitationPo record);

    int insertUpdateSelective(LauCreativeInvitationPo record);

    int insertSelective(LauCreativeInvitationPo record);

    List<LauCreativeInvitationPo> selectByExample(LauCreativeInvitationPoExample example);

    LauCreativeInvitationPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") LauCreativeInvitationPo record, @Param("example") LauCreativeInvitationPoExample example);

    int updateByExample(@Param("record") LauCreativeInvitationPo record, @Param("example") LauCreativeInvitationPoExample example);

    int updateByPrimaryKeySelective(LauCreativeInvitationPo record);

    int updateByPrimaryKey(LauCreativeInvitationPo record);
}