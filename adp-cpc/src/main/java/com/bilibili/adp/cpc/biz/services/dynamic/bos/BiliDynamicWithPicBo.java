package com.bilibili.adp.cpc.biz.services.dynamic.bos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
public class BiliDynamicWithPicBo {
    private Long dyn_id;
    private Integer type;
    private String title;
    private String cover;
    private Long rid;
    private String state;

    private DynamicMidResponseBo owner;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public class DynamicMidResponseBo {

        private Long uid;

        private String name;

        private String face;

    }

}

