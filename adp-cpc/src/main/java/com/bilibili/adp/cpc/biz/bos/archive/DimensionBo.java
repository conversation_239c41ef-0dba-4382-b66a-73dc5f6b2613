package com.bilibili.adp.cpc.biz.bos.archive;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DimensionBo {
    /**
     * 宽度
     */
    private Long width;
    /**
     * 高度
     */
    private Long height;
    /**
     * 是否翻转
     */
    private Long rotate;
}
