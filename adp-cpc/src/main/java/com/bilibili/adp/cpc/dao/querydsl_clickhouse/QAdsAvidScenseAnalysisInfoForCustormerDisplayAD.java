package com.bilibili.adp.cpc.dao.querydsl_clickhouse;

import static com.querydsl.core.types.PathMetadataFactory.*;
import com.bilibili.adp.cpc.dao.querydsl_clickhouse.pos.AdsAvidScenseAnalysisInfoForCustormerDisplayADPo;


import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;

import com.querydsl.sql.ColumnMetadata;
import java.sql.Types;




/**
 * QAdsAvidScenseAnalysisInfoForCustormerDisplayAD is a Querydsl query type for AdsAvidScenseAnalysisInfoForCustormerDisplayADPo
 */
@Generated("com.querydsl.sql.codegen.MetaDataSerializer")
public class QAdsAvidScenseAnalysisInfoForCustormerDisplayAD extends com.querydsl.sql.RelationalPathBase<AdsAvidScenseAnalysisInfoForCustormerDisplayADPo> {

    private static final long serialVersionUID = 235268840;

    public static final QAdsAvidScenseAnalysisInfoForCustormerDisplayAD adsAvidScenseAnalysisInfoForCustormerDisplayAD = new QAdsAvidScenseAnalysisInfoForCustormerDisplayAD("ads_avid_scense_analysis_info_for_custormer_display_a_d");

    public final NumberPath<Long> actionValid = createNumber("actionValid", Long.class);

    public final NumberPath<Long> androidAppFirstActive = createNumber("androidAppFirstActive", Long.class);

    public final NumberPath<Long> androidDownloadSuccess = createNumber("androidDownloadSuccess", Long.class);

    public final NumberPath<Long> androidInstallSuccess = createNumber("androidInstallSuccess", Long.class);

    public final NumberPath<Long> appCallup = createNumber("appCallup", Long.class);

    public final NumberPath<Long> appFirstActive = createNumber("appFirstActive", Long.class);

    public final StringPath avid = createString("avid");

    public final StringPath avidScense = createString("avidScense");

    public final StringPath bvid = createString("bvid");

    public final NumberPath<Long> callupSuc = createNumber("callupSuc", Long.class);

    public final NumberPath<Long> click = createNumber("click", Long.class);

    public final NumberPath<Long> clue = createNumber("clue", Long.class);

    public final NumberPath<Long> coin = createNumber("coin", Long.class);

    public final NumberPath<Long> coinDaily = createNumber("coinDaily", Long.class);

    public final NumberPath<Long> costMilli = createNumber("costMilli", Long.class);

    public final StringPath creativeId = createString("creativeId");

    public final NumberPath<Long> danmu = createNumber("danmu", Long.class);

    public final NumberPath<Long> danmuDaily = createNumber("danmuDaily", Long.class);

    public final NumberPath<Long> downloadSuccess = createNumber("downloadSuccess", Long.class);

    public final NumberPath<Long> fav = createNumber("fav", Long.class);

    public final NumberPath<Long> favDaily = createNumber("favDaily", Long.class);

    public final NumberPath<Long> firstOrderPlace = createNumber("firstOrderPlace", Long.class);

    public final NumberPath<Double> firstOrderPlaceValue = createNumber("firstOrderPlaceValue", Double.class);

    public final NumberPath<Long> formSubmit = createNumber("formSubmit", Long.class);

    public final NumberPath<Long> formUserCost = createNumber("formUserCost", Long.class);

    public final NumberPath<Long> gameActiveApi = createNumber("gameActiveApi", Long.class);

    public final NumberPath<Long> gameSubscribeApi = createNumber("gameSubscribeApi", Long.class);

    public final NumberPath<Long> gameUserCost = createNumber("gameUserCost", Long.class);

    public final NumberPath<Double> gameUserCostValue = createNumber("gameUserCostValue", Double.class);

    public final NumberPath<Long> gameUserFirstCost = createNumber("gameUserFirstCost", Long.class);

    public final NumberPath<Double> gameUserFirstCostValue = createNumber("gameUserFirstCostValue", Double.class);

    public final NumberPath<Long> installSuccess = createNumber("installSuccess", Long.class);

    public final NumberPath<Long> iosAppFirstActive = createNumber("iosAppFirstActive", Long.class);

    public final NumberPath<Long> iosDownloadSuccess = createNumber("iosDownloadSuccess", Long.class);

    public final NumberPath<Long> iosInstallSuccess = createNumber("iosInstallSuccess", Long.class);

    public final NumberPath<Long> likes = createNumber("likes", Long.class);

    public final NumberPath<Long> likesDaily = createNumber("likesDaily", Long.class);

    public final DatePath<java.sql.Date> logDate = createDate("logDate", java.sql.Date.class);

    public final NumberPath<Long> lpCallup = createNumber("lpCallup", Long.class);

    public final NumberPath<Long> lpCallupSuccStay = createNumber("lpCallupSuccStay", Long.class);

    public final NumberPath<Long> ocpcTarget = createNumber("ocpcTarget", Long.class);

    public final NumberPath<Long> orderPlace = createNumber("orderPlace", Long.class);

    public final NumberPath<Double> orderPlaceValue = createNumber("orderPlaceValue", Double.class);

    public final StringPath planId = createString("planId");

    public final NumberPath<Long> play = createNumber("play", Long.class);

    public final NumberPath<Long> play10sCnt = createNumber("play10sCnt", Long.class);

    public final NumberPath<Long> play3sCnt = createNumber("play3sCnt", Long.class);

    public final NumberPath<Long> play5sCnt = createNumber("play5sCnt", Long.class);

    public final NumberPath<Long> playCnt = createNumber("playCnt", Long.class);

    public final NumberPath<Long> playDaily = createNumber("playDaily", Long.class);

    public final NumberPath<Long> pv = createNumber("pv", Long.class);

    public final NumberPath<Long> reply = createNumber("reply", Long.class);

    public final NumberPath<Long> replyDaily = createNumber("replyDaily", Long.class);

    public final NumberPath<Long> retention = createNumber("retention", Long.class);

    public final NumberPath<Long> share = createNumber("share", Long.class);

    public final NumberPath<Long> shareDaily = createNumber("shareDaily", Long.class);

    public final StringPath unitId = createString("unitId");

    public final NumberPath<Long> userCost = createNumber("userCost", Long.class);

    public final NumberPath<Double> userCostValue = createNumber("userCostValue", Double.class);

    public final NumberPath<Long> userFirstCost = createNumber("userFirstCost", Long.class);

    public final NumberPath<Double> userFirstCostValue = createNumber("userFirstCostValue", Double.class);

    public final StringPath userId = createString("userId");

    public final NumberPath<Long> userRegister = createNumber("userRegister", Long.class);

    public QAdsAvidScenseAnalysisInfoForCustormerDisplayAD(String variable) {
        super(AdsAvidScenseAnalysisInfoForCustormerDisplayADPo.class, forVariable(variable), "null", "ads_avid_scense_analysis_info_for_custormer_display_a_d");
        addMetadata();
    }

    public QAdsAvidScenseAnalysisInfoForCustormerDisplayAD(String variable, String schema, String table) {
        super(AdsAvidScenseAnalysisInfoForCustormerDisplayADPo.class, forVariable(variable), schema, table);
        addMetadata();
    }

    public QAdsAvidScenseAnalysisInfoForCustormerDisplayAD(String variable, String schema) {
        super(AdsAvidScenseAnalysisInfoForCustormerDisplayADPo.class, forVariable(variable), schema, "ads_avid_scense_analysis_info_for_custormer_display_a_d");
        addMetadata();
    }

    public QAdsAvidScenseAnalysisInfoForCustormerDisplayAD(Path<? extends AdsAvidScenseAnalysisInfoForCustormerDisplayADPo> path) {
        super(path.getType(), path.getMetadata(), "null", "ads_avid_scense_analysis_info_for_custormer_display_a_d");
        addMetadata();
    }

    public QAdsAvidScenseAnalysisInfoForCustormerDisplayAD(PathMetadata metadata) {
        super(AdsAvidScenseAnalysisInfoForCustormerDisplayADPo.class, metadata, "null", "ads_avid_scense_analysis_info_for_custormer_display_a_d");
        addMetadata();
    }

    public void addMetadata() {
        addMetadata(actionValid, ColumnMetadata.named("action_valid").withIndex(32).ofType(Types.BIGINT).withSize(19));
        addMetadata(androidAppFirstActive, ColumnMetadata.named("android_app_first_active").withIndex(59).ofType(Types.BIGINT).withSize(19));
        addMetadata(androidDownloadSuccess, ColumnMetadata.named("android_download_success").withIndex(61).ofType(Types.BIGINT).withSize(19));
        addMetadata(androidInstallSuccess, ColumnMetadata.named("android_install_success").withIndex(63).ofType(Types.BIGINT).withSize(19));
        addMetadata(appCallup, ColumnMetadata.named("app_callup").withIndex(25).ofType(Types.BIGINT).withSize(19));
        addMetadata(appFirstActive, ColumnMetadata.named("app_first_active").withIndex(16).ofType(Types.BIGINT).withSize(19));
        addMetadata(avid, ColumnMetadata.named("avid").withIndex(2).ofType(Types.VARCHAR).withSize(128));
        addMetadata(avidScense, ColumnMetadata.named("avid_scense").withIndex(1).ofType(Types.VARCHAR).withSize(128));
        addMetadata(bvid, ColumnMetadata.named("bvid").withIndex(3).ofType(Types.VARCHAR).withSize(128));
        addMetadata(callupSuc, ColumnMetadata.named("callup_suc").withIndex(27).ofType(Types.BIGINT).withSize(19));
        addMetadata(click, ColumnMetadata.named("click").withIndex(10).ofType(Types.BIGINT).withSize(19));
        addMetadata(clue, ColumnMetadata.named("clue").withIndex(23).ofType(Types.BIGINT).withSize(19));
        addMetadata(coin, ColumnMetadata.named("coin").withIndex(38).ofType(Types.BIGINT).withSize(19));
        addMetadata(coinDaily, ColumnMetadata.named("coin_daily").withIndex(45).ofType(Types.BIGINT).withSize(19));
        addMetadata(costMilli, ColumnMetadata.named("cost_milli").withIndex(11).ofType(Types.BIGINT).withSize(19));
        addMetadata(creativeId, ColumnMetadata.named("creative_id").withIndex(4).ofType(Types.VARCHAR).withSize(128));
        addMetadata(danmu, ColumnMetadata.named("danmu").withIndex(39).ofType(Types.BIGINT).withSize(19));
        addMetadata(danmuDaily, ColumnMetadata.named("danmu_daily").withIndex(46).ofType(Types.BIGINT).withSize(19));
        addMetadata(downloadSuccess, ColumnMetadata.named("download_success").withIndex(51).ofType(Types.BIGINT).withSize(19));
        addMetadata(fav, ColumnMetadata.named("fav").withIndex(37).ofType(Types.BIGINT).withSize(19));
        addMetadata(favDaily, ColumnMetadata.named("fav_daily").withIndex(44).ofType(Types.BIGINT).withSize(19));
        addMetadata(firstOrderPlace, ColumnMetadata.named("first_order_place").withIndex(31).ofType(Types.BIGINT).withSize(19));
        addMetadata(firstOrderPlaceValue, ColumnMetadata.named("first_order_place_value").withIndex(34).ofType(Types.DOUBLE).withSize(22));
        addMetadata(formSubmit, ColumnMetadata.named("form_submit").withIndex(17).ofType(Types.BIGINT).withSize(19));
        addMetadata(formUserCost, ColumnMetadata.named("form_user_cost").withIndex(26).ofType(Types.BIGINT).withSize(19));
        addMetadata(gameActiveApi, ColumnMetadata.named("game_active_api").withIndex(22).ofType(Types.BIGINT).withSize(19));
        addMetadata(gameSubscribeApi, ColumnMetadata.named("game_subscribe_api").withIndex(52).ofType(Types.BIGINT).withSize(19));
        addMetadata(gameUserCost, ColumnMetadata.named("game_user_cost").withIndex(29).ofType(Types.BIGINT).withSize(19));
        addMetadata(gameUserCostValue, ColumnMetadata.named("game_user_cost_value").withIndex(56).ofType(Types.DOUBLE).withSize(22));
        addMetadata(gameUserFirstCost, ColumnMetadata.named("game_user_first_cost").withIndex(30).ofType(Types.BIGINT).withSize(19));
        addMetadata(gameUserFirstCostValue, ColumnMetadata.named("game_user_first_cost_value").withIndex(57).ofType(Types.DOUBLE).withSize(22));
        addMetadata(installSuccess, ColumnMetadata.named("install_success").withIndex(19).ofType(Types.BIGINT).withSize(19));
        addMetadata(iosAppFirstActive, ColumnMetadata.named("ios_app_first_active").withIndex(58).ofType(Types.BIGINT).withSize(19));
        addMetadata(iosDownloadSuccess, ColumnMetadata.named("ios_download_success").withIndex(60).ofType(Types.BIGINT).withSize(19));
        addMetadata(iosInstallSuccess, ColumnMetadata.named("ios_install_success").withIndex(62).ofType(Types.BIGINT).withSize(19));
        addMetadata(likes, ColumnMetadata.named("likes").withIndex(41).ofType(Types.BIGINT).withSize(19));
        addMetadata(likesDaily, ColumnMetadata.named("likes_daily").withIndex(48).ofType(Types.BIGINT).withSize(19));
        addMetadata(logDate, ColumnMetadata.named("log_date").withIndex(49).ofType(Types.DATE).withSize(10));
        addMetadata(lpCallup, ColumnMetadata.named("lp_callup").withIndex(50).ofType(Types.BIGINT).withSize(19));
        addMetadata(lpCallupSuccStay, ColumnMetadata.named("lp_callup_succ_stay").withIndex(28).ofType(Types.BIGINT).withSize(19));
        addMetadata(ocpcTarget, ColumnMetadata.named("ocpc_target").withIndex(8).ofType(Types.BIGINT).withSize(19));
        addMetadata(orderPlace, ColumnMetadata.named("order_place").withIndex(18).ofType(Types.BIGINT).withSize(19));
        addMetadata(orderPlaceValue, ColumnMetadata.named("order_place_value").withIndex(33).ofType(Types.DOUBLE).withSize(22));
        addMetadata(planId, ColumnMetadata.named("plan_id").withIndex(6).ofType(Types.VARCHAR).withSize(128));
        addMetadata(play, ColumnMetadata.named("play").withIndex(35).ofType(Types.BIGINT).withSize(19));
        addMetadata(play10sCnt, ColumnMetadata.named("play_10s_cnt").withIndex(14).ofType(Types.BIGINT).withSize(19));
        addMetadata(play3sCnt, ColumnMetadata.named("play_3s_cnt").withIndex(12).ofType(Types.BIGINT).withSize(19));
        addMetadata(play5sCnt, ColumnMetadata.named("play_5s_cnt").withIndex(13).ofType(Types.BIGINT).withSize(19));
        addMetadata(playCnt, ColumnMetadata.named("play_cnt").withIndex(15).ofType(Types.BIGINT).withSize(19));
        addMetadata(playDaily, ColumnMetadata.named("play_daily").withIndex(42).ofType(Types.BIGINT).withSize(19));
        addMetadata(pv, ColumnMetadata.named("pv").withIndex(9).ofType(Types.BIGINT).withSize(19));
        addMetadata(reply, ColumnMetadata.named("reply").withIndex(36).ofType(Types.BIGINT).withSize(19));
        addMetadata(replyDaily, ColumnMetadata.named("reply_daily").withIndex(43).ofType(Types.BIGINT).withSize(19));
        addMetadata(retention, ColumnMetadata.named("retention").withIndex(24).ofType(Types.BIGINT).withSize(19));
        addMetadata(share, ColumnMetadata.named("share").withIndex(40).ofType(Types.BIGINT).withSize(19));
        addMetadata(shareDaily, ColumnMetadata.named("share_daily").withIndex(47).ofType(Types.BIGINT).withSize(19));
        addMetadata(unitId, ColumnMetadata.named("unit_id").withIndex(5).ofType(Types.VARCHAR).withSize(128));
        addMetadata(userCost, ColumnMetadata.named("user_cost").withIndex(21).ofType(Types.BIGINT).withSize(19));
        addMetadata(userCostValue, ColumnMetadata.named("user_cost_value").withIndex(54).ofType(Types.DOUBLE).withSize(22));
        addMetadata(userFirstCost, ColumnMetadata.named("user_first_cost").withIndex(53).ofType(Types.BIGINT).withSize(19));
        addMetadata(userFirstCostValue, ColumnMetadata.named("user_first_cost_value").withIndex(55).ofType(Types.DOUBLE).withSize(22));
        addMetadata(userId, ColumnMetadata.named("user_id").withIndex(7).ofType(Types.VARCHAR).withSize(128));
        addMetadata(userRegister, ColumnMetadata.named("user_register").withIndex(20).ofType(Types.BIGINT).withSize(19));
    }

}

