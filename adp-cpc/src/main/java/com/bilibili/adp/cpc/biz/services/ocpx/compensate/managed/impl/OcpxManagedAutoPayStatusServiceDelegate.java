package com.bilibili.adp.cpc.biz.services.ocpx.compensate.managed.impl;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.launch.api.launch.dto.ocpx.OcpxManagedAutoPayStatusDto;
import com.bilibili.adp.launch.biz.dao.OcpxManagedAutoCompensationStatusDao;
import com.bilibili.adp.launch.biz.pojo.OcpxManagedAutoCompensationStatusPo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.AD_TM;

@Service
public class OcpxManagedAutoPayStatusServiceDelegate {
    private static final Logger LOGGER = LoggerFactory.getLogger(OcpxManagedAutoPayStatusServiceDelegate.class);

    @Autowired
    private OcpxManagedAutoCompensationStatusDao autoCompensationStatusDao;

    @Transactional(value = AD_TM, rollbackFor = Exception.class)
    public void saveAutoPayStatus(List<OcpxManagedAutoPayStatusDto> ocpxAutoPayStatusDtoList) {
        LOGGER.info("OcpxManagedAutoPayStatusServiceDelegate.saveAutoPayStatus.size: [{}]", ocpxAutoPayStatusDtoList == null ? 0 : ocpxAutoPayStatusDtoList.size());

        if (!CollectionUtils.isEmpty(ocpxAutoPayStatusDtoList)) {
            for (OcpxManagedAutoPayStatusDto ocpxAutoPayStatusDto : ocpxAutoPayStatusDtoList) {
                OcpxManagedAutoCompensationStatusPo po = new OcpxManagedAutoCompensationStatusPo();
                BeanUtils.copyProperties(ocpxAutoPayStatusDto, po);
                if(ocpxAutoPayStatusDto.getPayDate()!=null) {
                    po.setPayDate(Utils.getTimestamp(ocpxAutoPayStatusDto.getPayDate().getTime()));
                }
                autoCompensationStatusDao.insertUpdateSelective(po);
            }
        }
    }
}
