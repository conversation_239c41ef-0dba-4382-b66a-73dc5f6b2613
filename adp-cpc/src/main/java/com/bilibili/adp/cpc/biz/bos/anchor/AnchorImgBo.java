package com.bilibili.adp.cpc.biz.bos.anchor;

import com.bilibili.adp.common.annotation.DatabaseColumnEnum;
import com.bilibili.adp.common.annotation.DatabaseColumnName;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class AnchorImgBo {

    @ApiModelProperty(value = "width")
    private Integer width;
    @ApiModelProperty(value = "height")
    private Integer height;
    private String url;
}
