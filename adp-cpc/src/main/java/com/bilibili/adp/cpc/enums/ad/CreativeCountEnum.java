package com.bilibili.adp.cpc.enums.ad;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * @ClassName CreativeCountEnum
 * @<PERSON> xuhaoyu
 * @Date 2024/11/18 7:16 下午
 * @Version 1.0
 **/
@AllArgsConstructor
@Getter
public enum CreativeCountEnum {

    OPEN_API(1, "open_api_creative_account_count:", "open_api_today_creative_account_count:"),
    SANLIAN(2, "sanlian_creative_account_count:", "sanlian_today_creative_account_count:"),
    ;

    private final Integer code;
    private final String totalRedisKeyPrefix;
    private final String todayRedisKeyPrefix;

    public static CreativeCountEnum getByCode(Integer code) {
        for (CreativeCountEnum creativeCountEnum : CreativeCountEnum.values()) {
            if (Objects.equals(code, creativeCountEnum.getCode())) {
                return creativeCountEnum;
            }
        }
        throw new IllegalArgumentException("unknown creativeCountEnum code:" + code);
    }

}
