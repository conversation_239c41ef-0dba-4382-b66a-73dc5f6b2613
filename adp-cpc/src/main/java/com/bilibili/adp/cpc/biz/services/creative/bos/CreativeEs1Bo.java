package com.bilibili.adp.cpc.biz.services.creative.bos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName CreativeBinlogDto
 * <AUTHOR>
 * @Date 2023/6/16 2:10 下午
 * @Version 1.0
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CreativeEs1Bo {

    private Integer creativeId;
    private Integer unitId;
    private Integer accountId;
    private Integer campaignId;
    private Long videoId;
    private Long dynamicId;
    private Long mgkPageId;

    private Integer status;
    private Integer auditStatus;
    private Integer creativeStatus;
    private Integer progAuditStatus;
    private Integer adpVersion;
    private Integer flag;
    private String imageMd5;

    private String beginTime;
    private String endTime;
    private Integer bilibiliUserId;
    private String title;
    private String creativeName;
    private String description;
    private String extDescription;

    private Integer isProgrammatic;
    private Integer isRecheck;
    private Integer isPageGroup;
    private Integer autoAuditFlag;
    private Integer salesType;
    private Integer styleAbility;
    private Integer creativeType;

    private Integer isDeleted;
    private String ctime;
    private String mtime;

}

