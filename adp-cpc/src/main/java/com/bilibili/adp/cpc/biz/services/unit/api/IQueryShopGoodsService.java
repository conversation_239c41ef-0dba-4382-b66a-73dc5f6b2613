package com.bilibili.adp.cpc.biz.services.unit.api;

import com.bilibili.adp.cpc.dto.DpaShopGoodsDto;
import com.bilibili.adp.launch.api.common.QueryShopGoodsParamDto;
import com.bilibili.adp.launch.api.unit.dto.ShopGoodsDto;
import com.bilibili.adp.launch.api.unit.param.QueryDpaShopGoodsParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/3/15
 **/
public interface IQueryShopGoodsService {
    ShopGoodsDto getShopGoodsByItemId(Long mid, Integer goodsId);

    List<ShopGoodsDto> getShopGoodsList(QueryShopGoodsParamDto queryShopGoodsParamDto);

    List<DpaShopGoodsDto> getDpaShopGoodsList(QueryDpaShopGoodsParam param);

    DpaShopGoodsDto getDpaShopGoodsByProductId(Integer productId);

    DpaShopGoodsDto getDpaShopGoodsByGoodsId(Integer shopGoodsId);
}
