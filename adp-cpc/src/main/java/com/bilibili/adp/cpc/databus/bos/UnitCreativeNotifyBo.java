package com.bilibili.adp.cpc.databus.bos;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class UnitCreativeNotifyBo {
    private Integer unitId;
    private Integer businessDomain;
    private Integer isProgrammatic;
    private List<Integer> creativeIds;
    private OperatorBo operator;
}
