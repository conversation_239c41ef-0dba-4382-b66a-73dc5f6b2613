package com.bilibili.adp.common.enums;

public enum VirtualCurrencyRechargeStatus {
    RECHARGING(1, "充值中"),
    RECHARGE_SUCCESS(2, "充值成功"),
    RECHARGE_FAIL(3, "充值失败"),

    ;

    private Integer code;
    private String desc;

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    VirtualCurrencyRechargeStatus(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static VirtualCurrencyRechargeStatus getByCode(Integer code) {
        if(code != null) {
            for (VirtualCurrencyRechargeStatus bean : values()) {
                if (bean.getCode() == code) {
                    return bean;
                }
            }
        }
        return null;
    }

    public static VirtualCurrencyRechargeStatus validateByCode(Integer code) {

        if(code != null) {
            for (VirtualCurrencyRechargeStatus bean : values()) {
                if (bean.getCode() == code) {
                    return bean;
                }
            }
        }
        throw new IllegalArgumentException("unknown VirtualCurrencyRechargeStatus code is " + code);
    }
}
