package com.bilibili.adp.common.enums;

/**
 * Created by many2023 on 2018/7/19.
 */
public enum UnitTargetFansRelation {
    FANS(1, "该UP主的粉丝"),
    NON_FANS(2, "未关注该UP主的人");

    private Integer code;
    private String desc;

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    UnitTargetFansRelation(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static UnitTargetFansRelation getByCode(int code) {
        for (UnitTargetFansRelation bean : values()) {
            if (bean.getCode() == code) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown UnitTargetFansRelation code.");
    }
}
