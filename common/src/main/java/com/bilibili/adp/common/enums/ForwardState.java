package com.bilibili.adp.common.enums;

import com.bilibili.adp.common.util.func.Functions;
import com.bilibili.adp.common.util.func.HasCommonKV;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Map;

@Getter
@AllArgsConstructor
public enum ForwardState implements HasCommonKV {
    ALLOWED(0, "允许"),
    FORBID(1, "禁止"),
    ;

    public static final Map<Integer, String> commonEnumKV = Functions.getCommonEnumKV(ForwardState.class);

    private final Integer key;
    private final String value;
}
