package com.bilibili.adp.common.enums;

/**
 * Created by many2023 on 2018/6/14.
 */
public enum GameAction {
    ACTIVATE(1, "激活"),
    RESERVE(2, "预约");

    private Integer code;
    private String desc;

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    GameAction(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static GameAction getByCode(Integer code) {
        for (GameAction bean : values()) {
            if (bean.getCode().equals(code)) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown ACTIVATE code.");
    }
}
