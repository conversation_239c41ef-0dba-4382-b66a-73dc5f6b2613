package com.bilibili.adp.common.bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetOssSignRequest implements Serializable {
    private static final long serialVersionUID = -8235013396837160278L;

    /** 生成方标识符，各个业务方不同。新业务需@json分配 **/
    private String gen;
    /** 完整域名 **/
    private String domain;
    /** 访问路由，资源Url **/
    private String ossKey;
    /** 密钥 **/
    private String secret;
    /** 其他入参 **/
    private Map<String, Object> param;

    public Map<String, Object> getFullParaMap() {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("deadline", (System.currentTimeMillis() / 1000 + 7200L));
        paramMap.put("gen", gen);
        if(!CollectionUtils.isEmpty(param)) {
            paramMap.putAll(param);
        }

        return paramMap;
    }
}
